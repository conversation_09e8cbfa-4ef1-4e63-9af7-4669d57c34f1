# seller_id 过滤器使用说明

## 问题解决

您遇到的 `sqlFilter.doFilter(null, null, sql)` 方法报错问题已经解决。问题的原因是：

1. **方法不存在**：`SfSqlFilter` 继承自 `FilterEventAdapter`，没有 `doFilter` 方法
2. **正确的方法**：应该调用 `connection_prepareStatement` 方法
3. **测试复杂性**：直接测试 Druid 过滤器需要复杂的模拟设置

## 解决方案

### 1. 修复后的测试方法

我们提供了两种测试方式：

#### 方式一：完整的 Druid 集成测试
```java
// 在 SfSqlFilterTest.java 中
private void executeSqlFilter(String sql) throws SQLException {
    sqlFilter.connection_prepareStatement(filterChain, connectionProxy, sql);
}

@Test
public void testValidSellerIdQueries() {
    String sql = "SELECT * FROM users WHERE seller_id = 'valid_seller_123'";
    assertDoesNotThrow(() -> {
        executeSqlFilter(sql);
    });
}
```

#### 方式二：简化的反射测试
```java
// 在 SfSqlFilterSimpleTest.java 中
private void testSellerIdSql(String sql, boolean shouldThrow) throws Exception {
    Method method = SfSqlFilter.class.getDeclaredMethod("interceptSellerIdSql", String.class, boolean.class);
    method.setAccessible(true);
    // ... 反射调用
}
```

### 2. 演示程序

运行 `SellerIdFilterDemo.main()` 可以直接看到过滤效果：

```bash
=== seller_id 过滤功能演示 ===

测试 SQL: SELECT * FROM users WHERE seller_id = 'valid_seller_123'
  涉及表: [users]
  包含 seller_id 字段: true
  seller_id 值有效: true
  需要有效字段: false
  结果: ✅ 通过 - seller_id 检查通过

测试 SQL: SELECT * FROM users WHERE seller_id IS NULL
  涉及表: [users]
  包含 seller_id 字段: true
  seller_id 值有效: false
  需要有效字段: true
  结果: ❌ 拦截 - seller_id 字段缺失或值无效
```

## 功能验证

### 1. 有效查询（通过检查）

```sql
-- ✅ 具体值
SELECT * FROM users WHERE seller_id = 'valid_seller_123';

-- ✅ 参数化查询
SELECT * FROM users WHERE seller_id = ?;

-- ✅ IN 查询
SELECT * FROM users WHERE seller_id IN ('seller1', 'seller2');

-- ✅ 不等于 null
SELECT * FROM users WHERE seller_id != NULL;

-- ✅ 不等于空字符串
SELECT * FROM users WHERE seller_id <> '';
```

### 2. 无效查询（被拦截）

```sql
-- ❌ 等于 NULL
SELECT * FROM users WHERE seller_id = NULL;

-- ❌ IS NULL
SELECT * FROM users WHERE seller_id IS NULL;

-- ❌ 空字符串
SELECT * FROM users WHERE seller_id = '';

-- ❌ IS NOT NULL（仅仅不为null还不够）
SELECT * FROM users WHERE seller_id IS NOT NULL;

-- ❌ IN 查询（全部为无效值）
SELECT * FROM users WHERE seller_id IN (NULL, '');
```

### 3. 混合查询（智能判断）

```sql
-- ✅ 包含有效值，通过检查
SELECT * FROM users WHERE seller_id IN (NULL, '', 'valid');

-- ✅ OR 条件包含有效值
SELECT * FROM users WHERE seller_id = 'valid' OR seller_id IS NULL;
```

## 配置说明

### 1. 启用 seller_id 检查

```java
// 在 SqlConfigUtil 中配置
when(sqlConfigUtil.getSellerIdCheckEnable()).thenReturn(true);
when(sqlConfigUtil.getSellerIdThrowError()).thenReturn(true);
```

### 2. 配置目标表

```java
Set<String> targetTables = new HashSet<>();
targetTables.add("users");
targetTables.add("orders");
targetTables.add("products");
when(sqlConfigUtil.getSellerIdCheckTablesSet()).thenReturn(targetTables);
```

### 3. 配置白名单

```java
Set<String> whitelist = new HashSet<>();
whitelist.add("SELECT * FROM users WHERE seller_id IS NULL");
when(dynamicRefreshNacosConfiguration.getDruidFilterWhiteSqls()).thenReturn(whitelist);
```

## 错误处理

### 1. 异常信息

当检查失败时，会抛出 `BizServiceException`：

```
SQL异常：查询特定表时必须包含有效的sellerId条件(不能为null或空字符串)!
```

### 2. 日志记录

```
SQL seller_id 检查失败: sql=SELECT * FROM users WHERE seller_id IS NULL, 
targetTables=[users, orders], 原因: seller_id字段缺失或值无效(null/空字符串)
```

## 运行测试

### 1. 运行完整测试

```bash
# 运行完整的测试套件
mvn test -Dtest=SfSqlFilterTest

# 运行简化测试
mvn test -Dtest=SfSqlFilterSimpleTest

# 运行演示程序
mvn test -Dtest=SellerIdFilterDemo
```

### 2. 单独测试特定功能

```java
@Test
public void testSpecificCase() throws Exception {
    String sql = "SELECT * FROM users WHERE seller_id IS NULL";
    
    // 这个查询应该被拦截
    testSellerIdSql(sql, true);
}
```

## 注意事项

1. **测试环境**：确保测试环境中有正确的依赖配置
2. **模拟对象**：使用 Mockito 正确设置模拟对象的行为
3. **反射调用**：简化测试使用反射调用私有方法
4. **异常处理**：正确处理和验证异常类型和消息

## 故障排除

### 1. 如果测试仍然报错

检查以下几点：
- Maven 依赖是否正确
- Mockito 版本是否兼容
- JUnit 版本是否正确

### 2. 如果功能不生效

检查以下配置：
- `SqlConfigUtil.getSellerIdCheckEnable()` 是否返回 `true`
- `SqlConfigUtil.getSellerIdThrowError()` 是否返回 `true`
- 目标表配置是否正确

### 3. 如果解析失败

检查 SQL 语法是否正确，复杂的 SQL 可能需要特殊处理。

现在您可以使用修复后的测试类来验证 seller_id 过滤功能是否正常工作了！
