package com.meiyunji.sponsored.common.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 查询性能监控工具
 * 用于监控和分析 SQL 查询性能，提供优化建议
 */
@Slf4j
@Component
public class QueryPerformanceMonitor {

    /**
     * 查询统计信息
     */
    private final ConcurrentHashMap<String, QueryStats> queryStatsMap = new ConcurrentHashMap<>();

    /**
     * 慢查询阈值（毫秒）
     */
    private static final long SLOW_QUERY_THRESHOLD = 5000;

    /**
     * 记录查询开始
     */
    public QueryContext startQuery(String queryType, String field, int dataSize) {
        return new QueryContext(queryType, field, dataSize, System.currentTimeMillis());
    }

    /**
     * 记录查询结束
     */
    public void endQuery(QueryContext context) {
        long duration = System.currentTimeMillis() - context.getStartTime();
        String key = context.getQueryType() + ":" + context.getField();
        
        QueryStats stats = queryStatsMap.computeIfAbsent(key, k -> new QueryStats());
        stats.addQuery(duration, context.getDataSize());

        // 记录慢查询
        if (duration > SLOW_QUERY_THRESHOLD) {
            logSlowQuery(context, duration);
        }

        // 提供优化建议
        if (stats.getQueryCount() % 100 == 0) {
            analyzeAndSuggest(key, stats);
        }
    }

    /**
     * 记录慢查询
     */
    private void logSlowQuery(QueryContext context, long duration) {
        log.warn("Slow query detected: type={}, field={}, dataSize={}, duration={}ms", 
                context.getQueryType(), context.getField(), context.getDataSize(), duration);
    }

    /**
     * 分析并提供优化建议
     */
    private void analyzeAndSuggest(String key, QueryStats stats) {
        double avgDuration = stats.getAverageDuration();
        double avgDataSize = stats.getAverageDataSize();

        StringBuilder suggestions = new StringBuilder();
        suggestions.append("Query performance analysis for ").append(key).append(":\n");
        suggestions.append("  Average duration: ").append(String.format("%.2f", avgDuration)).append("ms\n");
        suggestions.append("  Average data size: ").append(String.format("%.0f", avgDataSize)).append("\n");

        // 基于统计数据提供建议
        if (avgDuration > SLOW_QUERY_THRESHOLD) {
            suggestions.append("  Suggestions:\n");
            
            if (avgDataSize > 5000) {
                suggestions.append("    - Consider using bitmap queries for large datasets\n");
                suggestions.append("    - Implement data partitioning strategies\n");
            } else if (avgDataSize > 1000) {
                suggestions.append("    - Consider using batch queries\n");
                suggestions.append("    - Optimize index usage\n");
            } else {
                suggestions.append("    - Check for missing indexes\n");
                suggestions.append("    - Review query execution plan\n");
            }

            if (key.contains("campaign")) {
                suggestions.append("    - Consider caching frequently accessed campaign data\n");
            }
        }

        log.info(suggestions.toString());
    }

    /**
     * 获取查询统计信息
     */
    public QueryStats getQueryStats(String queryType, String field) {
        String key = queryType + ":" + field;
        return queryStatsMap.get(key);
    }

    /**
     * 清理统计数据
     */
    public void clearStats() {
        queryStatsMap.clear();
    }

    /**
     * 查询上下文
     */
    public static class QueryContext {
        private final String queryType;
        private final String field;
        private final int dataSize;
        private final long startTime;

        public QueryContext(String queryType, String field, int dataSize, long startTime) {
            this.queryType = queryType;
            this.field = field;
            this.dataSize = dataSize;
            this.startTime = startTime;
        }

        public String getQueryType() { return queryType; }
        public String getField() { return field; }
        public int getDataSize() { return dataSize; }
        public long getStartTime() { return startTime; }
    }

    /**
     * 查询统计信息
     */
    public static class QueryStats {
        private final AtomicLong queryCount = new AtomicLong(0);
        private final AtomicLong totalDuration = new AtomicLong(0);
        private final AtomicLong totalDataSize = new AtomicLong(0);
        private final AtomicLong maxDuration = new AtomicLong(0);
        private final AtomicLong minDuration = new AtomicLong(Long.MAX_VALUE);

        public void addQuery(long duration, int dataSize) {
            queryCount.incrementAndGet();
            totalDuration.addAndGet(duration);
            totalDataSize.addAndGet(dataSize);
            
            // 更新最大值
            maxDuration.updateAndGet(current -> Math.max(current, duration));
            
            // 更新最小值
            minDuration.updateAndGet(current -> Math.min(current, duration));
        }

        public long getQueryCount() { return queryCount.get(); }
        public double getAverageDuration() { 
            return queryCount.get() > 0 ? (double) totalDuration.get() / queryCount.get() : 0; 
        }
        public double getAverageDataSize() { 
            return queryCount.get() > 0 ? (double) totalDataSize.get() / queryCount.get() : 0; 
        }
        public long getMaxDuration() { return maxDuration.get(); }
        public long getMinDuration() { 
            long min = minDuration.get();
            return min == Long.MAX_VALUE ? 0 : min; 
        }
    }
}
