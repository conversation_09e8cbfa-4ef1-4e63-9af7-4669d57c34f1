package com.meiyunji.sponsored.common.util;

import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 优化的 SQL 构建工具
 * 集成性能监控和智能查询策略选择
 */
@Slf4j
@Component
public class OptimizedSqlBuilder {

    @Autowired(required = false)
    private QueryPerformanceMonitor performanceMonitor;

    /**
     * 构建优化的 campaign_id 查询条件
     * 这是针对您提到的具体场景的优化方法
     */
    public <T> String buildCampaignIdQuery(String field, List<T> campaignIds, List<Object> args) {
        if (CollectionUtils.isEmpty(campaignIds)) {
            return "";
        }

        // 开始性能监控
        QueryPerformanceMonitor.QueryContext context = null;
        if (performanceMonitor != null) {
            context = performanceMonitor.startQuery("campaign", field, campaignIds.size());
        }

        try {
            String result = buildOptimizedQuery(field, campaignIds, args, "campaign");
            return result;
        } finally {
            // 结束性能监控
            if (performanceMonitor != null && context != null) {
                performanceMonitor.endQuery(context);
            }
        }
    }

    /**
     * 构建优化的查询条件（通用方法）
     */
    public <T> String buildOptimizedQuery(String field, List<T> values, List<Object> args, String queryType) {
        if (CollectionUtils.isEmpty(values)) {
            return "";
        }

        // 过滤空值
        List<T> filterList = values.stream()
                .filter(e -> e != null && !"null".equalsIgnoreCase(e.toString()))
                .collect(Collectors.toList());

        if (filterList.isEmpty()) {
            return "";
        }

        // 根据查询类型和数据量选择最优策略
        return selectOptimalStrategy(field, filterList, args, queryType);
    }

    /**
     * 选择最优查询策略
     */
    private <T> String selectOptimalStrategy(String field, List<T> list, List<Object> args, String queryType) {
        int size = list.size();
        
        // 根据不同的查询类型设置不同的阈值
        int smallThreshold = getSmallThreshold(queryType);
        int mediumThreshold = getMediumThreshold(queryType);

        if (size <= smallThreshold) {
            return buildTraditionalInQuery(field, list, args);
        } else if (size <= mediumThreshold) {
            return buildBatchInQuery(field, list, args);
        } else {
            // 对于超大数据量，根据查询类型选择不同策略
            switch (queryType.toLowerCase()) {
                case "campaign":
                    return buildCampaignOptimizedQuery(field, list, args);
                case "adgroup":
                    return buildAdGroupOptimizedQuery(field, list, args);
                default:
                    return buildBitmapQuery(field, list, args);
            }
        }
    }

    /**
     * 构建传统 IN 查询
     */
    private <T> String buildTraditionalInQuery(String field, List<T> list, List<Object> args) {
        StringBuilder sql = new StringBuilder(" and ").append(field).append(" in (");
        
        for (int i = 0; i < list.size(); i++) {
            if (i > 0) sql.append(",");
            sql.append("?");
            args.add(list.get(i));
        }
        
        sql.append(") ");
        return sql.toString();
    }

    /**
     * 构建分批 IN 查询
     */
    private <T> String buildBatchInQuery(String field, List<T> list, List<Object> args) {
        List<List<T>> batches = Lists.partition(list, 1000);
        StringBuilder sql = new StringBuilder(" and (");
        
        for (int i = 0; i < batches.size(); i++) {
            if (i > 0) sql.append(" or ");
            
            sql.append(field).append(" in (");
            List<T> batch = batches.get(i);
            
            for (int j = 0; j < batch.size(); j++) {
                if (j > 0) sql.append(",");
                sql.append("?");
                args.add(batch.get(j));
            }
            sql.append(")");
        }
        
        sql.append(") ");
        return sql.toString();
    }

    /**
     * 构建 Campaign 专用优化查询
     * 使用临时表或 CTE 优化
     */
    private <T> String buildCampaignOptimizedQuery(String field, List<T> list, List<Object> args) {
        // 方案1：使用 Doris 的 array_contains 函数
        StringBuilder sql = new StringBuilder(" and array_contains(split(?, ','), cast(")
                .append(field).append(" as string)) ");
        
        // 优化字符串拼接
        StringBuilder valueBuilder = new StringBuilder();
        for (int i = 0; i < list.size(); i++) {
            if (i > 0) valueBuilder.append(",");
            valueBuilder.append(list.get(i));
        }
        
        args.add(valueBuilder.toString());
        return sql.toString();
    }

    /**
     * 构建 AdGroup 专用优化查询
     */
    private <T> String buildAdGroupOptimizedQuery(String field, List<T> list, List<Object> args) {
        // AdGroup 查询通常关联性较强，使用 EXISTS 子查询
        return buildExistsSubquery(field, list, args);
    }

    /**
     * 构建 EXISTS 子查询
     */
    private <T> String buildExistsSubquery(String field, List<T> list, List<Object> args) {
        StringBuilder sql = new StringBuilder(" and EXISTS (");
        sql.append("SELECT 1 FROM (SELECT unnest(array[");
        
        for (int i = 0; i < list.size(); i++) {
            if (i > 0) sql.append(",");
            sql.append("?");
            args.add(list.get(i));
        }
        
        sql.append("]) as id) t WHERE t.id = ").append(field).append(") ");
        return sql.toString();
    }

    /**
     * 构建 Bitmap 查询
     */
    private <T> String buildBitmapQuery(String field, List<T> list, List<Object> args) {
        StringBuilder sql = new StringBuilder(" and bitmap_has_any(bitmap_from_string(?), to_bitmap(")
                .append(field).append(")) ");
        
        // 优化字符串拼接
        StringBuilder valueBuilder = new StringBuilder();
        for (int i = 0; i < list.size(); i++) {
            if (i > 0) valueBuilder.append(",");
            valueBuilder.append(list.get(i));
        }
        
        args.add(valueBuilder.toString());
        return sql.toString();
    }

    /**
     * 获取小数据量阈值
     */
    private int getSmallThreshold(String queryType) {
        switch (queryType.toLowerCase()) {
            case "campaign": return 500;
            case "adgroup": return 800;
            case "product": return 1000;
            default: return 1000;
        }
    }

    /**
     * 获取中等数据量阈值
     */
    private int getMediumThreshold(String queryType) {
        switch (queryType.toLowerCase()) {
            case "campaign": return 2000;
            case "adgroup": return 3000;
            case "product": return 5000;
            default: return 5000;
        }
    }

    /**
     * 获取查询性能统计
     */
    public QueryPerformanceMonitor.QueryStats getPerformanceStats(String queryType, String field) {
        if (performanceMonitor != null) {
            return performanceMonitor.getQueryStats(queryType, field);
        }
        return null;
    }
}
