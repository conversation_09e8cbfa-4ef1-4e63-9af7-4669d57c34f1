package com.meiyunji.sponsored.common.filter;

import com.alibaba.druid.filter.FilterChain;
import com.alibaba.druid.filter.FilterEventAdapter;
import com.alibaba.druid.proxy.jdbc.ConnectionProxy;
import com.alibaba.druid.proxy.jdbc.JdbcParameter;
import com.alibaba.druid.proxy.jdbc.PreparedStatementProxy;
import com.alibaba.druid.proxy.jdbc.ResultSetProxy;
import com.alibaba.druid.sql.ast.SQLExpr;
import com.alibaba.druid.sql.ast.SQLStatement;
import com.alibaba.druid.sql.ast.expr.*;
import com.alibaba.druid.sql.dialect.mysql.visitor.MySqlASTVisitorAdapter;
import com.alibaba.druid.util.JdbcConstants;
import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.config.DynamicRefreshNacosConfiguration;
import com.meiyunji.sponsored.common.exception.BizServiceException;
import com.meiyunji.sponsored.common.springjdbc.SqlTypeParser;
import com.meiyunji.sponsored.common.util.SqlConfigUtil;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.sql.SQLException;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * SQL拦截器 - 提供SQL安全检查功能
 *
 * 功能说明：
 * 1. 预编译阶段拦截：检查PUID字段（针对UPDATE/DELETE语句）
 * 2. 执行阶段拦截：检查seller_id字段（针对SELECT语句，包含参数值验证）
 * 3. 支持白名单机制和动态配置
 *
 */
@Slf4j
@Component
public class SfSqlFilter extends FilterEventAdapter {

    @Autowired
    private SqlConfigUtil sqlConfigUtil;

    @Autowired
    private DynamicRefreshNacosConfiguration dynamicRefreshNacosConfiguration;

    // ==================== 拦截阶段枚举 ====================

    /**
     * SQL拦截阶段
     */
    private enum InterceptPhase {
        COMPILE_TIME,    // 预编译阶段
        EXECUTION_TIME   // 执行阶段
    }

    // ==================== 主要拦截入口 ====================

    @Override
    public PreparedStatementProxy connection_prepareStatement(
            FilterChain chain, ConnectionProxy connection, String sql) throws SQLException {
        // 预编译阶段拦截：主要检查PUID
        executeInterceptPhase(InterceptPhase.COMPILE_TIME, sql, null);
        return super.connection_prepareStatement(chain, connection, sql);
    }

    // ==================== SQL拦截策略定义 ====================

    /**
     * SQL拦截策略枚举
     * 定义不同类型的SQL检查策略，支持预编译和执行阶段拦截
     */
    private enum SqlInterceptStrategy {
        /**
         * PUID字段检查策略
         * 适用阶段：预编译阶段
         * 适用SQL类型：UPDATE/DELETE语句
         * 检查内容：确保包含puid或id字段条件
         */
        PUID_CHECK {
            @Override
            public InterceptPhase getApplicablePhase() {
                return InterceptPhase.COMPILE_TIME;
            }

            @Override
            public boolean shouldProcess(SQLStatement statement, InterceptPhase phase) {
                return phase == InterceptPhase.COMPILE_TIME && SqlTypeParser.isUpdateOrDeleteSql(statement);
            }

            @Override
            public InterceptResult executeCheck(InterceptContext context) {
                List<String> targetFields = Lists.newArrayList("puid", "id");
                for (String targetField : targetFields) {
                    if (SqlTypeParser.containsFieldInWhere(context.getSqlStatement(), targetField)) {
                        return InterceptResult.success();
                    }
                }
                return InterceptResult.failure(getErrorMessage());
            }

            @Override
            public String getErrorMessage() {
                return "SQL异常：增删改需增加puid或id条件!";
            }

            @Override
            public String getLogPrefix() {
                return "interceptPuidSql";
            }

            @Override
            public boolean isEnabled(SfSqlFilter filter) {
                return filter.sqlConfigUtil.getStatEnable();
            }

            @Override
            public boolean shouldThrowError(SfSqlFilter filter) {
                return filter.sqlConfigUtil.getThrowError();
            }
        },

        /**
         * SellerID字段检查策略
         * 适用阶段：执行阶段（支持参数值验证）
         * 适用SQL类型：SELECT语句
         * 检查内容：确保包含有效的seller_id字段条件（不为null或空字符串）
         */
        SELLER_ID_CHECK {
            @Override
            public InterceptPhase getApplicablePhase() {
                return InterceptPhase.EXECUTION_TIME;
            }

            @Override
            public boolean shouldProcess(SQLStatement statement, InterceptPhase phase) {
                return phase == InterceptPhase.EXECUTION_TIME && SqlTypeParser.isSelectSql(statement);
            }

            @Override
            public InterceptResult executeCheck(InterceptContext context) {
                Set<String> targetTables = context.getFilter().sqlConfigUtil.getSellerIdCheckTablesSet();
                if (targetTables.isEmpty()) {
                    return InterceptResult.success(); // 如果没有配置目标表，则跳过检查
                }

                // 检查是否涉及目标表
                Set<String> tableNames = SqlTypeParser.extractTableNames(context.getSqlStatement());
                boolean involvesTargetTable = tableNames.stream()
                        .anyMatch(tableName -> targetTables.contains(tableName.toLowerCase()));

                if (!involvesTargetTable) {
                    return InterceptResult.success();
                }

                // 执行阶段：验证实际参数值
                if (context.getStatement() != null) {
                    boolean hasValidSellerIdValue = context.getFilter().validateSellerIdParameters(
                            context.getStatement(), targetTables);
                    if (!hasValidSellerIdValue) {
                        log.warn("执行阶段seller_id检查失败: sql={}, parameters={}",
                                context.getSql(), context.getStatement().getParameters());
                        return InterceptResult.failure(getErrorMessage());
                    }
                }

                return InterceptResult.success();
            }

            @Override
            public String getErrorMessage() {
                return "SQL异常：查询特定表时必须包含有效的sellerId条件(不能为null或空字符串)!";
            }

            @Override
            public String getLogPrefix() {
                return "interceptSellerIdSql";
            }

            @Override
            public boolean isEnabled(SfSqlFilter filter) {
                return filter.sqlConfigUtil.getSellerIdCheckEnable();
            }

            @Override
            public boolean shouldThrowError(SfSqlFilter filter) {
                return filter.sqlConfigUtil.getSellerIdThrowError();
            }
        };

        /**
         * 获取策略适用的拦截阶段
         */
        public abstract InterceptPhase getApplicablePhase();

        /**
         * 检查SQL语句是否需要处理
         */
        public abstract boolean shouldProcess(SQLStatement statement, InterceptPhase phase);

        /**
         * 执行具体的检查逻辑
         */
        public abstract InterceptResult executeCheck(InterceptContext context);

        /**
         * 获取错误消息
         */
        public abstract String getErrorMessage();

        /**
         * 获取日志前缀
         */
        public abstract String getLogPrefix();

        /**
         * 检查策略是否启用
         */
        public abstract boolean isEnabled(SfSqlFilter filter);

        /**
         * 检查是否应该抛出异常
         */
        public abstract boolean shouldThrowError(SfSqlFilter filter);
    }

    // ==================== 拦截上下文和结果类 ====================

    /**
     * 拦截上下文 - 封装拦截过程中需要的所有信息
     */
    @Getter
    private static class InterceptContext {
        private final String sql;
        private final SQLStatement sqlStatement;
        private final PreparedStatementProxy statement;
        private final SfSqlFilter filter;
        private final InterceptPhase phase;

        public InterceptContext(String sql, SQLStatement sqlStatement, PreparedStatementProxy statement,
                              SfSqlFilter filter, InterceptPhase phase) {
            this.sql = sql;
            this.sqlStatement = sqlStatement;
            this.statement = statement;
            this.filter = filter;
            this.phase = phase;
        }

    }

    /**
     * 拦截结果 - 封装拦截检查的结果
     */
    @Getter
    private static class InterceptResult {
        private final boolean success;
        private final String errorMessage;

        private InterceptResult(boolean success, String errorMessage) {
            this.success = success;
            this.errorMessage = errorMessage;
        }

        public static InterceptResult success() {
            return new InterceptResult(true, null);
        }

        public static InterceptResult failure(String errorMessage) {
            return new InterceptResult(false, errorMessage);
        }

    }


    /**
     * 统一拦截执行入口 - 模板方法
     * 根据拦截阶段执行相应的检查策略
     */
    private void executeInterceptPhase(InterceptPhase phase, String sql, PreparedStatementProxy statement) {
        try {
            // 基础日志记录
            if (phase == InterceptPhase.COMPILE_TIME && sqlConfigUtil.getLogEnable()) {
                log.info("sql = {}", sql);
            }

            // 白名单检查
            if (isInWhitelist(sql)) {
                log.debug("SQL在白名单中，跳过{}检查: {}", phase.name(), sql);
                return;
            }

            // 解析SQL
            List<SQLStatement> sqlStatements = SqlTypeParser.parseSql(sql, JdbcConstants.MYSQL.name());
            if (CollectionUtils.isEmpty(sqlStatements)) {
                log.debug("SQL解析结果为空，跳过检查: {}", sql);
                return;
            }

            SQLStatement sqlStatement = sqlStatements.get(0);

            // 执行所有适用于当前阶段的策略
            for (SqlInterceptStrategy strategy : SqlInterceptStrategy.values()) {
                if (strategy.isEnabled(this) && strategy.shouldProcess(sqlStatement, phase)) {
                    executeStrategy(strategy, sql, sqlStatement, statement, phase);
                }
            }

        } catch (Exception e) {
            log.error("{}阶段拦截失败: sql={}", phase.name(), sql, e);
            // 如果是业务异常，重新抛出
            if (e instanceof BizServiceException) {
                throw e;
            }
        }
    }

    /**
     * 执行具体的拦截策略
     */
    private void executeStrategy(SqlInterceptStrategy strategy, String sql, SQLStatement sqlStatement,
                               PreparedStatementProxy statement, InterceptPhase phase) {
        try {
            // 构建拦截上下文
            InterceptContext context = new InterceptContext(sql, sqlStatement, statement, this, phase);

            // 执行策略检查
            InterceptResult result = strategy.executeCheck(context);

            // 处理检查结果
            if (!result.isSuccess()) {
                handleInterceptFailure(strategy, sql, result.getErrorMessage());
            }

        } catch (Exception e) {
            log.error("策略{}执行失败: sql={}", strategy.name(), sql, e);
            if (e instanceof BizServiceException) {
                throw e;
            }
        }
    }

    /**
     * 处理拦截失败的情况
     */
    private void handleInterceptFailure(SqlInterceptStrategy strategy, String sql, String errorMessage) {
        BizServiceException bizServiceException = new BizServiceException(errorMessage);

        // 记录警告日志
        log.warn("SQL拦截失败: strategy={}, sql={}, error={}",
                strategy.name(), sql, ExceptionUtils.getStackTrace(bizServiceException));

        // 根据策略配置决定是否抛出异常
        if (strategy.shouldThrowError(this)) {
            throw bizServiceException;
        }
    }

    /**
     * 检查是否在白名单中
     */
    private boolean isInWhitelist(String sql) {
        Set<String> whitelistSqls = dynamicRefreshNacosConfiguration.getDruidFilterWhiteSqls();
        return whitelistSqls != null && whitelistSqls.contains(sql);
    }

    // ==================== 执行阶段拦截入口 ====================



    @Override
    public ResultSetProxy preparedStatement_executeQuery(FilterChain chain, PreparedStatementProxy statement) throws SQLException {
        // 执行阶段拦截：主要检查seller_id参数值
        executeInterceptPhase(InterceptPhase.EXECUTION_TIME, statement.getSql(), statement);
        return super.preparedStatement_executeQuery(chain, statement);
    }



    // ==================== 参数值验证工具方法 ====================

    /**
     * 验证PreparedStatement中的参数值是否包含有效的seller_id
     *
     * @param statement PreparedStatement代理对象
     * @param targetTables 需要检查的目标表集合
     * @return true表示包含有效的seller_id值，false表示不包含或值无效
     */
    private boolean validateSellerIdParameters(PreparedStatementProxy statement, Set<String> targetTables) {
        try {
            String sql = statement.getSql();
            Map<Integer, JdbcParameter> parameters = statement.getParameters();

            // 解析SQL
            List<SQLStatement> sqlStatements = SqlTypeParser.parseSql(sql, JdbcConstants.MYSQL.name());
            if (CollectionUtils.isEmpty(sqlStatements)) {
                return true; // 解析失败，跳过检查
            }

            SQLStatement sqlStatement = sqlStatements.get(0);

            // 检查是否涉及目标表
            Set<String> tableNames = SqlTypeParser.extractTableNames(sqlStatement);
            boolean involvesTargetTable = tableNames.stream()
                    .anyMatch(tableName -> targetTables.contains(tableName.toLowerCase()));

            if (!involvesTargetTable) {
                return true; // 不涉及目标表，跳过检查
            }

            // 使用访问器精确检查seller_id字段对应的参数值
            SellerIdParameterVisitor visitor = new SellerIdParameterVisitor(parameters);
            sqlStatement.accept(visitor);

            return visitor.hasValidSellerIdValue();

        } catch (Exception e) {
            log.error("参数值验证失败", e);
            return true; // 验证失败时默认通过
        }
    }

    /**
     * 检查参数值是否有效（非null且非空字符串）
     */
    private static boolean isValidParameterValue(Object value) {
        if (value == null) {
            return false;
        }
        if (value instanceof String) {
            String strValue = (String) value;
            return !strValue.trim().isEmpty(); // 非空且非空白字符串
        }
        return true; // 非字符串类型认为有效
    }

    // ==================== seller_id参数值访问器 ====================

    /**
     * seller_id参数访问器
     * 精确匹配seller_id字段对应的参数位置并检查参数值有效性
     * 支持的SQL模式：
     * - seller_id = ?
     * - seller_id IN (?, ?, ...)
     * - seller_id != ? / seller_id <> ?
     * - seller_id NOT IN (?, ?, ...)
     */
    private static class SellerIdParameterVisitor extends MySqlASTVisitorAdapter {
        private final Map<Integer, JdbcParameter> parameters;
        private boolean hasValidValue = false;
        private int globalParameterIndex = 0;
        private static final String SELLER_ID = "seller_id";

        public SellerIdParameterVisitor(Map<Integer, JdbcParameter> parameters) {
            this.parameters = parameters;
        }

        public boolean hasValidSellerIdValue() {
            return hasValidValue;
        }

        @Override
        public boolean visit(SQLBinaryOpExpr x) {
            SQLExpr left = x.getLeft();
            SQLExpr right = x.getRight();
            SQLBinaryOperator operator = x.getOperator();

            // 检查左侧是否是seller_id字段
            if (isSellerIdField(left)) {
                if (operator == SQLBinaryOperator.Equality) {
                    // seller_id = ?
                    if (right instanceof SQLVariantRefExpr) {
                        Object paramValue = getNextParameterValue();
                        if (isValidParameterValue(paramValue)) {
                            hasValidValue = true;
                        }
                    } else {
                        // seller_id = 'literal_value'
                        if (isValidLiteralValue(right)) {
                            hasValidValue = true;
                        }
                    }
                } else if (operator == SQLBinaryOperator.NotEqual ||
                          operator == SQLBinaryOperator.LessThanOrGreater) {
                    // seller_id != ? 或 seller_id <> ?
                    if (right instanceof SQLVariantRefExpr) {
                        Object paramValue = getNextParameterValue();
                        if (isNullOrEmpty(paramValue)) {
                            hasValidValue = true; // 不等于null或空字符串表示有有效值
                        }
                    }
                }
            }
            return true;
        }

        @Override
        public boolean visit(SQLInListExpr x) {
            SQLExpr expr = x.getExpr();

            if (isSellerIdField(expr)) {
                List<SQLExpr> targetList = x.getTargetList();
                boolean isNot = x.isNot();

                if (!isNot) {
                    // seller_id IN (?, ?, ...)
                    for (SQLExpr item : targetList) {
                        if (item instanceof SQLVariantRefExpr) {
                            Object paramValue = getNextParameterValue();
                            if (isValidParameterValue(paramValue)) {
                                hasValidValue = true;
                                break;
                            }
                        } else if (isValidLiteralValue(item)) {
                            hasValidValue = true;
                            break;
                        }
                    }
                } else {
                    // seller_id NOT IN (?, ?, ...)
                    boolean allInvalid = true;
                    for (SQLExpr item : targetList) {
                        if (item instanceof SQLVariantRefExpr) {
                            Object paramValue = getNextParameterValue();
                            if (isValidParameterValue(paramValue)) {
                                allInvalid = false;
                                break;
                            }
                        } else if (isValidLiteralValue(item)) {
                            allInvalid = false;
                            break;
                        }
                    }
                    if (allInvalid) {
                        hasValidValue = true;
                    }
                }
            }
            return true;
        }

        /**
         * 检查表达式是否是seller_id字段
         */
        private boolean isSellerIdField(SQLExpr expr) {
            if (expr instanceof SQLIdentifierExpr) {
                String fieldName = ((SQLIdentifierExpr) expr).getName();
                return SELLER_ID.equalsIgnoreCase(fieldName);
            } else if (expr instanceof SQLPropertyExpr) {
                String fieldName = ((SQLPropertyExpr) expr).getName();
                return SELLER_ID.equalsIgnoreCase(fieldName);
            }
            return false;
        }

        /**
         * 获取下一个参数值并递增索引
         */
        private Object getNextParameterValue() {
            JdbcParameter param = parameters.get(globalParameterIndex);
            globalParameterIndex++; // 递增全局参数索引
            return param != null ? param.getValue() : null;
        }

        /**
         * 检查字面值是否有效
         */
        private boolean isValidLiteralValue(SQLExpr expr) {
            if (expr instanceof SQLNullExpr) {
                return false;
            }
            if (expr instanceof SQLCharExpr) {
                String value = ((SQLCharExpr) expr).getText();
                return StringUtils.isNotBlank(value);
            }
            return true; // 其他类型认为有效
        }

        /**
         * 检查值是否为null或空
         */
        private boolean isNullOrEmpty(Object value) {
            if (value == null) {
                return true;
            }
            if (value instanceof String) {
                return StringUtils.isBlank((String) value);
            }
            return false;
        }
    }
}
