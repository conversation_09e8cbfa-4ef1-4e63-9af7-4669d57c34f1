package com.meiyunji.sponsored.common.filter;

import com.alibaba.druid.filter.FilterChain;
import com.alibaba.druid.filter.FilterEventAdapter;
import com.alibaba.druid.proxy.jdbc.ConnectionProxy;
import com.alibaba.druid.proxy.jdbc.JdbcParameter;
import com.alibaba.druid.proxy.jdbc.PreparedStatementProxy;
import com.alibaba.druid.proxy.jdbc.ResultSetProxy;
import com.alibaba.druid.sql.ast.SQLExpr;
import com.alibaba.druid.sql.ast.SQLStatement;
import com.alibaba.druid.sql.ast.expr.*;
import com.alibaba.druid.sql.dialect.mysql.visitor.MySqlASTVisitorAdapter;
import com.alibaba.druid.util.JdbcConstants;
import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.config.DynamicRefreshNacosConfiguration;
import com.meiyunji.sponsored.common.exception.BizServiceException;
import com.meiyunji.sponsored.common.springjdbc.SqlTypeParser;
import com.meiyunji.sponsored.common.util.SqlConfigUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.sql.SQLException;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * sql拦截器 sql安全
 */
@Slf4j
@Component
public class SfSqlFilter  extends FilterEventAdapter {


    @Autowired
    private SqlConfigUtil sqlConfigUtil;

    @Autowired
    private DynamicRefreshNacosConfiguration dynamicRefreshNacosConfiguration;

    /**
     * 白名单sql
     */
//    private static final Set<String> WHITE_SQL = new HashSet<>(Lists.newArrayList("sql"));

    @Override
    public PreparedStatementProxy connection_prepareStatement(
            FilterChain chain, ConnectionProxy connection, String sql) throws SQLException {
        // 拦截 SQL
        if (sqlConfigUtil.getLogEnable()) {
            log.info("sql = {}", sql);
        }
        if (sqlConfigUtil.getStatEnable()) {
            this.interceptPuidSql(sql, sqlConfigUtil.getThrowError());
        }
        // 检查sellerid字段
//        if (sqlConfigUtil.getSellerIdCheckEnable()) {
//            this.interceptSellerIdSql(sql, sqlConfigUtil.getSellerIdThrowError());
//        }
        return super.connection_prepareStatement(chain, connection, sql);
    }

    /**
     * SQL拦截策略枚举
     */
    private enum SqlInterceptStrategy {
        /**
         * PUID字段检查策略（针对UPDATE/DELETE语句）
         */
        PUID_CHECK {
            @Override
            public boolean shouldProcess(SQLStatement statement) {
                return SqlTypeParser.isUpdateOrDeleteSql(statement);
            }

            @Override
            public boolean checkSql(SQLStatement statement, String sql, SfSqlFilter filter) {
                List<String> targetFields = Lists.newArrayList("puid","id");
                for (String targetField : targetFields) {
                    if (SqlTypeParser.containsFieldInWhere(statement, targetField)) {
                        return true;
                    }
                }
                return false;
            }

            @Override
            public String getErrorMessage() {
                return "SQL异常：增删改需增加puid或id条件!";
            }

            @Override
            public String getLogPrefix() {
                return "interceptPuidSql";
            }
        },

        /**
         * SellerID字段检查策略（针对SELECT语句）
         * 增强版：不仅检查字段是否存在，还检查字段值是否有效（不为null或空字符串）
         */
        SELLER_ID_CHECK {
            @Override
            public boolean shouldProcess(SQLStatement statement) {
                return SqlTypeParser.isSelectSql(statement);
            }

            @Override
            public boolean checkSql(SQLStatement statement, String sql, SfSqlFilter filter) {
                Set<String> targetTables = filter.sqlConfigUtil.getSellerIdCheckTablesSet();
                if (targetTables.isEmpty()) {
                    return true; // 如果没有配置目标表，则跳过检查
                }

                // 校验查询是否带有seller_id且值有效
                boolean needsValidField = SqlTypeParser.needsValidFieldInWhere(statement, targetTables, "seller_id");

                if (needsValidField) {
                    // 记录详细的错误信息
                    log.warn("SQL seller_id 检查失败: sql={}, targetTables={}, 原因: seller_id字段缺失或值无效(null/空字符串)",
                            sql, targetTables);
                }

                // 返回true表示检查通过，false表示检查失败
                return !needsValidField;
            }

            @Override
            public String getErrorMessage() {
                return "SQL异常：查询特定表时必须包含有效的sellerId条件(不能为null或空字符串)!";
            }

            @Override
            public String getLogPrefix() {
                return "interceptSellerIdSql";
            }
        };

        /**
         * 检查SQL语句类型是否需要处理
         */
        public abstract boolean shouldProcess(SQLStatement statement);

        /**
         * 执行具体的检查逻辑
         */
        public abstract boolean checkSql(SQLStatement statement, String sql, SfSqlFilter filter);

        /**
         * 获取错误消息
         */
        public abstract String getErrorMessage();

        /**
         * 获取日志前缀
         */
        public abstract String getLogPrefix();
    }

    /**
     * 通用SQL拦截模板方法
     */
    private void interceptSqlTemplate(String sql, boolean throwError, SqlInterceptStrategy strategy) {
        try {
            // 白名单sql检查
            if (dynamicRefreshNacosConfiguration.getDruidFilterWhiteSqls().contains(sql)) {
                return;
            }

            // 解析 SQL
            List<SQLStatement> sqlStatements = SqlTypeParser.parseSql(sql, JdbcConstants.MYSQL.name());
            if (CollectionUtils.isEmpty(sqlStatements)) {
                return;
            }

            // 只取第一个
            SQLStatement statement = sqlStatements.get(0);

            // 检查是否需要处理此类型的SQL
            if (!strategy.shouldProcess(statement)) {
                return;
            }

            log.info("{} = {}", strategy.getLogPrefix(), sql);

            // 执行具体的检查逻辑
            boolean checkPassed = strategy.checkSql(statement, sql, this);
            if (!checkPassed) {
                BizServiceException bizServiceException = new BizServiceException(strategy.getErrorMessage());
                // 检查不包含sellerId时，记录警告日志
                log.warn("SQL拦截记录 sql = {}, error = {}", sql, ExceptionUtils.getStackTrace(bizServiceException));
                if (throwError) {
                    throw bizServiceException;
                }
            }
        } catch (Exception e) {
            // 解析失败时跳过（例如复杂 SQL）
            log.error("解析SQL错误 sql = {}, error = {}", sql, ExceptionUtils.getStackTrace(e));
            if (e instanceof BizServiceException) {
                if (throwError) {
                    throw new BizServiceException(strategy.getErrorMessage());
                }
            }
        }
    }

    private void interceptPuidSql(String sql, boolean throwError) {
        interceptSqlTemplate(sql, throwError, SqlInterceptStrategy.PUID_CHECK);
    }

    /**
     * 拦截针对特定表的查询，检查是否包含sellerid字段
     *
     * @param sql SQL语句
     * @param throwError 是否抛出异常
     */
    private void interceptSellerIdSql(String sql, boolean throwError) {
        interceptSqlTemplate(sql, throwError, SqlInterceptStrategy.SELLER_ID_CHECK);
    }

    // ==================== 执行阶段拦截（包含实际参数值） ====================

    @Override
    public ResultSetProxy preparedStatement_executeQuery(FilterChain chain, PreparedStatementProxy statement) throws SQLException {
        // 在查询执行阶段拦截
        if (sqlConfigUtil.getSellerIdCheckEnable() && sqlConfigUtil.getSellerIdExecutionCheckEnable()) {
            interceptExecutionSql(statement);
        }
        return super.preparedStatement_executeQuery(chain, statement);
    }

    /**
     * 拦截执行阶段的SQL，包含实际参数值
     * 方案1：构建完整SQL进行检查
     * 方案2：直接验证参数值（推荐）
     */
    private void interceptExecutionSql(PreparedStatementProxy statement) {
        try {
            Set<String> targetTables = sqlConfigUtil.getSellerIdCheckTablesSet();
            if (targetTables.isEmpty()) {
                return;
            }

            // 方案2：直接验证参数值（推荐，性能更好）
            boolean hasValidSellerIdValue = validateSellerIdParameters(statement, targetTables);

            if (!hasValidSellerIdValue) {
                String errorMsg = "SQL执行阶段检查失败：seller_id参数值无效(null或空字符串)";
                log.warn("执行阶段seller_id检查失败: sql={}, parameters={}",
                        statement.getSql(), statement.getParameters());

                if (sqlConfigUtil.getSellerIdThrowError()) {
                    throw new BizServiceException(errorMsg);
                }
            }

            if (log.isDebugEnabled()) {
                String executionSql = buildExecutionSql(statement.getSql(), statement.getParameters());
                log.debug("执行阶段SQL检查: originalSql={}, parameters={}, executionSql={}",
                         statement.getSql(), statement.getParameters(), executionSql);
            }

        } catch (Exception e) {
            log.error("执行阶段SQL拦截失败", e);
            if (sqlConfigUtil.getSellerIdThrowError()) {
                throw new BizServiceException("SQL执行阶段检查失败: " + e.getMessage());
            }
        }
    }

    /**
     * 构建包含实际参数值的执行SQL
     */
    private String buildExecutionSql(String originalSql, Map<Integer,JdbcParameter> parameters) {
        if (parameters == null || parameters.isEmpty()) {
            return originalSql;
        }

        String executionSql = originalSql;
        int paramIndex = 0;

        // 替换 ? 占位符为实际参数值
        while (executionSql.contains("?") && paramIndex < parameters.size()) {
            Object param = parameters.get(paramIndex);
            String paramValue = convertParameterToString(param);

            // 替换第一个 ? 为参数值
            executionSql = executionSql.replaceFirst("\\?", paramValue);
            paramIndex++;
        }

        return executionSql;
    }

    /**
     * 将参数值转换为SQL字符串表示
     */
    private String convertParameterToString(Object param) {
        if (param == null) {
            return "NULL";
        }

        if (param instanceof String) {
            String strValue = (String) param;
            // 转义单引号并用单引号包围
            return "'" + strValue.replace("'", "''") + "'";
        }

        if (param instanceof Number) {
            return param.toString();
        }

        if (param instanceof Boolean) {
            return param.toString();
        }

        // 其他类型转换为字符串并用单引号包围
        return "'" + param.toString().replace("'", "''") + "'";
    }

    /**
     * 简化的参数值验证方法
     * 检查PreparedStatement中的参数值是否包含有效的seller_id
     */
    private boolean validateSellerIdParameters(PreparedStatementProxy statement, Set<String> targetTables) {
        try {
            String sql = statement.getSql();
            Map<Integer, JdbcParameter> parameters = statement.getParameters();

            // 解析SQL
            List<SQLStatement> sqlStatements = SqlTypeParser.parseSql(sql, JdbcConstants.MYSQL.name());
            if (CollectionUtils.isEmpty(sqlStatements)) {
                return true; // 解析失败，跳过检查
            }

            SQLStatement sqlStatement = sqlStatements.get(0);

            // 检查是否涉及目标表
            Set<String> tableNames = SqlTypeParser.extractTableNames(sqlStatement);
            boolean involvesTargetTable = tableNames.stream()
                    .anyMatch(tableName -> targetTables.contains(tableName.toLowerCase()));

            if (!involvesTargetTable) {
                return true; // 不涉及目标表，跳过检查
            }

            // 使用访问器精确检查seller_id字段对应的参数值
            SellerIdParameterVisitor visitor = new SellerIdParameterVisitor(parameters);
            sqlStatement.accept(visitor);

            return visitor.hasValidSellerIdValue();

        } catch (Exception e) {
            log.error("参数值验证失败", e);
            return true; // 验证失败时默认通过
        }
    }

    /**
     * 检查参数值是否有效
     */
    private static boolean isValidParameterValue(Object value) {
        if (value == null) {
            return false;
        }
        if (value instanceof String) {
            String strValue = (String) value;
            return !strValue.trim().isEmpty(); // 非空且非空白字符串
        }
        return true; // 非字符串类型认为有效
    }

    /**
     * seller_id 参数访问器
     * 精确匹配 seller_id 字段对应的参数位置并检查参数值
     */
    private static class SellerIdParameterVisitor extends MySqlASTVisitorAdapter {
        private final Map<Integer, JdbcParameter> parameters;
        private boolean hasValidValue = false;
        private int globalParameterIndex = 0; // 全局参数索引

        public SellerIdParameterVisitor(Map<Integer, JdbcParameter> parameters) {
            this.parameters = parameters;
        }

        public boolean hasValidSellerIdValue() {
            return hasValidValue;
        }

        @Override
        public boolean visit(SQLBinaryOpExpr x) {
            SQLExpr left = x.getLeft();
            SQLExpr right = x.getRight();
            SQLBinaryOperator operator = x.getOperator();

            // 检查左侧是否是 seller_id 字段
            if (isSellerIdField(left)) {
                if (operator == SQLBinaryOperator.Equality) {
                    // seller_id = ?
                    if (right instanceof SQLVariantRefExpr) {
                        Object paramValue = getNextParameterValue();
                        if (isValidParameterValue(paramValue)) {
                            hasValidValue = true;
                        }
                    } else {
                        // seller_id = 'literal_value'
                        if (isValidLiteralValue(right)) {
                            hasValidValue = true;
                        }
                    }
                } else if (operator == SQLBinaryOperator.NotEqual ||
                          operator == SQLBinaryOperator.LessThanOrGreater) {
                    // seller_id != ? 或 seller_id <> ?
                    if (right instanceof SQLVariantRefExpr) {
                        Object paramValue = getNextParameterValue();
                        if (isNullOrEmpty(paramValue)) {
                            hasValidValue = true; // 不等于null或空字符串表示有有效值
                        }
                    }
                }
            }
            return true;
        }

        @Override
        public boolean visit(SQLInListExpr x) {
            SQLExpr expr = x.getExpr();

            if (isSellerIdField(expr)) {
                List<SQLExpr> targetList = x.getTargetList();
                boolean isNot = x.isNot();

                if (!isNot) {
                    // seller_id IN (?, ?, ...)
                    for (SQLExpr item : targetList) {
                        if (item instanceof SQLVariantRefExpr) {
                            Object paramValue = getNextParameterValue();
                            if (isValidParameterValue(paramValue)) {
                                hasValidValue = true;
                                break;
                            }
                        } else if (isValidLiteralValue(item)) {
                            hasValidValue = true;
                            break;
                        }
                    }
                } else {
                    // seller_id NOT IN (?, ?, ...)
                    boolean allInvalid = true;
                    for (SQLExpr item : targetList) {
                        if (item instanceof SQLVariantRefExpr) {
                            Object paramValue = getNextParameterValue();
                            if (isValidParameterValue(paramValue)) {
                                allInvalid = false;
                                break;
                            }
                        } else if (isValidLiteralValue(item)) {
                            allInvalid = false;
                            break;
                        }
                    }
                    if (allInvalid) {
                        hasValidValue = true;
                    }
                }
            }
            return true;
        }

        /**
         * 检查表达式是否是 seller_id 字段
         */
        private boolean isSellerIdField(SQLExpr expr) {
            if (expr instanceof SQLIdentifierExpr) {
                String fieldName = ((SQLIdentifierExpr) expr).getName();
                return "seller_id".equalsIgnoreCase(fieldName);
            } else if (expr instanceof SQLPropertyExpr) {
                String fieldName = ((SQLPropertyExpr) expr).getName();
                return "seller_id".equalsIgnoreCase(fieldName);
            }
            return false;
        }

        /**
         * 获取下一个参数值并递增索引
         */
        private Object getNextParameterValue() {
            JdbcParameter param = parameters.get(globalParameterIndex);
            globalParameterIndex++; // 递增全局参数索引
            return param != null ? param.getValue() : null;
        }

        /**
         * 检查字面值是否有效
         */
        private boolean isValidLiteralValue(SQLExpr expr) {
            if (expr instanceof SQLNullExpr) {
                return false;
            }
            if (expr instanceof SQLCharExpr) {
                String value = ((SQLCharExpr) expr).getText();
                return StringUtils.isNotBlank(value);
            }
            return true; // 其他类型认为有效
        }

        /**
         * 检查值是否为null或空
         */
        private boolean isNullOrEmpty(Object value) {
            if (value == null) {
                return true;
            }
            if (value instanceof String) {
                return StringUtils.isBlank((String) value);
            }
            return false;
        }
    }
}
