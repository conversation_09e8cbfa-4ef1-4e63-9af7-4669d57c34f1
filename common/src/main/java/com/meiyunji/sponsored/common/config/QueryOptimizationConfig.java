package com.meiyunji.sponsored.common.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 查询优化配置类
 * 用于配置不同场景下的查询优化策略
 */
@Data
@Component
@ConfigurationProperties(prefix = "query.optimization")
public class QueryOptimizationConfig {

    /**
     * 小数据量阈值（使用传统 IN 查询）
     */
    private int smallDataThreshold = 1000;

    /**
     * 中等数据量阈值（使用分批查询）
     */
    private int mediumDataThreshold = 5000;

    /**
     * 大数据量阈值（使用 bitmap 查询）
     */
    private int largeDataThreshold = 10000;

    /**
     * 分批查询的批次大小
     */
    private int batchSize = 1000;

    /**
     * 是否启用查询优化
     */
    private boolean enableOptimization = true;

    /**
     * 是否启用查询性能监控
     */
    private boolean enablePerformanceMonitoring = false;

    /**
     * 慢查询阈值（毫秒）
     */
    private long slowQueryThreshold = 5000;

    /**
     * Campaign ID 查询优化配置
     */
    private CampaignQueryConfig campaign = new CampaignQueryConfig();

    /**
     * Ad Group 查询优化配置
     */
    private AdGroupQueryConfig adGroup = new AdGroupQueryConfig();

    /**
     * Product 查询优化配置
     */
    private ProductQueryConfig product = new ProductQueryConfig();

    @Data
    public static class CampaignQueryConfig {
        /**
         * 小数据量阈值
         */
        private int smallThreshold = 500;
        
        /**
         * 中等数据量阈值
         */
        private int mediumThreshold = 2000;
        
        /**
         * 是否使用 EXISTS 子查询优化
         */
        private boolean useExistsOptimization = true;
        
        /**
         * 是否使用临时表优化
         */
        private boolean useTempTableOptimization = true;
    }

    @Data
    public static class AdGroupQueryConfig {
        /**
         * 小数据量阈值
         */
        private int smallThreshold = 800;
        
        /**
         * 中等数据量阈值
         */
        private int mediumThreshold = 3000;
        
        /**
         * 是否使用批量查询
         */
        private boolean useBatchQuery = true;
    }

    @Data
    public static class ProductQueryConfig {
        /**
         * 小数据量阈值
         */
        private int smallThreshold = 1000;
        
        /**
         * 中等数据量阈值
         */
        private int mediumThreshold = 5000;
        
        /**
         * 是否使用分区查询
         */
        private boolean usePartitionQuery = true;
        
        /**
         * 分区大小
         */
        private int partitionSize = 1000;
    }

    /**
     * 根据查询类型获取对应的配置
     */
    public QueryConfig getConfigByType(String queryType) {
        switch (queryType.toLowerCase()) {
            case "campaign":
                return new QueryConfig(campaign.smallThreshold, campaign.mediumThreshold);
            case "adgroup":
                return new QueryConfig(adGroup.smallThreshold, adGroup.mediumThreshold);
            case "product":
                return new QueryConfig(product.smallThreshold, product.mediumThreshold);
            default:
                return new QueryConfig(smallDataThreshold, mediumDataThreshold);
        }
    }

    @Data
    public static class QueryConfig {
        private final int smallThreshold;
        private final int mediumThreshold;

        public QueryConfig(int smallThreshold, int mediumThreshold) {
            this.smallThreshold = smallThreshold;
            this.mediumThreshold = mediumThreshold;
        }
    }
}
