package com.meiyunji.sponsored.common.filter.interceptor;

import com.alibaba.druid.sql.ast.SQLStatement;
import lombok.Data;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * 拦截上下文
 * 包含拦截过程中需要的各种信息
 */
@Data
public class InterceptContext {
    
    /**
     * SQL语句
     */
    private String sql;
    
    /**
     * 解析后的SQL语句
     */
    private SQLStatement sqlStatement;
    
    /**
     * 涉及的表名
     */
    private Set<String> tableNames;
    
    /**
     * 是否是SELECT语句
     */
    private boolean isSelectSql;
    
    /**
     * 是否是UPDATE语句
     */
    private boolean isUpdateSql;
    
    /**
     * 是否是DELETE语句
     */
    private boolean isDeleteSql;
    
    /**
     * 白名单SQL集合
     */
    private Set<String> whitelistSqls;
    
    /**
     * 额外的上下文数据
     */
    private Map<String, Object> attributes = new HashMap<>();

    /**
     * 设置属性
     */
    public void setAttribute(String key, Object value) {
        attributes.put(key, value);
    }

    /**
     * 获取属性
     */
    @SuppressWarnings("unchecked")
    public <T> T getAttribute(String key) {
        return (T) attributes.get(key);
    }

    /**
     * 获取属性，带默认值
     */
    @SuppressWarnings("unchecked")
    public <T> T getAttribute(String key, T defaultValue) {
        T value = (T) attributes.get(key);
        return value != null ? value : defaultValue;
    }

    /**
     * 检查是否在白名单中
     */
    public boolean isInWhitelist() {
        return whitelistSqls != null && whitelistSqls.contains(sql);
    }
}
