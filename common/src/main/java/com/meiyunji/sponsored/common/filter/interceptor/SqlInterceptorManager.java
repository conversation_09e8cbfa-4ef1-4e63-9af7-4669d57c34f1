package com.meiyunji.sponsored.common.filter.interceptor;

import com.alibaba.druid.proxy.jdbc.PreparedStatementProxy;
import com.alibaba.druid.sql.ast.SQLStatement;
import com.alibaba.druid.util.JdbcConstants;
import com.meiyunji.sponsored.common.config.DynamicRefreshNacosConfiguration;
import com.meiyunji.sponsored.common.exception.BizServiceException;
import com.meiyunji.sponsored.common.springjdbc.SqlTypeParser;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Set;

/**
 * SQL拦截器管理器
 * 统一管理所有SQL拦截器，提供统一的拦截入口
 */
@Slf4j
@Component
public class SqlInterceptorManager {

    @Autowired
    private DynamicRefreshNacosConfiguration dynamicRefreshNacosConfiguration;

    @Autowired
    private List<SqlInterceptor> interceptors;

    private List<SqlInterceptor> sortedInterceptors;

    @PostConstruct
    public void init() {
        // 按优先级排序拦截器
        sortedInterceptors = new ArrayList<>(interceptors);
        sortedInterceptors.sort(Comparator.comparingInt(SqlInterceptor::getPriority));
        
        log.info("初始化SQL拦截器管理器，共加载{}个拦截器", sortedInterceptors.size());
        for (SqlInterceptor interceptor : sortedInterceptors) {
            log.info("  - {}: priority={}, enabled={}", 
                    interceptor.getName(), interceptor.getPriority(), interceptor.isEnabled());
        }
    }

    /**
     * 预编译阶段拦截
     */
    public void interceptCompileTime(String sql) {
        try {
            // 解析SQL
            List<SQLStatement> sqlStatements = SqlTypeParser.parseSql(sql, JdbcConstants.MYSQL.name());
            if (CollectionUtils.isEmpty(sqlStatements)) {
                log.debug("SQL解析失败，跳过拦截: {}", sql);
                return;
            }

            SQLStatement sqlStatement = sqlStatements.get(0);
            
            // 构建拦截上下文
            InterceptContext context = buildInterceptContext(sql, sqlStatement);
            
            // 执行拦截器链
            for (SqlInterceptor interceptor : sortedInterceptors) {
                InterceptResult result = interceptor.interceptCompileTime(sql, sqlStatement, context);
                
                if (!result.isPassed()) {
                    handleInterceptResult(result, interceptor, sql);
                }
            }
            
        } catch (Exception e) {
            log.error("预编译阶段拦截失败: sql={}", sql, e);
            // 根据配置决定是否抛出异常
        }
    }

    /**
     * 执行阶段拦截
     */
    public void interceptExecutionTime(PreparedStatementProxy statement) {
        try {
            String sql = statement.getSql();
            
            // 解析SQL
            List<SQLStatement> sqlStatements = SqlTypeParser.parseSql(sql, JdbcConstants.MYSQL.name());
            if (CollectionUtils.isEmpty(sqlStatements)) {
                log.debug("SQL解析失败，跳过执行阶段拦截: {}", sql);
                return;
            }

            SQLStatement sqlStatement = sqlStatements.get(0);
            
            // 构建拦截上下文
            InterceptContext context = buildInterceptContext(sql, sqlStatement);
            
            // 执行拦截器链
            for (SqlInterceptor interceptor : sortedInterceptors) {
                InterceptResult result = interceptor.interceptExecutionTime(statement, sqlStatement, context);
                
                if (!result.isPassed()) {
                    handleInterceptResult(result, interceptor, sql);
                }
            }
            
        } catch (Exception e) {
            log.error("执行阶段拦截失败: sql={}", statement.getSql(), e);
            // 根据配置决定是否抛出异常
        }
    }

    /**
     * 构建拦截上下文
     */
    private InterceptContext buildInterceptContext(String sql, SQLStatement sqlStatement) {
        InterceptContext context = new InterceptContext();
        context.setSql(sql);
        context.setSqlStatement(sqlStatement);
        context.setTableNames(SqlTypeParser.extractTableNames(sqlStatement));
        context.setSelectSql(SqlTypeParser.isSelectSql(sqlStatement));
        context.setUpdateSql(SqlTypeParser.isUpdateSql(sqlStatement));
        context.setDeleteSql(SqlTypeParser.isDeleteSql(sqlStatement));
        
        // 设置白名单
        Set<String> whitelistSqls = dynamicRefreshNacosConfiguration.getDruidFilterWhiteSqls();
        context.setWhitelistSqls(whitelistSqls);
        
        return context;
    }

    /**
     * 处理拦截结果
     */
    private void handleInterceptResult(InterceptResult result, SqlInterceptor interceptor, String sql) {
        String errorMessage = result.getErrorMessage();
        if (errorMessage == null) {
            errorMessage = interceptor.getErrorMessage();
        }
        
        log.warn("SQL被拦截: interceptor={}, sql={}, error={}", 
                interceptor.getName(), sql, errorMessage);
        
        if (result.isShouldThrowException()) {
            throw new BizServiceException(errorMessage);
        }
    }

    /**
     * 获取所有拦截器
     */
    public List<SqlInterceptor> getInterceptors() {
        return new ArrayList<>(sortedInterceptors);
    }

    /**
     * 获取启用的拦截器
     */
    public List<SqlInterceptor> getEnabledInterceptors() {
        return sortedInterceptors.stream()
                .filter(SqlInterceptor::isEnabled)
                .collect(ArrayList::new, ArrayList::add, ArrayList::addAll);
    }

    /**
     * 根据名称获取拦截器
     */
    public SqlInterceptor getInterceptor(String name) {
        return sortedInterceptors.stream()
                .filter(interceptor -> interceptor.getName().equals(name))
                .findFirst()
                .orElse(null);
    }
}
