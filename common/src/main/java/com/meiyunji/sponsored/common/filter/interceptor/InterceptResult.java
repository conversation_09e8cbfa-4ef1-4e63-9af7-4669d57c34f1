package com.meiyunji.sponsored.common.filter.interceptor;

import lombok.Data;

/**
 * 拦截结果
 */
@Data
public class InterceptResult {
    
    /**
     * 是否通过检查
     */
    private boolean passed;
    
    /**
     * 错误消息
     */
    private String errorMessage;
    
    /**
     * 是否应该抛出异常
     */
    private boolean shouldThrowException;
    
    /**
     * 额外的上下文信息
     */
    private Object context;

    public static InterceptResult pass() {
        InterceptResult result = new InterceptResult();
        result.setPassed(true);
        return result;
    }

    public static InterceptResult fail(String errorMessage) {
        InterceptResult result = new InterceptResult();
        result.setPassed(false);
        result.setErrorMessage(errorMessage);
        return result;
    }

    public static InterceptResult fail(String errorMessage, boolean shouldThrowException) {
        InterceptResult result = new InterceptResult();
        result.setPassed(false);
        result.setErrorMessage(errorMessage);
        result.setShouldThrowException(shouldThrowException);
        return result;
    }

    public InterceptResult withContext(Object context) {
        this.setContext(context);
        return this;
    }
}
