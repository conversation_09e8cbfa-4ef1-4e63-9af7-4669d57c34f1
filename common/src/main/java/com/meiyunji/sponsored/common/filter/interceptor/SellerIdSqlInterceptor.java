package com.meiyunji.sponsored.common.filter.interceptor;

import com.alibaba.druid.proxy.jdbc.JdbcParameter;
import com.alibaba.druid.proxy.jdbc.PreparedStatementProxy;
import com.alibaba.druid.sql.ast.SQLStatement;
import com.alibaba.druid.sql.ast.expr.*;
import com.alibaba.druid.sql.dialect.mysql.visitor.MySqlASTVisitorAdapter;
import com.meiyunji.sponsored.common.springjdbc.SqlTypeParser;
import com.meiyunji.sponsored.common.util.SqlConfigUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Set;

/**
 * SellerID字段拦截器
 * 检查SELECT语句是否包含有效的seller_id条件
 */
@Slf4j
@Component
public class SellerIdSqlInterceptor extends AbstractSqlInterceptor {

    @Autowired
    private SqlConfigUtil sqlConfigUtil;

    @Override
    public String getName() {
        return "SELLER_ID_INTERCEPTOR";
    }

    @Override
    public int getPriority() {
        return 200; // 中等优先级
    }

    @Override
    public boolean isEnabled() {
        return sqlConfigUtil.getSellerIdCheckEnable();
    }

    @Override
    protected boolean supportsExecutionTimeIntercept() {
        return sqlConfigUtil.getSellerIdExecutionCheckEnable();
    }

    @Override
    protected boolean shouldProcessSql(SQLStatement sqlStatement, InterceptContext context) {
        // 只处理SELECT语句
        if (!context.isSelectSql()) {
            return false;
        }

        // 检查是否涉及目标表
        Set<String> targetTables = sqlConfigUtil.getSellerIdCheckTablesSet();
        return involvesTargetTables(context.getTableNames(), targetTables);
    }

    @Override
    protected InterceptResult doInterceptCompileTime(String sql, SQLStatement sqlStatement, InterceptContext context) {
        Set<String> targetTables = sqlConfigUtil.getSellerIdCheckTablesSet();
        
        // 检查是否需要有效的seller_id字段
        boolean needsValidField = SqlTypeParser.needsValidFieldInWhere(sqlStatement, targetTables, "seller_id");
        
        if (needsValidField) {
            String reason = "SQL缺少有效的seller_id条件";
            logIntercept(sql, reason);
            return createFailResult(sqlConfigUtil.getSellerIdThrowError());
        }

        return InterceptResult.pass();
    }

    @Override
    protected InterceptResult doInterceptExecutionTime(PreparedStatementProxy statement, SQLStatement sqlStatement, InterceptContext context) {
        Set<String> targetTables = sqlConfigUtil.getSellerIdCheckTablesSet();
        
        // 使用参数值验证器检查实际参数值
        boolean hasValidSellerIdValue = validateSellerIdParameters(statement, targetTables);
        
        if (!hasValidSellerIdValue) {
            String reason = "seller_id参数值无效(null或空字符串)";
            logIntercept(statement.getSql(), reason);
            return createFailResult(sqlConfigUtil.getSellerIdThrowError());
        }

        return InterceptResult.pass();
    }

    @Override
    public String getErrorMessage() {
        return "SQL异常：查询特定表时必须包含有效的sellerId条件(不能为null或空字符串)!";
    }

    /**
     * 验证seller_id参数值
     */
    private boolean validateSellerIdParameters(PreparedStatementProxy statement, Set<String> targetTables) {
        try {
            String sql = statement.getSql();
            Map<Integer, JdbcParameter> parameters = statement.getParameters();
            
            // 使用访问器精确检查seller_id字段对应的参数值
            SellerIdParameterVisitor visitor = new SellerIdParameterVisitor(parameters);
            InterceptContext context = new InterceptContext();
            context.setSql(sql);
            context.setSqlStatement(SqlTypeParser.parseSql(sql, "mysql").get(0));
            context.setTableNames(SqlTypeParser.extractTableNames(context.getSqlStatement()));
            
            if (!involvesTargetTables(context.getTableNames(), targetTables)) {
                return true; // 不涉及目标表，跳过检查
            }
            
            context.getSqlStatement().accept(visitor);
            return visitor.hasValidSellerIdValue();
            
        } catch (Exception e) {
            log.error("seller_id参数值验证失败", e);
            return true; // 验证失败时默认通过
        }
    }

    /**
     * seller_id 参数访问器
     */
    private static class SellerIdParameterVisitor extends MySqlASTVisitorAdapter {
        private final Map<Integer, JdbcParameter> parameters;
        private boolean hasValidValue = false;
        private int globalParameterIndex = 0;

        public SellerIdParameterVisitor(Map<Integer, JdbcParameter> parameters) {
            this.parameters = parameters;
        }

        public boolean hasValidSellerIdValue() {
            return hasValidValue;
        }

        @Override
        public boolean visit(SQLBinaryOpExpr x) {
            SQLExpr left = x.getLeft();
            SQLExpr right = x.getRight();
            SQLBinaryOperator operator = x.getOperator();

            if (isSellerIdField(left)) {
                if (operator == SQLBinaryOperator.Equality) {
                    if (right instanceof SQLVariantRefExpr) {
                        Object paramValue = getNextParameterValue();
                        if (isValidParameterValue(paramValue)) {
                            hasValidValue = true;
                        }
                    } else {
                        if (isValidLiteralValue(right)) {
                            hasValidValue = true;
                        }
                    }
                } else if (operator == SQLBinaryOperator.NotEqual || 
                          operator == SQLBinaryOperator.LessThanOrGreater) {
                    if (right instanceof SQLVariantRefExpr) {
                        Object paramValue = getNextParameterValue();
                        if (isNullOrEmpty(paramValue)) {
                            hasValidValue = true;
                        }
                    }
                }
            }
            return true;
        }

        @Override
        public boolean visit(SQLInListExpr x) {
            SQLExpr expr = x.getExpr();
            
            if (isSellerIdField(expr)) {
                boolean isNot = x.isNot();
                
                if (!isNot) {
                    for (SQLExpr item : x.getTargetList()) {
                        if (item instanceof SQLVariantRefExpr) {
                            Object paramValue = getNextParameterValue();
                            if (isValidParameterValue(paramValue)) {
                                hasValidValue = true;
                                break;
                            }
                        } else if (isValidLiteralValue(item)) {
                            hasValidValue = true;
                            break;
                        }
                    }
                } else {
                    boolean allInvalid = true;
                    for (SQLExpr item : x.getTargetList()) {
                        if (item instanceof SQLVariantRefExpr) {
                            Object paramValue = getNextParameterValue();
                            if (isValidParameterValue(paramValue)) {
                                allInvalid = false;
                                break;
                            }
                        } else if (isValidLiteralValue(item)) {
                            allInvalid = false;
                            break;
                        }
                    }
                    if (allInvalid) {
                        hasValidValue = true;
                    }
                }
            }
            return true;
        }

        private boolean isSellerIdField(SQLExpr expr) {
            if (expr instanceof SQLIdentifierExpr) {
                String fieldName = ((SQLIdentifierExpr) expr).getName();
                return "seller_id".equalsIgnoreCase(fieldName);
            } else if (expr instanceof SQLPropertyExpr) {
                String fieldName = ((SQLPropertyExpr) expr).getName();
                return "seller_id".equalsIgnoreCase(fieldName);
            }
            return false;
        }

        private Object getNextParameterValue() {
            JdbcParameter param = parameters.get(globalParameterIndex);
            globalParameterIndex++;
            return param != null ? param.getValue() : null;
        }

        private boolean isValidParameterValue(Object value) {
            if (value == null) {
                return false;
            }
            if (value instanceof String) {
                String strValue = (String) value;
                return strValue.trim().length() > 0;
            }
            return true;
        }

        private boolean isValidLiteralValue(SQLExpr expr) {
            if (expr instanceof SQLNullExpr) {
                return false;
            }
            if (expr instanceof SQLCharExpr) {
                String value = ((SQLCharExpr) expr).getText();
                return StringUtils.isNotBlank(value);
            }
            return true;
        }

        private boolean isNullOrEmpty(Object value) {
            if (value == null) {
                return true;
            }
            if (value instanceof String) {
                return StringUtils.isBlank((String) value);
            }
            return false;
        }
    }
}
