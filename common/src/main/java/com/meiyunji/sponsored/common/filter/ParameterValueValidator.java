package com.meiyunji.sponsored.common.filter;

import com.alibaba.druid.proxy.jdbc.PreparedStatementProxy;
import com.alibaba.druid.sql.ast.SQLExpr;
import com.alibaba.druid.sql.ast.SQLStatement;
import com.alibaba.druid.sql.ast.expr.*;
import com.alibaba.druid.sql.dialect.mysql.visitor.MySqlASTVisitorAdapter;
import com.alibaba.druid.util.JdbcConstants;
import com.meiyunji.sponsored.common.springjdbc.SqlTypeParser;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 参数值验证器
 * 用于在SQL执行阶段验证参数值的有效性
 */
@Slf4j
public class ParameterValueValidator {

    /**
     * 验证PreparedStatement中的参数值
     */
    public static boolean validateSellerIdParameters(PreparedStatementProxy statement, Set<String> targetTables) {
        try {
            String sql = statement.getSql();
            List<Object> parameters = statement.getParameters();
            
            // 解析SQL
            List<SQLStatement> sqlStatements = SqlTypeParser.parseSql(sql, JdbcConstants.MYSQL.name());
            if (sqlStatements == null || sqlStatements.isEmpty()) {
                return true; // 解析失败，跳过检查
            }
            
            SQLStatement sqlStatement = sqlStatements.get(0);
            
            // 检查是否涉及目标表
            Set<String> tableNames = SqlTypeParser.extractTableNames(sqlStatement);
            boolean involvesTargetTable = tableNames.stream()
                    .anyMatch(tableName -> targetTables.contains(tableName.toLowerCase()));
            
            if (!involvesTargetTable) {
                return true; // 不涉及目标表，跳过检查
            }
            
            // 创建参数映射
            Map<Integer, Object> parameterMap = new HashMap<>();
            for (int i = 0; i < parameters.size(); i++) {
                parameterMap.put(i, parameters.get(i));
            }
            
            // 使用访问器检查参数值
            ParameterValueVisitor visitor = new ParameterValueVisitor("seller_id", parameterMap);
            sqlStatement.accept(visitor);
            
            return visitor.hasValidSellerIdValue();
            
        } catch (Exception e) {
            log.error("参数值验证失败", e);
            return true; // 验证失败时默认通过
        }
    }

    /**
     * 参数值访问器
     */
    private static class ParameterValueVisitor extends MySqlASTVisitorAdapter {
        private final String targetField;
        private final Map<Integer, Object> parameterMap;
        private boolean hasValidValue = false;
        private int currentParameterIndex = 0;

        public ParameterValueVisitor(String targetField, Map<Integer, Object> parameterMap) {
            this.targetField = targetField;
            this.parameterMap = parameterMap;
        }

        public boolean hasValidSellerIdValue() {
            return hasValidValue;
        }

        @Override
        public boolean visit(SQLBinaryOpExpr x) {
            SQLExpr left = x.getLeft();
            SQLExpr right = x.getRight();
            SQLBinaryOperator operator = x.getOperator();

            // 检查左侧是否是目标字段
            if (isTargetField(left)) {
                if (operator == SQLBinaryOperator.Equality) {
                    // seller_id = ?
                    if (right instanceof SQLVariantRefExpr) {
                        Object paramValue = getParameterValue();
                        if (isValidParameterValue(paramValue)) {
                            hasValidValue = true;
                        }
                    } else {
                        // seller_id = 'literal_value'
                        if (isValidLiteralValue(right)) {
                            hasValidValue = true;
                        }
                    }
                } else if (operator == SQLBinaryOperator.NotEqual || 
                          operator == SQLBinaryOperator.LessThanOrGreater) {
                    // seller_id != ? 或 seller_id <> ?
                    if (right instanceof SQLVariantRefExpr) {
                        Object paramValue = getParameterValue();
                        if (isNullOrEmpty(paramValue)) {
                            hasValidValue = true; // 不等于null或空字符串表示有有效值
                        }
                    }
                }
            }
            return true;
        }

        @Override
        public boolean visit(SQLInListExpr x) {
            SQLExpr expr = x.getExpr();
            
            if (isTargetField(expr)) {
                List<SQLExpr> targetList = x.getTargetList();
                boolean isNot = x.isNot();
                
                if (!isNot) {
                    // seller_id IN (?, ?, ...)
                    for (SQLExpr item : targetList) {
                        if (item instanceof SQLVariantRefExpr) {
                            Object paramValue = getParameterValue();
                            if (isValidParameterValue(paramValue)) {
                                hasValidValue = true;
                                break;
                            }
                        } else if (isValidLiteralValue(item)) {
                            hasValidValue = true;
                            break;
                        }
                    }
                } else {
                    // seller_id NOT IN (?, ?, ...)
                    boolean allInvalid = true;
                    for (SQLExpr item : targetList) {
                        if (item instanceof SQLVariantRefExpr) {
                            Object paramValue = getParameterValue();
                            if (isValidParameterValue(paramValue)) {
                                allInvalid = false;
                                break;
                            }
                        } else if (isValidLiteralValue(item)) {
                            allInvalid = false;
                            break;
                        }
                    }
                    if (allInvalid) {
                        hasValidValue = true;
                    }
                }
            }
            return true;
        }

        @Override
        public boolean visit(SQLVariantRefExpr x) {
            // 每次遇到参数占位符时递增索引
            currentParameterIndex++;
            return true;
        }

        /**
         * 检查表达式是否是目标字段
         */
        private boolean isTargetField(SQLExpr expr) {
            if (expr instanceof SQLIdentifierExpr) {
                String fieldName = ((SQLIdentifierExpr) expr).getName();
                return isTargetFieldName(fieldName);
            } else if (expr instanceof SQLPropertyExpr) {
                String fieldName = ((SQLPropertyExpr) expr).getName();
                return isTargetFieldName(fieldName);
            }
            return false;
        }

        /**
         * 检查字段名是否匹配目标字段
         */
        private boolean isTargetFieldName(String fieldName) {
            if (StringUtils.isBlank(fieldName)) {
                return false;
            }
            return targetField.equalsIgnoreCase(fieldName) || fieldName.contains(targetField);
        }

        /**
         * 获取当前参数值
         */
        private Object getParameterValue() {
            return parameterMap.get(currentParameterIndex - 1);
        }

        /**
         * 检查参数值是否有效
         */
        private boolean isValidParameterValue(Object value) {
            if (value == null) {
                return false;
            }
            if (value instanceof String) {
                return StringUtils.isNotBlank((String) value);
            }
            return true; // 非字符串类型认为有效
        }

        /**
         * 检查字面值是否有效
         */
        private boolean isValidLiteralValue(SQLExpr expr) {
            if (expr instanceof SQLNullExpr) {
                return false;
            }
            if (expr instanceof SQLCharExpr) {
                String value = ((SQLCharExpr) expr).getText();
                return StringUtils.isNotBlank(value);
            }
            return true; // 其他类型认为有效
        }

        /**
         * 检查值是否为null或空
         */
        private boolean isNullOrEmpty(Object value) {
            if (value == null) {
                return true;
            }
            if (value instanceof String) {
                return StringUtils.isBlank((String) value);
            }
            return false;
        }
    }
}
