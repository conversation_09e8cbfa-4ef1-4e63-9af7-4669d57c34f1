package com.meiyunji.sponsored.common.filter.interceptor;

import com.alibaba.druid.proxy.jdbc.PreparedStatementProxy;
import com.alibaba.druid.sql.ast.SQLStatement;
import com.meiyunji.sponsored.common.springjdbc.SqlTypeParser;
import lombok.extern.slf4j.Slf4j;

import java.util.Set;

/**
 * SQL拦截器抽象基类
 * 提供通用的拦截逻辑和工具方法
 */
@Slf4j
public abstract class AbstractSqlInterceptor implements SqlInterceptor {

    @Override
    public InterceptResult interceptCompileTime(String sql, SQLStatement sqlStatement, InterceptContext context) {
        try {
            // 白名单检查
            if (context.isInWhitelist()) {
                log.debug("SQL在白名单中，跳过检查: {}", sql);
                return InterceptResult.pass();
            }

            // 检查是否启用
            if (!isEnabled()) {
                return InterceptResult.pass();
            }

            // 检查是否需要处理此类型的SQL
            if (!shouldProcessSql(sqlStatement, context)) {
                return InterceptResult.pass();
            }

            // 执行具体的预编译检查
            return doInterceptCompileTime(sql, sqlStatement, context);

        } catch (Exception e) {
            log.error("预编译阶段拦截失败: interceptor={}, sql={}", getName(), sql, e);
            return handleException(e);
        }
    }

    @Override
    public InterceptResult interceptExecutionTime(PreparedStatementProxy statement, SQLStatement sqlStatement, InterceptContext context) {
        try {
            // 白名单检查
            if (context.isInWhitelist()) {
                log.debug("SQL在白名单中，跳过执行阶段检查: {}", statement.getSql());
                return InterceptResult.pass();
            }

            // 检查是否启用
            if (!isEnabled()) {
                return InterceptResult.pass();
            }

            // 检查是否支持执行阶段拦截
            if (!supportsExecutionTimeIntercept()) {
                return InterceptResult.pass();
            }

            // 检查是否需要处理此类型的SQL
            if (!shouldProcessSql(sqlStatement, context)) {
                return InterceptResult.pass();
            }

            // 执行具体的执行阶段检查
            return doInterceptExecutionTime(statement, sqlStatement, context);

        } catch (Exception e) {
            log.error("执行阶段拦截失败: interceptor={}, sql={}", getName(), statement.getSql(), e);
            return handleException(e);
        }
    }

    /**
     * 是否支持执行阶段拦截
     * 子类可以重写此方法来控制是否启用执行阶段拦截
     */
    protected boolean supportsExecutionTimeIntercept() {
        return false;
    }

    /**
     * 检查是否需要处理此SQL
     * 子类可以重写此方法来实现自定义的SQL过滤逻辑
     */
    protected boolean shouldProcessSql(SQLStatement sqlStatement, InterceptContext context) {
        return true;
    }

    /**
     * 执行具体的预编译阶段检查
     * 子类必须实现此方法
     */
    protected abstract InterceptResult doInterceptCompileTime(String sql, SQLStatement sqlStatement, InterceptContext context);

    /**
     * 执行具体的执行阶段检查
     * 子类可以重写此方法来实现执行阶段检查
     */
    protected InterceptResult doInterceptExecutionTime(PreparedStatementProxy statement, SQLStatement sqlStatement, InterceptContext context) {
        return InterceptResult.pass();
    }

    /**
     * 处理异常
     */
    protected InterceptResult handleException(Exception e) {
        // 默认情况下，异常时通过检查
        return InterceptResult.pass();
    }

    /**
     * 检查是否涉及目标表
     */
    protected boolean involvesTargetTables(Set<String> tableNames, Set<String> targetTables) {
        if (targetTables == null || targetTables.isEmpty()) {
            return false;
        }
        return tableNames.stream()
                .anyMatch(tableName -> targetTables.contains(tableName.toLowerCase()));
    }

    /**
     * 检查字段是否存在于WHERE条件中
     */
    protected boolean containsFieldInWhere(SQLStatement sqlStatement, String fieldName) {
        return SqlTypeParser.containsFieldInWhere(sqlStatement, fieldName);
    }

    /**
     * 检查字段值是否有效
     */
    protected boolean containsValidFieldInWhere(SQLStatement sqlStatement, String fieldName) {
        return SqlTypeParser.containsValidFieldInWhere(sqlStatement, fieldName);
    }

    /**
     * 创建失败结果
     */
    protected InterceptResult createFailResult(boolean shouldThrowException) {
        return InterceptResult.fail(getErrorMessage(), shouldThrowException);
    }

    /**
     * 记录拦截日志
     */
    protected void logIntercept(String sql, String reason) {
        log.warn("SQL被拦截: interceptor={}, reason={}, sql={}", getName(), reason, sql);
    }
}
