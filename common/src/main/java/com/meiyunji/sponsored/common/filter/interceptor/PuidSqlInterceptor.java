package com.meiyunji.sponsored.common.filter.interceptor;

import com.alibaba.druid.sql.ast.SQLStatement;
import com.meiyunji.sponsored.common.springjdbc.SqlTypeParser;
import com.meiyunji.sponsored.common.util.SqlConfigUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * PUID字段拦截器
 * 检查UPDATE和DELETE语句是否包含puid条件
 */
@Slf4j
@Component
public class PuidSqlInterceptor extends AbstractSqlInterceptor {

    @Autowired
    private SqlConfigUtil sqlConfigUtil;

    @Override
    public String getName() {
        return "PUID_INTERCEPTOR";
    }

    @Override
    public int getPriority() {
        return 100; // 较高优先级
    }

    @Override
    public boolean isEnabled() {
        return sqlConfigUtil.getStatEnable();
    }

    @Override
    protected boolean shouldProcessSql(SQLStatement sqlStatement, InterceptContext context) {
        // 只处理UPDATE和DELETE语句
        return context.isUpdateSql() || context.isDeleteSql();
    }

    @Override
    protected InterceptResult doInterceptCompileTime(String sql, SQLStatement sqlStatement, InterceptContext context) {
        // 检查是否包含puid字段
        boolean containsPuid = containsFieldInWhere(sqlStatement, "puid");
        
        if (!containsPuid) {
            String reason = String.format("SQL缺少puid条件: type=%s", 
                    context.isUpdateSql() ? "UPDATE" : "DELETE");
            logIntercept(sql, reason);
            return createFailResult(sqlConfigUtil.getThrowError());
        }

        return InterceptResult.pass();
    }

    @Override
    public String getErrorMessage() {
        return "SQL异常：更新或删除操作必须包含puid条件!";
    }
}
