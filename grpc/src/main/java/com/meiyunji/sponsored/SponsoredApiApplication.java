package com.meiyunji.sponsored;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.data.redis.RedisRepositoriesAutoConfiguration;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.ApplicationContext;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import javax.annotation.PostConstruct;
import java.util.TimeZone;

@SpringBootApplication(exclude = {RedisRepositoriesAutoConfiguration.class})
@EnableAsync // 启动异步调用
//@EnableTransactionManagement // 开启事务注解
@EnableDiscoveryClient
@EnableFeignClients
@EnableTransactionManagement
public class SponsoredApiApplication {
    final ApplicationContext applicationContext;

    public SponsoredApiApplication(ApplicationContext applicationContext) {
        this.applicationContext = applicationContext;
    }

    public static void main(String[] args) {
        SpringApplication.run(SponsoredApiApplication.class, args);
    }

    @PostConstruct
    public void init() {
        //设置系统时区
        TimeZone.setDefault(TimeZone.getTimeZone("Asia/Shanghai"));
        //解决druid warning
        System.setProperty("druid.mysql.usePingMethod","false");
    }
}
