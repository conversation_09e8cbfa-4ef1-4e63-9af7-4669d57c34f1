package com.meiyunji.sponsored.api.sd;

import com.google.protobuf.Int32Value;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.util.JSONUtil;
import com.meiyunji.sponsored.rpc.adCommon.AsyncAddTargetingRequest;
import com.meiyunji.sponsored.rpc.sd.neTargeting.*;
import com.meiyunji.sponsored.rpc.vo.AsyncAddTargetingRpcVo;
import com.meiyunji.sponsored.rpc.vo.CommonResponse;
import com.meiyunji.sponsored.service.cpc.constants.AdTargetObjectTypeEnum;
import com.meiyunji.sponsored.service.cpc.constants.AdTargetTaskConstant;
import com.meiyunji.sponsored.service.cpc.constants.AdTargetTaskMatchTypeEnum;
import com.meiyunji.sponsored.service.cpc.constants.AdTargetTaskTypeEnum;
import com.meiyunji.sponsored.service.cpc.dto.AdTargetTaskDto;
import com.meiyunji.sponsored.service.cpc.po.TargetTypeEnum;
import com.meiyunji.sponsored.service.cpc.service2.IAdTargetTaskService;
import com.meiyunji.sponsored.service.cpc.service2.sd.ICpcSdNeTargetingService;
import com.meiyunji.sponsored.service.cpc.vo.AddSdNeTargetingVo;
import com.meiyunji.sponsored.service.cpc.vo.SdNeTargetingVo;
import io.grpc.stub.StreamObserver;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.lognet.springboot.grpc.GRpcService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @author: wade
 * @date: 2021/10/28 18:49
 * @describe: sd 否定投放rpc接口
 */
@GRpcService
@Slf4j
public class NeTargetingSdRpcService extends RPCSdNeTargetingServiceGrpc.RPCSdNeTargetingServiceImplBase {

    @Autowired
    private ICpcSdNeTargetingService cpcSdNeTargetingService;
    @Autowired
    private IAdTargetTaskService adTargetTaskService;

    /**
     * 创建
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void create(CreateSdNeTargetingRequest request, StreamObserver<CommonResponse> responseObserver) {
        log.info("sd-neTargeting创建 request {}", request);
        CommonResponse.Builder builder = CommonResponse.newBuilder();

        if (!request.hasPuid() || !request.hasShopId() || !request.hasUid() ||
                StringUtils.isBlank(request.getGroupId())
                || CollectionUtils.isEmpty(request.getSdNeTargetingVoList())) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
        } else {
            AddSdNeTargetingVo vo = new AddSdNeTargetingVo();
            vo.setShopId(request.getShopId().getValue());
            vo.setPuid(request.getPuid().getValue());
            vo.setUid(request.getUid().getValue());
            vo.setGroupId(request.getGroupId());
            //处理list
            List<SdNeTargetingRpcVo> rpcVos = request.getSdNeTargetingVoList();
            if (CollectionUtils.isNotEmpty(rpcVos)) {
                List<SdNeTargetingVo> vos = rpcVos.stream().filter(Objects::nonNull).map(item -> {
                    SdNeTargetingVo sdNeTargetingVo = new SdNeTargetingVo();
                    BeanUtils.copyProperties(item, sdNeTargetingVo);
                    return sdNeTargetingVo;
                }).collect(Collectors.toList());
                vo.setNeTargetings(vos);
            }
            //处理业务返回结果
            Result res = cpcSdNeTargetingService.create(vo,request.getLoginIp());
            builder.setCode(Int32Value.of(res.getCode()));
            if (StringUtils.isNotBlank(res.getMsg())) {
                builder.setMsg(res.getMsg());
            }
        }

        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void asyncCreate(AsyncAddTargetingRequest request, StreamObserver<CommonResponse> responseObserver) {
        log.info("sd-async-targeting-创建否定投放 request {}", request);
        CommonResponse.Builder builder = CommonResponse.newBuilder();
        String error = verifyAsyncNeParam(request);
        if (StringUtils.isNotBlank(error)) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg(error);
        } else {
            AdTargetTaskDto adTargetTaskDto = new AdTargetTaskDto();
            adTargetTaskDto.setPuid(request.getPuid().getValue());
            adTargetTaskDto.setShopId(request.getShopId().getValue());
            adTargetTaskDto.setUid(request.getUid().getValue());
            adTargetTaskDto.setType(AdTargetTaskTypeEnum.SD_NEGATIVE_TARGETS.getCode());
            adTargetTaskDto.setLoginIp(request.getLoginIp());
            adTargetTaskDto.setTargetingType(request.getTargetingType());
            adTargetTaskDto.setTargetPageType(request.getTargetingPageType().getValue());
            adTargetTaskDto.setSourceAdCampaignId(request.getSourceAdCampaignId());
            adTargetTaskDto.setSourceShopId(request.getSourceShopId().getValue());
            List<AdTargetTaskDto.AdTargetTaskDetailDto> detailList = request.getTargetingList().stream().filter(Objects::nonNull).map(item -> {
                AdTargetTaskDto.AdTargetTaskDetailDto detail = new AdTargetTaskDto.AdTargetTaskDetailDto();
                if (item.hasSourceShopId()) {
                    detail.setSourceShopId(item.getSourceShopId().getValue());
                }  else {
                    detail.setSourceShopId(request.getSourceShopId().getValue());
                }
                detail.setAdCampaignId(item.getAdCampaignId());
                detail.setAdGroupId(item.getAdGroupId());
                detail.setMatchType(item.getExpressionType());
                if (StringUtils.isNotBlank(item.getTargetId())) {
                    detail.setTargetId(item.getTargetId());
                } else {
                    detail.setTargetObject(item.getAsin());
                    detail.setTargetObjectDesc(item.getTitle());
                    detail.setImgUrl(item.getImgUrl());
                }
                detail.setTargetObjectType(AdTargetObjectTypeEnum.getCodeByTargetType(item.getType()));
                return detail;
            }).collect(Collectors.toList());
            adTargetTaskDto.setTaskDetails(detailList);
            long taskId = adTargetTaskService.recordTargetTask(adTargetTaskDto);
            adTargetTaskService.executeTask(taskId);
        }

        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    private String verifyAsyncNeParam(AsyncAddTargetingRequest request) {
        if (!request.hasShopId() || !request.hasPuid() || !request.hasTargetingPageType()
                || CollectionUtils.isEmpty(request.getTargetingList()) || request.getTargetingList().size() > AdTargetTaskConstant.MAX_TARGET_SIZE) {
            return "请求参数错误";
        }
        // 如果第一个包含targetId,那么列表中所有的对象都需要包含targetId
        int targetIdNum = 0;
        List<AsyncAddTargetingRpcVo> list = request.getTargetingList();
        for (AsyncAddTargetingRpcVo each : list) {
            boolean haveTargetId = StringUtils.isNotBlank(each.getTargetId());
            if (haveTargetId) {
                targetIdNum++;
            }
            if (!AdTargetTaskMatchTypeEnum.SD_NE_TARGET_SUPPORT_TYPES.contains(each.getExpressionType())) {
                return "请求参数错误,存在不支持的筛选条件";
            }
            if (!AdTargetObjectTypeEnum.SD_NE_TARGET_SUPPORT_TYPES.contains(each.getType())) {
                return "请求参数错误,存在不支持的投放类型";
            }
            if (TargetTypeEnum.asin.name().equals(each.getType()) && !haveTargetId && StringUtils.isBlank(each.getAsin())) {
                return "请求参数错误";
            }
        }
        if (targetIdNum > 0 && targetIdNum < list.size() || (!request.hasSourceShopId() && !request.getTargetingList().get(0).hasSourceShopId()) || StringUtils.isBlank(request.getTargetingType())) {
            return "请求参数错误";
        }
        return StringUtils.EMPTY;
    }

    /**
     * 归档
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void archive(ArchiveSdNeTargetingRequest request, StreamObserver<CommonResponse> responseObserver) {
        log.info("sd-neTargeting归档 request {}", request);
        CommonResponse.Builder builder = CommonResponse.newBuilder();

        if (!request.hasPuid() || !request.hasShopId() || !request.hasUid()
                || StringUtils.isBlank(request.getTargetId())) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
        } else {
            Result res = cpcSdNeTargetingService.archive(request.getPuid().getValue(), request.getShopId().getValue(), request.getUid().getValue(), request.getTargetId(), request.getLoginIp());
            builder.setCode(Int32Value.of(res.getCode()));
            if (StringUtils.isNotBlank(res.getMsg())) {
                builder.setMsg(res.getMsg());
            }
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * SD否定投放批量归档
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void batchArchive(BatchArchiveSdNeTargetingRequest request, StreamObserver<CommonResponse> responseObserver) {
        log.info("sd-否定投放-批量归档 request {}", request);
        CommonResponse.Builder builder = CommonResponse.newBuilder();

        if (!request.hasShopId() || !request.hasIdList() || !request.hasIdList() || !request.hasPuid() || !request.hasUid()) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("参数有误");
        } else {
            List<String> idList = Arrays.stream(request.getIdList().split(",")).collect(Collectors.toList());
            Result result = cpcSdNeTargetingService.batchArchive(request.getPuid().getValue(), request.getShopId().getValue(),
                    request.getUid().getValue(), idList, request.getIp());
            builder.setCode(Int32Value.newBuilder().setValue(result.getCode()).build());
            if (result.getData() != null) {
                builder.setData(JSONUtil.objectToJson(result.getData()));
            }
            if (result.getMsg() != null) {
                builder.setMsg(result.getMsg());
            }
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * 批量创建
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void batchAddNeTargeting(CreateSdNeTargetingRequest request, StreamObserver<BatchSdNetargetingResponse> responseObserver) {
        log.info("sd-批量添加多个广告活动的否定商品投放 request {}", request);
        BatchSdNetargetingResponse.Builder builder = BatchSdNetargetingResponse.newBuilder();
        if (!request.hasPuid() || !request.hasShopId() || !request.hasUid()
                || CollectionUtils.isEmpty(request.getSdNeTargetingVoList())) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
        } else {
            AddSdNeTargetingVo vo = new AddSdNeTargetingVo();
            vo.setShopId(request.getShopId().getValue());
            vo.setPuid(request.getPuid().getValue());
            vo.setUid(request.getUid().getValue());
            //处理list
            List<SdNeTargetingRpcVo> rpcVos = request.getSdNeTargetingVoList();
            if (CollectionUtils.isNotEmpty(rpcVos)) {
                List<SdNeTargetingVo> vos = rpcVos.stream().filter(Objects::nonNull).map(item -> {
                    SdNeTargetingVo sdNeTargetingVo = new SdNeTargetingVo();
                    BeanUtils.copyProperties(item, sdNeTargetingVo);
                    return sdNeTargetingVo;
                }).collect(Collectors.toList());
                vo.setNeTargetings(vos);
            }
            //处理业务返回结果
            Result<BatchSdDataResponse> result = cpcSdNeTargetingService.batchAddNeTarget(vo);
            // 错误信息返回：code = error返回给result.msg; code = success返回给data里的failmsg
            builder.setCode(Int32Value.of(result.getCode()));
            if (result.error()) {
                builder.setMsg(result.getMsg());
            }
            if (result.getData() != null) {
                builder.setData(result.getData());
            }
        }

        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }
}
