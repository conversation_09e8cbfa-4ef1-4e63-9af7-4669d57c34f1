package com.meiyunji.sponsored.api.newDashboard;


import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.rpc.newDashboard.DashboardQueryTimeLimitConfigRequest;
import com.meiyunji.sponsored.rpc.newDashboard.DashboardQueryTimeLimitConfigResponse;
import com.meiyunji.sponsored.rpc.newDashboard.RpcGetQueryTimeLimitConfigServiceGrpc;
import com.meiyunji.sponsored.service.config.DashboardQueryDateConfig;
import io.grpc.stub.StreamObserver;
import lombok.extern.slf4j.Slf4j;
import org.lognet.springboot.grpc.GRpcService;
import javax.annotation.Resource;
import java.util.Map;



/**
 * <AUTHOR>
 * @date 2024/12/09
 */
@GRpcService
@Slf4j
public class DashboardGetConfigRpcService extends RpcGetQueryTimeLimitConfigServiceGrpc.RpcGetQueryTimeLimitConfigServiceImplBase {

    @Resource
    private DashboardQueryDateConfig dashboardQueryDateConfig;

    @Override
    public void getQueryTimeLimitConfig(DashboardQueryTimeLimitConfigRequest request, StreamObserver<DashboardQueryTimeLimitConfigResponse> responseObserver) {
        log.info("dashboard query getQueryTimeLimitConfig , request data: {}", request);
        DashboardQueryTimeLimitConfigResponse.Builder builder = DashboardQueryTimeLimitConfigResponse.newBuilder();
        Map<String, Integer> dashboardQueryTimeAll = dashboardQueryDateConfig.getDashboardQueryTimeAll();
        builder.setCode(Result.SUCCESS);
        builder.putAllData(dashboardQueryTimeAll);
        log.info("dashboard query getQueryTimeLimitConfig, response data: {}", dashboardQueryTimeAll);
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }




}
