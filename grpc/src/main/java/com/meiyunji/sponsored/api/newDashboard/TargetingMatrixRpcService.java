package com.meiyunji.sponsored.api.newDashboard;

import com.google.common.collect.Range;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.rpc.newDashboard.DashboardAdTargetingMatrixRequest;
import com.meiyunji.sponsored.rpc.newDashboard.DashboardAdTargetingMatrixResponse;
import com.meiyunji.sponsored.rpc.newDashboard.DashboardAdTargetingMatrixVo;
import com.meiyunji.sponsored.rpc.newDashboard.RpcDashboardAdTargetingMatrixServiceGrpc;
import com.meiyunji.sponsored.service.config.DashboardQueryDateConfig;
import com.meiyunji.sponsored.service.newDashboard.enums.*;
import com.meiyunji.sponsored.service.newDashboard.service.IDashboardAdTargetingMatrixService;
import io.grpc.stub.StreamObserver;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTime;
import org.joda.time.Days;
import org.joda.time.format.DateTimeFormat;
import org.lognet.springboot.grpc.GRpcService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;

/**
 * @author: ys
 * @date: 2024/4/23 14:17
 * @describe:
 */
@GRpcService
@Slf4j
public class TargetingMatrixRpcService extends RpcDashboardAdTargetingMatrixServiceGrpc.RpcDashboardAdTargetingMatrixServiceImplBase {

    private static final Range<Integer> LIMIT_RANGE = Range.closed(20,1000);

    @Autowired
    private IDashboardAdTargetingMatrixService dashboardAdTargetingMatrixService;

    @Resource
    private DashboardQueryDateConfig dashboardQueryDateConfig;

    @Override
    public void getAdTargetingMatrix(DashboardAdTargetingMatrixRequest request, StreamObserver<DashboardAdTargetingMatrixResponse> responseObserver) {
        //根据queryField调用不同层级的service
        log.info("dashboard query ad keyword and targeting matrix charts, request data: {}", request);
        StopWatch sw = new StopWatch();
        sw.start();
        DashboardAdTargetingMatrixResponse.Builder builder = DashboardAdTargetingMatrixResponse.newBuilder();
        if (!checkParam(request, false)) {
            log.error("dashboard query ad keyword and targeting matrix charts, check param error");
            builder.setCode(Result.ERROR);
            builder.setMsg("请求参数错误");
        } else {
            request = resetTimeRange(request, DashboardModuleEnum.TARGETING_MATRIX);
            DashboardAdTargetingMatrixVo data = dashboardAdTargetingMatrixService.getKeywordAndTargetingMatrix(request);
            builder.setData(data);
            builder.setCode(Result.SUCCESS);
            sw.stop();
            log.info("dashboard query ad keyword and targeting matrix charts, puid: {}, 耗时: {}秒", request.getPuid(), sw.getTotalTimeSeconds());
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    private boolean checkParam(DashboardAdTargetingMatrixRequest req, boolean export) {
        //校验值是否合法
        if (!DashboardCurrencyEnum.currencySet.contains(req.getCurrency())) {
            log.error("dashboard query ad keyword and targeting matrix charts, req param error, currency is null:{}", req.getCurrency());
            return false;
        }
        if (!export && !LIMIT_RANGE.contains(req.getLimit())) {
            log.error("dashboard query ad keyword and targeting matrix charts, req param error, limit is null:{}", req.getLimit());
            return false;
        }
        if (!DashboardDataFieldEnum.fieldMap.containsKey(req.getDataField())) {
            log.error("dashboard query ad keyword and targeting matrix charts, req param error, dataField is null:{}", req.getDataField());
            return false;
        }
        if (!DashboardOrderByRateEnum.rateMap.containsKey(req.getOrderByField())) {
            log.error("dashboard query ad keyword and targeting matrix charts, req param error, orderField is null:{}", req.getOrderByField());
            return false;
        }
        if (!DashboardOrderByEnum.orderByMap.containsKey(req.getOrderBy())) {
            log.error("dashboard query ad keyword and targeting matrix charts, req param error, orderBy is null:{}", req.getOrderBy());
            return false;
        }
        return true;
    }

    private DashboardAdTargetingMatrixRequest resetTimeRange(DashboardAdTargetingMatrixRequest request, DashboardModuleEnum moduleEnum) {
        Integer queryTimeLimit = moduleEnum.getQueryTimeLimit();
        Integer queryTime = dashboardQueryDateConfig.getDashboardQueryTime(moduleEnum.name());
        if (queryTime != null) {
            queryTimeLimit = queryTime;
        }
        DashboardAdTargetingMatrixRequest.Builder newBuilder = request.toBuilder();
        DateTime startDateTime = DateTime.parse(request.getStartDate(), DateTimeFormat.forPattern(DateUtil.PATTERN_YYYYMMDD));
        DateTime endDateTime = DateTime.parse(request.getEndDate(), DateTimeFormat.forPattern(DateUtil.PATTERN_YYYYMMDD));
        int days = Days.daysBetween(startDateTime, endDateTime).getDays();
        if (days > queryTimeLimit) {
            startDateTime = endDateTime.minusDays(queryTimeLimit - 1);
            newBuilder.setStartDate(startDateTime.toString(DateUtil.PATTERN_YYYYMMDD));
        }
        return newBuilder.build();
    }
}
