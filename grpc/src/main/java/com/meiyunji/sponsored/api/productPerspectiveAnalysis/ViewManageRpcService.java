package com.meiyunji.sponsored.api.productPerspectiveAnalysis;

import com.amazon.advertising.mode.targeting.ExpressionEnum;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.common.util.StringUtil;
import com.meiyunji.sponsored.common.util.UCommonUtil;
import com.meiyunji.sponsored.rpc.productPerspectiveAnalysis.viewManage.*;
import com.meiyunji.sponsored.rpc.vo.AdAdvancedFilterData;
import com.meiyunji.sponsored.service.cpc.constants.BudgetStateEnum;
import com.meiyunji.sponsored.service.cpc.po.*;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.vo.CampaignPageVo;
import com.meiyunji.sponsored.service.enums.*;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.qo.*;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.service.IViewManageService;
import io.grpc.stub.StreamObserver;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.lognet.springboot.grpc.GRpcService;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * @author: liweibin
 * @email: <EMAIL>
 * @date: 2023-08-28  16:52
 */
@GRpcService
@Slf4j
public class ViewManageRpcService extends RpcViewManageServiceGrpc.RpcViewManageServiceImplBase {
    @Autowired
    private IViewManageService viewManageService;

    /**
     * 查询广告活动视图（列表页、汇总）
     */
    @Override
    public void getCampaignView(CampaignViewRequest request, StreamObserver<CampaignViewResponse> responseObserver) {
        log.info("查询广告活动视图 {}", request);
        long t = Instant.now().toEpochMilli();
        CampaignViewResponse.Builder builder = CampaignViewResponse.newBuilder();
        if (StringUtils.isBlank(request.getSearchValue()) || StringUtils.isBlank(request.getSearchType())
                || ViewBaseParam.SearchTypeEnum.getEnum(request.getSearchType()) == null) {
            CampaignViewResponse.CampaignVo.Builder campaignVo = CampaignViewResponse.CampaignVo.newBuilder();
            //总汇总初始化
            campaignVo.setAggregateViewVo(viewManageService.buildAggregateViewVo(null));
            CampaignViewResponse.CampaignVo.Page.Builder pageBuild = CampaignViewResponse.CampaignVo.Page.newBuilder();
            pageBuild.setPageNo(request.hasPageNo() ? request.getPageNo() : 0);
            pageBuild.setPageSize(request.hasPageSize() ? request.getPageSize() : 0);
            pageBuild.setTotalPage(0);
            pageBuild.setTotalSize(0);
            campaignVo.setPage(pageBuild.build());
            builder.setCode(Result.SUCCESS);
            builder.setData(campaignVo);
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
            return;
        }

        CampaignViewParam campaignViewParam = new CampaignViewParam();
        campaignViewParam.setPageSign(request.getPageSign());
        campaignViewParam.setPageNo(request.getPageNo());
        campaignViewParam.setPageSize(request.getPageSize());
        campaignViewParam.setMarketplaceId(request.getMarketplaceId());
        campaignViewParam.setShopIdList(request.getShopIdListList());
        campaignViewParam.setSearchType(request.getSearchType());
        campaignViewParam.setSearchValue(request.getSearchValue());
        campaignViewParam.setType(request.getType());
        campaignViewParam.setStartDate(request.getStartDate());
        campaignViewParam.setEndDate(request.getEndDate());
        campaignViewParam.setOrderField(request.getOrderField());
        campaignViewParam.setOrderType(request.getOrderType());

        campaignViewParam.setStrategyType(request.getStrategyType());
        campaignViewParam.setBudgetState(request.getBudgetState());
        campaignViewParam.setStatus(request.getStatus());
        campaignViewParam.setServingStatus(request.getServingStatus());
        campaignViewParam.setTargetingType(request.getTargetingType());
        campaignViewParam.setQueryValue(request.getQueryValue());
        campaignViewParam.setQueryType(request.getQueryType());

        campaignViewParam.setPuid(request.getPuid());
        campaignViewParam.setUid(request.getUid());
        //环比数据传参
        campaignViewParam.setIsCompare(request.getIsCompare());
        campaignViewParam.setCompareStartDate(request.getCompareStartDate());
        campaignViewParam.setCompareEndDate(request.getCompareEndDate());

        if (request.hasUseAdvanced()) {  //是否开启高级搜索
            campaignViewParam.setUseAdvanced(request.getUseAdvanced());
        }

        if (request.hasUseAdvanced() && request.getUseAdvanced()) {  //高级筛选
            AdAdvancedFilterData advancedFilter = request.getAdvancedFilter();
            campaignViewParam.setImpressionsMin(advancedFilter.hasImpressionsMin() ? advancedFilter.getImpressionsMin() : null);
            campaignViewParam.setImpressionsMax(advancedFilter.hasImpressionsMax() ? advancedFilter.getImpressionsMax() : null);
            campaignViewParam.setClicksMin(advancedFilter.hasClicksMin() ? advancedFilter.getClicksMin() : null);
            campaignViewParam.setClicksMax(advancedFilter.hasClicksMax() ? advancedFilter.getClicksMax() : null);
            campaignViewParam.setClickRateMin(advancedFilter.hasClickRateMin() ? BigDecimal.valueOf(advancedFilter.getClickRateMin()) : null);
            campaignViewParam.setClickRateMax(advancedFilter.hasClickRateMax() ? BigDecimal.valueOf(advancedFilter.getClickRateMax()) : null);
            campaignViewParam.setCostMin(advancedFilter.hasCostMin() ? BigDecimal.valueOf(advancedFilter.getCostMin()) : null);
            campaignViewParam.setCostMax(advancedFilter.hasCostMax() ? BigDecimal.valueOf(advancedFilter.getCostMax()) : null);
            campaignViewParam.setCpcMin(advancedFilter.hasCpcMin() ? BigDecimal.valueOf(advancedFilter.getCpcMin()) : null);
            campaignViewParam.setCpcMax(advancedFilter.hasCpcMax() ? BigDecimal.valueOf(advancedFilter.getCpcMax()) : null);
            campaignViewParam.setOrderNumMin(advancedFilter.hasOrderNumMin() ? advancedFilter.getOrderNumMin() : null);
            campaignViewParam.setOrderNumMax(advancedFilter.hasOrderNumMax() ? advancedFilter.getOrderNumMax() : null);
            campaignViewParam.setSalesMin(advancedFilter.hasSalesMin() ? BigDecimal.valueOf(advancedFilter.getSalesMin()) : null);
            campaignViewParam.setSalesMax(advancedFilter.hasSalesMax() ? BigDecimal.valueOf(advancedFilter.getSalesMax()) : null);
            campaignViewParam.setAcosMin(advancedFilter.hasAcosMin() ? BigDecimal.valueOf(advancedFilter.getAcosMin()) : null);
            campaignViewParam.setAcosMax(advancedFilter.hasAcosMax() ? BigDecimal.valueOf(advancedFilter.getAcosMax()) : null);
            campaignViewParam.setRoasMin(advancedFilter.hasRoasMin() ? BigDecimal.valueOf(advancedFilter.getRoasMin()) : null);
            campaignViewParam.setRoasMax(advancedFilter.hasRoasMax() ? BigDecimal.valueOf(advancedFilter.getRoasMax()) : null);
            campaignViewParam.setSalesConversionRateMin(advancedFilter.hasSalesConversionRateMin() ? BigDecimal.valueOf(advancedFilter.getSalesConversionRateMin()) : null);
            campaignViewParam.setSalesConversionRateMax(advancedFilter.hasSalesConversionRateMax() ? BigDecimal.valueOf(advancedFilter.getSalesConversionRateMax()) : null);
            campaignViewParam.setAcotsMin(advancedFilter.hasAcotsMin() ? BigDecimal.valueOf(advancedFilter.getAcotsMin()) : null);
            campaignViewParam.setAcotsMax(advancedFilter.hasAcotsMax() ? BigDecimal.valueOf(advancedFilter.getAcotsMax()) : null);
            campaignViewParam.setAsotsMin(advancedFilter.hasAsotsMin() ? BigDecimal.valueOf(advancedFilter.getAsotsMin()) : null);
            campaignViewParam.setAsotsMax(advancedFilter.hasAsotsMax() ? BigDecimal.valueOf(advancedFilter.getAsotsMax()) : null);
            campaignViewParam.setViewImpressionsMin(advancedFilter.hasViewImpressionsMin() ? advancedFilter.getViewImpressionsMin() : null);
            campaignViewParam.setViewImpressionsMax(advancedFilter.hasViewImpressionsMax() ? advancedFilter.getViewImpressionsMax() : null);
            campaignViewParam.setCpaMin(advancedFilter.hasCpaMin() ? BigDecimal.valueOf(advancedFilter.getCpaMin()) : null);
            campaignViewParam.setCpaMax(advancedFilter.hasCpaMax() ? BigDecimal.valueOf(advancedFilter.getCpaMax()) : null);
            campaignViewParam.setVcpmMin(advancedFilter.hasVcpmMin() ? BigDecimal.valueOf(advancedFilter.getVcpmMin()) : null);
            campaignViewParam.setVcpmMax(advancedFilter.hasVcpmMax() ? BigDecimal.valueOf(advancedFilter.getVcpmMax()) : null);
            campaignViewParam.setAdSaleNumMin(advancedFilter.hasAdSaleNumMin() ? advancedFilter.getAdSaleNumMin() : null);
            campaignViewParam.setAdSaleNumMax(advancedFilter.hasAdSaleNumMax() ? advancedFilter.getAdSaleNumMax() : null);
            campaignViewParam.setAdOtherOrderNumMin(advancedFilter.hasAdOtherOrderNumMin() ? advancedFilter.getAdOtherOrderNumMin() : null);
            campaignViewParam.setAdOtherOrderNumMax(advancedFilter.hasAdOtherOrderNumMax() ? advancedFilter.getAdOtherOrderNumMax() : null);
            campaignViewParam.setAdSalesMin(advancedFilter.hasAdSalesMin() ? BigDecimal.valueOf(advancedFilter.getAdSalesMin()) : null);
            campaignViewParam.setAdSalesMax(advancedFilter.hasAdSalesMax() ? BigDecimal.valueOf(advancedFilter.getAdSalesMax()) : null);
            campaignViewParam.setAdOtherSalesMin(advancedFilter.hasAdOtherSalesMin() ? BigDecimal.valueOf(advancedFilter.getAdOtherSalesMin()) : null);
            campaignViewParam.setAdOtherSalesMax(advancedFilter.hasAdOtherSalesMax() ? BigDecimal.valueOf(advancedFilter.getAdOtherSalesMax()) : null);
            campaignViewParam.setAdSelfSaleNumMin(advancedFilter.hasAdSelfSaleNumMin() ? advancedFilter.getAdSelfSaleNumMin() : null);
            campaignViewParam.setAdSelfSaleNumMax(advancedFilter.hasAdSelfSaleNumMax() ? advancedFilter.getAdSelfSaleNumMax() : null);
            campaignViewParam.setAdOtherSaleNumMin(advancedFilter.hasAdOtherSaleNumMin() ? advancedFilter.getAdOtherSaleNumMin() : null);
            campaignViewParam.setAdOtherSaleNumMax(advancedFilter.hasAdOtherSaleNumMax() ? advancedFilter.getAdOtherSaleNumMax() : null);
            campaignViewParam.setOrdersNewToBrandFTDMin(advancedFilter.hasOrdersNewToBrandFTDMin() ? advancedFilter.getOrdersNewToBrandFTDMin() : null);
            campaignViewParam.setOrdersNewToBrandFTDMax(advancedFilter.hasOrdersNewToBrandFTDMax() ? advancedFilter.getOrdersNewToBrandFTDMax() : null);
            campaignViewParam.setOrderRateNewToBrandFTDMin(advancedFilter.hasOrderRateNewToBrandFTDMin() ? BigDecimal.valueOf(advancedFilter.getOrderRateNewToBrandFTDMin()) : null);
            campaignViewParam.setOrderRateNewToBrandFTDMax(advancedFilter.hasOrderRateNewToBrandFTDMax() ? BigDecimal.valueOf(advancedFilter.getOrderRateNewToBrandFTDMax()) : null);
            campaignViewParam.setUnitsOrderedNewToBrandFTDMin(advancedFilter.hasUnitsOrderedNewToBrandFTDMin() ? advancedFilter.getUnitsOrderedNewToBrandFTDMin() : null);
            campaignViewParam.setUnitsOrderedNewToBrandFTDMax(advancedFilter.hasUnitsOrderedNewToBrandFTDMax() ? advancedFilter.getUnitsOrderedNewToBrandFTDMax() : null);
            campaignViewParam.setSalesNewToBrandFTDMin(advancedFilter.hasSalesNewToBrandFTDMin() ? BigDecimal.valueOf(advancedFilter.getSalesNewToBrandFTDMin()) : null);
            campaignViewParam.setSalesNewToBrandFTDMax(advancedFilter.hasSalesNewToBrandFTDMax() ? BigDecimal.valueOf(advancedFilter.getSalesNewToBrandFTDMax()) : null);
            campaignViewParam.setSalesRateNewToBrandFTDMin(advancedFilter.hasSalesRateNewToBrandFTDMin() ? BigDecimal.valueOf(advancedFilter.getSalesRateNewToBrandFTDMin()) : null);
            campaignViewParam.setSalesRateNewToBrandFTDMax(advancedFilter.hasSalesRateNewToBrandFTDMax() ? BigDecimal.valueOf(advancedFilter.getSalesRateNewToBrandFTDMax()) : null);
            campaignViewParam.setUnitsOrderedRateNewToBrandFTDMin(advancedFilter.hasUnitsOrderedRateNewToBrandFTDMin() ? BigDecimal.valueOf(advancedFilter.getUnitsOrderedRateNewToBrandFTDMin()) : null);
            campaignViewParam.setUnitsOrderedRateNewToBrandFTDMax(advancedFilter.hasUnitsOrderedRateNewToBrandFTDMax() ? BigDecimal.valueOf(advancedFilter.getUnitsOrderedRateNewToBrandFTDMax()) : null);
            campaignViewParam.setAdSalesTotalMin(advancedFilter.hasAdSalesTotalMin() ? advancedFilter.getAdSalesTotalMin() : null);
            campaignViewParam.setAdSalesTotalMax(advancedFilter.hasAdSalesTotalMax() ? advancedFilter.getAdSalesTotalMax() : null);
            campaignViewParam.setAdvertisingUnitPriceMin(advancedFilter.hasAdvertisingUnitPriceMin() ? new BigDecimal(String.valueOf(advancedFilter.getAdvertisingUnitPriceMin())) : null);
            campaignViewParam.setAdvertisingUnitPriceMax(advancedFilter.hasAdvertisingUnitPriceMax() ? new BigDecimal(String.valueOf(advancedFilter.getAdvertisingUnitPriceMax())) : null);
        }

        //做参数校验
        String err = checkCampaignViewParam(campaignViewParam);
        if (StringUtils.isNotBlank(err)) {
            log.error("查询广告活动视图参数校验异常 {}", request);
            builder.setCode(Result.ERROR);
            builder.setMsg(err);
        } else {
            CampaignViewResponse.CampaignVo campaignVo;
            //新版支持类型多选，后续删除旧分支即可
//            if (Constants.SP.equalsIgnoreCase(campaignViewParam.getType())) {
//                campaignVo = viewManageService.getCampaignView(campaignViewParam.getPuid(), campaignViewParam);
//            } else {
                campaignVo = viewManageService.getAllCampaignView(campaignViewParam.getPuid(), campaignViewParam);
//            }
            builder.setData(campaignVo);
            builder.setCode(Result.SUCCESS);
        }
        log.info("产品透视分析--广告活动视图接口调用花费时间 {} ,puid: {},marketplaceId: {},shopIds: {},searchType: {},searchValue: {},startDate：{},endDate：{}", (Instant.now().toEpochMilli() - t),
                campaignViewParam.getPuid(), campaignViewParam.getMarketplaceId(), StringUtil.joinInt(campaignViewParam.getShopIdList()),
                campaignViewParam.getSearchType(), campaignViewParam.getSearchValue(), campaignViewParam.getStartDate(), campaignViewParam.getEndDate());
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * 查询广告位视图（列表页）
     */
    @Override
    public void getPlacementView(PlacementViewRequest request, StreamObserver<PlacementViewResponse> responseObserver) {
        log.info("查询广告位视图列表页 {}", request);
        long t = Instant.now().toEpochMilli();
        PlacementViewResponse.Builder builder = PlacementViewResponse.newBuilder();
        if (StringUtils.isBlank(request.getSearchValue()) || StringUtils.isBlank(request.getSearchType())
                || ViewBaseParam.SearchTypeEnum.getEnum(request.getSearchType()) == null) {
            builder.setCode(Result.SUCCESS);
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
            return;
        }

        PlacementViewParam param = new PlacementViewParam();
        param.setPageNo(request.getPageNo());
        param.setPageSize(request.getPageSize());
        param.setMarketplaceId(request.getMarketplaceId());
        param.setShopIdList(request.getShopIdListList());
        param.setSearchType(request.getSearchType());
        param.setSearchValue(request.getSearchValue());
        param.setType(request.getType());
        param.setStartDate(request.getStartDate());
        param.setEndDate(request.getEndDate());
        param.setOrderField(request.getOrderField());
        param.setOrderType(request.getOrderType());

        param.setCampaignIds(request.getCampaignIds());
        param.setStrategyType(request.getStrategyType());
        param.setStatus(request.getStatus());
        param.setServingStatus(request.getServingStatus());
        param.setPredicate(request.getPredicate());

        param.setPuid(request.getPuid());
        //环比数据传参
        param.setIsCompare(request.getIsCompare());
        param.setCompareStartDate(request.getCompareStartDate());
        param.setCompareEndDate(request.getCompareEndDate());

        if (request.hasUseAdvanced()) {  //是否开启高级搜索
            param.setUseAdvanced(request.getUseAdvanced());
        }

        if (request.hasUseAdvanced() && request.getUseAdvanced()) {  //高级筛选
            AdAdvancedFilterData advancedFilter = request.getAdvancedFilter();
            param.setImpressionsMin(advancedFilter.hasImpressionsMin() ? advancedFilter.getImpressionsMin() : null);
            param.setImpressionsMax(advancedFilter.hasImpressionsMax() ? advancedFilter.getImpressionsMax() : null);
            param.setClicksMin(advancedFilter.hasClicksMin() ? advancedFilter.getClicksMin() : null);
            param.setClicksMax(advancedFilter.hasClicksMax() ? advancedFilter.getClicksMax() : null);
            param.setClickRateMin(advancedFilter.hasClickRateMin() ? BigDecimal.valueOf(advancedFilter.getClickRateMin()) : null);
            param.setClickRateMax(advancedFilter.hasClickRateMax() ? BigDecimal.valueOf(advancedFilter.getClickRateMax()) : null);
            param.setCostMin(advancedFilter.hasCostMin() ? BigDecimal.valueOf(advancedFilter.getCostMin()) : null);
            param.setCostMax(advancedFilter.hasCostMax() ? BigDecimal.valueOf(advancedFilter.getCostMax()) : null);
            param.setCpcMin(advancedFilter.hasCpcMin() ? BigDecimal.valueOf(advancedFilter.getCpcMin()) : null);
            param.setCpcMax(advancedFilter.hasCpcMax() ? BigDecimal.valueOf(advancedFilter.getCpcMax()) : null);
            param.setOrderNumMin(advancedFilter.hasOrderNumMin() ? advancedFilter.getOrderNumMin() : null);
            param.setOrderNumMax(advancedFilter.hasOrderNumMax() ? advancedFilter.getOrderNumMax() : null);
            param.setSalesMin(advancedFilter.hasSalesMin() ? BigDecimal.valueOf(advancedFilter.getSalesMin()) : null);
            param.setSalesMax(advancedFilter.hasSalesMax() ? BigDecimal.valueOf(advancedFilter.getSalesMax()) : null);
            param.setAcosMin(advancedFilter.hasAcosMin() ? BigDecimal.valueOf(advancedFilter.getAcosMin()) : null);
            param.setAcosMax(advancedFilter.hasAcosMax() ? BigDecimal.valueOf(advancedFilter.getAcosMax()) : null);
            param.setRoasMin(advancedFilter.hasRoasMin() ? BigDecimal.valueOf(advancedFilter.getRoasMin()) : null);
            param.setRoasMax(advancedFilter.hasRoasMax() ? BigDecimal.valueOf(advancedFilter.getRoasMax()) : null);
            param.setSalesConversionRateMin(advancedFilter.hasSalesConversionRateMin() ? BigDecimal.valueOf(advancedFilter.getSalesConversionRateMin()) : null);
            param.setSalesConversionRateMax(advancedFilter.hasSalesConversionRateMax() ? BigDecimal.valueOf(advancedFilter.getSalesConversionRateMax()): null);
            param.setAcotsMin(advancedFilter.hasAcotsMin() ? BigDecimal.valueOf(advancedFilter.getAcotsMin()) :null);
            param.setAcotsMax(advancedFilter.hasAcotsMax() ? BigDecimal.valueOf(advancedFilter.getAcotsMax()) : null);
            param.setAsotsMin(advancedFilter.hasAsotsMin() ? BigDecimal.valueOf(advancedFilter.getAsotsMin()) :null);
            param.setAsotsMax(advancedFilter.hasAsotsMax() ? BigDecimal.valueOf(advancedFilter.getAsotsMax()) : null);
            param.setCpaMin(advancedFilter.hasCpaMin() ? BigDecimal.valueOf(advancedFilter.getCpaMin()) : null);
            param.setCpaMax(advancedFilter.hasCpaMax() ? BigDecimal.valueOf(advancedFilter.getCpaMax()) : null);
            param.setAdSaleNumMin(advancedFilter.hasAdSaleNumMin() ? advancedFilter.getAdSaleNumMin() : null);
            param.setAdSaleNumMax(advancedFilter.hasAdSaleNumMax() ? advancedFilter.getAdSaleNumMax() : null);
            param.setAdOtherOrderNumMin(advancedFilter.hasAdOtherOrderNumMin() ? advancedFilter.getAdOtherOrderNumMin() : null);
            param.setAdOtherOrderNumMax(advancedFilter.hasAdOtherOrderNumMax() ? advancedFilter.getAdOtherOrderNumMax() : null);
            param.setAdSalesMin(advancedFilter.hasAdSalesMin() ? BigDecimal.valueOf(advancedFilter.getAdSalesMin()) : null);
            param.setAdSalesMax(advancedFilter.hasAdSalesMax() ? BigDecimal.valueOf(advancedFilter.getAdSalesMax()) : null);
            param.setAdOtherSalesMin(advancedFilter.hasAdOtherSalesMin() ? BigDecimal.valueOf(advancedFilter.getAdOtherSalesMin()) : null);
            param.setAdOtherSalesMax(advancedFilter.hasAdOtherSalesMax() ? BigDecimal.valueOf(advancedFilter.getAdOtherSalesMax()) : null);
            param.setAdSelfSaleNumMin(advancedFilter.hasAdSelfSaleNumMin() ? advancedFilter.getAdSelfSaleNumMin() : null);
            param.setAdSelfSaleNumMax(advancedFilter.hasAdSelfSaleNumMax() ? advancedFilter.getAdSelfSaleNumMax() : null);
            param.setAdOtherSaleNumMin(advancedFilter.hasAdOtherSaleNumMin() ? advancedFilter.getAdOtherSaleNumMin() : null);
            param.setAdOtherSaleNumMax(advancedFilter.hasAdOtherSaleNumMax() ? advancedFilter.getAdOtherSaleNumMax() : null);
            param.setAdSalesTotalMin(advancedFilter.hasAdSalesTotalMin() ? advancedFilter.getAdSalesTotalMin() : null);
            param.setAdSalesTotalMax(advancedFilter.hasAdSalesTotalMax() ? advancedFilter.getAdSalesTotalMax() : null);
            param.setAdvertisingUnitPriceMin(advancedFilter.hasAdvertisingUnitPriceMin() ? new BigDecimal(String.valueOf(advancedFilter.getAdvertisingUnitPriceMin())) : null);
            param.setAdvertisingUnitPriceMax(advancedFilter.hasAdvertisingUnitPriceMax() ? new BigDecimal(String.valueOf(advancedFilter.getAdvertisingUnitPriceMax())) : null);
            param.setVcpmMin(advancedFilter.hasVcpmMin() ? new BigDecimal(String.valueOf(advancedFilter.getVcpmMin())) : null);
            param.setVcpmMax(advancedFilter.hasVcpmMax() ? new BigDecimal(String.valueOf(advancedFilter.getVcpmMax())) : null);
        }

        //做参数校验
        String err = checkPlacementPageParam(param, false);
        if (StringUtils.isNotBlank(err)) {
            log.error("查询广告位视图列表页参数校验异常 {}", request);
            builder.setCode(Result.ERROR);
            builder.setMsg(err);
        } else {
            PlacementViewResponse.PlacementVo placementVo = viewManageService.getPlacementView(param.getPuid(), param);
            builder.setData(placementVo);
            builder.setCode(Result.SUCCESS);
        }
        log.info("产品透视分析--广告位视图列表页接口调用花费时间 {} ,puid: {},marketplaceId: {},shopIds: {},searchType: {},searchValue: {},startDate：{},endDate：{}", (Instant.now().toEpochMilli() - t),
                param.getPuid(), param.getMarketplaceId(), StringUtil.joinInt(param.getShopIdList()), param.getSearchType(), param.getSearchValue(), param.getStartDate(), param.getEndDate());
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * 查询广告位视图（汇总）
     */
    @Override
    public void getPlacementViewAggregate(PlacementViewAggregateRequest request, StreamObserver<PlacementViewAggregateResponse> responseObserver) {
        log.info("查询广告位视图汇总 {}", request);
        long t = Instant.now().toEpochMilli();

        PlacementViewAggregateResponse.Builder builder = PlacementViewAggregateResponse.newBuilder();
        if (StringUtils.isBlank(request.getSearchValue()) || StringUtils.isBlank(request.getSearchType())
                || ViewBaseParam.SearchTypeEnum.getEnum(request.getSearchType()) == null) {
            builder.setCode(Result.SUCCESS);
            PlacementViewAggregateResponse.AggregateVo.Builder aggregateVo = PlacementViewAggregateResponse.AggregateVo.newBuilder();
            //总汇总初始化
            aggregateVo.setAggregateVo(viewManageService.buildAggregateViewVo(null));
            builder.setData(aggregateVo);
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
            return;
        }

        PlacementViewParam param = new PlacementViewParam();
        param.setPageSign(request.getPageSign());
        param.setMarketplaceId(request.getMarketplaceId());
        param.setShopIdList(request.getShopIdListList());
        param.setSearchType(request.getSearchType());
        param.setSearchValue(request.getSearchValue());
        param.setType(request.getType());
        param.setStartDate(request.getStartDate());
        param.setEndDate(request.getEndDate());

        param.setCampaignIds(request.getCampaignIds());
        param.setStrategyType(request.getStrategyType());
        param.setStatus(request.getStatus());
        param.setServingStatus(request.getServingStatus());

        param.setPuid(request.getPuid());
        //环比数据传参
        param.setIsCompare(request.getIsCompare());
        param.setCompareStartDate(request.getCompareStartDate());
        param.setCompareEndDate(request.getCompareEndDate());

        if (request.hasUseAdvanced()) {  //是否开启高级搜索
            param.setUseAdvanced(request.getUseAdvanced());
        }

        if (request.hasUseAdvanced() && request.getUseAdvanced()) {  //高级筛选
            AdAdvancedFilterData advancedFilter = request.getAdvancedFilter();
            param.setImpressionsMin(advancedFilter.hasImpressionsMin() ? advancedFilter.getImpressionsMin() : null);
            param.setImpressionsMax(advancedFilter.hasImpressionsMax() ? advancedFilter.getImpressionsMax() : null);
            param.setClicksMin(advancedFilter.hasClicksMin() ? advancedFilter.getClicksMin() : null);
            param.setClicksMax(advancedFilter.hasClicksMax() ? advancedFilter.getClicksMax() : null);
            param.setClickRateMin(advancedFilter.hasClickRateMin() ? BigDecimal.valueOf(advancedFilter.getClickRateMin()) : null);
            param.setClickRateMax(advancedFilter.hasClickRateMax() ? BigDecimal.valueOf(advancedFilter.getClickRateMax()) : null);
            param.setCostMin(advancedFilter.hasCostMin() ? BigDecimal.valueOf(advancedFilter.getCostMin()) : null);
            param.setCostMax(advancedFilter.hasCostMax() ? BigDecimal.valueOf(advancedFilter.getCostMax()) : null);
            param.setCpcMin(advancedFilter.hasCpcMin() ? BigDecimal.valueOf(advancedFilter.getCpcMin()) : null);
            param.setCpcMax(advancedFilter.hasCpcMax() ? BigDecimal.valueOf(advancedFilter.getCpcMax()) : null);
            param.setOrderNumMin(advancedFilter.hasOrderNumMin() ? advancedFilter.getOrderNumMin() : null);
            param.setOrderNumMax(advancedFilter.hasOrderNumMax() ? advancedFilter.getOrderNumMax() : null);
            param.setSalesMin(advancedFilter.hasSalesMin() ? BigDecimal.valueOf(advancedFilter.getSalesMin()) : null);
            param.setSalesMax(advancedFilter.hasSalesMax() ? BigDecimal.valueOf(advancedFilter.getSalesMax()) : null);
            param.setAcosMin(advancedFilter.hasAcosMin() ? BigDecimal.valueOf(advancedFilter.getAcosMin()) : null);
            param.setAcosMax(advancedFilter.hasAcosMax() ? BigDecimal.valueOf(advancedFilter.getAcosMax()) : null);
            param.setRoasMin(advancedFilter.hasRoasMin() ? BigDecimal.valueOf(advancedFilter.getRoasMin()) : null);
            param.setRoasMax(advancedFilter.hasRoasMax() ? BigDecimal.valueOf(advancedFilter.getRoasMax()) : null);
            param.setSalesConversionRateMin(advancedFilter.hasSalesConversionRateMin() ? BigDecimal.valueOf(advancedFilter.getSalesConversionRateMin()) : null);
            param.setSalesConversionRateMax(advancedFilter.hasSalesConversionRateMax() ? BigDecimal.valueOf(advancedFilter.getSalesConversionRateMax()): null);
            param.setAcotsMin(advancedFilter.hasAcotsMin() ? BigDecimal.valueOf(advancedFilter.getAcotsMin()) :null);
            param.setAcotsMax(advancedFilter.hasAcotsMax() ? BigDecimal.valueOf(advancedFilter.getAcotsMax()) : null);
            param.setAsotsMin(advancedFilter.hasAsotsMin() ? BigDecimal.valueOf(advancedFilter.getAsotsMin()) :null);
            param.setAsotsMax(advancedFilter.hasAsotsMax() ? BigDecimal.valueOf(advancedFilter.getAsotsMax()) : null);
            param.setCpaMin(advancedFilter.hasCpaMin() ? BigDecimal.valueOf(advancedFilter.getCpaMin()) : null);
            param.setCpaMax(advancedFilter.hasCpaMax() ? BigDecimal.valueOf(advancedFilter.getCpaMax()) : null);
            param.setAdSaleNumMin(advancedFilter.hasAdSaleNumMin() ? advancedFilter.getAdSaleNumMin() : null);
            param.setAdSaleNumMax(advancedFilter.hasAdSaleNumMax() ? advancedFilter.getAdSaleNumMax() : null);
            param.setAdOtherOrderNumMin(advancedFilter.hasAdOtherOrderNumMin() ? advancedFilter.getAdOtherOrderNumMin() : null);
            param.setAdOtherOrderNumMax(advancedFilter.hasAdOtherOrderNumMax() ? advancedFilter.getAdOtherOrderNumMax() : null);
            param.setAdSalesMin(advancedFilter.hasAdSalesMin() ? BigDecimal.valueOf(advancedFilter.getAdSalesMin()) : null);
            param.setAdSalesMax(advancedFilter.hasAdSalesMax() ? BigDecimal.valueOf(advancedFilter.getAdSalesMax()) : null);
            param.setAdOtherSalesMin(advancedFilter.hasAdOtherSalesMin() ? BigDecimal.valueOf(advancedFilter.getAdOtherSalesMin()) : null);
            param.setAdOtherSalesMax(advancedFilter.hasAdOtherSalesMax() ? BigDecimal.valueOf(advancedFilter.getAdOtherSalesMax()) : null);
            param.setAdSelfSaleNumMin(advancedFilter.hasAdSelfSaleNumMin() ? advancedFilter.getAdSelfSaleNumMin() : null);
            param.setAdSelfSaleNumMax(advancedFilter.hasAdSelfSaleNumMax() ? advancedFilter.getAdSelfSaleNumMax() : null);
            param.setAdOtherSaleNumMin(advancedFilter.hasAdOtherSaleNumMin() ? advancedFilter.getAdOtherSaleNumMin() : null);
            param.setAdOtherSaleNumMax(advancedFilter.hasAdOtherSaleNumMax() ? advancedFilter.getAdOtherSaleNumMax() : null);
            param.setAdSalesTotalMin(advancedFilter.hasAdSalesTotalMin() ? advancedFilter.getAdSalesTotalMin() : null);
            param.setAdSalesTotalMax(advancedFilter.hasAdSalesTotalMax() ? advancedFilter.getAdSalesTotalMax() : null);
            param.setAdvertisingUnitPriceMin(advancedFilter.hasAdvertisingUnitPriceMin() ? new BigDecimal(String.valueOf(advancedFilter.getAdvertisingUnitPriceMin())) : null);
            param.setAdvertisingUnitPriceMax(advancedFilter.hasAdvertisingUnitPriceMax() ? new BigDecimal(String.valueOf(advancedFilter.getAdvertisingUnitPriceMax())) : null);
            param.setVcpmMin(advancedFilter.hasVcpmMin() ? new BigDecimal(String.valueOf(advancedFilter.getVcpmMin())) : null);
            param.setVcpmMax(advancedFilter.hasVcpmMax() ? new BigDecimal(String.valueOf(advancedFilter.getVcpmMax())) : null);
        }

        //做参数校验
        String err = checkPlacementPageParam(param, true);
        if (StringUtils.isNotBlank(err)) {
            log.error("查询广告位视图汇总参数校验异常 {}", request);
            builder.setCode(Result.ERROR);
            builder.setMsg(err);
        } else {
            PlacementViewAggregateResponse.AggregateVo aggregateVo = viewManageService.getPlacementViewAggregate(param.getPuid(), param);
            builder.setData(aggregateVo);
            builder.setCode(Result.SUCCESS);
        }
        log.info("产品透视分析--广告位视图汇总接口调用花费时间 {} ,puid: {},marketplaceId: {},shopIds: {},searchType: {},searchValue: {},startDate：{},endDate：{}", (Instant.now().toEpochMilli() - t),
                param.getPuid(), param.getMarketplaceId(), StringUtil.joinInt(param.getShopIdList()), param.getSearchType(), param.getSearchValue(), param.getStartDate(), param.getEndDate());
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * 查询关键词投放视图（列表页）
     */
    @Override
    public void getKeywordView(KeywordViewRequest request, StreamObserver<KeywordViewResponse> responseObserver) {
        log.info("查询关键词投放视图列表页 {}", request);
        long t = Instant.now().toEpochMilli();
        KeywordViewResponse.Builder builder = KeywordViewResponse.newBuilder();

        if (StringUtils.isBlank(request.getSearchValue()) || StringUtils.isBlank(request.getSearchType())
                || ViewBaseParam.SearchTypeEnum.getEnum(request.getSearchType()) == null) {
            builder.setCode(Result.SUCCESS);
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
            return;
        }

        KeywordViewParam param = new KeywordViewParam();
        param.setPageNo(request.getPageNo());
        param.setPageSize(request.getPageSize());
        param.setMarketplaceId(request.getMarketplaceId());
        param.setShopIdList(request.getShopIdListList());
        param.setSearchType(request.getSearchType());
        param.setSearchValue(request.getSearchValue());
        param.setKeywordText(request.getKeywordText());
        param.setStartDate(request.getStartDate());
        param.setEndDate(request.getEndDate());
        param.setOrderField(request.getOrderField());
        param.setOrderType(request.getOrderType());
        param.setType(request.getType());

        param.setCampaignId(request.getCampaignId());
        param.setGroupId(request.getGroupId());
        param.setMatchType(request.getMatchType());
        param.setStatus(request.getStatus());
        param.setServingStatus(request.getServingStatus());

        param.setPuid(request.getPuid());
        //环比数据传参
        param.setIsCompare(request.getIsCompare());
        param.setCompareStartDate(request.getCompareStartDate());
        param.setCompareEndDate(request.getCompareEndDate());


        if (request.hasUseAdvanced()) {  //是否开启高级搜索
            param.setUseAdvanced(request.getUseAdvanced());
        }

        if (request.hasUseAdvanced() && request.getUseAdvanced()) {  //高级筛选
            AdAdvancedFilterData advancedFilter = request.getAdvancedFilter();
            param.setImpressionsMin(advancedFilter.hasImpressionsMin() ? advancedFilter.getImpressionsMin() : null);
            param.setImpressionsMax(advancedFilter.hasImpressionsMax() ? advancedFilter.getImpressionsMax() : null);
            param.setClicksMin(advancedFilter.hasClicksMin() ? advancedFilter.getClicksMin() : null);
            param.setClicksMax(advancedFilter.hasClicksMax() ? advancedFilter.getClicksMax() : null);
            param.setClickRateMin(advancedFilter.hasClickRateMin() ? BigDecimal.valueOf(advancedFilter.getClickRateMin()) : null);
            param.setClickRateMax(advancedFilter.hasClickRateMax() ? BigDecimal.valueOf(advancedFilter.getClickRateMax()) : null);
            param.setCostMin(advancedFilter.hasCostMin() ? BigDecimal.valueOf(advancedFilter.getCostMin()) : null);
            param.setCostMax(advancedFilter.hasCostMax() ? BigDecimal.valueOf(advancedFilter.getCostMax()) : null);
            param.setCpcMin(advancedFilter.hasCpcMin() ? BigDecimal.valueOf(advancedFilter.getCpcMin()) : null);
            param.setCpcMax(advancedFilter.hasCpcMax() ? BigDecimal.valueOf(advancedFilter.getCpcMax()) : null);
            param.setOrderNumMin(advancedFilter.hasOrderNumMin() ? advancedFilter.getOrderNumMin() : null);
            param.setOrderNumMax(advancedFilter.hasOrderNumMax() ? advancedFilter.getOrderNumMax() : null);
            param.setSalesMin(advancedFilter.hasSalesMin() ? BigDecimal.valueOf(advancedFilter.getSalesMin()) : null);
            param.setSalesMax(advancedFilter.hasSalesMax() ? BigDecimal.valueOf(advancedFilter.getSalesMax()) : null);
            param.setAcosMin(advancedFilter.hasAcosMin() ? BigDecimal.valueOf(advancedFilter.getAcosMin()) : null);
            param.setAcosMax(advancedFilter.hasAcosMax() ? BigDecimal.valueOf(advancedFilter.getAcosMax()) : null);
            param.setRoasMin(advancedFilter.hasRoasMin() ? BigDecimal.valueOf(advancedFilter.getRoasMin()) : null);
            param.setRoasMax(advancedFilter.hasRoasMax() ? BigDecimal.valueOf(advancedFilter.getRoasMax()) : null);
            param.setSalesConversionRateMin(advancedFilter.hasSalesConversionRateMin() ? BigDecimal.valueOf(advancedFilter.getSalesConversionRateMin()) : null);
            param.setSalesConversionRateMax(advancedFilter.hasSalesConversionRateMax() ? BigDecimal.valueOf(advancedFilter.getSalesConversionRateMax()): null);
            param.setAcotsMin(advancedFilter.hasAcotsMin() ? BigDecimal.valueOf(advancedFilter.getAcotsMin()) :null);
            param.setAcotsMax(advancedFilter.hasAcotsMax() ? BigDecimal.valueOf(advancedFilter.getAcotsMax()) : null);
            param.setAsotsMin(advancedFilter.hasAsotsMin() ? BigDecimal.valueOf(advancedFilter.getAsotsMin()) :null);
            param.setAsotsMax(advancedFilter.hasAsotsMax() ? BigDecimal.valueOf(advancedFilter.getAsotsMax()) : null);
            param.setViewImpressionsMin(advancedFilter.hasViewImpressionsMin() ? advancedFilter.getViewImpressionsMin() : null);
            param.setViewImpressionsMax(advancedFilter.hasViewImpressionsMax() ? advancedFilter.getViewImpressionsMax() : null);
            param.setCpaMin(advancedFilter.hasCpaMin() ? BigDecimal.valueOf(advancedFilter.getCpaMin()) : null);
            param.setCpaMax(advancedFilter.hasCpaMax() ? BigDecimal.valueOf(advancedFilter.getCpaMax()) : null);
            param.setVcpmMin(advancedFilter.hasVcpmMin() ? BigDecimal.valueOf(advancedFilter.getVcpmMin()) : null);
            param.setVcpmMax(advancedFilter.hasVcpmMax() ? BigDecimal.valueOf(advancedFilter.getVcpmMax()) : null);
            param.setAdSaleNumMin(advancedFilter.hasAdSaleNumMin() ? advancedFilter.getAdSaleNumMin() : null);
            param.setAdSaleNumMax(advancedFilter.hasAdSaleNumMax() ? advancedFilter.getAdSaleNumMax() : null);
            param.setAdOtherOrderNumMin(advancedFilter.hasAdOtherOrderNumMin() ? advancedFilter.getAdOtherOrderNumMin() : null);
            param.setAdOtherOrderNumMax(advancedFilter.hasAdOtherOrderNumMax() ? advancedFilter.getAdOtherOrderNumMax() : null);
            param.setAdSalesMin(advancedFilter.hasAdSalesMin() ? BigDecimal.valueOf(advancedFilter.getAdSalesMin()) : null);
            param.setAdSalesMax(advancedFilter.hasAdSalesMax() ? BigDecimal.valueOf(advancedFilter.getAdSalesMax()) : null);
            param.setAdOtherSalesMin(advancedFilter.hasAdOtherSalesMin() ? BigDecimal.valueOf(advancedFilter.getAdOtherSalesMin()) : null);
            param.setAdOtherSalesMax(advancedFilter.hasAdOtherSalesMax() ? BigDecimal.valueOf(advancedFilter.getAdOtherSalesMax()) : null);
            param.setAdSelfSaleNumMin(advancedFilter.hasAdSelfSaleNumMin() ? advancedFilter.getAdSelfSaleNumMin() : null);
            param.setAdSelfSaleNumMax(advancedFilter.hasAdSelfSaleNumMax() ? advancedFilter.getAdSelfSaleNumMax() : null);
            param.setAdOtherSaleNumMin(advancedFilter.hasAdOtherSaleNumMin() ? advancedFilter.getAdOtherSaleNumMin() : null);
            param.setAdOtherSaleNumMax(advancedFilter.hasAdOtherSaleNumMax() ? advancedFilter.getAdOtherSaleNumMax() : null);
            param.setOrdersNewToBrandFTDMin(advancedFilter.hasOrdersNewToBrandFTDMin() ? advancedFilter.getOrdersNewToBrandFTDMin() : null);
            param.setOrdersNewToBrandFTDMax(advancedFilter.hasOrdersNewToBrandFTDMax() ? advancedFilter.getOrdersNewToBrandFTDMax() : null);
            param.setOrderRateNewToBrandFTDMin(advancedFilter.hasOrderRateNewToBrandFTDMin() ? BigDecimal.valueOf(advancedFilter.getOrderRateNewToBrandFTDMin()) : null);
            param.setOrderRateNewToBrandFTDMax(advancedFilter.hasOrderRateNewToBrandFTDMax() ? BigDecimal.valueOf(advancedFilter.getOrderRateNewToBrandFTDMax()) : null);
            param.setUnitsOrderedNewToBrandFTDMin(advancedFilter.hasUnitsOrderedNewToBrandFTDMin() ? advancedFilter.getUnitsOrderedNewToBrandFTDMin() : null);
            param.setUnitsOrderedNewToBrandFTDMax(advancedFilter.hasUnitsOrderedNewToBrandFTDMax() ? advancedFilter.getUnitsOrderedNewToBrandFTDMax() : null);
            param.setSalesNewToBrandFTDMin(advancedFilter.hasSalesNewToBrandFTDMin() ? BigDecimal.valueOf(advancedFilter.getSalesNewToBrandFTDMin()) : null);
            param.setSalesNewToBrandFTDMax(advancedFilter.hasSalesNewToBrandFTDMax() ? BigDecimal.valueOf(advancedFilter.getSalesNewToBrandFTDMax()) : null);
            param.setSalesRateNewToBrandFTDMin(advancedFilter.hasSalesRateNewToBrandFTDMin() ? BigDecimal.valueOf(advancedFilter.getSalesRateNewToBrandFTDMin()) : null);
            param.setSalesRateNewToBrandFTDMax(advancedFilter.hasSalesRateNewToBrandFTDMax() ? BigDecimal.valueOf(advancedFilter.getSalesRateNewToBrandFTDMax()) : null);
            param.setUnitsOrderedRateNewToBrandFTDMin(advancedFilter.hasUnitsOrderedRateNewToBrandFTDMin() ? BigDecimal.valueOf(advancedFilter.getUnitsOrderedRateNewToBrandFTDMin()) : null);
            param.setUnitsOrderedRateNewToBrandFTDMax(advancedFilter.hasUnitsOrderedRateNewToBrandFTDMax() ? BigDecimal.valueOf(advancedFilter.getUnitsOrderedRateNewToBrandFTDMax()) : null);
            param.setAdSalesTotalMin(advancedFilter.hasAdSalesTotalMin() ? advancedFilter.getAdSalesTotalMin() : null);
            param.setAdSalesTotalMax(advancedFilter.hasAdSalesTotalMax() ? advancedFilter.getAdSalesTotalMax() : null);
            param.setBrandNewBuyerOrderConversionRateMin(advancedFilter.hasBrandNewBuyerOrderConversionRateMin() ? BigDecimal.valueOf(advancedFilter.getBrandNewBuyerOrderConversionRateMin()) : null);
            param.setBrandNewBuyerOrderConversionRateMax(advancedFilter.hasBrandNewBuyerOrderConversionRateMax() ? BigDecimal.valueOf(advancedFilter.getBrandNewBuyerOrderConversionRateMax()) : null);
            param.setAdvertisingUnitPriceMin(advancedFilter.hasAdvertisingUnitPriceMin() ? new BigDecimal(String.valueOf(advancedFilter.getAdvertisingUnitPriceMin())) : null);
            param.setAdvertisingUnitPriceMax(advancedFilter.hasAdvertisingUnitPriceMax() ? new BigDecimal(String.valueOf(advancedFilter.getAdvertisingUnitPriceMax())) : null);
            param.setVcpmMin(advancedFilter.hasVcpmMin() ? BigDecimal.valueOf(advancedFilter.getVcpmMin()) : null);
            param.setVcpmMax(advancedFilter.hasVcpmMax() ? BigDecimal.valueOf(advancedFilter.getVcpmMax()) : null);
            param.setViewImpressionsMin(advancedFilter.hasViewImpressionsMin() ? advancedFilter.getViewImpressionsMin() : null);
            param.setViewImpressionsMax(advancedFilter.hasViewImpressionsMax() ? advancedFilter.getViewImpressionsMax() : null);
            param.setOrdersNewToBrandFTDMin(advancedFilter.hasOrdersNewToBrandFTDMin() ? advancedFilter.getOrdersNewToBrandFTDMin() : null);
            param.setOrdersNewToBrandFTDMax(advancedFilter.hasOrdersNewToBrandFTDMax() ? advancedFilter.getOrdersNewToBrandFTDMax() : null);
            param.setOrderRateNewToBrandFTDMin(advancedFilter.hasOrderRateNewToBrandFTDMin() ? BigDecimal.valueOf(advancedFilter.getOrderRateNewToBrandFTDMin()) : null);
            param.setOrderRateNewToBrandFTDMax(advancedFilter.hasOrderRateNewToBrandFTDMax() ? BigDecimal.valueOf(advancedFilter.getOrderRateNewToBrandFTDMax()) : null);
            param.setUnitsOrderedNewToBrandFTDMin(advancedFilter.hasUnitsOrderedNewToBrandFTDMin() ? advancedFilter.getUnitsOrderedNewToBrandFTDMin() : null);
            param.setUnitsOrderedNewToBrandFTDMax(advancedFilter.hasUnitsOrderedNewToBrandFTDMax() ? advancedFilter.getUnitsOrderedNewToBrandFTDMax() : null);
            param.setSalesNewToBrandFTDMin(advancedFilter.hasSalesNewToBrandFTDMin() ? BigDecimal.valueOf(advancedFilter.getSalesNewToBrandFTDMin()) : null);
            param.setSalesNewToBrandFTDMax(advancedFilter.hasSalesNewToBrandFTDMax() ? BigDecimal.valueOf(advancedFilter.getSalesNewToBrandFTDMax()) : null);
            param.setSalesRateNewToBrandFTDMin(advancedFilter.hasSalesRateNewToBrandFTDMin() ? BigDecimal.valueOf(advancedFilter.getSalesRateNewToBrandFTDMin()) : null);
            param.setSalesRateNewToBrandFTDMax(advancedFilter.hasSalesRateNewToBrandFTDMax() ? BigDecimal.valueOf(advancedFilter.getSalesRateNewToBrandFTDMax()) : null);
            param.setUnitsOrderedRateNewToBrandFTDMin(advancedFilter.hasUnitsOrderedRateNewToBrandFTDMin() ? BigDecimal.valueOf(advancedFilter.getUnitsOrderedRateNewToBrandFTDMin()) : null);
            param.setUnitsOrderedRateNewToBrandFTDMax(advancedFilter.hasUnitsOrderedRateNewToBrandFTDMax() ? BigDecimal.valueOf(advancedFilter.getUnitsOrderedRateNewToBrandFTDMax()) : null);
        }
        //做参数校验
        String err = checkKeywordsPageParam(param, false);
        if (StringUtils.isNotBlank(err)) {
            log.error("查询关键词视图列表页参数校验异常 {}", request);
            builder.setCode(Result.ERROR);
            builder.setMsg(err);
        } else {
            KeywordViewResponse.KeywordVo keywordVo;
            //新版支持类型多选，后续删除旧分支即可
//            if (Constants.SP.equalsIgnoreCase(param.getType())) {
//                keywordVo = viewManageService.getKeywordView(param.getPuid(), param);
//            } else {
                keywordVo = viewManageService.getAllKeywordView(param.getPuid(), param);
//            }
            builder.setData(keywordVo);
            builder.setCode(Result.SUCCESS);
        }
        log.info("产品透视分析--关键词视图列表页接口调用花费时间 {} ,puid: {},marketplaceId: {},shopIds: {},searchType: {},searchValue: {},startDate：{},endDate：{}", (Instant.now().toEpochMilli() - t),
                param.getPuid(), param.getMarketplaceId(), StringUtil.joinInt(param.getShopIdList()), param.getSearchType(), param.getSearchValue(), param.getStartDate(), param.getEndDate());
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * 查询关键词投放视图（汇总）
     */
    @Override
    public void getKeywordViewAggregate(KeywordViewAggregateRequest request, StreamObserver<KeywordViewAggregateResponse> responseObserver) {
        log.info("查询关键词投放视图汇总 {}", request);
        long t = Instant.now().toEpochMilli();
        KeywordViewAggregateResponse.Builder builder = KeywordViewAggregateResponse.newBuilder();

        if (StringUtils.isBlank(request.getSearchValue()) || StringUtils.isBlank(request.getSearchType())
                || ViewBaseParam.SearchTypeEnum.getEnum(request.getSearchType()) == null) {
            builder.setCode(Result.SUCCESS);
            KeywordViewAggregateResponse.AggregateVo.Builder aggregateVo = KeywordViewAggregateResponse.AggregateVo.newBuilder();
            //总汇总初始化
            aggregateVo.setAggregateVo(viewManageService.buildKeywordAggregateViewVo(null));
            builder.setData(aggregateVo);
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
            return;
        }

        KeywordViewParam param = new KeywordViewParam();
        param.setPageSign(request.getPageSign());
        param.setPageNo(request.getPageNo());
        param.setPageSize(request.getPageSize());
        param.setMarketplaceId(request.getMarketplaceId());
        param.setShopIdList(request.getShopIdListList());
        param.setSearchType(request.getSearchType());
        param.setSearchValue(request.getSearchValue());
        param.setStartDate(request.getStartDate());
        param.setEndDate(request.getEndDate());
        param.setOrderField(request.getOrderField());
        param.setOrderType(request.getOrderType());
        param.setType(request.getType());

        param.setCampaignId(request.getCampaignId());
        param.setGroupId(request.getGroupId());
        param.setMatchType(request.getMatchType());
        param.setStatus(request.getStatus());
        param.setServingStatus(request.getServingStatus());
        param.setQueryValue(request.getQueryValue());
        param.setQueryType(request.getQueryType());

        param.setPuid(request.getPuid());
        //环比数据传参
        param.setIsCompare(request.getIsCompare());
        param.setCompareStartDate(request.getCompareStartDate());
        param.setCompareEndDate(request.getCompareEndDate());


        if (request.hasUseAdvanced()) {  //是否开启高级搜索
            param.setUseAdvanced(request.getUseAdvanced());
        }

        if (request.hasUseAdvanced() && request.getUseAdvanced()) {  //高级筛选
            AdAdvancedFilterData advancedFilter = request.getAdvancedFilter();
            param.setImpressionsMin(advancedFilter.hasImpressionsMin() ? advancedFilter.getImpressionsMin() : null);
            param.setImpressionsMax(advancedFilter.hasImpressionsMax() ? advancedFilter.getImpressionsMax() : null);
            param.setClicksMin(advancedFilter.hasClicksMin() ? advancedFilter.getClicksMin() : null);
            param.setClicksMax(advancedFilter.hasClicksMax() ? advancedFilter.getClicksMax() : null);
            param.setClickRateMin(advancedFilter.hasClickRateMin() ? BigDecimal.valueOf(advancedFilter.getClickRateMin()) : null);
            param.setClickRateMax(advancedFilter.hasClickRateMax() ? BigDecimal.valueOf(advancedFilter.getClickRateMax()) : null);
            param.setCostMin(advancedFilter.hasCostMin() ? BigDecimal.valueOf(advancedFilter.getCostMin()) : null);
            param.setCostMax(advancedFilter.hasCostMax() ? BigDecimal.valueOf(advancedFilter.getCostMax()) : null);
            param.setCpcMin(advancedFilter.hasCpcMin() ? BigDecimal.valueOf(advancedFilter.getCpcMin()) : null);
            param.setCpcMax(advancedFilter.hasCpcMax() ? BigDecimal.valueOf(advancedFilter.getCpcMax()) : null);
            param.setOrderNumMin(advancedFilter.hasOrderNumMin() ? advancedFilter.getOrderNumMin() : null);
            param.setOrderNumMax(advancedFilter.hasOrderNumMax() ? advancedFilter.getOrderNumMax() : null);
            param.setSalesMin(advancedFilter.hasSalesMin() ? BigDecimal.valueOf(advancedFilter.getSalesMin()) : null);
            param.setSalesMax(advancedFilter.hasSalesMax() ? BigDecimal.valueOf(advancedFilter.getSalesMax()) : null);
            param.setAcosMin(advancedFilter.hasAcosMin() ? BigDecimal.valueOf(advancedFilter.getAcosMin()) : null);
            param.setAcosMax(advancedFilter.hasAcosMax() ? BigDecimal.valueOf(advancedFilter.getAcosMax()) : null);
            param.setRoasMin(advancedFilter.hasRoasMin() ? BigDecimal.valueOf(advancedFilter.getRoasMin()) : null);
            param.setRoasMax(advancedFilter.hasRoasMax() ? BigDecimal.valueOf(advancedFilter.getRoasMax()) : null);
            param.setSalesConversionRateMin(advancedFilter.hasSalesConversionRateMin() ? BigDecimal.valueOf(advancedFilter.getSalesConversionRateMin()) : null);
            param.setSalesConversionRateMax(advancedFilter.hasSalesConversionRateMax() ? BigDecimal.valueOf(advancedFilter.getSalesConversionRateMax()): null);
            param.setAcotsMin(advancedFilter.hasAcotsMin() ? BigDecimal.valueOf(advancedFilter.getAcotsMin()) :null);
            param.setAcotsMax(advancedFilter.hasAcotsMax() ? BigDecimal.valueOf(advancedFilter.getAcotsMax()) : null);
            param.setAsotsMin(advancedFilter.hasAsotsMin() ? BigDecimal.valueOf(advancedFilter.getAsotsMin()) :null);
            param.setAsotsMax(advancedFilter.hasAsotsMax() ? BigDecimal.valueOf(advancedFilter.getAsotsMax()) : null);
            param.setViewImpressionsMin(advancedFilter.hasViewImpressionsMin() ? advancedFilter.getViewImpressionsMin() : null);
            param.setViewImpressionsMax(advancedFilter.hasViewImpressionsMax() ? advancedFilter.getViewImpressionsMax() : null);
            param.setCpaMin(advancedFilter.hasCpaMin() ? BigDecimal.valueOf(advancedFilter.getCpaMin()) : null);
            param.setCpaMax(advancedFilter.hasCpaMax() ? BigDecimal.valueOf(advancedFilter.getCpaMax()) : null);
            param.setVcpmMin(advancedFilter.hasVcpmMin() ? BigDecimal.valueOf(advancedFilter.getVcpmMin()) : null);
            param.setVcpmMax(advancedFilter.hasVcpmMax() ? BigDecimal.valueOf(advancedFilter.getVcpmMax()) : null);
            param.setAdSaleNumMin(advancedFilter.hasAdSaleNumMin() ? advancedFilter.getAdSaleNumMin() : null);
            param.setAdSaleNumMax(advancedFilter.hasAdSaleNumMax() ? advancedFilter.getAdSaleNumMax() : null);
            param.setAdOtherOrderNumMin(advancedFilter.hasAdOtherOrderNumMin() ? advancedFilter.getAdOtherOrderNumMin() : null);
            param.setAdOtherOrderNumMax(advancedFilter.hasAdOtherOrderNumMax() ? advancedFilter.getAdOtherOrderNumMax() : null);
            param.setAdSalesMin(advancedFilter.hasAdSalesMin() ? BigDecimal.valueOf(advancedFilter.getAdSalesMin()) : null);
            param.setAdSalesMax(advancedFilter.hasAdSalesMax() ? BigDecimal.valueOf(advancedFilter.getAdSalesMax()) : null);
            param.setAdOtherSalesMin(advancedFilter.hasAdOtherSalesMin() ? BigDecimal.valueOf(advancedFilter.getAdOtherSalesMin()) : null);
            param.setAdOtherSalesMax(advancedFilter.hasAdOtherSalesMax() ? BigDecimal.valueOf(advancedFilter.getAdOtherSalesMax()) : null);
            param.setAdSelfSaleNumMin(advancedFilter.hasAdSelfSaleNumMin() ? advancedFilter.getAdSelfSaleNumMin() : null);
            param.setAdSelfSaleNumMax(advancedFilter.hasAdSelfSaleNumMax() ? advancedFilter.getAdSelfSaleNumMax() : null);
            param.setAdOtherSaleNumMin(advancedFilter.hasAdOtherSaleNumMin() ? advancedFilter.getAdOtherSaleNumMin() : null);
            param.setAdOtherSaleNumMax(advancedFilter.hasAdOtherSaleNumMax() ? advancedFilter.getAdOtherSaleNumMax() : null);
            param.setOrdersNewToBrandFTDMin(advancedFilter.hasOrdersNewToBrandFTDMin() ? advancedFilter.getOrdersNewToBrandFTDMin() : null);
            param.setOrdersNewToBrandFTDMax(advancedFilter.hasOrdersNewToBrandFTDMax() ? advancedFilter.getOrdersNewToBrandFTDMax() : null);
            param.setOrderRateNewToBrandFTDMin(advancedFilter.hasOrderRateNewToBrandFTDMin() ? BigDecimal.valueOf(advancedFilter.getOrderRateNewToBrandFTDMin()) : null);
            param.setOrderRateNewToBrandFTDMax(advancedFilter.hasOrderRateNewToBrandFTDMax() ? BigDecimal.valueOf(advancedFilter.getOrderRateNewToBrandFTDMax()) : null);
            param.setUnitsOrderedNewToBrandFTDMin(advancedFilter.hasUnitsOrderedNewToBrandFTDMin() ? advancedFilter.getUnitsOrderedNewToBrandFTDMin() : null);
            param.setUnitsOrderedNewToBrandFTDMax(advancedFilter.hasUnitsOrderedNewToBrandFTDMax() ? advancedFilter.getUnitsOrderedNewToBrandFTDMax() : null);
            param.setSalesNewToBrandFTDMin(advancedFilter.hasSalesNewToBrandFTDMin() ? BigDecimal.valueOf(advancedFilter.getSalesNewToBrandFTDMin()) : null);
            param.setSalesNewToBrandFTDMax(advancedFilter.hasSalesNewToBrandFTDMax() ? BigDecimal.valueOf(advancedFilter.getSalesNewToBrandFTDMax()) : null);
            param.setSalesRateNewToBrandFTDMin(advancedFilter.hasSalesRateNewToBrandFTDMin() ? BigDecimal.valueOf(advancedFilter.getSalesRateNewToBrandFTDMin()) : null);
            param.setSalesRateNewToBrandFTDMax(advancedFilter.hasSalesRateNewToBrandFTDMax() ? BigDecimal.valueOf(advancedFilter.getSalesRateNewToBrandFTDMax()) : null);
            param.setUnitsOrderedRateNewToBrandFTDMin(advancedFilter.hasUnitsOrderedRateNewToBrandFTDMin() ? BigDecimal.valueOf(advancedFilter.getUnitsOrderedRateNewToBrandFTDMin()) : null);
            param.setUnitsOrderedRateNewToBrandFTDMax(advancedFilter.hasUnitsOrderedRateNewToBrandFTDMax() ? BigDecimal.valueOf(advancedFilter.getUnitsOrderedRateNewToBrandFTDMax()) : null);
            param.setAdSalesTotalMin(advancedFilter.hasAdSalesTotalMin() ? advancedFilter.getAdSalesTotalMin() : null);
            param.setAdSalesTotalMax(advancedFilter.hasAdSalesTotalMax() ? advancedFilter.getAdSalesTotalMax() : null);
            param.setBrandNewBuyerOrderConversionRateMin(advancedFilter.hasBrandNewBuyerOrderConversionRateMin() ? BigDecimal.valueOf(advancedFilter.getBrandNewBuyerOrderConversionRateMin()) : null);
            param.setBrandNewBuyerOrderConversionRateMax(advancedFilter.hasBrandNewBuyerOrderConversionRateMax() ? BigDecimal.valueOf(advancedFilter.getBrandNewBuyerOrderConversionRateMax()) : null);
            param.setAdvertisingUnitPriceMin(advancedFilter.hasAdvertisingUnitPriceMin() ? new BigDecimal(String.valueOf(advancedFilter.getAdvertisingUnitPriceMin())) : null);
            param.setAdvertisingUnitPriceMax(advancedFilter.hasAdvertisingUnitPriceMax() ? new BigDecimal(String.valueOf(advancedFilter.getAdvertisingUnitPriceMax())) : null);
            param.setVcpmMin(advancedFilter.hasVcpmMin() ? BigDecimal.valueOf(advancedFilter.getVcpmMin()) : null);
            param.setVcpmMax(advancedFilter.hasVcpmMax() ? BigDecimal.valueOf(advancedFilter.getVcpmMax()) : null);
            param.setViewImpressionsMin(advancedFilter.hasViewImpressionsMin() ? advancedFilter.getViewImpressionsMin() : null);
            param.setViewImpressionsMax(advancedFilter.hasViewImpressionsMax() ? advancedFilter.getViewImpressionsMax() : null);
            param.setOrdersNewToBrandFTDMin(advancedFilter.hasOrdersNewToBrandFTDMin() ? advancedFilter.getOrdersNewToBrandFTDMin() : null);
            param.setOrdersNewToBrandFTDMax(advancedFilter.hasOrdersNewToBrandFTDMax() ? advancedFilter.getOrdersNewToBrandFTDMax() : null);
            param.setOrderRateNewToBrandFTDMin(advancedFilter.hasOrderRateNewToBrandFTDMin() ? BigDecimal.valueOf(advancedFilter.getOrderRateNewToBrandFTDMin()) : null);
            param.setOrderRateNewToBrandFTDMax(advancedFilter.hasOrderRateNewToBrandFTDMax() ? BigDecimal.valueOf(advancedFilter.getOrderRateNewToBrandFTDMax()) : null);
            param.setUnitsOrderedNewToBrandFTDMin(advancedFilter.hasUnitsOrderedNewToBrandFTDMin() ? advancedFilter.getUnitsOrderedNewToBrandFTDMin() : null);
            param.setUnitsOrderedNewToBrandFTDMax(advancedFilter.hasUnitsOrderedNewToBrandFTDMax() ? advancedFilter.getUnitsOrderedNewToBrandFTDMax() : null);
            param.setSalesNewToBrandFTDMin(advancedFilter.hasSalesNewToBrandFTDMin() ? BigDecimal.valueOf(advancedFilter.getSalesNewToBrandFTDMin()) : null);
            param.setSalesNewToBrandFTDMax(advancedFilter.hasSalesNewToBrandFTDMax() ? BigDecimal.valueOf(advancedFilter.getSalesNewToBrandFTDMax()) : null);
            param.setSalesRateNewToBrandFTDMin(advancedFilter.hasSalesRateNewToBrandFTDMin() ? BigDecimal.valueOf(advancedFilter.getSalesRateNewToBrandFTDMin()) : null);
            param.setSalesRateNewToBrandFTDMax(advancedFilter.hasSalesRateNewToBrandFTDMax() ? BigDecimal.valueOf(advancedFilter.getSalesRateNewToBrandFTDMax()) : null);
            param.setUnitsOrderedRateNewToBrandFTDMin(advancedFilter.hasUnitsOrderedRateNewToBrandFTDMin() ? BigDecimal.valueOf(advancedFilter.getUnitsOrderedRateNewToBrandFTDMin()) : null);
            param.setUnitsOrderedRateNewToBrandFTDMax(advancedFilter.hasUnitsOrderedRateNewToBrandFTDMax() ? BigDecimal.valueOf(advancedFilter.getUnitsOrderedRateNewToBrandFTDMax()) : null);
        }
        //做参数校验
        String err = checkKeywordsPageParam(param, true);
        if (StringUtils.isNotBlank(err)) {
            log.error("查询关键词视图汇总参数校验异常 {}", request);
            builder.setCode(Result.ERROR);
            builder.setMsg(err);
        } else {
            KeywordViewAggregateResponse.AggregateVo aggregateVo;
            //新版支持类型多选，后续删除旧分支即可
//            if (Constants.SP.equalsIgnoreCase(param.getType())) {
//                aggregateVo = viewManageService.getKeywordViewAggregate(param.getPuid(), param);
//            } else {
                aggregateVo = viewManageService.getAllKeywordViewAggregate(param.getPuid(), param);
//            }
            builder.setData(aggregateVo);
            builder.setCode(Result.SUCCESS);
        }
        log.info("产品透视分析--关键词投放视图汇总接口调用花费时间 {} ,puid: {},marketplaceId: {},shopIds: {},searchType: {},searchValue: {},startDate：{},endDate：{}", (Instant.now().toEpochMilli() - t),
                param.getPuid(), param.getMarketplaceId(), StringUtil.joinInt(param.getShopIdList()), param.getSearchType(), param.getSearchValue(), param.getStartDate(), param.getEndDate());
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * 查询自动投放视图（列表页、汇总）
     */
    @Override
    public void getAutoTargetView(AutoTargetViewRequest request, StreamObserver<AutoTargetViewResponse> responseObserver) {
        log.info("查询自动投放视图列表页 {}", request);
        long t = Instant.now().toEpochMilli();
        AutoTargetViewResponse.Builder builder = AutoTargetViewResponse.newBuilder();

        if (StringUtils.isBlank(request.getSearchValue()) || StringUtils.isBlank(request.getSearchType())
                || ViewBaseParam.SearchTypeEnum.getEnum(request.getSearchType()) == null) {
            builder.setCode(Result.SUCCESS);
            AutoTargetViewResponse.TargetVo.Builder targetVo = AutoTargetViewResponse.TargetVo.newBuilder();
            //总汇总初始化
            targetVo.setAggregateVo(viewManageService.buildAggregateViewVo(null));
            builder.setData(targetVo);
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
            return;
        }

        TargetViewParam param = new TargetViewParam();
        param.setPageSign(request.getPageSign());
        param.setPageNo(request.getPageNo());
        param.setPageSize(request.getPageSize());
        param.setMarketplaceId(request.getMarketplaceId());
        param.setShopIdList(request.getShopIdListList());
        param.setSearchType(request.getSearchType());
        param.setSearchValue(request.getSearchValue());
        param.setType(request.getType());
        param.setStartDate(request.getStartDate());
        param.setEndDate(request.getEndDate());
        param.setOrderField(request.getOrderField());
        param.setOrderType(request.getOrderType());

        param.setCampaignId(request.getCampaignId());
        param.setGroupId(request.getGroupId());
        param.setTargetType(request.getTargetType());
        param.setStatus(request.getStatus());
        param.setServingStatus(request.getServingStatus());

        param.setPuid(request.getPuid());
        //环比数据传参
        param.setIsCompare(request.getIsCompare());
        param.setCompareStartDate(request.getCompareStartDate());
        param.setCompareEndDate(request.getCompareEndDate());

        if (request.hasUseAdvanced()) {  //是否开启高级搜索
            param.setUseAdvanced(request.getUseAdvanced());
        }

        if (request.hasUseAdvanced() && request.getUseAdvanced()) {  //高级筛选
            AdAdvancedFilterData advancedFilter = request.getAdvancedFilter();
            param.setImpressionsMin(advancedFilter.hasImpressionsMin() ? advancedFilter.getImpressionsMin() : null);
            param.setImpressionsMax(advancedFilter.hasImpressionsMax() ? advancedFilter.getImpressionsMax() : null);
            param.setClicksMin(advancedFilter.hasClicksMin() ? advancedFilter.getClicksMin() : null);
            param.setClicksMax(advancedFilter.hasClicksMax() ? advancedFilter.getClicksMax() : null);
            param.setClickRateMin(advancedFilter.hasClickRateMin() ? BigDecimal.valueOf(advancedFilter.getClickRateMin()) : null);
            param.setClickRateMax(advancedFilter.hasClickRateMax() ? BigDecimal.valueOf(advancedFilter.getClickRateMax()) : null);
            param.setCostMin(advancedFilter.hasCostMin() ? BigDecimal.valueOf(advancedFilter.getCostMin()) : null);
            param.setCostMax(advancedFilter.hasCostMax() ? BigDecimal.valueOf(advancedFilter.getCostMax()) : null);
            param.setCpcMin(advancedFilter.hasCpcMin() ? BigDecimal.valueOf(advancedFilter.getCpcMin()) : null);
            param.setCpcMax(advancedFilter.hasCpcMax() ? BigDecimal.valueOf(advancedFilter.getCpcMax()) : null);
            param.setOrderNumMin(advancedFilter.hasOrderNumMin() ? advancedFilter.getOrderNumMin() : null);
            param.setOrderNumMax(advancedFilter.hasOrderNumMax() ? advancedFilter.getOrderNumMax() : null);
            param.setSalesMin(advancedFilter.hasSalesMin() ? BigDecimal.valueOf(advancedFilter.getSalesMin()) : null);
            param.setSalesMax(advancedFilter.hasSalesMax() ? BigDecimal.valueOf(advancedFilter.getSalesMax()) : null);
            param.setAcosMin(advancedFilter.hasAcosMin() ? BigDecimal.valueOf(advancedFilter.getAcosMin()) : null);
            param.setAcosMax(advancedFilter.hasAcosMax() ? BigDecimal.valueOf(advancedFilter.getAcosMax()) : null);
            param.setRoasMin(advancedFilter.hasRoasMin() ? BigDecimal.valueOf(advancedFilter.getRoasMin()) : null);
            param.setRoasMax(advancedFilter.hasRoasMax() ? BigDecimal.valueOf(advancedFilter.getRoasMax()) : null);
            param.setSalesConversionRateMin(advancedFilter.hasSalesConversionRateMin() ? BigDecimal.valueOf(advancedFilter.getSalesConversionRateMin()) : null);
            param.setSalesConversionRateMax(advancedFilter.hasSalesConversionRateMax() ? BigDecimal.valueOf(advancedFilter.getSalesConversionRateMax()): null);
            param.setAcotsMin(advancedFilter.hasAcotsMin() ? BigDecimal.valueOf(advancedFilter.getAcotsMin()) :null);
            param.setAcotsMax(advancedFilter.hasAcotsMax() ? BigDecimal.valueOf(advancedFilter.getAcotsMax()) : null);
            param.setAsotsMin(advancedFilter.hasAsotsMin() ? BigDecimal.valueOf(advancedFilter.getAsotsMin()) :null);
            param.setAsotsMax(advancedFilter.hasAsotsMax() ? BigDecimal.valueOf(advancedFilter.getAsotsMax()) : null);
            param.setCpaMin(advancedFilter.hasCpaMin() ? BigDecimal.valueOf(advancedFilter.getCpaMin()) : null);
            param.setCpaMax(advancedFilter.hasCpaMax() ? BigDecimal.valueOf(advancedFilter.getCpaMax()) : null);
            param.setAdSaleNumMin(advancedFilter.hasAdSaleNumMin() ? advancedFilter.getAdSaleNumMin() : null);
            param.setAdSaleNumMax(advancedFilter.hasAdSaleNumMax() ? advancedFilter.getAdSaleNumMax() : null);
            param.setAdOtherOrderNumMin(advancedFilter.hasAdOtherOrderNumMin() ? advancedFilter.getAdOtherOrderNumMin() : null);
            param.setAdOtherOrderNumMax(advancedFilter.hasAdOtherOrderNumMax() ? advancedFilter.getAdOtherOrderNumMax() : null);
            param.setAdSalesMin(advancedFilter.hasAdSalesMin() ? BigDecimal.valueOf(advancedFilter.getAdSalesMin()) : null);
            param.setAdSalesMax(advancedFilter.hasAdSalesMax() ? BigDecimal.valueOf(advancedFilter.getAdSalesMax()) : null);
            param.setAdOtherSalesMin(advancedFilter.hasAdOtherSalesMin() ? BigDecimal.valueOf(advancedFilter.getAdOtherSalesMin()) : null);
            param.setAdOtherSalesMax(advancedFilter.hasAdOtherSalesMax() ? BigDecimal.valueOf(advancedFilter.getAdOtherSalesMax()) : null);
            param.setAdSelfSaleNumMin(advancedFilter.hasAdSelfSaleNumMin() ? advancedFilter.getAdSelfSaleNumMin() : null);
            param.setAdSelfSaleNumMax(advancedFilter.hasAdSelfSaleNumMax() ? advancedFilter.getAdSelfSaleNumMax() : null);
            param.setAdOtherSaleNumMin(advancedFilter.hasAdOtherSaleNumMin() ? advancedFilter.getAdOtherSaleNumMin() : null);
            param.setAdOtherSaleNumMax(advancedFilter.hasAdOtherSaleNumMax() ? advancedFilter.getAdOtherSaleNumMax() : null);
            param.setAdSalesTotalMin(advancedFilter.hasAdSalesTotalMin() ? advancedFilter.getAdSalesTotalMin() : null);
            param.setAdSalesTotalMax(advancedFilter.hasAdSalesTotalMax() ? advancedFilter.getAdSalesTotalMax() : null);
            param.setAdvertisingUnitPriceMin(advancedFilter.hasAdvertisingUnitPriceMin() ? new BigDecimal(String.valueOf(advancedFilter.getAdvertisingUnitPriceMin())) : null);
            param.setAdvertisingUnitPriceMax(advancedFilter.hasAdvertisingUnitPriceMax() ? new BigDecimal(String.valueOf(advancedFilter.getAdvertisingUnitPriceMax())) : null);
            param.setVcpmMin(advancedFilter.hasVcpmMin() ? new BigDecimal(String.valueOf(advancedFilter.getVcpmMin())) : null);
            param.setVcpmMax(advancedFilter.hasVcpmMax() ? new BigDecimal(String.valueOf(advancedFilter.getVcpmMax())) : null);
        }

        //做参数校验
        String err = checkAutoTargetPageParam(param);
        if (StringUtils.isNotBlank(err)) {
            log.error("查询自动投放视图汇总参数校验异常 {}", request);
            builder.setCode(Result.ERROR);
            builder.setMsg(err);
        } else {
            AutoTargetViewResponse.TargetVo targetVo = viewManageService.getAutoTargetView(param.getPuid(), param);
            builder.setData(targetVo);
            builder.setCode(Result.SUCCESS);
        }
        log.info("产品透视分析--自动投放视图汇总接口调用花费时间 {} ,puid: {},marketplaceId: {},shopIds: {},searchType: {},searchValue: {},startDate：{},endDate：{}", (Instant.now().toEpochMilli() - t),
                param.getPuid(), param.getMarketplaceId(), StringUtil.joinInt(param.getShopIdList()), param.getSearchType(), param.getSearchValue(), param.getStartDate(), param.getEndDate());
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * 查询商品投放视图（列表页）
     */
    @Override
    public void getCategoryTargetView(CategoryTargetViewRequest request, StreamObserver<CategoryTargetViewResponse> responseObserver) {
        log.info("查询商品投放视图列表页 {}", request);
        long t = Instant.now().toEpochMilli();
        CategoryTargetViewResponse.Builder builder = CategoryTargetViewResponse.newBuilder();

        if (StringUtils.isBlank(request.getSearchValue()) || StringUtils.isBlank(request.getSearchType())
                || ViewBaseParam.SearchTypeEnum.getEnum(request.getSearchType()) == null) {
            builder.setCode(Result.SUCCESS);
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
            return;
        }

        TargetViewParam param = new TargetViewParam();
        param.setPageNo(request.getPageNo());
        param.setPageSize(request.getPageSize());
        param.setMarketplaceId(request.getMarketplaceId());
        param.setShopIdList(request.getShopIdListList());
        param.setSearchType(request.getSearchType());
        param.setSearchValue(request.getSearchValue());
        param.setTargetText(request.getTargetText());
        param.setType(request.getType());
        param.setStartDate(request.getStartDate());
        param.setEndDate(request.getEndDate());
        param.setOrderField(request.getOrderField());
        param.setOrderType(request.getOrderType());

        param.setCampaignId(request.getCampaignId());
        param.setGroupId(request.getGroupId());
        param.setSelectType(request.getSelectType());
        param.setTargetType(request.getTargetType());
        param.setStatus(request.getStatus());
        param.setServingStatus(request.getServingStatus());

        param.setPuid(request.getPuid());
        //环比数据传参
        param.setIsCompare(request.getIsCompare());
        param.setCompareStartDate(request.getCompareStartDate());
        param.setCompareEndDate(request.getCompareEndDate());

        if (request.hasUseAdvanced()) {  //是否开启高级搜索
            param.setUseAdvanced(request.getUseAdvanced());
        }

        if (request.hasUseAdvanced() && request.getUseAdvanced()) {  //高级筛选
            AdAdvancedFilterData advancedFilter = request.getAdvancedFilter();
            param.setImpressionsMin(advancedFilter.hasImpressionsMin() ? advancedFilter.getImpressionsMin() : null);
            param.setImpressionsMax(advancedFilter.hasImpressionsMax() ? advancedFilter.getImpressionsMax() : null);
            param.setClicksMin(advancedFilter.hasClicksMin() ? advancedFilter.getClicksMin() : null);
            param.setClicksMax(advancedFilter.hasClicksMax() ? advancedFilter.getClicksMax() : null);
            param.setClickRateMin(advancedFilter.hasClickRateMin() ? BigDecimal.valueOf(advancedFilter.getClickRateMin()) : null);
            param.setClickRateMax(advancedFilter.hasClickRateMax() ? BigDecimal.valueOf(advancedFilter.getClickRateMax()) : null);
            param.setCostMin(advancedFilter.hasCostMin() ? BigDecimal.valueOf(advancedFilter.getCostMin()) : null);
            param.setCostMax(advancedFilter.hasCostMax() ? BigDecimal.valueOf(advancedFilter.getCostMax()) : null);
            param.setCpcMin(advancedFilter.hasCpcMin() ? BigDecimal.valueOf(advancedFilter.getCpcMin()) : null);
            param.setCpcMax(advancedFilter.hasCpcMax() ? BigDecimal.valueOf(advancedFilter.getCpcMax()) : null);
            param.setOrderNumMin(advancedFilter.hasOrderNumMin() ? advancedFilter.getOrderNumMin() : null);
            param.setOrderNumMax(advancedFilter.hasOrderNumMax() ? advancedFilter.getOrderNumMax() : null);
            param.setSalesMin(advancedFilter.hasSalesMin() ? BigDecimal.valueOf(advancedFilter.getSalesMin()) : null);
            param.setSalesMax(advancedFilter.hasSalesMax() ? BigDecimal.valueOf(advancedFilter.getSalesMax()) : null);
            param.setAcosMin(advancedFilter.hasAcosMin() ? BigDecimal.valueOf(advancedFilter.getAcosMin()) : null);
            param.setAcosMax(advancedFilter.hasAcosMax() ? BigDecimal.valueOf(advancedFilter.getAcosMax()) : null);
            param.setRoasMin(advancedFilter.hasRoasMin() ? BigDecimal.valueOf(advancedFilter.getRoasMin()) : null);
            param.setRoasMax(advancedFilter.hasRoasMax() ? BigDecimal.valueOf(advancedFilter.getRoasMax()) : null);
            param.setSalesConversionRateMin(advancedFilter.hasSalesConversionRateMin() ? BigDecimal.valueOf(advancedFilter.getSalesConversionRateMin()) : null);
            param.setSalesConversionRateMax(advancedFilter.hasSalesConversionRateMax() ? BigDecimal.valueOf(advancedFilter.getSalesConversionRateMax()): null);
            param.setAcotsMin(advancedFilter.hasAcotsMin() ? BigDecimal.valueOf(advancedFilter.getAcotsMin()) :null);
            param.setAcotsMax(advancedFilter.hasAcotsMax() ? BigDecimal.valueOf(advancedFilter.getAcotsMax()) : null);
            param.setAsotsMin(advancedFilter.hasAsotsMin() ? BigDecimal.valueOf(advancedFilter.getAsotsMin()) :null);
            param.setAsotsMax(advancedFilter.hasAsotsMax() ? BigDecimal.valueOf(advancedFilter.getAsotsMax()) : null);
            param.setCpaMin(advancedFilter.hasCpaMin() ? BigDecimal.valueOf(advancedFilter.getCpaMin()) : null);
            param.setCpaMax(advancedFilter.hasCpaMax() ? BigDecimal.valueOf(advancedFilter.getCpaMax()) : null);
            param.setAdSaleNumMin(advancedFilter.hasAdSaleNumMin() ? advancedFilter.getAdSaleNumMin() : null);
            param.setAdSaleNumMax(advancedFilter.hasAdSaleNumMax() ? advancedFilter.getAdSaleNumMax() : null);
            param.setAdOtherOrderNumMin(advancedFilter.hasAdOtherOrderNumMin() ? advancedFilter.getAdOtherOrderNumMin() : null);
            param.setAdOtherOrderNumMax(advancedFilter.hasAdOtherOrderNumMax() ? advancedFilter.getAdOtherOrderNumMax() : null);
            param.setAdSalesMin(advancedFilter.hasAdSalesMin() ? BigDecimal.valueOf(advancedFilter.getAdSalesMin()) : null);
            param.setAdSalesMax(advancedFilter.hasAdSalesMax() ? BigDecimal.valueOf(advancedFilter.getAdSalesMax()) : null);
            param.setAdOtherSalesMin(advancedFilter.hasAdOtherSalesMin() ? BigDecimal.valueOf(advancedFilter.getAdOtherSalesMin()) : null);
            param.setAdOtherSalesMax(advancedFilter.hasAdOtherSalesMax() ? BigDecimal.valueOf(advancedFilter.getAdOtherSalesMax()) : null);
            param.setAdSelfSaleNumMin(advancedFilter.hasAdSelfSaleNumMin() ? advancedFilter.getAdSelfSaleNumMin() : null);
            param.setAdSelfSaleNumMax(advancedFilter.hasAdSelfSaleNumMax() ? advancedFilter.getAdSelfSaleNumMax() : null);
            param.setAdOtherSaleNumMin(advancedFilter.hasAdOtherSaleNumMin() ? advancedFilter.getAdOtherSaleNumMin() : null);
            param.setAdOtherSaleNumMax(advancedFilter.hasAdOtherSaleNumMax() ? advancedFilter.getAdOtherSaleNumMax() : null);
            param.setAdSalesTotalMin(advancedFilter.hasAdSalesTotalMin() ? advancedFilter.getAdSalesTotalMin() : null);
            param.setAdSalesTotalMax(advancedFilter.hasAdSalesTotalMax() ? advancedFilter.getAdSalesTotalMax() : null);
            param.setAdvertisingUnitPriceMin(advancedFilter.hasAdvertisingUnitPriceMin() ? new BigDecimal(String.valueOf(advancedFilter.getAdvertisingUnitPriceMin())) : null);
            param.setAdvertisingUnitPriceMax(advancedFilter.hasAdvertisingUnitPriceMax() ? new BigDecimal(String.valueOf(advancedFilter.getAdvertisingUnitPriceMax())) : null);
            param.setVcpmMin(advancedFilter.hasVcpmMin() ? BigDecimal.valueOf(advancedFilter.getVcpmMin()) : null);
            param.setVcpmMax(advancedFilter.hasVcpmMax() ? BigDecimal.valueOf(advancedFilter.getVcpmMax()) : null);
            param.setViewImpressionsMin(advancedFilter.hasViewImpressionsMin() ? advancedFilter.getViewImpressionsMin() : null);
            param.setViewImpressionsMax(advancedFilter.hasViewImpressionsMax() ? advancedFilter.getViewImpressionsMax() : null);
            param.setOrdersNewToBrandFTDMin(advancedFilter.hasOrdersNewToBrandFTDMin() ? advancedFilter.getOrdersNewToBrandFTDMin() : null);
            param.setOrdersNewToBrandFTDMax(advancedFilter.hasOrdersNewToBrandFTDMax() ? advancedFilter.getOrdersNewToBrandFTDMax() : null);
            param.setOrderRateNewToBrandFTDMin(advancedFilter.hasOrderRateNewToBrandFTDMin() ? BigDecimal.valueOf(advancedFilter.getOrderRateNewToBrandFTDMin()) : null);
            param.setOrderRateNewToBrandFTDMax(advancedFilter.hasOrderRateNewToBrandFTDMax() ? BigDecimal.valueOf(advancedFilter.getOrderRateNewToBrandFTDMax()) : null);
            param.setUnitsOrderedNewToBrandFTDMin(advancedFilter.hasUnitsOrderedNewToBrandFTDMin() ? advancedFilter.getUnitsOrderedNewToBrandFTDMin() : null);
            param.setUnitsOrderedNewToBrandFTDMax(advancedFilter.hasUnitsOrderedNewToBrandFTDMax() ? advancedFilter.getUnitsOrderedNewToBrandFTDMax() : null);
            param.setSalesNewToBrandFTDMin(advancedFilter.hasSalesNewToBrandFTDMin() ? BigDecimal.valueOf(advancedFilter.getSalesNewToBrandFTDMin()) : null);
            param.setSalesNewToBrandFTDMax(advancedFilter.hasSalesNewToBrandFTDMax() ? BigDecimal.valueOf(advancedFilter.getSalesNewToBrandFTDMax()) : null);
            param.setSalesRateNewToBrandFTDMin(advancedFilter.hasSalesRateNewToBrandFTDMin() ? BigDecimal.valueOf(advancedFilter.getSalesRateNewToBrandFTDMin()) : null);
            param.setSalesRateNewToBrandFTDMax(advancedFilter.hasSalesRateNewToBrandFTDMax() ? BigDecimal.valueOf(advancedFilter.getSalesRateNewToBrandFTDMax()) : null);
            param.setUnitsOrderedRateNewToBrandFTDMin(advancedFilter.hasUnitsOrderedRateNewToBrandFTDMin() ? BigDecimal.valueOf(advancedFilter.getUnitsOrderedRateNewToBrandFTDMin()) : null);
            param.setUnitsOrderedRateNewToBrandFTDMax(advancedFilter.hasUnitsOrderedRateNewToBrandFTDMax() ? BigDecimal.valueOf(advancedFilter.getUnitsOrderedRateNewToBrandFTDMax()) : null);
        }

        //做参数校验
        String err = checkCategoryTargetPageParam(param, false);
        if (StringUtils.isNotBlank(err)) {
            log.error("查询商品投放视图列表页参数校验异常 {}", request);
            builder.setCode(Result.ERROR);
            builder.setMsg(err);
        } else {
            CategoryTargetViewResponse.TargetVo targetVo;
            //新版支持类型多选，后续删除旧分支即可
//            if (Constants.SP.equalsIgnoreCase(param.getType())) {
//                targetVo = viewManageService.getCategoryTargetView(param.getPuid(), param);
//            } else {
                targetVo = viewManageService.getAllCategoryTargetView(param.getPuid(), param);
//            }
            builder.setData(targetVo);
            builder.setCode(Result.SUCCESS);
        }
        log.info("产品透视分析--商品投放视图列表页接口调用花费时间 {} ,puid: {},marketplaceId: {},shopIds: {},searchType: {},searchValue: {},startDate：{},endDate：{}", (Instant.now().toEpochMilli() - t),
                param.getPuid(), param.getMarketplaceId(), StringUtil.joinInt(param.getShopIdList()), param.getSearchType(), param.getSearchValue(), param.getStartDate(), param.getEndDate());
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * 查询商品投放视图（汇总）
     */
    @Override
    public void getCategoryTargetViewAggregate(CategoryTargetViewAggregateRequest request, StreamObserver<CategoryTargetViewAggregateResponse> responseObserver) {
        log.info("查询商品投放视图列表页 {}", request);
        long t = Instant.now().toEpochMilli();
        CategoryTargetViewAggregateResponse.Builder builder = CategoryTargetViewAggregateResponse.newBuilder();

        if (StringUtils.isBlank(request.getSearchValue()) || StringUtils.isBlank(request.getSearchType())
                || ViewBaseParam.SearchTypeEnum.getEnum(request.getSearchType()) == null) {
            builder.setCode(Result.SUCCESS);
            CategoryTargetViewAggregateResponse.AggregateVo.Builder aggregateVo = CategoryTargetViewAggregateResponse.AggregateVo.newBuilder();
            //总汇总初始化
            aggregateVo.setAggregateVo(viewManageService.buildTargetAggregateViewVo(null));
            builder.setData(aggregateVo);
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
            return;
        }
        TargetViewParam param = new TargetViewParam();
        param.setPageSign(request.getPageSign());
        param.setPageNo(request.getPageNo());
        param.setPageSize(request.getPageSize());
        param.setMarketplaceId(request.getMarketplaceId());
        param.setShopIdList(request.getShopIdListList());
        param.setSearchType(request.getSearchType());
        param.setSearchValue(request.getSearchValue());
        param.setType(request.getType());
        param.setStartDate(request.getStartDate());
        param.setEndDate(request.getEndDate());
        param.setOrderField(request.getOrderField());
        param.setOrderType(request.getOrderType());
        param.setQueryField(request.getQueryField());
        param.setQueryType(request.getQueryType());
        param.setQueryValue(request.getQueryValue());

        param.setCampaignId(request.getCampaignId());
        param.setGroupId(request.getGroupId());
        param.setSelectType(request.getSelectType());
        param.setTargetType(request.getTargetType());
        param.setStatus(request.getStatus());
        param.setServingStatus(request.getServingStatus());

        param.setPuid(request.getPuid());
        //环比数据传参
        param.setIsCompare(request.getIsCompare());
        param.setCompareStartDate(request.getCompareStartDate());
        param.setCompareEndDate(request.getCompareEndDate());

        if (request.hasUseAdvanced()) {  //是否开启高级搜索
            param.setUseAdvanced(request.getUseAdvanced());
        }

        if (request.hasUseAdvanced() && request.getUseAdvanced()) {  //高级筛选
            AdAdvancedFilterData advancedFilter = request.getAdvancedFilter();
            param.setImpressionsMin(advancedFilter.hasImpressionsMin() ? advancedFilter.getImpressionsMin() : null);
            param.setImpressionsMax(advancedFilter.hasImpressionsMax() ? advancedFilter.getImpressionsMax() : null);
            param.setClicksMin(advancedFilter.hasClicksMin() ? advancedFilter.getClicksMin() : null);
            param.setClicksMax(advancedFilter.hasClicksMax() ? advancedFilter.getClicksMax() : null);
            param.setClickRateMin(advancedFilter.hasClickRateMin() ? BigDecimal.valueOf(advancedFilter.getClickRateMin()) : null);
            param.setClickRateMax(advancedFilter.hasClickRateMax() ? BigDecimal.valueOf(advancedFilter.getClickRateMax()) : null);
            param.setCostMin(advancedFilter.hasCostMin() ? BigDecimal.valueOf(advancedFilter.getCostMin()) : null);
            param.setCostMax(advancedFilter.hasCostMax() ? BigDecimal.valueOf(advancedFilter.getCostMax()) : null);
            param.setCpcMin(advancedFilter.hasCpcMin() ? BigDecimal.valueOf(advancedFilter.getCpcMin()) : null);
            param.setCpcMax(advancedFilter.hasCpcMax() ? BigDecimal.valueOf(advancedFilter.getCpcMax()) : null);
            param.setOrderNumMin(advancedFilter.hasOrderNumMin() ? advancedFilter.getOrderNumMin() : null);
            param.setOrderNumMax(advancedFilter.hasOrderNumMax() ? advancedFilter.getOrderNumMax() : null);
            param.setSalesMin(advancedFilter.hasSalesMin() ? BigDecimal.valueOf(advancedFilter.getSalesMin()) : null);
            param.setSalesMax(advancedFilter.hasSalesMax() ? BigDecimal.valueOf(advancedFilter.getSalesMax()) : null);
            param.setAcosMin(advancedFilter.hasAcosMin() ? BigDecimal.valueOf(advancedFilter.getAcosMin()) : null);
            param.setAcosMax(advancedFilter.hasAcosMax() ? BigDecimal.valueOf(advancedFilter.getAcosMax()) : null);
            param.setRoasMin(advancedFilter.hasRoasMin() ? BigDecimal.valueOf(advancedFilter.getRoasMin()) : null);
            param.setRoasMax(advancedFilter.hasRoasMax() ? BigDecimal.valueOf(advancedFilter.getRoasMax()) : null);
            param.setSalesConversionRateMin(advancedFilter.hasSalesConversionRateMin() ? BigDecimal.valueOf(advancedFilter.getSalesConversionRateMin()) : null);
            param.setSalesConversionRateMax(advancedFilter.hasSalesConversionRateMax() ? BigDecimal.valueOf(advancedFilter.getSalesConversionRateMax()): null);
            param.setAcotsMin(advancedFilter.hasAcotsMin() ? BigDecimal.valueOf(advancedFilter.getAcotsMin()) :null);
            param.setAcotsMax(advancedFilter.hasAcotsMax() ? BigDecimal.valueOf(advancedFilter.getAcotsMax()) : null);
            param.setAsotsMin(advancedFilter.hasAsotsMin() ? BigDecimal.valueOf(advancedFilter.getAsotsMin()) :null);
            param.setAsotsMax(advancedFilter.hasAsotsMax() ? BigDecimal.valueOf(advancedFilter.getAsotsMax()) : null);
            param.setCpaMin(advancedFilter.hasCpaMin() ? BigDecimal.valueOf(advancedFilter.getCpaMin()) : null);
            param.setCpaMax(advancedFilter.hasCpaMax() ? BigDecimal.valueOf(advancedFilter.getCpaMax()) : null);
            param.setAdSaleNumMin(advancedFilter.hasAdSaleNumMin() ? advancedFilter.getAdSaleNumMin() : null);
            param.setAdSaleNumMax(advancedFilter.hasAdSaleNumMax() ? advancedFilter.getAdSaleNumMax() : null);
            param.setAdOtherOrderNumMin(advancedFilter.hasAdOtherOrderNumMin() ? advancedFilter.getAdOtherOrderNumMin() : null);
            param.setAdOtherOrderNumMax(advancedFilter.hasAdOtherOrderNumMax() ? advancedFilter.getAdOtherOrderNumMax() : null);
            param.setAdSalesMin(advancedFilter.hasAdSalesMin() ? BigDecimal.valueOf(advancedFilter.getAdSalesMin()) : null);
            param.setAdSalesMax(advancedFilter.hasAdSalesMax() ? BigDecimal.valueOf(advancedFilter.getAdSalesMax()) : null);
            param.setAdOtherSalesMin(advancedFilter.hasAdOtherSalesMin() ? BigDecimal.valueOf(advancedFilter.getAdOtherSalesMin()) : null);
            param.setAdOtherSalesMax(advancedFilter.hasAdOtherSalesMax() ? BigDecimal.valueOf(advancedFilter.getAdOtherSalesMax()) : null);
            param.setAdSelfSaleNumMin(advancedFilter.hasAdSelfSaleNumMin() ? advancedFilter.getAdSelfSaleNumMin() : null);
            param.setAdSelfSaleNumMax(advancedFilter.hasAdSelfSaleNumMax() ? advancedFilter.getAdSelfSaleNumMax() : null);
            param.setAdOtherSaleNumMin(advancedFilter.hasAdOtherSaleNumMin() ? advancedFilter.getAdOtherSaleNumMin() : null);
            param.setAdOtherSaleNumMax(advancedFilter.hasAdOtherSaleNumMax() ? advancedFilter.getAdOtherSaleNumMax() : null);
            param.setAdSalesTotalMin(advancedFilter.hasAdSalesTotalMin() ? advancedFilter.getAdSalesTotalMin() : null);
            param.setAdSalesTotalMax(advancedFilter.hasAdSalesTotalMax() ? advancedFilter.getAdSalesTotalMax() : null);
            param.setAdvertisingUnitPriceMin(advancedFilter.hasAdvertisingUnitPriceMin() ? new BigDecimal(String.valueOf(advancedFilter.getAdvertisingUnitPriceMin())) : null);
            param.setAdvertisingUnitPriceMax(advancedFilter.hasAdvertisingUnitPriceMax() ? new BigDecimal(String.valueOf(advancedFilter.getAdvertisingUnitPriceMax())) : null);
            param.setVcpmMin(advancedFilter.hasVcpmMin() ? BigDecimal.valueOf(advancedFilter.getVcpmMin()) : null);
            param.setVcpmMax(advancedFilter.hasVcpmMax() ? BigDecimal.valueOf(advancedFilter.getVcpmMax()) : null);
            param.setViewImpressionsMin(advancedFilter.hasViewImpressionsMin() ? advancedFilter.getViewImpressionsMin() : null);
            param.setViewImpressionsMax(advancedFilter.hasViewImpressionsMax() ? advancedFilter.getViewImpressionsMax() : null);
            param.setOrdersNewToBrandFTDMin(advancedFilter.hasOrdersNewToBrandFTDMin() ? advancedFilter.getOrdersNewToBrandFTDMin() : null);
            param.setOrdersNewToBrandFTDMax(advancedFilter.hasOrdersNewToBrandFTDMax() ? advancedFilter.getOrdersNewToBrandFTDMax() : null);
            param.setOrderRateNewToBrandFTDMin(advancedFilter.hasOrderRateNewToBrandFTDMin() ? BigDecimal.valueOf(advancedFilter.getOrderRateNewToBrandFTDMin()) : null);
            param.setOrderRateNewToBrandFTDMax(advancedFilter.hasOrderRateNewToBrandFTDMax() ? BigDecimal.valueOf(advancedFilter.getOrderRateNewToBrandFTDMax()) : null);
            param.setUnitsOrderedNewToBrandFTDMin(advancedFilter.hasUnitsOrderedNewToBrandFTDMin() ? advancedFilter.getUnitsOrderedNewToBrandFTDMin() : null);
            param.setUnitsOrderedNewToBrandFTDMax(advancedFilter.hasUnitsOrderedNewToBrandFTDMax() ? advancedFilter.getUnitsOrderedNewToBrandFTDMax() : null);
            param.setSalesNewToBrandFTDMin(advancedFilter.hasSalesNewToBrandFTDMin() ? BigDecimal.valueOf(advancedFilter.getSalesNewToBrandFTDMin()) : null);
            param.setSalesNewToBrandFTDMax(advancedFilter.hasSalesNewToBrandFTDMax() ? BigDecimal.valueOf(advancedFilter.getSalesNewToBrandFTDMax()) : null);
            param.setSalesRateNewToBrandFTDMin(advancedFilter.hasSalesRateNewToBrandFTDMin() ? BigDecimal.valueOf(advancedFilter.getSalesRateNewToBrandFTDMin()) : null);
            param.setSalesRateNewToBrandFTDMax(advancedFilter.hasSalesRateNewToBrandFTDMax() ? BigDecimal.valueOf(advancedFilter.getSalesRateNewToBrandFTDMax()) : null);
            param.setUnitsOrderedRateNewToBrandFTDMin(advancedFilter.hasUnitsOrderedRateNewToBrandFTDMin() ? BigDecimal.valueOf(advancedFilter.getUnitsOrderedRateNewToBrandFTDMin()) : null);
            param.setUnitsOrderedRateNewToBrandFTDMax(advancedFilter.hasUnitsOrderedRateNewToBrandFTDMax() ? BigDecimal.valueOf(advancedFilter.getUnitsOrderedRateNewToBrandFTDMax()) : null);
        }

        //做参数校验
        String err = checkCategoryTargetPageParam(param, true);
        if (StringUtils.isNotBlank(err)) {
            log.error("查询商品投放视图汇总参数校验异常 {}", request);
            builder.setCode(Result.ERROR);
            builder.setMsg(err);
        } else {
            CategoryTargetViewAggregateResponse.AggregateVo aggregateVo;
            //新版支持类型多选，后续删除旧分支即可
//            if (Constants.SP.equalsIgnoreCase(param.getType())) {
//                aggregateVo = viewManageService.getCategoryTargetViewAggregate(param.getPuid(), param);
//            } else {
                aggregateVo = viewManageService.getAllCategoryTargetViewAggregate(param.getPuid(), param);
//            }
            builder.setData(aggregateVo);
            builder.setCode(Result.SUCCESS);
        }
        log.info("产品透视分析--商品投放视图汇总接口调用花费时间 {} ,puid: {},marketplaceId: {},shopIds: {},searchType: {},searchValue: {},startDate：{},endDate：{}", (Instant.now().toEpochMilli() - t),
                param.getPuid(), param.getMarketplaceId(), StringUtil.joinInt(param.getShopIdList()), param.getSearchType(), param.getSearchValue(), param.getStartDate(), param.getEndDate());
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * 查询受众投放视图（列表页）
     */
    @Override
    public void getAudienceTargetView(AudienceTargetViewRequest request, StreamObserver<AudienceTargetViewResponse> responseObserver) {
        log.info("查询受众投放视图列表页 {}", request);
        long t = Instant.now().toEpochMilli();
        AudienceTargetViewResponse.Builder builder = AudienceTargetViewResponse.newBuilder();

        if (StringUtils.isBlank(request.getSearchValue()) || StringUtils.isBlank(request.getSearchType())
                || ViewBaseParam.SearchTypeEnum.getEnum(request.getSearchType()) == null) {
            builder.setCode(Result.SUCCESS);
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
            return;
        }

        AudienceTargetViewParam param = new AudienceTargetViewParam();
        param.setPageNo(request.getPageNo());
        param.setPageSize(request.getPageSize());
        param.setMarketplaceId(request.getMarketplaceId());
        param.setShopIdList(request.getShopIdListList());
        param.setSearchType(request.getSearchType());
        param.setSearchValue(request.getSearchValue());
        param.setTargetText(request.getTargetText());
        param.setType(request.getType());
        param.setStartDate(request.getStartDate());
        param.setEndDate(request.getEndDate());
        param.setOrderField(request.getOrderField());
        param.setOrderType(request.getOrderType());

        param.setCampaignId(request.getCampaignId());
        param.setGroupId(request.getGroupId());
        param.setTargetType(request.getTargetType());
        param.setStatus(request.getStatus());
        param.setServingStatus(request.getServingStatus());

        param.setPuid(request.getPuid());
        //环比数据传参
        param.setIsCompare(request.getIsCompare());
        param.setCompareStartDate(request.getCompareStartDate());
        param.setCompareEndDate(request.getCompareEndDate());

        if (request.hasUseAdvanced()) {  //是否开启高级搜索
            param.setUseAdvanced(request.getUseAdvanced());
        }

        if (request.hasUseAdvanced() && request.getUseAdvanced()) {  //高级筛选
            AdAdvancedFilterData advancedFilter = request.getAdvancedFilter();
            param.setImpressionsMin(advancedFilter.hasImpressionsMin() ? advancedFilter.getImpressionsMin() : null);
            param.setImpressionsMax(advancedFilter.hasImpressionsMax() ? advancedFilter.getImpressionsMax() : null);
            param.setClicksMin(advancedFilter.hasClicksMin() ? advancedFilter.getClicksMin() : null);
            param.setClicksMax(advancedFilter.hasClicksMax() ? advancedFilter.getClicksMax() : null);
            param.setClickRateMin(advancedFilter.hasClickRateMin() ? BigDecimal.valueOf(advancedFilter.getClickRateMin()) : null);
            param.setClickRateMax(advancedFilter.hasClickRateMax() ? BigDecimal.valueOf(advancedFilter.getClickRateMax()) : null);
            param.setCostMin(advancedFilter.hasCostMin() ? BigDecimal.valueOf(advancedFilter.getCostMin()) : null);
            param.setCostMax(advancedFilter.hasCostMax() ? BigDecimal.valueOf(advancedFilter.getCostMax()) : null);
            param.setCpcMin(advancedFilter.hasCpcMin() ? BigDecimal.valueOf(advancedFilter.getCpcMin()) : null);
            param.setCpcMax(advancedFilter.hasCpcMax() ? BigDecimal.valueOf(advancedFilter.getCpcMax()) : null);
            param.setOrderNumMin(advancedFilter.hasOrderNumMin() ? advancedFilter.getOrderNumMin() : null);
            param.setOrderNumMax(advancedFilter.hasOrderNumMax() ? advancedFilter.getOrderNumMax() : null);
            param.setSalesMin(advancedFilter.hasSalesMin() ? BigDecimal.valueOf(advancedFilter.getSalesMin()) : null);
            param.setSalesMax(advancedFilter.hasSalesMax() ? BigDecimal.valueOf(advancedFilter.getSalesMax()) : null);
            param.setAcosMin(advancedFilter.hasAcosMin() ? BigDecimal.valueOf(advancedFilter.getAcosMin()) : null);
            param.setAcosMax(advancedFilter.hasAcosMax() ? BigDecimal.valueOf(advancedFilter.getAcosMax()) : null);
            param.setRoasMin(advancedFilter.hasRoasMin() ? BigDecimal.valueOf(advancedFilter.getRoasMin()) : null);
            param.setRoasMax(advancedFilter.hasRoasMax() ? BigDecimal.valueOf(advancedFilter.getRoasMax()) : null);
            param.setSalesConversionRateMin(advancedFilter.hasSalesConversionRateMin() ? BigDecimal.valueOf(advancedFilter.getSalesConversionRateMin()) : null);
            param.setSalesConversionRateMax(advancedFilter.hasSalesConversionRateMax() ? BigDecimal.valueOf(advancedFilter.getSalesConversionRateMax()): null);
            param.setAcotsMin(advancedFilter.hasAcotsMin() ? BigDecimal.valueOf(advancedFilter.getAcotsMin()) :null);
            param.setAcotsMax(advancedFilter.hasAcotsMax() ? BigDecimal.valueOf(advancedFilter.getAcotsMax()) : null);
            param.setAsotsMin(advancedFilter.hasAsotsMin() ? BigDecimal.valueOf(advancedFilter.getAsotsMin()) :null);
            param.setAsotsMax(advancedFilter.hasAsotsMax() ? BigDecimal.valueOf(advancedFilter.getAsotsMax()) : null);
            param.setCpaMin(advancedFilter.hasCpaMin() ? BigDecimal.valueOf(advancedFilter.getCpaMin()) : null);
            param.setCpaMax(advancedFilter.hasCpaMax() ? BigDecimal.valueOf(advancedFilter.getCpaMax()) : null);
            param.setAdSaleNumMin(advancedFilter.hasAdSaleNumMin() ? advancedFilter.getAdSaleNumMin() : null);
            param.setAdSaleNumMax(advancedFilter.hasAdSaleNumMax() ? advancedFilter.getAdSaleNumMax() : null);
            param.setAdOtherOrderNumMin(advancedFilter.hasAdOtherOrderNumMin() ? advancedFilter.getAdOtherOrderNumMin() : null);
            param.setAdOtherOrderNumMax(advancedFilter.hasAdOtherOrderNumMax() ? advancedFilter.getAdOtherOrderNumMax() : null);
            param.setAdSalesMin(advancedFilter.hasAdSalesMin() ? BigDecimal.valueOf(advancedFilter.getAdSalesMin()) : null);
            param.setAdSalesMax(advancedFilter.hasAdSalesMax() ? BigDecimal.valueOf(advancedFilter.getAdSalesMax()) : null);
            param.setAdOtherSalesMin(advancedFilter.hasAdOtherSalesMin() ? BigDecimal.valueOf(advancedFilter.getAdOtherSalesMin()) : null);
            param.setAdOtherSalesMax(advancedFilter.hasAdOtherSalesMax() ? BigDecimal.valueOf(advancedFilter.getAdOtherSalesMax()) : null);
            param.setAdSelfSaleNumMin(advancedFilter.hasAdSelfSaleNumMin() ? advancedFilter.getAdSelfSaleNumMin() : null);
            param.setAdSelfSaleNumMax(advancedFilter.hasAdSelfSaleNumMax() ? advancedFilter.getAdSelfSaleNumMax() : null);
            param.setAdOtherSaleNumMin(advancedFilter.hasAdOtherSaleNumMin() ? advancedFilter.getAdOtherSaleNumMin() : null);
            param.setAdOtherSaleNumMax(advancedFilter.hasAdOtherSaleNumMax() ? advancedFilter.getAdOtherSaleNumMax() : null);
            param.setAdSalesTotalMin(advancedFilter.hasAdSalesTotalMin() ? advancedFilter.getAdSalesTotalMin() : null);
            param.setAdSalesTotalMax(advancedFilter.hasAdSalesTotalMax() ? advancedFilter.getAdSalesTotalMax() : null);
            param.setAdvertisingUnitPriceMin(advancedFilter.hasAdvertisingUnitPriceMin() ? new BigDecimal(String.valueOf(advancedFilter.getAdvertisingUnitPriceMin())) : null);
            param.setAdvertisingUnitPriceMax(advancedFilter.hasAdvertisingUnitPriceMax() ? new BigDecimal(String.valueOf(advancedFilter.getAdvertisingUnitPriceMax())) : null);
            param.setVcpmMin(advancedFilter.hasVcpmMin() ? BigDecimal.valueOf(advancedFilter.getVcpmMin()) : null);
            param.setVcpmMax(advancedFilter.hasVcpmMax() ? BigDecimal.valueOf(advancedFilter.getVcpmMax()) : null);
            param.setViewImpressionsMin(advancedFilter.hasViewImpressionsMin() ? advancedFilter.getViewImpressionsMin() : null);
            param.setViewImpressionsMax(advancedFilter.hasViewImpressionsMax() ? advancedFilter.getViewImpressionsMax() : null);
            param.setOrdersNewToBrandFTDMin(advancedFilter.hasOrdersNewToBrandFTDMin() ? advancedFilter.getOrdersNewToBrandFTDMin() : null);
            param.setOrdersNewToBrandFTDMax(advancedFilter.hasOrdersNewToBrandFTDMax() ? advancedFilter.getOrdersNewToBrandFTDMax() : null);
            param.setOrderRateNewToBrandFTDMin(advancedFilter.hasOrderRateNewToBrandFTDMin() ? BigDecimal.valueOf(advancedFilter.getOrderRateNewToBrandFTDMin()) : null);
            param.setOrderRateNewToBrandFTDMax(advancedFilter.hasOrderRateNewToBrandFTDMax() ? BigDecimal.valueOf(advancedFilter.getOrderRateNewToBrandFTDMax()) : null);
            param.setUnitsOrderedNewToBrandFTDMin(advancedFilter.hasUnitsOrderedNewToBrandFTDMin() ? advancedFilter.getUnitsOrderedNewToBrandFTDMin() : null);
            param.setUnitsOrderedNewToBrandFTDMax(advancedFilter.hasUnitsOrderedNewToBrandFTDMax() ? advancedFilter.getUnitsOrderedNewToBrandFTDMax() : null);
            param.setSalesNewToBrandFTDMin(advancedFilter.hasSalesNewToBrandFTDMin() ? BigDecimal.valueOf(advancedFilter.getSalesNewToBrandFTDMin()) : null);
            param.setSalesNewToBrandFTDMax(advancedFilter.hasSalesNewToBrandFTDMax() ? BigDecimal.valueOf(advancedFilter.getSalesNewToBrandFTDMax()) : null);
            param.setSalesRateNewToBrandFTDMin(advancedFilter.hasSalesRateNewToBrandFTDMin() ? BigDecimal.valueOf(advancedFilter.getSalesRateNewToBrandFTDMin()) : null);
            param.setSalesRateNewToBrandFTDMax(advancedFilter.hasSalesRateNewToBrandFTDMax() ? BigDecimal.valueOf(advancedFilter.getSalesRateNewToBrandFTDMax()) : null);
            param.setUnitsOrderedRateNewToBrandFTDMin(advancedFilter.hasUnitsOrderedRateNewToBrandFTDMin() ? BigDecimal.valueOf(advancedFilter.getUnitsOrderedRateNewToBrandFTDMin()) : null);
            param.setUnitsOrderedRateNewToBrandFTDMax(advancedFilter.hasUnitsOrderedRateNewToBrandFTDMax() ? BigDecimal.valueOf(advancedFilter.getUnitsOrderedRateNewToBrandFTDMax()) : null);
        }

        //做参数校验
        String err = checkAudienceTargetPageParam(param, false);
        if (StringUtils.isNotBlank(err)) {
            log.error("查询受众投放视图列表页参数校验异常 {}", request);
            builder.setCode(Result.ERROR);
            builder.setMsg(err);
        } else {
            AudienceTargetViewResponse.TargetVo targetVo = viewManageService.getAllAudienceTargetView(param.getPuid(), param);
            builder.setData(targetVo);
            builder.setCode(Result.SUCCESS);
        }
        log.info("产品透视分析--受众投放视图列表页接口调用花费时间 {} ,puid: {},marketplaceId: {},shopIds: {},searchType: {},searchValue: {},startDate：{},endDate：{}", (Instant.now().toEpochMilli() - t),
                param.getPuid(), param.getMarketplaceId(), StringUtil.joinInt(param.getShopIdList()), param.getSearchType(), param.getSearchValue(), param.getStartDate(), param.getEndDate());
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * 查询受众投放视图（汇总）
     */
    @Override
    public void getAudienceTargetViewAggregate(AudienceTargetViewAggregateRequest request, StreamObserver<AudienceTargetViewAggregateResponse> responseObserver) {
        log.info("查询受众投放视图列表页 {}", request);
        long t = Instant.now().toEpochMilli();
        AudienceTargetViewAggregateResponse.Builder builder = AudienceTargetViewAggregateResponse.newBuilder();

        if (StringUtils.isBlank(request.getSearchValue()) || StringUtils.isBlank(request.getSearchType())
                || ViewBaseParam.SearchTypeEnum.getEnum(request.getSearchType()) == null) {
            builder.setCode(Result.SUCCESS);
            AudienceTargetViewAggregateResponse.AggregateVo.Builder aggregateVo = AudienceTargetViewAggregateResponse.AggregateVo.newBuilder();
            //总汇总初始化
            aggregateVo.setAggregateVo(viewManageService.buildAudienceTargetAggregateViewVo(null));
            builder.setData(aggregateVo);
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
            return;
        }
        AudienceTargetViewParam param = new AudienceTargetViewParam();
        param.setPageSign(request.getPageSign());
        param.setPageNo(request.getPageNo());
        param.setPageSize(request.getPageSize());
        param.setMarketplaceId(request.getMarketplaceId());
        param.setShopIdList(request.getShopIdListList());
        param.setSearchType(request.getSearchType());
        param.setSearchValue(request.getSearchValue());
        param.setType(request.getType());
        param.setStartDate(request.getStartDate());
        param.setEndDate(request.getEndDate());
        param.setOrderField(request.getOrderField());
        param.setOrderType(request.getOrderType());
        param.setQueryValue(request.getQueryValue());

        param.setCampaignId(request.getCampaignId());
        param.setGroupId(request.getGroupId());
        param.setTargetType(request.getTargetType());
        param.setStatus(request.getStatus());
        param.setServingStatus(request.getServingStatus());

        param.setPuid(request.getPuid());
        //环比数据传参
        param.setIsCompare(request.getIsCompare());
        param.setCompareStartDate(request.getCompareStartDate());
        param.setCompareEndDate(request.getCompareEndDate());

        if (request.hasUseAdvanced()) {  //是否开启高级搜索
            param.setUseAdvanced(request.getUseAdvanced());
        }

        if (request.hasUseAdvanced() && request.getUseAdvanced()) {  //高级筛选
            AdAdvancedFilterData advancedFilter = request.getAdvancedFilter();
            param.setImpressionsMin(advancedFilter.hasImpressionsMin() ? advancedFilter.getImpressionsMin() : null);
            param.setImpressionsMax(advancedFilter.hasImpressionsMax() ? advancedFilter.getImpressionsMax() : null);
            param.setClicksMin(advancedFilter.hasClicksMin() ? advancedFilter.getClicksMin() : null);
            param.setClicksMax(advancedFilter.hasClicksMax() ? advancedFilter.getClicksMax() : null);
            param.setClickRateMin(advancedFilter.hasClickRateMin() ? BigDecimal.valueOf(advancedFilter.getClickRateMin()) : null);
            param.setClickRateMax(advancedFilter.hasClickRateMax() ? BigDecimal.valueOf(advancedFilter.getClickRateMax()) : null);
            param.setCostMin(advancedFilter.hasCostMin() ? BigDecimal.valueOf(advancedFilter.getCostMin()) : null);
            param.setCostMax(advancedFilter.hasCostMax() ? BigDecimal.valueOf(advancedFilter.getCostMax()) : null);
            param.setCpcMin(advancedFilter.hasCpcMin() ? BigDecimal.valueOf(advancedFilter.getCpcMin()) : null);
            param.setCpcMax(advancedFilter.hasCpcMax() ? BigDecimal.valueOf(advancedFilter.getCpcMax()) : null);
            param.setOrderNumMin(advancedFilter.hasOrderNumMin() ? advancedFilter.getOrderNumMin() : null);
            param.setOrderNumMax(advancedFilter.hasOrderNumMax() ? advancedFilter.getOrderNumMax() : null);
            param.setSalesMin(advancedFilter.hasSalesMin() ? BigDecimal.valueOf(advancedFilter.getSalesMin()) : null);
            param.setSalesMax(advancedFilter.hasSalesMax() ? BigDecimal.valueOf(advancedFilter.getSalesMax()) : null);
            param.setAcosMin(advancedFilter.hasAcosMin() ? BigDecimal.valueOf(advancedFilter.getAcosMin()) : null);
            param.setAcosMax(advancedFilter.hasAcosMax() ? BigDecimal.valueOf(advancedFilter.getAcosMax()) : null);
            param.setRoasMin(advancedFilter.hasRoasMin() ? BigDecimal.valueOf(advancedFilter.getRoasMin()) : null);
            param.setRoasMax(advancedFilter.hasRoasMax() ? BigDecimal.valueOf(advancedFilter.getRoasMax()) : null);
            param.setSalesConversionRateMin(advancedFilter.hasSalesConversionRateMin() ? BigDecimal.valueOf(advancedFilter.getSalesConversionRateMin()) : null);
            param.setSalesConversionRateMax(advancedFilter.hasSalesConversionRateMax() ? BigDecimal.valueOf(advancedFilter.getSalesConversionRateMax()): null);
            param.setAcotsMin(advancedFilter.hasAcotsMin() ? BigDecimal.valueOf(advancedFilter.getAcotsMin()) :null);
            param.setAcotsMax(advancedFilter.hasAcotsMax() ? BigDecimal.valueOf(advancedFilter.getAcotsMax()) : null);
            param.setAsotsMin(advancedFilter.hasAsotsMin() ? BigDecimal.valueOf(advancedFilter.getAsotsMin()) :null);
            param.setAsotsMax(advancedFilter.hasAsotsMax() ? BigDecimal.valueOf(advancedFilter.getAsotsMax()) : null);
            param.setCpaMin(advancedFilter.hasCpaMin() ? BigDecimal.valueOf(advancedFilter.getCpaMin()) : null);
            param.setCpaMax(advancedFilter.hasCpaMax() ? BigDecimal.valueOf(advancedFilter.getCpaMax()) : null);
            param.setAdSaleNumMin(advancedFilter.hasAdSaleNumMin() ? advancedFilter.getAdSaleNumMin() : null);
            param.setAdSaleNumMax(advancedFilter.hasAdSaleNumMax() ? advancedFilter.getAdSaleNumMax() : null);
            param.setAdOtherOrderNumMin(advancedFilter.hasAdOtherOrderNumMin() ? advancedFilter.getAdOtherOrderNumMin() : null);
            param.setAdOtherOrderNumMax(advancedFilter.hasAdOtherOrderNumMax() ? advancedFilter.getAdOtherOrderNumMax() : null);
            param.setAdSalesMin(advancedFilter.hasAdSalesMin() ? BigDecimal.valueOf(advancedFilter.getAdSalesMin()) : null);
            param.setAdSalesMax(advancedFilter.hasAdSalesMax() ? BigDecimal.valueOf(advancedFilter.getAdSalesMax()) : null);
            param.setAdOtherSalesMin(advancedFilter.hasAdOtherSalesMin() ? BigDecimal.valueOf(advancedFilter.getAdOtherSalesMin()) : null);
            param.setAdOtherSalesMax(advancedFilter.hasAdOtherSalesMax() ? BigDecimal.valueOf(advancedFilter.getAdOtherSalesMax()) : null);
            param.setAdSelfSaleNumMin(advancedFilter.hasAdSelfSaleNumMin() ? advancedFilter.getAdSelfSaleNumMin() : null);
            param.setAdSelfSaleNumMax(advancedFilter.hasAdSelfSaleNumMax() ? advancedFilter.getAdSelfSaleNumMax() : null);
            param.setAdOtherSaleNumMin(advancedFilter.hasAdOtherSaleNumMin() ? advancedFilter.getAdOtherSaleNumMin() : null);
            param.setAdOtherSaleNumMax(advancedFilter.hasAdOtherSaleNumMax() ? advancedFilter.getAdOtherSaleNumMax() : null);
            param.setAdSalesTotalMin(advancedFilter.hasAdSalesTotalMin() ? advancedFilter.getAdSalesTotalMin() : null);
            param.setAdSalesTotalMax(advancedFilter.hasAdSalesTotalMax() ? advancedFilter.getAdSalesTotalMax() : null);
            param.setAdvertisingUnitPriceMin(advancedFilter.hasAdvertisingUnitPriceMin() ? new BigDecimal(String.valueOf(advancedFilter.getAdvertisingUnitPriceMin())) : null);
            param.setAdvertisingUnitPriceMax(advancedFilter.hasAdvertisingUnitPriceMax() ? new BigDecimal(String.valueOf(advancedFilter.getAdvertisingUnitPriceMax())) : null);
            param.setVcpmMin(advancedFilter.hasVcpmMin() ? BigDecimal.valueOf(advancedFilter.getVcpmMin()) : null);
            param.setVcpmMax(advancedFilter.hasVcpmMax() ? BigDecimal.valueOf(advancedFilter.getVcpmMax()) : null);
            param.setViewImpressionsMin(advancedFilter.hasViewImpressionsMin() ? advancedFilter.getViewImpressionsMin() : null);
            param.setViewImpressionsMax(advancedFilter.hasViewImpressionsMax() ? advancedFilter.getViewImpressionsMax() : null);
            param.setOrdersNewToBrandFTDMin(advancedFilter.hasOrdersNewToBrandFTDMin() ? advancedFilter.getOrdersNewToBrandFTDMin() : null);
            param.setOrdersNewToBrandFTDMax(advancedFilter.hasOrdersNewToBrandFTDMax() ? advancedFilter.getOrdersNewToBrandFTDMax() : null);
            param.setOrderRateNewToBrandFTDMin(advancedFilter.hasOrderRateNewToBrandFTDMin() ? BigDecimal.valueOf(advancedFilter.getOrderRateNewToBrandFTDMin()) : null);
            param.setOrderRateNewToBrandFTDMax(advancedFilter.hasOrderRateNewToBrandFTDMax() ? BigDecimal.valueOf(advancedFilter.getOrderRateNewToBrandFTDMax()) : null);
            param.setUnitsOrderedNewToBrandFTDMin(advancedFilter.hasUnitsOrderedNewToBrandFTDMin() ? advancedFilter.getUnitsOrderedNewToBrandFTDMin() : null);
            param.setUnitsOrderedNewToBrandFTDMax(advancedFilter.hasUnitsOrderedNewToBrandFTDMax() ? advancedFilter.getUnitsOrderedNewToBrandFTDMax() : null);
            param.setSalesNewToBrandFTDMin(advancedFilter.hasSalesNewToBrandFTDMin() ? BigDecimal.valueOf(advancedFilter.getSalesNewToBrandFTDMin()) : null);
            param.setSalesNewToBrandFTDMax(advancedFilter.hasSalesNewToBrandFTDMax() ? BigDecimal.valueOf(advancedFilter.getSalesNewToBrandFTDMax()) : null);
            param.setSalesRateNewToBrandFTDMin(advancedFilter.hasSalesRateNewToBrandFTDMin() ? BigDecimal.valueOf(advancedFilter.getSalesRateNewToBrandFTDMin()) : null);
            param.setSalesRateNewToBrandFTDMax(advancedFilter.hasSalesRateNewToBrandFTDMax() ? BigDecimal.valueOf(advancedFilter.getSalesRateNewToBrandFTDMax()) : null);
            param.setUnitsOrderedRateNewToBrandFTDMin(advancedFilter.hasUnitsOrderedRateNewToBrandFTDMin() ? BigDecimal.valueOf(advancedFilter.getUnitsOrderedRateNewToBrandFTDMin()) : null);
            param.setUnitsOrderedRateNewToBrandFTDMax(advancedFilter.hasUnitsOrderedRateNewToBrandFTDMax() ? BigDecimal.valueOf(advancedFilter.getUnitsOrderedRateNewToBrandFTDMax()) : null);
        }

        //做参数校验
        String err = checkAudienceTargetPageParam(param, true);
        if (StringUtils.isNotBlank(err)) {
            log.error("查询受众投放视图汇总参数校验异常 {}", request);
            builder.setCode(Result.ERROR);
            builder.setMsg(err);
        } else {
            AudienceTargetViewAggregateResponse.AggregateVo aggregateVo = viewManageService.getAllAudienceTargetViewAggregate(param.getPuid(), param);
            builder.setData(aggregateVo);
            builder.setCode(Result.SUCCESS);
        }
        log.info("产品透视分析--受众投放视图汇总接口调用花费时间 {} ,puid: {},marketplaceId: {},shopIds: {},searchType: {},searchValue: {},startDate：{},endDate：{}", (Instant.now().toEpochMilli() - t),
                param.getPuid(), param.getMarketplaceId(), StringUtil.joinInt(param.getShopIdList()), param.getSearchType(), param.getSearchValue(), param.getStartDate(), param.getEndDate());
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    // 校验查询的参数
    public String checkAutoTargetPageParam(TargetViewParam param) {
        if (param == null || StringUtils.isBlank(param.getMarketplaceId())
                || CollectionUtils.isEmpty(param.getShopIdList()) || StringUtils.isBlank(param.getSearchValue()) || StringUtils.isBlank(param.getSearchType())
                || ViewBaseParam.SearchTypeEnum.getEnum(param.getSearchType()) == null) {
            return "请求参数错误";
        }
        if (StringUtils.isBlank(param.getType())) {
            return "请求参数错误";
        } else {
            String campaignValue = CampaignTypeEnum.getCampaignValue(param.getType());
            if (StringUtils.isBlank(campaignValue)) {
                return "请求参数错误";
            }
        }
        if (param.getPageNo() == null || param.getPageNo() <= 0) {
            param.setPageNo(1);
        }
        if (param.getPageSize() == null || param.getPageSize() <= 0) {
            param.setPageSize(20);
        }
        if (StringUtils.isBlank(param.getStartDate()) || StringUtils.isBlank(param.getEndDate())) {
            param.setStartDate(LocalDate.now().minusDays(7).format(DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD)));
            param.setEndDate(LocalDate.now().format(DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD)));
        }
        //匹配方式校验
        if (StringUtils.isNotBlank(param.getTargetType())) {
            if (StringUtils.isBlank(AutoTargetTypeEnum.getAutoTargetValue(param.getTargetType()))) {
                return "请求参数错误";
            }
        }
        //运行状态校验
        if (StringUtils.isNotBlank(param.getStatus())) {
            List<String> statusList = StringUtil.splitStr(param.getStatus(), ",");
            for (String status : statusList) {
                AmazonAdTargeting.stateEnum stateEnum = UCommonUtil.getByCode(status, AmazonAdTargeting.stateEnum.class);
                if (stateEnum == null) {
                    return "请求参数错误";
                }
            }
        }
        //服务状态校验
        if (StringUtils.isNotBlank(param.getServingStatus())) {
            List<String> searvingStatusList = StringUtil.splitStr(param.getServingStatus(), ",");
            for (String searvingStatus : searvingStatusList) {
                AmazonAdTargeting.servingStatusEnum servingStatusEnum = UCommonUtil.getByCode(searvingStatus, AmazonAdTargeting.servingStatusEnum.class);
                if (servingStatusEnum == null) {
                    return "请求参数错误";
                }
            }
        }
        if (StringUtils.isNotBlank(param.getOrderField())) {
            if (!Constants.isADperformanceOrderField(param.getOrderField())) {
                return "请求参数错误";
            }
        }
        return null;
    }

    // 校验查询的参数
    public String checkCategoryTargetPageParam(TargetViewParam param, boolean isAggregate) {
        if (param == null || StringUtils.isBlank(param.getMarketplaceId())
                || CollectionUtils.isEmpty(param.getShopIdList()) || StringUtils.isBlank(param.getSearchValue()) || StringUtils.isBlank(param.getSearchType())
                || ViewBaseParam.SearchTypeEnum.getEnum(param.getSearchType()) == null) {
            return "请求参数错误";
        }
        if (StringUtils.isBlank(param.getType())) {
            return "请求参数错误";
        } else {
            String campaignValue = CampaignTypeEnum.getCampaignValue(param.getType());
            if (StringUtils.isBlank(campaignValue)) {
                return "请求参数错误";
            }
        }
        if (param.getPageNo() == null || param.getPageNo() <= 0) {
            param.setPageNo(1);
        }
        if (param.getPageSize() == null || param.getPageSize() <= 0) {
            param.setPageSize(20);
        }

        if (StringUtils.isBlank(param.getStartDate()) || StringUtils.isBlank(param.getEndDate())) {
            param.setStartDate(LocalDate.now().minusDays(7).format(DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD)));
            param.setEndDate(LocalDate.now().format(DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD)));
        }
        //筛选条件校验
        if (StringUtils.isNotBlank(param.getSelectType())) {
            List<String> selectTypeList = StringUtil.splitStr(param.getSelectType(), ",");
            for (String selectType : selectTypeList) {
                if (!ExpressionEnum.asinSameAs.value().equals(selectType) && !ExpressionEnum.asinExpandedFrom.value().equals(selectType)) {
                    return "请求参数错误";
                }
            }
        }
        //匹配方式/定位类型校验
        if (StringUtils.isNotBlank(param.getTargetType())) {
            List<String> targetTypeList = StringUtil.splitStr(param.getTargetType(), ",");
            for (String targetType : targetTypeList) {
                if (!TargetTypeEnum.asin.name().equals(targetType) && !TargetTypeEnum.category.name().equals(targetType) && !SdTargetTypeEnum.similarProduct.getValue().equalsIgnoreCase(targetType)) {
                    return "请求参数错误";
                }
            }
        }
        //运行状态校验
        if (StringUtils.isNotBlank(param.getStatus())) {
            List<String> statusList = StringUtil.splitStr(param.getStatus(), ",");
            for (String status : statusList) {
                AmazonAdTargeting.stateEnum stateEnum = UCommonUtil.getByCode(status, AmazonAdTargeting.stateEnum.class);
                if (stateEnum == null) {
                    return "请求参数错误";
                }
            }
        }
        if (StringUtils.isNotBlank(param.getOrderField())) {
            if (!Constants.isADperformanceOrderField(param.getOrderField())) {
                return "请求参数错误";
            }
        }

        if (!isAggregate) {
            if (StringUtils.isBlank(param.getTargetText())) {
                return "请求参数错误";
            }
        }
        return null;
    }

    // 校验查询的参数
    public String checkAudienceTargetPageParam(AudienceTargetViewParam param, boolean isAggregate) {
        if (param == null || StringUtils.isBlank(param.getMarketplaceId())
                || CollectionUtils.isEmpty(param.getShopIdList()) || StringUtils.isBlank(param.getSearchValue()) || StringUtils.isBlank(param.getSearchType())
                || ViewBaseParam.SearchTypeEnum.getEnum(param.getSearchType()) == null) {
            return "请求参数错误";
        }
        if (StringUtils.isBlank(param.getType())) {
            return "请求参数错误";
        } else {
            String campaignValue = CampaignTypeEnum.getCampaignValue(param.getType());
            if (StringUtils.isBlank(campaignValue)) {
                return "请求参数错误";
            }
        }
        if (param.getPageNo() == null || param.getPageNo() <= 0) {
            param.setPageNo(1);
        }
        if (param.getPageSize() == null || param.getPageSize() <= 0) {
            param.setPageSize(20);
        }

        if (StringUtils.isBlank(param.getStartDate()) || StringUtils.isBlank(param.getEndDate())) {
            param.setStartDate(LocalDate.now().minusDays(7).format(DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD)));
            param.setEndDate(LocalDate.now().format(DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD)));
        }
        //匹配方式/定位类型校验
        if (StringUtils.isNotBlank(param.getTargetType())) {
            List<String> targetTypeList = StringUtil.splitStr(param.getTargetType(), ",");
            for (String targetType : targetTypeList) {
                SDTargetingTargetTypeEnum sdTargetingTargetTypeEnum = SDTargetingTargetTypeEnum.fromValue(targetType);
                if (sdTargetingTargetTypeEnum == null) {
                    return "请求参数错误";
                }
            }
        }
        //运行状态校验
        if (StringUtils.isNotBlank(param.getStatus())) {
            List<String> statusList = StringUtil.splitStr(param.getStatus(), ",");
            for (String status : statusList) {
                AmazonAdTargeting.stateEnum stateEnum = UCommonUtil.getByCode(status, AmazonAdTargeting.stateEnum.class);
                if (stateEnum == null) {
                    return "请求参数错误";
                }
            }
        }
        if (StringUtils.isNotBlank(param.getOrderField())) {
            if (!Constants.isADperformanceOrderField(param.getOrderField())) {
                return "请求参数错误";
            }
        }

        if (!isAggregate) {
            if (StringUtils.isBlank(param.getTargetType()) || StringUtils.isBlank(param.getTargetText())) {
                return "请求参数错误";
            }
            if (SDTargetingTargetTypeEnum.AUDIENCE.getType().equals(param.getTargetType())) {
                AudienceCategoryTypeEnum audienceCategoryTypeEnum = AudienceCategoryTypeEnum.fromDesc(param.getTargetText());
                if (audienceCategoryTypeEnum == null) {
                    return "请求参数错误";
                }
                //设置为type值，后续方便处理
                param.setTargetText(audienceCategoryTypeEnum.getType());
            }
        }
        return null;
    }

    // 校验查询的参数
    public String checkKeywordsPageParam(KeywordViewParam param, boolean isAggregate) {
        if (param == null || StringUtils.isBlank(param.getMarketplaceId())
                || CollectionUtils.isEmpty(param.getShopIdList()) || StringUtils.isBlank(param.getSearchValue()) || StringUtils.isBlank(param.getSearchType())
                || ViewBaseParam.SearchTypeEnum.getEnum(param.getSearchType()) == null) {
            return "请求参数错误";
        }
        if (StringUtils.isBlank(param.getType())) {
            return "请求参数错误";
        } else {
            String campaignValue = CampaignTypeEnum.getCampaignValue(param.getType());
            if (StringUtils.isBlank(campaignValue)) {
                return "请求参数错误";
            }
        }
        if (!isAggregate) {
            if (param.getPageNo() == null || param.getPageNo() <= 0) {
                param.setPageNo(1);
            }
            if (param.getPageSize() == null || param.getPageSize() <= 0) {
                param.setPageSize(20);
            }
            if (StringUtils.isBlank(param.getKeywordText())) {
                return "请求参数错误";
            }
        }

        if (StringUtils.isBlank(param.getStartDate()) || StringUtils.isBlank(param.getEndDate())) {
            param.setStartDate(LocalDate.now().minusDays(7).format(DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD)));
            param.setEndDate(LocalDate.now().format(DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD)));
        }
        //匹配方式校验
        if (StringUtils.isNotBlank(param.getMatchType())) {
            List<String> matchTypeList = StringUtil.splitStr(param.getMatchType(), ",");
            for (String matchType : matchTypeList) {
                if (StringUtils.isBlank(MatchValueEnum.getMatchValue(matchType))) {
                    return "请求参数错误";
                }
            }
        }
        //运行状态校验
        if (StringUtils.isNotBlank(param.getStatus())) {
            List<String> statusList = StringUtil.splitStr(param.getStatus(), ",");
            for (String status : statusList) {
                AmazonAdKeyword.stateEnum stateEnum = UCommonUtil.getByCode(status, AmazonAdKeyword.stateEnum.class);
                if (stateEnum == null) {
                    return "请求参数错误";
                }
            }
        }
        //服务状态校验
        if (StringUtils.isNotBlank(param.getServingStatus())) {
            List<String> searvingStatusList = StringUtil.splitStr(param.getServingStatus(), ",");
            for (String searvingStatus : searvingStatusList) {
                AmazonAdKeyword.servingStatusEnum servingStatusEnum = UCommonUtil.getByCode(searvingStatus, AmazonAdKeyword.servingStatusEnum.class);
                if (servingStatusEnum == null) {
                    return "请求参数错误";
                }
            }
        }
        if (StringUtils.isNotBlank(param.getOrderField())) {
            if (!Constants.isADperformanceOrderField(param.getOrderField())) {
                return "请求参数错误";
            }
        }
        return null;
    }

    // 校验查询的参数
    public String checkPlacementPageParam(PlacementViewParam param, boolean isAggregate) {
        if (param == null || StringUtils.isBlank(param.getMarketplaceId())
                || CollectionUtils.isEmpty(param.getShopIdList()) || StringUtils.isBlank(param.getSearchValue()) || StringUtils.isBlank(param.getSearchType())
                || ViewBaseParam.SearchTypeEnum.getEnum(param.getSearchType()) == null) {
            return "请求参数错误";
        }
        if (StringUtils.isBlank(param.getType())) {
            return "请求参数错误";
        } else {
            String campaignValue = CampaignTypeEnum.getCampaignValue(param.getType());
            if (StringUtils.isBlank(campaignValue)) {
                return "请求参数错误";
            }
        }
        if (!isAggregate) {
            if (param.getPageNo() == null || param.getPageNo() <= 0) {
                param.setPageNo(1);
            }
            if (param.getPageSize() == null || param.getPageSize() <= 0) {
                param.setPageSize(20);
            }

            if (StringUtils.isBlank(param.getPredicate())) {
                return "请求参数错误";
            } else {
                AmazonAdvertisePredicateEnum predicateEnum = UCommonUtil.getByCode(param.getPredicate(), AmazonAdvertisePredicateEnum.class);
                if (predicateEnum == null) {
                    return "请求参数错误";
                } else {
                    param.setPlacement(predicateEnum.getValue());
                }
            }
        }
        if (StringUtils.isBlank(param.getStartDate()) || StringUtils.isBlank(param.getEndDate())) {
            param.setStartDate(LocalDate.now().minusDays(7).format(DateTimeFormatter.ofPattern(DateUtil.PATTERN)));
            param.setEndDate(LocalDate.now().format(DateTimeFormatter.ofPattern(DateUtil.PATTERN)));
        }
        //策略类型校验
        if (StringUtils.isNotBlank(param.getStrategyType())) {
            List<String> strategyTypeList = StringUtil.splitStr(param.getStrategyType(), ",");
            for (String strategyType : strategyTypeList) {
                if (StringUtils.isBlank(StrategyEnum.getStrategyValue(strategyType))) {
                    return "请求参数错误";
                }
            }
        }
        //运行状态校验
        if (StringUtils.isNotBlank(param.getStatus())) {
            List<String> statusList = StringUtil.splitStr(param.getStatus(), ",");
            for (String status : statusList) {
                AmazonAdCampaignAll.stateEnum stateEnum = UCommonUtil.getByCode(status, AmazonAdCampaignAll.stateEnum.class);
                if (stateEnum == null) {
                    return "请求参数错误";
                }
            }
        }
        //服务状态校验
        if (StringUtils.isNotBlank(param.getServingStatus())) {
            List<String> searvingStatusList = StringUtil.splitStr(param.getServingStatus(), ",");
            for (String searvingStatus : searvingStatusList) {
                AmazonAdCampaignAll.servingStatusEnum servingStatusEnum = UCommonUtil.getByCode(searvingStatus, AmazonAdCampaignAll.servingStatusEnum.class);
                if (servingStatusEnum == null) {
                    return "请求参数错误";
                }
            }
        }
        if (StringUtils.isNotBlank(param.getOrderField()) && !Constants.isADperformanceOrderField(param.getOrderField())) {
            return "请求参数错误";
        }
        return null;
    }

    // 校验查询的参数
    public String checkCampaignViewParam(CampaignViewParam param) {
        if (param == null || StringUtils.isBlank(param.getMarketplaceId())
                || CollectionUtils.isEmpty(param.getShopIdList()) || StringUtils.isBlank(param.getSearchValue()) || StringUtils.isBlank(param.getSearchType())
                || ViewBaseParam.SearchTypeEnum.getEnum(param.getSearchType()) == null) {
            return "请求参数错误";
        }
        if (StringUtils.isBlank(param.getType())) {
            return "请求参数错误";
        } else {
            String campaignValue = CampaignTypeEnum.getCampaignValue(param.getType());
            if (StringUtils.isBlank(campaignValue)) {
                return "请求参数错误";
            }
        }
        if (param.getPageNo() == null || param.getPageNo() <= 0) {
            param.setPageNo(1);
        }
        if (param.getPageSize() == null || param.getPageSize() <= 0) {
            param.setPageSize(20);
        }
        if (StringUtils.isBlank(param.getStartDate()) || StringUtils.isBlank(param.getEndDate())) {
            param.setStartDate(LocalDate.now().minusDays(7).format(DateTimeFormatter.ofPattern(DateUtil.PATTERN)));
            param.setEndDate(LocalDate.now().format(DateTimeFormatter.ofPattern(DateUtil.PATTERN)));
        }
        //策略类型校验
        if (StringUtils.isNotBlank(param.getStrategyType())) {
            List<String> strategyTypeList = StringUtil.splitStr(param.getStrategyType(), ",");
            for (String strategyType : strategyTypeList) {
                if (StringUtils.isBlank(StrategyEnum.getStrategyValue(strategyType))) {
                    return "请求参数错误";
                }
            }
        }
        //预算状态校验
        if (StringUtils.isNotBlank(param.getBudgetState())) {
            BudgetStateEnum budgetStateEnum = UCommonUtil.getByCode(param.getBudgetState(), BudgetStateEnum.class);
            if (budgetStateEnum == null) {
                return "请求参数错误";
            }
        }
        //运行状态校验
        if (StringUtils.isNotBlank(param.getStatus())) {
            List<String> statusList = StringUtil.splitStr(param.getStatus(), ",");
            for (String status : statusList) {
                AmazonAdCampaignAll.stateEnum stateEnum = UCommonUtil.getByCode(status, AmazonAdCampaignAll.stateEnum.class);
                if (stateEnum == null) {
                    return "请求参数错误";
                }
            }
        }
        //服务状态校验
        if (StringUtils.isNotBlank(param.getServingStatus())) {
            List<String> searvingStatusList = StringUtil.splitStr(param.getServingStatus(), ",");
            for (String searvingStatus : searvingStatusList) {
                AmazonAdCampaignAll.servingStatusEnum servingStatusEnum = UCommonUtil.getByCode(searvingStatus, AmazonAdCampaignAll.servingStatusEnum.class);
                if (servingStatusEnum == null) {
                    return "请求参数错误";
                }
            }
        }
        if (StringUtils.isNotBlank(param.getOrderField())) {
            if (!Constants.isADOrderField(param.getOrderField(), CampaignPageVo.class)) {
                return "请求参数错误";
            }
        }
        return null;
    }

    /**
     * 查询自动投放视图（列表页、汇总）
     */
    @Override
    public void getSearchTermsView(SearchTermsViewRequest request, StreamObserver<SearchTermsViewResponse> responseObserver) {
        log.info("查询搜索词放视图列表页 {}", request);
        long t = Instant.now().toEpochMilli();
        SearchTermsViewResponse.Builder builder = SearchTermsViewResponse.newBuilder();

        if (StringUtils.isBlank(request.getSearchValue()) || StringUtils.isBlank(request.getSearchType())
                || ViewBaseParam.SearchTypeEnum.getEnum(request.getSearchType()) == null) {
            builder.setCode(Result.SUCCESS);
            SearchTermsViewResponse.SearchTermsVo.Builder searchTermsVo = SearchTermsViewResponse.SearchTermsVo.newBuilder();
            //总汇总初始化
            searchTermsVo.setAggregateVo(viewManageService.buildAggregateViewVo(null));
            builder.setData(searchTermsVo);
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
            return;
        }

        SearchTermsViewParam param = new SearchTermsViewParam();
        param.setPageSign(request.getPageSign());
        param.setPageNo(request.getPageNo());
        param.setPageSize(request.getPageSize());
        param.setMarketplaceId(request.getMarketplaceId());
        param.setShopIdList(request.getShopIdListList());
        param.setType(request.getType());
        param.setStartDate(request.getStartDate());
        param.setEndDate(request.getEndDate());
        param.setOrderField(request.getOrderField());
        param.setOrderType(request.getOrderType());

        param.setCampaignId(request.getCampaignId());
        param.setGroupId(request.getGroupId());
        param.setMatchType(request.getMatchType());
        param.setStatus(request.getStatus());
        param.setServingStatus(request.getServingStatus());
        param.setQueryField(request.getQueryField());
        param.setQueryValue(request.getQueryValue());
        param.setQueryType(request.getQueryType());
        param.setSearchType(request.getSearchType());
        param.setSearchValue(request.getSearchValue());

        param.setPuid(request.getPuid());
        //环比数据传参
        param.setIsCompare(request.getIsCompare());
        param.setCompareStartDate(request.getCompareStartDate());
        param.setCompareEndDate(request.getCompareEndDate());

        if (request.hasUseAdvanced()) {  //是否开启高级搜索
            param.setUseAdvanced(request.getUseAdvanced());
        }

        if (request.hasUseAdvanced() && request.getUseAdvanced()) {  //高级筛选
            AdAdvancedFilterData advancedFilter = request.getAdvancedFilter();
            param.setImpressionsMin(advancedFilter.hasImpressionsMin() ? advancedFilter.getImpressionsMin() : null);
            param.setImpressionsMax(advancedFilter.hasImpressionsMax() ? advancedFilter.getImpressionsMax() : null);
            param.setClicksMin(advancedFilter.hasClicksMin() ? advancedFilter.getClicksMin() : null);
            param.setClicksMax(advancedFilter.hasClicksMax() ? advancedFilter.getClicksMax() : null);
            param.setClickRateMin(advancedFilter.hasClickRateMin() ? BigDecimal.valueOf(advancedFilter.getClickRateMin()) : null);
            param.setClickRateMax(advancedFilter.hasClickRateMax() ? BigDecimal.valueOf(advancedFilter.getClickRateMax()) : null);
            param.setCostMin(advancedFilter.hasCostMin() ? BigDecimal.valueOf(advancedFilter.getCostMin()) : null);
            param.setCostMax(advancedFilter.hasCostMax() ? BigDecimal.valueOf(advancedFilter.getCostMax()) : null);
            param.setCpcMin(advancedFilter.hasCpcMin() ? BigDecimal.valueOf(advancedFilter.getCpcMin()) : null);
            param.setCpcMax(advancedFilter.hasCpcMax() ? BigDecimal.valueOf(advancedFilter.getCpcMax()) : null);
            param.setOrderNumMin(advancedFilter.hasOrderNumMin() ? advancedFilter.getOrderNumMin() : null);
            param.setOrderNumMax(advancedFilter.hasOrderNumMax() ? advancedFilter.getOrderNumMax() : null);
            param.setSalesMin(advancedFilter.hasSalesMin() ? BigDecimal.valueOf(advancedFilter.getSalesMin()) : null);
            param.setSalesMax(advancedFilter.hasSalesMax() ? BigDecimal.valueOf(advancedFilter.getSalesMax()) : null);
            param.setAcosMin(advancedFilter.hasAcosMin() ? BigDecimal.valueOf(advancedFilter.getAcosMin()) : null);
            param.setAcosMax(advancedFilter.hasAcosMax() ? BigDecimal.valueOf(advancedFilter.getAcosMax()) : null);
            param.setRoasMin(advancedFilter.hasRoasMin() ? BigDecimal.valueOf(advancedFilter.getRoasMin()) : null);
            param.setRoasMax(advancedFilter.hasRoasMax() ? BigDecimal.valueOf(advancedFilter.getRoasMax()) : null);
            param.setSalesConversionRateMin(advancedFilter.hasSalesConversionRateMin() ? BigDecimal.valueOf(advancedFilter.getSalesConversionRateMin()) : null);
            param.setSalesConversionRateMax(advancedFilter.hasSalesConversionRateMax() ? BigDecimal.valueOf(advancedFilter.getSalesConversionRateMax()): null);
            param.setAcotsMin(advancedFilter.hasAcotsMin() ? BigDecimal.valueOf(advancedFilter.getAcotsMin()) :null);
            param.setAcotsMax(advancedFilter.hasAcotsMax() ? BigDecimal.valueOf(advancedFilter.getAcotsMax()) : null);
            param.setAsotsMin(advancedFilter.hasAsotsMin() ? BigDecimal.valueOf(advancedFilter.getAsotsMin()) :null);
            param.setAsotsMax(advancedFilter.hasAsotsMax() ? BigDecimal.valueOf(advancedFilter.getAsotsMax()) : null);
            param.setCpaMin(advancedFilter.hasCpaMin() ? BigDecimal.valueOf(advancedFilter.getCpaMin()) : null);
            param.setCpaMax(advancedFilter.hasCpaMax() ? BigDecimal.valueOf(advancedFilter.getCpaMax()) : null);
            param.setAdSaleNumMin(advancedFilter.hasAdSaleNumMin() ? advancedFilter.getAdSaleNumMin() : null);
            param.setAdSaleNumMax(advancedFilter.hasAdSaleNumMax() ? advancedFilter.getAdSaleNumMax() : null);
            param.setAdOtherOrderNumMin(advancedFilter.hasAdOtherOrderNumMin() ? advancedFilter.getAdOtherOrderNumMin() : null);
            param.setAdOtherOrderNumMax(advancedFilter.hasAdOtherOrderNumMax() ? advancedFilter.getAdOtherOrderNumMax() : null);
            param.setAdSalesMin(advancedFilter.hasAdSalesMin() ? BigDecimal.valueOf(advancedFilter.getAdSalesMin()) : null);
            param.setAdSalesMax(advancedFilter.hasAdSalesMax() ? BigDecimal.valueOf(advancedFilter.getAdSalesMax()) : null);
            param.setAdOtherSalesMin(advancedFilter.hasAdOtherSalesMin() ? BigDecimal.valueOf(advancedFilter.getAdOtherSalesMin()) : null);
            param.setAdOtherSalesMax(advancedFilter.hasAdOtherSalesMax() ? BigDecimal.valueOf(advancedFilter.getAdOtherSalesMax()) : null);
            param.setAdSelfSaleNumMin(advancedFilter.hasAdSelfSaleNumMin() ? advancedFilter.getAdSelfSaleNumMin() : null);
            param.setAdSelfSaleNumMax(advancedFilter.hasAdSelfSaleNumMax() ? advancedFilter.getAdSelfSaleNumMax() : null);
            param.setAdOtherSaleNumMin(advancedFilter.hasAdOtherSaleNumMin() ? advancedFilter.getAdOtherSaleNumMin() : null);
            param.setAdOtherSaleNumMax(advancedFilter.hasAdOtherSaleNumMax() ? advancedFilter.getAdOtherSaleNumMax() : null);
            param.setAdSalesTotalMin(advancedFilter.hasAdSalesTotalMin() ? advancedFilter.getAdSalesTotalMin() : null);
            param.setAdSalesTotalMax(advancedFilter.hasAdSalesTotalMax() ? advancedFilter.getAdSalesTotalMax() : null);
            param.setVcpmMin(advancedFilter.hasVcpmMin() ? BigDecimal.valueOf(advancedFilter.getVcpmMin()) : null);
            param.setVcpmMax(advancedFilter.hasVcpmMax() ? BigDecimal.valueOf(advancedFilter.getVcpmMax()) : null);
            param.setAdvertisingUnitPriceMin(advancedFilter.hasAdvertisingUnitPriceMin() ? new BigDecimal(String.valueOf(advancedFilter.getAdvertisingUnitPriceMin())) : null);
            param.setAdvertisingUnitPriceMax(advancedFilter.hasAdvertisingUnitPriceMax() ? new BigDecimal(String.valueOf(advancedFilter.getAdvertisingUnitPriceMax())) : null);
        }

        //做参数校验
        String err = checkSearchTermsPageParam(param);
        if (StringUtils.isNotBlank(err)) {
            log.error("查询搜索词视图参数校验异常 {}", request);
            builder.setCode(Result.ERROR);
            builder.setMsg(err);
        } else {
            SearchTermsViewResponse.SearchTermsVo searchTermsVo;
            //新版支持类型多选，后续删除旧分支即可
//            if (Constants.SP.equalsIgnoreCase(param.getType())) {
//                searchTermsVo = viewManageService.getSearchTermsView(param.getPuid(), param);
//            } else {
                searchTermsVo = viewManageService.getAllSearchTermsView(param.getPuid(), param);
//            }
            builder.setData(searchTermsVo);
            builder.setCode(Result.SUCCESS);
        }
        log.info("产品透视分析--搜索词放视图汇总接口调用花费时间 {} ,puid: {},marketplaceId: {},shopIds: {},searchType: {},searchValue: {},startDate：{},endDate：{}", (Instant.now().toEpochMilli() - t),
                param.getPuid(), param.getMarketplaceId(), StringUtil.joinInt(param.getShopIdList()), param.getSearchType(), param.getSearchValue(), param.getStartDate(), param.getEndDate());
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }


    // 校验查询的参数
    public String checkSearchTermsPageParam(SearchTermsViewParam param) {
        if (param == null || StringUtils.isBlank(param.getMarketplaceId())
                || CollectionUtils.isEmpty(param.getShopIdList()) || StringUtils.isBlank(param.getSearchValue()) || StringUtils.isBlank(param.getSearchType())
                || ViewBaseParam.SearchTypeEnum.getEnum(param.getSearchType()) == null) {
            return "请求参数错误";
        }
        if (StringUtils.isBlank(param.getType())) {
            return "请求参数错误";
        } else {
            String campaignValue = CampaignTypeEnum.getCampaignValue(param.getType());
            if (StringUtils.isBlank(campaignValue)) {
                return "请求参数错误";
            }
        }
        if (param.getPageNo() == null || param.getPageNo() <= 0) {
            param.setPageNo(1);
        }
        if (param.getPageSize() == null || param.getPageSize() <= 0) {
            param.setPageSize(20);
        }
        if (StringUtils.isBlank(param.getStartDate()) || StringUtils.isBlank(param.getEndDate())) {
            param.setStartDate(LocalDate.now().minusDays(7).format(DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD)));
            param.setEndDate(LocalDate.now().format(DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD)));
        }

        //运行状态校验
        if (StringUtils.isNotBlank(param.getStatus())) {
            List<String> statusList = StringUtil.splitStr(param.getStatus(), ",");
            for (String status : statusList) {
                AmazonAdCampaignAll.stateEnum stateEnum = UCommonUtil.getByCode(status, AmazonAdCampaignAll.stateEnum.class);
                if (stateEnum == null) {
                    return "请求参数错误";
                }
            }
        }
        //服务状态校验
        if (StringUtils.isNotBlank(param.getServingStatus())) {
            List<String> searvingStatusList = StringUtil.splitStr(param.getServingStatus(), ",");
            for (String searvingStatus : searvingStatusList) {
                AmazonAdCampaignAll.servingStatusEnum servingStatusEnum = UCommonUtil.getByCode(searvingStatus, AmazonAdCampaignAll.servingStatusEnum.class);
                if (servingStatusEnum == null) {
                    return "请求参数错误";
                }
            }
        }
        if (StringUtils.isNotBlank(param.getOrderField())) {
            if (!Constants.isADperformanceOrderField(param.getOrderField())) {
                return "请求参数错误";
            }
        }
        return null;
    }


}
