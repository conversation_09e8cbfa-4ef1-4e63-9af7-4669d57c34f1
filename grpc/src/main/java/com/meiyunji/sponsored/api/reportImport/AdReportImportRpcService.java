package com.meiyunji.sponsored.api.reportImport;

import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.grpc.reportImport.*;
import com.meiyunji.sponsored.service.cpc.vo.reportImport.ReportCosFileParam;
import com.meiyunji.sponsored.service.cpc.vo.reportImport.ReportImportAddParam;
import com.meiyunji.sponsored.service.cpc.vo.reportImport.ReportImportFileParam;
import com.meiyunji.sponsored.service.cpc.vo.reportImport.ReportImportPageListParam;
import com.meiyunji.sponsored.service.reportImport.dto.CpcReportsImportPlatformDto;
import com.meiyunji.sponsored.service.reportImport.service.impl.CpcReportsImportPlatformServiceImpl;
import com.meiyunji.sponsored.service.reportImport.vo.ReportImportDetailVo;
import io.grpc.stub.StreamObserver;
import lombok.extern.slf4j.Slf4j;
import org.lognet.springboot.grpc.GRpcService;

import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@GRpcService
@Slf4j
public class AdReportImportRpcService extends AdReportImportApiServiceGrpc.AdReportImportApiServiceImplBase {

    private final CpcReportsImportPlatformServiceImpl cpcReportsImportPlatformService;

    public AdReportImportRpcService(CpcReportsImportPlatformServiceImpl cpcReportsImportPlatformService) {
        this.cpcReportsImportPlatformService = cpcReportsImportPlatformService;
    }

    @Override
    public void addReportImportTask(AddReportImportTaskRequestPb.AddReportImportTaskRequest request, StreamObserver<AddReportImportTaskResponsePb.AddReportImportTaskResponse> responseObserver) {
        log.info("request: {}", request);
        try {
            ReportImportAddParam reportImportAddParam = new ReportImportAddParam();
            ArrayList<ReportImportFileParam> reportImportFileParams = new ArrayList<>();
            for (AddReportImportTaskRequestPb.ReportImportFile reportImportFile : request.getImportFileList()) {
                ReportImportFileParam reportImportFileParam = new ReportImportFileParam();
                reportImportFileParam.setReportType(reportImportFile.getReportType());

                List<ReportCosFileParam> cosFileParams = new ArrayList<>();
                reportImportFile.getFileList().forEach(item -> {
                    ReportCosFileParam reportCosFileParam = new ReportCosFileParam();
                    reportCosFileParam.setTitle(item.getTitle());
                    reportCosFileParam.setFilePath(item.getFilePath());
                    reportCosFileParam.setFileId(item.getFileId());
                    reportCosFileParam.setOrder(item.getOrder());
                    cosFileParams.add(reportCosFileParam);
                });

                reportImportFileParam.setReportCosFiles(cosFileParams);
                reportImportFileParams.add(reportImportFileParam);
            }
            reportImportAddParam.setShopIds(request.getShopIdList());
            reportImportAddParam.setCreateName(request.getCreateName());
            reportImportAddParam.setUid(request.getCreateUid());
            reportImportAddParam.setReports(reportImportFileParams);
            cpcReportsImportPlatformService.addReportImportTask(request.getPuid(), reportImportAddParam);

            AddReportImportTaskResponsePb.AddReportImportTaskResponse response = AddReportImportTaskResponsePb.AddReportImportTaskResponse.newBuilder().build();
            log.info("response: {}", response);
            responseObserver.onNext(response);
            responseObserver.onCompleted();
        } catch (Exception e) {
            log.error("", e);
            responseObserver.onError(e);
        }
    }

    @Override
    public void getReportImportTasks(GetReportImportTaskRequestPb.GetReportImportTaskRequest request, StreamObserver<GetReportImportTaskResponsePb.GetReportImportTaskResponse> responseObserver) {
        log.info("request: {}", request);
        try {
            ReportImportPageListParam reportImportPageListParam = new ReportImportPageListParam();
            reportImportPageListParam.setReportTypes(request.getReportTypeList());
            reportImportPageListParam.setStartDate(request.getStartDate());
            reportImportPageListParam.setEndDate(request.getEndDate());
            reportImportPageListParam.setMarketplaceIds(request.getMarketplaceIdList());
            reportImportPageListParam.setStatuses(request.getStatusList());
            reportImportPageListParam.setShopIds(request.getShopIdList());
            reportImportPageListParam.setUids(request.getUidList());

            Page<CpcReportsImportPlatformDto> page = new Page<>();
            page.setPageNo(request.getPageNo());
            page.setPageSize(request.getPageSize());
            //执行查询逻辑
            Page<CpcReportsImportPlatformDto> taskPage = cpcReportsImportPlatformService.
                    getPageList(request.getPuid(), page, reportImportPageListParam);

            //构建grpc返回response
            GetReportImportTaskResponsePb.GetReportImportTaskResponse.Builder builder =
                    GetReportImportTaskResponsePb.GetReportImportTaskResponse.newBuilder();
            builder.setCode(Result.SUCCESS);
            GetReportImportTaskResponsePb.GetReportImportTaskResponse.Page.Builder pageBuilder =
                    GetReportImportTaskResponsePb.GetReportImportTaskResponse.Page.newBuilder();
            pageBuilder.setPageNo(request.getPageNo());
            pageBuilder.setPageSize(taskPage.getPageSize());
            pageBuilder.setTotalPage(taskPage.getTotalPage());
            pageBuilder.setTotalSize(taskPage.getTotalSize());

            ArrayList<GetReportImportTaskResponsePb.GetReportImportTaskResponse.ReportImportTask> rows = new ArrayList<>();
            List<CpcReportsImportPlatformDto> tasks = taskPage.getRows() == null ? new ArrayList<>() : taskPage.getRows();
            for (CpcReportsImportPlatformDto row : tasks) {
                GetReportImportTaskResponsePb.GetReportImportTaskResponse.ReportImportTask.Builder taskBuilder =
                        GetReportImportTaskResponsePb.GetReportImportTaskResponse.ReportImportTask.newBuilder();
                taskBuilder.setShopName(Optional.ofNullable(row.getShopName()).orElse(""));
                taskBuilder.setCountryName(Optional.ofNullable(row.getCountry()).orElse(""));
                taskBuilder.setShopId(String.valueOf(row.getShopId()));
                taskBuilder.setReportType(row.getReportType());

                List<GetReportImportTaskResponsePb.GetReportImportTaskResponse.Error> errs = new ArrayList<>();
                List<ReportImportDetailVo> reportImportDetailVos = row.getErrors() == null ? new ArrayList<ReportImportDetailVo>() : row.getErrors();
                for (ReportImportDetailVo detailVo : reportImportDetailVos) {
                    GetReportImportTaskResponsePb.GetReportImportTaskResponse.Error err = GetReportImportTaskResponsePb.GetReportImportTaskResponse.Error.newBuilder()
                            .setStatus(detailVo.getStatus())
                            .setFileName(detailVo.getFileName())
                            .setErrType(detailVo.getErrType())
                            .addAllErrInfos(detailVo.getErrInfos())
                            .setRowNumber(detailVo.getRowNumber())
                            .build();
                    errs.add(err);
                }

                taskBuilder.addAllError(errs);
                taskBuilder.setStatus(row.getStatus());
                taskBuilder.setCreateName(row.getCreateName());
                taskBuilder.setLastUpdateAt(row.getLastUpdateAt().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                rows.add(taskBuilder.build());
            }
            pageBuilder.addAllRows(rows);
            builder.setData(pageBuilder.build());

            log.info("response: {}", builder.build());
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        } catch (Exception e) {
            log.error("", e);
            responseObserver.onError(e);
        }
    }
}
