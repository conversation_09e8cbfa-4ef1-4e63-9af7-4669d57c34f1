package com.meiyunji.sponsored.api.category;


import com.google.protobuf.Int32Value;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.rpc.category.*;
import com.meiyunji.sponsored.service.category.service.IAmazonAdTargetCategoriesService;
import com.meiyunji.sponsored.service.category.vo.TargetCategoryParam;
import com.meiyunji.sponsored.service.category.vo.TargetCategoryVo;
import io.grpc.stub.StreamObserver;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.lognet.springboot.grpc.GRpcService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.Instant;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: wade
 * @date: 2021/10/23 10:34
 * @describe:
 */
@GRpcService
@Slf4j
public class AdCategoryRpcService extends RPCCategoryServiceGrpc.RPCCategoryServiceImplBase {

    @Autowired
    private IAmazonAdTargetCategoriesService amazonAdTargetCategoriesService;


    /**
     * 查询分类信息
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void pageList(CategoryPageListRequest request, StreamObserver<CategoryPageListResponse> responseObserver) {
        log.info("查询所有类型广告活动 {}", request);
        long t = Instant.now().toEpochMilli();
        CategoryPageListResponse.Builder builder = CategoryPageListResponse.newBuilder();
        TargetCategoryParam param = new TargetCategoryParam();
        BeanUtils.copyProperties(request, param);
        if (!request.hasMarketplaceId()) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
        } else {
            Result<Page<TargetCategoryVo>> pageList = amazonAdTargetCategoriesService.getPageList(param);
            if (pageList != null && pageList.getData() != null) {
                Page<TargetCategoryVo> data = pageList.getData();
                CategoryPage.Builder page = CategoryPage.newBuilder();
                page.setPageNo(Int32Value.of(data.getPageNo()));
                page.setPageSize(Int32Value.of(data.getPageSize()));
                page.setTotalPage(Int32Value.of(data.getTotalPage()));
                page.setTotalSize(Int32Value.of(data.getTotalSize()));
                if (CollectionUtils.isNotEmpty(data.getRows())) {
                    List<CategoryVo> collect = data.getRows().stream().map(e -> {
                        CategoryVo.Builder voBuilder = CategoryVo.newBuilder();
                        if (e.getId() != null) {
                            voBuilder.setId(e.getId());
                        }
                        if (e.getPath() != null) {
                            voBuilder.setPath(e.getPath());
                        }
                        if (e.getName() != null) {
                            voBuilder.setName(e.getName());
                        }
                        return voBuilder.build();
                    }).collect(Collectors.toList());
                    page.addAllRows(collect);
                }
                builder.setData(page.build());
            }
            builder.setCode(Int32Value.of(Result.SUCCESS));
        }

        log.info("分类--分类信息列表查询时间 {} ,puid: {},shopId: {}", (Instant.now().toEpochMilli() - t), request.getPuid(), request.getShopId());
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }


}

