package com.meiyunji.sponsored.api.sb;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.protobuf.Int32Value;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.util.CheckParamUtil;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.common.util.JSONUtil;
import com.meiyunji.sponsored.rpc.adCommon.CommonShowAdPerformanceRequest;
import com.meiyunji.sponsored.rpc.adCommon.CommonShowAdPerformanceResponse;
import com.meiyunji.sponsored.rpc.sb.campaign.SbKeywordsVo;
import com.meiyunji.sponsored.rpc.sb.campaign.SbTargetsVo;
import com.meiyunji.sponsored.rpc.sb.campaign.*;
import com.meiyunji.sponsored.rpc.vo.AdPerformanceRpcVo;
import com.meiyunji.sponsored.rpc.vo.CpcCommPageRpcVo;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdProfileDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdCampaignAll;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdProfile;
import com.meiyunji.sponsored.service.cpc.po.AmazonSbAdCampaign;
import com.meiyunji.sponsored.service.cpc.po.AmazonSbAdGroup;
import com.meiyunji.sponsored.service.cpc.service2.sb.ICpcSbCampaignService;
import com.meiyunji.sponsored.service.cpc.vo.*;
import com.meiyunji.sponsored.service.enums.CampaignTypeEnum;
import com.meiyunji.sponsored.service.enums.SBAdFormatEnum;
import com.meiyunji.sponsored.service.enums.SBCreateErrorEnum;
import com.meiyunji.sponsored.service.log.enums.OperationLogResultEnum;
import com.meiyunji.sponsored.service.log.po.AdManageOperationLog;
import com.meiyunji.sponsored.service.log.service.IAdManageOperationLogService;
import com.meiyunji.sponsored.service.util.Constant;
import io.grpc.stub.StreamObserver;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.lognet.springboot.grpc.GRpcService;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;

@GRpcService
@Slf4j
public class SbCampaignRpcService extends RPCCpcSbCampaignServiceGrpc.RPCCpcSbCampaignServiceImplBase {

    @Autowired
    private ICpcSbCampaignService campaignService;
    @Autowired
    private IAdManageOperationLogService adManageOperationLogService;
    @Autowired
    private IAmazonAdProfileDao amazonAdProfileDao;
    @Autowired
    private IScVcShopAuthDao shopAuthDao;


    @Override
    public void showAdPerformanceVo(CommonShowAdPerformanceRequest request, StreamObserver<CommonShowAdPerformanceResponse> responseObserver) {
        CommonShowAdPerformanceResponse.Builder builder = CommonShowAdPerformanceResponse.newBuilder();
        //做参数校验
        if (!request.hasShopId()) {
            builder.setCode(Result.ERROR);
            builder.setMsg("请求参数错误");
        } else {
            AdPerformanceParam param = new AdPerformanceParam();
            param.setShopId(request.getShopId());
            param.setPuid(request.getPuid());
            param.setCampaignId(request.getCampaignId());
            param.setGroupId(request.getGroupId());
            param.setKeywordId(request.getKeywordId());
            param.setTargetId(request.getTargetId());
            param.setCpcProductId(request.getCpcProductId());
            param.setQuery(request.getQuery());
            param.setPlacement(request.getPlacement());
            param.setStartDate(request.getStartDate());
            param.setEndDate(request.getEndDate());

            if (StringUtils.isBlank(param.getStartDate()) || StringUtils.isBlank(param.getEndDate())) {
                param.setStartDate(LocalDate.now().minusDays(7).format(DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD)));
                param.setEndDate(LocalDate.now().format(DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD)));
            } else {
                param.setStartDate(param.getStartDate().replace("-", ""));
                param.setEndDate(param.getEndDate().replace("-", ""));
            }
            //处理业务返回结果
            Result<AdPerformanceVo> res = campaignService.showCampaignPerformance(request.getPuid(), param);
            builder.setCode(res.getCode());
            if (res.getMsg()!=null) {
                builder.setMsg(res.getMsg());
            }
            if (res.success()) {
                //处理data
                AdPerformanceVo data = res.getData();
                if (data!=null) {
                    AdPerformanceRpcVo.Builder voBuilder = AdPerformanceRpcVo.newBuilder();
                    if (data.getShopId()!=null) {
                        voBuilder.setShopId(data.getShopId());
                    }
                    if (data.getCampaignId()!=null) {
                        voBuilder.setCampaignId(data.getCampaignId());
                    }
                    if (data.getGroupId()!=null) {
                        voBuilder.setGroupId(data.getGroupId());
                    }
                    if (data.getKeywordId()!=null) {
                        voBuilder.setKeywordId(data.getKeywordId());
                    }
                    if (data.getTargetId()!=null) {
                        voBuilder.setTargetId(data.getTargetId());
                    }
                    if (data.getAdId()!=null) {
                        voBuilder.setAdId(data.getAdId());
                    }
                    if (data.getQuery()!=null) {
                        voBuilder.setQuery(data.getQuery());
                    }
                    if (data.getPlacement()!=null) {
                        voBuilder.setPlacement(data.getPlacement());
                    }
                    Map<String, CpcCommPageRpcVo> rpcVoMap = Maps.newHashMap();

                    if (MapUtils.isNotEmpty(data.getMap())) {
                        for (Map.Entry<String, CpcCommPageVo> entry : data.getMap().entrySet()) {
                            CpcCommPageVo vo = entry.getValue();
                            //vo转message
                            CpcCommPageRpcVo.Builder cpcRpcVo = CpcCommPageRpcVo.newBuilder();
                            cpcRpcVo.setImpressions(Optional.ofNullable(vo.getImpressions()).orElse(0));
                            cpcRpcVo.setClicks(Optional.ofNullable(vo.getClicks()).orElse(0));
                            cpcRpcVo.setCtr(StringUtils.isNotBlank(vo.getCtr()) ? vo.getCtr(): "0");
                            cpcRpcVo.setCvr(StringUtils.isNotBlank(vo.getCvr()) ? vo.getCvr(): "0");
                            cpcRpcVo.setAcos(StringUtils.isNotBlank(vo.getAcos()) ? vo.getAcos(): "0");
                            cpcRpcVo.setRoas(StringUtils.isNotBlank(vo.getRoas()) ? vo.getRoas(): "0");
                            cpcRpcVo.setAcots(StringUtils.isNotBlank(vo.getAcots()) ? vo.getAcots(): "0");
                            cpcRpcVo.setAdOrderNum(Optional.ofNullable(vo.getAdOrderNum()).orElse(0));
                            cpcRpcVo.setAdCost(StringUtils.isNotBlank(vo.getAdCost()) ? vo.getAdCost(): "0");
                            cpcRpcVo.setAdCostPerClick(StringUtils.isNotBlank(vo.getAdCostPerClick()) ? vo.getAdCostPerClick(): "0");
                            cpcRpcVo.setAdSale(StringUtils.isNotBlank(vo.getAdSale()) ? vo.getAdSale(): "0");

                            rpcVoMap.put(entry.getKey(),cpcRpcVo.build());
                        }

                        voBuilder.putAllMap(rpcVoMap);
                    }
                    builder.setData(voBuilder.build());
                }
            }
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void createCampaign(CreateCampaignRequest request, StreamObserver<CommonResponse> responseObserver) {
        log.info("sb-campaign-创建活动 request {}", request);
        SbCampaignVo vo = new SbCampaignVo();
        vo.setPuid(request.getPuid().getValue());
        vo.setShopId(request.getShopId().getValue());
        vo.setUid(request.getUid().getValue());
        vo.setName(request.getName());
        vo.setState(request.getState());
        vo.setBudgetType(request.getBudgetType());
        vo.setBudget(request.getBudget());
        vo.setBrandEntityId(request.getBrandEntityId());
        if (request.hasPortfolioId() && StringUtils.isNotBlank(request.getPortfolioId())) {
            if (Constant.NON_PORTFOLIO_ID.equals(request.getPortfolioId())) {
                vo.setPortfolioId("");
            } else {
                vo.setPortfolioId(request.getPortfolioId());
            }
        }
        vo.setStartDateStr(request.getStartDateStr());
        vo.setEndDateStr(request.getEndDateStr());
        if (request.hasBidOptimization()) {
            vo.setBidOptimization(request.getBidOptimization());
        }
        if (request.hasSegmentPercentage()) {
            vo.setSegmentPercentage(Double.valueOf(request.getSegmentPercentage()));
        }
        if (request.hasShopperSegment()) {
            vo.setShopperSegment(request.getShopperSegment());
        }
        if (request.hasPlacementPercentage()) {
            vo.setPlacementPercentage(Double.valueOf(request.getPlacementPercentage()));
        }
        if (request.hasProductLocation()) {
            vo.setProductLocation(request.getProductLocation());
        }
        if (MapUtils.isNotEmpty(request.getTagsMap())) {
            vo.setTags(request.getTags());
        }

        CommonResponse.Builder builder = CommonResponse.newBuilder();
        //校验参数
        String errMsg = checkCreateCampaignParams(vo);
        if (StringUtils.isNotBlank(errMsg)) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg(errMsg);
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        } else {
            Result<String> result = null;
            try {
                result = campaignService.createCampaign(vo);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }

            AmazonAdCampaignAll amazonAdCampaign = buildCreateInfo(vo, result.success() ? result.getData() : null);
            AdManageOperationLog operationLog = adManageOperationLogService.getSbCampaignOperationLog(null, amazonAdCampaign);
            operationLog.setIp(request.getLoginIp());
            if (result.success()) {
                operationLog.setResult(result.getCode());
                operationLog.setCampaignId(Objects.isNull(result.getData()) ? null : result.getData());
            } else {
                operationLog.setResult(OperationLogResultEnum.FAIL.getResultValue());
                operationLog.setResultInfo(result.getMsg());
            }
            List<AdManageOperationLog> operationLogs = new ArrayList<>();
            operationLogs.add(operationLog);
            adManageOperationLogService.printAdOperationLog(operationLogs);

            builder.setCode(Int32Value.of(result.getCode()));
            builder.setData(result.getData() != null ? result.getData() : "");
            builder.setMsg(result.getMsg() != null ? result.getMsg() : "");
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        }
    }

    private AmazonAdCampaignAll buildCreateInfo(SbCampaignVo vo, String campaignId) {
        AmazonAdCampaignAll amazonAdCampaign = new AmazonAdCampaignAll();
        AmazonAdProfile amazonAdProfile = amazonAdProfileDao.getProfile(vo.getPuid(), vo.getShopId());
        amazonAdCampaign.setPuid(vo.getPuid());
        if (Objects.nonNull(amazonAdProfile)) {
            amazonAdCampaign.setShopId(amazonAdProfile.getShopId());
            amazonAdCampaign.setMarketplaceId(amazonAdProfile.getMarketplaceId());
        }
        amazonAdCampaign.setCampaignId(campaignId);
        amazonAdCampaign.setName(vo.getName());
        amazonAdCampaign.setCreateId(vo.getUid());
        return amazonAdCampaign;
    }

    @Override
    public void newCreateCampaign(NewCreateCampaignRequest request, StreamObserver<NewCreateResponse> responseObserver) {
        //先校验入参，判断对应属性是否合规
        NewCreateResponse.Builder response = NewCreateResponse.newBuilder();
        //校验参数
        String errMsg = checkNewCreateCampaignParams(request);
        if (StringUtils.isNotBlank(errMsg)) {
            response.setCode(Result.SUCCESS);
            response.setMsg(errMsg);
            responseObserver.onNext(response.build());
            responseObserver.onCompleted();
        } else {
            try {
                NewCreateInfoResponse responseInfo = campaignService.submitTogetherCreateCampaign(request);
                response.setData(responseInfo);
                response.setCode(Result.SUCCESS);
            } catch (InterruptedException e) {
                log.error(e.getMessage());
                throw new RuntimeException(e);
            }
            responseObserver.onNext(response.build());
            responseObserver.onCompleted();
        }
    }

    @Override
    public void addAdsCreative(AddAdsCreativeRequest request, StreamObserver<AddAdsCreativeResponse> responseObserver) {
        log.info("addAdsCreative, req:{}", request);
        AddAdsCreativeResponse.Builder response = AddAdsCreativeResponse.newBuilder();
        //1,校验参数
        String errMsg = checkAddAdsCreativeParams(request);
        if (StringUtils.isNotBlank(errMsg)) {
            response.setCode(Result.ERROR);
            response.setMsg(errMsg);
            responseObserver.onNext(response.build());
            responseObserver.onCompleted();
            return;
        }
        //2,权限校验
        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(request.getShopId(), request.getPuid());
        if (shop == null) {
            response.setCode(Result.ERROR);
            response.setMsg(SBCreateErrorEnum.NOT_BEEN_AUTHORIZED.getMsg());
            responseObserver.onNext(response.build());
            responseObserver.onCompleted();
            return ;
        }
        AmazonAdProfile profile = amazonAdProfileDao.getProfile(request.getPuid(), request.getShopId());
        if (profile == null) {
            response.setCode(Result.ERROR);
            response.setMsg(SBCreateErrorEnum.NOT_HAVE_PROFILE.getMsg());
            responseObserver.onNext(response.build());
            responseObserver.onCompleted();
            return ;
        }
        //3，调用
        try {
            AdsResponse responseInfo = campaignService.submitAddAdsCreative(request, shop, profile);
            response.setData(responseInfo);
            response.setCode(Result.SUCCESS);
        } catch (InterruptedException e) {
            log.error(e.getMessage());
            throw new RuntimeException(e);
        }
        responseObserver.onNext(response.build());
        responseObserver.onCompleted();
    }
    private String checkAddAdsCreativeParams(AddAdsCreativeRequest req) {
        if (Objects.isNull(req)) {
            log.error("checkAddAdsCreativeParams req can not be null");
            return "请求参数不能为空";
        }
        if (StringUtils.isBlank(req.getAdGroupId())) {
            log.error("checkAddAdsCreativeParams adGroupId can not be null");
            return "adGroupId 不能为空";
        }
        if (StringUtils.isBlank(req.getAdsState())) {
            log.error("checkAddAdsCreativeParams adsState can not be null");
            return "adsState 不能为空";
        }
        if (StringUtils.isBlank(req.getAdsName())) {
            log.error("checkAddAdsCreativeParams adsName can not be null");
            return "adsName 不能为空";
        }
        if (StringUtils.isBlank(req.getLandingPageVo())) {
            log.error("checkAddAdsCreativeParams landingPageVo can not be null");
            return "landingPageVo 不能为空";
        }
        if (StringUtils.isBlank(req.getCreativeVo())) {
            log.error("checkAddAdsCreativeParams creativeVo can not be null");
            return "creativeVo 不能为空";
        }

        return null;
    }


    @Override
    public void addTarget(AddTargetRequest request, StreamObserver<AddTargetResponse> responseObserver) {
        log.info("addTarget, req:{}", request);
        AddTargetResponse.Builder response = AddTargetResponse.newBuilder();
        //1，校验参数
        String errMsg = checkAddTargetParams(request);
        if (StringUtils.isNotBlank(errMsg)) {
            response.setCode(Result.SUCCESS);
            response.setMsg(errMsg);
            responseObserver.onNext(response.build());
            responseObserver.onCompleted();
            return;
        }

        //2,权限校验
        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(request.getShopId(), request.getPuid());
        if (shop == null) {
            response.setCode(Result.ERROR);
            response.setMsg(SBCreateErrorEnum.NOT_BEEN_AUTHORIZED.getMsg());
            responseObserver.onNext(response.build());
            responseObserver.onCompleted();
            return ;
        }
        AmazonAdProfile profile = amazonAdProfileDao.getProfile(request.getPuid(), request.getShopId());
        if (profile == null) {
            response.setCode(Result.ERROR);
            response.setMsg(SBCreateErrorEnum.NOT_HAVE_PROFILE.getMsg());
            responseObserver.onNext(response.build());
            responseObserver.onCompleted();
            return ;
        }

        try {
            TargetResponse responseInfo = campaignService.submitAddTarget(request, shop, profile);
            response.setData(responseInfo);
            response.setCode(Result.SUCCESS);
        } catch (InterruptedException e) {
            log.error(e.getMessage());
            throw new RuntimeException(e);
        }
        responseObserver.onNext(response.build());
        responseObserver.onCompleted();
    }
    private String checkAddTargetParams(AddTargetRequest req) {
        if (Objects.isNull(req)) {
            log.error("checkAddTargetParams req can not be null");
            return "请求参数不能为空";
        }
        if (StringUtils.isBlank(req.getCampaignId())) {
            log.error("checkAddTargetParams campaignId can not be null");
            return "adGroupId 不能为空";
        }
        if (StringUtils.isBlank(req.getAdGroupId())) {
            log.error("checkAddTargetParams adGroupId can not be null");
            return "adGroupId 不能为空";
        }
        if (CollectionUtils.isEmpty(req.getKeywordsVoList()) && CollectionUtils.isEmpty(req.getThemesVoList()) && CollectionUtils.isEmpty(req.getTargetsVoList())) {
            log.error("checkAddTargetParams 投放列表 can not be null");
            return "投放列表 不能为空";
        }

        return null;
    }

    @Override
    public void verifyName(VerifyNameRequest request, StreamObserver<CommonResponse> responseObserver) {
        CommonResponse.Builder builder = CommonResponse.newBuilder();
        if (!request.hasShopId() || StringUtils.isBlank(request.getName())) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("参数有误");
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        } else {
            Result result = campaignService.verifyName(request.getPuid().getValue(), request.getShopId().getValue(), request.getName());
            builder.setCode(Int32Value.of(result.getCode()));
            builder.setMsg(result.getMsg() != null ? result.getMsg() : "");
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        }
    }

    @Override
    public void updateCampaign(UpdateCampaignRequest request, StreamObserver<CommonResponse> responseObserver) {
        CommonResponse.Builder builder = CommonResponse.newBuilder();
        if (StringUtils.isBlank(request.getCampaignId()) ||
                (StringUtils.isNotBlank(request.getName()) && request.getName().length() > AmazonSbAdCampaign.nameLimit)) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("参数有误");
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        } else {
            SbUpdateCampaignVo vo = new SbUpdateCampaignVo();
            vo.setPuid(request.getPuid().getValue());
            vo.setUid(request.getUid().getValue());
            vo.setShopId(request.getShopId().getValue());
            vo.setCampaignId(request.getCampaignId());
            vo.setIp(request.getIp());
            if (StringUtils.isNotBlank(request.getName())) {
                vo.setName(request.getName());
            }
            if (StringUtils.isNotBlank(request.getState())) {
                vo.setState(request.getState());
            }if (StringUtils.isNotBlank(request.getBudget())) {
                vo.setBudget(request.getBudget());
            }
            if (request.hasStartDateStr()) {  //结束时间可以删除
                vo.setStartDateStr(request.getStartDateStr());
            }
            if (request.hasEndDateStr()) {  //结束时间可以删除
                vo.setEndDateStr(request.getEndDateStr());
            }
            if (request.hasPortfolioId() && StringUtils.isNotBlank(request.getPortfolioId())) {
                if (Constant.NON_PORTFOLIO_ID.equals(request.getPortfolioId())) {
                    vo.setPortfolioId("");
                } else {
                    vo.setPortfolioId(request.getPortfolioId());
                }
            }
            if (request.hasBidOptimization()) {
                vo.setBidOptimization(request.getBidOptimization());
            }
            if (request.hasBidAdjustmentPredicate()) {
                vo.setBidAdjustmentPredicate(request.getBidAdjustmentPredicate());
            }
            if (request.hasBidAdjustmentPercent()) {
                vo.setBidAdjustmentPercent(Double.valueOf(request.getBidAdjustmentPercent()));
            }
            if (MapUtils.isNotEmpty(request.getTags())) {
                vo.setTags(request.getTags());
            }

            Result result = null;
            try {
                result = campaignService.updateCampaign(vo);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
            builder.setCode(Int32Value.of(result.getCode()));
            builder.setMsg(result.getMsg() != null ? result.getMsg() : "");
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        }
    }

    @Override
    public void archive(ArchiveRequest request, StreamObserver<CommonResponse> responseObserver) {
        CommonResponse.Builder builder = CommonResponse.newBuilder();
        if (!request.hasShopId() || StringUtils.isBlank(request.getCampaignId())) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("参数有误");
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        } else {
            Result result = null;
            try {
                result = campaignService.archive(request.getPuid().getValue(), request.getShopId().getValue(),
                        request.getUid().getValue(), request.getCampaignId(), request.getIp());
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
            builder.setCode(Int32Value.of(result.getCode()));
            builder.setMsg(result.getMsg() != null ? result.getMsg() : "");
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        }
    }



    private String checkCreateCampaignParams(SbCampaignVo vo) {
        if (vo == null || vo.getShopId() == null) {
            return "参数有误";
        }

        if (StringUtils.isBlank(vo.getName()) || StringUtils.isBlank(vo.getState())) {
            return "参数有误";
        }

        if (vo.getName().length() > AmazonSbAdCampaign.nameLimit) {
            return "参数有误";
        }
        return null;
    }

    private String checkNewCreateCampaignParams(NewCreateCampaignRequest req) {
        if (Objects.isNull(req)) {
            log.error("shop id can not be null");
            return "请求参数不能为空";
        }

        //判断广告活动参数
        if (StringUtils.isEmpty(req.getCampaignId()) && (StringUtils.isBlank(req.getCampaignName()) || StringUtils.isBlank(req.getCampaignState()))) {
            log.error("campaign name and state can not be null");
            return "广告活动名称或广告状态不能为空";
        }

        if (StringUtils.isNotEmpty(req.getCampaignName()) && req.getCampaignName().length() > AmazonSbAdCampaign.nameLimit) {
            log.error("campaign name length over limit");
            return "广告活动名称长度超过限制";
        }

        if (StringUtils.isEmpty(req.getCampaignId()) && StringUtils.isEmpty(req.getBudget()) || req.getBudget().equals("0")) {
            log.error("campaign budget can not be zero");
            return "广告活动预算不能为空";
        }
        String result = "";
        //判断广告组参数
        if (StringUtils.isNotEmpty(checkNewCreateGroupParams(req))) {
            return checkNewCreateGroupParams(req);
        }
        if (StringUtils.isNotEmpty(checkNewCreateAdsParams(req))) {
            return checkNewCreateAdsParams(req);
        }
        if (StringUtils.isNotEmpty(checkNewCreateTargetParams(req))) {
            return checkNewCreateTargetParams(req);
        }
        return null;
    }

    private String checkNewCreateGroupParams(NewCreateCampaignRequest req) {
        if (StringUtils.isNotBlank(req.getGroupName()) && req.getGroupName().length() > AmazonSbAdGroup.nameLimit) {
            log.error("ad group name length over limit");
            return "广告组名称长度超过限制";
        }
        if (!CheckParamUtil.checkRequired(req, false, "groupState")) {
            log.error("ad group param is null:{}", req);
            return "广告组启停状态不能为空";
        }
        return null;
    }

    private String checkNewCreateAdsParams(NewCreateCampaignRequest req) {
        if (Objects.isNull(SBAdFormatEnum.getSBAdFormatEnumByCode(req.getAdFormat()))) {
            log.error("ads format not exist ad format:{}", req.getAdFormat());
            return "广告格式不存在";
        }
        if (!CheckParamUtil.checkRequired(req, false, "adsName, adsState, landingPageVo, creativeVo")) {
            log.error("ads format is null:{}", req);
            return "广告格式参数不能为空";
        }
        return null;
    }

    private String checkNewCreateTargetParams(NewCreateCampaignRequest req) {
        if (StringUtils.isEmpty(req.getTargetId()) && CollectionUtils.isEmpty(req.getKeywordsVoList())
                && CollectionUtils.isEmpty(req.getTargetsVoList()) && CollectionUtils.isEmpty(req.getThemesVoList())) {
            log.error("target param can not be null:{}", req);
            return "投放参数不能为空";
        }
        if (CollectionUtils.isNotEmpty(req.getKeywordsVoList())){
            for (SbKeywordsVo keywordVo : req.getKeywordsVoList()) {
                if (StringUtils.isEmpty(keywordVo.getMatchType()) || keywordVo.getBid() == 0.0D) {
                    log.error("keyword matchType or bid is null, keyword:{}, bid:{}",
                            keywordVo.getKeywordText(), keywordVo.getBid());
                    return "关键词投放匹配类型或竞价不能为空";
                }
            }
        }

        if (CollectionUtils.isNotEmpty(req.getTargetsVoList())) {
            for (SbTargetsVo targetsVo : req.getTargetsVoList()) {
                if (StringUtils.isNotEmpty(targetsVo.getAsin()) && targetsVo.getBid() == 0.0D) {
                    log.error("targeting bid is null, asin:{}, bid:{}", targetsVo.getAsin(), targetsVo.getBid());
                    return "商品投放的asin或竞价不能为空";
                }
            }
        }
        return null;
    }

    @Override
    public void updateBatchCampaign(UpdateBatchCampaignRequest request, StreamObserver<CommonResponse> responseObserver) {
        CommonResponse.Builder builder = CommonResponse.newBuilder();
        List<UpdateCampaignRequest> vosList = request.getVosList();
        if (!request.hasPuid() || !request.hasShopId() || !request.hasUid() || request.getVosCount() < 1 || !request.hasType()) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("参数有误");
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        } else {
            Integer puid = request.getPuid().getValue();
            Integer shopId = request.getShopId().getValue();
            Integer uid = request.getUid().getValue();
            String ip = "";
            String type = request.getType();
            List<BatchCampaignVo> updateList = Lists.newArrayListWithCapacity(vosList.size());
            for (UpdateCampaignRequest vo : vosList) {
                BatchCampaignVo.BatchCampaignVoBuilder campaignVoBuilder = BatchCampaignVo.builder();
                campaignVoBuilder.puid(puid);
                campaignVoBuilder.shopId(shopId);
                campaignVoBuilder.uid(uid);
                if (StringUtils.isNotBlank(ip)) {
                    campaignVoBuilder.loginIp(ip);
                }
                if (vo.hasBudget()) {
                    campaignVoBuilder.dailyBudget(Double.valueOf(vo.getBudget()));
                }

                campaignVoBuilder.type(CampaignTypeEnum.sb.getCampaignType());

                if (vo.hasState()) {
                    campaignVoBuilder.state(vo.getState());
                }


                if (vo.hasCampaignId()) {
                    campaignVoBuilder.campaignId(vo.getCampaignId());
                }

                BatchCampaignVo campaignvo = campaignVoBuilder.build();
                updateList.add(campaignvo);
            }
            Result result = null;
            try {
                result = campaignService.updateBatchCampaign(updateList,request.getType());
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
            builder.setCode(Int32Value.of(result.getCode()));
            if(result.getData() != null){
                builder.setData(JSONUtil.objectToJson(result.getData()));
            }
            builder.setMsg(result.getMsg() != null ? result.getMsg() : "");
            responseObserver.onNext(builder.build());
            responseObserver.onCompleted();
        }
    }

}
