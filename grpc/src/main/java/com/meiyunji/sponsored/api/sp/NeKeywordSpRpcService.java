package com.meiyunji.sponsored.api.sp;

import com.google.protobuf.Int32Value;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.util.JSONUtil;
import com.meiyunji.sponsored.common.util.StringUtil;
import com.meiyunji.sponsored.rpc.keywords.AsyncAddKeywordsRequest;
import com.meiyunji.sponsored.rpc.sp.neKeyword.*;
import com.meiyunji.sponsored.rpc.vo.AsyncAddKeywordsRpcVo;
import com.meiyunji.sponsored.rpc.vo.CommonResponse;
import com.meiyunji.sponsored.rpc.vo.NeKeywordsRpcVo;
import com.meiyunji.sponsored.service.cpc.constants.AdTargetObjectTypeEnum;
import com.meiyunji.sponsored.service.cpc.constants.AdTargetTaskConstant;
import com.meiyunji.sponsored.service.cpc.constants.AdTargetTaskMatchTypeEnum;
import com.meiyunji.sponsored.service.cpc.constants.AdTargetTaskTypeEnum;
import com.meiyunji.sponsored.service.cpc.dto.AdTargetTaskDto;
import com.meiyunji.sponsored.service.cpc.service2.IAdTargetTaskService;
import com.meiyunji.sponsored.service.cpc.service2.sp.ICpcNeKeywordsService;
import com.meiyunji.sponsored.service.cpc.vo.AddNeKeywordsVo;
import com.meiyunji.sponsored.service.cpc.vo.NeKeywordsVo;
import com.meiyunji.sponsored.service.enums.StateEnum;
import io.grpc.stub.StreamObserver;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.lognet.springboot.grpc.GRpcService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @author: wade
 * @date: 2021/10/27 19:08
 * @describe:
 */
@GRpcService
@Slf4j
public class NeKeywordSpRpcService extends RpcSpNeKeywordsServiceGrpc.RpcSpNeKeywordsServiceImplBase {

    @Autowired
    private ICpcNeKeywordsService cpcNeKeywordsService;
    @Autowired
    private IAdTargetTaskService adTargetTaskService;

    /**
     * 创建否定关键词
     * @param request
     * @param responseObserver
     */
    @Override
    public void createNeKeywords(CreateSpNeKeywordsRequest request, StreamObserver<NeKeywordResponse> responseObserver) {
        log.info("sp-nekeyword-创建否定关键词 request {}", request);
        NeKeywordResponse.Builder builder = NeKeywordResponse.newBuilder();

        //处理list
        List<NeKeywordsRpcVo> neKeywordsList = request.getNeKeywordsList();
        List<NeKeywordsVo> keywordsVoList = neKeywordsList.stream().filter(Objects::nonNull).map(item -> {
            NeKeywordsVo keywordsVo = new NeKeywordsVo();
            keywordsVo.setKeywordText(StringUtil.replaceSpecialSymbol(item.getKeywordText()));
            keywordsVo.setMatchType(item.getMatchType());
            return keywordsVo;
        }).collect(Collectors.toList());

        AddNeKeywordsVo addNeKeywordsVo = new AddNeKeywordsVo();
        addNeKeywordsVo.setPuid(request.getPuid().getValue());
        addNeKeywordsVo.setShopId(request.getShopId().getValue());
        addNeKeywordsVo.setUid(request.getUid().getValue());
        addNeKeywordsVo.setGroupId(request.getGroupId());
        addNeKeywordsVo.setNeKeywords(keywordsVoList);

        if (StringUtils.isBlank(addNeKeywordsVo.getGroupId())
                || !request.hasPuid() || !request.hasShopId()
                || CollectionUtils.isEmpty(addNeKeywordsVo.getNeKeywords())) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
        } else {
            //处理业务返回结果
            Result<List<NeKeywordResponse.Data>> res = cpcNeKeywordsService.addNeKeywords(addNeKeywordsVo, request.getLoginIp());
            builder.setCode(Int32Value.of(res.getCode()));
            if (res.getMsg()!=null) {
                builder.setMsg(res.getMsg());
            }
            if (res.getData() != null && res.getCode() == 0) {
                builder.addAllData(res.getData());
            }
        }

        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void asyncCreateNeKeywords(AsyncAddKeywordsRequest request, StreamObserver<CommonResponse> responseObserver) {
        log.info("sp-nekeyword-异步添加否定关键词 request {}", request);
        CommonResponse.Builder builder = CommonResponse.newBuilder();
        String error = verifyAsyncParam(request);
        if (StringUtils.isNotBlank(error)) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg(error);
        } else {
            AdTargetTaskDto adTargetTaskDto = new AdTargetTaskDto();
            adTargetTaskDto.setPuid(request.getPuid().getValue());
            adTargetTaskDto.setShopId(request.getShopId().getValue());
            adTargetTaskDto.setUid(request.getUid().getValue());
            if (request.getType() != 0 && Objects.nonNull(AdTargetTaskTypeEnum.enumMap.get(request.getType()))) {
                adTargetTaskDto.setType(AdTargetTaskTypeEnum.enumMap.get(request.getType()).getCode());
            } else {
                adTargetTaskDto.setType(AdTargetTaskTypeEnum.SP_NEGATIVE_KEYWORDS.getCode());
            }
            adTargetTaskDto.setLoginIp(request.getLoginIp());
            adTargetTaskDto.setTargetPageType(request.getTargetingPageType().getValue());
            adTargetTaskDto.setSourceAdCampaignId(request.getSourceAdCampaignId());
            adTargetTaskDto.setSourceShopId(request.getSourceShopId());
            List<AdTargetTaskDto.AdTargetTaskDetailDto> detailList = request.getKeywordList().stream().filter(Objects::nonNull).map(item -> {
                AdTargetTaskDto.AdTargetTaskDetailDto detail = new AdTargetTaskDto.AdTargetTaskDetailDto();
                detail.setAdCampaignId(item.getAdCampaignId());
                detail.setAdGroupId(item.getAdGroupId());
                detail.setMatchType(item.getMatchType());
                detail.setTargetObject(item.getKeywordText());
                detail.setTargetObjectDesc(item.getKeywordCn());
                detail.setTargetObjectType(AdTargetObjectTypeEnum.KEYWORD.getCode());
                return detail;
            }).collect(Collectors.toList());
            adTargetTaskDto.setTaskDetails(detailList);
            long taskId = adTargetTaskService.recordTargetTask(adTargetTaskDto);
            adTargetTaskService.executeTask(taskId);
        }

        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    private String verifyAsyncParam(AsyncAddKeywordsRequest request) {
        if (!request.hasShopId() || !request.hasPuid() || !request.hasTargetingPageType()
                || CollectionUtils.isEmpty(request.getKeywordList()) || request.getKeywordList().size() > AdTargetTaskConstant.MAX_TARGET_SIZE) {
            return "请求参数错误";
        }
        List<AsyncAddKeywordsRpcVo> keywordList = request.getKeywordList();
        for (AsyncAddKeywordsRpcVo keyword : keywordList) {
            if (!AdTargetTaskMatchTypeEnum.SP_NE_KEYWORD_SUPPORT_TYPES.contains(keyword.getMatchType())) {
                return "包含未支持的匹配类型";
            }
        }
        return StringUtils.EMPTY;
    }

    /**
     * sp更新否定关键词状态
     * @param request
     * @param responseObserver
     */
    @Override
    public void updateState(UpdateSpNeKeywordStateRequest request, StreamObserver<CommonResponse> responseObserver) {
        log.info("sp-nekeyword-更新否定关键词状态 request {}", request);
        CommonResponse.Builder builder = CommonResponse.newBuilder();

        String stateValue = StateEnum.getStateValue(request.getState());
        if (!request.hasId() ||!request.hasPuid() || StringUtils.isBlank(request.getState()) ||
                StringUtils.isBlank(stateValue)) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
        } else {
            //处理业务
            Result res = cpcNeKeywordsService.updateState(request.getPuid().getValue(), request.getUid().getValue()
                    , request.getId().getValue(), request.getState());
            builder.setCode(Int32Value.of(res.getCode()));
            if (res.getMsg()!=null) {
                builder.setMsg(res.getMsg());
            }
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * sp归档否定关键词
     * @param request
     * @param responseObserver
     */
    @Override
    public void archive(ArchiveNeKeywordRequest request, StreamObserver<CommonResponse> responseObserver) {
        log.info("sp-nekeyword-归档否定关键词 request {}", request);
        CommonResponse.Builder builder = CommonResponse.newBuilder();
        if (!request.hasId() || !request.hasPuid() ||
                !request.hasUid()) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("请求参数错误");
        } else {
            //处理业务返回结果
            Result res = cpcNeKeywordsService.archive(request.getPuid().getValue(), request.getUid().getValue(), request.getId().getValue(), request.getLoginIp());
            builder.setCode(Int32Value.of(res.getCode()));
            if (res.getMsg()!=null) {
                builder.setMsg(res.getMsg());
            }
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * SP否定关键词批量归档（组）
     *
     * @param request
     * @param responseObserver
     */
    @Override
    public void batchArchive(BatchArchiveNeKeywordRequest request, StreamObserver<CommonResponse> responseObserver) {
        log.info("sp-neKeyword-批量归档 request {}", request);
        CommonResponse.Builder builder = CommonResponse.newBuilder();

        if (!request.hasShopId() || !request.hasIdList() || !request.hasPuid() || !request.hasUid() || !request.hasLoginIp()) {
            builder.setCode(Int32Value.of(Result.ERROR));
            builder.setMsg("参数有误");
        } else {
            List<Long> idList = Arrays.stream(request.getIdList().split(",")).map(Long::parseLong).collect(Collectors.toList());
            Result result = cpcNeKeywordsService.batchArchive(request.getPuid().getValue(), request.getShopId().getValue(),
                    request.getUid().getValue(), request.getLoginIp(), idList);
            builder.setCode(Int32Value.newBuilder().setValue(result.getCode()).build());
            if(result.getData() != null){
                builder.setData(JSONUtil.objectToJson(result.getData()));
            }
            if (result.getMsg()!=null) {
                builder.setMsg(result.getMsg());
            }
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }
}
