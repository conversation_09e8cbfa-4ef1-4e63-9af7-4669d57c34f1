package com.meiyunji.sponsored.api.download;

import com.google.protobuf.Int32Value;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.rpc.download.center.*;
import com.meiyunji.sponsored.rpc.vo.CommonResponse;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.account.service.IShopAuthService;
import com.meiyunji.sponsored.service.account.service.IUserAuthService;
import com.meiyunji.sponsored.service.download.enums.AdTypeDownloadEnum;
import com.meiyunji.sponsored.service.download.enums.ReportTypeDownloadEnum;
import com.meiyunji.sponsored.service.download.po.AdDownloadCenter;
import com.meiyunji.sponsored.service.download.service.IAdDownloadCenterService;
import com.meiyunji.sponsored.service.download.vo.AdDownloadCenterQueryRequestVo;
import com.meiyunji.sponsored.service.download.vo.AdDownloadCenterQueryResponseVo;
import io.grpc.stub.StreamObserver;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.lognet.springboot.grpc.GRpcService;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@GRpcService
public class AdDownloadCenterRpcService extends RPCAdDownloadCenterServiceGrpc.RPCAdDownloadCenterServiceImplBase {

    @Autowired
    private IAdDownloadCenterService adDownloadCenterService;
    @Autowired
    private IScVcShopAuthDao shopAuthDao;
    @Autowired
    private IShopAuthService shopAuthService;
    @Autowired
    private IUserAuthService userAuthService;
    private final List<String> spAdDownloadTypes = new ArrayList<String>(){
        {
            add("adCampaignReport");
            add("adGroupReport");
            add("adProductReport");
            add("adTargeringReport");
            add("adSpaceReport");
            add("adSearchTermReport");
            add("adPurchasedItemReport");
            add("adGrossAndInvalidTrafficAllReport");
            add(ReportTypeDownloadEnum.AMAZON_BUSINESS_REPORT.getCode());
        }
    };
    private final List<String> sbAdDownloadTypes = new ArrayList<String>(){
        {
            add("adCampaignReport");
            add("adTargeringReport");
            add("adSpaceReport");
            add("adSearchTermReport");
            add("adGrossAndInvalidTrafficAllReport");
        }
    };
    private final List<String> sbvAdDownloadTypes = new ArrayList<String>(){
        {
            add("adCampaignReport");
            add("adTargeringReport");
            add("adSpaceReport");
            add("adSearchTermReport");
        }
    };
    private final List<String> sdAdDownloadTypes = new ArrayList<String>(){
        {
            add("adCampaignReport");
            add("adGroupReport");
            add("adProductReport");
            add("adPurchasedItemReport");
            add("adCampaignMatchedTargetReport");
            add("sdTargetListReport");
            add("adGrossAndInvalidTrafficAllReport");
        }
    };

    @Override
    public void pageList(DownloadCenterPageRequest request, StreamObserver<DownloadCenterPageResponse> responseObserver) {
        DownloadCenterPageResponse.Builder responseBuilder = DownloadCenterPageResponse.newBuilder();
        if (!request.hasPuid() || !request.hasPageSize() || !request.hasPageNo()) {
            responseBuilder.setCode(Result.ERROR);
            responseBuilder.setMsg("请求参数错误");
        } else {
            AdDownloadCenterQueryRequestVo searchVo = new AdDownloadCenterQueryRequestVo();
            searchVo.setPuid(request.getPuid());
            searchVo.setPageNo(request.getPageNo());
            searchVo.setPageSize(request.getPageSize());
            if (StringUtils.isNotBlank(request.getStartDate())) {
                searchVo.setStartDate(LocalDate.parse(request.getStartDate(), DateTimeFormatter.ofPattern("yyyy-MM-dd")));
            }
            if (StringUtils.isNotBlank(request.getEndDate())) {
                searchVo.setEndDate(LocalDate.parse(request.getEndDate(), DateTimeFormatter.ofPattern("yyyy-MM-dd")));
            }
            if (CollectionUtils.isNotEmpty(request.getAdTypeList())) {
                searchVo.setAdTypeList(request.getAdTypeList());
            }
            if (CollectionUtils.isNotEmpty(request.getReportTypeList())) {
                searchVo.setReportTypeList(request.getReportTypeList());
            }
            if (CollectionUtils.isNotEmpty(request.getTaskIdsList())) {
                searchVo.setTaskIds(request.getTaskIdsList());
            }
            if (StringUtils.isNotBlank(request.getTaskName())) {
                searchVo.setTaskName(request.getTaskName());
            }
            if (CollectionUtils.isNotEmpty(request.getShopIdList())) {
                searchVo.setShopIds(request.getShopIdList());
            }
            Result<Page<AdDownloadCenterQueryResponseVo>> result = adDownloadCenterService.pageList(searchVo);
            if (result.getCode() == Result.SUCCESS) {
                //查询所有门店
                List<ShopAuth> shopAuths = shopAuthService.listByPuid(request.getPuid());
                Map<Integer, ShopAuth> shopMap = null;
                if (CollectionUtils.isNotEmpty(shopAuths)) {
                    shopMap = shopAuths.stream().filter(Objects::nonNull).collect(Collectors.toMap(ShopAuth::getId, Function.identity()));
                }
                Page<AdDownloadCenterQueryResponseVo> page = result.getData();
                List<AdDownloadCenterQueryResponseVo> adDownloadCenterList = page.getRows();
                DownloadCenterData.Builder data = DownloadCenterData.newBuilder();
                DownloadCenterPage.Builder pageBuilder = DownloadCenterPage.newBuilder();
                pageBuilder.setPageNo(page.getPageNo());
                pageBuilder.setPageSize(page.getPageSize());
                pageBuilder.setTotalPage(page.getTotalPage());
                pageBuilder.setTotalSize(page.getTotalSize());
                if (CollectionUtils.isNotEmpty(adDownloadCenterList)) {
                 List<DownloadCenterRpc> list = new ArrayList<>(adDownloadCenterList.size());
                    for (AdDownloadCenterQueryResponseVo vo:adDownloadCenterList) {
                        DownloadCenterRpc.Builder builder = DownloadCenterRpc.newBuilder();
                        builder.setId(vo.getId());
                        builder.setPuid(vo.getPuid());
                        builder.setAdTypeCode(vo.getAdType());
                        builder.setAdTypeName(AdTypeDownloadEnum.getByValue(vo.getAdType()));
                        if (StringUtils.isNotBlank(vo.getReportUrl())) {
                            builder.addAllDownloadUrl(Arrays.asList(vo.getReportUrl().split(",")));
                        }
                        builder.setReportTypeCode(vo.getReportType());
                        builder.setReportTypeName(ReportTypeDownloadEnum.getByValue(vo.getReportType()));
                        builder.setReportCycle(vo.getReportCycle());
                        builder.setTaskName(vo.getTaskName());
                        builder.setTimeUnit(vo.getTimeUnit());
                        builder.setReportState(vo.getReportState());
                        builder.setCreateTime(vo.getCreateTime());
                        //店铺名称转换
                        List<Integer> shopIds = vo.getShopIdList();
                        builder.addAllShopId(shopIds);
                        String shopNames = "";
                        for (int i = 0; i<shopIds.size();i++) {
                            if (MapUtils.isNotEmpty(shopMap) && shopMap.containsKey(shopIds.get(i))) {
                                if (i == 0){
                                    shopNames = shopMap.get(shopIds.get(i)).getName();
                                } else {
                                    shopNames += "、"+shopMap.get(shopIds.get(i)).getName();
                                }
                            }
                        }
                        builder.setShopNames(shopNames);
                        list.add(builder.build());
                    }
                    pageBuilder.addAllRows(list);
                }
                data.setPage(pageBuilder.build());
                responseBuilder.setCode(result.getCode());
                responseBuilder.setData(data.build());
            } else {
                responseBuilder.setMsg(result.getMsg());
                responseBuilder.setCode(result.getCode());
            }
        }
        responseObserver.onNext(responseBuilder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void addDownload(DownloadCenterAddRequest request, StreamObserver<DownloadCenterAddResponse> responseObserver) {
        DownloadCenterAddResponse.Builder responseBuilder = DownloadCenterAddResponse.newBuilder();
        String err = checkAddParam(request);
        if (StringUtils.isNotBlank(err)) {
            responseBuilder.setCode(Result.ERROR);
            responseBuilder.setMsg(err);
        } else {
            AdDownloadCenter requestVo  = new AdDownloadCenter();
            requestVo.setPuid(request.getPuid());
            requestVo.setAdType(request.getAdTypeCode());
            requestVo.setReportType(request.getReportTypeCode());
            if ("".equals(request.getReportCycle())) {
                requestVo.setReportCycle(request.getStartDate()+"~"+request.getEndDate());
            }else {
                requestVo.setReportCycle(request.getReportCycle());
            }
            StringBuilder taskName = new StringBuilder();
            taskName.append(AdTypeDownloadEnum.getByValue(request.getAdTypeCode()));
            taskName.append(ReportTypeDownloadEnum.getByValue(request.getReportTypeCode()));
            requestVo.setTaskName(taskName.toString());
            requestVo.setTimeUnit(request.getTimeUnit());
            requestVo.setReportUrl(request.getDownloadUrl());
            requestVo.setCreateId(request.getUid());
            requestVo.setReportState("生成中");
            requestVo.setCreateDate(LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
            requestVo.setReportStartDate(request.getStartDate());
            requestVo.setReportEndDate(request.getEndDate());
            //查询所有门店
            List<ShopAuth> shopAuths = shopAuthDao.listAllByIds(request.getPuid(),request.getShopIdsList());
            Result<Long> result = adDownloadCenterService.createDownloadTask(request.getPuid(),requestVo,shopAuths);
            if (result.getCode() == Result.SUCCESS) {
                responseBuilder.setCode(result.getCode());
                responseBuilder.setMsg(result.getMsg());
                responseBuilder.setData(result.getData());
            } else {
                responseBuilder.setCode(Result.ERROR);
                responseBuilder.setMsg("创建任务错误");
            }
        }

        responseObserver.onNext(responseBuilder.build());
        responseObserver.onCompleted();
    }


    private String checkAddParam (DownloadCenterAddRequest param) {
        if (!param.hasPuid()) {
            return "puid为空";
        }
        if (!param.hasAdTypeCode()) {
            return "广告类型为空";
        }
        if (!param.hasReportTypeCode()) {
            return "报告类型为空";
        }
        if (CollectionUtils.isEmpty(param.getShopIdsList())) {
            return "店铺为空";
        }
        if ("sp".equals(param.getAdTypeCode())) {
            if (!spAdDownloadTypes.contains(param.getReportTypeCode())) {
                return "sp报告下载类型错误";
            }
        } else if ("sb".equals(param.getAdTypeCode())) {
            if (!sbAdDownloadTypes.contains(param.getReportTypeCode())) {
                return "sb报告下载类型错误";
            }
        } else if ("sbv".equals(param.getAdTypeCode())) {
            if (!sbvAdDownloadTypes.contains(param.getReportTypeCode())) {
                return "sbv报告下载类型错误";
            }
        } else if ("sd".equals(param.getAdTypeCode())) {
            if (!sdAdDownloadTypes.contains(param.getReportTypeCode())) {
                return "sd报告下载类型错误";
            }
        }
        return null;
    }

    @Override
    public void copyDownload(DownloadCenterCopyRequest request, StreamObserver<DownloadCenterCopyResponse> responseObserver) {
        DownloadCenterCopyResponse.Builder responseBuilder = DownloadCenterCopyResponse.newBuilder();
        if (!request.hasPuid() || !request.hasTaskId()) {
            responseBuilder.setCode(Result.ERROR);
            responseBuilder.setMsg("请求参数错误");
        } else {
            Result<AdDownloadCenter> result = adDownloadCenterService.copyDownloadTask(request.getPuid(),request.getTaskId());
            if (result.getCode() == Result.SUCCESS) {
                DownloadCenterRpc.Builder builder = DownloadCenterRpc.newBuilder();
                AdDownloadCenter vo = result.getData();
                responseBuilder.setCode(result.getCode());
                if (vo != null) {
                    //查询所有门店
                    List<ShopAuth> shopAuths = shopAuthService.listByPuid(request.getPuid());
                    Map<Integer, ShopAuth> shopMap = null;
                    if (CollectionUtils.isNotEmpty(shopAuths)) {
                        shopMap = shopAuths.stream().filter(Objects::nonNull).collect(Collectors.toMap(ShopAuth::getId, Function.identity()));
                    }
                    builder.setId(vo.getId());
                    builder.setPuid(vo.getPuid());
                    builder.setAdTypeCode(vo.getAdType());
                    builder.setAdTypeName(AdTypeDownloadEnum.getByValue(vo.getAdType()));
                    builder.setReportTypeCode(vo.getReportType());
                    builder.setReportTypeName(ReportTypeDownloadEnum.getByValue(vo.getReportType()));
                    builder.setTimeUnit(vo.getTimeUnit());
                    builder.setReportState(vo.getReportState());
                    builder.setCreateTime(vo.getCreateTime().toString());
                    //日期转换
                    dateConversion(builder,vo);
                    //店铺名称转换
                    List<Integer> shopIds = vo.getDetailsList().stream().
                            map(e->e.getShopId()).collect(Collectors.toList());
                    builder.addAllShopId(shopIds);
                    String shopNames = "";
                    for (int i = 0; i<shopIds.size();i++) {
                        if (MapUtils.isNotEmpty(shopMap) && shopMap.containsKey(shopIds.get(i))) {
                            if (i == 0){
                                shopNames = shopMap.get(shopIds.get(i)).getName();
                            } else {
                                shopNames += "、"+shopMap.get(shopIds.get(i)).getName();
                            }
                        }
                    }
                    builder.setShopNames(shopNames);
                }
                responseBuilder.setData(builder.build());
            } else {
                responseBuilder.setCode(result.getCode());
                responseBuilder.setMsg(result.getMsg());
            }
        }
        responseObserver.onNext(responseBuilder.build());
        responseObserver.onCompleted();
    }

    //日期格式转换
    private void dateConversion(DownloadCenterRpc.Builder builder,AdDownloadCenter vo) {
        builder.setReportCycle(vo.getReportCycle());
        LocalDate localDate = LocalDate.now();
        String today = localDate.format(DateTimeFormatter.ofPattern(DateUtil.PATTERN));
        switch (vo.getReportCycle()) {
            case "今天" :
                builder.setReportStartDate(today);
                builder.setReportEndDate(today);
                break;
            case "昨天":
                String last1Days = localDate.minusDays(1).
                        format(DateTimeFormatter.ofPattern(DateUtil.PATTERN));
                builder.setReportStartDate(last1Days);
                builder.setReportEndDate(last1Days);
                break;
            case "最近3天":
                String last3Days = localDate.minusDays(3).format(DateTimeFormatter.ofPattern(DateUtil.PATTERN));
                builder.setReportStartDate(last3Days);
                builder.setReportEndDate(today);
                break;
            case "最近7天":
                String last7Days = localDate.minusDays(7).
                        format(DateTimeFormatter.ofPattern(DateUtil.PATTERN));
                builder.setReportStartDate(last7Days);
                builder.setReportEndDate(today);
                break;
            case "本周":
                LocalDate monday = localDate.with(TemporalAdjusters.
                        previousOrSame( DayOfWeek.MONDAY));
                builder.setReportStartDate(monday.format(DateTimeFormatter.ofPattern(DateUtil.PATTERN)));
                builder.setReportEndDate(today);
                break;
            case "上周":
                LocalDate lastMonday = localDate.with(TemporalAdjusters.
                        previousOrSame( DayOfWeek.MONDAY)).minusWeeks(1);
                LocalDate lastSunday = localDate.with(TemporalAdjusters.
                        nextOrSame(DayOfWeek.SUNDAY)).minusWeeks(1);
                builder.setReportStartDate(lastMonday.format(DateTimeFormatter.ofPattern(DateUtil.PATTERN)));
                builder.setReportEndDate(lastSunday.format(DateTimeFormatter.ofPattern(DateUtil.PATTERN)));
                break;
            case "本月":
                LocalDate firstDay = localDate.with(TemporalAdjusters.firstDayOfMonth()); // 获取当前月的第一天
                LocalDate lastDay = localDate.with(TemporalAdjusters.lastDayOfMonth());
                builder.setReportStartDate(firstDay.format(DateTimeFormatter.ofPattern(DateUtil.PATTERN)));
                builder.setReportEndDate(today);
                break;
            case "上个月":
                LocalDate firstDays = localDate.with(TemporalAdjusters.firstDayOfMonth()).minusMonths(1); // 获取当前月的第一天
                LocalDate lastDays = localDate.with(TemporalAdjusters.lastDayOfMonth()).minusMonths(1);
                builder.setReportStartDate(firstDays.format(DateTimeFormatter.ofPattern(DateUtil.PATTERN)));
                builder.setReportEndDate(lastDays.format(DateTimeFormatter.ofPattern(DateUtil.PATTERN)));
                break;
            case "最近14天":
                String last14Days = localDate.minusDays(14).
                        format(DateTimeFormatter.ofPattern(DateUtil.PATTERN));
                builder.setReportStartDate(last14Days);
                builder.setReportEndDate(today);
                break;
            case "最近30天":
                String last30Days = localDate.minusDays(30).
                        format(DateTimeFormatter.ofPattern(DateUtil.PATTERN));
                builder.setReportStartDate(last30Days);
                builder.setReportEndDate(today);
                break;
            case "最近60天":
                String last60Days = localDate.minusDays(60).
                        format(DateTimeFormatter.ofPattern(DateUtil.PATTERN));
                builder.setReportStartDate(last60Days);
                builder.setReportEndDate(today);
                break;
            case "最近90天":
                String last90Days = localDate.minusDays(90).
                        format(DateTimeFormatter.ofPattern(DateUtil.PATTERN));
                builder.setReportStartDate(last90Days);
                builder.setReportEndDate(today);
                break;
            default:
                builder.setReportStartDate(vo.getReportStartDate());
                builder.setReportEndDate(vo.getReportEndDate());
                break;
        }
    }

    @Override
    public void delDownload(DownloadCenterDelRequest request, StreamObserver<CommonResponse> responseObserver) {
        CommonResponse.Builder responseBuilder = CommonResponse.newBuilder();
        if (!request.hasPuid() || !request.hasTaskId()) {
            responseBuilder.setCode(Int32Value.of(Result.ERROR));
            responseBuilder.setMsg("请求参数错误");
        } else {
            Result<Integer> result = adDownloadCenterService.delDownloadTask(request.getPuid(),request.getTaskId(), request.getShopIdsList());
            if (result.getCode() == Result.SUCCESS) {
                responseBuilder.setCode(Int32Value.of(result.getCode()));
                responseBuilder.setMsg(result.getMsg());
                Integer count = result.getData();
                if (count != null) {
                    responseBuilder.setData(result.getData().toString());
                }
            } else {
                responseBuilder.setCode(Int32Value.of(result.getCode()));
                responseBuilder.setMsg(result.getMsg());
            }
        }
        responseObserver.onNext(responseBuilder.build());
        responseObserver.onCompleted();
    }
}
