package com.meiyunji.sponsored.api.report;

import com.amazon.advertising.mode.MarketTimezoneAndCurrencyEnum;
import com.google.protobuf.DoubleValue;
import com.google.protobuf.Int32Value;
import com.google.protobuf.ProtocolStringList;
import com.meiyunji.amazon.mws.base.AmznEndpoint;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdSdProductReportDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdSdProductReport;
import com.meiyunji.sponsored.rpc.report.sd.*;
import com.meiyunji.sponsored.rpc.vo.ReportDataRpcVo;
import com.meiyunji.sponsored.service.cpc.service.IAmazonAdSdCampaignReportService;
import com.meiyunji.sponsored.service.cpc.service.IAmazonAdSdGroupReportService;
import com.meiyunji.sponsored.service.cpc.service.IAmazonAdSdProductReportService;
import com.meiyunji.sponsored.service.cpc.service.IAmazonAdSdTargetingReportService;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.vo.ReportDataVo;
import com.meiyunji.sponsored.service.cpc.vo.ReportParam;
import com.meiyunji.sponsored.service.cpc.vo.SearchVo;
import com.meiyunji.sponsored.service.cpc.vo.SumReportDataVo;
import io.grpc.stub.StreamObserver;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.lognet.springboot.grpc.GRpcService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: wade
 * @date: 2021/10/22 17:02
 * @describe: 广告报告
 */
@GRpcService
@Slf4j
public class AdSdReportRpcService extends RPCSdReportServiceGrpc.RPCSdReportServiceImplBase {

    @Autowired
    private IAmazonAdSdCampaignReportService campaignReportService;
    @Autowired
    private IAmazonAdSdGroupReportService groupReportService;
    @Autowired
    private IAmazonAdSdProductReportService productReportService;
    @Autowired
    private IAmazonAdSdProductReportDao productReportDao;
    @Autowired
    private IAmazonAdSdTargetingReportService targetingReportService;

    /**
     * 汇总数据
     * @param request
     * @param responseObserver
     */
    @Override
    public void getSumReport(SdReportSumReportRequest request, StreamObserver<SdReportSumReportResponse> responseObserver) {
        log.info("sd-report汇总数据 request {}", request);
        SdReportSumReportResponse.Builder builder = SdReportSumReportResponse.newBuilder();
        ReportParam param = new ReportParam();
        param.setPuid(request.getPuid().getValue());
        param.setShopId(request.getShopId().getValue());
        param.setMarketplaceId(request.getMarketplaceId());
        param.setTabType(request.getTabType());
        param.setTabId(request.getTabId());
        param.setCampaignId(request.getCampaignId());
        param.setGroupId(request.getGroupId());
        param.setKeywordId(request.getKeywordId());
        param.setTargetId(request.getTargetId());
        param.setLastMonth(request.getLastMonth().getValue());
        param.setOrderField(request.getOrderField());
        param.setOrderValue(request.getOrderValue());
        param.setExportTitle(request.getExportTitle());
        param.setStartDate(request.getStartDate());
        param.setEndDate(request.getEndDate());


        //做参数校验
        if (!request.hasShopId() || !request.hasPuid() || checkReportParam(param, request.getPageType())) {
            builder.setMsg("请求参数错误");
            builder.setCode(Int32Value.of(Result.ERROR));
        } else {
            SumReportDataVo vo = null;
            if (Constants.PAGE_TYPE_CAMPAIGN.equals(request.getPageType())) {  //广告活动
                vo = campaignReportService.getSumReport(param.getPuid(), param.getShopId(), param.getMarketplaceId(), param);
            } else if (Constants.PAGE_TYPE_GROUP.equals(request.getPageType())) {  //广告组
                vo = groupReportService.getSumReport(param.getPuid(), param.getShopId(), param.getMarketplaceId(), param);
            } else if (Constants.ITEM_TYPE_TARGET.equals(request.getPageType())) {  //商品投放
                vo = targetingReportService.getSumReport(param.getPuid(), param.getShopId(), param.getMarketplaceId(), param);
            } else if (Constants.PAGE_TYPE_PRODUCT.equals(request.getPageType())) {  //广告产品
                vo = productReportService.getSumReport(param.getPuid(), param.getShopId(), param.getMarketplaceId(), param);
            }

            //处理返回结果
            builder.setCode(Int32Value.of(Result.SUCCESS));
            SdReportSumReportResponse.SumReportData.Builder dataBuilder = SdReportSumReportResponse.SumReportData.newBuilder();
            MarketTimezoneAndCurrencyEnum marketCurrency = MarketTimezoneAndCurrencyEnum.getByMarketplaceId(param.getMarketplaceId());
            if (marketCurrency != null) {
                dataBuilder.setCurrency(marketCurrency.getCurrencyCode());
            }

            SdReportSumReportResponse.SumReportData.SumReportInnerData.Builder growDataBuilder = SdReportSumReportResponse.SumReportData.SumReportInnerData.newBuilder();
            if (vo!=null) {
                if (vo.getCpcGrow()!=null) {
                    growDataBuilder.setCpcGrow(DoubleValue.of(vo.getCpcGrow().doubleValue()));
                }
                if (vo.getCostGrow()!=null) {
                    growDataBuilder.setCostGrow(DoubleValue.of(vo.getCostGrow().doubleValue()));
                }
                if (vo.getSalesGrow()!=null) {
                    growDataBuilder.setSalesGrow(DoubleValue.of(vo.getSalesGrow().doubleValue()));
                }
                if (vo.getAcosGrow()!=null) {
                    growDataBuilder.setAcosGrow(DoubleValue.of(vo.getAcosGrow().doubleValue()));
                }
                if (vo.getImpressionsGrow()!=null) {
                    growDataBuilder.setImpressionsGrow(DoubleValue.of(vo.getImpressionsGrow()));
                }
                if (vo.getClicksGrow()!=null) {
                    growDataBuilder.setClicksGrow(DoubleValue.of(vo.getClicksGrow()));
                }
                if (vo.getOrderNumGrow()!=null) {
                    growDataBuilder.setOrderNumGrow(DoubleValue.of(vo.getOrderNumGrow()));
                }
                if (vo.getClickRateGrow()!=null) {
                    growDataBuilder.setClickRateGrow(DoubleValue.of(vo.getClickRateGrow()));
                }
                if (vo.getSalesConversionRateGrow()!=null) {
                    growDataBuilder.setSalesConversionRateGrow(DoubleValue.of(vo.getSalesConversionRateGrow()));
                }
            }

            ReportDataVo reportVo = vo.getReportVo();

            ReportDataRpcVo.Builder reportRpcVoBuilder = convertVoToRpcMessage(reportVo);

            growDataBuilder.setReportVo(reportRpcVoBuilder.build());

            dataBuilder.setData(growDataBuilder.build());

            builder.setData(dataBuilder.build());
        }

        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();

    }

    /**
     * chart图表数据
     * @param request
     * @param responseObserver
     */
    @Override
    public void getChartList(SdReportChartListRequest request, StreamObserver<SdReportChartListResponse> responseObserver) {
        log.info("sd-reportchart图表数据 request {}", request);
        SdReportChartListResponse.Builder builder = SdReportChartListResponse.newBuilder();

        ReportParam param = new ReportParam();
        param.setPuid(request.getPuid().getValue());
        param.setShopId(request.getShopId().getValue());
        param.setMarketplaceId(request.getMarketplaceId());
        param.setTabType(request.getTabType());
        param.setTabId(request.getTabId());
        param.setCampaignId(request.getCampaignId());
        param.setGroupId(request.getGroupId());
        param.setKeywordId(request.getKeywordId());
        param.setTargetId(request.getTargetId());
        param.setLastMonth(request.getLastMonth().getValue());
        param.setOrderField(request.getOrderField());
        param.setOrderValue(request.getOrderValue());
        param.setExportTitle(request.getExportTitle());
        param.setStartDate(request.getStartDate());
        param.setEndDate(request.getEndDate());

        //做参数校验
        if (!request.hasPuid() || !request.hasShopId() || checkReportParam(param, request.getPageType())) {
            builder.setMsg("请求参数错误");
            builder.setCode(Int32Value.of(Result.ERROR));
        } else {
            List<ReportDataVo> vo = null;
            if (Constants.PAGE_TYPE_CAMPAIGN.equals(request.getPageType())) {  //广告活动
                vo = campaignReportService.getChartList(param.getPuid(), param.getShopId(), param.getMarketplaceId(), param);
            } else if (Constants.PAGE_TYPE_GROUP.equals(request.getPageType())) {  //广告组
                vo = groupReportService.getChartList(param.getPuid(), param.getShopId(), param.getMarketplaceId(), param);
            } else if (Constants.ITEM_TYPE_TARGET.equals(request.getPageType())) {  //商品投放
                vo = targetingReportService.getChartList(param.getPuid(), param.getShopId(), param.getMarketplaceId(), param);
            } else if (Constants.PAGE_TYPE_PRODUCT.equals(request.getPageType())) {  //广告产品
                vo = productReportService.getChartList(param.getPuid(), param.getShopId(), param.getMarketplaceId(), param);
            }
            builder.setCode(Int32Value.of(Result.SUCCESS));

            if (CollectionUtils.isNotEmpty(vo)) {
                List<ReportDataRpcVo> rpcVos = vo.stream().filter(Objects::nonNull).map(item -> {
                    return convertVoToRpcMessage(item).build();
                }).collect(Collectors.toList());

                builder.addAllData(rpcVos);
            }
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();

    }

    /**
     * 分页数据
     * @param request
     * @param responseObserver
     */
    @Override
    public void pageList(SdReportPageListRequest request, StreamObserver<SdReportPageListResponse> responseObserver) {
        log.info("sd-report分页数据 request {}", request);
        SdReportPageListResponse.Builder builder = SdReportPageListResponse.newBuilder();

        SearchVo searchVo = new SearchVo();
        searchVo.setPuid(request.getPuid().getValue());
        searchVo.setShopId(request.getShopId().getValue());
        searchVo.setMarketplaceId(request.getMarketplaceId());
        searchVo.setStartDate(request.getStartDate());
        searchVo.setEndDate(request.getEndDate());
        searchVo.setSearchType(request.getSearchType());
        searchVo.setSearchValue(request.getSearchValue());
        searchVo.setOrderField(request.getOrderField());
        searchVo.setOrderValue(request.getOrderValue());
        searchVo.setAdGroupName(request.getAdGroupName());
        searchVo.setTabType(request.getTabType());
        searchVo.setCampaignId(request.getCampaignId());
        searchVo.setGroupId(request.getGroupId());
        //处理list
        List<Integer> shopIds = request.getShopIdsList().stream().map(Int32Value::getValue).collect(Collectors.toList());
        searchVo.setShopIds(shopIds);
        //处理list
        searchVo.setSpCampaignType(request.getSpCampaignType());
        ProtocolStringList campaignIdsList = request.getCampaignIdsList();
        List<String> campaignIds = new ArrayList<>(campaignIdsList);
        searchVo.setCampaignIds(campaignIds);
        searchVo.setSpGroupType(request.getSpGroupType());
        //处理list
        ProtocolStringList groupIdsList = request.getGroupIdsList();
        List<String> groupIds = new ArrayList<>(groupIdsList);
        searchVo.setGroupIds(groupIds);
        searchVo.setMatchType(request.getMatchType());
        searchVo.setSpTargetType(request.getSpTargetType());

        if (!request.hasPuid() || !request.hasShopId() || checkParam(searchVo, request.getPageType())) {
            builder.setMsg("请求参数错误");
            builder.setCode(Int32Value.of(Result.ERROR));
        } else {
            Page page = new Page(request.hasPageNo() ? request.getPageNo().getValue() : 1, request.hasPageSize() ? request.getPageSize().getValue() : 20);
            String marketplaceId = searchVo.getMarketplaceId();
            //处理业务返回结果
            SdReportPageListResponse.SdReportData.Builder dataBuilder = SdReportPageListResponse.SdReportData.newBuilder();
            if (Constants.PAGE_TYPE_CAMPAIGN.equals(request.getPageType())) {  //广告活动
                page = campaignReportService.pageList(searchVo.getPuid(), searchVo, page);
            } else if (Constants.PAGE_TYPE_GROUP.equals(request.getPageType())) {  //广告组
                page = groupReportService.pageList(searchVo.getPuid(), searchVo, page);
            } else if (Constants.ITEM_TYPE_TARGET.equals(request.getPageType())) {  //商品投放
                page = targetingReportService.pageList(searchVo.getPuid(), searchVo, page);
            } else if (Constants.PAGE_TYPE_PRODUCT.equals(request.getPageType())) {  //广告产品
                page = productReportService.pageList(searchVo.getPuid(), searchVo, page);
                if (searchVo.getTabType()!=null) {
                    dataBuilder.setTabType(searchVo.getTabType());
                }
                AmznEndpoint amznEndpoint = AmznEndpoint.getByMarketplaceId(searchVo.getMarketplaceId());
                if (amznEndpoint !=null && amznEndpoint.getDomain()!=null) {
                    dataBuilder.setDomain(amznEndpoint.getDomain());
                }
            }
            MarketTimezoneAndCurrencyEnum mt;
            if ((mt = MarketTimezoneAndCurrencyEnum.getByMarketplaceId(marketplaceId)) != null) {
                dataBuilder.setCurrency(mt.getCurrencyCode());
            }
            //处理page
            SdReportDataPage.Builder pageBuilder = SdReportDataPage.newBuilder();
            pageBuilder.setPageNo(Int32Value.of(page.getPageNo()));
            pageBuilder.setPageSize(Int32Value.of(page.getPageSize()));
            pageBuilder.setTotalPage(Int32Value.of(page.getTotalPage()));
            pageBuilder.setTotalSize(Int32Value.of(page.getTotalSize()));

            //处理list
            List<ReportDataVo> rows = page.getRows();
            if (CollectionUtils.isNotEmpty(rows)) {
                List<ReportDataRpcVo> rpcVos = rows.stream().filter(Objects::nonNull).map(item -> {
                    ReportDataRpcVo.Builder voBuilder = convertVoToRpcMessage(item);
                    return voBuilder.build();
                }).collect(Collectors.toList());
                pageBuilder.addAllRows(rpcVos);
            }
            dataBuilder.setPage(pageBuilder.build());
            builder.setData(dataBuilder.build());
        }

        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * 详情分页数据
     * @param request
     * @param responseObserver
     */
    @Override
    public void dePageList(SdReportDePageListRequest request, StreamObserver<SdReportDePageListResponse> responseObserver) {
        log.info("sd-report详情分页数据 request {}", request);
        SdReportDePageListResponse.Builder builder = SdReportDePageListResponse.newBuilder();

        ReportParam param = new ReportParam();
        param.setPuid(request.getPuid().getValue());
        param.setShopId(request.getShopId().getValue());
        param.setMarketplaceId(request.getMarketplaceId());
        param.setTabType(request.getTabType());
        param.setTabId(request.getTabId());
        param.setCampaignId(request.getCampaignId());
        param.setGroupId(request.getGroupId());
        param.setKeywordId(request.getKeywordId());
        param.setTargetId(request.getTargetId());
        param.setLastMonth(request.getLastMonth().getValue());
        param.setOrderField(request.getOrderField());
        param.setOrderValue(request.getOrderValue());
        param.setExportTitle(request.getExportTitle());
        param.setStartDate(request.getStartDate());
        param.setEndDate(request.getEndDate());
        //做参数校验
        if (!request.hasShopId() || !request.hasPuid() || checkReportParam(param, request.getPageType())) {
            builder.setMsg("请求参数错误");
            builder.setCode(Int32Value.of(Result.ERROR));
        } else {
            Page page = new Page(request.hasPageNo() ? request.getPageNo().getValue() : 1, request.hasPageSize() ? request.getPageSize().getValue() : 20);
            String marketplaceId = param.getMarketplaceId();
            if (Constants.PAGE_TYPE_CAMPAIGN.equals(request.getPageType())) {  //广告活动
                page = campaignReportService.detailPageList(param.getPuid(), param, page);
            } else if (Constants.PAGE_TYPE_GROUP.equals(request.getPageType())) {  //广告组
                page = groupReportService.detailPageList(param.getPuid(), param, page);
            } else if (Constants.ITEM_TYPE_TARGET.equals(request.getPageType())) {  //商品投放
                page = targetingReportService.detailPageList(param.getPuid(), param, page);
            } else if (Constants.PAGE_TYPE_PRODUCT.equals(request.getPageType())) {  //广告产品
                page = productReportService.detailPageList(param.getPuid(), param, page);
            }
            SdReportDePageListResponse.SdReportDeData.Builder dataBuilder = SdReportDePageListResponse.SdReportDeData.newBuilder();
            MarketTimezoneAndCurrencyEnum mt;
            if ((mt = MarketTimezoneAndCurrencyEnum.getByMarketplaceId(marketplaceId)) != null) {
                dataBuilder.setCurrency(mt.getCurrencyCode());
            }
            //处理page
            SdReportDataPage.Builder pageBuilder = SdReportDataPage.newBuilder();
            pageBuilder.setPageNo(Int32Value.of(page.getPageNo()));
            pageBuilder.setPageSize(Int32Value.of(page.getPageSize()));
            pageBuilder.setTotalPage(Int32Value.of(page.getTotalPage()));
            pageBuilder.setTotalSize(Int32Value.of(page.getTotalSize()));

            //处理list
            List<ReportDataVo> rows = page.getRows();
            if (CollectionUtils.isNotEmpty(rows)) {
                List<ReportDataRpcVo> rpcVos = rows.stream().filter(Objects::nonNull).map(item -> {
                    ReportDataRpcVo.Builder voBuilder = convertVoToRpcMessage(item);
                    return voBuilder.build();
                }).collect(Collectors.toList());
                pageBuilder.addAllRows(rpcVos);
            }
            dataBuilder.setPage(pageBuilder.build());
            builder.setData(dataBuilder.build());
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * 广告活动数据
     * @param request
     * @param responseObserver
     */
    @Override
    public void getAdCampaignMap(SdGetAdCampaignMapRequest request, StreamObserver<SdGetAdCampaignMapResponse> responseObserver) {
        log.info("sd-report广告活动数据 request {}", request);
        SdGetAdCampaignMapResponse.Builder builder = SdGetAdCampaignMapResponse.newBuilder();

        if (!request.hasShopId() || !request.hasPuid() ||StringUtils.isBlank(request.getMarketplaceId())) {
            builder.setMsg("请求参数错误");
            builder.setCode(Int32Value.of(Result.ERROR));
        } else {
            Map<String, String> map = groupReportService.getAdCampaignMap(request.getPuid().getValue(), request.getShopId().getValue(), request.getMarketplaceId());
            builder.setCode(Int32Value.of(Result.SUCCESS));
            if (MapUtils.isNotEmpty(map)) {
                builder.putAllData(map);
            }
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();

    }

    /**
     * 广告组数据
     * @param request
     * @param responseObserver
     */
    @Override
    public void getAdProductMap(SdGetAdProductMapRequest request, StreamObserver<SdGetAdProductMapResponse> responseObserver) {
        log.info("sd-report广告组数据 request {}", request);
        SdGetAdProductMapResponse.Builder builder = SdGetAdProductMapResponse.newBuilder();

        if (!request.hasShopId() || !request.hasPuid() || StringUtils.isBlank(request.getMarketplaceId())) {
            builder.setMsg("请求参数错误");
            builder.setCode(Int32Value.of(Result.ERROR));
        } else {
            Map<String, String> map = null;
            if (Constants.PAGE_TYPE_TARGETING.equals(request.getPageType())) {  //商品投放
                map = targetingReportService.getAdProductMap(request.getPuid().getValue(), request.getShopId().getValue(),  request.getMarketplaceId(), request.getCampaignId());
            } else if (Constants.PAGE_TYPE_PRODUCT.equals(request.getPageType())) {  //广告产品
                map = productReportService.getAdProductMap(request.getPuid().getValue(), request.getShopId().getValue(), request.getMarketplaceId(), request.getCampaignId());
            }
            builder.setCode(Int32Value.of(Result.SUCCESS));
            if (MapUtils.isNotEmpty(map)) {
                builder.putAllData(map);
            }
        }

        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void getTaskSumReport(GetSdTaskSumReportRequest request, StreamObserver<GetSdTaskSumReportResponse> responseObserver) {
        GetSdTaskSumReportResponse.Builder builder = GetSdTaskSumReportResponse.newBuilder();
        List<AmazonAdSdProductReport> list = productReportDao.getTaskSumReport(request.getPuid(), request.getShopId(),
                    request.getMarketplaceId(), request.getStartDate(), request.getEndDate());
        builder.setCode(Result.SUCCESS);
        if (CollectionUtils.isNotEmpty(list)) {
            List<AmazonAdSdProductReportRpcVo> rpcVos = list.stream().filter(Objects::nonNull).map(item -> {
                AmazonAdSdProductReportRpcVo.Builder voBuilder = AmazonAdSdProductReportRpcVo.newBuilder();
                if (item.getSku() != null) {
                    voBuilder.setSku(item.getSku());
                }
                if (item.getCurrency() != null) {
                    voBuilder.setCurrency(item.getCurrency());
                }
                if (item.getCost() != null) {
                    voBuilder.setCost(String.valueOf(item.getCost()));
                }
                if (item.getSales7d() != null) {
                    voBuilder.setSales7D(String.valueOf(item.getSales7d()));
                }
                if (item.getConversions7d() != null) {
                    //广告订单量7天
                    voBuilder.setUnitsOrdered7D(String.valueOf(item.getConversions7d()));
                }
                if (item.getSales14d() != null) {
                    voBuilder.setSales14D(String.valueOf(item.getSales14d()));
                }
                if (item.getConversions14d() != null) {
                    //广告订单量14天
                    voBuilder.setUnitsOrdered14D(String.valueOf(item.getConversions14d()));
                }
                return voBuilder.build();
            }).collect(Collectors.toList());
            builder.addAllData(rpcVos);
        }

        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    private boolean checkParam(SearchVo vo, String itemType) {
        if (StringUtils.isBlank(vo.getStartDate()) || StringUtils.isBlank(vo.getEndDate())
                || StringUtils.isBlank(vo.getMarketplaceId())) {
            return true;
        }
        if (Constants.PAGE_TYPE_PRODUCT.equals(itemType) && StringUtils.isBlank(vo.getTabType())) {
            return true;
        }
        Date start = DateUtil.strToDate(vo.getStartDate(), "yyyy-MM-dd");
        Date end = DateUtil.strToDate(vo.getEndDate(), "yyyy-MM-dd");
        if (start == null || end == null) {
            return true;
        }
        vo.setStart(start);
        vo.setEnd(end);
        return false;
    }


    /**
     * vo转message
     */
    private ReportDataRpcVo.Builder convertVoToRpcMessage(ReportDataVo reportVo) {
        ReportDataRpcVo.Builder reportBuilder = ReportDataRpcVo.newBuilder();

        if (reportVo != null) {
            if (reportVo.getShopId() != null) {
                reportBuilder.setShopId(Int32Value.of(reportVo.getShopId()));
            }
            if (reportVo.getCountDate() != null) {
                reportBuilder.setCountDate(reportVo.getCountDate());
            }
            if (reportVo.getCpc() != null) {
                reportBuilder.setCpc(DoubleValue.of(reportVo.getCpc().doubleValue()));
            }
            if (reportVo.getCost() != null) {
                reportBuilder.setCost(DoubleValue.of(reportVo.getCost().doubleValue()));
            }
            if (reportVo.getSales() != null) {
                reportBuilder.setSales(DoubleValue.of(reportVo.getSales().doubleValue()));
            }
            if (reportVo.getAcos() != null) {
                reportBuilder.setAcos(DoubleValue.of(reportVo.getAcos().doubleValue()));
            }
            if (reportVo.getImpressions() != null) {
                reportBuilder.setImpressions(Int32Value.of(reportVo.getImpressions()));
            }
            if (reportVo.getClicks() != null) {
                reportBuilder.setClicks(Int32Value.of(reportVo.getClicks()));
            }
            if (reportVo.getOrderNum() != null) {
                reportBuilder.setOrderNum(Int32Value.of(reportVo.getOrderNum()));
            }
            if (reportVo.getClickRate() != null) {
                reportBuilder.setClickRate(DoubleValue.of(reportVo.getClickRate()));
            }
            if (reportVo.getSalesConversionRate() != null) {
                reportBuilder.setSalesConversionRate(DoubleValue.of(reportVo.getSalesConversionRate()));
            }
            if (reportVo.getCampaignId() != null) {
                reportBuilder.setCampaignId(reportVo.getCampaignId());
            }
            if (reportVo.getCampaignName() != null) {
                reportBuilder.setCampaignName(reportVo.getCampaignName());
            }
            if (reportVo.getAdGroupId() != null) {
                reportBuilder.setAdGroupId(reportVo.getAdGroupId());
            }
            if (reportVo.getAdGroupName() != null) {
                reportBuilder.setAdGroupName(reportVo.getAdGroupName());
            }
            if (reportVo.getKeywordText() != null) {
                reportBuilder.setKeywordText(reportVo.getKeywordText());
            }
            if (reportVo.getKeywordId() != null) {
                reportBuilder.setKeywordId(reportVo.getKeywordId());
            }
            if (reportVo.getSku() != null) {
                reportBuilder.setSku(reportVo.getSku());
            }
            if (reportVo.getAsin() != null) {
                reportBuilder.setAsin(reportVo.getAsin());
            }
            if (reportVo.getParentAsin() != null) {
                reportBuilder.setParentAsin(reportVo.getParentAsin());
            }
            if (reportVo.getTitle() != null) {
                reportBuilder.setTitle(reportVo.getTitle());
            }
            if (reportVo.getMainImage() != null) {
                reportBuilder.setMainImage(reportVo.getMainImage());
            }
            if (reportVo.getTargetingExpression() != null) {
                reportBuilder.setTargetingExpression(reportVo.getTargetingExpression());
            }
            if (reportVo.getTargetId() != null) {
                reportBuilder.setTargetId(reportVo.getTargetId());
            }
            if (reportVo.getAdId() != null) {
                reportBuilder.setAdId(reportVo.getAdId());
            }
            if (reportVo.getTargetingText() != null) {
                reportBuilder.setTargetingText(reportVo.getTargetingText());
            }
        }

        return reportBuilder;
    }



    private boolean checkReportParam(ReportParam param, String pageType) {
        if (StringUtils.isBlank(param.getStartDate()) || StringUtils.isBlank(param.getEndDate())
                || param.getShopId() == null || StringUtils.isBlank(param.getMarketplaceId())) {
            return true;
        }
        if (Constants.PAGE_TYPE_PRODUCT.equals(pageType) && (StringUtils.isBlank(param.getTabType()) || StringUtils.isBlank(param.getTabId()))) {
            return true;
        }
        if (Constants.PAGE_TYPE_CAMPAIGN.equals(pageType) && StringUtils.isBlank(param.getCampaignId())) {
            return true;
        }
        if (Constants.PAGE_TYPE_GROUP.equals(pageType) && StringUtils.isBlank(param.getGroupId())) {
            return true;
        }
        if (Constants.ITEM_TYPE_TARGET.equals(pageType) && StringUtils.isBlank(param.getTargetId())) {
            return true;
        }
        Date start = DateUtil.strToDate(param.getStartDate(), "yyyy-MM-dd");
        Date end = DateUtil.strToDate(param.getEndDate(), "yyyy-MM-dd");
        if (start == null || end == null) {
            return true;
        }
        param.setStart(start);
        param.setEnd(end);
        return false;
    }
}
