package com.meiyunji.sponsored.api.common;

import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.common.util.ResultUtil;
import com.meiyunji.sponsored.grpc.common.*;
import com.meiyunji.sponsored.grpc.entry.GetCampaignBudgetShortfallCountRpcVoPb;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdCampaignAllReportDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdShopReport;
import com.meiyunji.sponsored.service.doris.dao.IOdsAmazonAdBudgetUsageDao;
import com.meiyunji.sponsored.service.doris.dao.impl.OdsAmazonAdBudgetUsageDaoImpl;
import com.meiyunji.sponsored.service.newDashboard.util.CalculateAdDataUtil;
import com.meiyunji.sponsored.service.newDashboard.util.CalculateUtil;
import io.grpc.stub.StreamObserver;
import lombok.extern.slf4j.Slf4j;
import org.lognet.springboot.grpc.GRpcService;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@GRpcService
@Slf4j
public class SellerHomeApiService extends SellfoxHomeApiServiceGrpc.SellfoxHomeApiServiceImplBase {

    private final IAmazonAdCampaignAllReportDao amazonAdCampaignAllReportDao;
    @Resource
    private IOdsAmazonAdBudgetUsageDao odsAmazonAdBudgetUsageDao;

    public SellerHomeApiService(IAmazonAdCampaignAllReportDao amazonAdCampaignAllReportDao) {
        this.amazonAdCampaignAllReportDao = amazonAdCampaignAllReportDao;
    }

    @Override
    public void listAdPerformance(ListAdPerformanceRequestPb.ListAdPerformanceRequest request, StreamObserver<ListAdPerformanceResponsePb.ListAdPerformanceResponse> responseObserver) {
        log.info("request: {}", request);
        StopWatch stopWatch = new StopWatch();
        try {
            stopWatch.start("sellfox-home 查询准备");
            List<ListAdPerformanceRequestPb.ListAdPerformanceRequest.Item> itemList = request.getItemList();
            String startDate = itemList.stream().map(ListAdPerformanceRequestPb.ListAdPerformanceRequest.Item::getStartDate)
                    .min(Comparator.comparing(String::valueOf)).get();

            Map<Integer, String> startDateMap = itemList.stream().collect(Collectors.toMap(
                    ListAdPerformanceRequestPb.ListAdPerformanceRequest.Item::getShopId,
                    ListAdPerformanceRequestPb.ListAdPerformanceRequest.Item::getStartDate));

            List<Integer> shopIds = itemList.stream()
                    .map(ListAdPerformanceRequestPb.ListAdPerformanceRequest.Item::getShopId).collect(Collectors.toList());
            stopWatch.stop();

            stopWatch.start("sellfox-home 查询数据库");
            List<AmazonAdShopReport> reports = amazonAdCampaignAllReportDao.getNewHomeCpcData(itemList.get(0).getPuid(), shopIds,
                    startDate);
            stopWatch.stop();

            stopWatch.start("sellfox-home 组装grpc response对象");
            List<ListAdPerformanceResponsePb.ListAdPerformanceResponse.Item> itemPbs = reports.stream()
                    .filter(o-> o.getCountDate() != null).filter($ -> {
                try {
                    LocalDate countDate = LocalDate.parse($.getCountDate(), DateTimeFormatter.ofPattern("yyyyMMdd"));
                    LocalDate start = LocalDate.parse(startDateMap.get($.getShopId()), DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                    return !countDate.isBefore(start);
                } catch (Exception exception) {
                    log.error("LocalDate parse error, errorMsg: {}", exception.getMessage());
                    return false;
                }
            }).map(o -> {
                ListAdPerformanceResponsePb.ListAdPerformanceResponse.Item.Builder itemBuilder =
                        ListAdPerformanceResponsePb.ListAdPerformanceResponse.Item.newBuilder();
                itemBuilder.setPuid(o.getPuid());
                itemBuilder.setShopId(o.getShopId());
                itemBuilder.setDate(LocalDate.parse(o.getCountDate(),
                        DateTimeFormatter.ofPattern("yyyyMMdd")).format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                itemBuilder.setAdOrder(o.getOrderNum());
                itemBuilder.setAdSales(o.getTotalSales().doubleValue());
                itemBuilder.setCosts(o.getCost().doubleValue());
                itemBuilder.setLastUpdateAt(DateUtil.dateToStrWithFormat(o.getUpdateTime(), "yyyy-MM-dd HH:mm:ss"));
                itemBuilder.setImpressions(o.getImpressions());
                itemBuilder.setClicks(o.getClicks());
                return itemBuilder.build();
            }).collect(Collectors.toList());
            stopWatch.stop();

            log.info("Sellfox home api consume time---------->{}",stopWatch.prettyPrint());
            ListAdPerformanceResponsePb.ListAdPerformanceResponse.Builder builder =
                    ListAdPerformanceResponsePb.ListAdPerformanceResponse.newBuilder();
            builder.addAllItem(itemPbs);
            ListAdPerformanceResponsePb.ListAdPerformanceResponse response = builder.build();
            log.info("response: {}", response);
            responseObserver.onNext(response);
            responseObserver.onCompleted();
        } catch (Exception e) {
            log.error("", e);
            responseObserver.onError(e);
        }
    }

    @Override
    public void getCampaignBudgetShortfallCount(GetCampaignBudgetShortfallCountRequestPb.GetCampaignBudgetShortfallCountRequest request, StreamObserver<GetCampaignBudgetShortfallCountResponsePb.GetCampaignBudgetShortfallCountResponse> responseObserver) {
        log.info("sellfox-home 预算不足 request: {}", request);
        StopWatch stopWatch = new StopWatch();
        GetCampaignBudgetShortfallCountResponsePb.GetCampaignBudgetShortfallCountResponse.Builder builder = GetCampaignBudgetShortfallCountResponsePb.GetCampaignBudgetShortfallCountResponse.newBuilder();
        GetCampaignBudgetShortfallCountRpcVoPb.GetCampaignBudgetShortfallCountRpcVo.Builder vo = GetCampaignBudgetShortfallCountRpcVoPb.GetCampaignBudgetShortfallCountRpcVo.newBuilder();
        vo.setCount(0);
        builder.setCode(ResultUtil.SUCCESS);
        try {
            stopWatch.start("sellfox-home 预算不足 ");
            if (request.getPuid() == 0 || request.getShopIdsCount() < 1) {
                builder.setData(vo.build());
                log.info("查询条件缺失");
                responseObserver.onNext(builder.build());
                responseObserver.onCompleted();
                return;
            }
            List<String> siteToday = CalculateAdDataUtil.getSiteToday(request.getMarketplaceIdsList());
            Integer countByBudgetUsagePercentage = odsAmazonAdBudgetUsageDao.getCountByBudgetUsagePercentage(request.getPuid(), request.getShopIdsList(), request.getMarketplaceIdsList(), siteToday, 100);
            if (countByBudgetUsagePercentage != null) {
                vo.setCount(countByBudgetUsagePercentage);
            }
            builder.setData(vo.build());
            stopWatch.stop();
            log.info("Sellfox home 预算不足 api consume time---------->{}",stopWatch.prettyPrint());
        } catch (Exception e) {
            log.error("", e);
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }
}
