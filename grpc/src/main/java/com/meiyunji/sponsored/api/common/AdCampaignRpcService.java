package com.meiyunji.sponsored.api.common;

import com.amazon.advertising.mode.Adjustment;
import com.amazon.advertising.mode.PredicateEnum;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.protobuf.Int32Value;
import com.google.protobuf.Int64Value;
import com.meiyunji.amazon.mws.base.AmznEndpoint;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.common.util.JSONUtil;
import com.meiyunji.sponsored.common.util.ParamCopyUtil;
import com.meiyunji.sponsored.rpc.adCampaign.*;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdCampaignAll;
import com.meiyunji.sponsored.service.cpc.service2.ICpcCampaignService;
import com.meiyunji.sponsored.service.cpc.service2.sp.ICpcAdGroupService;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.vo.GroupProductVo;
import com.meiyunji.sponsored.service.enums.CampaignTypeEnum;
import com.meiyunji.sponsored.service.enums.SBCampaignCostTypeEnum;
import com.meiyunji.sponsored.service.enums.SBCampaignGoalEnum;
import io.grpc.stub.StreamObserver;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.lognet.springboot.grpc.GRpcService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StopWatch;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @author: ys
 * @date: 2024/1/19 14:26
 * @describe:
 */
@GRpcService
@Slf4j
public class AdCampaignRpcService extends RPCAdCampaignServiceGrpc.RPCAdCampaignServiceImplBase {

    @Autowired
    private ICpcCampaignService cpcCampaignService;

    @Autowired
    private ICpcAdGroupService cpcAdGroupService;

    @Override
    public void getCampaignInfo(CampaignInfoRequest request, StreamObserver<CampaignInfoResponse> responseObserver) {
        CampaignInfoResponse.Builder response = CampaignInfoResponse.newBuilder();
        CampaignInfoResponse.CampaignPageVo.Builder info = CampaignInfoResponse.CampaignPageVo.newBuilder();
        //查询广告活动基本数据
        Integer puid = Optional.of(request.getPuid()).map(Int32Value::getValue).orElse(null);
        Integer shopId = Optional.of(request.getShopId()).map(Int32Value::getValue).orElse(null);
        String campaignId = Optional.of(request.getCampaignId()).orElse("");
        if (!checkParams(puid, shopId, campaignId)) {
            response.setCode(Int32Value.of(Result.ERROR));
            response.setMsg("请求参数错误");
            responseObserver.onNext(response.build());
            responseObserver.onCompleted();
            return;
        }
        StopWatch sw = new StopWatch();
        sw.start();
        //查询广告活动信息
        AmazonAdCampaignAll campaignInfo = cpcCampaignService.getCampaignInfo(puid, shopId, campaignId);
        if (Objects.isNull(campaignInfo)) {
            response.setCode(Int32Value.of(Result.ERROR));
            response.setMsg("对应广告活动不存在");
            responseObserver.onNext(response.build());
            responseObserver.onCompleted();
            return;
        }
        buildCampaignResponse(campaignInfo, info);
        sw.stop();
        //设置返回值
        response.setData(info.build());
        response.setCode(Int32Value.of(Result.SUCCESS));
        log.info("获取广告活动基本信息接口调用花费时间 :{} ,puid: {},shopId: {}", sw.getTotalTimeSeconds(), request.getPuid(), request.getShopId());
        responseObserver.onNext(response.build());
        responseObserver.onCompleted();
    }

    private boolean checkParams(Integer puid, Integer shopId,
                                String campaignId) {
        return !Objects.isNull(puid) && !Objects.isNull(shopId) && !StringUtils.isBlank(campaignId);
    }

    private void buildCampaignResponse(AmazonAdCampaignAll sourceInfo, CampaignInfoResponse.CampaignPageVo.Builder info) {
        BeanUtils.copyProperties(sourceInfo, info, ParamCopyUtil.checkPropertiesNullOrEmpty(sourceInfo));
        Optional.ofNullable(sourceInfo.getId()).ifPresent(s -> info.setId(Int64Value.of(s)));
        Optional.ofNullable(sourceInfo.getShopId()).ifPresent(s -> info.setShopId(Int32Value.of(s)));
        Optional.ofNullable(sourceInfo.getName()).ifPresent(info::setCampaignName);
        Optional.ofNullable(sourceInfo.getBudget()).map(budget -> {
            budget = budget.setScale(2, RoundingMode.HALF_UP);
            return budget;
        }).map(BigDecimal::toString).ifPresent(info::setDailyBudget);
        Optional.ofNullable(sourceInfo.getCampaignType()).ifPresent(info::setCampaignType);
        Optional.ofNullable(sourceInfo.getStartDate()).map(DateUtil::format).ifPresent(info::setStartDate);
        Optional.ofNullable(sourceInfo.getEndDate()).map(DateUtil::format).ifPresent(info::setEndDate);
        Optional.ofNullable(sourceInfo.getType()).ifPresent(info::setAdType);
        if (CampaignTypeEnum.sp.getCampaignType().equals(sourceInfo.getType())) {
            Optional.ofNullable(sourceInfo.getTargetType()).ifPresent(info::setTargetType);
            List<String> adGroupTypeList = cpcAdGroupService.getAdGroupTypeByCampaignId(sourceInfo.getPuid(), sourceInfo.getShopId(), sourceInfo.getCampaignId());
            Optional.ofNullable(adGroupTypeList).ifPresent(info::addAllAdGroupTypeList);
        }
        //SB类型需要单独处理
        if (CampaignTypeEnum.sb.getCampaignType().equals(sourceInfo.getType())) {
            info.setTargetingType(Constants.MANUAL);
            Optional.ofNullable(sourceInfo.getTargetType()).ifPresent(info::setTargetType);
            Optional.ofNullable(sourceInfo.getBidOptimization()).ifPresent(info::setBidOptimization);
            Optional.ofNullable(sourceInfo.getBrandEntityId()).ifPresent(info::setBrandEntityId);
            Optional.ofNullable(sourceInfo.getBidMultiplier()).map(b -> b.setScale(2, RoundingMode.HALF_UP)
                    .toString()).ifPresent(info::setPlacementProductPage);
            SBCampaignGoalEnum sbCampaignGoalEnumByType = SBCampaignGoalEnum.getSBCampaignGoalEnumByType(sourceInfo.getAdGoal());
            if (sbCampaignGoalEnumByType!= null) {
                Optional.ofNullable(sbCampaignGoalEnumByType.getCode()).ifPresent(info::setAdGoal);
            }
            Optional.ofNullable(sourceInfo.getIsMultiAdGroupsEnabled()).ifPresent(info::setMultiAdGroups);
        }
        //如果costType为空，默认设置为cpc,适配前端
        if (StringUtils.isEmpty(sourceInfo.getCostType())) {
            info.setCostType(SBCampaignCostTypeEnum.CPC.getCode());
        }
        if (CampaignTypeEnum.sd.getCampaignType().equals(sourceInfo.getType())) {
            Optional.ofNullable(sourceInfo.getTactic()).ifPresent(info::setTargetingType);
        }
        // 广告位调整价
        if (StringUtils.isNotBlank(sourceInfo.getAdjustments())) {
            List<Adjustment> adjustments = null;
            try {
                adjustments = JSONUtil.jsonToObjectIgnoreUnKnownThrowable(sourceInfo.getAdjustments(), new TypeReference<List<Adjustment>>() {
                });
            } catch (IOException e) {
                log.error("adjustment:", e);
            }
            if (CollectionUtils.isNotEmpty(adjustments)) {
                for (Adjustment adjustment : adjustments) {
                    if (PredicateEnum.PLACEMENTPRODUCTPAGE.value().equals(adjustment.getPredicate())) {
                        info.setPlacementProductPage(String.valueOf(adjustment.getPercentage()));
                    } else if (PredicateEnum.PLACEMENTTOP.value().equals(adjustment.getPredicate())) {
                        info.setPlacementTop(String.valueOf(adjustment.getPercentage()));
                    } else if (PredicateEnum.PLACEMENTRESTOFSEARCH.value().equals(adjustment.getPredicate())) {
                        info.setPlacementRestOfSearch(String.valueOf(adjustment.getPercentage()));
                    } else if (PredicateEnum.SITEAMAZONBUSINESS.value().equals(adjustment.getPredicate()) && Constants.placementSiteAmazonBusinessMarketplaceIds.contains(sourceInfo.getMarketplaceId())) {
                        info.setPlacementSiteAmazonBusiness(String.valueOf(adjustment.getPercentage()));
                    }
                }
            }
        }
    }

    @Override
    public void getGroupProductList(GroupProductRequest request, StreamObserver<GroupProductResponse> responseObserver) {
        log.info("获取活动下广告组广告产品列表 request {}", request);
        GroupProductResponse.Builder response = GroupProductResponse.newBuilder();
        Integer puid = Optional.of(request.getPuid()).map(Int32Value::getValue).orElse(null);
        Integer sourceShopId = Optional.of(request.getSourceShopId()).map(Int32Value::getValue).orElse(null);
        Integer targetShopId = Optional.of(request.getTargetShopId()).map(Int32Value::getValue).orElse(null);
        String campaignId = Optional.of(request.getCampaignId()).orElse("");
        StopWatch sw = new StopWatch();
        sw.start();
        GroupProductVo productVo = cpcAdGroupService.getGroupProductList(puid, sourceShopId, targetShopId, campaignId);
        GroupProductResponse.GroupProductVo.Builder data = buildGroupProductResponse(productVo);
        response.setData(data.build());
        response.setCode(Int32Value.of(Result.SUCCESS));
        sw.stop();
        log.info("获取活动下广告组广告产品列表接口调用花费时间 :{} ,puid: {},shopId: {}", sw.getTotalTimeSeconds(), request.getPuid(), request.getSourceShopId());
        responseObserver.onNext(response.build());
        responseObserver.onCompleted();
    }

    private GroupProductResponse.GroupProductVo.Builder buildGroupProductResponse(GroupProductVo productVo) {
        GroupProductResponse.GroupProductVo.Builder data = GroupProductResponse.GroupProductVo.newBuilder();
        Optional.ofNullable(productVo.getOverGroupLimit()).ifPresent(data::setOverGroupLimit);
        Optional.ofNullable(productVo.getOverProductLimit()).ifPresent(data::setOverProductLimit);
        List<GroupProductResponse.GroupProductVo.GroupInfo> groupInfoList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(productVo.getGroupList())) {
            productVo.getGroupList().forEach(it -> {
                GroupProductResponse.GroupProductVo.GroupInfo.Builder groupBuilder = GroupProductResponse.GroupProductVo.GroupInfo.newBuilder();
                Optional.ofNullable(it.getAdGroupId()).ifPresent(groupBuilder::setAdGroupId);
                Optional.ofNullable(it.getDefaultBid()).ifPresent(groupBuilder::setDefaultBid);
                Optional.ofNullable(it.getName()).ifPresent(groupBuilder::setName);
                List<GroupProductResponse.GroupProductVo.GroupInfo.ProductInfo> productInfoList = new ArrayList<>();
                List<GroupProductResponse.GroupProductVo.GroupInfo.ProductInfo> notExistproductList = new ArrayList<>();
                if (CollectionUtils.isNotEmpty(it.getProductList())) {
                    it.getProductList().forEach(product -> {
                        GroupProductResponse.GroupProductVo.GroupInfo.ProductInfo.Builder productBuilder = getProductBuilder(product);
                        productInfoList.add(productBuilder.build());
                    });
                }
                groupBuilder.addAllProductList(productInfoList);
                groupInfoList.add(groupBuilder.build());
            });
        }
        data.addAllGroupList(groupInfoList);
        return data;
    }

    private static GroupProductResponse.GroupProductVo.GroupInfo.ProductInfo.Builder getProductBuilder(GroupProductVo.ProductInfo product) {
        GroupProductResponse.GroupProductVo.GroupInfo.ProductInfo.Builder productBuilder = GroupProductResponse.GroupProductVo.GroupInfo.ProductInfo.newBuilder();
        Optional.ofNullable(product.getId()).ifPresent(s -> productBuilder.setId(Int64Value.of(s)));
        Optional.ofNullable(product.getAsin()).ifPresent(productBuilder::setAsin);
        Optional.ofNullable(product.getSku()).ifPresent(productBuilder::setSku);
        Optional.ofNullable(product.getMainImage()).ifPresent(productBuilder::setMainImage);
        Optional.ofNullable(product.getTitle()).ifPresent(productBuilder::setTitle);
        Optional.ofNullable(product.getOnlineStatus()).ifPresent(productBuilder::setOnlineStatus);
        Optional.ofNullable(product.getMarketplaceId()).ifPresent(productBuilder::setMarketplaceId);
        Optional.ofNullable(product.getMarketplaceId()).ifPresent(s -> productBuilder.setDomain(AmznEndpoint.getByMarketplaceId(s).getDomain()));
        Optional.ofNullable(product.getExist()).ifPresent(productBuilder::setExist);
        return productBuilder;
    }
}
