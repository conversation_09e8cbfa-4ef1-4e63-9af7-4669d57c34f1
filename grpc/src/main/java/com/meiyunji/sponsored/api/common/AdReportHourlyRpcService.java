package com.meiyunji.sponsored.api.common;

import com.google.common.collect.Lists;
import com.google.protobuf.Int32Value;
import com.meiyunji.amazon.mws.base.AmznEndpoint;
import com.meiyunji.sellfox.ams.api.service.AdReportHourlyAMSApiServiceGrpc;
import com.meiyunji.sellfox.ams.api.service.AdReportHourlyRequestPb;
import com.meiyunji.sellfox.ams.api.service.AdReportHourlyResponsePb;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.enums.OrderTypeEnum;
import com.meiyunji.sponsored.common.exception.SponsoredBizException;
import com.meiyunji.sponsored.common.util.DataFormatUtil;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.common.util.MathUtil;
import com.meiyunji.sponsored.grpc.common.AdHourReportRequest;
import com.meiyunji.sponsored.grpc.common.AdHourReportResponsePb;
import com.meiyunji.sponsored.grpc.common.AdReportHourlyAnalysisApiServiceGrpc;
import com.meiyunji.sponsored.grpc.common.NeTargetReportAnalysisRequest;
import com.meiyunji.sponsored.grpc.entry.ReportDateModelPb;
import com.meiyunji.sponsored.rpc.adAggregateHour.AdPageBasicData;
import com.meiyunji.sponsored.rpc.export.UrlResponse;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdCampaignAllDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonMarketingStreamDataDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonSdAdGroupDao;
import com.meiyunji.sponsored.service.cpc.dto.CampaignHourlyReportSelectDto;
import com.meiyunji.sponsored.service.cpc.dto.NeTargetReportDataDto;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdCampaignAll;
import com.meiyunji.sponsored.service.cpc.po.AmazonMarketingStreamData;
import com.meiyunji.sponsored.service.cpc.service2.IAdHourlyReportAnalysisService;
import com.meiyunji.sponsored.service.cpc.service2.sp.ICpcAdGroupService;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.util.SplitDateByWeekUtil;
import com.meiyunji.sponsored.service.doris.dao.IOdsAmazonAdNeKeywordDao;
import com.meiyunji.sponsored.service.enums.NetargetTypeTableEnum;
import com.meiyunji.sponsored.service.excel.excelTools.WriteHandlerBuild;
import com.meiyunji.sponsored.service.excel.excelTools.service.IExcelService;
import com.meiyunji.sponsored.service.export.util.ExportStringUtil;
import com.meiyunji.sponsored.service.reportHour.service.IAmazonAdCampaignHourReportService;
import com.meiyunji.sponsored.service.reportHour.utils.ReportChartUtil;
import com.meiyunji.sponsored.service.reportHour.vo.AdNetargetExportVo;
import com.meiyunji.sponsored.service.reportHour.vo.AdReportHourlyVO;
import io.grpc.stub.StreamObserver;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.lognet.springboot.grpc.GRpcService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * @author: ys
 * @date: 2023/12/21 21:21
 * @describe:
 */
@GRpcService
@Slf4j
public class AdReportHourlyRpcService extends AdReportHourlyAnalysisApiServiceGrpc.AdReportHourlyAnalysisApiServiceImplBase {

    @Autowired
    private IAdHourlyReportAnalysisService adHourlyReportAnalysisService;

    @Qualifier("adHourlyFeedBlockingStub")
    @Autowired
    private AdReportHourlyAMSApiServiceGrpc.AdReportHourlyAMSApiServiceBlockingStub adHourlyFeedBlockingStub;

    @Autowired
    private IAmazonAdCampaignHourReportService amazonAdCampaignHourReportService;

    @Autowired
    private ICpcAdGroupService cpcAdGroupService;
    @Autowired
    private IAmazonAdCampaignAllDao amazonAdCampaignAllDao;
    @Autowired
    private IAmazonSdAdGroupDao amazonSdAdGroupDao;
    @Autowired
    private IAmazonMarketingStreamDataDao amazonMarketingStreamDataDao;
    @Autowired
    private IOdsAmazonAdNeKeywordDao odsAmazonAdNeKeywordDao;
    @Autowired
    private IExcelService excelService;
    @Autowired
    private IScVcShopAuthDao shopAuthDao;


    @Override
    public void getCampaignHourReport(AdHourReportRequest request, StreamObserver<AdHourReportResponsePb.AdHourReportResponse> responseObserver) {
        AdPageBasicData pageInfo = request.getPageBasic();
        int puid = Optional.of(pageInfo.getPuid()).map(Int32Value::getValue).orElse(null);
        String campaignType = Constants.SP;
        String costType = Constants.SD_REPORT_CPC;
        AmazonAdCampaignAll amazonAdCampaignAll = amazonAdCampaignAllDao.getByCampaignId(puid, pageInfo.getShopId().getValue(), request.getCampaignId());
        if (amazonAdCampaignAll != null) {
            campaignType = amazonAdCampaignAll.getType();
            if (StringUtils.isNotBlank(amazonAdCampaignAll.getCostType())) {
                costType = amazonAdCampaignAll.getCostType();
            }
        }
        //获取天维度的报告sup
        String finalCampaignType = campaignType;
        Supplier<List<AdReportHourlyVO>> sup = () -> amazonAdCampaignHourReportService.getAdCampaignDailyReports(puid, request, finalCampaignType, false);
        Supplier<List<AdReportHourlyVO>> compareSup = () -> amazonAdCampaignHourReportService.getAdCampaignDailyReports(puid, request, finalCampaignType, true);

        if (StringUtils.isNotBlank(request.getFindType()) && StringUtils.isNotBlank(request.getFindValue())) {
            //商品透视分析需要单独处理
            this.getAdReportHourlyDataFromDoris(request, responseObserver,
                    x -> amazonMarketingStreamDataDao.statisticsProductPerspectiveCampaignHourlyReport(x), sup, compareSup,finalCampaignType, costType);
        } else {
            this.getAdReportHourlyDataFromDoris(request, responseObserver,
                    x -> amazonMarketingStreamDataDao.campaignStatisticsByHour(x), sup, compareSup,finalCampaignType, costType);
        }
    }

    @Override
    public void getGroupHourReport(AdHourReportRequest request, StreamObserver<AdHourReportResponsePb.AdHourReportResponse> responseObserver) {
        log.info("请求广告组小时级数据，请求参数：{}，", request);
        AdPageBasicData pageInfo = request.getPageBasic();
        int puid = Optional.of(pageInfo.getPuid()).map(Int32Value::getValue).orElse(null);
        String campaignType = StringUtils.isNotBlank(request.getPageBasic().getType()) ? request.getPageBasic().getType() : Constants.SP;
        String costType = Constants.SD_REPORT_CPC;
        if (Constants.SD.equalsIgnoreCase(campaignType) && StringUtils.isNotBlank(request.getGroupId())) {
            List<String> adCampaignIdsByGroupIds = amazonSdAdGroupDao.getAdCampaignIdsByGroupIds(puid, request.getPageBasic().getShopId().getValue(), null, Lists.newArrayList(request.getGroupId()));
            if (CollectionUtils.isNotEmpty(adCampaignIdsByGroupIds) && StringUtils.isNotBlank(adCampaignIdsByGroupIds.get(0))) {
                AmazonAdCampaignAll amazonAdCampaignAll = amazonAdCampaignAllDao.getByCampaignId(puid, pageInfo.getShopId().getValue(), adCampaignIdsByGroupIds.get(0));
                if (amazonAdCampaignAll != null && StringUtils.isNotBlank(amazonAdCampaignAll.getCostType())) {
                    costType = amazonAdCampaignAll.getCostType();
                }
            }
        }
        this.getAdReportHourlyDataFromDoris(request, responseObserver, x -> amazonMarketingStreamDataDao.groupStatisticsByHour(x),
                () -> cpcAdGroupService.getAdGroupDailyReport(puid, request, false), () -> cpcAdGroupService.getAdGroupDailyReport(puid, request, true), campaignType, costType);
    }

    @Override
    public void getGroupHourExport(AdHourReportRequest request, StreamObserver<UrlResponse> responseObserver) {
        super.getGroupHourExport(request, responseObserver);
    }

    @Override
    public void getNeTargetReportAnalysis(NeTargetReportAnalysisRequest request, StreamObserver<AdHourReportResponsePb.NeTargetAnalysisResponse> responseObserver) {
        //1，日志
        log.info("getNeTargetReportAnalysis request:{}", request);

        //2,参数校验
        paramCheck(request, false);

        //3,查询数据并返回
        AdHourReportResponsePb.NeTargetAnalysisResponse.Builder respBuilder = AdHourReportResponsePb.NeTargetAnalysisResponse.newBuilder();
        AdHourReportResponsePb.NeTargetAnalysisResponse.AnalysisData adHourReportResponse = getAdHourReportResponse(request);
        respBuilder.setData(adHourReportResponse);
        responseObserver.onNext(respBuilder.setCode(Result.SUCCESS).build());
        responseObserver.onCompleted();

    }

    @Override
    public void exportNetargetAnalysisData(NeTargetReportAnalysisRequest request, StreamObserver<UrlResponse> responseObserver) {
        log.info("否定投放分析数据导出 request {}", request);
        UrlResponse.Builder urlBuilder = UrlResponse.newBuilder();
        paramCheck(request, true);
        //3,查询数据
        AdHourReportResponsePb.NeTargetAnalysisResponse.AnalysisData adHourReportResponse = getAdHourReportResponse(request);
        String startDate = request.getStartDate();
        String endDate = request.getEndDate();
        String orderField = request.getOrderField();
        String orderType = request.getOrderType();

        ShopAuth shop = shopAuthDao.getScAndVcById(request.getShopId());
        //站点币种
        String currency = AmznEndpoint.getByMarketplaceId(shop.getMarketplaceId()).getCurrencyCode().value();

        //查询targetId对应的报告数据，筛选条件：①时间 ②广告类型 ③puid ④shopId ；
        NetargetTypeTableEnum neTargetType = NetargetTypeTableEnum.getNetargetTypeTableEnumByType(request.getAdType());
        List<NeTargetReportDataDto> data = odsAmazonAdNeKeywordDao.getNetargetAnalysisList(request.getPuid(), request.getShopId(), request.getTargetId(), neTargetType, startDate, endDate);

        List<AdHourReportResponsePb.AdReportHourRpcVo> list;
        if (CollectionUtils.isEmpty(data)) {
            list = builderEmptyAnalysisRespByModel(startDate, endDate, request.getDateModel()).getListList();
        } else {
            list = getAdNetargetReportVoList(request.getDateModel(), data, startDate, endDate, orderField, orderType);
        }

        //AdReportHourRpcVoLIst to AdNetargetExportVoLIst
        List<AdNetargetExportVo> voList = list.stream().map(x -> {
            AdNetargetExportVo adNetargetExportVo = new AdNetargetExportVo();
            adNetargetExportVo.setTitle(x.getTitle());
            adNetargetExportVo.setAcos(ExportStringUtil.formatToNumber(x.getAcos()) + "%");
            adNetargetExportVo.setAdOrderNum(x.getAdOrderNum());
            adNetargetExportVo.setClicks((int)x.getClicks());
            adNetargetExportVo.setImpressions((int)x.getImpressions());
            adNetargetExportVo.setAdCost(currency + ExportStringUtil.formatToNumber(x.getAdCost()));
            adNetargetExportVo.setAdSale(currency + ExportStringUtil.formatToNumber(x.getAdSale()));
            adNetargetExportVo.setCtr(ExportStringUtil.formatToNumber(x.getCtr()) + "%");
            adNetargetExportVo.setCvr(ExportStringUtil.formatToNumber(x.getCvr()) + "%");
            adNetargetExportVo.setRoas(ExportStringUtil.formatToNumber(x.getRoas()));
            adNetargetExportVo.setCpc(ExportStringUtil.formatToNumber(x.getAdCostPerClick()));
            adNetargetExportVo.setCpa(ExportStringUtil.formatToNumber(x.getCpa()));
            return adNetargetExportVo;
        }).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(voList)) {
            urlBuilder.setCode(Int32Value.of(Result.ERROR));
            urlBuilder.setMsg("process.msg.sync.none");
            responseObserver.onNext(urlBuilder.build());
            responseObserver.onCompleted();
            return;
        }
        List<String> voExcludeFileds = new ArrayList<>();

        try {
            //excel币种表头渲染
            WriteHandlerBuild build = new WriteHandlerBuild().rate();
            String downloadUrl = excelService.easyExcelHandlerExport(request.getPuid(), voList, request.getFileName() + "(" + 1 + ")", AdNetargetExportVo.class, build.currencyNew(AdNetargetExportVo.class), voExcludeFileds);
            urlBuilder.addAllUrls(Collections.singletonList(downloadUrl));
            urlBuilder.setCode(Int32Value.of(Result.SUCCESS));
        } catch (Exception e) {
            log.error("puid{}, shopId{}否定投放分析数据导出异常:{}", request.getPuid(), request.getShopId(), e);
            urlBuilder.setCode(Int32Value.of(Result.ERROR));
            urlBuilder.setMsg("process.msg.sync.fail");
        } finally {
            responseObserver.onNext(urlBuilder.build());
            responseObserver.onCompleted();
        }
    }

    private AdHourReportResponsePb.NeTargetAnalysisResponse.AnalysisData getAdHourReportResponse(NeTargetReportAnalysisRequest request) {
        String startDate = request.getStartDate();
        String endDate = request.getEndDate();
        String orderField = request.getOrderField();
        String orderType = request.getOrderType();

        //查询targetId对应的报告数据，筛选条件：①时间 ②广告类型 ③puid ④shopId ；
        NetargetTypeTableEnum neTargetType = NetargetTypeTableEnum.getNetargetTypeTableEnumByType(request.getAdType());
        List<NeTargetReportDataDto> data = odsAmazonAdNeKeywordDao.getNetargetAnalysisList(request.getPuid(), request.getShopId(), request.getTargetId(), neTargetType, startDate, endDate);
        //data空，则构建空数据返回给前端
        if (CollectionUtils.isEmpty(data)) {
            return builderEmptyAnalysisData(startDate, endDate);
        }

        AdHourReportResponsePb.NeTargetAnalysisResponse.AnalysisData.Builder resp = AdHourReportResponsePb.NeTargetAnalysisResponse.AnalysisData.newBuilder();

        //汇总数据，日周月是一样的
        AdHourReportResponsePb.AdReportHourRpcVo.Builder summary = AdHourReportResponsePb.AdReportHourRpcVo.newBuilder();
        fillSummaryData(data, summary);

        resp.setDay(builderAnalysisRespByModel(startDate, endDate, orderField, orderType, data, summary, ReportDateModelPb.ReportDateModel.DAILY));
        resp.setWeek(builderAnalysisRespByModel(startDate, endDate, orderField, orderType, data, summary, ReportDateModelPb.ReportDateModel.WEEKLY));
        resp.setMonth(builderAnalysisRespByModel(startDate, endDate, orderField, orderType, data, summary, ReportDateModelPb.ReportDateModel.MONTHLY));

        return resp.build();
    }

    /**
     * 构建空AnalysisData
     * @param startDate 开始时间 yyyy-MM-dd
     * @param endDate 结束时间 yyyy-MM-dd
     * @return AdHourReportResponsePb.NeTargetAnalysisResponse.AnalysisData
     */
    private AdHourReportResponsePb.NeTargetAnalysisResponse.AnalysisData builderEmptyAnalysisData(String startDate, String endDate) {
        AdHourReportResponsePb.NeTargetAnalysisResponse.AnalysisData.Builder resp = AdHourReportResponsePb.NeTargetAnalysisResponse.AnalysisData.newBuilder();
        resp.setDay(builderEmptyAnalysisRespByModel(startDate, endDate, ReportDateModelPb.ReportDateModel.DAILY));
        resp.setWeek(builderEmptyAnalysisRespByModel(startDate, endDate, ReportDateModelPb.ReportDateModel.WEEKLY));
        resp.setMonth(builderEmptyAnalysisRespByModel(startDate, endDate, ReportDateModelPb.ReportDateModel.MONTHLY));
        return resp.build();
    }

    /**
     * 根据dateModel构建空AnalysisResp
     * @param startDate 开始时间 yyyy-MM-dd
     * @param endDate 开始时间 yyyy-MM-dd
     * @param dateModel ReportDateModelPb.ReportDateModel
     * @return AdHourReportResponsePb.NeTargetAnalysisResponse.AnalysisResp
     */
    private AdHourReportResponsePb.NeTargetAnalysisResponse.AnalysisResp builderEmptyAnalysisRespByModel(String startDate, String endDate, ReportDateModelPb.ReportDateModel dateModel) {
        AdHourReportResponsePb.NeTargetAnalysisResponse.AnalysisResp.Builder analusisRespBuilder = AdHourReportResponsePb.NeTargetAnalysisResponse.AnalysisResp.newBuilder();
        AdHourReportResponsePb.AdReportHourRpcVo.Builder summary = AdHourReportResponsePb.AdReportHourRpcVo.newBuilder();
        analusisRespBuilder.setSummary(summary);

        NeTargetReportDataDto dto = new NeTargetReportDataDto();
        dto.setTargetId("");
        dto.setCost(new BigDecimal("0"));
        dto.setTotalSales(new BigDecimal("0"));
        dto.setImpressions(0);
        dto.setClicks(0);
        dto.setAdOrderNum(0);
        dto.setCountDay(DateUtil.strToDate(startDate, "yyyy-MM-dd"));
        dto.setCountMonth(Integer.valueOf(DateUtil.dateToStrWithFormat(DateUtil.strToDate(startDate, "yyyy-MM-dd"), "yyyyMM")));
        dto.setCountDate(DateUtil.dateToStrWithFormat(DateUtil.strToDate(startDate, "yyyy-MM-dd"), "yyyyMMdd"));

        List<AdHourReportResponsePb.AdReportHourRpcVo> list = getAdNetargetReportVoList(dateModel, Collections.singletonList(dto), startDate, endDate, null, null);
        analusisRespBuilder.addAllList(list);
        analusisRespBuilder.addAllChart(ReportChartUtil.getNetargetAnalysisChartData(list, false));
        return analusisRespBuilder.build();
    }


    private AdHourReportResponsePb.NeTargetAnalysisResponse.AnalysisResp builderAnalysisRespByModel(String startDate, String endDate, String orderField, String orderType, List<NeTargetReportDataDto> data, AdHourReportResponsePb.AdReportHourRpcVo.Builder summary, ReportDateModelPb.ReportDateModel model) {
        AdHourReportResponsePb.NeTargetAnalysisResponse.AnalysisResp.Builder analusisRespBuilder = AdHourReportResponsePb.NeTargetAnalysisResponse.AnalysisResp.newBuilder();
        analusisRespBuilder.setSummary(summary);
        List<AdHourReportResponsePb.AdReportHourRpcVo> list = getAdNetargetReportVoList(model, data, startDate, endDate, orderField, orderType);
        analusisRespBuilder.addAllList(list);
        analusisRespBuilder.addAllChart(ReportChartUtil.getNetargetAnalysisChartData(list, false));
        return analusisRespBuilder.build();
    }

    /**
     * 获取
     * @param dateModel
     * @param data
     * @param startDate
     * @param endDate
     * @return
     */
   List<AdHourReportResponsePb.AdReportHourRpcVo> getAdNetargetReportVoList(ReportDateModelPb.ReportDateModel dateModel, List<NeTargetReportDataDto> data, String startDate, String endDate, String orderField, String orderType){
       List<AdHourReportResponsePb.AdReportHourRpcVo> list = new ArrayList<>(data.size());
       if (dateModel == ReportDateModelPb.ReportDateModel.DAILY) {
           for (NeTargetReportDataDto dto : data) {
               AdHourReportResponsePb.AdReportHourRpcVo.Builder vo = AdHourReportResponsePb.AdReportHourRpcVo.newBuilder();
               fillDailyData(vo, dto);
               list.add(vo.build());
           }
       } else if (dateModel == ReportDateModelPb.ReportDateModel.WEEKLY) {
           fillWeeklyData(list, data, startDate, endDate);
       } else if (dateModel == ReportDateModelPb.ReportDateModel.MONTHLY) {
           fillMonthlyData(list, data);
       }
       //排序
       if ( StringUtils.isNotBlank(orderField) && StringUtils.isNotBlank(orderType)) {
           Comparator<AdHourReportResponsePb.AdReportHourRpcVo> comparator ;
           switch (orderField) {
               case "acos":
                   comparator = Comparator.comparing((a)-> new BigDecimal(a.getAcos()));
                   break;
               case "adOrderNum":
                   comparator =Comparator.comparing(AdHourReportResponsePb.AdReportHourRpcVo::getAdOrderNum);
                   break;
               case "clicks":
                   comparator = Comparator.comparing(AdHourReportResponsePb.AdReportHourRpcVo::getClicks);
                   break;
               case "impressions":
                   comparator = Comparator.comparing(AdHourReportResponsePb.AdReportHourRpcVo::getImpressions);
                   break;
               case "adCost":
                   comparator = Comparator.comparing((a)-> new BigDecimal(a.getAdCost()));
                   break;
               case "adSale":
                   comparator = Comparator.comparing((a)-> new BigDecimal(a.getAdSale()));
                   break;
               case "ctr":
                   comparator = Comparator.comparing((a)-> new BigDecimal(a.getCtr()));
                   break;
               case "cvr":
                   comparator = Comparator.comparing((a)-> new BigDecimal(a.getCvr()));
                   break;
               case "roas":
                   comparator = Comparator.comparing((a)-> new BigDecimal(a.getRoas()));
                   break;
               case "adCostPerClick":
                   comparator = Comparator.comparing((a)-> new BigDecimal(a.getAdCostPerClick()));
                   break;
               case "cpa":
                   comparator = Comparator.comparing((a)-> new BigDecimal(a.getCpa()));
                   break;
               default:
                   comparator = Comparator.comparing(AdHourReportResponsePb.AdReportHourRpcVo::getTitle);
           }
           // 根据 desc 确定是否需要反转排序
           if (OrderTypeEnum.desc.getType().equals(orderType)) {
               comparator = comparator.reversed();
           }
           list.sort(comparator);
       }
       return list;
   }

    private void fillMonthlyData(List<AdHourReportResponsePb.AdReportHourRpcVo> list, List<NeTargetReportDataDto> dtoList) {
        Map<String, List<NeTargetReportDataDto>> monthDataMap = dtoList.stream().collect(Collectors.groupingBy(item -> DateUtil.dateToStrWithFormat(DateUtil.strToDate(item.getCountDate(), DateUtil.PATTERN_YYYYMMDD), DateUtil.PATTERN_MONTH)));
        List<String> dateList = dtoList.stream().filter(Objects::nonNull).map(item -> DateUtil.dateToStrWithFormat(DateUtil.strToDate(item.getCountDate(), DateUtil.PATTERN_YYYYMMDD), DateUtil.PATTERN_MONTH)).distinct().sorted().collect(Collectors.toList());
        dateList.stream().filter(Objects::nonNull).forEach(item -> {
            List<NeTargetReportDataDto> neTargetReportDataDtos = monthDataMap.get(item);
            AdHourReportResponsePb.AdReportHourRpcVo.Builder builder = AdHourReportResponsePb.AdReportHourRpcVo.newBuilder();
            builder.setTitle(item);
            fillSummaryData(neTargetReportDataDtos, builder);
            list.add(builder.build());
        });
    }

    private void fillWeeklyData(List<AdHourReportResponsePb.AdReportHourRpcVo> list, List<NeTargetReportDataDto> dtoList, String startDate, String endDate) {
        if (CollectionUtils.isEmpty(dtoList)) {
            return;
        }
        List<List<String>> weekDate = SplitDateByWeekUtil.split(DateUtil.stringToDate(startDate), DateUtil.stringToDate(endDate));

        weekDate.stream().filter(Objects::nonNull).forEach(week -> {
            String weekDay = String.join("~", week);
            List<NeTargetReportDataDto> singleWeekData = dtoList.stream().filter(Objects::nonNull).
                filter(item -> DateUtil.compareDate(DateUtil.strToDate(item.getCountDate(), DateUtil.PATTERN_YYYYMMDD), DateUtil.strToDate4(week.get(0))) >= 0
                    && DateUtil.compareDate(DateUtil.strToDate(item.getCountDate(), DateUtil.PATTERN_YYYYMMDD), DateUtil.strToDate4(week.get(1))) <= 0)
                .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(singleWeekData)) {
                return;
            }
            AdHourReportResponsePb.AdReportHourRpcVo.Builder builder = AdHourReportResponsePb.AdReportHourRpcVo.newBuilder();
            builder.setTitle(weekDay);
            fillSummaryData(singleWeekData, builder);
            list.add(builder.build());
        });

    }
    private void fillDailyData(AdHourReportResponsePb.AdReportHourRpcVo.Builder builder, NeTargetReportDataDto dto) {
        if (dto == null) {
            return;
        }
        builder.setTitle(DataFormatUtil.dataFormart(dto.getCountDate(), DateUtil.PATTERN_YYYYMMDD, DateUtil.PATTERN));
        if (dto.getTotalSales()!= null && dto.getCost() != null) {
            builder.setAcos(String.valueOf((dto.getTotalSales().compareTo(BigDecimal.ZERO) == 0 || dto.getCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : dto.getCost().multiply(new BigDecimal("100")).divide(dto.getTotalSales(), 4, RoundingMode.HALF_UP))));
        }
        if (dto.getAdOrderNum() != null) {
            builder.setAdOrderNum(dto.getAdOrderNum());
        }
        if (dto.getClicks() != null) {
            builder.setClicks(dto.getClicks());
        }
        if (dto.getImpressions() != null) {
            builder.setImpressions(dto.getImpressions());
        }
        if (dto.getCost() != null) {
            builder.setAdCost(String.valueOf(dto.getCost()));
        }
        if (dto.getTotalSales()!= null) {
            builder.setAdSale(String.valueOf(dto.getTotalSales()));
        }
        if (dto.getClicks() != null && dto.getImpressions() != null) {
            builder.setCtr(String.valueOf(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(dto.getClicks()), BigDecimal.valueOf(100)), BigDecimal.valueOf(dto.getImpressions()))));
        }
        if (dto.getAdOrderNum() != null && dto.getClicks() != null) {
            builder.setCvr(String.valueOf(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(dto.getAdOrderNum()), BigDecimal.valueOf(100)), BigDecimal.valueOf(dto.getClicks()))));
        }
        if (dto.getTotalSales() != null && dto.getCost() != null) {
            builder.setRoas(String.valueOf((dto.getTotalSales().compareTo(BigDecimal.ZERO) == 0 || dto.getCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : dto.getTotalSales().divide(dto.getCost(), 4, RoundingMode.HALF_UP))));
        }
        if (dto.getCost()!= null && dto.getClicks() != null) {
            builder.setAdCostPerClick(String.valueOf((dto.getCost().compareTo(BigDecimal.ZERO) == 0 || dto.getClicks() == 0 ? BigDecimal.ZERO : dto.getCost().divide(new BigDecimal(dto.getClicks()), 4, RoundingMode.HALF_UP))));
        }
        if (dto.getCost()!= null && dto.getAdOrderNum() != null) {
            builder.setCpa(String.valueOf((dto.getCost().compareTo(BigDecimal.ZERO) == 0 || dto.getAdOrderNum() == 0 ? BigDecimal.ZERO : dto.getCost().divide(new BigDecimal(dto.getAdOrderNum()), 4, RoundingMode.HALF_UP))));
        }
    }

    private void fillSummaryData(List<NeTargetReportDataDto> data, AdHourReportResponsePb.AdReportHourRpcVo.Builder builder) {
        if (CollectionUtils.isEmpty(data)) {
            return;
        }
        BigDecimal totalSales = data.stream().filter(dto -> dto != null && dto.getTotalSales() != null).map(NeTargetReportDataDto::getTotalSales).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal cost = data.stream().filter(dto -> dto != null && dto.getCost() != null).map(NeTargetReportDataDto::getCost).reduce(BigDecimal.ZERO, BigDecimal::add);
        builder.setAcos(String.valueOf(totalSales.compareTo(BigDecimal.ZERO) == 0 || cost.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : cost.multiply(new BigDecimal("100")).divide(totalSales, 4, RoundingMode.HALF_UP)));
        int adOrderNum = data.stream().filter(dto -> dto != null && dto.getAdOrderNum() != null).mapToInt(NeTargetReportDataDto::getAdOrderNum).sum();
        builder.setAdOrderNum(adOrderNum);
        int clicks = data.stream().filter(dto -> dto != null && dto.getClicks() != null).mapToInt(NeTargetReportDataDto::getClicks).sum();
        builder.setClicks(clicks);
        int impressions = data.stream().filter(dto -> dto != null && dto.getImpressions() != null).mapToInt(NeTargetReportDataDto::getImpressions).sum();
        builder.setImpressions(impressions);
        builder.setAdCost(String.valueOf(cost));
        builder.setAdSale(String.valueOf(totalSales));
        builder.setCtr(String.valueOf(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(clicks), BigDecimal.valueOf(100)), BigDecimal.valueOf(impressions))));
        builder.setCvr(String.valueOf(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(adOrderNum), BigDecimal.valueOf(100)), BigDecimal.valueOf(clicks))));
        builder.setRoas(String.valueOf((totalSales.compareTo(BigDecimal.ZERO) == 0 || cost.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : totalSales.divide(cost, 4, RoundingMode.HALF_UP))));
        builder.setAdCostPerClick(String.valueOf((cost.compareTo(BigDecimal.ZERO) == 0 || clicks == 0 ? BigDecimal.ZERO : cost.divide(new BigDecimal(clicks), 4, RoundingMode.HALF_UP))));
        builder.setCpa(String.valueOf((cost.compareTo(BigDecimal.ZERO) == 0 || adOrderNum == 0 ? BigDecimal.ZERO : cost.divide(new BigDecimal(adOrderNum), 4, RoundingMode.HALF_UP))));
    }

    private void paramCheck(NeTargetReportAnalysisRequest param, boolean export) {
        if (!param.hasPuid()) {
            throw new SponsoredBizException("puid不能为空");
        }
        if (!param.hasShopId()) {
            throw new SponsoredBizException("shopId不能为空");
        }
        if (!param.hasAdType() || NetargetTypeTableEnum.getNetargetTypeTableEnumByType(param.getAdType()) == null) {
            throw new SponsoredBizException("adType不能为空");
        }
        if (export && (!param.hasDateModel() ||
            (!ReportDateModelPb.ReportDateModel.DAILY.equals(param.getDateModel())
                && !ReportDateModelPb.ReportDateModel.WEEKLY.equals(param.getDateModel())
                && !ReportDateModelPb.ReportDateModel.MONTHLY.equals(param.getDateModel())))) {
            throw new SponsoredBizException("dateModel error");
        }
        if (!param.hasTargetId()) {
            throw new SponsoredBizException("targetId不能为空");
        }
        if (!param.hasStartDate()) {
            throw new SponsoredBizException("startDate不能为空");
        }
        if (!param.hasEndDate()) {
            throw new SponsoredBizException("endDate不能为空");
        }
        if (!param.hasDateModel()) {
            throw new SponsoredBizException("dateModel不能为空");
        }
    }

    @Deprecated
    private void getAdReportHourlyData(AdHourReportRequest request,
                                       StreamObserver<AdHourReportResponsePb.AdHourReportResponse> responseObserver,
                                       Function<AdReportHourlyRequestPb.AdReportHourlyRequest, AdReportHourlyResponsePb.AdReportHourlyResponse> func,
                                       Supplier<List<AdReportHourlyVO>> sup, String campaignType, String costType) {
        log.info("ad report hourly report analysis request:{}", request);
        AdHourReportResponsePb.AdHourReportResponse.Builder builder = AdHourReportResponsePb.AdHourReportResponse.newBuilder();
        AdPageBasicData pageBasicData = request.getPageBasic();
        //检查参数
        if (!paramCheck(pageBasicData)) {
            builder.setCode(Result.ERROR);
            builder.setMsg("request param error");
        } else {
            AdHourReportResponsePb.AdHourReportResponse.AdHour hourlyReport =
                    adHourlyReportAnalysisService.getAdHourlyReport(request, func, sup, campaignType, costType);
            if (hourlyReport != null) {
                builder.setData(hourlyReport);
            }
            builder.setCode(Result.SUCCESS);
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    private void getAdReportHourlyDataFromDoris(AdHourReportRequest request,
                                                StreamObserver<AdHourReportResponsePb.AdHourReportResponse> responseObserver,
                                                Function<CampaignHourlyReportSelectDto, List<AmazonMarketingStreamData>> func,
                                                Supplier<List<AdReportHourlyVO>> sup, Supplier<List<AdReportHourlyVO>> compareSup, String campaignType, String costType) {
        log.info("ad report hourly report analysis request:{}", request);
        AdHourReportResponsePb.AdHourReportResponse.Builder builder = AdHourReportResponsePb.AdHourReportResponse.newBuilder();
        AdPageBasicData pageBasicData = request.getPageBasic();
        //检查参数
        if (!paramCheck(pageBasicData)) {
            builder.setCode(Result.ERROR);
            builder.setMsg("request param error");
        } else {
            AdHourReportResponsePb.AdHourReportResponse.AdHour hourlyReport =
                    adHourlyReportAnalysisService.getAdHourlyReportFromDoris(request, func, sup, compareSup, campaignType, costType);
            if (hourlyReport != null) {
                builder.setData(hourlyReport);
            }
            builder.setCode(Result.SUCCESS);
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    private boolean paramCheck(AdPageBasicData pageBasicData) {
        if (!pageBasicData.hasPuid() || !pageBasicData.hasShopId() || !pageBasicData.hasEndDate() || !pageBasicData.hasStartDate()
                || (pageBasicData.hasIsCompare() && pageBasicData.getIsCompare().getValue() == 1 &&
                (!pageBasicData.hasStartDateCompare() || !pageBasicData.hasEndDateCompare()))) {
            return false;
        }
        return true;
    }
}
