package com.meiyunji.sponsored.api.autorule;

import com.google.api.client.util.Lists;
import com.google.protobuf.Int32Value;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.rpc.autorule.task.*;
import com.meiyunji.sponsored.rpc.vo.CommonResponse;
import com.meiyunji.sponsored.service.autoRuleTask.bo.TemplateTaskHourglassBo;
import com.meiyunji.sponsored.service.autoRuleTask.po.AutoRuleTask;
import com.meiyunji.sponsored.service.autoRuleTask.po.AutoRuleTaskRecord;
import com.meiyunji.sponsored.service.autoRuleTask.service.AutoRuleTaskService;
import com.meiyunji.sponsored.service.autoRuleTask.vo.ProcessTaskParam;
import com.meiyunji.sponsored.service.autoRuleTask.vo.QueryTaskHourglassParam;
import com.meiyunji.sponsored.service.autoRuleTask.vo.StatusVo;
import io.grpc.stub.StreamObserver;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.lognet.springboot.grpc.GRpcService;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * @author: chenzimeng
 * @email: <EMAIL>
 * @date: 2024-01-18  09:42
 */
@GRpcService
@Slf4j
public class AutoRuleTaskRpcService extends RPCAutoRuleTaskServiceGrpc.RPCAutoRuleTaskServiceImplBase{

    @Autowired
    private AutoRuleTaskService autoRuleTaskService;

    @Override
    public void queryTaskHourglass(QueryTaskHourglassRequest request, StreamObserver<QueryTaskHourglassResponse> responseObserver) {
        log.info("自动化规则受控对象页面查询任务沙漏接口 queryTaskHourglass request:{}", request);
        QueryTaskHourglassResponse.Builder response = QueryTaskHourglassResponse.newBuilder();
        QueryTaskHourglassParam param = new QueryTaskHourglassParam();
        param.setPuid(request.getPuid());
        param.setShopId(request.getShopId());
        param.setTemplateId(request.getTemplateId());
        Result<List<AutoRuleTask>> result = autoRuleTaskService.queryTaskHourglass(param);
        if (result.getCode() == Result.SUCCESS) {
            if (CollectionUtils.isNotEmpty(result.getData())) {
                for (AutoRuleTask task : result.getData()) {
                    QueryTaskHourglass.Builder builder = QueryTaskHourglass.newBuilder();
                    builder.setPuid(task.getPuid());
                    builder.setShopId(task.getShopId());
                    builder.setCount(task.getCount());
                    builder.setState(task.getState());
                    builder.setTaskId(task.getId());
                    builder.setTaskAction(task.getTaskAction());
                    builder.setSchedule(task.getSchedule());
                    builder.setCreateTime(task.getCreateTime().format(DateTimeFormatter.ofPattern(DateUtil.PATTERN_DATE_TIME)));
                    builder.setUpdateTime(task.getUpdateTime().format(DateTimeFormatter.ofPattern(DateUtil.PATTERN_DATE_TIME)));
                    response.addData(builder.build());
                }
            }
        } else {
            response.setMsg(result.getMsg());
        }
        response.setCode(result.getCode());
        responseObserver.onNext(response.build());
        responseObserver.onCompleted();
    }

    @Override
    public void pageListByTaskId(PageListByTaskIdRequest request, StreamObserver<PageListByTaskIdResponse> responseObserver) {
        log.info("分时策略查询结果详情接口 pageListByTaskId request:{}", request);
        PageListByTaskIdResponse.Builder response = PageListByTaskIdResponse.newBuilder();
        Result<Page<AutoRuleTaskRecord>> result = autoRuleTaskService.pageListByTaskId(request.getPuid(), request.getTaskId(), request.getPageNo(), request.getPageSize());
        if (result.getCode() == Result.SUCCESS) {
            Page<AutoRuleTaskRecord> voPage = result.getData();
            PageListByTaskId.Builder pageBuilder = PageListByTaskId.newBuilder();
            pageBuilder.setPageNo(voPage.getPageNo());
            pageBuilder.setPageSize(voPage.getPageSize());
            pageBuilder.setTotalPage(voPage.getTotalPage());
            pageBuilder.setTotalSize(voPage.getTotalSize());
            if (CollectionUtils.isNotEmpty(result.getData().getRows())) {
                for (AutoRuleTaskRecord record : result.getData().getRows()) {
                    ListByTaskId.Builder builder = ListByTaskId.newBuilder();
                    builder.setPuid(record.getPuid());
                    builder.setShopId(record.getShopId());
                    builder.setItemName(record.getItemName());
                    builder.setTaskId(record.getTaskId());
                    builder.setMsg(record.getStateError());
                    builder.setCreateTime(record.getCreateTime().format(DateTimeFormatter.ofPattern(DateUtil.PATTERN_DATE_TIME)));
                    builder.setUpdateTime(record.getUpdateTime().format(DateTimeFormatter.ofPattern(DateUtil.PATTERN_DATE_TIME)));
                    pageBuilder.addRows(builder.build());
                }
            }
            response.setData(pageBuilder.build());
        } else {
            response.setMsg(result.getMsg());
        }
        response.setCode(result.getCode());
        responseObserver.onNext(response.build());
        responseObserver.onCompleted();
    }

    @Override
    public void processTask(ProcessTaskRequest request, StreamObserver<CommonResponse> responseObserver) {
        log.info("自动化规则处理任务的接口 processTask request:{}", request);
        CommonResponse.Builder response = CommonResponse.newBuilder();
        ProcessTaskParam param = new ProcessTaskParam();
        param.setPuid(request.getPuid());
        param.setShopId(request.getShopId());
        param.setOldTemplateId(request.getOldTemplateId());
        param.setNewTemplateId(request.getNewTemplateId());
        param.setTaskAction(request.getTaskAction());
        param.setLoginIp(request.getLoginIp());
        param.setUid(request.getUid());
        param.setOperation(request.getOperation());
        param.setSimilarRuleUpdate(request.getSimilarRuleUpdate());
        param.setExcludedStatusIdList(request.getExcludedStatusIdList());
        param.setPortfolioIds(request.getPortfolioIdList());
        param.setCampaignIds(request.getCampaignIdList());
        param.setGroupIds(request.getGroupIdList());
        param.setSearchValue(request.getSearchValue());
        param.setTargetType(request.getSearchField());
        param.setMatchType(request.getMatchType());
        param.setState(request.getState());
        param.setAdTypeList(request.getAdTypeList());
        param.setServingStatusList(request.getServingStatusList());
        param.setChildrenItemType(request.getChildrenItemType());
        if (StringUtils.isNotBlank(request.getBidStrategy())) {
            param.setStrategyType(request.getBidStrategy());
        }
        if (CollectionUtils.isNotEmpty(request.getStatusRpcsList())) {
            List<StatusVo> statusVoList = Lists.newArrayList();
            for (StatusRpc statusRpc : request.getStatusRpcsList()) {
                StatusVo statusVo = new StatusVo();
                statusVo.setStatusId(statusRpc.getStatusId());
                statusVo.setOriginBudgetValue(BigDecimal.valueOf(statusRpc.getOriginBudgetValue()));
                statusVo.setOriginDefaultBiddingValue(BigDecimal.valueOf(statusRpc.getOriginDefaultBiddingValue()));
                statusVo.setOriginBiddingValue(BigDecimal.valueOf(statusRpc.getOriginBiddingValue()));
                statusVo.setItemName(statusRpc.getItemName());
                statusVoList.add(statusVo);
            }
            param.setStatusVoList(statusVoList);
        }
        Result<String> result = autoRuleTaskService.processTask(param);
        response.setCode(Int32Value.of(result.getCode()));
        if (StringUtils.isNotBlank(result.getMsg())) {
            response.setMsg(result.getMsg());
        }
        responseObserver.onNext(response.build());
        responseObserver.onCompleted();
    }

    @Override
    public void queryTemplateTaskHourglass(QueryTemplateTaskHourglassRequest request, StreamObserver<QueryTemplateTaskHourglassResponse> responseObserver) {
        log.info("自动化规则模板页面查询任务沙漏接口 queryTemplateTaskHourglass request:{}", request);
        QueryTemplateTaskHourglassResponse.Builder response = QueryTemplateTaskHourglassResponse.newBuilder();
        Result<List<TemplateTaskHourglassBo>> result = autoRuleTaskService.queryTemplateTaskHourglass(request.getPuid(), request.getShopId(), request.getTemplateIdList());
        if (result.getCode() == Result.SUCCESS) {
            if (CollectionUtils.isNotEmpty(result.getData())) {
                for (TemplateTaskHourglassBo bo : result.getData()) {
                    QueryTemplateTaskHourglass.Builder builder = QueryTemplateTaskHourglass.newBuilder();
                    builder.setState(bo.getState());
                    builder.setTemplateId(bo.getTemplateId());
                    response.addData(builder.build());
                }
            }
        } else {
            response.setMsg(result.getMsg());
        }
        response.setCode(result.getCode());
        responseObserver.onNext(response.build());
        responseObserver.onCompleted();
    }
}
