package com.meiyunji.sponsored.controller.post;

import com.meiyunji.sellfox.right.annotation.CheckRight;
import com.meiyunji.sellfox.right.utils.RightContextUtil;
import com.meiyunji.sellfox.right.utils.ShopRightUtils;
import com.meiyunji.sponsored.common.base.ProcessMsg;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.base.SellfoxRightResource;
import com.meiyunji.sponsored.common.enums.SearchTypeEnum;
import com.meiyunji.sponsored.common.support.RedisService;
import com.meiyunji.sponsored.common.util.ResultUtil;
import com.meiyunji.sponsored.common.util.StringUtil;
import com.meiyunji.sponsored.service.account.service.IShopAuthService;
import com.meiyunji.sponsored.service.cpc.po.AmazonPost;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.post.request.*;
import com.meiyunji.sponsored.service.post.response.*;
import com.meiyunji.sponsored.service.post.service.IPostService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.*;
import java.util.stream.Collectors;

import static com.meiyunji.sponsored.service.cpc.util.Constants.EXPORT_MAX_SIZE;

/**
 * post controller入口
 *
 * @Author: heqiwen
 * @Date: 2025/04/01 09:51
 */
@RestController
@RequestMapping("/sellfox/posts")
@Slf4j
public class PostController {

    @Autowired
    private IPostService postService;

    @Autowired
    private IShopAuthService shopAuthService;

    @Resource
    private ShopRightUtils shopRightUtils;

    @Autowired
    private RedisService redisservice;

    /**
     * 店铺下拉查询
     * @return
     */
    @CheckRight(value = {SellfoxRightResource.MOD_AD_POST__VIEW_LIST})
    @PostMapping("/getShop")
    public Result<ShopSitesResponse> getShop() {
        int puid = RightContextUtil.getPuid().intValue();
        int uid = RightContextUtil.getUser().getId().intValue();
        log.info("帖子店铺下拉接口puid :{}",puid);
        List<Integer> shopIdList = shopAuthService.checkAuthByShopIds(puid, shopRightUtils.checkShopRight(uid, null));
        if (CollectionUtils.isEmpty(shopIdList)) {
            log.info("帖子店铺下拉接口无已授权店铺");
            return ResultUtil.error(-1, "帖子店铺下拉接口无已授权店铺");
        }
        ShopSitesResponse response = new ShopSitesResponse();
        // 只支持美国站点
        String marketplaceId = Constants.US;
        response = postService.getShopInfo(puid, marketplaceId, shopIdList);
        return ResultUtil.returnSucc(response);
    }

    
    /**
     * 列表页面 - 品牌下拉框查询接口
     */
    @CheckRight(value = {SellfoxRightResource.MOD_AD_POST__VIEW_LIST})
    @PostMapping("/multiShopGetBrands")
    public Result<GetBrandsResponse> multiShopGetBrands(@RequestBody @Valid GetBrandsRequest req) {
        int puid = RightContextUtil.getPuid().intValue();
        int uid = RightContextUtil.getUser().getId().intValue();
        req.setPuid(puid);
        log.info("帖子列表页获取品牌接口入参 param:{}",req);
        List<Integer> shopIdList = shopAuthService.checkAuthByShopIds(puid, shopRightUtils.checkShopRight(uid, req.getShopIdList()));
        GetBrandsResponse response = new GetBrandsResponse();
        if (CollectionUtils.isEmpty(shopIdList)) {
            log.info("帖子列表页获取品牌接口无已授权店铺");
            return ResultUtil.error(-1, "列表页获取品牌接口无已授权店铺");
        }
        req.setShopIdList(shopIdList);
        response = postService.multiShopGetBrands(req);
        if (Objects.isNull(response)) {
            ResultUtil.success( "无授权品牌", response);
        }
        return ResultUtil.success(response);
    }

    /**
     * 创建页面 - 品牌下拉框查询接口
     */
    @CheckRight(value = {SellfoxRightResource.MOD_AD_POST__VIEW_LIST})
    @PostMapping("/getBrands")
    public Result<GetBrandsResponse> getBrands(@RequestBody @Valid GetBrandsRequest req) {
        int puid = RightContextUtil.getPuid().intValue();
        int uid = RightContextUtil.getUser().getId().intValue();
        req.setPuid(puid);
        log.info("创建帖子下拉框查询品牌接口入参 param:{}",req);
        List<Integer> shopIdList = shopAuthService.checkAuthByShopIds(puid, shopRightUtils.checkShopRight(uid, req.getShopIdList()));
        GetBrandsResponse response = new GetBrandsResponse();
        if (CollectionUtils.isEmpty(shopIdList)) {
            log.info("创建帖子下拉框查询品牌接口无已授权店铺");
            return ResultUtil.success(response);
        }
        req.setShopIdList(shopIdList);
        response = postService.getBrands(req);
        if (Objects.isNull(response)) {
            ResultUtil.success( "无授权品牌", response);
        }
        return ResultUtil.success(response);
    }

    /**
     * 列表页面 - 创建人下拉框接口
     */
    @CheckRight(value = {SellfoxRightResource.MOD_AD_POST__VIEW_LIST})
    @PostMapping("/getUser")
    public Result<List<GetUserInfoResponse.UserInfo>> getUser(@RequestBody GetUserInfoRequest request) {
        int puid = RightContextUtil.getPuid().intValue();
        int uid = RightContextUtil.getUser().getId().intValue();
        log.info("创建人下拉框查询接口入参 param:{}",request);
        List<GetUserInfoResponse.UserInfo> response = new ArrayList<>();
        response = postService.getUser(puid, uid, request);
        return ResultUtil.success(response);
    }

    /**
     * 列表页面 - 列表查询接口
     */
    @CheckRight(value = {SellfoxRightResource.MOD_AD_POST__VIEW_LIST})
    @PostMapping("/pageList")
    public Result<GetPostsResponse> pageList(@RequestBody @Valid GetPostsRequest req) {
        int puid = RightContextUtil.getPuid().intValue();
        int uid = RightContextUtil.getUser().getId().intValue();
        req.getFilterDto().setPuid(puid);
        log.info("帖子列表页查询接口入参 param:{}",req);
        GetPostsResponse response = new GetPostsResponse();
        List<Integer> shopIdList = shopAuthService.checkAuthByShopIds(puid, shopRightUtils.checkShopRight(uid, req.getFilterDto().getShopIdList()));
        if (CollectionUtils.isEmpty(shopIdList)) {
            log.info("帖子列表页查询接口无已授权店铺");
            return ResultUtil.success(response);
        }
        req.getFilterDto().setShopIdList(shopIdList);
        // 批量精确查询
        //asin
        fillSearchValue(req);
        response = postService.pageList(req);
        return ResultUtil.success(response);
    }

    /**
     * 列表页面 - 图标汇总查询接口
     */
    @CheckRight(value = {SellfoxRightResource.MOD_AD_POST__VIEW_LIST})
    @PostMapping("/getChartData")
    public Result<GetPostsChartResponse> getChartData(@RequestBody @Valid GetPostsRequest req) {
        int puid = RightContextUtil.getPuid().intValue();
        int uid = RightContextUtil.getUser().getId().intValue();
        req.getFilterDto().setPuid(puid);
        log.info("帖子列表页图标汇总查询接口入参 param:{}",req);
        List<Integer> shopIdList = shopAuthService.checkAuthByShopIds(puid, shopRightUtils.checkShopRight(uid, req.getFilterDto().getShopIdList()));
        GetPostsChartResponse response = new GetPostsChartResponse();
        if (CollectionUtils.isEmpty(shopIdList)) {
            log.info("帖子列表页图标汇总查询接口无已授权店铺");
            return ResultUtil.success(response);
        }
        req.getFilterDto().setShopIdList(shopIdList);
        fillSearchValue(req);
        response = postService.getChartData(req);
        return ResultUtil.success(response);
    }

    /**
     * 列表页面 - 列表导出接口
     */
    @CheckRight(value = {SellfoxRightResource.MOD_AD_POST__EXPORT_DATA})
    @PostMapping("/pageExport")
    public Result<String> pageExport(@RequestBody @Valid GetPostsRequest req) {
        int puid = RightContextUtil.getPuid().intValue();
        int uid = RightContextUtil.getUser().getId().intValue();
        req.getFilterDto().setPuid(puid);
        log.info("帖子列表页导出接口入参 param:{}",req);
        List<Integer> shopIdList = shopAuthService.checkAuthByShopIds(puid, shopRightUtils.checkShopRight(uid, req.getFilterDto().getShopIdList()));
        if (CollectionUtils.isEmpty(shopIdList)) {
            log.info("帖子列表页导出接口无已授权店铺");
            return ResultUtil.success("无授权店铺");
        }
        req.getFilterDto().setShopIdList(shopIdList);
        // 限制导出条数
        req.setPageNo(1);
        req.setPageSize(EXPORT_MAX_SIZE);
        // 生成Uuid
        Result success = ResultUtil.success();
        String uuid = UUID.randomUUID().toString().replace("-", "");
        success.setData(uuid);
        ProcessMsg pm = new ProcessMsg(0, 0,"process.msg.syncing");
        redisservice.set(uuid, pm);
        req.getFilterDto().setUuid(uuid);
        req.getFilterDto().setExportUid(uid);
        fillSearchValue(req);
        return postService.pageExport(req);
    }

    /**
     * 填充列表页asin搜索框内容
     * @param req
     */
    private static void fillSearchValue(GetPostsRequest req) {
        if (StringUtils.isNotBlank(req.getFilterDto().getAsins())) {
            //asin
            List<String> values = Arrays.stream(req.getFilterDto().getAsins().split(StringUtil.SPECIAL_COMMA)).collect(Collectors.toList());
            req.getFilterDto().setAsinList(values);
        }
    }

    /**
     * 列表页 - 编辑备注接口
     */
    @CheckRight(value = {SellfoxRightResource.MOD_AD_POST__EDIT_DATA})
    @PostMapping("/updateRemark")
    public Result<String> updateRemark(@RequestBody @Valid UpdateRemarkRequest req) {
        int puid = RightContextUtil.getPuid().intValue();
        int uid = RightContextUtil.getUser().getId().intValue();
        req.setPuid(puid);
        req.setUid(uid);
        log.info("编辑备注接口入参 param:{}",req);
        List<Integer> shopIdList = shopAuthService.checkAuthByShopIds(puid, shopRightUtils.checkShopRight(uid, Arrays.asList(req.getShopId())));
        if (CollectionUtils.isEmpty(shopIdList)) {
            log.info("编辑备注接口无已授权店铺");
            return ResultUtil.success("无授权店铺");
        }
        return postService.updateRemark(req);
    }

    /**
     * 创建页面 - 建议ASIN查询接口
     */
    @CheckRight(value = {SellfoxRightResource.MOD_AD_POST__VIEW_LIST})
    @PostMapping("/getSuggestAsin")
    public Result<GetSuggestAsinResponse> getSuggestAsin(@RequestBody @Valid GetSuggestAsinRequest req) {
        int puid = RightContextUtil.getPuid().intValue();
        int uid = RightContextUtil.getUser().getId().intValue();
        req.setPuid(puid);
        List<Integer> shopIdList = shopAuthService.checkAuthByShopIds(puid, shopRightUtils.checkShopRight(uid, Arrays.asList(req.getShopId())));
        log.info("建议ASIN查询接口入参 param:{}",req);
        GetSuggestAsinResponse response = new GetSuggestAsinResponse();
        if (CollectionUtils.isEmpty(shopIdList)) {
            log.info("建议ASIN查询接口无已授权店铺");
            return ResultUtil.success("无授权店铺");
        }
        response = postService.getSuggestAsin(req);
        return ResultUtil.success(response);
    }

    /**
     * 创建页面 - 搜索查询接口
     */
    @CheckRight(value = {SellfoxRightResource.MOD_AD_POST__VIEW_LIST})
    @PostMapping("/getAsinPageList")
    public Result<AsinPageResponse> getAsinPageList(@RequestBody @Valid GetSuggestAsinRequest req) {
        int puid = RightContextUtil.getPuid().intValue();
        int uid = RightContextUtil.getUser().getId().intValue();
        req.setPuid(puid);
        List<Integer> shopIdList = shopAuthService.checkAuthByShopIds(puid, shopRightUtils.checkShopRight(uid, Arrays.asList(req.getShopId())));
        log.info("搜索框查询接口入参 param:{}",req);
        AsinPageResponse response = new AsinPageResponse();
        if (CollectionUtils.isEmpty(shopIdList)) {
            log.info("搜索框查询接口无已授权店铺");
            return ResultUtil.success("无授权店铺");
        }
        response = postService.getAsinPageList(req);
        return ResultUtil.success(response);
    }

    /**
     * 创建页面 - 保存草稿接口
     */
    @CheckRight(value = {SellfoxRightResource.MOD_AD_POST__EDIT_DATA})
    @PostMapping("/submitForDraft")
    public Result<AmazonPost> submitForDraft(@RequestBody @Valid SubmitForDraftPostRequest req) {
        int puid = RightContextUtil.getPuid().intValue();
        int uid = RightContextUtil.getUser().getId().intValue();
        req.setPuid(puid);
        req.setUid(uid);
        log.info("保存为草稿接口入参 param:{}",req);
        List<Integer> shopIdList = shopAuthService.checkAuthByShopIds(puid, shopRightUtils.checkShopRight(uid, Arrays.asList(req.getShopId())));
        if (CollectionUtils.isEmpty(shopIdList)) {
            log.info("保存为草稿接口无已授权店铺");
            return ResultUtil.success("无授权店铺");
        }
        return postService.submitForDraft(req);
    }

    /**
     * 创建页面 - 提交帖子接口
     */
    @CheckRight(value = {SellfoxRightResource.MOD_AD_POST__EDIT_DATA})
    @PostMapping("/submitForReview")
    public Result<String> submitForReview(@RequestBody @Valid SubmitForDraftPostRequest req) {
        int puid = RightContextUtil.getPuid().intValue();
        req.setPuid(puid);
        int uid = RightContextUtil.getUser().getId().intValue();
        req.setUid(uid);
        log.info("创建页面 - 提交帖子接口入参 param:{}",req);
        List<Integer> shopIdList = shopAuthService.checkAuthByShopIds(puid, shopRightUtils.checkShopRight(uid, Arrays.asList(req.getShopId())));
        if (CollectionUtils.isEmpty(shopIdList)) {
            log.info("提交帖子接口无已授权店铺");
            return ResultUtil.success("无授权店铺");
        }
        return postService.submitForReview(req);
    }

    /**
     *  编辑页面 - 保存为草稿帖子接口
     */
    @CheckRight(value = {SellfoxRightResource.MOD_AD_POST__EDIT_DATA})
    @PostMapping("/updatePost")
    public Result<String> updatePost(@RequestBody @Valid SubmitPostRequest req) {
        int puid = RightContextUtil.getPuid().intValue();
        req.setPuid(puid);
        int uid = RightContextUtil.getUser().getId().intValue();
        req.setUid(uid);
        log.info("编辑页面 - 提交帖子接口入参 param:{}",req);
        List<Integer> shopIdList = shopAuthService.checkAuthByShopIds(puid, shopRightUtils.checkShopRight(uid, Arrays.asList(req.getShopId())));
        if (CollectionUtils.isEmpty(shopIdList)) {
            log.info("提交帖子接口无已授权店铺");
            return ResultUtil.success("无授权店铺");
        }
        return postService.updatePost(req);
    }


    /**
     *  编辑页面 - 提交帖子接口
     */
    @CheckRight(value = {SellfoxRightResource.MOD_AD_POST__EDIT_DATA})
    @PostMapping("/submitForUpdate")
    public Result<String> submitForUpdate(@RequestBody @Valid SubmitPostRequest req) {
        int puid = RightContextUtil.getPuid().intValue();
        req.setPuid(puid);
        int uid = RightContextUtil.getUser().getId().intValue();
        req.setUid(uid);
        log.info("编辑页面 - 提交帖子接口入参 param:{}",req);
        List<Integer> shopIdList = shopAuthService.checkAuthByShopIds(puid, shopRightUtils.checkShopRight(uid, Arrays.asList(req.getShopId())));
        if (CollectionUtils.isEmpty(shopIdList)) {
            log.info("提交帖子接口无已授权店铺");
            return ResultUtil.success("无授权店铺");
        }
        return postService.submitForUpdate(req);
    }

    /**
     * 详情页面 - 帖子详情接口
     */
    @CheckRight(value = {SellfoxRightResource.MOD_AD_POST__VIEW_LIST})
    @PostMapping("/getPostDetail")
    public Result<PostDetailResponse> getPostDetail(@RequestBody @Valid GetPostDetailRequest req) {
        int puid = RightContextUtil.getPuid().intValue();
        req.setPuid(puid);
        int uid = RightContextUtil.getUser().getId().intValue();
        req.setUid(uid);
        log.info("帖子详情接口入参 param:{}",req);
        List<Integer> shopIdList = shopAuthService.checkAuthByShopIds(puid, shopRightUtils.checkShopRight(uid, Arrays.asList(req.getShopId())));
        PostDetailResponse response = new PostDetailResponse();
        if (CollectionUtils.isEmpty(shopIdList)) {
            log.info("帖子详情接口无已授权店铺");
            return ResultUtil.success(response);
        }
        response = postService.getPostDetail(req);
        return ResultUtil.success(response);
    }

    /**
     * 列表页面 - 撤回帖子接口
     */
    @CheckRight(value = {SellfoxRightResource.MOD_AD_POST__EDIT_DATA})
    @PostMapping("/unpublish")
    public Result<String> unpublish(@RequestBody @Valid UnpublishPostRequest req) {
        int puid = RightContextUtil.getPuid().intValue();
        req.setPuid(puid);
        int uid = RightContextUtil.getUser().getId().intValue();
        req.setUid(uid);
        log.info("撤回帖子接口入参 param:{}",req);
        List<Integer> shopIdList = shopAuthService.checkAuthByShopIds(puid, shopRightUtils.checkShopRight(uid, Arrays.asList(req.getShopId())));
        if (CollectionUtils.isEmpty(shopIdList)) {
            log.info("撤回帖子接口无已授权店铺");
            return ResultUtil.success("已授权店铺");
        }
        return postService.unpublish(req);
    }

    /**
     * 列表页面 - 手动同步接口
     */
    @CheckRight(value = {SellfoxRightResource.MOD_AD_POST__EDIT_DATA})
    @PostMapping("/syncData")
    public Result<String> syncData(@RequestBody @Valid SyncDataRequest request) {
        int puid = RightContextUtil.getPuid().intValue();
        int uid = RightContextUtil.getUser().getId().intValue();
        request.setPuid(puid);
        log.info("手动同步接口入参 param:{}",request);
        List<Integer> shopIdList = shopAuthService.checkAuthByShopIds(puid, shopRightUtils.checkShopRight(uid, Arrays.asList(request.getShopId())));
        if (CollectionUtils.isEmpty(shopIdList)) {
            log.info("手动同步接口无已授权店铺");
            return ResultUtil.error("无已授权店铺");
        }
        try {
            postService.manualSyncUserPosts(puid, request.getShopId());
            return ResultUtil.success();
        } catch (Exception e) {
            log.error("手动同步接口异常", e);
            return ResultUtil.error(-1, "手动同步接口异常");
        }
    }

    /**
     * 列表页面 - 同步轮询接口
     */
    @CheckRight(value = {SellfoxRightResource.MOD_AD_POST__EDIT_DATA})
    @PostMapping("/checkSyncData")
    public Result<String> checkSyncData(@RequestBody @Valid SyncDataRequest request) {
        int puid = RightContextUtil.getPuid().intValue();
        int uid = RightContextUtil.getUser().getId().intValue();
        log.info("同步轮询接口入参 param:{}",request);
        List<Integer> shopIdList = shopAuthService.checkAuthByShopIds(puid, shopRightUtils.checkShopRight(uid, Arrays.asList(request.getShopId())));
        if (CollectionUtils.isEmpty(shopIdList)) {
            log.info("手动同步接口无已授权店铺");
            return ResultUtil.error("无已授权店铺");
        }
        String msg = postService.checkSyncData(puid, request.getShopId());
        if ("同步完成".equals(msg)) {
            return ResultUtil.success( "", "同步完成");
        } else if ("同步失败".equals(msg)) {
            return ResultUtil.error("同步失败");
        } else {
            return ResultUtil.success("", "同步中");
        }
    }
}
