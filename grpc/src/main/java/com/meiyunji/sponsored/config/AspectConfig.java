package com.meiyunji.sponsored.config;

import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.EnableAspectJAutoProxy;

@Configuration
@EnableAspectJAutoProxy
public class AspectConfig {

    @Bean
    @ConditionalOnMissingBean(GrpcLogAspect.class)
    public GrpcLogAspect catchLogAspect() {
        return new GrpcLogAspect();
    }
}
