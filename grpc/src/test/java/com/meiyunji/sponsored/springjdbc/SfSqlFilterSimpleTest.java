package com.meiyunji.sponsored.springjdbc;

import com.meiyunji.sponsored.common.config.DynamicRefreshNacosConfiguration;
import com.meiyunji.sponsored.common.exception.BizServiceException;
import com.meiyunji.sponsored.common.filter.SfSqlFilter;
import com.meiyunji.sponsored.common.util.SqlConfigUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.lang.reflect.Method;
import java.util.HashSet;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;

/**
 * SfSqlFilter 简化测试类
 * 直接测试内部的拦截方法，避免复杂的 Druid 依赖
 */
public class SfSqlFilterSimpleTest {

    private SfSqlFilter sqlFilter;
    private DynamicRefreshNacosConfiguration mockConfig;
    private SqlConfigUtil mockSqlConfigUtil;

    @BeforeEach
    void setUp() throws Exception {
        // 创建模拟对象
        mockConfig = new DynamicRefreshNacosConfiguration() {
            @Override
            public Set<String> getDruidFilterWhiteSqls() {
                return new HashSet<>();
            }
        };

        mockSqlConfigUtil = new SqlConfigUtil() {
            @Override
            public Set<String> getSellerIdCheckTablesSet() {
                Set<String> tables = new HashSet<>();
                tables.add("users");
                tables.add("orders");
                tables.add("products");
                return tables;
            }
        };

        // 创建 SfSqlFilter 实例并注入依赖
        sqlFilter = new SfSqlFilter();
        
        // 通过反射设置私有字段
        java.lang.reflect.Field configField = SfSqlFilter.class.getDeclaredField("dynamicRefreshNacosConfiguration");
        configField.setAccessible(true);
        configField.set(sqlFilter, mockConfig);

        java.lang.reflect.Field utilField = SfSqlFilter.class.getDeclaredField("sqlConfigUtil");
        utilField.setAccessible(true);
        utilField.set(sqlFilter, mockSqlConfigUtil);
    }

    /**
     * 通过反射调用私有的 interceptSellerIdSql 方法
     */
    private void testSellerIdSql(String sql, boolean shouldThrow) throws Exception {
        Method method = SfSqlFilter.class.getDeclaredMethod("interceptSellerIdSql", String.class, boolean.class);
        method.setAccessible(true);
        
        if (shouldThrow) {
            assertThrows(BizServiceException.class, () -> {
                try {
                    method.invoke(sqlFilter, sql, true);
                } catch (Exception e) {
                    if (e.getCause() instanceof BizServiceException) {
                        throw (BizServiceException) e.getCause();
                    }
                    throw new RuntimeException(e);
                }
            }, "SQL should be intercepted: " + sql);
        } else {
            assertDoesNotThrow(() -> {
                try {
                    method.invoke(sqlFilter, sql, true);
                } catch (Exception e) {
                    if (e.getCause() instanceof BizServiceException) {
                        throw (BizServiceException) e.getCause();
                    }
                    throw new RuntimeException(e);
                }
            }, "SQL should not be intercepted: " + sql);
        }
    }

    @Test
    public void testValidSellerIdQueries() throws Exception {
        // 测试有效的 seller_id 查询 - 应该通过
        String[] validQueries = {
            "SELECT * FROM users WHERE seller_id = 'valid_seller_123'",
            "SELECT * FROM users WHERE seller_id = ?",
            "SELECT * FROM users WHERE seller_id IN ('seller1', 'seller2')",
            "SELECT * FROM users WHERE seller_id != NULL",
            "SELECT * FROM users WHERE seller_id <> ''",
            "SELECT * FROM users WHERE seller_id = 'valid' AND status = 'active'"
        };

        for (String sql : validQueries) {
            testSellerIdSql(sql, false);
        }
    }

    @Test
    public void testInvalidSellerIdQueries() throws Exception {
        // 测试无效的 seller_id 查询 - 应该被拦截
        String[] invalidQueries = {
            "SELECT * FROM users WHERE seller_id = NULL",
            "SELECT * FROM users WHERE seller_id IS NULL",
            "SELECT * FROM users WHERE seller_id = ''",
            "SELECT * FROM users WHERE seller_id IS NOT NULL", // 仅仅不为null还不够
            "SELECT * FROM users WHERE seller_id IN (NULL, '')" // 全部为无效值
        };

        for (String sql : invalidQueries) {
            testSellerIdSql(sql, true);
        }
    }

    @Test
    public void testMixedValidInvalidQueries() throws Exception {
        // 测试混合有效/无效值的查询 - 包含有效值应该通过
        String[] mixedQueries = {
            "SELECT * FROM users WHERE seller_id IN (NULL, '', 'valid')", // 包含有效值，应该通过
            "SELECT * FROM users WHERE seller_id = 'valid' OR seller_id IS NULL" // 包含有效值，应该通过
        };

        for (String sql : mixedQueries) {
            testSellerIdSql(sql, false);
        }
    }

    @Test
    public void testNonTargetTableQueries() throws Exception {
        // 测试不涉及目标表的查询 - 应该通过（不需要检查）
        String[] nonTargetQueries = {
            "SELECT * FROM logs WHERE seller_id IS NULL",
            "SELECT * FROM config WHERE seller_id = ''",
            "SELECT * FROM temp_table WHERE seller_id = NULL"
        };

        for (String sql : nonTargetQueries) {
            testSellerIdSql(sql, false);
        }
    }

    @Test
    public void testComplexQueries() throws Exception {
        // 测试复杂查询
        String validComplexSql = "SELECT u.*, o.order_id FROM users u " +
                "JOIN orders o ON u.id = o.user_id " +
                "WHERE u.seller_id = 'valid_seller' AND o.status = 'completed'";
        
        testSellerIdSql(validComplexSql, false);

        String invalidComplexSql = "SELECT u.*, o.order_id FROM users u " +
                "JOIN orders o ON u.id = o.user_id " +
                "WHERE u.seller_id IS NULL AND o.status = 'completed'";
        
        testSellerIdSql(invalidComplexSql, true);
    }

    @Test
    public void testSubqueryWithSellerIdCheck() throws Exception {
        // 测试子查询中的 seller_id 检查
        String validSubquerySql = "SELECT * FROM users WHERE id IN (" +
                "SELECT user_id FROM orders WHERE seller_id = 'valid_seller'" +
                ")";
        
        testSellerIdSql(validSubquerySql, false);

        String invalidSubquerySql = "SELECT * FROM users WHERE id IN (" +
                "SELECT user_id FROM orders WHERE seller_id IS NULL" +
                ")";
        
        testSellerIdSql(invalidSubquerySql, true);
    }

    @Test
    public void testWhitelistQueries() throws Exception {
        // 测试白名单查询
        String whitelistSql = "SELECT * FROM users WHERE seller_id IS NULL";
        
        // 修改模拟配置以包含白名单
        mockConfig = new DynamicRefreshNacosConfiguration() {
            @Override
            public Set<String> getDruidFilterWhiteSqls() {
                Set<String> whitelist = new HashSet<>();
                whitelist.add(whitelistSql);
                return whitelist;
            }
        };
        
        // 重新注入配置
        java.lang.reflect.Field configField = SfSqlFilter.class.getDeclaredField("dynamicRefreshNacosConfiguration");
        configField.setAccessible(true);
        configField.set(sqlFilter, mockConfig);

        // 白名单查询应该通过，即使 seller_id 无效
        testSellerIdSql(whitelistSql, false);
    }

    @Test
    public void testEmptyTargetTables() throws Exception {
        // 测试空的目标表配置
        mockSqlConfigUtil = new SqlConfigUtil() {
            @Override
            public Set<String> getSellerIdCheckTablesSet() {
                return new HashSet<>();
            }
        };
        
        // 重新注入配置
        java.lang.reflect.Field utilField = SfSqlFilter.class.getDeclaredField("sqlConfigUtil");
        utilField.setAccessible(true);
        utilField.set(sqlFilter, mockSqlConfigUtil);

        String sql = "SELECT * FROM users WHERE seller_id IS NULL";
        
        // 如果没有配置目标表，应该跳过检查
        testSellerIdSql(sql, false);
    }

    @Test
    public void testCaseInsensitiveFieldNames() throws Exception {
        // 测试字段名大小写不敏感
        String[] caseVariations = {
            "SELECT * FROM users WHERE SELLER_ID = 'valid'",
            "SELECT * FROM users WHERE Seller_Id = 'valid'",
            "SELECT * FROM users WHERE seller_ID = 'valid'",
            "SELECT * FROM users WHERE u.seller_id = 'valid'"
        };

        for (String sql : caseVariations) {
            testSellerIdSql(sql, false);
        }
    }

    @Test
    public void testErrorMessage() throws Exception {
        String invalidSql = "SELECT * FROM users WHERE seller_id IS NULL";
        
        BizServiceException exception = assertThrows(BizServiceException.class, () -> {
            testSellerIdSql(invalidSql, true);
        });
        
        assertTrue(exception.getMessage().contains("sellerId"), 
            "Exception message should mention sellerId: " + exception.getMessage());
        assertTrue(exception.getMessage().contains("null"), 
            "Exception message should mention null: " + exception.getMessage());
    }
}
