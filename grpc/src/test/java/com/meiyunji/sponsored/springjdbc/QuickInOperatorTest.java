package com.meiyunji.sponsored.springjdbc;

import com.alibaba.druid.sql.ast.SQLStatement;
import com.alibaba.druid.util.JdbcConstants;
import com.meiyunji.sponsored.common.springjdbc.SqlTypeParser;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 快速验证 IN 操作符修复效果
 */
public class QuickInOperatorTest {

    public static void main(String[] args) {
        System.out.println("=== IN 操作符修复验证 ===\n");

        // 设置目标表
        Set<String> targetTables = new HashSet<>();
        targetTables.add("users");

        // 测试原始问题案例
        testSql("SELECT * FROM users WHERE seller_id IN ('seller1', 'seller2')", targetTables);
        
        // 测试其他 IN 相关案例
        testSql("SELECT * FROM users WHERE seller_id IN (NULL, '')", targetTables);
        testSql("SELECT * FROM users WHERE seller_id IN (NULL, '', 'valid')", targetTables);
        testSql("SELECT * FROM users WHERE seller_id NOT IN (NULL, '')", targetTables);
        testSql("SELECT * FROM users WHERE seller_id NOT IN ('seller1', 'seller2')", targetTables);
    }

    private static void testSql(String sql, Set<String> targetTables) {
        System.out.println("测试 SQL: " + sql);
        
        try {
            List<SQLStatement> statements = SqlTypeParser.parseSql(sql, JdbcConstants.MYSQL.name());
            if (statements == null || statements.isEmpty()) {
                System.out.println("  结果: SQL 解析失败");
                System.out.println();
                return;
            }

            SQLStatement statement = statements.get(0);

            // 检查字段是否存在
            boolean containsField = SqlTypeParser.containsFieldInWhere(statement, "seller_id");
            System.out.println("  包含 seller_id 字段: " + containsField);

            // 检查字段值是否有效
            boolean hasValidValue = SqlTypeParser.containsValidFieldInWhere(statement, "seller_id");
            System.out.println("  seller_id 值有效: " + hasValidValue);

            // 检查是否需要有效字段
            boolean needsValidField = SqlTypeParser.needsValidFieldInWhere(statement, targetTables, "seller_id");
            System.out.println("  需要有效字段: " + needsValidField);

            // 最终结果
            if (needsValidField) {
                System.out.println("  结果: ❌ 拦截 - seller_id 字段缺失或值无效");
            } else {
                System.out.println("  结果: ✅ 通过 - seller_id 检查通过");
            }

        } catch (Exception e) {
            System.out.println("  结果: ⚠️  解析异常 - " + e.getMessage());
        }
        
        System.out.println();
    }
}
