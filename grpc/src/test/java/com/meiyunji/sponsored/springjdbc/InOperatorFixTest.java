package com.meiyunji.sponsored.springjdbc;

import com.alibaba.druid.sql.ast.SQLStatement;
import com.alibaba.druid.util.JdbcConstants;
import com.meiyunji.sponsored.common.springjdbc.SqlTypeParser;
import org.junit.jupiter.api.Test;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;

/**
 * IN 操作符修复验证测试
 */
public class InOperatorFixTest {

    @Test
    public void testInOperatorWithValidValues() {
        // 测试 IN 操作符包含有效值的情况
        String sql = "SELECT * FROM users WHERE seller_id IN ('seller1', 'seller2')";
        
        List<SQLStatement> statements = SqlTypeParser.parseSql(sql, JdbcConstants.MYSQL.name());
        assertNotNull(statements);
        assertFalse(statements.isEmpty());
        
        SQLStatement statement = statements.get(0);
        
        // 检查字段是否存在
        boolean containsField = SqlTypeParser.containsFieldInWhere(statement, "seller_id");
        assertTrue(containsField, "应该包含 seller_id 字段");
        
        // 检查字段值是否有效
        boolean hasValidValue = SqlTypeParser.containsValidFieldInWhere(statement, "seller_id");
        assertTrue(hasValidValue, "IN ('seller1', 'seller2') 应该被认为是有效值");
        
        // 检查是否需要有效字段
        Set<String> targetTables = new HashSet<>();
        targetTables.add("users");
        boolean needsValidField = SqlTypeParser.needsValidFieldInWhere(statement, targetTables, "seller_id");
        assertFalse(needsValidField, "因为有有效值，所以不需要额外的字段检查");
    }

    @Test
    public void testInOperatorWithInvalidValues() {
        // 测试 IN 操作符包含无效值的情况
        String sql = "SELECT * FROM users WHERE seller_id IN (NULL, '')";
        
        List<SQLStatement> statements = SqlTypeParser.parseSql(sql, JdbcConstants.MYSQL.name());
        assertNotNull(statements);
        assertFalse(statements.isEmpty());
        
        SQLStatement statement = statements.get(0);
        
        // 检查字段是否存在
        boolean containsField = SqlTypeParser.containsFieldInWhere(statement, "seller_id");
        assertTrue(containsField, "应该包含 seller_id 字段");
        
        // 检查字段值是否有效
        boolean hasValidValue = SqlTypeParser.containsValidFieldInWhere(statement, "seller_id");
        assertFalse(hasValidValue, "IN (NULL, '') 应该被认为是无效值");
        
        // 检查是否需要有效字段
        Set<String> targetTables = new HashSet<>();
        targetTables.add("users");
        boolean needsValidField = SqlTypeParser.needsValidFieldInWhere(statement, targetTables, "seller_id");
        assertTrue(needsValidField, "因为没有有效值，所以需要额外的字段检查");
    }

    @Test
    public void testInOperatorWithMixedValues() {
        // 测试 IN 操作符包含混合值的情况
        String sql = "SELECT * FROM users WHERE seller_id IN (NULL, '', 'valid_seller')";
        
        List<SQLStatement> statements = SqlTypeParser.parseSql(sql, JdbcConstants.MYSQL.name());
        assertNotNull(statements);
        assertFalse(statements.isEmpty());
        
        SQLStatement statement = statements.get(0);
        
        // 检查字段值是否有效
        boolean hasValidValue = SqlTypeParser.containsValidFieldInWhere(statement, "seller_id");
        assertTrue(hasValidValue, "IN (NULL, '', 'valid_seller') 包含有效值，应该被认为是有效的");
        
        // 检查是否需要有效字段
        Set<String> targetTables = new HashSet<>();
        targetTables.add("users");
        boolean needsValidField = SqlTypeParser.needsValidFieldInWhere(statement, targetTables, "seller_id");
        assertFalse(needsValidField, "因为包含有效值，所以不需要额外的字段检查");
    }

    @Test
    public void testNotInOperator() {
        // 测试 NOT IN 操作符
        String sql1 = "SELECT * FROM users WHERE seller_id NOT IN (NULL, '')";
        
        List<SQLStatement> statements = SqlTypeParser.parseSql(sql1, JdbcConstants.MYSQL.name());
        assertNotNull(statements);
        assertFalse(statements.isEmpty());
        
        SQLStatement statement = statements.get(0);
        
        // 检查字段值是否有效
        boolean hasValidValue = SqlTypeParser.containsValidFieldInWhere(statement, "seller_id");
        assertTrue(hasValidValue, "NOT IN (NULL, '') 排除无效值，应该被认为是有效的");
        
        // 测试 NOT IN 包含有效值的情况
        String sql2 = "SELECT * FROM users WHERE seller_id NOT IN ('seller1', 'seller2')";
        statements = SqlTypeParser.parseSql(sql2, JdbcConstants.MYSQL.name());
        statement = statements.get(0);
        
        hasValidValue = SqlTypeParser.containsValidFieldInWhere(statement, "seller_id");
        assertFalse(hasValidValue, "NOT IN ('seller1', 'seller2') 排除有效值，不能确保有有效值");
    }

    @Test
    public void testLikeOperator() {
        // 测试 LIKE 操作符
        String sql1 = "SELECT * FROM users WHERE seller_id LIKE 'seller%'";
        
        List<SQLStatement> statements = SqlTypeParser.parseSql(sql1, JdbcConstants.MYSQL.name());
        assertNotNull(statements);
        assertFalse(statements.isEmpty());
        
        SQLStatement statement = statements.get(0);
        
        // 检查字段值是否有效
        boolean hasValidValue = SqlTypeParser.containsValidFieldInWhere(statement, "seller_id");
        assertTrue(hasValidValue, "LIKE 'seller%' 应该被认为是有效的");
        
        // 测试 LIKE 空字符串
        String sql2 = "SELECT * FROM users WHERE seller_id LIKE ''";
        statements = SqlTypeParser.parseSql(sql2, JdbcConstants.MYSQL.name());
        statement = statements.get(0);
        
        hasValidValue = SqlTypeParser.containsValidFieldInWhere(statement, "seller_id");
        assertFalse(hasValidValue, "LIKE '' 应该被认为是无效的");
    }

    @Test
    public void testNotLikeOperator() {
        // 测试 NOT LIKE 操作符
        String sql1 = "SELECT * FROM users WHERE seller_id NOT LIKE ''";
        
        List<SQLStatement> statements = SqlTypeParser.parseSql(sql1, JdbcConstants.MYSQL.name());
        assertNotNull(statements);
        assertFalse(statements.isEmpty());
        
        SQLStatement statement = statements.get(0);
        
        // 检查字段值是否有效
        boolean hasValidValue = SqlTypeParser.containsValidFieldInWhere(statement, "seller_id");
        assertTrue(hasValidValue, "NOT LIKE '' 排除空字符串，应该被认为是有效的");
        
        // 测试 NOT LIKE 有效模式
        String sql2 = "SELECT * FROM users WHERE seller_id NOT LIKE 'seller%'";
        statements = SqlTypeParser.parseSql(sql2, JdbcConstants.MYSQL.name());
        statement = statements.get(0);
        
        hasValidValue = SqlTypeParser.containsValidFieldInWhere(statement, "seller_id");
        assertFalse(hasValidValue, "NOT LIKE 'seller%' 排除有效模式，不能确保有有效值");
    }

    @Test
    public void testComplexInQueries() {
        // 测试复杂的 IN 查询
        String[] validQueries = {
            "SELECT * FROM users WHERE seller_id IN ('a', 'b', 'c')",
            "SELECT * FROM users WHERE seller_id IN (?)",
            "SELECT * FROM users WHERE seller_id IN (1, 2, 3)", // 数字值
            "SELECT * FROM users WHERE seller_id IN ('valid', NULL)", // 混合值，包含有效值
        };
        
        for (String sql : validQueries) {
            List<SQLStatement> statements = SqlTypeParser.parseSql(sql, JdbcConstants.MYSQL.name());
            assertNotNull(statements, "SQL 解析失败: " + sql);
            assertFalse(statements.isEmpty(), "SQL 解析结果为空: " + sql);
            
            SQLStatement statement = statements.get(0);
            boolean hasValidValue = SqlTypeParser.containsValidFieldInWhere(statement, "seller_id");
            assertTrue(hasValidValue, "应该被认为是有效值: " + sql);
        }
        
        String[] invalidQueries = {
            "SELECT * FROM users WHERE seller_id IN (NULL)",
            "SELECT * FROM users WHERE seller_id IN ('')",
            "SELECT * FROM users WHERE seller_id IN (NULL, '', '   ')", // 全部无效值
        };
        
        for (String sql : invalidQueries) {
            List<SQLStatement> statements = SqlTypeParser.parseSql(sql, JdbcConstants.MYSQL.name());
            assertNotNull(statements, "SQL 解析失败: " + sql);
            assertFalse(statements.isEmpty(), "SQL 解析结果为空: " + sql);
            
            SQLStatement statement = statements.get(0);
            boolean hasValidValue = SqlTypeParser.containsValidFieldInWhere(statement, "seller_id");
            assertFalse(hasValidValue, "应该被认为是无效值: " + sql);
        }
    }

    @Test
    public void testOriginalProblemCase() {
        // 测试原始问题案例
        String sql = "SELECT * FROM users WHERE seller_id IN ('seller1', 'seller2')";

        System.out.println("=== 修复后的测试结果 ===");
        System.out.println("测试 SQL: " + sql);

        List<SQLStatement> statements = SqlTypeParser.parseSql(sql, JdbcConstants.MYSQL.name());
        assertNotNull(statements);
        assertFalse(statements.isEmpty());

        SQLStatement statement = statements.get(0);

        // 检查表名
        Set<String> tableNames = SqlTypeParser.extractTableNames(statement);
        System.out.println("  涉及表: " + tableNames);

        // 检查字段是否存在
        boolean containsField = SqlTypeParser.containsFieldInWhere(statement, "seller_id");
        System.out.println("  包含 seller_id 字段: " + containsField);

        // 检查字段值是否有效
        boolean hasValidValue = SqlTypeParser.containsValidFieldInWhere(statement, "seller_id");
        System.out.println("  seller_id 值有效: " + hasValidValue);

        // 检查是否需要有效字段
        Set<String> targetTables = new HashSet<>();
        targetTables.add("users");
        boolean needsValidField = SqlTypeParser.needsValidFieldInWhere(statement, targetTables, "seller_id");
        System.out.println("  需要有效字段: " + needsValidField);

        // 最终结果
        if (needsValidField) {
            System.out.println("  结果: ❌ 拦截 - seller_id 字段缺失或值无效");
        } else {
            System.out.println("  结果: ✅ 通过 - seller_id 检查通过");
        }

        // 断言验证
        assertTrue(containsField, "应该包含 seller_id 字段");
        assertTrue(hasValidValue, "seller_id 值应该有效");
        assertFalse(needsValidField, "不应该需要额外的字段检查");

        System.out.println("=== 修复验证成功！===\n");
    }
}
