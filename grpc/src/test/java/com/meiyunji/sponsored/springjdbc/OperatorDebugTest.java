package com.meiyunji.sponsored.springjdbc;

import com.alibaba.druid.sql.ast.SQLStatement;
import com.alibaba.druid.sql.ast.expr.SQLBinaryOpExpr;
import com.alibaba.druid.sql.ast.expr.SQLBinaryOperator;
import com.alibaba.druid.sql.dialect.mysql.visitor.MySqlASTVisitorAdapter;
import com.alibaba.druid.util.JdbcConstants;
import com.meiyunji.sponsored.common.springjdbc.SqlTypeParser;
import org.junit.jupiter.api.Test;

import java.util.List;

/**
 * 操作符调试测试
 * 用于查看 IN 操作符的正确名称
 */
public class OperatorDebugTest {

    @Test
    public void debugInOperator() {
        String sql = "SELECT * FROM users WHERE seller_id IN ('seller1', 'seller2')";
        
        System.out.println("调试 SQL: " + sql);
        
        List<SQLStatement> statements = SqlTypeParser.parseSql(sql, JdbcConstants.MYSQL.name());
        if (statements != null && !statements.isEmpty()) {
            SQLStatement statement = statements.get(0);
            
            OperatorVisitor visitor = new OperatorVisitor();
            statement.accept(visitor);
        }
    }

    @Test
    public void debugNotInOperator() {
        String sql = "SELECT * FROM users WHERE seller_id NOT IN ('seller1', 'seller2')";
        
        System.out.println("调试 SQL: " + sql);
        
        List<SQLStatement> statements = SqlTypeParser.parseSql(sql, JdbcConstants.MYSQL.name());
        if (statements != null && !statements.isEmpty()) {
            SQLStatement statement = statements.get(0);
            
            OperatorVisitor visitor = new OperatorVisitor();
            statement.accept(visitor);
        }
    }

    @Test
    public void debugAllOperators() {
        String[] testSqls = {
            "SELECT * FROM users WHERE seller_id = 'value'",
            "SELECT * FROM users WHERE seller_id != 'value'",
            "SELECT * FROM users WHERE seller_id <> 'value'",
            "SELECT * FROM users WHERE seller_id IS NULL",
            "SELECT * FROM users WHERE seller_id IS NOT NULL",
            "SELECT * FROM users WHERE seller_id IN ('a', 'b')",
            "SELECT * FROM users WHERE seller_id NOT IN ('a', 'b')",
            "SELECT * FROM users WHERE seller_id LIKE 'pattern%'",
            "SELECT * FROM users WHERE seller_id NOT LIKE 'pattern%'"
        };

        for (String sql : testSqls) {
            System.out.println("\n调试 SQL: " + sql);
            
            List<SQLStatement> statements = SqlTypeParser.parseSql(sql, JdbcConstants.MYSQL.name());
            if (statements != null && !statements.isEmpty()) {
                SQLStatement statement = statements.get(0);
                
                OperatorVisitor visitor = new OperatorVisitor();
                statement.accept(visitor);
            }
        }
    }

    private static class OperatorVisitor extends MySqlASTVisitorAdapter {
        @Override
        public boolean visit(SQLBinaryOpExpr x) {
            SQLBinaryOperator operator = x.getOperator();
            System.out.println("  操作符: " + operator);
            System.out.println("  操作符名称: " + operator.name);
            System.out.println("  左侧: " + x.getLeft());
            System.out.println("  右侧: " + x.getRight());
            return true;
        }
    }
}
