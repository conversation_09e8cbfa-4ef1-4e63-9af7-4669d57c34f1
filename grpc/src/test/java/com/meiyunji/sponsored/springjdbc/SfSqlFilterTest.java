package com.meiyunji.sponsored.springjdbc;

import com.alibaba.druid.filter.FilterChain;
import com.alibaba.druid.proxy.jdbc.ConnectionProxy;
import com.alibaba.druid.proxy.jdbc.PreparedStatementProxy;
import com.meiyunji.sponsored.common.config.DynamicRefreshNacosConfiguration;
import com.meiyunji.sponsored.common.exception.BizServiceException;
import com.meiyunji.sponsored.common.filter.SfSqlFilter;
import com.meiyunji.sponsored.common.util.SqlConfigUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.sql.SQLException;
import java.util.HashSet;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * SfSqlFilter 测试类
 * 专门测试 seller_id 字段的有效值拦截功能
 */
public class SfSqlFilterTest {

    @Mock
    private DynamicRefreshNacosConfiguration dynamicRefreshNacosConfiguration;

    @Mock
    private SqlConfigUtil sqlConfigUtil;

    @Mock
    private FilterChain filterChain;

    @Mock
    private ConnectionProxy connectionProxy;

    private SfSqlFilter sqlFilter;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);

        // 设置模拟对象的行为
        when(dynamicRefreshNacosConfiguration.getDruidFilterWhiteSqls()).thenReturn(new HashSet<>());

        Set<String> targetTables = new HashSet<>();
        targetTables.add("users");
        targetTables.add("orders");
        targetTables.add("products");
        when(sqlConfigUtil.getSellerIdCheckTablesSet()).thenReturn(targetTables);

        // 设置 SqlConfigUtil 的其他配置
        when(sqlConfigUtil.getLogEnable()).thenReturn(false); // 关闭日志以简化测试
        when(sqlConfigUtil.getStatEnable()).thenReturn(false); // 关闭 PUID 检查以专注于 seller_id 检查
        when(sqlConfigUtil.getSellerIdCheckEnable()).thenReturn(true); // 启用 seller_id 检查
        when(sqlConfigUtil.getSellerIdThrowError()).thenReturn(true); // 启用异常抛出

        // 创建 SfSqlFilter 实例并注入依赖
        sqlFilter = new SfSqlFilter();
        // 通过反射设置私有字段
        try {
            java.lang.reflect.Field configField = SfSqlFilter.class.getDeclaredField("dynamicRefreshNacosConfiguration");
            configField.setAccessible(true);
            configField.set(sqlFilter, dynamicRefreshNacosConfiguration);

            java.lang.reflect.Field utilField = SfSqlFilter.class.getDeclaredField("sqlConfigUtil");
            utilField.setAccessible(true);
            utilField.set(sqlFilter, sqlConfigUtil);
        } catch (Exception e) {
            throw new RuntimeException("Failed to inject dependencies", e);
        }
    }

    /**
     * 辅助方法：执行 SQL 过滤检查
     */
    private void executeSqlFilter(String sql) throws SQLException {
        sqlFilter.connection_prepareStatement(filterChain, connectionProxy, sql);
    }

    @Test
    public void testValidSellerIdQueries() {
        // 测试有效的 seller_id 查询 - 应该通过
        String[] validQueries = {
            "SELECT * FROM users WHERE seller_id = 'valid_seller_123'",
            "SELECT * FROM users WHERE seller_id = ?",
            "SELECT * FROM users WHERE seller_id IN ('seller1', 'seller2')",
            "SELECT * FROM users WHERE seller_id != NULL",
            "SELECT * FROM users WHERE seller_id <> ''",
            "SELECT * FROM users WHERE seller_id = 'valid' AND status = 'active'"
        };

        for (String sql : validQueries) {
            assertDoesNotThrow(() -> {
                executeSqlFilter(sql);
            }, "Valid SQL should not throw exception: " + sql);
        }
    }

    @Test
    public void testInvalidSellerIdQueries() {
        // 测试无效的 seller_id 查询 - 应该被拦截
        String[] invalidQueries = {
            "SELECT * FROM users WHERE seller_id = NULL",
            "SELECT * FROM users WHERE seller_id IS NULL",
            "SELECT * FROM users WHERE seller_id = ''",
            "SELECT * FROM users WHERE seller_id = '   '",
            "SELECT * FROM users WHERE seller_id IS NOT NULL", // 仅仅不为null还不够
            "SELECT * FROM users WHERE seller_id IN (NULL, '')" // 全部为无效值
        };

        for (String sql : invalidQueries) {
            BizServiceException exception = assertThrows(BizServiceException.class, () -> {
                executeSqlFilter(sql);
            }, "Invalid SQL should throw exception: " + sql);

            assertTrue(exception.getMessage().contains("sellerId"),
                "Exception message should mention sellerId: " + exception.getMessage());
        }
    }

    @Test
    public void testMixedValidInvalidQueries() {
        // 测试混合有效/无效值的查询
        String[] mixedQueries = {
            "SELECT * FROM users WHERE seller_id IN (NULL, '', 'valid')", // 包含有效值，应该通过
            "SELECT * FROM users WHERE seller_id = 'valid' OR seller_id IS NULL", // 包含有效值，应该通过
            "SELECT * FROM users WHERE (seller_id = 'valid' OR status = 'active') AND seller_id != ''" // 复杂条件，应该通过
        };

        for (String sql : mixedQueries) {
            assertDoesNotThrow(() -> {
                executeSqlFilter(sql);
            }, "Mixed valid SQL should not throw exception: " + sql);
        }
    }

    @Test
    public void testNonTargetTableQueries() {
        // 测试不涉及目标表的查询 - 应该通过（不需要检查）
        String[] nonTargetQueries = {
            "SELECT * FROM logs WHERE seller_id IS NULL",
            "SELECT * FROM config WHERE seller_id = ''",
            "SELECT * FROM temp_table WHERE seller_id = NULL"
        };

        for (String sql : nonTargetQueries) {
            assertDoesNotThrow(() -> {
                executeSqlFilter(sql);
            }, "Non-target table SQL should not throw exception: " + sql);
        }
    }

    @Test
    public void testComplexQueries() {
        // 测试复杂查询
        String validComplexSql = "SELECT u.*, o.order_id FROM users u " +
                "JOIN orders o ON u.id = o.user_id " +
                "WHERE u.seller_id = 'valid_seller' AND o.status = 'completed'";

        assertDoesNotThrow(() -> {
            executeSqlFilter(validComplexSql);
        }, "Valid complex SQL should not throw exception");

        String invalidComplexSql = "SELECT u.*, o.order_id FROM users u " +
                "JOIN orders o ON u.id = o.user_id " +
                "WHERE u.seller_id IS NULL AND o.status = 'completed'";

        assertThrows(BizServiceException.class, () -> {
            executeSqlFilter(invalidComplexSql);
        }, "Invalid complex SQL should throw exception");
    }

    @Test
    public void testSubqueryWithSellerIdCheck() {
        // 测试子查询中的 seller_id 检查
        String validSubquerySql = "SELECT * FROM users WHERE id IN (" +
                "SELECT user_id FROM orders WHERE seller_id = 'valid_seller'" +
                ")";

        assertDoesNotThrow(() -> {
            executeSqlFilter(validSubquerySql);
        }, "Valid subquery SQL should not throw exception");

        String invalidSubquerySql = "SELECT * FROM users WHERE id IN (" +
                "SELECT user_id FROM orders WHERE seller_id IS NULL" +
                ")";

        assertThrows(BizServiceException.class, () -> {
            executeSqlFilter(invalidSubquerySql);
        }, "Invalid subquery SQL should throw exception");
    }

    @Test
    public void testUpdateDeleteStatements() {
        // 测试 UPDATE 和 DELETE 语句（这些主要检查 puid，但也可能涉及 seller_id）
        // 需要启用 PUID 检查来测试这些语句
        when(sqlConfigUtil.getStatEnable()).thenReturn(true);
        when(sqlConfigUtil.getThrowError()).thenReturn(true);

        String updateSql = "UPDATE users SET status = 'inactive' WHERE puid = 123 AND seller_id = 'valid'";
        String deleteSql = "DELETE FROM users WHERE puid = 123 AND seller_id = 'valid'";

        // 这些语句主要由 PUID_CHECK 策略处理，但不应该因为 seller_id 检查而失败
        assertDoesNotThrow(() -> {
            executeSqlFilter(updateSql);
        }, "UPDATE with valid seller_id should not throw exception");

        assertDoesNotThrow(() -> {
            executeSqlFilter(deleteSql);
        }, "DELETE with valid seller_id should not throw exception");

        // 恢复原始设置
        when(sqlConfigUtil.getStatEnable()).thenReturn(false);
    }

    @Test
    public void testWhitelistQueries() {
        // 测试白名单查询
        String whitelistSql = "SELECT * FROM users WHERE seller_id IS NULL";

        // 将查询添加到白名单
        Set<String> whitelist = new HashSet<>();
        whitelist.add(whitelistSql);
        when(dynamicRefreshNacosConfiguration.getDruidFilterWhiteSqls()).thenReturn(whitelist);

        // 白名单查询应该通过，即使 seller_id 无效
        assertDoesNotThrow(() -> {
            executeSqlFilter(whitelistSql);
        }, "Whitelisted SQL should not throw exception");

        // 恢复原始设置
        when(dynamicRefreshNacosConfiguration.getDruidFilterWhiteSqls()).thenReturn(new HashSet<>());
    }

    @Test
    public void testEmptyTargetTables() {
        // 测试空的目标表配置
        Set<String> originalTables = new HashSet<>();
        originalTables.add("users");
        originalTables.add("orders");
        originalTables.add("products");

        when(sqlConfigUtil.getSellerIdCheckTablesSet()).thenReturn(new HashSet<>());

        String sql = "SELECT * FROM users WHERE seller_id IS NULL";

        // 如果没有配置目标表，应该跳过检查
        assertDoesNotThrow(() -> {
            executeSqlFilter(sql);
        }, "SQL should not throw exception when no target tables configured");

        // 恢复原始设置
        when(sqlConfigUtil.getSellerIdCheckTablesSet()).thenReturn(originalTables);
    }

    @Test
    public void testInvalidSqlSyntax() {
        // 测试语法错误的 SQL
        String invalidSyntaxSql = "SELECT * FROM WHERE seller_id = 'valid'";

        // 语法错误的 SQL 应该被跳过（不抛出异常）
        assertDoesNotThrow(() -> {
            executeSqlFilter(invalidSyntaxSql);
        }, "Invalid syntax SQL should be skipped");
    }

    @Test
    public void testCaseInsensitiveFieldNames() {
        // 测试字段名大小写不敏感
        String[] caseVariations = {
            "SELECT * FROM users WHERE SELLER_ID = 'valid'",
            "SELECT * FROM users WHERE Seller_Id = 'valid'",
            "SELECT * FROM users WHERE seller_ID = 'valid'",
            "SELECT * FROM users WHERE u.seller_id = 'valid'"
        };

        for (String sql : caseVariations) {
            assertDoesNotThrow(() -> {
                executeSqlFilter(sql);
            }, "Case variation should not throw exception: " + sql);
        }
    }
}
