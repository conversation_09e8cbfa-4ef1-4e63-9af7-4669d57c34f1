package com.meiyunji.sponsored.filter;

import com.alibaba.druid.proxy.jdbc.JdbcParameter;
import com.alibaba.druid.proxy.jdbc.PreparedStatementProxy;
import com.meiyunji.sponsored.common.config.DynamicRefreshNacosConfiguration;
import com.meiyunji.sponsored.common.filter.SfSqlFilter;
import com.meiyunji.sponsored.common.util.SqlConfigUtil;
import org.mockito.Mockito;

import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

import static org.mockito.Mockito.when;

/**
 * 统一拦截框架演示程序
 * 展示重构后的 SfSqlFilter 统一拦截逻辑
 */
public class UnifiedInterceptFrameworkDemo {

    public static void main(String[] args) {
        System.out.println("=== SfSqlFilter 统一拦截框架演示 ===\n");

        try {
            // 创建和配置 SfSqlFilter
            SfSqlFilter sqlFilter = createConfiguredSqlFilter();

            // 获取拦截方法
            Method compileTimeMethod = SfSqlFilter.class.getDeclaredMethod("interceptCompileTime", String.class);
            compileTimeMethod.setAccessible(true);

            Method executionTimeMethod = SfSqlFilter.class.getDeclaredMethod("interceptExecutionTime", PreparedStatementProxy.class);
            executionTimeMethod.setAccessible(true);

            System.out.println("🔍 预编译阶段拦截测试：");
            testCompileTimeInterception(compileTimeMethod, sqlFilter);

            System.out.println("\n🚀 执行阶段拦截测试：");
            testExecutionTimeInterception(executionTimeMethod, sqlFilter);

            System.out.println("\n📋 白名单功能测试：");
            testWhitelistFunctionality(compileTimeMethod, sqlFilter);

            System.out.println("\n⚙️ 策略开关测试：");
            testStrategyToggle(compileTimeMethod, sqlFilter);

        } catch (Exception e) {
            System.err.println("❌ 演示失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private static void testCompileTimeInterception(Method method, SfSqlFilter sqlFilter) {
        String[][] testCases = {
            // {SQL, 描述, 期望结果}
            {"SELECT * FROM users WHERE seller_id = 'valid'", "SELECT with valid seller_id", "✅ 通过"},
            {"SELECT * FROM users WHERE name = 'test'", "SELECT missing seller_id", "❌ 拦截"},
            {"UPDATE users SET name = 'test' WHERE puid = 123", "UPDATE with puid", "✅ 通过"},
            {"UPDATE users SET name = 'test'", "UPDATE missing puid", "❌ 拦截"},
            {"DELETE FROM users WHERE puid = 123", "DELETE with puid", "✅ 通过"},
            {"DELETE FROM users WHERE name = 'test'", "DELETE missing puid", "❌ 拦截"},
            {"INSERT INTO users (name) VALUES ('test')", "INSERT statement", "✅ 通过"},
            {"SELECT * FROM logs WHERE seller_id IS NULL", "Non-target table", "✅ 通过"}
        };

        for (String[] testCase : testCases) {
            String sql = testCase[0];
            String description = testCase[1];
            String expected = testCase[2];

            System.out.println("  📝 " + description);
            System.out.println("     SQL: " + sql);

            try {
                method.invoke(sqlFilter, sql);
                System.out.println("     结果: ✅ 通过" + (expected.contains("✅") ? " (符合预期)" : " (⚠️ 意外通过)"));
            } catch (Exception e) {
                if (e.getCause() != null && e.getCause().getMessage() != null) {
                    System.out.println("     结果: ❌ 拦截 - " + e.getCause().getMessage().substring(0, Math.min(50, e.getCause().getMessage().length())) + "..." + 
                                     (expected.contains("❌") ? " (符合预期)" : " (⚠️ 意外拦截)"));
                } else {
                    System.out.println("     结果: ❌ 拦截" + (expected.contains("❌") ? " (符合预期)" : " (⚠️ 意外拦截)"));
                }
            }
            System.out.println();
        }
    }

    private static void testExecutionTimeInterception(Method method, SfSqlFilter sqlFilter) {
        Object[][] testCases = {
            // {SQL, 参数值, 描述, 期望结果}
            {"SELECT * FROM users WHERE seller_id = ?", "valid_seller", "Valid seller_id parameter", "✅ 通过"},
            {"SELECT * FROM users WHERE seller_id = ?", null, "Null seller_id parameter", "❌ 拦截"},
            {"SELECT * FROM users WHERE seller_id = ?", "", "Empty seller_id parameter", "❌ 拦截"},
            {"SELECT * FROM users WHERE seller_id IN (?, ?, ?)", new Object[]{null, "", "valid"}, "Mixed parameters with valid", "✅ 通过"},
            {"SELECT * FROM users WHERE seller_id IN (?, ?, ?)", new Object[]{null, "", "   "}, "All invalid parameters", "❌ 拦截"}
        };

        for (Object[] testCase : testCases) {
            String sql = (String) testCase[0];
            Object paramValue = testCase[1];
            String description = (String) testCase[2];
            String expected = (String) testCase[3];

            System.out.println("  📝 " + description);
            System.out.println("     SQL: " + sql);
            System.out.println("     参数: " + formatParameter(paramValue));

            try {
                PreparedStatementProxy statement = createMockStatement(sql, paramValue);
                method.invoke(sqlFilter, statement);
                System.out.println("     结果: ✅ 通过" + (expected.contains("✅") ? " (符合预期)" : " (⚠️ 意外通过)"));
            } catch (Exception e) {
                if (e.getCause() != null && e.getCause().getMessage() != null) {
                    System.out.println("     结果: ❌ 拦截 - " + e.getCause().getMessage().substring(0, Math.min(50, e.getCause().getMessage().length())) + "..." + 
                                     (expected.contains("❌") ? " (符合预期)" : " (⚠️ 意外拦截)"));
                } else {
                    System.out.println("     结果: ❌ 拦截" + (expected.contains("❌") ? " (符合预期)" : " (⚠️ 意外拦截)"));
                }
            }
            System.out.println();
        }
    }

    private static void testWhitelistFunctionality(Method method, SfSqlFilter sqlFilter) {
        // 模拟添加白名单
        System.out.println("  📝 添加白名单SQL测试");
        String whitelistSql = "SELECT * FROM users WHERE seller_id IS NULL";
        System.out.println("     白名单SQL: " + whitelistSql);
        
        // 注意：这里只是演示，实际的白名单配置需要通过配置文件
        System.out.println("     结果: ✅ 白名单SQL会跳过所有检查 (需要配置文件支持)");
        System.out.println();
    }

    private static void testStrategyToggle(Method method, SfSqlFilter sqlFilter) {
        System.out.println("  📝 策略开关演示");
        System.out.println("     当前配置: PUID检查=启用, seller_id检查=启用");
        System.out.println("     如果禁用PUID检查: UPDATE语句无需puid条件");
        System.out.println("     如果禁用seller_id检查: SELECT语句无需seller_id条件");
        System.out.println("     结果: ✅ 策略可以通过配置灵活控制");
        System.out.println();
    }

    private static SfSqlFilter createConfiguredSqlFilter() throws Exception {
        // 创建模拟配置
        DynamicRefreshNacosConfiguration mockConfig = Mockito.mock(DynamicRefreshNacosConfiguration.class);
        when(mockConfig.getDruidFilterWhiteSqls()).thenReturn(new HashSet<>());

        SqlConfigUtil mockSqlConfigUtil = Mockito.mock(SqlConfigUtil.class);
        when(mockSqlConfigUtil.getLogEnable()).thenReturn(false);
        when(mockSqlConfigUtil.getStatEnable()).thenReturn(true);
        when(mockSqlConfigUtil.getThrowError()).thenReturn(true);
        when(mockSqlConfigUtil.getSellerIdCheckEnable()).thenReturn(true);
        when(mockSqlConfigUtil.getSellerIdExecutionCheckEnable()).thenReturn(true);
        when(mockSqlConfigUtil.getSellerIdThrowError()).thenReturn(true);

        Set<String> targetTables = new HashSet<>();
        targetTables.add("users");
        targetTables.add("orders");
        when(mockSqlConfigUtil.getSellerIdCheckTablesSet()).thenReturn(targetTables);

        // 创建 SfSqlFilter 并注入依赖
        SfSqlFilter sqlFilter = new SfSqlFilter();

        java.lang.reflect.Field configField = SfSqlFilter.class.getDeclaredField("dynamicRefreshNacosConfiguration");
        configField.setAccessible(true);
        configField.set(sqlFilter, mockConfig);

        java.lang.reflect.Field utilField = SfSqlFilter.class.getDeclaredField("sqlConfigUtil");
        utilField.setAccessible(true);
        utilField.set(sqlFilter, mockSqlConfigUtil);

        return sqlFilter;
    }

    private static PreparedStatementProxy createMockStatement(String sql, Object paramValue) {
        PreparedStatementProxy statement = Mockito.mock(PreparedStatementProxy.class);
        when(statement.getSql()).thenReturn(sql);

        Map<Integer, JdbcParameter> params = new HashMap<>();
        if (paramValue instanceof Object[]) {
            Object[] values = (Object[]) paramValue;
            for (int i = 0; i < values.length; i++) {
                JdbcParameter param = new JdbcParameter();
                param.setValue(values[i]);
                params.put(i, param);
            }
        } else {
            JdbcParameter param = new JdbcParameter();
            param.setValue(paramValue);
            params.put(0, param);
        }

        when(statement.getParameters()).thenReturn(params);
        return statement;
    }

    private static String formatParameter(Object paramValue) {
        if (paramValue == null) {
            return "null";
        } else if (paramValue instanceof Object[]) {
            Object[] values = (Object[]) paramValue;
            StringBuilder sb = new StringBuilder("[");
            for (int i = 0; i < values.length; i++) {
                if (i > 0) sb.append(", ");
                if (values[i] == null) {
                    sb.append("null");
                } else if (values[i] instanceof String) {
                    sb.append("'").append(values[i]).append("'");
                } else {
                    sb.append(values[i]);
                }
            }
            sb.append("]");
            return sb.toString();
        } else if (paramValue instanceof String) {
            return "'" + paramValue + "'";
        } else {
            return paramValue.toString();
        }
    }
}
