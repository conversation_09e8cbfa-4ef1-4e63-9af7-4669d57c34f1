package com.meiyunji.sponsored.filter;

import com.alibaba.druid.proxy.jdbc.PreparedStatementProxy;
import com.meiyunji.sponsored.common.filter.ParameterValueValidator;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

/**
 * 执行阶段拦截测试
 * 测试参数值的实际验证功能
 */
public class ExecutionStageInterceptTest {

    @Test
    public void testValidParameterValues() {
        // 测试有效的参数值
        String sql = "SELECT * FROM users WHERE seller_id = ?";
        List<Object> validParameters = Arrays.asList("valid_seller_123");
        
        PreparedStatementProxy statement = createMockStatement(sql, validParameters);
        Set<String> targetTables = createTargetTables();
        
        boolean result = ParameterValueValidator.validateSellerIdParameters(statement, targetTables);
        assertTrue(result, "有效的seller_id参数值应该通过验证");
    }

    @Test
    public void testInvalidParameterValues() {
        // 测试无效的参数值
        String[] invalidValues = {null, "", "   "};
        String sql = "SELECT * FROM users WHERE seller_id = ?";
        Set<String> targetTables = createTargetTables();
        
        for (Object invalidValue : invalidValues) {
            List<Object> parameters = Arrays.asList(invalidValue);
            PreparedStatementProxy statement = createMockStatement(sql, parameters);
            
            boolean result = ParameterValueValidator.validateSellerIdParameters(statement, targetTables);
            assertFalse(result, "无效的seller_id参数值应该被拦截: " + invalidValue);
        }
    }

    @Test
    public void testInParameterValues() {
        // 测试 IN 查询的参数值
        String sql = "SELECT * FROM users WHERE seller_id IN (?, ?, ?)";
        Set<String> targetTables = createTargetTables();
        
        // 测试包含有效值的情况
        List<Object> mixedParameters = Arrays.asList(null, "", "valid_seller");
        PreparedStatementProxy statement = createMockStatement(sql, mixedParameters);
        
        boolean result = ParameterValueValidator.validateSellerIdParameters(statement, targetTables);
        assertTrue(result, "IN查询包含有效值应该通过验证");
        
        // 测试全部无效值的情况
        List<Object> invalidParameters = Arrays.asList(null, "", "   ");
        statement = createMockStatement(sql, invalidParameters);
        
        result = ParameterValueValidator.validateSellerIdParameters(statement, targetTables);
        assertFalse(result, "IN查询全部无效值应该被拦截");
    }

    @Test
    public void testNotEqualParameterValues() {
        // 测试不等于操作符
        String sql = "SELECT * FROM users WHERE seller_id != ?";
        Set<String> targetTables = createTargetTables();
        
        // seller_id != null 表示有有效值
        List<Object> parameters = Arrays.asList((Object) null);
        PreparedStatementProxy statement = createMockStatement(sql, parameters);
        
        boolean result = ParameterValueValidator.validateSellerIdParameters(statement, targetTables);
        assertTrue(result, "seller_id != null 应该表示有有效值");
        
        // seller_id != 'valid_value' 不能确保有有效值
        parameters = Arrays.asList("valid_value");
        statement = createMockStatement(sql, parameters);
        
        result = ParameterValueValidator.validateSellerIdParameters(statement, targetTables);
        assertFalse(result, "seller_id != 'valid_value' 不能确保有有效值");
    }

    @Test
    public void testNonTargetTables() {
        // 测试非目标表
        String sql = "SELECT * FROM logs WHERE seller_id = ?";
        List<Object> parameters = Arrays.asList((Object) null);
        
        PreparedStatementProxy statement = createMockStatement(sql, parameters);
        Set<String> targetTables = createTargetTables(); // 不包含 logs 表
        
        boolean result = ParameterValueValidator.validateSellerIdParameters(statement, targetTables);
        assertTrue(result, "非目标表的查询应该跳过检查");
    }

    @Test
    public void testMixedLiteralAndParameterValues() {
        // 测试混合字面值和参数值
        String sql = "SELECT * FROM users WHERE seller_id IN ('literal_value', ?, ?)";
        Set<String> targetTables = createTargetTables();
        
        // 即使参数值无效，但有字面值有效
        List<Object> parameters = Arrays.asList(null, "");
        PreparedStatementProxy statement = createMockStatement(sql, parameters);
        
        boolean result = ParameterValueValidator.validateSellerIdParameters(statement, targetTables);
        assertTrue(result, "包含有效字面值的查询应该通过验证");
    }

    @Test
    public void testComplexQuery() {
        // 测试复杂查询
        String sql = "SELECT u.*, o.order_id FROM users u " +
                "JOIN orders o ON u.id = o.user_id " +
                "WHERE u.seller_id = ? AND o.status = ?";
        
        Set<String> targetTables = createTargetTables();
        
        // 有效的seller_id参数
        List<Object> validParameters = Arrays.asList("valid_seller", "completed");
        PreparedStatementProxy statement = createMockStatement(sql, validParameters);
        
        boolean result = ParameterValueValidator.validateSellerIdParameters(statement, targetTables);
        assertTrue(result, "复杂查询中有效的seller_id应该通过验证");
        
        // 无效的seller_id参数
        List<Object> invalidParameters = Arrays.asList(null, "completed");
        statement = createMockStatement(sql, invalidParameters);
        
        result = ParameterValueValidator.validateSellerIdParameters(statement, targetTables);
        assertFalse(result, "复杂查询中无效的seller_id应该被拦截");
    }

    @Test
    public void testParameterIndexMapping() {
        // 测试参数索引映射
        String sql = "SELECT * FROM users WHERE name = ? AND seller_id = ? AND status = ?";
        Set<String> targetTables = createTargetTables();
        
        // seller_id 是第二个参数
        List<Object> parameters = Arrays.asList("john", "valid_seller", "active");
        PreparedStatementProxy statement = createMockStatement(sql, parameters);
        
        boolean result = ParameterValueValidator.validateSellerIdParameters(statement, targetTables);
        assertTrue(result, "正确的参数索引映射应该通过验证");
        
        // seller_id 参数无效
        parameters = Arrays.asList("john", null, "active");
        statement = createMockStatement(sql, parameters);
        
        result = ParameterValueValidator.validateSellerIdParameters(statement, targetTables);
        assertFalse(result, "无效的seller_id参数应该被拦截");
    }

    /**
     * 创建模拟的PreparedStatementProxy
     */
    private PreparedStatementProxy createMockStatement(String sql, List<Object> parameters) {
        PreparedStatementProxy statement = Mockito.mock(PreparedStatementProxy.class);
        when(statement.getSql()).thenReturn(sql);
        when(statement.getParameters()).thenReturn(parameters);
        return statement;
    }

    /**
     * 创建目标表集合
     */
    private Set<String> createTargetTables() {
        Set<String> tables = new HashSet<>();
        tables.add("users");
        tables.add("orders");
        tables.add("products");
        return tables;
    }

    @Test
    public void testPerformanceComparison() {
        // 性能对比测试
        String sql = "SELECT * FROM users WHERE seller_id = ?";
        List<Object> parameters = Arrays.asList("valid_seller");
        PreparedStatementProxy statement = createMockStatement(sql, parameters);
        Set<String> targetTables = createTargetTables();
        
        // 预热
        for (int i = 0; i < 100; i++) {
            ParameterValueValidator.validateSellerIdParameters(statement, targetTables);
        }
        
        // 性能测试
        long startTime = System.currentTimeMillis();
        for (int i = 0; i < 10000; i++) {
            ParameterValueValidator.validateSellerIdParameters(statement, targetTables);
        }
        long endTime = System.currentTimeMillis();
        
        System.out.println("执行10000次参数验证耗时: " + (endTime - startTime) + "ms");
        assertTrue((endTime - startTime) < 1000, "参数验证性能应该足够好");
    }
}
