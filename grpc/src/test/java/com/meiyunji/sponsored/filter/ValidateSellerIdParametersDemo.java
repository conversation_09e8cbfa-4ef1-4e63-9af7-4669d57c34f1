package com.meiyunji.sponsored.filter;

import com.alibaba.druid.proxy.jdbc.JdbcParameter;
import com.alibaba.druid.proxy.jdbc.PreparedStatementProxy;
import com.meiyunji.sponsored.common.filter.SfSqlFilter;
import org.mockito.Mockito;

import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

import static org.mockito.Mockito.when;

/**
 * validateSellerIdParameters 修复效果演示
 */
public class ValidateSellerIdParametersDemo {

    public static void main(String[] args) {
        System.out.println("=== validateSellerIdParameters 修复效果演示 ===\n");

        try {
            SfSqlFilter sqlFilter = new SfSqlFilter();
            Method method = SfSqlFilter.class.getDeclaredMethod("validateSellerIdParameters", 
                    PreparedStatementProxy.class, Set.class);
            method.setAccessible(true);

            Set<String> targetTables = new HashSet<>();
            targetTables.add("users");

            System.out.println("目标表: " + targetTables + "\n");

            // 测试案例
            testCase(method, sqlFilter, targetTables, 
                    "基础等值查询 - 有效参数",
                    "SELECT * FROM users WHERE seller_id = ?",
                    createParams("valid_seller"));

            testCase(method, sqlFilter, targetTables,
                    "基础等值查询 - null参数",
                    "SELECT * FROM users WHERE seller_id = ?",
                    createParams((String) null));

            testCase(method, sqlFilter, targetTables,
                    "基础等值查询 - 空字符串参数",
                    "SELECT * FROM users WHERE seller_id = ?",
                    createParams(""));

            testCase(method, sqlFilter, targetTables,
                    "多参数查询 - seller_id有效",
                    "SELECT * FROM users WHERE name = ? AND seller_id = ? AND status = ?",
                    createParams("john", "valid_seller", "active"));

            testCase(method, sqlFilter, targetTables,
                    "多参数查询 - seller_id无效",
                    "SELECT * FROM users WHERE name = ? AND seller_id = ? AND status = ?",
                    createParams("john", null, "active"));

            testCase(method, sqlFilter, targetTables,
                    "IN查询 - 包含有效值",
                    "SELECT * FROM users WHERE seller_id IN (?, ?, ?)",
                    createParams(null, "", "valid_seller"));

            testCase(method, sqlFilter, targetTables,
                    "IN查询 - 全部无效值",
                    "SELECT * FROM users WHERE seller_id IN (?, ?, ?)",
                    createParams(null, "", "   "));

            testCase(method, sqlFilter, targetTables,
                    "非seller_id字段查询",
                    "SELECT * FROM users WHERE name = ? AND status = ?",
                    createParams(null, ""));

            testCase(method, sqlFilter, targetTables,
                    "非目标表查询",
                    "SELECT * FROM logs WHERE seller_id = ?",
                    createParams((String) null));

            testCase(method, sqlFilter, targetTables,
                    "复杂查询 - seller_id在中间位置",
                    "SELECT * FROM users WHERE (name = ? OR email = ?) AND seller_id = ? AND status IN (?, ?)",
                    createParams("john", "<EMAIL>", "valid_seller", "active", "pending"));

            testCase(method, sqlFilter, targetTables,
                    "复杂查询 - seller_id无效",
                    "SELECT * FROM users WHERE (name = ? OR email = ?) AND seller_id = ? AND status IN (?, ?)",
                    createParams("john", "<EMAIL>", null, "active", "pending"));

        } catch (Exception e) {
            System.err.println("❌ 演示失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private static void testCase(Method method, SfSqlFilter sqlFilter, Set<String> targetTables,
                                String description, String sql, Map<Integer, JdbcParameter> params) {
        System.out.println("📋 " + description);
        System.out.println("   SQL: " + sql);
        System.out.println("   参数: " + formatParameters(params));

        try {
            PreparedStatementProxy statement = createMockStatement(sql, params);
            Boolean result = (Boolean) method.invoke(sqlFilter, statement, targetTables);

            if (result) {
                System.out.println("   结果: ✅ 通过 - seller_id参数有效");
            } else {
                System.out.println("   结果: ❌ 拦截 - seller_id参数无效");
            }

        } catch (Exception e) {
            System.out.println("   结果: ⚠️  异常 - " + e.getMessage());
        }

        System.out.println();
    }

    private static Map<Integer, JdbcParameter> createParams(Object... values) {
        Map<Integer, JdbcParameter> params = new HashMap<>();
        for (int i = 0; i < values.length; i++) {
            JdbcParameter param = new JdbcParameter();
            param.setValue(values[i]);
            params.put(i, param);
        }
        return params;
    }

    private static PreparedStatementProxy createMockStatement(String sql, Map<Integer, JdbcParameter> params) {
        PreparedStatementProxy statement = Mockito.mock(PreparedStatementProxy.class);
        when(statement.getSql()).thenReturn(sql);
        when(statement.getParameters()).thenReturn(params);
        return statement;
    }

    private static String formatParameters(Map<Integer, JdbcParameter> params) {
        StringBuilder sb = new StringBuilder("[");
        for (int i = 0; i < params.size(); i++) {
            if (i > 0) sb.append(", ");
            JdbcParameter param = params.get(i);
            Object value = param != null ? param.getValue() : null;
            if (value == null) {
                sb.append("null");
            } else if (value instanceof String) {
                sb.append("'").append(value).append("'");
            } else {
                sb.append(value);
            }
        }
        sb.append("]");
        return sb.toString();
    }
}
