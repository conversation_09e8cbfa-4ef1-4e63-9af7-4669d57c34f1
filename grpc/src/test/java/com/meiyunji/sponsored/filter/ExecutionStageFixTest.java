package com.meiyunji.sponsored.filter;

import com.alibaba.druid.proxy.jdbc.PreparedStatementProxy;
import com.meiyunji.sponsored.common.config.DynamicRefreshNacosConfiguration;
import com.meiyunji.sponsored.common.exception.BizServiceException;
import com.meiyunji.sponsored.common.filter.SfSqlFilter;
import com.meiyunji.sponsored.common.util.SqlConfigUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

/**
 * 执行阶段修复测试
 * 验证 buildExecutionSql 方法修复后的功能
 */
public class ExecutionStageFixTest {

    @Mock
    private DynamicRefreshNacosConfiguration dynamicRefreshNacosConfiguration;

    @Mock
    private SqlConfigUtil sqlConfigUtil;

    @Mock
    private PreparedStatementProxy preparedStatementProxy;

    private SfSqlFilter sqlFilter;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        
        // 设置模拟对象的行为
        when(dynamicRefreshNacosConfiguration.getDruidFilterWhiteSqls()).thenReturn(new HashSet<>());
        
        Set<String> targetTables = new HashSet<>();
        targetTables.add("users");
        targetTables.add("orders");
        when(sqlConfigUtil.getSellerIdCheckTablesSet()).thenReturn(targetTables);
        
        // 启用执行阶段检查
        when(sqlConfigUtil.getSellerIdCheckEnable()).thenReturn(true);
        when(sqlConfigUtil.getSellerIdExecutionCheckEnable()).thenReturn(true);
        when(sqlConfigUtil.getSellerIdThrowError()).thenReturn(true);

        // 创建 SfSqlFilter 实例并注入依赖
        sqlFilter = new SfSqlFilter();
        
        // 通过反射设置私有字段
        try {
            java.lang.reflect.Field configField = SfSqlFilter.class.getDeclaredField("dynamicRefreshNacosConfiguration");
            configField.setAccessible(true);
            configField.set(sqlFilter, dynamicRefreshNacosConfiguration);

            java.lang.reflect.Field utilField = SfSqlFilter.class.getDeclaredField("sqlConfigUtil");
            utilField.setAccessible(true);
            utilField.set(sqlFilter, sqlConfigUtil);
        } catch (Exception e) {
            throw new RuntimeException("Failed to inject dependencies", e);
        }
    }

    @Test
    public void testBuildExecutionSql() throws Exception {
        // 通过反射调用私有方法
        Method method = SfSqlFilter.class.getDeclaredMethod("buildExecutionSql", String.class, List.class);
        method.setAccessible(true);
        
        // 测试基础替换
        String sql = "SELECT * FROM users WHERE seller_id = ? AND name = ?";
        List<Object> parameters = Arrays.asList("valid_seller", "john");
        
        String result = (String) method.invoke(sqlFilter, sql, parameters);
        
        assertNotNull(result);
        assertFalse(result.contains("?"));
        assertTrue(result.contains("'valid_seller'"));
        assertTrue(result.contains("'john'"));
        
        System.out.println("原始SQL: " + sql);
        System.out.println("参数: " + parameters);
        System.out.println("构建结果: " + result);
    }

    @Test
    public void testBuildExecutionSqlWithNullValues() throws Exception {
        Method method = SfSqlFilter.class.getDeclaredMethod("buildExecutionSql", String.class, List.class);
        method.setAccessible(true);
        
        // 测试null值替换
        String sql = "SELECT * FROM users WHERE seller_id = ? AND status = ?";
        List<Object> parameters = Arrays.asList(null, "active");
        
        String result = (String) method.invoke(sqlFilter, sql, parameters);
        
        assertNotNull(result);
        assertTrue(result.contains("NULL"));
        assertTrue(result.contains("'active'"));
        
        System.out.println("原始SQL: " + sql);
        System.out.println("参数: " + parameters);
        System.out.println("构建结果: " + result);
    }

    @Test
    public void testValidateSellerIdParameters() throws Exception {
        // 通过反射调用私有方法
        Method method = SfSqlFilter.class.getDeclaredMethod("validateSellerIdParameters", 
                PreparedStatementProxy.class, Set.class);
        method.setAccessible(true);
        
        Set<String> targetTables = new HashSet<>();
        targetTables.add("users");
        
        // 测试有效参数
        when(preparedStatementProxy.getSql()).thenReturn("SELECT * FROM users WHERE seller_id = ?");
        when(preparedStatementProxy.getParameters()).thenReturn(Arrays.asList("valid_seller"));
        
        Boolean result = (Boolean) method.invoke(sqlFilter, preparedStatementProxy, targetTables);
        assertTrue(result, "有效的seller_id参数应该通过验证");
        
        // 测试无效参数
        when(preparedStatementProxy.getParameters()).thenReturn(Arrays.asList((Object) null));
        
        result = (Boolean) method.invoke(sqlFilter, preparedStatementProxy, targetTables);
        assertFalse(result, "null的seller_id参数应该被拦截");
        
        // 测试空字符串参数
        when(preparedStatementProxy.getParameters()).thenReturn(Arrays.asList(""));
        
        result = (Boolean) method.invoke(sqlFilter, preparedStatementProxy, targetTables);
        assertFalse(result, "空字符串的seller_id参数应该被拦截");
    }

    @Test
    public void testIsValidParameterValue() throws Exception {
        // 通过反射调用私有方法
        Method method = SfSqlFilter.class.getDeclaredMethod("isValidParameterValue", Object.class);
        method.setAccessible(true);
        
        // 测试各种参数值
        assertTrue((Boolean) method.invoke(sqlFilter, "valid_value"));
        assertTrue((Boolean) method.invoke(sqlFilter, 123));
        assertTrue((Boolean) method.invoke(sqlFilter, true));
        
        assertFalse((Boolean) method.invoke(sqlFilter, (Object) null));
        assertFalse((Boolean) method.invoke(sqlFilter, ""));
        assertFalse((Boolean) method.invoke(sqlFilter, "   "));
    }

    @Test
    public void testConvertParameterToString() throws Exception {
        // 通过反射调用私有方法
        Method method = SfSqlFilter.class.getDeclaredMethod("convertParameterToString", Object.class);
        method.setAccessible(true);
        
        // 测试各种类型转换
        assertEquals("NULL", method.invoke(sqlFilter, (Object) null));
        assertEquals("'test'", method.invoke(sqlFilter, "test"));
        assertEquals("'test''s'", method.invoke(sqlFilter, "test's")); // 单引号转义
        assertEquals("123", method.invoke(sqlFilter, 123));
        assertEquals("true", method.invoke(sqlFilter, true));
    }

    @Test
    public void testExecutionStageIntegration() {
        // 集成测试：模拟完整的执行阶段拦截流程
        
        // 设置有效的SQL和参数
        when(preparedStatementProxy.getSql()).thenReturn("SELECT * FROM users WHERE seller_id = ?");
        when(preparedStatementProxy.getParameters()).thenReturn(Arrays.asList("valid_seller"));
        
        // 这个调用应该不抛出异常
        assertDoesNotThrow(() -> {
            try {
                Method method = SfSqlFilter.class.getDeclaredMethod("interceptExecutionSql", PreparedStatementProxy.class);
                method.setAccessible(true);
                method.invoke(sqlFilter, preparedStatementProxy);
            } catch (Exception e) {
                if (e.getCause() instanceof BizServiceException) {
                    throw (BizServiceException) e.getCause();
                }
                throw new RuntimeException(e);
            }
        });
        
        // 设置无效的参数
        when(preparedStatementProxy.getParameters()).thenReturn(Arrays.asList((Object) null));
        
        // 这个调用应该抛出异常
        assertThrows(BizServiceException.class, () -> {
            try {
                Method method = SfSqlFilter.class.getDeclaredMethod("interceptExecutionSql", PreparedStatementProxy.class);
                method.setAccessible(true);
                method.invoke(sqlFilter, preparedStatementProxy);
            } catch (Exception e) {
                if (e.getCause() instanceof BizServiceException) {
                    throw (BizServiceException) e.getCause();
                }
                throw new RuntimeException(e);
            }
        });
    }

    @Test
    public void testNonTargetTableSkip() throws Exception {
        // 测试非目标表跳过检查
        Method method = SfSqlFilter.class.getDeclaredMethod("validateSellerIdParameters", 
                PreparedStatementProxy.class, Set.class);
        method.setAccessible(true);
        
        Set<String> targetTables = new HashSet<>();
        targetTables.add("users"); // 只包含users表
        
        // 查询logs表（非目标表）
        when(preparedStatementProxy.getSql()).thenReturn("SELECT * FROM logs WHERE seller_id = ?");
        when(preparedStatementProxy.getParameters()).thenReturn(Arrays.asList((Object) null));
        
        Boolean result = (Boolean) method.invoke(sqlFilter, preparedStatementProxy, targetTables);
        assertTrue(result, "非目标表的查询应该跳过检查");
    }
}
