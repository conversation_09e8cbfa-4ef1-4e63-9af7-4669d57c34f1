package com.meiyunji.sponsored.filter;

import com.alibaba.druid.proxy.jdbc.JdbcParameter;
import com.alibaba.druid.proxy.jdbc.PreparedStatementProxy;
import com.meiyunji.sponsored.common.config.DynamicRefreshNacosConfiguration;
import com.meiyunji.sponsored.common.exception.BizServiceException;
import com.meiyunji.sponsored.common.filter.SfSqlFilter;
import com.meiyunji.sponsored.common.util.SqlConfigUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

/**
 * 统一拦截框架测试
 * 验证重构后的 SfSqlFilter 统一拦截逻辑
 */
public class UnifiedInterceptFrameworkTest {

    @Mock
    private DynamicRefreshNacosConfiguration dynamicRefreshNacosConfiguration;

    @Mock
    private SqlConfigUtil sqlConfigUtil;

    @Mock
    private PreparedStatementProxy preparedStatementProxy;

    private SfSqlFilter sqlFilter;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        
        // 设置基础配置
        when(dynamicRefreshNacosConfiguration.getDruidFilterWhiteSqls()).thenReturn(new HashSet<>());
        
        // 设置 PUID 检查配置
        when(sqlConfigUtil.getStatEnable()).thenReturn(true);
        when(sqlConfigUtil.getThrowError()).thenReturn(true);
        
        // 设置 seller_id 检查配置
        when(sqlConfigUtil.getSellerIdCheckEnable()).thenReturn(true);
        when(sqlConfigUtil.getSellerIdExecutionCheckEnable()).thenReturn(true);
        when(sqlConfigUtil.getSellerIdThrowError()).thenReturn(true);
        
        Set<String> targetTables = new HashSet<>();
        targetTables.add("users");
        targetTables.add("orders");
        when(sqlConfigUtil.getSellerIdCheckTablesSet()).thenReturn(targetTables);

        // 创建 SfSqlFilter 实例并注入依赖
        sqlFilter = new SfSqlFilter();
        
        try {
            java.lang.reflect.Field configField = SfSqlFilter.class.getDeclaredField("dynamicRefreshNacosConfiguration");
            configField.setAccessible(true);
            configField.set(sqlFilter, dynamicRefreshNacosConfiguration);

            java.lang.reflect.Field utilField = SfSqlFilter.class.getDeclaredField("sqlConfigUtil");
            utilField.setAccessible(true);
            utilField.set(sqlFilter, sqlConfigUtil);
        } catch (Exception e) {
            throw new RuntimeException("Failed to inject dependencies", e);
        }
    }

    @Test
    public void testCompileTimeInterception() throws Exception {
        Method method = SfSqlFilter.class.getDeclaredMethod("interceptCompileTime", String.class);
        method.setAccessible(true);

        // 测试 PUID 检查 - UPDATE 语句缺少 puid
        assertThrows(BizServiceException.class, () -> {
            try {
                method.invoke(sqlFilter, "UPDATE users SET name = 'test'");
            } catch (Exception e) {
                if (e.getCause() instanceof BizServiceException) {
                    throw (BizServiceException) e.getCause();
                }
                throw e;
            }
        }, "UPDATE语句缺少puid应该被拦截");

        // 测试 PUID 检查 - UPDATE 语句包含 puid
        assertDoesNotThrow(() -> {
            try {
                method.invoke(sqlFilter, "UPDATE users SET name = 'test' WHERE puid = 123");
            } catch (Exception e) {
                if (e.getCause() instanceof BizServiceException) {
                    throw (BizServiceException) e.getCause();
                }
                throw e;
            }
        }, "UPDATE语句包含puid应该通过");

        // 测试 seller_id 检查 - SELECT 语句缺少 seller_id
        assertThrows(BizServiceException.class, () -> {
            try {
                method.invoke(sqlFilter, "SELECT * FROM users WHERE name = 'test'");
            } catch (Exception e) {
                if (e.getCause() instanceof BizServiceException) {
                    throw (BizServiceException) e.getCause();
                }
                throw e;
            }
        }, "SELECT语句缺少seller_id应该被拦截");

        // 测试 seller_id 检查 - SELECT 语句包含有效 seller_id
        assertDoesNotThrow(() -> {
            try {
                method.invoke(sqlFilter, "SELECT * FROM users WHERE seller_id = 'valid_seller'");
            } catch (Exception e) {
                if (e.getCause() instanceof BizServiceException) {
                    throw (BizServiceException) e.getCause();
                }
                throw e;
            }
        }, "SELECT语句包含有效seller_id应该通过");
    }

    @Test
    public void testExecutionTimeInterception() throws Exception {
        Method method = SfSqlFilter.class.getDeclaredMethod("interceptExecutionTime", PreparedStatementProxy.class);
        method.setAccessible(true);

        // 测试 seller_id 执行阶段检查 - 有效参数
        when(preparedStatementProxy.getSql()).thenReturn("SELECT * FROM users WHERE seller_id = ?");
        Map<Integer, JdbcParameter> validParams = createParameterMap("valid_seller");
        when(preparedStatementProxy.getParameters()).thenReturn(validParams);

        assertDoesNotThrow(() -> {
            try {
                method.invoke(sqlFilter, preparedStatementProxy);
            } catch (Exception e) {
                if (e.getCause() instanceof BizServiceException) {
                    throw (BizServiceException) e.getCause();
                }
                throw e;
            }
        }, "有效的seller_id参数应该通过执行阶段检查");

        // 测试 seller_id 执行阶段检查 - 无效参数
        Map<Integer, JdbcParameter> invalidParams = createParameterMap((String) null);
        when(preparedStatementProxy.getParameters()).thenReturn(invalidParams);

        assertThrows(BizServiceException.class, () -> {
            try {
                method.invoke(sqlFilter, preparedStatementProxy);
            } catch (Exception e) {
                if (e.getCause() instanceof BizServiceException) {
                    throw (BizServiceException) e.getCause();
                }
                throw e;
            }
        }, "无效的seller_id参数应该被执行阶段拦截");
    }

    @Test
    public void testWhitelistBypass() throws Exception {
        // 设置白名单
        Set<String> whitelist = new HashSet<>();
        String whitelistSql = "SELECT * FROM users WHERE seller_id IS NULL";
        whitelist.add(whitelistSql);
        when(dynamicRefreshNacosConfiguration.getDruidFilterWhiteSqls()).thenReturn(whitelist);

        Method compileMethod = SfSqlFilter.class.getDeclaredMethod("interceptCompileTime", String.class);
        compileMethod.setAccessible(true);

        // 白名单SQL应该跳过所有检查
        assertDoesNotThrow(() -> {
            try {
                compileMethod.invoke(sqlFilter, whitelistSql);
            } catch (Exception e) {
                if (e.getCause() instanceof BizServiceException) {
                    throw (BizServiceException) e.getCause();
                }
                throw e;
            }
        }, "白名单SQL应该跳过所有检查");
    }

    @Test
    public void testStrategyEnableDisable() throws Exception {
        Method method = SfSqlFilter.class.getDeclaredMethod("interceptCompileTime", String.class);
        method.setAccessible(true);

        // 禁用 PUID 检查
        when(sqlConfigUtil.getStatEnable()).thenReturn(false);

        // 现在 UPDATE 语句缺少 puid 应该通过
        assertDoesNotThrow(() -> {
            try {
                method.invoke(sqlFilter, "UPDATE users SET name = 'test'");
            } catch (Exception e) {
                if (e.getCause() instanceof BizServiceException) {
                    throw (BizServiceException) e.getCause();
                }
                throw e;
            }
        }, "禁用PUID检查后，UPDATE语句缺少puid应该通过");

        // 禁用 seller_id 检查
        when(sqlConfigUtil.getSellerIdCheckEnable()).thenReturn(false);

        // 现在 SELECT 语句缺少 seller_id 应该通过
        assertDoesNotThrow(() -> {
            try {
                method.invoke(sqlFilter, "SELECT * FROM users WHERE name = 'test'");
            } catch (Exception e) {
                if (e.getCause() instanceof BizServiceException) {
                    throw (BizServiceException) e.getCause();
                }
                throw e;
            }
        }, "禁用seller_id检查后，SELECT语句缺少seller_id应该通过");
    }

    @Test
    public void testMixedSqlTypes() throws Exception {
        Method method = SfSqlFilter.class.getDeclaredMethod("interceptCompileTime", String.class);
        method.setAccessible(true);

        // 测试各种SQL类型的组合检查
        String[] testCases = {
            // SELECT - 需要 seller_id 检查
            "SELECT * FROM users WHERE seller_id = 'valid'", // 应该通过
            
            // UPDATE - 需要 puid 检查
            "UPDATE users SET name = 'test' WHERE puid = 123", // 应该通过
            
            // DELETE - 需要 puid 检查
            "DELETE FROM users WHERE puid = 123", // 应该通过
            
            // INSERT - 不需要特殊检查
            "INSERT INTO users (name) VALUES ('test')" // 应该通过
        };

        for (String sql : testCases) {
            assertDoesNotThrow(() -> {
                try {
                    method.invoke(sqlFilter, sql);
                } catch (Exception e) {
                    if (e.getCause() instanceof BizServiceException) {
                        throw (BizServiceException) e.getCause();
                    }
                    throw e;
                }
            }, "有效的SQL应该通过检查: " + sql);
        }
    }

    @Test
    public void testErrorMessageAndLogging() throws Exception {
        Method method = SfSqlFilter.class.getDeclaredMethod("interceptCompileTime", String.class);
        method.setAccessible(true);

        // 测试 PUID 错误消息
        BizServiceException puidException = assertThrows(BizServiceException.class, () -> {
            try {
                method.invoke(sqlFilter, "UPDATE users SET name = 'test'");
            } catch (Exception e) {
                if (e.getCause() instanceof BizServiceException) {
                    throw (BizServiceException) e.getCause();
                }
                throw e;
            }
        });
        assertTrue(puidException.getMessage().contains("puid"), 
                "PUID错误消息应该包含puid关键字");

        // 测试 seller_id 错误消息
        BizServiceException sellerIdException = assertThrows(BizServiceException.class, () -> {
            try {
                method.invoke(sqlFilter, "SELECT * FROM users WHERE name = 'test'");
            } catch (Exception e) {
                if (e.getCause() instanceof BizServiceException) {
                    throw (BizServiceException) e.getCause();
                }
                throw e;
            }
        });
        assertTrue(sellerIdException.getMessage().contains("sellerId"), 
                "seller_id错误消息应该包含sellerId关键字");
    }

    /**
     * 创建参数映射
     */
    private Map<Integer, JdbcParameter> createParameterMap(String value) {
        Map<Integer, JdbcParameter> params = new HashMap<>();
        JdbcParameter param = new JdbcParameter();
        param.setValue(value);
        params.put(0, param);
        return params;
    }
}
