package com.meiyunji.sponsored.filter;

import com.alibaba.druid.proxy.jdbc.JdbcParameter;
import com.alibaba.druid.proxy.jdbc.PreparedStatementProxy;
import com.meiyunji.sponsored.common.config.DynamicRefreshNacosConfiguration;
import com.meiyunji.sponsored.common.filter.SfSqlFilter;
import com.meiyunji.sponsored.common.util.SqlConfigUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

/**
 * validateSellerIdParameters 方法修复验证测试
 */
public class ValidateSellerIdParametersFixTest {

    @Mock
    private DynamicRefreshNacosConfiguration dynamicRefreshNacosConfiguration;

    @Mock
    private SqlConfigUtil sqlConfigUtil;

    @Mock
    private PreparedStatementProxy preparedStatementProxy;

    private SfSqlFilter sqlFilter;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        
        when(dynamicRefreshNacosConfiguration.getDruidFilterWhiteSqls()).thenReturn(new HashSet<>());
        
        Set<String> targetTables = new HashSet<>();
        targetTables.add("users");
        targetTables.add("orders");
        when(sqlConfigUtil.getSellerIdCheckTablesSet()).thenReturn(targetTables);

        sqlFilter = new SfSqlFilter();
        
        try {
            java.lang.reflect.Field configField = SfSqlFilter.class.getDeclaredField("dynamicRefreshNacosConfiguration");
            configField.setAccessible(true);
            configField.set(sqlFilter, dynamicRefreshNacosConfiguration);

            java.lang.reflect.Field utilField = SfSqlFilter.class.getDeclaredField("sqlConfigUtil");
            utilField.setAccessible(true);
            utilField.set(sqlFilter, sqlConfigUtil);
        } catch (Exception e) {
            throw new RuntimeException("Failed to inject dependencies", e);
        }
    }

    @Test
    public void testBasicSellerIdValidation() throws Exception {
        Method method = SfSqlFilter.class.getDeclaredMethod("validateSellerIdParameters", 
                PreparedStatementProxy.class, Set.class);
        method.setAccessible(true);
        
        Set<String> targetTables = new HashSet<>();
        targetTables.add("users");
        
        // 测试案例1：seller_id = ? 有效参数
        when(preparedStatementProxy.getSql()).thenReturn("SELECT * FROM users WHERE seller_id = ?");
        Map<Integer, JdbcParameter> validParams = createParameterMap("valid_seller");
        when(preparedStatementProxy.getParameters()).thenReturn(validParams);
        
        Boolean result = (Boolean) method.invoke(sqlFilter, preparedStatementProxy, targetTables);
        assertTrue(result, "有效的seller_id参数应该通过验证");
        
        // 测试案例2：seller_id = ? null参数
        Map<Integer, JdbcParameter> nullParams = createParameterMap((String) null);
        when(preparedStatementProxy.getParameters()).thenReturn(nullParams);
        
        result = (Boolean) method.invoke(sqlFilter, preparedStatementProxy, targetTables);
        assertFalse(result, "null的seller_id参数应该被拦截");
        
        // 测试案例3：seller_id = ? 空字符串参数
        Map<Integer, JdbcParameter> emptyParams = createParameterMap("");
        when(preparedStatementProxy.getParameters()).thenReturn(emptyParams);
        
        result = (Boolean) method.invoke(sqlFilter, preparedStatementProxy, targetTables);
        assertFalse(result, "空字符串的seller_id参数应该被拦截");
    }

    @Test
    public void testMultipleParametersValidation() throws Exception {
        Method method = SfSqlFilter.class.getDeclaredMethod("validateSellerIdParameters", 
                PreparedStatementProxy.class, Set.class);
        method.setAccessible(true);
        
        Set<String> targetTables = new HashSet<>();
        targetTables.add("users");
        
        // 测试案例：多个参数，seller_id是第二个参数
        String sql = "SELECT * FROM users WHERE name = ? AND seller_id = ? AND status = ?";
        when(preparedStatementProxy.getSql()).thenReturn(sql);
        
        // seller_id参数有效
        Map<Integer, JdbcParameter> params = new HashMap<>();
        params.put(0, createJdbcParameter("john"));      // name
        params.put(1, createJdbcParameter("valid_seller")); // seller_id (有效)
        params.put(2, createJdbcParameter("active"));    // status
        when(preparedStatementProxy.getParameters()).thenReturn(params);
        
        Boolean result = (Boolean) method.invoke(sqlFilter, preparedStatementProxy, targetTables);
        assertTrue(result, "seller_id参数有效时应该通过验证");
        
        // seller_id参数无效
        params.put(1, createJdbcParameter(null)); // seller_id (无效)
        when(preparedStatementProxy.getParameters()).thenReturn(params);
        
        result = (Boolean) method.invoke(sqlFilter, preparedStatementProxy, targetTables);
        assertFalse(result, "seller_id参数无效时应该被拦截");
    }

    @Test
    public void testInQueryValidation() throws Exception {
        Method method = SfSqlFilter.class.getDeclaredMethod("validateSellerIdParameters", 
                PreparedStatementProxy.class, Set.class);
        method.setAccessible(true);
        
        Set<String> targetTables = new HashSet<>();
        targetTables.add("users");
        
        // 测试 IN 查询
        String sql = "SELECT * FROM users WHERE seller_id IN (?, ?, ?)";
        when(preparedStatementProxy.getSql()).thenReturn(sql);
        
        // 包含有效值的情况
        Map<Integer, JdbcParameter> params = new HashMap<>();
        params.put(0, createJdbcParameter(null));
        params.put(1, createJdbcParameter(""));
        params.put(2, createJdbcParameter("valid_seller"));
        when(preparedStatementProxy.getParameters()).thenReturn(params);
        
        Boolean result = (Boolean) method.invoke(sqlFilter, preparedStatementProxy, targetTables);
        assertTrue(result, "IN查询包含有效值时应该通过验证");
        
        // 全部无效值的情况
        params.put(2, createJdbcParameter("   ")); // 改为空白字符串
        when(preparedStatementProxy.getParameters()).thenReturn(params);
        
        result = (Boolean) method.invoke(sqlFilter, preparedStatementProxy, targetTables);
        assertFalse(result, "IN查询全部无效值时应该被拦截");
    }

    @Test
    public void testNonSellerIdFieldIgnored() throws Exception {
        Method method = SfSqlFilter.class.getDeclaredMethod("validateSellerIdParameters", 
                PreparedStatementProxy.class, Set.class);
        method.setAccessible(true);
        
        Set<String> targetTables = new HashSet<>();
        targetTables.add("users");
        
        // 测试不包含seller_id字段的查询
        String sql = "SELECT * FROM users WHERE name = ? AND status = ?";
        when(preparedStatementProxy.getSql()).thenReturn(sql);
        
        Map<Integer, JdbcParameter> params = new HashMap<>();
        params.put(0, createJdbcParameter(null));  // name参数为null
        params.put(1, createJdbcParameter(""));    // status参数为空
        when(preparedStatementProxy.getParameters()).thenReturn(params);
        
        Boolean result = (Boolean) method.invoke(sqlFilter, preparedStatementProxy, targetTables);
        assertTrue(result, "不包含seller_id字段的查询应该通过验证");
    }

    @Test
    public void testNonTargetTableSkipped() throws Exception {
        Method method = SfSqlFilter.class.getDeclaredMethod("validateSellerIdParameters", 
                PreparedStatementProxy.class, Set.class);
        method.setAccessible(true);
        
        Set<String> targetTables = new HashSet<>();
        targetTables.add("users"); // 只包含users表
        
        // 查询logs表（非目标表）
        String sql = "SELECT * FROM logs WHERE seller_id = ?";
        when(preparedStatementProxy.getSql()).thenReturn(sql);
        
        Map<Integer, JdbcParameter> params = createParameterMap((String) null);
        when(preparedStatementProxy.getParameters()).thenReturn(params);
        
        Boolean result = (Boolean) method.invoke(sqlFilter, preparedStatementProxy, targetTables);
        assertTrue(result, "非目标表的查询应该跳过检查");
    }

    @Test
    public void testMixedLiteralAndParameterValues() throws Exception {
        Method method = SfSqlFilter.class.getDeclaredMethod("validateSellerIdParameters", 
                PreparedStatementProxy.class, Set.class);
        method.setAccessible(true);
        
        Set<String> targetTables = new HashSet<>();
        targetTables.add("users");
        
        // 测试混合字面值和参数值
        String sql = "SELECT * FROM users WHERE seller_id IN ('literal_valid', ?, ?)";
        when(preparedStatementProxy.getSql()).thenReturn(sql);
        
        // 即使参数值无效，但有字面值有效
        Map<Integer, JdbcParameter> params = new HashMap<>();
        params.put(0, createJdbcParameter(null));
        params.put(1, createJdbcParameter(""));
        when(preparedStatementProxy.getParameters()).thenReturn(params);
        
        Boolean result = (Boolean) method.invoke(sqlFilter, preparedStatementProxy, targetTables);
        assertTrue(result, "包含有效字面值的查询应该通过验证");
    }

    /**
     * 创建参数映射
     */
    private Map<Integer, JdbcParameter> createParameterMap(String value) {
        Map<Integer, JdbcParameter> params = new HashMap<>();
        params.put(0, createJdbcParameter(value));
        return params;
    }

    /**
     * 创建JdbcParameter
     */
    private JdbcParameter createJdbcParameter(Object value) {
        JdbcParameter param = new JdbcParameter();
        param.setValue(value);
        return param;
    }

    @Test
    public void testParameterIndexMapping() throws Exception {
        // 这个测试验证参数索引映射是否正确
        Method method = SfSqlFilter.class.getDeclaredMethod("validateSellerIdParameters", 
                PreparedStatementProxy.class, Set.class);
        method.setAccessible(true);
        
        Set<String> targetTables = new HashSet<>();
        targetTables.add("users");
        
        // 复杂查询：seller_id出现在不同位置
        String sql = "SELECT * FROM users WHERE (name = ? OR email = ?) AND seller_id = ? AND status IN (?, ?)";
        when(preparedStatementProxy.getSql()).thenReturn(sql);
        
        Map<Integer, JdbcParameter> params = new HashMap<>();
        params.put(0, createJdbcParameter("john"));        // name
        params.put(1, createJdbcParameter("<EMAIL>")); // email  
        params.put(2, createJdbcParameter("valid_seller")); // seller_id (第3个参数)
        params.put(3, createJdbcParameter("active"));      // status1
        params.put(4, createJdbcParameter("pending"));     // status2
        when(preparedStatementProxy.getParameters()).thenReturn(params);
        
        Boolean result = (Boolean) method.invoke(sqlFilter, preparedStatementProxy, targetTables);
        assertTrue(result, "复杂查询中正确位置的seller_id参数应该被正确验证");
        
        // 将seller_id参数改为无效值
        params.put(2, createJdbcParameter(null));
        when(preparedStatementProxy.getParameters()).thenReturn(params);
        
        result = (Boolean) method.invoke(sqlFilter, preparedStatementProxy, targetTables);
        assertFalse(result, "复杂查询中无效的seller_id参数应该被拦截");
    }
}
