package com.meiyunji.sponsored.filter;

import com.alibaba.druid.proxy.jdbc.PreparedStatementProxy;
import com.meiyunji.sponsored.common.filter.ParameterValueValidator;
import org.mockito.Mockito;

import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import static org.mockito.Mockito.when;

/**
 * 执行阶段拦截演示程序
 * 展示如何在SQL执行阶段检查实际参数值
 */
public class ExecutionStageDemo {

    public static void main(String[] args) {
        System.out.println("=== 执行阶段 seller_id 参数值拦截演示 ===\n");

        // 设置目标表
        Set<String> targetTables = new HashSet<>();
        targetTables.add("users");
        targetTables.add("orders");
        targetTables.add("products");

        System.out.println("目标表: " + targetTables + "\n");

        // 测试案例
        testCase("基础等值查询 - 有效参数", 
                "SELECT * FROM users WHERE seller_id = ?", 
                Arrays.asList("valid_seller_123"), targetTables);

        testCase("基础等值查询 - null参数", 
                "SELECT * FROM users WHERE seller_id = ?", 
                Arrays.asList((Object) null), targetTables);

        testCase("基础等值查询 - 空字符串参数", 
                "SELECT * FROM users WHERE seller_id = ?", 
                Arrays.asList(""), targetTables);

        testCase("IN查询 - 混合参数值", 
                "SELECT * FROM users WHERE seller_id IN (?, ?, ?)", 
                Arrays.asList(null, "", "valid_seller"), targetTables);

        testCase("IN查询 - 全部无效参数", 
                "SELECT * FROM users WHERE seller_id IN (?, ?, ?)", 
                Arrays.asList(null, "", "   "), targetTables);

        testCase("NOT IN查询 - 排除无效值", 
                "SELECT * FROM users WHERE seller_id NOT IN (?, ?)", 
                Arrays.asList(null, ""), targetTables);

        testCase("复杂查询 - 多个参数", 
                "SELECT * FROM users WHERE name = ? AND seller_id = ? AND status = ?", 
                Arrays.asList("john", "valid_seller", "active"), targetTables);

        testCase("复杂查询 - seller_id无效", 
                "SELECT * FROM users WHERE name = ? AND seller_id = ? AND status = ?", 
                Arrays.asList("john", null, "active"), targetTables);

        testCase("非目标表查询", 
                "SELECT * FROM logs WHERE seller_id = ?", 
                Arrays.asList((Object) null), targetTables);

        testCase("混合字面值和参数", 
                "SELECT * FROM users WHERE seller_id IN ('literal_valid', ?, ?)", 
                Arrays.asList(null, ""), targetTables);

        System.out.println("=== 演示完成 ===");
    }

    private static void testCase(String description, String sql, List<Object> parameters, Set<String> targetTables) {
        System.out.println("📋 " + description);
        System.out.println("   SQL: " + sql);
        System.out.println("   参数: " + parameters);

        try {
            PreparedStatementProxy statement = createMockStatement(sql, parameters);
            boolean result = ParameterValueValidator.validateSellerIdParameters(statement, targetTables);

            if (result) {
                System.out.println("   结果: ✅ 通过 - 参数值有效");
            } else {
                System.out.println("   结果: ❌ 拦截 - 参数值无效");
            }

        } catch (Exception e) {
            System.out.println("   结果: ⚠️  异常 - " + e.getMessage());
        }

        System.out.println();
    }

    private static PreparedStatementProxy createMockStatement(String sql, List<Object> parameters) {
        PreparedStatementProxy statement = Mockito.mock(PreparedStatementProxy.class);
        when(statement.getSql()).thenReturn(sql);
        when(statement.getParameters()).thenReturn(parameters);
        return statement;
    }
}
