package com.meiyunji.sponsored.cron.service.impl;

import com.meiyunji.sponsored.cron.service.IDeleteColdTableDataService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import static org.junit.Assert.*;

@SpringBootTest
@RunWith(SpringRunner.class)
public class DeleteColdTableDataServiceImplTest {

    @Autowired
    private IDeleteColdTableDataService deleteColdTableDataService;


    @Test
    public void testDeleteColdTableData() throws InterruptedException {

        // todo 验证
        for (int i = 0; i < 3; i++) {
            deleteColdTableDataService.deleteColdTableData(1, 8 , "20000401", 1);
            Thread.sleep(1000  * 10);
        }
    }


    @Test
    public void testDeleteColdTableData2() throws InterruptedException {
        deleteColdTableDataService.deleteColdTableData(1, 8 , null, 1);
    }


}