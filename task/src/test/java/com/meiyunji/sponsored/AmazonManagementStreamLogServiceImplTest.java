//package com.meiyunji.sponsored;
//
//import com.meiyunji.sponsored.service.stream.po.AmazonManagementStreamTaskRetry;
//import com.meiyunji.sponsored.service.stream.service.IAmazonManagementStreamLogService;
//import com.meiyunji.sponsored.service.stream.service.IAmazonManagementStreamRedisCountService;
//import com.meiyunji.sponsored.service.syncTask.entity.monitor.MonitorStreamTypeEnum;
//import com.meiyunji.sponsored.service.syncTask.entity.monitor.MonitorTypeEnum;
//import lombok.extern.slf4j.Slf4j;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.test.context.junit4.SpringRunner;
//
//import java.math.BigDecimal;
//import java.math.RoundingMode;
//import java.time.LocalDateTime;
//import java.util.ArrayList;
//import java.util.Date;
//import java.util.List;
//import java.util.Random;
//
//@RunWith(SpringRunner.class)
//@SpringBootTest
//@Slf4j
//public class AmazonManagementStreamLogServiceImplTest {
//
//    @Autowired
//    private IAmazonManagementStreamLogService amazonManagementStreamLogService;
//
//    @Autowired
//    private IAmazonManagementStreamRedisCountService amazonManagementStreamRedisCountService;
//
//    @Test
//    public void printAllManagementStreamCountTest(){
//        amazonManagementStreamLogService.printAllManagementStreamCount(MonitorTypeEnum.all, MonitorStreamTypeEnum.ad_campaign, new Date(), 200);
//    }
//
//    @Test
//    public void printPuidManagementStreamCountTest(){
//        amazonManagementStreamLogService.printPuidManagementStreamCount(100,200,MonitorTypeEnum.puid, MonitorStreamTypeEnum.ad_group, new Date(), 123);
//    }
//
//    @Test
//    public void sendAmazonManagementStreamWarnTest(){
//        amazonManagementStreamRedisCountService.sendAmazonManagementStreamWarn();
//    }
//
//    @Test
//    public void sendWarnTest(){
//        LocalDateTime localDateTime = LocalDateTime.now().plusHours(-2);
//        Random random = new Random();
//        for (int i = 0; i < 10 ; i++){
//            amazonManagementStreamRedisCountService.countAllAmazonManagementStreamHour(localDateTime, random.nextInt(10));
//        }
//        for (int i = 0; i < 5 ; i++){
//            amazonManagementStreamRedisCountService.countMinutesSuccessAmazonManagementStreamHour(localDateTime, random.nextInt(5));
//        }
//        amazonManagementStreamRedisCountService.sendAmazonManagementStreamWarn();
//    }
//
//    public static void main(String[] args) {
//        System.out.println(new BigDecimal("2.4011").setScale(2, RoundingMode.HALF_UP).stripTrailingZeros());
//    }
//
//    @Test
//    public void countAmazonManagementStreamRetryKeyTest(){
//        AmazonManagementStreamTaskRetry amazonManagementStreamTaskRetry = new AmazonManagementStreamTaskRetry();
//        amazonManagementStreamTaskRetry.setCreateTime(new Date());
//        amazonManagementStreamTaskRetry.setIdsCount(10);
//
//        AmazonManagementStreamTaskRetry amazonManagementStreamTaskRetry1 = new AmazonManagementStreamTaskRetry();
//        amazonManagementStreamTaskRetry1.setCreateTime(new Date());
//        amazonManagementStreamTaskRetry1.setIdsCount(8);
//
//        AmazonManagementStreamTaskRetry amazonManagementStreamTaskRetry3 = new AmazonManagementStreamTaskRetry();
//        amazonManagementStreamTaskRetry3.setCreateTime(new Date());
//        amazonManagementStreamTaskRetry3.setIdsCount(5);
//
//        List<AmazonManagementStreamTaskRetry> executeList = new ArrayList<>();
//        executeList.add(amazonManagementStreamTaskRetry);
//        executeList.add(amazonManagementStreamTaskRetry1);
//        executeList.add(amazonManagementStreamTaskRetry3);
//        amazonManagementStreamRedisCountService.countAmazonManagementStreamRetryKey(executeList);
//    }
//
//
//}
