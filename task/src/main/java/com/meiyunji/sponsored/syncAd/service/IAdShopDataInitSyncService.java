package com.meiyunji.sponsored.syncAd.service;

import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdProfile;
import com.meiyunji.sponsored.service.syncAd.enums.ShopDataSyncAdTypeEnum;

import java.util.List;
import java.util.Set;

/**
 * @author: sun<PERSON><PERSON>
 * @email: sunin<PERSON>@dianxiaomi.com
 * @date: 2024-01-29  19:18
 */


public interface IAdShopDataInitSyncService {

    /**
     * 授权初始化数据同步-触发
     * @param shopIdList
     */
    void initSync(List<Integer> shopIdList);

    /**
     * 授权初始化数据同步-执行
     * @param shop 店铺
     */
    void doShopDataInitSync(ShopAuth shop, AmazonAdProfile profile, ShopDataSyncAdTypeEnum adTypeEnum);

    /**
     * 授权初始化数据同步-重试店铺级任务
     * @param index
     * @param total
     * @param shopIdList
     */
    void doShopDataInitSyncRetry4ShopLevel(int index, int total, List<Integer> shopIdList);

    /**
     * 授权初始化数据同步-重试组级任务
     * @param index
     * @param total
     * @param shopIdList
     * @param groupIdList
     */
    void shopDataInitSyncRetry4GroupLevel(int index, int total, List<Integer> shopIdList, List<String> groupIdList);

    /**
     * 店铺授权数据初始化历史数据清理
     * @param shopIdList
     */
    int deleteShopDataInitHistoryData(List<Integer> shopIdList, int deleteTimeLimitSecond, int onceDelMaxCount);

}
