package com.meiyunji.sponsored.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * 接口测速时间段对比监控config
 *
 * @Author: hejh
 * @Date: 2024/7/3 10:56
 */
@Configuration
@RefreshScope
@Data
public class NginxCgiMonitorConfig {

    @Value("${cgi.monitor.dbUrl:************************************************************}")
    private String dbUrl;
    @Value("${cgi.monitor.user:root}")
    private String user;
    @Value("${cgi.monitor.pass:dxm123654}")
    private String pass;
}
