package com.meiyunji.sponsored.executor;

import com.meiyunji.sponsored.service.kafka.service.ScheduleNewTaskFinishedService;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.KafkaConsumer;

/**
 * @author: chenzimeng
 * @email: <EMAIL>
 * @date: 2023-11-21  09:30
 */
@Slf4j
public class ScheduleNewTaskFinishedMessageConsumerExecutor extends ScheduleNewTaskFinishedMessageExecutor<ConsumerRecord<?, byte[]>> {

    private final ScheduleNewTaskFinishedService scheduleNewTaskFinishedService;

    public ScheduleNewTaskFinishedMessageConsumerExecutor(Integer poolSize,KafkaConsumer ScheduleNewTaskFinishedConsumer,
                                                          ScheduleNewTaskFinishedService scheduleNewTaskFinishedService) {
        super(poolSize,ScheduleNewTaskFinishedConsumer);
        this.scheduleNewTaskFinishedService = scheduleNewTaskFinishedService;
    }

    @Override
    public void exec(ConsumerRecord<?, byte[]> record) throws Exception {
        log.info("分时策略多线程消费数据线程数据 active: {}  pool-size: {}  queue-size: {} complete-size: {}", executor.getActiveCount(), executor.getPoolSize(),
                executor.getQueue().size(), executor.getCompletedTaskCount());
        scheduleNewTaskFinishedService.consumer(record);
    }

}
