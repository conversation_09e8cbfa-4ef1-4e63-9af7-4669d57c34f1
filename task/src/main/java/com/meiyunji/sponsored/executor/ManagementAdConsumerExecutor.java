package com.meiyunji.sponsored.executor;

import com.meiyunji.sponsored.service.syncTask.message.ManagementAdStreamMessage;
import com.meiyunji.sponsored.service.syncTask.ManagementAdConsumer;
import lombok.extern.slf4j.Slf4j;
import org.apache.pulsar.client.api.Consumer;
import org.apache.pulsar.shade.com.fasterxml.jackson.core.type.TypeReference;

import java.time.Duration;
import java.time.Instant;
import java.util.List;
import java.util.concurrent.Callable;

@Slf4j
public class ManagementAdConsumerExecutor extends SimpleStreamTaskExecutor<ManagementAdStreamMessage> {
    private static TypeReference<ManagementAdStreamMessage> typeReference =
            new TypeReference<ManagementAdStreamMessage>() {
            };
    private final ManagementAdConsumer managementAdConsumer;

    public ManagementAdConsumerExecutor(
            Integer poolSize, Consumer<byte[]> budgetUsageConsumer,
            ManagementAdConsumer managementAdConsumer) {
        super(poolSize, budgetUsageConsumer);
        this.managementAdConsumer = managementAdConsumer;
    }

    @Override
    protected TypeReference<ManagementAdStreamMessage> getTypeReference() {
        return typeReference;
    }

    @Override
    public Callable<Void> genCallable(List<ManagementAdStreamMessage> messages, String messageId) throws Exception {
        log.info("线程数据 active: {}  pool-size: {}  queue-size: {} complete-size: {}", executor.getActiveCount(), executor.getPoolSize(),
                executor.getQueue().size(), executor.getCompletedTaskCount());
        printMessageDelayTime(messages, "ad product");
        return () -> {
            Instant start = Instant.now();
            managementAdConsumer.process(messages);
            log.info("Process traffic management ad stream data seconds: {}", Duration.between(start, Instant.now()).getSeconds());
            return null;
        };
    }
}
