package com.meiyunji.sponsored.executor;

import com.meiyunji.sellfox.aadras.types.schedule.AdvertiseRuleTaskExecuteRecordMessage;
import com.meiyunji.sponsored.service.syncTask.AdvertiseAutoRuleConsumer;
import lombok.extern.slf4j.Slf4j;
import org.apache.pulsar.client.api.Consumer;
import org.apache.pulsar.shade.com.fasterxml.jackson.core.type.TypeReference;

@Slf4j
public class AutoRuleExecuteRecordConsumerExecutor extends AutoRuleExecutor<AdvertiseRuleTaskExecuteRecordMessage> {

    private static TypeReference<AdvertiseRuleTaskExecuteRecordMessage> typeReference =
            new TypeReference<AdvertiseRuleTaskExecuteRecordMessage>() {
            };
    private final AdvertiseAutoRuleConsumer advertiseAutoRuleConsumer;

    public AutoRuleExecuteRecordConsumerExecutor(
            Integer poolSize, Consumer<byte[]> autoRuleConsumer,
            AdvertiseAutoRuleConsumer advertiseAutoRuleConsumer) {
        super(poolSize, autoRuleConsumer);
        this.advertiseAutoRuleConsumer = advertiseAutoRuleConsumer;
    }

    @Override
    protected TypeReference<AdvertiseRuleTaskExecuteRecordMessage> getTypeReference() {
        return typeReference;
    }

    @Override
    public void exec(AdvertiseRuleTaskExecuteRecordMessage messages) throws Exception {
        log.info("wade 线程数据 active: {}  pool-size: {}  queue-size: {} complete-size: {}", executor.getActiveCount(), executor.getPoolSize(),
                executor.getQueue().size(), executor.getCompletedTaskCount());
        advertiseAutoRuleConsumer.process(messages);
    }
}
