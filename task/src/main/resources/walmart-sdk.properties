api_url=https://marketplace.walmartapis.com/
app_name=<PERSON><PERSON><PERSON><PERSON><PERSON>(http://www.sellfox.com)
app_version=0.0.1-alpha
format=JSON
socket_timeout=50
connection_timeout=50
connection_request_timeout=20
max_connections=100
max_connections_per_route=20
max_error_retry=3
velocity_limit_delay=4
#proxy_host=proxy.sellfox.com
#proxy_port=6128
#proxy_username=
#proxy_password=

client_id=4fde28ac-9a89-4382-91c7-25400d7dcce5
client_secret=AM1KuXHbXEnICJ_oeuB-nS9HnLwe4zLO_9WJurow21lBIuMBHPR7_QXRKRIVri0D6h0ZJqRrc2bpTZsJ546ngRQ
#redirect_uri=https://www.dianxiaomi.com/shop/walmart/authRedirect.htm
#auth_url=https://login.account.wal-mart.com/authorize
#walmart test properties
#advertiser_url=https://developer.api.stg.walmart.com/api-proxy/service/WPA/Api/v1/api/
#advertiser_client_id=eacee33e-8ea4-4891-9455-42a1da22d98b
#advertiser_access_token=1960f9e6-03a4-4eb6-8303-7a833985e5c2
#advertiser_agency_private_key=/walmartPrivateKey/test/agency_private_key.pem

#walmart prod properties
advertiser_url=https://developer.api.walmart.com/api-proxy/service/WPA/Api/v1/api/
advertiser_client_id=4942c2cf-c61f-4f6f-9879-655422fb4f3b
advertiser_access_token=********-6b3b-4107-b828-57153cefb231
advertiser_agency_private_key=/walmartPrivateKey/agency_private_key.pem