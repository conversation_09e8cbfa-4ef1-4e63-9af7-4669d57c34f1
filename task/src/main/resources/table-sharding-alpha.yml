_00_07: 0x00..0x07
_08_0f: 0x08..0x0f
_10_17: 0x10..0x17
_18_1f: 0x18..0x1f
_20_27: 0x20..0x27
_28_2f: 0x28..0x2f
_30_37: 0x30..0x37
_38_3f: 0x38..0x3f
_40_47: 0x40..0x47
_48_4f: 0x48..0x4f
_50_57: 0x50..0x57
_58_5f: 0x58..0x5f
_60_67: 0x60..0x67
_68_6f: 0x68..0x6f
_70_77: 0x70..0x77
_78_7f: 0x78..0x7f
_80_87: 0x80..0x87
_88_8f: 0x88..0x8f
_90_97: 0x90..0x97
_98_9f: 0x98..0x9f
_a0_a7: 0xa0..0xa7
_a8_af: 0xa8..0xaf
_b0_b7: 0xb0..0xb7
_b8_bf: 0xb8..0xbf
_c0_c7: 0xc0..0xc7
_c8_cf: 0xc8..0xcf
_d0_d7: 0xd0..0xd7
_d8_df: 0xd8..0xdf
_e0_e7: 0xe0..0xe7
_e8_ef: 0xe8..0xef
_f0_f7: 0xf0..0xf7
_f8_ff: 0xf8..0xff
