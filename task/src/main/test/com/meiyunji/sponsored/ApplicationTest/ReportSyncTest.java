package com.meiyunji.sponsored.ApplicationTest;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.meiyunji.sellfox.aadas.types.enumeration.AmazonReportV3Type;
import com.meiyunji.sellfox.aadas.types.message.notification.ReportReadyNotification;
import com.meiyunji.sponsored.common.util.JSONUtil;
import com.meiyunji.sponsored.cron.CronjobService;
import com.meiyunji.sponsored.service.cpc.service2.sb.impl.CpcSbAdsApiService;
import com.meiyunji.sponsored.service.syncTask.report.strategy.sponsoredProducts.SpAdvertisedProductReportV3Strategy;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Lazy;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.Map;

@SpringBootTest
@RunWith(SpringRunner.class)
public class ReportSyncTest {

    @Resource
    SpAdvertisedProductReportV3Strategy strategy;
    @Autowired
    @Lazy
    private CronjobService cronjobService;

    @Test
    public void syncAdProduct() throws Exception {
        String params = "{\"wxUrl\":\"https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=fedac8bb-2773-4e8c-a0f3-8446b8a348d1\",\"4636\":5,\"puids\":100}";
        Map<String, Object> stringObjectMap = JSONUtil.jsonToObjects(params, new TypeReference<Map<String, Object>>() {
        });
    }


    @Test
    public void testSyncSb(){
        cronjobService.cpcSbSync(0,1,null,4636,null,"3");
    }
}
