# getCampaignIdsTypeByPortfolioId 方法重构说明

## 重构目标

将 `getCampaignIdsTypeByPortfolioId` 方法从返回 `List<CampaignTypeDto>` 重构为返回 `String` 类型的 SQL，以支持更灵活的 SQL 组合和子查询优化。

## 重构前后对比

### 原始方法
```java
@Override
public List<CampaignTypeDto> getCampaignIdsTypeByPortfolioId(
    Integer puid, List<Integer> shopId, String portfolioId, 
    String campaignId, String type, String state, String servingStatus) {
    
    // 构建查询条件
    ConditionBuilder.Builder builder = new ConditionBuilder.Builder();
    // ... 条件构建逻辑
    
    StringBuilder sql = new StringBuilder("select campaign_id,type from t_amazon_ad_campaign_all where " + builder.build().getSql());
    return getJdbcTemplate(puid).query(sql.toString(), new ObjectMapper<>(CampaignTypeDto.class), builder.build().getValues());
}
```

### 重构后的新方法
```java
public String getCampaignIdsTypeByPortfolioIdSql(
    Integer puid, List<Integer> shopId, String portfolioId, 
    String campaignId, String type, String state, String servingStatus, 
    List<Object> argsList) {
    
    // 构建查询条件
    ConditionBuilder.Builder builder = new ConditionBuilder.Builder();
    // ... 相同的条件构建逻辑
    
    // 构建SQL并收集参数
    ConditionBuilder condition = builder.build();
    argsList.addAll(Arrays.asList(condition.getValues()));
    
    return "select campaign_id,type from t_amazon_ad_campaign_all where " + condition.getSql();
}
```

## 重构优势

### 1. 支持子查询组合
新方法返回 SQL 字符串，可以作为子查询嵌入到更复杂的查询中：

```java
// 使用示例：作为子查询
List<Object> argsList = new ArrayList<>();
String campaignSql = getCampaignIdsTypeByPortfolioIdSql(puid, shopIds, portfolioId, campaignId, type, state, servingStatus, argsList);

StringBuilder mainSql = new StringBuilder();
mainSql.append("SELECT r.asin, r.sku, r.shop_id FROM ods_t_amazon_ad_product r ");
mainSql.append("WHERE r.campaign_id IN (");
mainSql.append("SELECT campaign_id FROM (").append(campaignSql).append(") temp_campaigns");
mainSql.append(")");

// 执行主查询
Object[] args = argsList.toArray();
List<ProductDto> results = jdbcTemplate.query(mainSql.toString(), args, new BeanPropertyRowMapper<>(ProductDto.class));
```

### 2. 性能优化
避免了两次数据库查询，直接在一个查询中完成：

```java
// 原始方式（两次查询）
List<CampaignTypeDto> campaigns = getCampaignIdsTypeByPortfolioId(...);
List<String> campaignIds = campaigns.stream().map(CampaignTypeDto::getCampaignId).collect(Collectors.toList());
List<ProductDto> products = getProductsByCampaignIds(campaignIds);

// 优化方式（一次查询）
List<Object> argsList = new ArrayList<>();
String campaignSql = getCampaignIdsTypeByPortfolioIdSql(..., argsList);
String productSql = "SELECT * FROM products WHERE campaign_id IN (" + campaignSql + ")";
List<ProductDto> products = jdbcTemplate.query(productSql, argsList.toArray(), new BeanPropertyRowMapper<>(ProductDto.class));
```

### 3. 减少内存使用
不需要在内存中存储中间结果：

```java
// 原始方式：需要存储中间结果
List<CampaignTypeDto> campaigns = getCampaignIdsTypeByPortfolioId(...); // 占用内存
List<String> campaignIds = extractCampaignIds(campaigns); // 额外内存

// 优化方式：直接生成SQL，无中间结果
String sql = getCampaignIdsTypeByPortfolioIdSql(..., argsList);
```

## 使用场景

### 1. 子查询优化
```java
// 在 OdsAmazonAdProductDaoImpl 中使用
private String buildOptimizedCampaignFilter(String field, List<String> campaignIds, List<Object> argsList) {
    if (campaignIds.size() > 5000) {
        // 使用子查询替代大量 IN 参数
        String campaignSql = amazonAdCampaignAllDao.getCampaignIdsTypeByPortfolioIdSql(
            puid, shopIds, portfolioId, null, type, state, servingStatus, argsList);
        return " and " + field + " in (select campaign_id from (" + campaignSql + ") temp_campaigns)";
    }
    // 其他策略...
}
```

### 2. 复杂报表查询
```java
// 构建复杂的报表查询
public List<ReportDto> getComplexReport(ReportParam param) {
    List<Object> argsList = new ArrayList<>();
    
    // 获取符合条件的 campaign SQL
    String campaignSql = getCampaignIdsTypeByPortfolioIdSql(
        param.getPuid(), param.getShopIds(), param.getPortfolioId(), 
        null, param.getType(), param.getState(), param.getServingStatus(), argsList);
    
    StringBuilder reportSql = new StringBuilder();
    reportSql.append("SELECT c.campaign_id, c.type, r.impressions, r.clicks, r.cost ");
    reportSql.append("FROM (").append(campaignSql).append(") c ");
    reportSql.append("LEFT JOIN campaign_reports r ON c.campaign_id = r.campaign_id ");
    reportSql.append("WHERE r.date_range = ? ");
    argsList.add(param.getDateRange());
    
    return jdbcTemplate.query(reportSql.toString(), argsList.toArray(), new BeanPropertyRowMapper<>(ReportDto.class));
}
```

### 3. 动态 SQL 构建
```java
// 根据条件动态构建查询
public String buildDynamicQuery(QueryParam param, List<Object> argsList) {
    StringBuilder sql = new StringBuilder("SELECT * FROM main_table WHERE 1=1 ");
    
    if (param.hasPortfolioFilter()) {
        String campaignSql = getCampaignIdsTypeByPortfolioIdSql(
            param.getPuid(), param.getShopIds(), param.getPortfolioId(), 
            null, null, null, null, argsList);
        sql.append("AND campaign_id IN (").append(campaignSql).append(") ");
    }
    
    // 其他条件...
    return sql.toString();
}
```

## 向后兼容性

为了保持向后兼容，原始方法 `getCampaignIdsTypeByPortfolioId` 保持不变：

```java
// 原始方法仍然可用
List<CampaignTypeDto> campaigns = amazonAdCampaignAllDao.getCampaignIdsTypeByPortfolioId(
    puid, shopIds, portfolioId, campaignId, type, state, servingStatus);

// 新方法提供更多灵活性
List<Object> argsList = new ArrayList<>();
String sql = amazonAdCampaignAllDao.getCampaignIdsTypeByPortfolioIdSql(
    puid, shopIds, portfolioId, campaignId, type, state, servingStatus, argsList);
```

## 接口定义

在 `IAmazonAdCampaignAllDao` 接口中添加了新方法：

```java
/**
 * 重构版本：返回 SQL 字符串而非查询结果
 * 获取根据 portfolio_id 筛选的 campaign_id 和 type 的 SQL 查询语句
 * 
 * @param puid 用户ID
 * @param shopId 店铺ID列表
 * @param portfolioId 广告组合ID
 * @param campaignId 广告活动ID
 * @param type 广告类型
 * @param state 状态
 * @param servingStatus 服务状态
 * @param argsList 参数列表（用于收集SQL参数）
 * @return SQL查询字符串
 */
String getCampaignIdsTypeByPortfolioIdSql(Integer puid, List<Integer> shopId, String portfolioId, 
    String campaignId, String type, String state, String servingStatus, List<Object> argsList);
```

## 总结

这次重构提供了更灵活的 SQL 构建能力，特别适合：

1. **子查询优化**：解决大量 campaign_id 的性能问题
2. **复杂查询构建**：支持多表关联和复杂条件
3. **性能提升**：减少数据库查询次数和内存使用
4. **代码复用**：SQL 片段可以在多个场景中复用

同时保持了向后兼容性，现有代码无需修改即可继续使用。
