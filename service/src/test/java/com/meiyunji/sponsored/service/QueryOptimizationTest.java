package com.meiyunji.sponsored.service;

import com.meiyunji.sponsored.common.util.OptimizedSqlBuilder;
import com.meiyunji.sponsored.common.util.QueryPerformanceMonitor;
import com.meiyunji.sponsored.common.util.SqlStringUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.IntStream;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 查询优化测试类
 * 用于验证不同查询策略的性能和正确性
 */
@SpringBootTest
public class QueryOptimizationTest {

    private OptimizedSqlBuilder sqlBuilder;
    private QueryPerformanceMonitor performanceMonitor;

    @BeforeEach
    void setUp() {
        sqlBuilder = new OptimizedSqlBuilder();
        performanceMonitor = new QueryPerformanceMonitor();
    }

    @Test
    void testSmallDataSetQuery() {
        // 测试小数据量查询（<= 500）
        List<String> campaignIds = generateCampaignIds(300);
        List<Object> args = new ArrayList<>();
        
        String sql = sqlBuilder.buildCampaignIdQuery("r.campaign_id", campaignIds, args);
        
        assertNotNull(sql);
        assertTrue(sql.contains("in ("));
        assertEquals(300, args.size());
        
        System.out.println("Small dataset SQL: " + sql);
        System.out.println("Args count: " + args.size());
    }

    @Test
    void testMediumDataSetQuery() {
        // 测试中等数据量查询（500 < size <= 2000）
        List<String> campaignIds = generateCampaignIds(1500);
        List<Object> args = new ArrayList<>();
        
        String sql = sqlBuilder.buildCampaignIdQuery("r.campaign_id", campaignIds, args);
        
        assertNotNull(sql);
        assertTrue(sql.contains("or") || sql.contains("in ("));
        assertTrue(args.size() > 0);
        
        System.out.println("Medium dataset SQL: " + sql.substring(0, Math.min(200, sql.length())) + "...");
        System.out.println("Args count: " + args.size());
    }

    @Test
    void testLargeDataSetQuery() {
        // 测试大数据量查询（> 2000）
        List<String> campaignIds = generateCampaignIds(5000);
        List<Object> args = new ArrayList<>();
        
        String sql = sqlBuilder.buildCampaignIdQuery("r.campaign_id", campaignIds, args);
        
        assertNotNull(sql);
        assertTrue(sql.contains("array_contains") || sql.contains("bitmap"));
        assertTrue(args.size() > 0);
        
        System.out.println("Large dataset SQL: " + sql);
        System.out.println("Args count: " + args.size());
    }

    @Test
    void testPerformanceComparison() {
        // 性能对比测试
        List<String> campaignIds = generateCampaignIds(3000);
        
        // 测试原始 bitmap 方法
        long startTime = System.currentTimeMillis();
        List<Object> bitmapArgs = new ArrayList<>();
        String bitmapSql = SqlStringUtil.dealBitMapDorisInList("r.campaign_id", campaignIds, bitmapArgs);
        long bitmapTime = System.currentTimeMillis() - startTime;
        
        // 测试优化后的方法
        startTime = System.currentTimeMillis();
        List<Object> optimizedArgs = new ArrayList<>();
        String optimizedSql = sqlBuilder.buildCampaignIdQuery("r.campaign_id", campaignIds, optimizedArgs);
        long optimizedTime = System.currentTimeMillis() - startTime;
        
        System.out.println("Bitmap method time: " + bitmapTime + "ms");
        System.out.println("Optimized method time: " + optimizedTime + "ms");
        System.out.println("Bitmap SQL: " + bitmapSql);
        System.out.println("Optimized SQL: " + optimizedSql);
        
        // 优化后的方法应该更快或相当
        assertTrue(optimizedTime <= bitmapTime * 2, "Optimized method should not be significantly slower");
    }

    @Test
    void testDifferentQueryTypes() {
        List<String> ids = generateCampaignIds(1200);
        List<Object> args = new ArrayList<>();
        
        // 测试不同查询类型
        String campaignSql = sqlBuilder.buildOptimizedQuery("campaign_id", ids, args, "campaign");
        args.clear();
        String adGroupSql = sqlBuilder.buildOptimizedQuery("ad_group_id", ids, args, "adgroup");
        args.clear();
        String productSql = sqlBuilder.buildOptimizedQuery("product_id", ids, args, "product");
        
        assertNotNull(campaignSql);
        assertNotNull(adGroupSql);
        assertNotNull(productSql);
        
        System.out.println("Campaign SQL: " + campaignSql.substring(0, Math.min(100, campaignSql.length())) + "...");
        System.out.println("AdGroup SQL: " + adGroupSql.substring(0, Math.min(100, adGroupSql.length())) + "...");
        System.out.println("Product SQL: " + productSql.substring(0, Math.min(100, productSql.length())) + "...");
    }

    @Test
    void testEmptyAndNullValues() {
        // 测试空值和 null 值处理
        List<String> emptyList = new ArrayList<>();
        List<Object> args = new ArrayList<>();
        
        String sql = sqlBuilder.buildCampaignIdQuery("r.campaign_id", emptyList, args);
        assertEquals("", sql);
        
        // 测试包含 null 值的列表
        List<String> listWithNulls = new ArrayList<>();
        listWithNulls.add("123");
        listWithNulls.add(null);
        listWithNulls.add("456");
        listWithNulls.add("null");
        
        args.clear();
        sql = sqlBuilder.buildCampaignIdQuery("r.campaign_id", listWithNulls, args);
        
        assertNotNull(sql);
        // 应该只有 2 个有效值
        assertEquals(2, args.size());
        assertTrue(args.contains("123"));
        assertTrue(args.contains("456"));
        assertFalse(args.contains(null));
        assertFalse(args.contains("null"));
    }

    @Test
    void testPerformanceMonitoring() {
        // 测试性能监控功能
        List<String> campaignIds = generateCampaignIds(1000);
        
        QueryPerformanceMonitor.QueryContext context = 
            performanceMonitor.startQuery("campaign", "campaign_id", campaignIds.size());
        
        // 模拟查询执行
        try {
            Thread.sleep(100); // 模拟查询时间
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        performanceMonitor.endQuery(context);
        
        QueryPerformanceMonitor.QueryStats stats = 
            performanceMonitor.getQueryStats("campaign", "campaign_id");
        
        assertNotNull(stats);
        assertEquals(1, stats.getQueryCount());
        assertTrue(stats.getAverageDuration() >= 100);
        assertEquals(1000.0, stats.getAverageDataSize(), 0.1);
    }

    @Test
    void testBoundaryValues() {
        // 测试边界值
        testQueryWithSize(499);  // 小数据量边界
        testQueryWithSize(500);  // 小数据量边界
        testQueryWithSize(501);  // 中等数据量边界
        testQueryWithSize(1999); // 中等数据量边界
        testQueryWithSize(2000); // 中等数据量边界
        testQueryWithSize(2001); // 大数据量边界
    }

    private void testQueryWithSize(int size) {
        List<String> campaignIds = generateCampaignIds(size);
        List<Object> args = new ArrayList<>();
        
        String sql = sqlBuilder.buildCampaignIdQuery("r.campaign_id", campaignIds, args);
        
        assertNotNull(sql, "SQL should not be null for size: " + size);
        assertFalse(sql.trim().isEmpty(), "SQL should not be empty for size: " + size);
        assertTrue(args.size() > 0, "Args should not be empty for size: " + size);
        
        System.out.println("Size " + size + " - SQL type: " + getSqlType(sql) + ", Args count: " + args.size());
    }

    private String getSqlType(String sql) {
        if (sql.contains("array_contains")) return "array_contains";
        if (sql.contains("bitmap")) return "bitmap";
        if (sql.contains(" or ")) return "batch_in";
        if (sql.contains(" in (")) return "simple_in";
        return "unknown";
    }

    private List<String> generateCampaignIds(int count) {
        return IntStream.range(1, count + 1)
                .mapToObj(i -> "campaign_" + i)
                .collect(ArrayList::new, ArrayList::add, ArrayList::addAll);
    }
}
