package com.meiyunji.sponsored.service.cpc.service2.sp;

import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.service.cpc.vo.*;

import java.util.List;

/**
 * Created by xp on 2021/3/30.
 * asin报告业务
 */
public interface ICpcAsinReportService {

    /**
     * 广告组-asin报告列表页
     * @param param :
     * @return :
     */
    Result<Page<AsinReportPageVo>> pageList(AsinReportPageParam param);

    /**
     * sp-已购买商品报告导出
     *
     * @param puid
     * @param search
     * @param page
     * @return
     */
    Page pageList(int puid, SearchVo search, Page page);


    /**
     * sp-已购买ASIN页面列表页数据
     * @param puid
     * @param param
     * @return
     */
    List<AdAsinPageVo> getSpAsinPageVoList(Integer puid, AdAsinPageParam param);


    /**
     * sp-已购买ASIN页面列表分页数据
     * @param puid
     * @param param
     * @param page
     * @return
     */
    Page getSpAsinPageVoPageList(Integer puid, AdAsinPageParam param, Page page);


    /**
     * sp-已购买ASIN页面关联分析列表页数据
     * @param puid
     * @param param
     * @return
     */
    List<AdAsinPageVo> getSpAsinAssociationPageVoList(Integer puid, AdAsinPageParam param);

}
