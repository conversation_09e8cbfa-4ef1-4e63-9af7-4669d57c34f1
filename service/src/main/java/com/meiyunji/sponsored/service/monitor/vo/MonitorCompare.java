package com.meiyunji.sponsored.service.monitor.vo;

import com.meiyunji.sponsored.common.util.MathUtil;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Map;

/**
 * @description: some desc
 * @author: pxq
 * @email: <EMAIL>
 * @date: 2023/7/25 14:41
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MonitorCompare {
    private Long impressions;
    private Long clicks;
    private Integer saleNum;
    private BigDecimal cost;
    private BigDecimal adSales;
    private BigDecimal adSale;
    private Integer adOrderNum;
    private Integer orderNum;


    public MonitorCompare sum(MonitorCompare monitorCompare) {
        if (monitorCompare == null) {
            return this;
        }
        this.setCost(MathUtil.sum(this.getCost(), monitorCompare.getCost()));
        this.setImpressions(MathUtil.sumLong(this.getImpressions(), monitorCompare.getImpressions()));
        this.setClicks(MathUtil.sumLong(this.getClicks(), monitorCompare.getClicks()));
        this.setSaleNum(MathUtil.sumInteger(this.getSaleNum(), monitorCompare.getSaleNum()));
        this.setAdSales(MathUtil.sum(this.getAdSales(), monitorCompare.getAdSales()));
        this.setAdOrderNum(MathUtil.sumInteger(this.getAdOrderNum(), monitorCompare.getAdOrderNum()));
        this.setOrderNum(MathUtil.sumInteger(this.getOrderNum(), monitorCompare.getOrderNum()));
        this.setAdSale(MathUtil.sum(this.getAdSale(), monitorCompare.getAdSale()));
        return this;
    }


    public boolean lessThanComparable(MonitorCompare value) {
        if (comparable(this.getOrderNum(), value.getOrderNum()) < 0) {
            return false;
        }
        if (comparable(this.getCost(), value.getCost()) < 0) {
            return false;
        }
        if (comparable(this.getClicks(), value.getClicks()) < 0) {
            return false;
        }
        if (comparable(this.getImpressions(), value.getImpressions()) < 0) {
            return false;
        }
        if (comparable(this.getAdSales(), value.getAdSales()) < 0) {
            return false;
        }
        if (comparable(this.getAdSale(), value.getAdSale()) < 0) {
            return false;
        }
        if (comparable(this.getSaleNum(), value.getSaleNum()) < 0) {
            return false;
        }
        if (comparable(this.getAdOrderNum(), value.getAdOrderNum()) < 0) {
            return false;
        }
        return true;

    }

    public boolean greaterThanComparable(MonitorCompare value) {
        if (comparable(this.getOrderNum(), value.getOrderNum()) > 0) {
            return false;
        }
        if (comparable(this.getCost(), value.getCost()) > 0) {
            return false;
        }
        if (comparable(this.getClicks(), value.getClicks()) > 0) {
            return false;
        }
        if (comparable(this.getImpressions(), value.getImpressions()) > 0) {
            return false;
        }
        if (comparable(this.getAdSales(), value.getAdSales()) > 0) {
            return false;
        }
        if (comparable(this.getAdSale(), value.getAdSale()) > 0) {
            return false;
        }
        if (comparable(this.getSaleNum(), value.getSaleNum()) > 0) {
            return false;
        }
        if (comparable(this.getAdOrderNum(), value.getAdOrderNum()) > 0) {
            return false;
        }
        return true;

    }

    public boolean comparable(MonitorCompare value) {
        if (comparable(this.getOrderNum(), value.getOrderNum()) != 0) {
            return false;
        }
        if (comparable(this.getCost(), value.getCost()) != 0) {
            return false;
        }
        if (comparable(this.getClicks(), value.getClicks()) != 0) {
            return false;
        }
        if (comparable(this.getImpressions(), value.getImpressions()) != 0) {
            return false;
        }
        if (comparable(this.getAdSales(), value.getAdSales()) != 0) {
            return false;
        }
        if (comparable(this.getAdSale(), value.getAdSale()) != 0) {
            return false;
        }
        if (comparable(this.getSaleNum(), value.getSaleNum()) != 0) {
            return false;
        }
        if (comparable(this.getAdOrderNum(), value.getAdOrderNum()) != 0) {
            return false;
        }
        return true;
    }


    private int comparable(Integer v1, Integer v2) {
        if (v1 == null) {
            v1 = 0;
        }
        if (v2 == null) {
            v2 = 0;
        }
        return v1.compareTo(v2);
    }

    private int comparable(Long v1, Long v2) {
        if (v1 == null) {
            v1 = 0L;
        }
        if (v2 == null) {
            v2 = 0L;
        }
        return v1.compareTo(v2);

    }


    private int comparable(BigDecimal v1, BigDecimal v2) {
        if (v1 == null) {
            v1 = BigDecimal.ZERO;
        }
        if (v2 == null) {
            v2 = BigDecimal.ZERO;
        }
        return v1.compareTo(v2);
    }


}
