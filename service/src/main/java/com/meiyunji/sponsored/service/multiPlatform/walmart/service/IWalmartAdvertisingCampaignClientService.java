package com.meiyunji.sponsored.service.multiPlatform.walmart.service;

import com.meiyunji.sponsored.common.exception.ServiceException;
import com.walmart.oms.advertiser.base.dto.CreateNewCampaignDTO;
import com.walmart.oms.advertiser.model.ListAllTheCampaignsResponse;

import java.util.List;

/**
 * @author: ys
 * @date: 2025/3/21 10:17
 * @describe:
 */
public interface IWalmartAdvertisingCampaignClientService {

    String createCampaign(List<CreateNewCampaignDTO> campaignDTO) throws ServiceException;
    ListAllTheCampaignsResponse getAllCampaign(String campaignId, String advertiserId,
                                               String filterName, String filterLastModifiedDate) throws ServiceException;
}
