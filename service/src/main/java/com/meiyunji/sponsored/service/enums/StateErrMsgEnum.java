package com.meiyunji.sponsored.service.enums;

public enum StateErrMsgEnum {
    /**
     * 自动化规则亚马逊接口返回报错枚举类
     * */
    entityStateError("entityStateError","实体状态错误"),
    missingValueError("missingValueError","缺少值错误"),
    dateError("dateError","日期错误"),
    biddingError("biddingError","竞价错误"),
    duplicateValueError("duplicateValueError","重复值错误"),
    rangeError("rangeError","范围错误"),
    parentEntityError("parentEntityError","父实体错误"),
    otherError("otherError","其他错误"),
    throttledError("throttledError","节流错误"),
    entityNotFoundError("entityNotFoundError","实体找不到错误"),
    malformedValueError("malformedValueError","格式错误的值错误"),
    budgetError("budgetError","预算值错误"),
    currencyError("currencyError","货币错误"),
    billingError("billingError","账单错误"),
    entityQuotaError("entityQuotaError","实体配额错误"),
    internalServerError("internalServerError","内部服务器错误"),
    apiException("apiException","接口请求异常"),
    accessTokenExpired("accessTokenExpired","access-token已经过期");

    private String code;

    private String message;

    StateErrMsgEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public static String getMessage(String code){
        StateErrMsgEnum[] message = values();
        for (StateErrMsgEnum value : message) {
            if(value.getCode().equals(code)){
                return value.getMessage();
            }
        }
        return "";
    }


    public String getCode() {
        return code;
    }


    public String getMessage() {
        return message;
    }

}
