package com.meiyunji.sponsored.service.purchasedItem.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONValidator;
import com.google.common.collect.Iterables;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.base.ProcessMsg;
import com.meiyunji.sponsored.common.support.StringRedisService;
import com.meiyunji.sponsored.common.util.MathUtil;
import com.meiyunji.sponsored.common.util.StringUtil;
import com.meiyunji.sponsored.rpc.purchasedItem.*;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdCampaignAllDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdGroupDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdPortfolioDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonSdAdGroupDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdCampaignAll;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdGroup;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdPortfolio;
import com.meiyunji.sponsored.service.cpc.po.AmazonSdAdGroup;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.doris.dao.IOdsAmazonAdProductDao;
import com.meiyunji.sponsored.service.doris.dao.IOdsAmazonAdProductSdDao;
import com.meiyunji.sponsored.service.doris.dao.IOdsProductDao;
import com.meiyunji.sponsored.service.doris.dao.impl.OdsSpSdProductReportDao;
import com.meiyunji.sponsored.service.doris.po.OdsAmazonAdProduct;
import com.meiyunji.sponsored.service.doris.po.OdsProduct;
import com.meiyunji.sponsored.service.excel.excelTools.ExcelExtraParam;
import com.meiyunji.sponsored.service.excel.excelTools.WriteHandlerBuild;
import com.meiyunji.sponsored.service.excel.excelTools.service.IExcelService;
import com.meiyunji.sponsored.service.purchasedItem.bo.PurchasedItemDetailBO;
import com.meiyunji.sponsored.service.purchasedItem.bo.PurchasedItemListBO;
import com.meiyunji.sponsored.service.purchasedItem.dao.IPurchasedItemDorisDao;
import com.meiyunji.sponsored.service.purchasedItem.param.PurchasedItemListParam;
import com.meiyunji.sponsored.service.sellfoxApi.IProductApi;
import com.meiyunji.sponsored.service.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.SpreadsheetVersion;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
@Slf4j
public class PurchasedItemService {

    private static final Map<String, Function<PurchasedItemDetailBO, BigDecimal>> DETAIL_SORT_FIELD_MAP = Maps.newHashMap();

    static {
        DETAIL_SORT_FIELD_MAP.put("adSelfSale", PurchasedItemDetailBO::getAdSales); // 销售额
        DETAIL_SORT_FIELD_MAP.put("selfAdOrderNum", PurchasedItemDetailBO::getAdSaleNum); // 订单量
        DETAIL_SORT_FIELD_MAP.put("adSelfSaleNum", PurchasedItemDetailBO::getAdOrderNum); // 销量
        DETAIL_SORT_FIELD_MAP.put("adOrderNumPercentage", PurchasedItemDetailBO::getSaleNumPercentage); // 订单量占比
        DETAIL_SORT_FIELD_MAP.put("orderNumPercentage", PurchasedItemDetailBO::getOrderNumPercentage); // 销量占比
    }

    @Autowired
    private StringRedisService stringRedisService;
    @Autowired
    private IPurchasedItemDorisDao purchasedItemDorisDao;
    @Autowired
    private IAmazonAdCampaignAllDao amazonAdCampaignAllDao;
    @Autowired
    private IScVcShopAuthDao shopAuthDao;
    @Autowired
    private IAmazonAdGroupDao groupDao;
    @Autowired
    private IAmazonSdAdGroupDao sdGroupDao;
    @Autowired
    private IAmazonAdPortfolioDao amazonAdPortfolioDao;
    @Autowired
    private IExcelService excelService;
    @Autowired
    private IProductApi productApi;
    @Autowired
    private IOdsAmazonAdProductSdDao sdAdProductDorisDao;
    @Autowired
    private IOdsAmazonAdProductDao spAdProductDorisDao;
    @Autowired
    private IOdsProductDao odsProductDao;
    @Autowired
    private OdsSpSdProductReportDao odsSpSdProductReportDao;

    private List<PurchasedItemListRow> convertPurchasedItemList(PurchasedItemListParam param, Page<PurchasedItemListBO> page) {
        List<PurchasedItemListBO> rows = page.getRows();
        if (CollectionUtils.isEmpty(rows)) {
            return Collections.emptyList();
        }

        int puid = param.getPuid();
        List<Integer> shopIdList = rows.stream().map(PurchasedItemListBO::getShopId).distinct().collect(Collectors.toList());

        List<ShopAuth> shopAuths = shopAuthDao.listAllByIds(puid, shopIdList);
        Map<Integer, ShopAuth> shopAuthMap = shopAuths.stream().collect(Collectors.toMap(ShopAuth::getId, v -> v, (v1, v2) -> v2));

        // asin维度聚合，反查推广msku
        List<String> asinList = rows.stream().map(PurchasedItemListBO::getAsin).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        Map<String, List<String>> shopAsinSkuMap = getShopAsinSkuMap(puid, shopIdList, asinList, null);

        // 父asin维度聚合，查出父asin对应的推广msku
        List<String> parentAsinList = rows.stream().map(PurchasedItemListBO::getParentAsin).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        Map<String, List<String>> shopParentAsinSkuMap = getShopParentAisnSkuMap(puid, shopIdList, parentAsinList);

        return rows.stream().map(item -> {
            PurchasedItemListRow.Builder rowBuilder = PurchasedItemListRow.newBuilder();
            Integer shopId = item.getShopId();
            rowBuilder.setShopId(shopId);
            ShopAuth shopAuth = shopAuthMap.get(shopId);
            if (shopAuth != null) {
                rowBuilder.setShopName(shopAuth.getName());
            }

            if (param.groupByParent()) {
                rowBuilder.setAsin(item.getParentAsin());
                String key = shopId + "-" + item.getParentAsin();
                if (shopParentAsinSkuMap.containsKey(key)) {
                    rowBuilder.addAllSku(shopParentAsinSkuMap.get(key).stream().distinct().collect(Collectors.toList()));
                }
            } else {
                String asin = item.getAsin();
                rowBuilder.setAsin(asin);
                String key = shopId + "-" + asin;
                if (shopAsinSkuMap.containsKey(key)) {
                    rowBuilder.addAllSku(shopAsinSkuMap.get(key).stream().distinct().collect(Collectors.toList()));
                }
            }

            Integer clicks = item.getClicks();
            Integer impressions = item.getImpressions();
            if (Objects.nonNull(item.getCost())) {
                rowBuilder.setCost(item.getCost().setScale(2, RoundingMode.HALF_UP).toString());
            }
            if (Objects.nonNull(impressions)) {
                rowBuilder.setImpressions(impressions);
            }
            if (Objects.nonNull(clicks)) {
                rowBuilder.setClicks(clicks);
            }

            if (Objects.nonNull(item.getTotalSales())) {
                rowBuilder.setTotalSales(item.getTotalSales().setScale(2, RoundingMode.HALF_UP).toString());
            }
            if (Objects.nonNull(item.getAdSales())) {
                rowBuilder.setAdSales(item.getAdSales().setScale(2, RoundingMode.HALF_UP).toString());
            }
            if (Objects.nonNull(item.getAdOtherSales())) {
                rowBuilder.setAdOtherSales(item.getAdOtherSales().setScale(2, RoundingMode.HALF_UP).toString());
            }

            if (Objects.nonNull(item.getSaleNum())) {
                rowBuilder.setSaleNum(item.getSaleNum().setScale(0, RoundingMode.HALF_UP).toString());
            }
            if (Objects.nonNull(item.getAdSaleNum())) {
                rowBuilder.setAdSaleNum(item.getAdSaleNum().setScale(0, RoundingMode.HALF_UP).toString());
            }
            if (Objects.nonNull(item.getAdOtherSaleNum())) {
                rowBuilder.setAdOtherSaleNum(item.getAdOtherSaleNum().setScale(0, RoundingMode.HALF_UP).toString());
            }

            if (Objects.nonNull(item.getOrderNum())) {
                rowBuilder.setOrderNum(item.getOrderNum().setScale(0, RoundingMode.HALF_UP).toString());
            }
            if (Objects.nonNull(item.getAdOrderNum())) {
                rowBuilder.setAdOrderNum(item.getAdOrderNum().setScale(0, RoundingMode.HALF_UP).toString());
            }
            if (Objects.nonNull(item.getAdOtherOrderNum())) {
                rowBuilder.setAdOtherOrderNum(item.getAdOtherOrderNum().setScale(0, RoundingMode.HALF_UP).toString());
            }

            rowBuilder.setSaleNumBounce(percentage(MathUtil.divideByZero(item.getAdOtherSaleNum(), item.getSaleNum(), 4)));
            rowBuilder.setOrderNumBounce(percentage(MathUtil.divideByZero(item.getAdOtherOrderNum(), item.getOrderNum(), 4)));

            BigDecimal clickNum = Objects.nonNull(clicks) ? new BigDecimal(clicks) : null;
            BigDecimal impressNum = Objects.nonNull(impressions) ? new BigDecimal(impressions) : null;
            rowBuilder.setCtr(percentage(MathUtil.divideByZero(clickNum, impressNum, 4)));
            rowBuilder.setCpc(MathUtil.divideByZero(item.getCost(), clickNum, 4).setScale(2, RoundingMode.HALF_UP).toString());
            rowBuilder.setCvr(percentage(MathUtil.divideByZero(item.getSaleNum(), clickNum, 4)));
            rowBuilder.setAcos(percentage(MathUtil.divideByZero(item.getCost(), item.getTotalSales(), 4)));
            rowBuilder.setRoas(MathUtil.divideByZero(item.getTotalSales(), item.getCost(), 4).setScale(2, RoundingMode.HALF_UP).toString());

            return rowBuilder.build();
        }).collect(Collectors.toList());
    }

    private Map<String, List<String>> getShopAsinSkuMap(int puid, List<Integer> shopIdList, List<String> asinList, List<String> adGroupIds) {
        Map<String, List<String>> shopAsinSkuMap = Maps.newHashMap();
        if (CollectionUtils.isEmpty(asinList)) {
            return shopAsinSkuMap;
        }
        List<OdsAmazonAdProduct> spAdProductList = spAdProductDorisDao.listByShopAsin(puid, shopIdList, asinList, adGroupIds);
        if (CollectionUtils.isNotEmpty(spAdProductList)) {
            for (OdsAmazonAdProduct item : spAdProductList) {
                String key = item.getShopId() + "-" + item.getAsin();
                if (CollectionUtils.isNotEmpty(adGroupIds)) {
                    key = key + "-" + item.getAdGroupId();
                }
                if (shopAsinSkuMap.containsKey(key)) {
                    shopAsinSkuMap.get(key).add(item.getSku());
                } else {
                    shopAsinSkuMap.put(key, Lists.newArrayList(item.getSku()));
                }
            }
        }
        // todo purchased-todo sd不上线
//        List<OdsAmazonAdProductSd> sdAdProductList = sdAdProductDorisDao.listByShopAsin(puid, shopIdList, asinList, adGroupIds);
//        if (CollectionUtils.isNotEmpty(sdAdProductList)) {
//            for (OdsAmazonAdProductSd item : sdAdProductList) {
//                String key = item.getShopId() + "-" + item.getAsin();
//                if (CollectionUtils.isNotEmpty(adGroupIds)) {
//                    key = key + "-" + item.getAdGroupId();
//                }
//                if (shopAsinSkuMap.containsKey(key)) {
//                    shopAsinSkuMap.get(key).add(item.getSku());
//                } else {
//                    shopAsinSkuMap.put(key, Lists.newArrayList(item.getSku()));
//                }
//            }
//        }
        return shopAsinSkuMap;
    }

    private Map<String, List<String>> getShopParentAisnSkuMap(int puid, List<Integer> shopIdList, List<String> parentAsinList) {
        Map<String, List<String>> resMap = Maps.newHashMap();
        if (CollectionUtils.isEmpty(parentAsinList)) {
            return resMap;
        }
        List<OdsAmazonAdProduct> spAdProductList = spAdProductDorisDao.listByShopParentAsin(puid, shopIdList, parentAsinList);
        if (CollectionUtils.isNotEmpty(spAdProductList)) {
            for (OdsAmazonAdProduct item : spAdProductList) {
                String key = item.getShopId() + "-" + item.getParentAsin();
                if (resMap.containsKey(key)) {
                    resMap.get(key).add(item.getSku());
                } else {
                    resMap.put(key, Lists.newArrayList(item.getSku()));
                }
            }
        }
        // todo purchased-todo sd不上线
//        List<OdsAmazonAdProductSd> sdAdProductList = sdAdProductDorisDao.listByShopParentAsin(puid, shopIdList, parentAsinList);
//        if (CollectionUtils.isNotEmpty(sdAdProductList)) {
//            for (OdsAmazonAdProductSd item : sdAdProductList) {
//                String key = item.getShopId() + "-" + item.getParentAsin();
//                if (resMap.containsKey(key)) {
//                    resMap.get(key).add(item.getSku());
//                } else {
//                    resMap.put(key, Lists.newArrayList(item.getSku()));
//                }
//            }
//        }
        return resMap;
    }

    private String percentage(BigDecimal value) {
        if (Objects.isNull(value)) {
            return "0";
        }
        return value.multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP).toString();
    }

    public PurchasedItemDetailListResponse getPurchasedItemDetail(PurchasedItemListParam param) {
        log.info("getPurchasedItemDetail detail param: {}", JSON.toJSONString(param));
        int puid = param.getPuid();

        PurchasedItemDetailListResponse.Builder builder = PurchasedItemDetailListResponse.newBuilder();
        builder.setPageNo(param.getPageNo());
        builder.setPageSize(param.getPageSize());
        builder.setTotalPage(0);
        builder.setTotalSize(0);
        builder.addAllRows(Lists.newArrayList());
        PurchasedItemDetailListResponse emptyResponse = builder.build();

        if (CollectionUtils.isNotEmpty(param.getPortfolioIdList()) && CollectionUtils.isEmpty(param.getPortfolioCampaignIds())) {
            return emptyResponse;
        }

        List<OdsProduct> purchasedProductList = Lists.newArrayList();
        if (StringUtils.equalsIgnoreCase(PurchasedItemListParam.SEARCH_TYPE_PARENT_ASIN, param.getPurchasedAsinSearchField())
                && StringUtils.isNotBlank(param.getPurchasedAsinSearchValue())) {
            List<String> purchasedParentAsins = StringUtil.splitStr(param.getPurchasedAsinSearchValue(), StringUtil.SPECIAL_COMMA);
            purchasedProductList = odsProductDao.listByParentAsins(puid, param.getMarketplaceId(), param.getShopIdList(), purchasedParentAsins);
            log.info("已购子行，购买父asin查询，purchasedParentAsins: {}, 对应子asin: {}", purchasedParentAsins, JSON.toJSONString(purchasedProductList));
            if (CollectionUtils.isEmpty(purchasedProductList)) {
                return emptyResponse;
            }
        }

        Map<String, PurchasedItemDetailBO> map = Maps.newHashMap();
        // 本产品asin 即只展示这些asin下的购买asin
        List<String> asinList = Lists.newArrayList();
        Set<String> asinSet = Sets.newHashSet();
        if (param.groupByParent()) {
            String parentAsin = param.getAsinList().get(0);
            List<String> asins = odsProductDao.listByParentAsin(puid, param.getMarketplaceId(), param.getShopIdList(), Lists.newArrayList(parentAsin));
            log.info("父asin聚合下的子行查询，父asin: {}, 对应子asin: {}", parentAsin, asins);
            if (CollectionUtils.isNotEmpty(asins)) {
                asinList.addAll(asins);
                if (param.isNeedParentSelf()) {
                    asinSet.addAll(asins);
                }
            }
        } else {
            asinList.add(param.getAsinList().get(0));
        }
        if (CollectionUtils.isEmpty(asinList)) {
            return emptyResponse;
        }

        // todo purchased-todo 只查了sp的 sd上线时适配
        getSpDetail(param, asinList, purchasedProductList, map);
//        if (CollectionUtils.isEmpty(param.getAdTypeList()) || new HashSet<>(param.getAdTypeList()).containsAll(PurchasedItemListParam.TYPE_LIST)) {
//            getSdDetail(param, asinList, map);
//        } else if (param.getAdTypeList().contains(Constants.SP)) {
//            getSpDetail(param, asinList, purchasedProductList, map);
//        } else {
//            getSdDetail(param, asinList, map);
//        }
        if (MapUtils.isEmpty(map)) {
            return emptyResponse;
        }

        List<PurchasedItemDetailBO> detailList = map.values().stream().filter(Objects::nonNull).collect(Collectors.toList());
        BigDecimal salesSum = detailList.stream().map(PurchasedItemDetailBO::getAdSales).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal saleNumSum = detailList.stream().map(PurchasedItemDetailBO::getAdSaleNum).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal orderNumSum = detailList.stream().map(PurchasedItemDetailBO::getAdOrderNum).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
        builder.setSalesSum(salesSum.setScale(2, RoundingMode.HALF_UP).toString());
        builder.setSaleNumSum(saleNumSum.setScale(0, RoundingMode.HALF_UP).toString());
        builder.setOrderNumSum(orderNumSum.setScale(0, RoundingMode.HALF_UP).toString());

        detailList.forEach(item -> {
            item.setSaleNumPercentage(MathUtil.divideByZero(item.getAdSaleNum(), saleNumSum, 4));
            item.setOrderNumPercentage(MathUtil.divideByZero(item.getAdOrderNum(), orderNumSum, 4));
        });
        topRows(detailList, builder, param, saleNumSum);
        sortAndPage(detailList, builder, param, asinSet);
        return builder.build();
    }

    private void getSpDetail(PurchasedItemListParam param, List<String> asinList, List<OdsProduct> purchasedProductList, Map<String, PurchasedItemDetailBO> map) {
        List<PurchasedItemListBO> spAsinReportList = purchasedItemDorisDao.getSpAsinReportList(param, asinList, purchasedProductList);
        List<PurchasedItemListBO> spOtherAsinReportList = purchasedItemDorisDao.getSpOtherAsinReportList(param, asinList);
        processDetailData(map, spAsinReportList, Constants.SP, 1);
        processDetailData(map, spOtherAsinReportList, Constants.SP, 0);
    }

    private void getSdDetail(PurchasedItemListParam param, List<String> asinList, Map<String, PurchasedItemDetailBO> map) {
        List<PurchasedItemListBO> sdAsinReportList = purchasedItemDorisDao.getSdAsinReportList(param, asinList);
        List<PurchasedItemListBO> sdOtherAsinReportList = purchasedItemDorisDao.getSdOtherAsinReportList(param, asinList);
        processDetailData(map, sdAsinReportList, Constants.SD, 1);
        processDetailData(map, sdOtherAsinReportList, Constants.SD, 0);
    }

    private void processDetailData(Map<String, PurchasedItemDetailBO> map, List<PurchasedItemListBO> list, String type, int adSelf) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        list.forEach(item -> {
            String key = type + "-" + item.getAsin() + "-" + item.getCampaignId() + "-" + item.getAdGroupId();
            if (map.containsKey(key)) {
                PurchasedItemDetailBO detailBO = map.get(key);
                detailBO.setAdSales(MathUtil.add(detailBO.getAdSales(), item.getAdSales()));
                detailBO.setAdSaleNum(MathUtil.add(detailBO.getAdSaleNum(), item.getAdSaleNum()));
                detailBO.setAdOrderNum(MathUtil.add(detailBO.getAdOrderNum(), item.getAdOrderNum()));
            } else {
                PurchasedItemDetailBO detailBO = PurchasedItemDetailBO.builder()
                        .asin(item.getAsin())
                        .campaignId(item.getCampaignId())
                        .adGroupId(item.getAdGroupId())
                        .type(type)
                        .adSelf(adSelf)
                        .adSales(item.getAdSales())
                        .adSaleNum(item.getAdSaleNum())
                        .adOrderNum(item.getAdOrderNum())
                        .build();
                map.put(key, detailBO);
            }
        });
    }

    private void topRows(List<PurchasedItemDetailBO> detailList, PurchasedItemDetailListResponse.Builder builder, PurchasedItemListParam param,
                         BigDecimal saleNumSum) {
        if (param.isNotNeedTopRows()) {
            return;
        }
        detailList.sort(Comparator.comparing(PurchasedItemDetailBO::getAdSaleNum).reversed());
        if (detailList.size() <= 10) {
            builder.addAllTopRows(convert2DetailRow(detailList, param, Sets.newHashSet()));
        } else {
            List<PurchasedItemDetailBO> top9Rows = Lists.newArrayListWithCapacity(10);
            for (int i = 0; i < 9; i++) {
                top9Rows.add(detailList.get(i));
            }
            List<PurchasedItemDetailRow> topRows = convert2DetailRow(top9Rows, param, Sets.newHashSet());
            PurchasedItemDetailRow.Builder row10Builder = PurchasedItemDetailRow.newBuilder();
            row10Builder.setAsin("其它")
                    .setAdGroupName("-");

            BigDecimal top9SaleNumSum = top9Rows.stream().map(PurchasedItemDetailBO::getAdSaleNum).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal row10SaleNum = saleNumSum.subtract(top9SaleNumSum);
            row10Builder.setAdSaleNum(row10SaleNum.setScale(0, RoundingMode.HALF_UP).toString());

            if (row10SaleNum.compareTo(BigDecimal.ZERO) > 0) {
                BigDecimal top9SaleNumPercentage = top9Rows.stream().map(PurchasedItemDetailBO::getSaleNumPercentage).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
                row10Builder.setSaleNumPercentage(percentage(BigDecimal.valueOf(1).subtract(top9SaleNumPercentage).setScale(4, RoundingMode.HALF_UP)));
            } else {
                row10Builder.setSaleNumPercentage("0");
            }

            topRows.add(row10Builder.build());
            builder.addAllTopRows(topRows);
        }
    }

    private void sortAndPage(List<PurchasedItemDetailBO> detailList, PurchasedItemDetailListResponse.Builder builder,
                             PurchasedItemListParam param, Set<String> asinSet) {
        String orderField = param.getOrderField();
        if (StringUtils.isBlank(orderField) || !DETAIL_SORT_FIELD_MAP.containsKey(orderField)) {
            orderField = "selfAdOrderNum";
        }
        Function<PurchasedItemDetailBO, BigDecimal> keySelector = DETAIL_SORT_FIELD_MAP.get(orderField);
        Comparator<PurchasedItemDetailBO> comparator = Comparator.comparing(keySelector);
        if (!"asc".equalsIgnoreCase(param.getOrderType())) {
            comparator = comparator.reversed();
        }
        detailList.sort(comparator);

        int pageNo = param.getPageNo();
        int pageSize = param.getPageSize();
        int totalPage = (int) Math.ceil((double) detailList.size() / pageSize);
        if (pageNo < 1) {
            pageNo = 1;
        } else if (pageNo > totalPage) {
            pageNo = totalPage;
        }

        int startIndex = (pageNo - 1) * pageSize;
        int endIndex = Math.min(startIndex + pageSize, detailList.size());
        List<PurchasedItemDetailBO> pageData = detailList.subList(startIndex, endIndex);

        if (CollectionUtils.isNotEmpty(pageData)) {
            builder.addAllRows(convert2DetailRow(pageData, param, asinSet));
        }
        builder.setTotalPage(totalPage);
        builder.setTotalSize(detailList.size());
        builder.setPageNo(pageNo);
        builder.setPageSize(pageSize);
    }

    private List<PurchasedItemDetailRow> convert2DetailRow(List<PurchasedItemDetailBO> pageData, PurchasedItemListParam param, Set<String> asinSet) {
        if (CollectionUtils.isEmpty(pageData)) {
            return Collections.emptyList();
        }
        List<String> portfolioIds = Lists.newArrayList();
        List<String> campaignIds = Lists.newArrayList();
        List<String> spGroupIds = Lists.newArrayList();
        List<String> sdGroupIds = Lists.newArrayList();
        Map<String, AmazonAdPortfolio> portfolioMap = new HashMap<>();
        Map<String, AmazonAdCampaignAll> campaignMap = new HashMap<>();
        Map<String, AmazonAdGroup> spGroupMap = new HashMap<>();
        Map<String, AmazonSdAdGroup> sdGroupMap = new HashMap<>();
        pageData.forEach(item -> {
            campaignIds.add(item.getCampaignId());
            if (Constants.SP.equalsIgnoreCase(item.getType())) {
                spGroupIds.add(item.getAdGroupId());
            } else {
                sdGroupIds.add(item.getAdGroupId());
            }
        });

        int puid = param.getPuid();
        // 获取详情，只会传一个shopId
        Integer shopId = param.getShopIdList().get(0);
        String marketplaceId = param.getMarketplaceId();

        List<String> asinList = Lists.newArrayList();
        List<String> adSelfGroupIds = Lists.newArrayList();
        List<String> otherAsinList = Lists.newArrayList();
        for (PurchasedItemDetailBO detail : pageData) {
            if (detail.adSelfAsin()) {
                adSelfGroupIds.add(detail.getAdGroupId());
                asinList.add(detail.getAsin());
            } else {
                otherAsinList.add(detail.getAsin());
            }
        }

        Map<String, List<String>> shopAsinSkuMap = getShopAsinSkuMap(puid, Lists.newArrayList(shopId), asinList, adSelfGroupIds);
        List<OdsProduct> otherAsinProducts = odsProductDao.listByAsins(puid, marketplaceId, Lists.newArrayList(shopId), otherAsinList);
        Map<String, List<String>> otherAsinSkuMap = otherAsinProducts.stream().collect(Collectors.groupingBy(OdsProduct::getAsin, Collectors.mapping(OdsProduct::getSku, Collectors.toList())));

        if (CollectionUtils.isNotEmpty(campaignIds)) {
            List<AmazonAdCampaignAll> campaignList = amazonAdCampaignAllDao.listByCampaignIdNoType(puid, shopId, campaignIds);
            List<String> portfolioIdList = campaignList.stream().map(AmazonAdCampaignAll::getPortfolioId).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(portfolioIdList)) {
                portfolioIds.addAll(portfolioIdList);
            }
            campaignMap = campaignList.stream().collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, Function.identity(), (e1, e2) -> e2));
        }
        if (CollectionUtils.isNotEmpty(spGroupIds)) {
            List<AmazonAdGroup> adGroupByIds = groupDao.getAdGroupByIds(puid, shopId, marketplaceId, spGroupIds);
            spGroupMap = adGroupByIds.stream().collect(Collectors.toMap(AmazonAdGroup::getAdGroupId, Function.identity(), (e1, e2) -> e2));
        }
        if (CollectionUtils.isNotEmpty(sdGroupIds)) {
            List<AmazonSdAdGroup> sdGroupByIds = sdGroupDao.listByGroupId(puid, shopId, sdGroupIds);
            sdGroupMap = sdGroupByIds.stream().collect(Collectors.toMap(AmazonSdAdGroup::getAdGroupId, Function.identity(), (e1, e2) -> e2));
        }
        if (CollectionUtils.isNotEmpty(portfolioIds)) {
            List<AmazonAdPortfolio> portfolioList = amazonAdPortfolioDao.getPortfolioList(puid, shopId, portfolioIds);
            portfolioMap = portfolioList.stream().collect(Collectors.toMap(AmazonAdPortfolio::getPortfolioId, Function.identity(), (e1, e2) -> e2));
        }


        List<PurchasedItemDetailRow> rowList = Lists.newArrayList();
        for (PurchasedItemDetailBO item : pageData) {
            PurchasedItemDetailRow.Builder builder = PurchasedItemDetailRow.newBuilder();
            builder.setAsin(item.getAsin())
                    .setCampaignId(item.getCampaignId())
                    .setAdGroupId(item.getAdGroupId())
                    .setType(item.getType());
            if (item.adSelfAsin()) {
                builder.setAdSelf(item.getAdSelf());
            } else {
                if (asinSet.contains(item.getAsin())) {
                    builder.setAdSelf(1);
                } else {
                    builder.setAdSelf(0);
                }
            }

            if (item.adSelfAsin()) {
                String key = shopId + "-" + item.getAsin() + "-" + item.getAdGroupId();
                if (shopAsinSkuMap.containsKey(key)) {
                    builder.addAllSku(shopAsinSkuMap.get(key).stream().distinct().collect(Collectors.toList()));
                }
            } else {
                List<String> skuList = otherAsinSkuMap.get(item.getAsin());
                if (CollectionUtils.isNotEmpty(skuList)) {
                    builder.addAllSku(skuList.stream().distinct().collect(Collectors.toList()));
                }
            }
            if (campaignMap.containsKey(item.getCampaignId())) {
                AmazonAdCampaignAll campaign = campaignMap.get(item.getCampaignId());
                builder.setCampaignName(campaign.getName());
                if (StringUtils.isNotBlank(campaign.getPortfolioId())) {
                    AmazonAdPortfolio portfolio = portfolioMap.get(campaign.getPortfolioId());
                    if (Objects.nonNull(portfolio)) {
                        builder.setPortfolioId(portfolio.getPortfolioId())
                                .setPortfolioName(portfolio.getName());
                    }
                }
            }
            if (Constants.SP.equalsIgnoreCase(item.getType())) {
                if (spGroupMap.containsKey(item.getAdGroupId())) {
                    AmazonAdGroup adGroup = spGroupMap.get(item.getAdGroupId());
                    builder.setAdGroupName(adGroup.getName());
                }
            }
            if (Constants.SD.equalsIgnoreCase(item.getType())) {
                if (sdGroupMap.containsKey(item.getAdGroupId())) {
                    AmazonSdAdGroup sdAdGroup = sdGroupMap.get(item.getAdGroupId());
                    builder.setAdGroupName(sdAdGroup.getName());
                }
            }
            if (Objects.nonNull(item.getAdSales())) {
                builder.setAdSales(item.getAdSales().setScale(2, RoundingMode.HALF_UP).toString());
            }
            if (Objects.nonNull(item.getAdSaleNum())) {
                builder.setAdSaleNum(item.getAdSaleNum().setScale(0, RoundingMode.HALF_UP).toString());
            }
            if (Objects.nonNull(item.getAdOrderNum())) {
                builder.setAdOrderNum(item.getAdOrderNum().setScale(0, RoundingMode.HALF_UP).toString());
            }
            if (Objects.nonNull(item.getSaleNumPercentage())) {
                builder.setSaleNumPercentage(percentage(item.getSaleNumPercentage()));
            }
            if (Objects.nonNull(item.getOrderNumPercentage())) {
                builder.setOrderNumPercentage(percentage(item.getOrderNumPercentage()));
            }
            rowList.add(builder.build());
        }
        return rowList;
    }

    public PurchasedItemSummary summary(PurchasedItemListParam param) {
        if (param.isNoNeedQuery()) {
            return PurchasedItemSummary.newBuilder().build();
        }
        param.setQuery4Summary(true);
        if (param.groupByParent()) {
            if (CollectionUtils.isNotEmpty(param.getCombineRequestList())) {
                param.setCombine4Parent(param.getCombineRequestList());
            }
        }
        PurchasedItemSummary.Builder builder = PurchasedItemSummary.newBuilder();
        builder.setSponsoredNum(purchasedItemDorisDao.getSponsoredCount(param));

        PurchasedItemListBO summaryData = purchasedItemDorisDao.getSummaryData(param);
        if (Objects.nonNull(summaryData)) {
            if (Objects.nonNull(summaryData.getTotalSales())) {
                builder.setAdSale(summaryData.getTotalSales().setScale(2, RoundingMode.HALF_UP).toString());
            }
            if (Objects.nonNull(summaryData.getAdSales())) {
                builder.setAdSelfSale(summaryData.getAdSales().setScale(2, RoundingMode.HALF_UP).toString());
            }
            if (Objects.nonNull(summaryData.getAdOtherSales())) {
                builder.setAdOtherSales(summaryData.getAdOtherSales().setScale(2, RoundingMode.HALF_UP).toString());
            }
            if (Objects.nonNull(summaryData.getSaleNum())) {
                builder.setAdOrderNum(summaryData.getSaleNum().setScale(0, RoundingMode.HALF_UP).toString());
            }
            if (Objects.nonNull(summaryData.getAdSaleNum())) {
                builder.setSelfAdOrderNum(summaryData.getAdSaleNum().setScale(0, RoundingMode.HALF_UP).toString());
            }
            if (Objects.nonNull(summaryData.getAdOtherSaleNum())) {
                builder.setOtherAdOrderNum(summaryData.getAdOtherSaleNum().setScale(0, RoundingMode.HALF_UP).toString());
            }
            if (Objects.nonNull(summaryData.getOrderNum())) {
                builder.setAdSaleNum(summaryData.getOrderNum().setScale(0, RoundingMode.HALF_UP).toString());
            }
            if (Objects.nonNull(summaryData.getAdOrderNum())) {
                builder.setAdSelfSaleNum(summaryData.getAdOrderNum().setScale(0, RoundingMode.HALF_UP).toString());
            }
            if (Objects.nonNull(summaryData.getAdOtherOrderNum())) {
                builder.setAdOtherSaleNum(summaryData.getAdOtherOrderNum().setScale(0, RoundingMode.HALF_UP).toString());
            }
            builder.setSaleNumBounce(percentage(MathUtil.divideByZero(summaryData.getAdOtherSaleNum(), summaryData.getSaleNum(), 4)));
            builder.setOrderNumBounce(percentage(MathUtil.divideByZero(summaryData.getAdOtherOrderNum(), summaryData.getOrderNum(), 4)));
        }
        return builder.build();
    }

    public PurchasedItemBubble bubbleData(PurchasedItemListParam param) {
        if (param.isNoNeedQuery()) {
            return PurchasedItemBubble.newBuilder().build();
        }

        param.setQuery4Bubble(true);
        if (param.groupByParent()) {
            if (CollectionUtils.isNotEmpty(param.getCombineRequestList())) {
                param.setCombine4Parent(param.getCombineRequestList());
            }
        }

        PurchasedItemBubble.Builder builder = PurchasedItemBubble.newBuilder();
        List<PurchasedItemListBO> bubbleData = purchasedItemDorisDao.getBubbleData(param);
        List<Integer> shopIds = bubbleData.stream().map(PurchasedItemListBO::getShopId).distinct().collect(Collectors.toList());

        List<ShopAuth> shopAuths = shopAuthDao.listAllByIds(param.getPuid(), shopIds);
        Map<Integer, ShopAuth> shopAuthMap = shopAuths.stream().collect(Collectors.toMap(ShopAuth::getId, v -> v, (v1, v2) -> v2));

        List<BubbleDetail> list = bubbleData.stream().map(item -> {
            BubbleDetail.Builder detailBuilder = BubbleDetail.newBuilder();
            if (param.groupByParent()) {
                detailBuilder.setAsin(item.getParentAsin());
            } else {
                detailBuilder.setAsin(item.getAsin());
            }
            detailBuilder.setShopId(item.getShopId());
            if (shopAuthMap.containsKey(item.getShopId())) {
                ShopAuth shopAuth = shopAuthMap.get(item.getShopId());
                detailBuilder.setShopName(shopAuth.getName());
            }
            if (Objects.nonNull(item.getAdSaleNum())) {
                detailBuilder.setSelfAdOrderNum(item.getAdSaleNum().setScale(2, RoundingMode.HALF_UP).toString());
            }
            if (Objects.nonNull(item.getAdOtherSaleNum())) {
                detailBuilder.setOtherAdOrderNum(item.getAdOtherSaleNum().setScale(2, RoundingMode.HALF_UP).toString());
            }
            if (Objects.nonNull(item.getCost())) {
                detailBuilder.setAdCost(item.getCost().setScale(2, RoundingMode.HALF_UP).toString());
            }
            return detailBuilder.build();
        }).collect(Collectors.toList());
        builder.addAllBubbleDetails(list);
        return builder.build();
    }

    @Async("purchasedItemExportTaskExecutor")
    public void export(PurchasedItemListParam param) {
        if (param.isNoNeedQuery()) {
            stringRedisService.set(param.getUuid(), new ProcessMsg(-1, 0, "没有可供导出的数据"));
        }
        PurchasedItemListParam originParam = new PurchasedItemListParam();
        BeanUtils.copyProperties(param, originParam);

        int puid = param.getPuid();
        List<String> urlList = Lists.newArrayList();
        WriteHandlerBuild build = new WriteHandlerBuild().rate().currencyNew(PurchasedParentItemExportVO.class);

        // 父行数据
        List<PurchasedItemListRow> parentRowData = getParentRowData(param);
        if (CollectionUtils.isEmpty(parentRowData)) {
            stringRedisService.set(param.getUuid(), new ProcessMsg(-1, 0, "没有可供导出的数据"));
        }
        List<PurchasedParentItemExportVO> parentExportList = buildParentExportList(parentRowData, param);
        if (CollectionUtils.isNotEmpty(parentExportList)) {
            String fileName = "已购商品分析_" + (param.groupByParent() ? "按父ASIN聚合" : "按ASIN聚合");
            ExcelExtraParam excelExtraParam = new ExcelExtraParam();
            excelExtraParam.setWrapped(true);

            List<PurchasedChildItemExportVO> childExportList = Lists.newArrayList();
            for (PurchasedParentItemExportVO parentExportVO : parentExportList) {
                if (CollectionUtils.size(childExportList) >= 30000) {
                    break;
                }
                PurchasedItemListParam childParam = new PurchasedItemListParam();
                BeanUtils.copyProperties(originParam, childParam);
                childParam.setAsinList(Collections.singletonList(parentExportVO.getAsin()));
                childParam.setShopIdList(Collections.singletonList(parentExportVO.getShopId()));
//                if (StringUtils.isNotBlank(parentExportVO.getMskuList())) {
//                    childParam.setSkuList(Arrays.stream(parentExportVO.getMskuList().split("\n")).distinct().collect(Collectors.toList()));
//                }
                childParam.setPageNo(1);
                childParam.setPageSize(30000);
                childParam.setNotNeedTopRows(true);
                childParam.setNeedParentSelf(false);
                PurchasedItemDetailListResponse itemDetail = getPurchasedItemDetail(childParam);
                List<PurchasedChildItemExportVO> exportList = buildChildExportList(itemDetail, childParam);
                if (CollectionUtils.isNotEmpty(exportList)) {
                    childExportList.addAll(exportList);
                }
            }

            urlList.add(excelService.easyExcelHandlerExportWithExtraParamMultiSheets(puid, Lists.newArrayList(parentExportList, childExportList),
                    fileName, Lists.newArrayList(PurchasedParentItemExportVO.class, PurchasedChildItemExportVO.class), build,
                    buildVoExcludeFields(param), excelExtraParam));

        }
        if (CollectionUtils.isEmpty(urlList)) {
            stringRedisService.set(param.getUuid(), new ProcessMsg(-1, 0, "导出数据为空"));
        }
        stringRedisService.set(param.getUuid(), new ProcessMsg(1, urlList.size(), "导出成功", urlList));
    }

    private List<PurchasedItemListRow> getParentRowData(PurchasedItemListParam param) {
        List<PurchasedItemListRow> resList = Lists.newArrayList();
        param.setPageNo(1);
        int totalPage = -1;
        while (param.getPageNo() <= totalPage || totalPage == -1) { // 若totalPage还未知 或 没有达到totalPage 继续请求
            if (resList.size() >= 30000) {
                break;
            }
            Page<PurchasedItemListBO> listPage;
            if (param.groupByParent()) {
                listPage = purchasedItemDorisDao.listParentAsinPage(param);
            } else {
                listPage = purchasedItemDorisDao.listAsinPage(param);
            }
            if (totalPage == -1) {
                if (CollectionUtils.isEmpty(listPage.getRows())) {
                    // 初次请求时就没有数据
                    return resList;
                } else {
                    totalPage = listPage.getTotalPage();
                }
            }
            resList.addAll(buildRows(param, listPage));
            param.getCombine4Parent().clear();
            param.setPageNo(param.getPageNo() + 1);
        }
        return resList;
    }

    private List<String> buildVoExcludeFields(PurchasedItemListParam param) {
        if (param.groupByParent()) {
            return Lists.newArrayList("shopId", "quantity", "attribute", "parentAsinList");
        }
        return Lists.newArrayList("shopId");
    }

    private List<PurchasedParentItemExportVO> buildParentExportList(List<PurchasedItemListRow> parentRowsList, PurchasedItemListParam param) {
        if (CollectionUtils.isEmpty(parentRowsList)) {
            return Collections.emptyList();
        }
        List<Integer> shopIdList = parentRowsList.stream().map(PurchasedItemListRow::getShopId).distinct().collect(Collectors.toList());

        Map<Integer, String> nameMap = Maps.newHashMap();
        Map<Long, List<ProductDeveloperRela>> relaMap = Maps.newHashMap();
        Map<Long, List<ProductLabelItemVo>> labelMap = Maps.newHashMap();
        List<OdsProduct> productList;
        Map<Long, OdsProduct> productResMap = Maps.newHashMap();

        List<List<PurchasedItemListRow>> splitRowList = Lists.newArrayList();
        if (CollectionUtils.size(parentRowsList) > 200) {
            splitRowList = Lists.newArrayList(Iterables.partition(parentRowsList, 200));
        } else {
            splitRowList.add(parentRowsList);
        }
        for (List<PurchasedItemListRow> rowList : splitRowList) {
            List<String> asinList = rowList.stream().map(PurchasedItemListRow::getAsin).distinct().collect(Collectors.toList());
            List<String> asinSkuList = rowList.stream().map(PurchasedItemListRow::getSkuList).filter(CollectionUtils::isNotEmpty)
                    .flatMap(List::stream).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
            PurchasedProductInfoVO resultInfo = productApi.getProductLabelDevs(param.getPuid(), param.getUid(),
                    param.getMarketplaceId(), param.getAsinType(), shopIdList, asinList, asinSkuList);
            if (Objects.nonNull(resultInfo)) {
                if (MapUtils.isEmpty(nameMap)) {
                    nameMap = resultInfo.getNameMap();
                }
                if (CollectionUtils.isNotEmpty(resultInfo.getProductList())) {
                    productResMap.putAll(resultInfo.getProductList().stream().collect(Collectors.toMap(OdsProduct::getId, v -> v, (v1, v2) -> v2)));
                }
                if (MapUtils.isNotEmpty(resultInfo.getRelaMap())) {
                    relaMap.putAll(resultInfo.getRelaMap());
                }
                if (MapUtils.isNotEmpty(resultInfo.getLabelMap())) {
                    labelMap.putAll(resultInfo.getLabelMap());
                }
            }
        }
        productList = Lists.newArrayList(productResMap.values());

        Map<String, List<OdsProduct>> productMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(productList)) {
            if (param.groupByParent()) {
                productMap = productList.stream().collect(Collectors.groupingBy(p -> {
                    String asin = StringUtils.isNotBlank(p.getParentAsin()) ? p.getParentAsin() : p.getAsin();
                    return p.getShopId() + "-" + asin + "-" + p.getSku();
                }));
            } else {
                productMap = productList.stream().collect(Collectors.groupingBy(p -> p.getShopId() + "-" + p.getAsin() + "-" + p.getSku()));
            }
        }

        List<PurchasedParentItemExportVO> exportVOList = Lists.newArrayList();
        for (PurchasedItemListRow item : parentRowsList) {
            PurchasedParentItemExportVO exportVO = buildParentExportVO(param, item, productMap, nameMap, relaMap, labelMap);
            exportVOList.add(exportVO);
        }
        return exportVOList;
    }

    private PurchasedParentItemExportVO buildParentExportVO(PurchasedItemListParam param, PurchasedItemListRow item,
                                                            Map<String, List<OdsProduct>> productMap, Map<Integer, String> nameMap,
                                                            Map<Long, List<ProductDeveloperRela>> relaMap, Map<Long, List<ProductLabelItemVo>> labelMap) {
        List<OdsProduct> productList = item.getSkuList().stream().map(s -> productMap.get(item.getShopId() + "-" + item.getAsin() + "-" + s))
                .filter(CollectionUtils::isNotEmpty).flatMap(List::stream).collect(Collectors.toList());

        PurchasedParentItemExportVO exportVO = new PurchasedParentItemExportVO();
        exportVO.setAsin(item.getAsin());
        exportVO.setShopId(item.getShopId());
        exportVO.setMskuList(StringUtils.join(item.getSkuList(), "\n"));

        if (CollectionUtils.isNotEmpty(productList)) {
            if (!param.groupByParent()) {
                exportVO.setParentAsinList(productList.stream()
                        .map(i -> StringUtils.isNotBlank(i.getParentAsin()) ? i.getParentAsin() : i.getAsin())
                        .filter(StringUtils::isNotBlank).distinct().collect(Collectors.joining("、")));
                productList.stream().map(OdsProduct::getQuantity).filter(i -> Objects.nonNull(i) && i > 0).reduce(Integer::sum).ifPresent(exportVO::setQuantity);
                Optional<String> variationChildStr = productList.stream().map(OdsProduct::getVariationChildStr).filter(StringUtils::isNotBlank)
                        .filter(i -> JSONValidator.from(i).validate()).findFirst();
                if (variationChildStr.isPresent()) {
                    JSONObject jsonObject = null;
                    try {
                        jsonObject = JSON.parseObject(variationChildStr.get());
                    } catch (Exception ignore) {
                        log.info("no valid json variationChildStr: {}", variationChildStr.get());
                    }
                    if (jsonObject != null) {
                        String color = jsonObject.containsKey("color") ? jsonObject.getString("color") : "";
                        String size = jsonObject.containsKey("size") ? jsonObject.getString("size") : "";
                        exportVO.setAttribute(Stream.of(color, size).filter(StringUtils::isNotBlank).collect(Collectors.joining("\n")));
                    }
                }
            }

            Map<String, List<String>> labelsMap = Maps.newHashMap();
            Map<String, List<String>> devNamesMap = Maps.newHashMap();
            Map<String, String> titleSkuMap = Maps.newHashMap();
            productList.forEach(p -> {
                String msku = p.getSku();

                // 品名 SKU
                String title = StringUtils.isNotBlank(p.getCommodityName()) ? p.getCommodityName() : "";
                String commoditySku = StringUtils.isNotBlank(p.getCommoditySku()) ? p.getCommoditySku() : "";
                String titleAndSku = Stream.of(title, commoditySku).filter(StringUtils::isNotBlank).collect(Collectors.joining("/"));
                if (StringUtils.isNotBlank(titleAndSku)) {
                    titleSkuMap.put(msku, titleAndSku);
                }

                // 业务员
                List<Integer> devIds = Lists.newArrayList();
                List<String> devNames = Lists.newArrayList();
                if (StringUtils.isNotBlank(p.getDevId())) {
                    devIds.add(Integer.parseInt(p.getDevId()));
                    if (MapUtils.isNotEmpty(nameMap)) {
                        String devName = nameMap.get(Integer.parseInt(p.getDevId()));
                        if (StringUtils.isNotBlank(devName)) {
                            devNames.add(devName);
                        }
                    }
                }
                if (MapUtils.isNotEmpty(relaMap)) {
                    List<ProductDeveloperRela> relaList = relaMap.get(p.getId());
                    if (CollectionUtils.isNotEmpty(relaList)) {
                        List<Integer> relaDevIds = relaList.stream().map(ProductDeveloperRela::getDevId).filter(Objects::nonNull).collect(Collectors.toList());
                        devIds.addAll(relaDevIds);
                    }
                }
                if (CollectionUtils.isNotEmpty(devIds) && MapUtils.isNotEmpty(nameMap)) {
                    List<Integer> devIdList = devIds.stream().distinct().collect(Collectors.toList());
                    List<String> devNameList = devIdList.stream().map(nameMap::get).filter(StringUtils::isNotBlank).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(devNameList)) {
                        devNames.addAll(devNameList);
                    }
                }
                if (CollectionUtils.isNotEmpty(devNames)) {
                    String devNameStr = devNames.stream().filter(StringUtils::isNotBlank).distinct().collect(Collectors.joining("、"));
                    List<String> devList = devNamesMap.get(msku);
                    if (CollectionUtils.isEmpty(devList)) {
                        devList = Lists.newArrayList(devNameStr);
                        devNamesMap.put(msku, devList);
                    } else {
                        devList.addAll(Lists.newArrayList(devNameStr));
                    }
                }

                // 产品标签
                List<ProductLabelItemVo> labels = labelMap.get(p.getId());
                if (CollectionUtils.isNotEmpty(labels)) {
                    String labelStr = labels.stream().map(ProductLabelItemVo::getLabelName).filter(StringUtils::isNotBlank).collect(Collectors.joining("、"));
                    List<String> labelList = labelsMap.get(msku);
                    if (CollectionUtils.isEmpty(labelList)) {
                        labelList = Lists.newArrayList(labelStr);
                        labelsMap.put(msku, labelList);
                    } else {
                        labelList.addAll(Lists.newArrayList(labelStr));
                    }
                }
            });
            if (MapUtils.isNotEmpty(labelsMap)) {
                List<String> labelList = labelsMap.entrySet().stream().map(entry -> entry.getKey() + "：" + String.join("、", entry.getValue())).collect(Collectors.toList());
                exportVO.setLabels(String.join("\n", labelList));
            }
            if (MapUtils.isNotEmpty(devNamesMap)) {
                List<String> devList = devNamesMap.entrySet().stream().map(entry -> entry.getKey() + "：" + String.join("、", entry.getValue())).collect(Collectors.toList());
                exportVO.setDevNames(String.join("\n", devList));
            }
            if (MapUtils.isNotEmpty(titleSkuMap)) {
                exportVO.setTitleAndSku(joinMapWithLengthLimit(titleSkuMap, SpreadsheetVersion.EXCEL2007.getMaxTextLength()));
            }
        }

        exportVO.setSaleNumBounce(StringUtils.isNotBlank(item.getSaleNumBounce()) ? item.getSaleNumBounce() + "%" : "");
        exportVO.setOrderNumBounce(StringUtils.isNotBlank(item.getOrderNumBounce()) ? item.getOrderNumBounce() + "%" : "");

        exportVO.setOrderNum(item.getOrderNum());
        exportVO.setTotalSales(item.getTotalSales());
        exportVO.setAdOrderNum(item.getAdOrderNum());
        exportVO.setAdSales(item.getAdSales());
        exportVO.setAdOtherOrderNum(item.getAdOtherOrderNum());
        exportVO.setAdOtherSales(item.getAdOtherSales());
        exportVO.setSaleNum(item.getSaleNum());
        exportVO.setAdSaleNum(item.getAdSaleNum());
        exportVO.setAdOtherSaleNum(item.getAdOtherSaleNum());
        exportVO.setImpressions(String.valueOf(item.getImpressions()));
        exportVO.setClicks(String.valueOf(item.getClicks()));
        exportVO.setCtr(StringUtils.isNotBlank(item.getCtr()) ? item.getCtr() + "%" : "");
        exportVO.setCpc(StringUtils.isNotBlank(item.getCpc()) ? item.getCpc() : "");
        exportVO.setCvr(StringUtils.isNotBlank(item.getCvr()) ? item.getCvr() + "%" : "");
        exportVO.setCost(item.getCost());
        exportVO.setAcos(StringUtils.isNotBlank(item.getAcos()) ? item.getAcos() + "%" : "");
        exportVO.setRoas(StringUtils.isNotBlank(item.getRoas()) ? item.getRoas() : "");
        return exportVO;
    }

    private List<PurchasedChildItemExportVO> buildChildExportList(PurchasedItemDetailListResponse itemDetail, PurchasedItemListParam param) {
        if (Objects.isNull(itemDetail) || CollectionUtils.isEmpty(itemDetail.getRowsList())) {
            return Collections.emptyList();
        }
        List<PurchasedItemDetailRow> rowsList = itemDetail.getRowsList();

        Map<Integer, String> nameMap = Maps.newHashMap();
        Map<Long, List<ProductDeveloperRela>> relaMap = Maps.newHashMap();
        Map<Long, List<ProductLabelItemVo>> labelMap = Maps.newHashMap();
        List<OdsProduct> productList;
        Map<Long, OdsProduct> productResMap = Maps.newHashMap();

        List<List<PurchasedItemDetailRow>> splitRowList = Lists.newArrayList();
        if (CollectionUtils.size(rowsList) > 200) {
            splitRowList = Lists.newArrayList(Iterables.partition(rowsList, 200));
        } else {
            splitRowList.add(rowsList);
        }
        for (List<PurchasedItemDetailRow> detailRows : splitRowList) {
            List<String> asinList = detailRows.stream().map(PurchasedItemDetailRow::getAsin).distinct().collect(Collectors.toList());
            List<String> asinSkuList = detailRows.stream().map(PurchasedItemDetailRow::getSkuList).filter(CollectionUtils::isNotEmpty)
                    .flatMap(List::stream).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
            PurchasedProductInfoVO resultInfo = productApi.getProductLabelDevs(param.getPuid(), param.getUid(),
                    param.getMarketplaceId(), "asin", param.getShopIdList(), asinList, asinSkuList);
            if (Objects.nonNull(resultInfo)) {
                if (MapUtils.isEmpty(nameMap)) {
                    nameMap = resultInfo.getNameMap();
                }
                if (CollectionUtils.isNotEmpty(resultInfo.getProductList())) {
                    productResMap.putAll(resultInfo.getProductList().stream().collect(Collectors.toMap(OdsProduct::getId, v -> v, (v1, v2) -> v2)));
                }
                if (MapUtils.isNotEmpty(resultInfo.getRelaMap())) {
                    relaMap.putAll(resultInfo.getRelaMap());
                }
                if (MapUtils.isNotEmpty(resultInfo.getLabelMap())) {
                    labelMap.putAll(resultInfo.getLabelMap());
                }
            }
        }
        productList = Lists.newArrayList(productResMap.values());
        Map<String, List<OdsProduct>> productMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(productList)) {
            productMap = productList.stream().collect(Collectors.groupingBy(p -> p.getAsin() + "-" + p.getSku()));
        }

        // 推广sku
        List<PurchasedItemDetailRow> spOtherAsinList = Lists.newArrayList();
        List<PurchasedItemDetailRow> sdOtherAsinList = Lists.newArrayList();
        for (PurchasedItemDetailRow item : rowsList) {
            if (item.getAdSelf() == PurchasedItemDetailBO.AD_SELF) {
                continue;
            }
            if (StringUtils.equalsIgnoreCase(Constants.SP, item.getType())) {
                spOtherAsinList.add(item);
            }
            if (StringUtils.equalsIgnoreCase(Constants.SD, item.getType())) {
                sdOtherAsinList.add(item);
            }
        }
        Map<String, List<PurchasedItemDetailBO>> spOtherAsinSkuMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(spOtherAsinList)) {
            List<String> asinList = spOtherAsinList.stream().map(PurchasedItemDetailRow::getAsin).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
            List<String> groupIdList = spOtherAsinList.stream().map(PurchasedItemDetailRow::getAdGroupId).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
            List<PurchasedItemDetailBO> sponsoredAsinSku = purchasedItemDorisDao.getSponsoredAsinSku(param.getPuid(), param.getMarketplaceId(), Constants.SP,
                    param.getShopIdList(), asinList, groupIdList, param.getStartDate(), param.getEndDate());
            if (CollectionUtils.isNotEmpty(sponsoredAsinSku)) {
                spOtherAsinSkuMap = sponsoredAsinSku.stream().collect(Collectors.groupingBy(i -> i.getOtherAsin() + "-" + i.getAdGroupId()));
            }
        }
        Map<String, List<PurchasedItemDetailBO>> sdOtherAsinSkuMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(sdOtherAsinList)) {
            List<String> asinList = sdOtherAsinList.stream().map(PurchasedItemDetailRow::getAsin).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
            List<String> groupIdList = sdOtherAsinList.stream().map(PurchasedItemDetailRow::getAdGroupId).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
            List<PurchasedItemDetailBO> sdonsoredAsinSku = purchasedItemDorisDao.getSponsoredAsinSku(param.getPuid(), param.getMarketplaceId(), Constants.SD,
                    param.getShopIdList(), asinList, groupIdList, param.getStartDate(), param.getEndDate());
            if (CollectionUtils.isNotEmpty(sdonsoredAsinSku)) {
                sdOtherAsinSkuMap = sdonsoredAsinSku.stream().collect(Collectors.groupingBy(i -> i.getOtherAsin() + "-" + i.getAdGroupId()));
            }
        }

        List<PurchasedChildItemExportVO> exportVOList = Lists.newArrayList();
        for (PurchasedItemDetailRow item : rowsList) {
            PurchasedChildItemExportVO exportVO = buildChildExportVO(item, productMap, nameMap, relaMap, labelMap, spOtherAsinSkuMap, sdOtherAsinSkuMap);
            exportVOList.add(exportVO);
        }
        return exportVOList;
    }

    private PurchasedChildItemExportVO buildChildExportVO(PurchasedItemDetailRow item, Map<String, List<OdsProduct>> productMap,
                                                          Map<Integer, String> nameMap, Map<Long, List<ProductDeveloperRela>> relaMap,
                                                          Map<Long, List<ProductLabelItemVo>> labelMap,
                                                          Map<String, List<PurchasedItemDetailBO>> spOtherAsinSkuMap,
                                                          Map<String, List<PurchasedItemDetailBO>> sdOtherAsinSkuMap) {
        List<OdsProduct> productList = item.getSkuList().stream().map(sku -> productMap.get(item.getAsin() + "-" + sku)).filter(CollectionUtils::isNotEmpty)
                .flatMap(List::stream).collect(Collectors.toList());
        PurchasedChildItemExportVO exportVO = new PurchasedChildItemExportVO();
        exportVO.setPurchasedAsin(item.getAsin());

        Optional<String> variationChildStr = productList.stream().map(OdsProduct::getVariationChildStr).filter(StringUtils::isNotBlank)
                .filter(i -> JSONValidator.from(i).validate()).findFirst();
        if (variationChildStr.isPresent()) {
            JSONObject jsonObject = null;
            try {
                jsonObject = JSON.parseObject(variationChildStr.get());
            } catch (Exception ignore) {
                log.info("no valid json variationChildStr: {}", variationChildStr.get());
            }
            if (jsonObject != null) {
                String color = jsonObject.containsKey("color") ? jsonObject.getString("color") : "";
                String size = jsonObject.containsKey("size") ? jsonObject.getString("size") : "";
                exportVO.setAttributes(Stream.of(color, size).filter(StringUtils::isNotBlank).collect(Collectors.joining("\n")));
            }
        }

        if (item.getAdSelf() == PurchasedItemDetailBO.AD_SELF) {
            exportVO.setSponsoredSkuAndAsin(item.getAsin() + "：" + StringUtils.join(item.getSkuList(), "、"));
        } else {
            if (StringUtils.equalsIgnoreCase(Constants.SP, item.getType())) {
                List<PurchasedItemDetailBO> details = spOtherAsinSkuMap.get(item.getAsin() + "-" + item.getAdGroupId());
                if (CollectionUtils.isNotEmpty(details)) {
                    Map<String, List<String>> asinSkuMap = details.stream().collect(Collectors.groupingBy(PurchasedItemDetailBO::getAsin, Collectors.mapping(PurchasedItemDetailBO::getSku, Collectors.toList())));
                    String sponsoredSkuAndAsin = asinSkuMap.entrySet().stream().map(e -> e.getKey() + "：" + StringUtils.join(e.getValue(), "、")).collect(Collectors.joining("\n"));
                    exportVO.setSponsoredSkuAndAsin(sponsoredSkuAndAsin);
                }
            }
            if (StringUtils.equalsIgnoreCase(Constants.SD, item.getType())) {
                List<PurchasedItemDetailBO> details = sdOtherAsinSkuMap.get(item.getAsin() + "-" + item.getAdGroupId());
                if (CollectionUtils.isNotEmpty(details)) {
                    Map<String, List<String>> asinSkuMap = details.stream().collect(Collectors.groupingBy(PurchasedItemDetailBO::getAsin, Collectors.mapping(PurchasedItemDetailBO::getSku, Collectors.toList())));
                    String sponsoredSkuAndAsin = asinSkuMap.entrySet().stream().map(e -> e.getKey() + "：" + StringUtils.join(e.getValue(), "、")).collect(Collectors.joining("\n"));
                    exportVO.setSponsoredSkuAndAsin(sponsoredSkuAndAsin);
                }
            }
        }

        Map<String, List<String>> labelsMap = Maps.newHashMap();
        Map<String, List<String>> devNamesMap = Maps.newHashMap();
        Map<String, String> titleSkuMap = Maps.newHashMap();
        productList.forEach(p -> {
            String msku = p.getSku();

            // 品名 SKU
            String title = StringUtils.isNotBlank(p.getCommodityName()) ? p.getCommodityName() : "";
            String commoditySku = StringUtils.isNotBlank(p.getCommoditySku()) ? p.getCommoditySku() : "";
            String titleAndSku = Stream.of(title, commoditySku).filter(StringUtils::isNotBlank).collect(Collectors.joining("/"));
            if (StringUtils.isNotBlank(titleAndSku)) {
                titleSkuMap.put(msku, titleAndSku);
            }

            // 业务员
            List<Integer> devIds = Lists.newArrayList();
            List<String> devNames = Lists.newArrayList();
            if (StringUtils.isNotBlank(p.getDevId())) {
                devIds.add(Integer.parseInt(p.getDevId()));
                if (MapUtils.isNotEmpty(nameMap)) {
                    String devName = nameMap.get(Integer.parseInt(p.getDevId()));
                    if (StringUtils.isNotBlank(devName)) {
                        devNames.add(devName);
                    }
                }
            }
            if (MapUtils.isNotEmpty(relaMap)) {
                List<ProductDeveloperRela> relaList = relaMap.get(p.getId());
                if (CollectionUtils.isNotEmpty(relaList)) {
                    List<Integer> relaDevIds = relaList.stream().map(ProductDeveloperRela::getDevId).filter(Objects::nonNull).collect(Collectors.toList());
                    devIds.addAll(relaDevIds);
                }
            }
            if (CollectionUtils.isNotEmpty(devIds) && MapUtils.isNotEmpty(nameMap)) {
                List<Integer> devIdList = devIds.stream().distinct().collect(Collectors.toList());
                List<String> devNameList = devIdList.stream().map(nameMap::get).filter(StringUtils::isNotBlank).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(devNameList)) {
                    devNames.addAll(devNameList);
                }
            }
            if (CollectionUtils.isNotEmpty(devNames)) {
                String devNameStr = devNames.stream().filter(StringUtils::isNotBlank).distinct().collect(Collectors.joining("、"));
                List<String> devList = devNamesMap.get(msku);
                if (CollectionUtils.isEmpty(devList)) {
                    devList = Lists.newArrayList(devNameStr);
                    devNamesMap.put(msku, devList);
                } else {
                    devList.addAll(Lists.newArrayList(devNameStr));
                }
            }

            // 产品标签
            List<ProductLabelItemVo> labels = labelMap.get(p.getId());
            if (CollectionUtils.isNotEmpty(labels)) {
                String labelStr = labels.stream().map(ProductLabelItemVo::getLabelName).filter(StringUtils::isNotBlank).collect(Collectors.joining("、"));
                List<String> labelList = labelsMap.get(msku);
                if (CollectionUtils.isEmpty(labelList)) {
                    labelList = Lists.newArrayList(labelStr);
                    labelsMap.put(msku, labelList);
                } else {
                    labelList.addAll(Lists.newArrayList(labelStr));
                }
            }
        });
        if (MapUtils.isNotEmpty(labelsMap)) {
            List<String> labelList = labelsMap.entrySet().stream().map(entry -> entry.getKey() + "：" + String.join("、", entry.getValue())).collect(Collectors.toList());
            exportVO.setLabels(String.join("\n", labelList));
        }
        if (MapUtils.isNotEmpty(devNamesMap)) {
            List<String> devList = devNamesMap.entrySet().stream().map(entry -> entry.getKey() + "：" + String.join("、", entry.getValue())).collect(Collectors.toList());
            exportVO.setDevNames(String.join("\n", devList));
        }
        if (MapUtils.isNotEmpty(titleSkuMap)) {
            exportVO.setTitleAndSku(joinMapWithLengthLimit(titleSkuMap, SpreadsheetVersion.EXCEL2007.getMaxTextLength()));
        }

        exportVO.setPortfolioName(item.getPortfolioName());
        exportVO.setCampaignName(item.getCampaignName());
        exportVO.setAdGroupName(item.getAdGroupName());

        exportVO.setAdOrderNum(item.getAdOrderNum());
        exportVO.setOrderNumPercentage(StringUtils.isNotBlank(item.getOrderNumPercentage()) ? item.getOrderNumPercentage() + "%" : "");
        exportVO.setAdSales(item.getAdSales());
        exportVO.setAdSaleNum(item.getAdSaleNum());
        exportVO.setSaleNumPercentage(StringUtils.isNotBlank(item.getSaleNumPercentage()) ? item.getSaleNumPercentage() + "%" : "");
        return exportVO;
    }

    // ============================ refactor ============================

    public PurchasedItemList listPage(PurchasedItemListParam param) {
        log.info("purchased_item getListPage param {}", JSON.toJSONString(param));
        PurchasedItemList.Builder listBuilder = PurchasedItemList.newBuilder();
        listBuilder.setPageNo(param.getPageNo());
        listBuilder.setPageSize(param.getPageSize());
        listBuilder.setTotalPage(0);
        listBuilder.setTotalSize(0);
        listBuilder.addAllRows(Lists.newArrayList());
        PurchasedItemList emptyResponse = listBuilder.build();
        if (param.isNoNeedQuery()) {
            return emptyResponse;
        }

        Page<PurchasedItemListBO> page;
        if (param.groupByParent()) {
            page = purchasedItemDorisDao.listParentAsinPage(param);
        } else {
            page = purchasedItemDorisDao.listAsinPage(param);
        }

        listBuilder.setPageNo(page.getPageNo());
        listBuilder.setPageSize(page.getPageSize());
        listBuilder.setTotalPage(page.getTotalPage());
        listBuilder.setTotalSize(page.getTotalSize());
        listBuilder.addAllRows(buildRows(param, page));
        return listBuilder.build();
    }

    private List<PurchasedItemListRow> buildRows(PurchasedItemListParam param, Page<PurchasedItemListBO> page) {
        List<PurchasedItemListBO> rows = page.getRows();
        if (CollectionUtils.isEmpty(rows)) {
            return Collections.emptyList();
        }

        List<PurchasedItemListBO> reportList = Lists.newArrayList();
        if (param.groupByParent()) {
            List<OdsProduct> parentProductList = rows.stream().map(i -> {
                OdsProduct p = new OdsProduct();
                p.setShopId(i.getShopId());
                p.setParentAsin(i.getParentAsin());
                return p;
            }).collect(Collectors.toList());

            List<OdsProduct> productList = odsSpSdProductReportDao.listProductByParent(param, false, parentProductList);
            List<PurchasedItemListParam.CombineRequest> combineRequestList = productList.stream().map(i -> {
                PurchasedItemListParam.CombineRequest combineRequest = new PurchasedItemListParam.CombineRequest();
                combineRequest.setShopId(i.getShopId());
                combineRequest.setAsin(i.getAsin());
                combineRequest.setSku(i.getSku());
                return combineRequest;
            }).collect(Collectors.toList());
            param.setCombine4Parent(combineRequestList);

            List<PurchasedItemListBO> list = purchasedItemDorisDao.parentAsinReportList(param, rows);
            if (CollectionUtils.isNotEmpty(list)) {
                reportList.addAll(list);
            }
        } else {
            List<PurchasedItemListBO> list = purchasedItemDorisDao.asinReportList(param, rows);
            if (CollectionUtils.isNotEmpty(list)) {
                reportList.addAll(list);
            }
        }
        Map<String, PurchasedItemListBO> reportMap = reportList.stream()
                .collect(Collectors.toMap(k -> k.getShopId() + "-" + (param.groupByParent() ? k.getParentAsin() : k.getAsin()),
                        v -> v, (v1, v2) -> v1));

        int puid = param.getPuid();
        List<Integer> shopIdList = rows.stream().map(PurchasedItemListBO::getShopId).distinct().collect(Collectors.toList());
        List<ShopAuth> shopAuths = shopAuthDao.listAllByIds(puid, shopIdList);
        Map<Integer, ShopAuth> shopAuthMap = shopAuths.stream().collect(Collectors.toMap(ShopAuth::getId, v -> v, (v1, v2) -> v2));

        // asin维度聚合，反查推广msku
        List<String> asinList = rows.stream().map(PurchasedItemListBO::getAsin).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        Map<String, List<String>> shopAsinSkuMap = getShopAsinSkuMap(puid, shopIdList, asinList, null);

        // 父asin维度聚合，查出父asin对应的推广msku
        List<String> parentAsinList = rows.stream().map(PurchasedItemListBO::getParentAsin).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        Map<String, List<String>> shopParentAsinSkuMap = getShopParentAisnSkuMap(puid, shopIdList, parentAsinList);

        return rows.stream().map(item -> {
            PurchasedItemListRow.Builder rowBuilder = PurchasedItemListRow.newBuilder();

            Integer shopId = item.getShopId();
            rowBuilder.setShopId(shopId);
            ShopAuth shopAuth = shopAuthMap.get(shopId);
            if (shopAuth != null) {
                rowBuilder.setShopName(shopAuth.getName());
            }

            String key;
            if (param.groupByParent()) {
                rowBuilder.setAsin(item.getParentAsin());
                key = shopId + "-" + item.getParentAsin();
                if (shopParentAsinSkuMap.containsKey(key)) {
                    rowBuilder.addAllSku(shopParentAsinSkuMap.get(key).stream().distinct().collect(Collectors.toList()));
                }
            } else {
                String asin = item.getAsin();
                rowBuilder.setAsin(asin);
                key = shopId + "-" + asin;
                if (shopAsinSkuMap.containsKey(key)) {
                    rowBuilder.addAllSku(shopAsinSkuMap.get(key).stream().distinct().collect(Collectors.toList()));
                }
            }

            if (reportMap.containsKey(key)) {
                PurchasedItemListBO report = reportMap.get(key);
                Integer clicks = report.getClicks();
                Integer impressions = report.getImpressions();
                if (Objects.nonNull(report.getCost())) {
                    rowBuilder.setCost(report.getCost().setScale(2, RoundingMode.HALF_UP).toPlainString());
                }
                if (Objects.nonNull(impressions)) {
                    rowBuilder.setImpressions(impressions);
                }
                if (Objects.nonNull(clicks)) {
                    rowBuilder.setClicks(clicks);
                }

                if (Objects.nonNull(report.getTotalSales())) {
                    rowBuilder.setTotalSales(report.getTotalSales().setScale(2, RoundingMode.HALF_UP).toPlainString());
                }
                if (Objects.nonNull(report.getAdSales())) {
                    rowBuilder.setAdSales(report.getAdSales().setScale(2, RoundingMode.HALF_UP).toPlainString());
                }
                if (Objects.nonNull(report.getAdOtherSales())) {
                    rowBuilder.setAdOtherSales(report.getAdOtherSales().setScale(2, RoundingMode.HALF_UP).toPlainString());
                }

                if (Objects.nonNull(report.getSaleNum())) {
                    rowBuilder.setSaleNum(report.getSaleNum().setScale(0, RoundingMode.HALF_UP).toPlainString());
                }
                if (Objects.nonNull(report.getAdSaleNum())) {
                    rowBuilder.setAdSaleNum(report.getAdSaleNum().setScale(0, RoundingMode.HALF_UP).toPlainString());
                }
                if (Objects.nonNull(report.getAdOtherSaleNum())) {
                    rowBuilder.setAdOtherSaleNum(report.getAdOtherSaleNum().setScale(0, RoundingMode.HALF_UP).toPlainString());
                }

                if (Objects.nonNull(report.getOrderNum())) {
                    rowBuilder.setOrderNum(report.getOrderNum().setScale(0, RoundingMode.HALF_UP).toPlainString());
                }
                if (Objects.nonNull(report.getAdOrderNum())) {
                    rowBuilder.setAdOrderNum(report.getAdOrderNum().setScale(0, RoundingMode.HALF_UP).toPlainString());
                }
                if (Objects.nonNull(report.getAdOtherOrderNum())) {
                    rowBuilder.setAdOtherOrderNum(report.getAdOtherOrderNum().setScale(0, RoundingMode.HALF_UP).toPlainString());
                }

                rowBuilder.setSaleNumBounce(percentage(MathUtil.divideByZero(report.getAdOtherSaleNum(), report.getSaleNum(), 4)));
                rowBuilder.setOrderNumBounce(percentage(MathUtil.divideByZero(report.getAdOtherOrderNum(), report.getOrderNum(), 4)));

                BigDecimal clickNum = Objects.nonNull(clicks) ? new BigDecimal(clicks) : null;
                BigDecimal impressNum = Objects.nonNull(impressions) ? new BigDecimal(impressions) : null;
                rowBuilder.setCtr(percentage(MathUtil.divideByZero(clickNum, impressNum, 4)));
                rowBuilder.setCpc(MathUtil.divideByZero(report.getCost(), clickNum, 4).setScale(2, RoundingMode.HALF_UP).toPlainString());
                rowBuilder.setCvr(percentage(MathUtil.divideByZero(report.getSaleNum(), clickNum, 4)));
                rowBuilder.setAcos(percentage(MathUtil.divideByZero(report.getCost(), report.getTotalSales(), 4)));
                rowBuilder.setRoas(MathUtil.divideByZero(report.getTotalSales(), report.getCost(), 4).setScale(2, RoundingMode.HALF_UP).toPlainString());
            }
            return rowBuilder.build();
        }).collect(Collectors.toList());
    }

    private String joinMapWithLengthLimit(Map<String, String> map, int maxLength) {
        if (MapUtils.isEmpty(map)) {
            return "";
        }
        StringBuilder sb = new StringBuilder();
        for (Map.Entry<String, String> entry : map.entrySet()) {
            String keyValuePair = entry.getKey() + "：" + entry.getValue() + "\n";
            // 检查拼接当前键值对后是否会超过长度限制
            if (sb.length() + keyValuePair.length() > maxLength) {
                break;
            }
            sb.append(keyValuePair);
        }
        return sb.toString();
    }

}
