package com.meiyunji.sponsored.service.multiPlatform.walmart.service.impl;

import com.meiyunji.sponsored.common.exception.ServiceException;
import com.meiyunji.sponsored.service.multiPlatform.walmart.service.IWalmartAdvertisingKeywordClientService;
import com.walmart.oms.advertiser.WalmartAdvertiserClient;
import com.walmart.oms.advertiser.base.dto.AddKeywordsToExistingKeywordBiddedCampaignDTO;
import com.walmart.oms.advertiser.model.AddKeywordsToExistingKeywordBiddedCampaignResponse;
import com.walmart.oms.advertiser.model.ListAllTheKeywordsCampaignResponse;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @author: ys
 * @date: 2025/3/25 11:28
 * @describe:
 */
@Service
public class WalmartAdvertisingKeywordClientServiceImpl implements IWalmartAdvertisingKeywordClientService {
    @Override
    public AddKeywordsToExistingKeywordBiddedCampaignResponse addKeywordsToExistingKeywordBiddedCampaign(List<AddKeywordsToExistingKeywordBiddedCampaignDTO> dtos)  throws ServiceException {
        WalmartAdvertiserClient advertiserClient = new WalmartAdvertiserClient();
        AddKeywordsToExistingKeywordBiddedCampaignResponse response = advertiserClient.addKeywordsToExistingKeywordBiddedCampaign(dtos);
        if (response == null) {
            throw new ServiceException("关键字添加到广告组失败！平台响应response is null");
        }
        if (StringUtils.isNotBlank(response.getError())) {
            throw new ServiceException("关键字添加到广告组失败！平台响应:" + response.getError());
        }
        if (response.getDetailsObj() != null && StringUtils.isNotBlank(response.getDetailsObj().getDescription())) {
            throw new ServiceException("关键字添加到广告组失败！平台响应:" + response.getDetailsObj().getDescription());
        }
        if (org.apache.commons.collections.CollectionUtils.isEmpty(response.getData())) {
            throw new ServiceException("关键字添加到广告组失败！平台响应response Data is null");
        }
        return response;
    }

    @Override
    public ListAllTheKeywordsCampaignResponse getListAllTheKeywordsCampaign(String campaignId)  throws ServiceException{
        WalmartAdvertiserClient advertiserClient = new WalmartAdvertiserClient();
        ListAllTheKeywordsCampaignResponse listAllResponse = advertiserClient.getListAllTheKeywordsCampaign(campaignId);
        if (listAllResponse == null) {
            throw new ServiceException("同步关键词失败！平台响应response is null");
        }
        if (StringUtils.isNotBlank(listAllResponse.getError())) {
            throw new ServiceException("同步关键词失败！平台响应：" + listAllResponse.getError());
        }
        if (listAllResponse.getDetailsObj() != null && StringUtils.isNotBlank(listAllResponse.getDetailsObj().getDescription())) {
            throw new ServiceException("同步关键词失败！平台响应:" + listAllResponse.getDetailsObj().getDescription());
        }
        return listAllResponse;
    }
}
