package com.meiyunji.sponsored.service.cpc.service2.sb.impl;

import com.alibaba.fastjson.JSONObject;
import com.amazon.advertising.assets.entity.assetsGet.AssetVersionList;
import com.amazon.advertising.assets.entity.assetsGet.GetAssetResult;
import com.amazon.advertising.assets.entity.assetsRegister.RegisterAssetResult;
import com.amazon.advertising.assets.entity.assetsUpload.UploadAssetResult;
import com.amazon.advertising.assets.entity.searchAssets.SearchAssetsResult;
import com.amazon.advertising.assets.enums.*;
import com.amazon.advertising.sb.entity.brands.BrandResult;
import com.amazon.advertising.sb.entity.media.MediaCompleteResult;
import com.amazon.advertising.sb.entity.media.MediaResult;
import com.amazon.advertising.sb.entity.media.MediaUploadResult;
import com.amazon.advertising.sb.entity.storeInfo.StoresResult;
import com.amazon.advertising.sb.entity.stores.StoreAssetResult;
import com.google.common.collect.Maps;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.util.ResultUtil;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdCampaignAllDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdProfileDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonSbAdGroupDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonSbAdKeywordDao;
import com.meiyunji.sponsored.service.cpc.dto.SbGroupInfoDto;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdCampaignAll;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdProfile;
import com.meiyunji.sponsored.service.cpc.po.AmazonSbAdGroup;
import com.meiyunji.sponsored.service.cpc.po.AmazonSbAdKeyword;
import com.meiyunji.sponsored.service.cpc.service2.assets.CpcAssetsApiService;
import com.meiyunji.sponsored.service.cpc.service2.sb.ICpcSbStoreService;
import com.meiyunji.sponsored.service.cpc.vo.SbStoreInfoVo;
import com.meiyunji.sponsored.service.cpc.vo.ThemesVo;
import com.meiyunji.sponsored.service.cpc.vo.assets.ImageAssetVo;
import com.meiyunji.sponsored.service.enums.CampaignTypeEnum;
import com.meiyunji.sponsored.service.enums.SBAdsLandingPageTypeEnum;
import com.meiyunji.sponsored.service.enums.SBVideoCheckApprovedProgramsEnum;
import com.meiyunji.sponsored.service.log.enums.MatchTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by lm on 2021/7/31.
 */
@Service
@Slf4j
public class CpcSbStoreServiceImpl implements ICpcSbStoreService {

    @Autowired
    private IScVcShopAuthDao shopAuthDao;
    @Autowired
    private IAmazonAdProfileDao amazonAdProfileDao;
    @Autowired
    private IAmazonAdCampaignAllDao amazonAdCampaignAllDao;
    @Autowired
    private IAmazonSbAdGroupDao sbAdGroupDao;
    @Autowired
    private IAmazonSbAdKeywordDao keywordDao;
    @Autowired
    private CpcSbBrandApiService cpcSbBrandApiService;
    @Autowired
    private CpcSbStoreApiService cpcSbStoreApiService;
    @Autowired
    private CpcSbStoreInfoApiService cpcSbStoreInfoApiService;
    @Autowired
    private CpcSbMediaUploadApiService cpcSbMediaUploadApiService;
    @Autowired
    private CpcAssetsApiService cpcAssetsApiService;

    @Override
    public Result<SbStoreInfoVo> getStoreInfo(Integer puid, Integer shopId) {

        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(shopId, puid);
        if (shop == null) {
            return ResultUtil.returnErr("没有CPC授权");
        }

        AmazonAdProfile profile = amazonAdProfileDao.getProfile(puid, shopId);
        if (profile == null) {
            return ResultUtil.returnErr("没有站点对应的配置信息");
        }
        StopWatch sw = new StopWatch();
        sw.start("get store brand info");
        Result<List<BrandResult>> result = cpcSbBrandApiService.getBrand(shop, profile);
        sw.stop();
        SbStoreInfoVo info = new SbStoreInfoVo();
        if (result.success()) {
            info.setBrandList(result.getData());
            sw.start("get store page info");
            Result<List<StoresResult>> storeInfoResult = cpcSbStoreInfoApiService.getStoreInfo(shop, profile);
            log.info("store page info:{}", JSONObject.toJSONString(storeInfoResult.getData()));
            sw.stop();
            if (storeInfoResult.success()) {
                List<StoresResult> data = storeInfoResult.getData();
                if (CollectionUtils.isNotEmpty(data)) {
                    Map<String, StoresResult> storeMap = Maps.newLinkedHashMap();
                    for (StoresResult storesResult : data) {
                        storeMap.put(storesResult.getBrandEntityId(), storesResult);
                    }
                    if (storeMap.size() > 0) {
                        info.setStoresInfoMap(storeMap);
                    }
                }
            }
        }
        log.info(sw.prettyPrint());
        return ResultUtil.returnSucc(info);
    }

    @Override
    public Result<List<BrandResult>> getBrand(Integer puid, Integer shopId) {
        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(shopId, puid);
        if (shop == null) {
            return ResultUtil.returnErr("没有CPC授权");
        }

        AmazonAdProfile profile = amazonAdProfileDao.getProfile(puid, shopId);
        if (profile == null) {
            return ResultUtil.returnErr("没有站点对应的配置信息");
        }
        Result<List<BrandResult>> result = cpcSbBrandApiService.getBrand(shop, profile);
        List<BrandResult> brandList = new ArrayList<>();
        if (result.success()) {
            brandList = result.getData();
        }
        return ResultUtil.returnSucc(brandList);
    }

    @Override
    public Result<List<ImageAssetVo>> getStoreImage(Integer puid, Integer shopId,
                                                    String brandEntityId, String filterType) {
        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(shopId, puid);
        if (shop == null) {
            return ResultUtil.returnErr("没有CPC授权");
        }

        AmazonAdProfile profile = amazonAdProfileDao.getProfile(puid, shopId);
        if (profile == null) {
            return ResultUtil.returnErr("没有站点对应的配置信息");
        }

        Map<String, List<String>> valueFilters = new HashMap<>();
        valueFilters.put(FilterFieldEnum.ASSET_TYPE.name(), Collections.singletonList(AssetTypeEnum.IMAGE.name()));
        valueFilters.put(FilterFieldEnum.TAG.name(), Collections.singletonList(CampaignTypeEnum.sb.getCampaignType()));
        Map<String, List<Map<String, String>>> rangeFilters = new HashMap<>();
        Result<SearchAssetsResult> searchResult = cpcAssetsApiService.searchAssets(shop, profile, null, valueFilters, rangeFilters,
                SortFieldEnum.CREATED_TIME.name(), SortOrderEnum.DESC.name());
        SearchAssetsResult data = searchResult.getData();
        List<ImageAssetVo> results = new ArrayList<>();
        if (data != null && CollectionUtils.isNotEmpty(data.getAssetList())) {
            List<com.amazon.advertising.assets.entity.searchAssets.Asset> assetList = data.getAssetList();
            results = assetList.stream()
                    .filter(asset -> asset.getAssociatedAccountIds().stream().anyMatch(s->s.contains(brandEntityId)))
                    .map(asset -> {
                        ImageAssetVo result = new ImageAssetVo();
                        result.setAssetId(asset.getAssetId());
                        result.setVersionId(asset.getVersion());
                        result.setName(asset.getName());
                        result.setUrl(asset.getStorageLocationUrls().getDefaultUrl());
                        result.setMediaType(asset.getFileMetadata().getContentType());
                        result.setHeight(asset.getFileMetadata().getHeight());
                        result.setWidth(asset.getFileMetadata().getWidth());
                        return result;
                    }).collect(Collectors.toList());
        }

        // 兼容历史数据
        Result<List<StoreAssetResult>> storesAssetsResult = cpcSbStoreApiService.getStoresAssets(shop, profile, brandEntityId, "brandLogo");
        if (storesAssetsResult.success() && CollectionUtils.isNotEmpty(storesAssetsResult.getData())) {
            results.addAll(storesAssetsResult.getData().stream()
                    .map(asset -> {
                        ImageAssetVo result = new ImageAssetVo();
                        result.setAssetId(asset.getAssetId());
                        result.setVersionId("");
                        result.setName(asset.getName());
                        result.setUrl(asset.getUrl());
                        result.setMediaType(asset.getMediaType());
                        result.setHeight(asset.getHeight());
                        result.setWidth(asset.getWidth());
                        return result;
                    }).collect(Collectors.toList()));
        }
        if (CollectionUtils.isNotEmpty(results) && StringUtils.isNotEmpty(filterType) && filterType.split(",").length != 0) {
            results = results.stream().filter(r -> {
                if (StringUtils.isEmpty(r.getName()) || r.getName().trim().split("\\.").length != 2) {
                    return false;
                }
                return Arrays.asList(filterType.split(",")).parallelStream().anyMatch(f -> r.getName().trim().split("\\.")[1].equalsIgnoreCase(f));
            }).collect(Collectors.toList());

        }
        return ResultUtil.success(results);
    }

    @Override
    public Result<List<ImageAssetVo>> getStoreImages(ShopAuth shop, AmazonAdProfile profile, String campaignId) {

        //1，调用资产接口查询店铺图片资产
        Map<String, List<String>> valueFilters = new HashMap<>();
        valueFilters.put(FilterFieldEnum.ASSET_TYPE.name(), Collections.singletonList(AssetTypeEnum.IMAGE.name()));
        valueFilters.put(FilterFieldEnum.TAG.name(), Collections.singletonList(CampaignTypeEnum.sb.getCampaignType()));
        Map<String, List<Map<String, String>>> rangeFilters = new HashMap<>();
        Result<SearchAssetsResult> searchResult = cpcAssetsApiService.searchAssets(shop, profile, null, valueFilters, rangeFilters, SortFieldEnum.CREATED_TIME.name(), SortOrderEnum.DESC.name());
        if (searchResult.error()) {
            return ResultUtil.error(searchResult.getMsg());
        }

        //通过广告活动id查询brandEntityId
        String brandEntityId;
        if (StringUtils.isNotBlank(campaignId)) {
            AmazonAdCampaignAll amazonAdCampaign = amazonAdCampaignAllDao.getByCampaignId(shop.getPuid(), shop.getId(), campaignId);
            if (Objects.nonNull(amazonAdCampaign)) {
                brandEntityId = amazonAdCampaign.getBrandEntityId();
            } else {brandEntityId = null;}
        } else {brandEntityId = null;}

        //2，数据组装
        SearchAssetsResult data = searchResult.getData();
        List<ImageAssetVo> results = new ArrayList<>();
        if (data != null && CollectionUtils.isNotEmpty(data.getAssetList())) {
            List<com.amazon.advertising.assets.entity.searchAssets.Asset> assetList = data.getAssetList();
            //通过brandEntityId进行过滤
            if (StringUtils.isNotBlank(brandEntityId)) {
                assetList = assetList.stream().filter(asset -> asset.getAssociatedAccountIds().stream().anyMatch(s -> s.contains(brandEntityId))).collect(Collectors.toList());
            }
            results = assetList.stream()
                .map(asset -> {
                    ImageAssetVo result = new ImageAssetVo();
                    result.setAssetId(asset.getAssetId());
                    result.setVersionId(asset.getVersion());
                    result.setName(asset.getName());
                    result.setUrl(asset.getStorageLocationUrls().getDefaultUrl());
                    result.setMediaType(asset.getFileMetadata().getContentType());
                    result.setHeight(asset.getFileMetadata().getHeight());
                    result.setWidth(asset.getFileMetadata().getWidth());
                    return result;
                }).collect(Collectors.toList());
        }

        // 3，兼容历史数据
        if (StringUtils.isNotBlank(brandEntityId)) {
            Result<List<StoreAssetResult>> storesAssetsResult = cpcSbStoreApiService.getStoresAssets(shop, profile, brandEntityId, "brandLogo");
            if (storesAssetsResult.success() && CollectionUtils.isNotEmpty(storesAssetsResult.getData())) {
                results.addAll(storesAssetsResult.getData().stream()
                    .map(asset -> {
                        ImageAssetVo result = new ImageAssetVo();
                        result.setAssetId(asset.getAssetId());
                        result.setVersionId("");
                        result.setName(asset.getName());
                        result.setUrl(asset.getUrl());
                        result.setMediaType(asset.getMediaType());
                        result.setHeight(asset.getHeight());
                        result.setWidth(asset.getWidth());
                        return result;
                    }).collect(Collectors.toList()));
            }
        }

        return ResultUtil.success(results);
    }

    @Override
    public Result<SbGroupInfoDto> getSbGroupInfo(ShopAuth shop, AmazonAdProfile profile, String campaignId, String groupId) {
        SbGroupInfoDto sbGroupInfoDto = new SbGroupInfoDto();
        AmazonSbAdGroup sbAdGroup = sbAdGroupDao.getByCampaignIdAndGroupId(shop.getPuid(), shop.getId(), campaignId, groupId);
        if (Objects.nonNull(sbAdGroup)) {
            sbGroupInfoDto.setAdGroupName(sbAdGroup.getName());
            sbGroupInfoDto.setCampaignId(sbAdGroup.getCampaignId());
            sbGroupInfoDto.setAdFormat(sbAdGroup.getAdFormat());
            sbGroupInfoDto.setSbAdGroupType(sbAdGroup.getAdGroupType());
            if (sbAdGroup.getLandingPage() != null) {
                String upperCase = sbAdGroup.getLandingPage().toUpperCase();
                if(upperCase.contains(SBAdsLandingPageTypeEnum.DETAIL_PAGE.getType())) {
                    sbGroupInfoDto.setSbvLandingPageType(SBAdsLandingPageTypeEnum.DETAIL_PAGE.getType());
                }
                if(upperCase.contains(SBAdsLandingPageTypeEnum.STORE.getType())) {
                    sbGroupInfoDto.setSbvLandingPageType(SBAdsLandingPageTypeEnum.STORE.getType());
                }
            }
            //查询活动每日预算
            List<AmazonAdCampaignAll> campaignList = amazonAdCampaignAllDao.listByCampaignIds(shop.getPuid(), shop.getId(), Collections.singletonList(sbAdGroup.getCampaignId()));
            if (CollectionUtils.isNotEmpty(campaignList)) {
                Optional.ofNullable(campaignList.get(0).getBudget()).map(BigDecimal::doubleValue).ifPresent(sbGroupInfoDto::setCampaignDailyBudget);
            }

        }
        List<AmazonSbAdKeyword> list = keywordDao.listByMatchType(shop.getPuid(), shop.getId(), groupId, MatchTypeEnum.theme.getMatchType());
        List<ThemesVo> themesVos = new ArrayList<>(2);
        for (AmazonSbAdKeyword theme: list) {
            ThemesVo themesVo = new ThemesVo();
            themesVo.setThemeType(theme.getKeywordText());
            themesVo.setBid(theme.getBid().doubleValue());
            themesVos.add(themesVo);
        }
        sbGroupInfoDto.setThemesVo(themesVos);
        return ResultUtil.success(sbGroupInfoDto);
    }

    @Override
    public Result<ImageAssetVo> uploadImage(Integer puid, Integer shopId, String fileName, String imageType, byte[] bytes, String brandEntityId) {
        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(shopId, puid);
        if (shop == null) {
            return ResultUtil.returnErr("没有CPC授权");
        }

        AmazonAdProfile profile = amazonAdProfileDao.getProfile(puid, shopId);
        if (profile == null) {
            return ResultUtil.returnErr("没有站点对应的配置信息");
        }
        Result<UploadAssetResult> urlResult = cpcAssetsApiService.getAssetUploadUrl(shop, profile, fileName);
        if (urlResult.error()) return ResultUtil.returnErr(urlResult.getMsg());
        String uploadUrl = urlResult.getData().getUrl();
        Result<?> uploadResult = cpcAssetsApiService.uploadAsset(shop, uploadUrl, bytes, imageType);
        if (uploadResult.error()) return ResultUtil.returnErr(urlResult.getMsg());
        Result<RegisterAssetResult> registerResult = cpcAssetsApiService.registerAsset(shop, profile, fileName, uploadUrl, AssetTypeEnum.IMAGE.name(),
                Collections.singletonList(AssetSubTypeEnum.LOGO.name()), Collections.singletonList(CampaignTypeEnum.sb.getCampaignType()), Collections.singletonList(brandEntityId), null);
        if (registerResult.error()) return ResultUtil.returnErr(registerResult.getMsg());
        RegisterAssetResult data = registerResult.getData();
        ImageAssetVo assetVo = new ImageAssetVo();
        assetVo.setAssetId(data.getAssetId());
        assetVo.setVersionId(data.getVersionId());
        Result<GetAssetResult> assetResult = cpcAssetsApiService.getAsset(shop, profile, assetVo.getAssetId(), assetVo.getVersionId());
        if (assetResult.error()) return ResultUtil.returnSucc(assetVo);

        GetAssetResult asset;
        List<AssetVersionList> versions;
        if ((asset = assetResult.getData()) != null && CollectionUtils.isNotEmpty(versions = asset.getAssetVersionList())) {
            assetVo.setAssetId(asset.getAssetGlobal().getAssetId());
            AssetVersionList version = versions.get(0);
            assetVo.setName(version.getName());
            assetVo.setUrl(version.getUrl());
            assetVo.setMediaType(version.getFileMetadata().getContentType());
            assetVo.setHeight(version.getFileMetadata().getHeight());
            assetVo.setWidth(version.getFileMetadata().getWidth());
        }
        return ResultUtil.returnSucc(assetVo);
    }

    @Override
    public Result<ImageAssetVo> uploadVideo(Integer puid, Integer shopId, String fileName, String videoType, byte[] bytes, String brandEntityId) {
        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(shopId, puid);
        if (shop == null) {
            return ResultUtil.returnErr("没有CPC授权");
        }

        AmazonAdProfile profile = amazonAdProfileDao.getProfile(puid, shopId);
        if (profile == null) {
            return ResultUtil.returnErr("没有站点对应的配置信息");
        }
        Result<UploadAssetResult> urlResult = cpcAssetsApiService.getAssetUploadUrl(shop, profile, fileName);
        if (urlResult.error()) return ResultUtil.returnErr("网络超时,请稍后重试."+urlResult.getMsg());
        String uploadUrl = urlResult.getData().getUrl();
        Result<?> uploadResult = cpcAssetsApiService.uploadAsset(shop, uploadUrl, bytes, videoType);
        if (uploadResult.error()) return ResultUtil.returnErr("网络超时,请稍后重试."+urlResult.getMsg());
        Result<RegisterAssetResult> registerResult = cpcAssetsApiService.registerAsset(shop, profile, fileName, uploadUrl, AssetTypeEnum.VIDEO.name(),
                Collections.singletonList(AssetSubTypeEnum.BACKGROUND_VIDEO.name()), Collections.singletonList(CampaignTypeEnum.sb.getCampaignType()), Collections.singletonList(brandEntityId), null);
        if (registerResult.error()) return ResultUtil.returnErr("网络超时,请稍后重试."+registerResult.getMsg());
        RegisterAssetResult data = registerResult.getData();
        ImageAssetVo assetVo = new ImageAssetVo();
        assetVo.setAssetId(data.getAssetId());
        assetVo.setVersionId(data.getVersionId());
        Result<GetAssetResult> assetResult = cpcAssetsApiService.getAsset(shop, profile, assetVo.getAssetId(), assetVo.getVersionId());
        if (assetResult.error()) return ResultUtil.returnSucc(assetVo);

        GetAssetResult asset;
        List<AssetVersionList> versions;
        if ((asset = assetResult.getData()) != null && CollectionUtils.isNotEmpty(versions = asset.getAssetVersionList())) {
            assetVo.setAssetId(asset.getAssetGlobal().getAssetId());
            AssetVersionList version = versions.get(0);

            //校验视频上传后的标签，检查是否符合创建SBV的要求
            List<String> checkApprovedList = version.getSpecCheckApprovedPrograms();
            if (CollectionUtils.isEmpty(checkApprovedList)) {
                return ResultUtil.returnErr("亚马逊上传请求超时，请稍后重试");
            }
            if (checkApprovedList.stream().noneMatch(approved ->
                    SBVideoCheckApprovedProgramsEnum.SPONSORED_BRANDS_VIDEO.getApproved().equals(approved) ||
                            SBVideoCheckApprovedProgramsEnum.SPONSORED_DISPLAY_VIDEO.getApproved().equals(approved) )) {
                return ResultUtil.returnErr("视频格式不符合要求，请检查后重新上传");
            }
            assetVo.setName(version.getName());
            assetVo.setUrl(version.getUrl());
            assetVo.setMediaType(version.getFileMetadata().getContentType());
            assetVo.setHeight(version.getFileMetadata().getHeight());
            assetVo.setWidth(version.getFileMetadata().getWidth());
        }
        return ResultUtil.returnSucc(assetVo);
    }

    @Override
    public Result<MediaUploadResult> mediaCreate(Integer puid, Integer shopId) {

        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(shopId, puid);
        if (shop == null) {
            return ResultUtil.returnErr("没有CPC授权");
        }

        AmazonAdProfile profile = amazonAdProfileDao.getProfile(puid, shopId);
        if (profile == null) {
            return ResultUtil.returnErr("没有站点对应的配置信息");
        }

        Result<MediaUploadResult> resultResult = cpcSbMediaUploadApiService.mediaCreate(shop, profile);
        if (resultResult.success()) {
            return ResultUtil.returnSucc(resultResult.getData());
        }
        return ResultUtil.returnErr(resultResult.getMsg());
    }

    @Override
    public Result<MediaCompleteResult> mediaComplete(Integer puid, Integer shopId, String uploadLocation) {

        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(shopId, puid);
        if (shop == null) {
            return ResultUtil.returnErr("没有CPC授权");
        }

        AmazonAdProfile profile = amazonAdProfileDao.getProfile(puid, shopId);
        if (profile == null) {
            return ResultUtil.returnErr("没有站点对应的配置信息");
        }

        Result<MediaCompleteResult> resultResult = cpcSbMediaUploadApiService.mediaComplete(shop, profile, uploadLocation);
        if (resultResult.success()) {
            return ResultUtil.returnSucc(resultResult.getData());
        }
        return ResultUtil.returnErr(resultResult.getMsg());
    }

    @Override
    public Result<MediaResult> mediaDescribe(Integer puid, Integer shopId, String mediaId) {

        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(shopId, puid);
        if (shop == null) {
            return ResultUtil.returnErr("没有CPC授权");
        }

        AmazonAdProfile profile = amazonAdProfileDao.getProfile(puid, shopId);
        if (profile == null) {
            return ResultUtil.returnErr("没有站点对应的配置信息");
        }

        Result<MediaResult> resultResult = cpcSbMediaUploadApiService.mediaDescribe(shop, profile, mediaId);
        if (resultResult.success()) {
            return ResultUtil.returnSucc(resultResult.getData());
        }

        return ResultUtil.returnErr(resultResult.getMsg());
    }
}
