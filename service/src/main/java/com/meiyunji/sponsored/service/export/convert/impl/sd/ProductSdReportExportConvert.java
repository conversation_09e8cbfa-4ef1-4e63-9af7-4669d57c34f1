package com.meiyunji.sponsored.service.export.convert.impl.sd;

import com.amazon.advertising.mode.MarketTimezoneAndCurrencyEnum;
import com.meiyunji.sponsored.service.export.constants.AdReportExportTypeEnum;
import com.meiyunji.sponsored.service.export.constants.ConvertBeanIdConstant;
import com.meiyunji.sponsored.service.export.convert.ReportDataVoExportConvert;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.cpc.vo.ReportDataVo;
import com.meiyunji.sponsored.service.cpc.vo.SearchVo;
import com.meiyunji.sponsored.service.vo.AdSdProductExportVo;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @author: sun<PERSON><PERSON>
 * @email: <EMAIL>
 * @date: 2023-09-08  14:23
 */

@Component(ConvertBeanIdConstant.SD_PRODUCT)
public class ProductSdReportExportConvert implements ReportDataVoExportConvert {

    @Override
    public AdReportExportTypeEnum getReportExportType() {
        return AdReportExportTypeEnum.SD_PRODUCT;
    }

    @Override
    public void processExcelDataList(ReportDataVo reportVo, List list, Map<Integer, ShopAuth> shopAuthMap, SearchVo searchVo) {
        AdSdProductExportVo vo = new AdSdProductExportVo();
        vo.setStartDate(searchVo.getStartDate());
        vo.setEndDate(searchVo.getEndDate());
        //每日日期格式转换
        if ("daily".equals(searchVo.getTabType()) && StringUtils.isNotBlank(reportVo.getCountDate())) {
            Date dailyDate = DateUtil.strToDate(reportVo.getCountDate(), "yyyyMMdd");
            vo.setDailyDate(DateUtil.dateToStrWithFormat(dailyDate, "yyyy-MM-dd"));
        }

        vo.setCampaignStartDate(reportVo.getCampaignStartDate());
        vo.setCampaignEndDate(reportVo.getCampaignEndDate());
        vo.setProductState(reportVo.getState());

        vo.setAsin(reportVo.getAsin());
        vo.setSku(reportVo.getSku());
        vo.setCampaignName(reportVo.getCampaignName());
        vo.setAdGroupName(reportVo.getAdGroupName());
        vo.setAttributedUnitsOrdered14d(reportVo.getAttributedUnitsOrdered14d());
        vo.setCost(reportVo.getCost());
        vo.setCostType(reportVo.getCostType());
        vo.setBidOptimization(reportVo.getBidOptimization());
        vo.setImpressions(reportVo.getImpressions());
        vo.setClicks(reportVo.getClicks());
        if ("cpc".equals(reportVo.getCostType())) {
            vo.setCpc(reportVo.getCpc());
        }
        if ("vcpm".equals(reportVo.getCostType())) {
            vo.setVcpm(reportVo.getVcpm());
        }
        vo.setViewImpressions(reportVo.getViewImpressions());
        vo.setClickRate(reportVo.getClickRate());
        vo.setSalesConversionRate(reportVo.getSalesConversionRate());
        vo.setAttributedDetailPageView14d(reportVo.getAttributedDetailPageView14d());
        vo.setAcos(reportVo.getAcos());
        vo.setRoas(reportVo.getRoas());
        vo.setAttributedConversions14d(reportVo.getAttributedConversions14d());
        vo.setAttributedConversions14dSameSKU(reportVo.getAttributedConversions14dSameSKU());
        vo.setAttributedConversions14dOtherSameSKU(reportVo.getAttributedConversions14dOtherSameSKU());
        vo.setAttributedSales14d(reportVo.getAttributedSales14d());
        vo.setAttributedSales14dSameSKU(reportVo.getAttributedSales14dSameSKU());
        vo.setAttributedSales14dOtherSameSKU(reportVo.getAttributedSales14dOtherSameSKU());
        vo.setAttributedOrdersNewToBrand14d(reportVo.getAttributedOrdersNewToBrand14d());
        vo.setAttributedOrderRateNewToBrand14d(reportVo.getAttributedOrderRateNewToBrand14d());
        vo.setAttributedSalesNewToBrand14d(reportVo.getAttributedSalesNewToBrand14d());
        vo.setAttributedSalesNewToBrandPercentage14d(reportVo.getAttributedSalesNewToBrandPercentage14d());
        vo.setAttributedUnitsOrderedNewToBrand14d(reportVo.getAttributedUnitsOrderedNewToBrand14d());
        vo.setAttributedUnitsOrderedNewToBrandPercentage14d(reportVo.getAttributedUnitsOrderedNewToBrandPercentage14d());

        if (MapUtils.isNotEmpty(shopAuthMap) && shopAuthMap.containsKey(reportVo.getShopId())) {
            ShopAuth shop = shopAuthMap.get(reportVo.getShopId());
            vo.setShopName(shop.getName());
            MarketTimezoneAndCurrencyEnum m = MarketTimezoneAndCurrencyEnum.getByMarketplaceId(shop.getMarketplaceId());
            if (null != m) {
                vo.setCurrency(m.getCurrencyCode());
            }
        }
        list.add(vo);
    }
}
