package com.meiyunji.sponsored.service.autoRuleTask.service;

import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.service.autoRuleTask.bo.TemplateTaskHourglassBo;
import com.meiyunji.sponsored.service.autoRuleTask.po.AutoRuleTask;
import com.meiyunji.sponsored.service.autoRuleTask.po.AutoRuleTaskRecord;
import com.meiyunji.sponsored.service.autoRuleTask.vo.ProcessTaskParam;
import com.meiyunji.sponsored.service.autoRuleTask.vo.QueryTaskHourglassParam;

import java.util.List;

/**
 * @author: chenzimeng
 * @email: <EMAIL>
 * @date: 2024-01-08  09:24
 */
public interface AutoRuleTaskService {

    Result<List<AutoRuleTask>> queryTaskHourglass(QueryTaskHourglassParam param);

    Result<Page<AutoRuleTaskRecord>> pageListByTaskId(Integer puid, Long id, Integer pageNo, Integer pageSize);

    Result<String> processTask(ProcessTaskParam param);

    Result<List<TemplateTaskHourglassBo>> queryTemplateTaskHourglass(Integer puid, Integer shopId, List<Long> templateIdList);
}
