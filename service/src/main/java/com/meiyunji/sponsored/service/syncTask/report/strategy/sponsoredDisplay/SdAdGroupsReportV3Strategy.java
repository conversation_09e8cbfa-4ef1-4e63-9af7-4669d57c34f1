package com.meiyunji.sponsored.service.syncTask.report.strategy.sponsoredDisplay;

import com.alibaba.fastjson.JSONReader;
import com.google.common.collect.Lists;
import com.meiyunji.sellfox.aadas.types.enumeration.AmazonReportV3Type;
import com.meiyunji.sellfox.aadas.types.message.notification.ReportReadyNotification;
import com.meiyunji.sponsored.common.springjdbc.PartitionSqlUtil;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.service.config.CosBucketClient;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdCampaignAllDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdSdGroupReportDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdCampaignAll;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdSdGroupReport;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.syncTask.entity.sd.SponsoredDisplayAdGroup;
import com.meiyunji.sponsored.service.syncTask.report.strategy.AbstractReportProcessStrategy;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.io.ByteArrayInputStream;
import java.io.InputStreamReader;
import java.math.BigDecimal;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import java.util.zip.GZIPInputStream;

/**
 * @author: wade
 * @date: 2021/12/28 16:32
 * @describe: sd 广告组T00030报告处理类
 */
@Component
@Slf4j
@ConditionalOnProperty(name = "aadas.scheduler.executors.reports-consumer.enabled", havingValue = "true")
public class SdAdGroupsReportV3Strategy extends AbstractReportProcessStrategy {
    private final IAmazonAdSdGroupReportDao amazonAdSdGroupReportDao;
    private final IAmazonAdCampaignAllDao amazonAdCampaignAllDao;
    private final PartitionSqlUtil partitionSqlUtil;

    public SdAdGroupsReportV3Strategy(CosBucketClient dataBucketClient,
                                      IAmazonAdSdGroupReportDao amazonAdSdGroupReportDao,
                                      IAmazonAdCampaignAllDao amazonAdCampaignAllDao,
                                      PartitionSqlUtil partitionSqlUtil) {
        super(dataBucketClient);
        this.amazonAdSdGroupReportDao = amazonAdSdGroupReportDao;
        this.amazonAdCampaignAllDao = amazonAdCampaignAllDao;
        this.partitionSqlUtil = partitionSqlUtil;
    }

    @Override
    public Boolean checkValid(ReportReadyNotification notification) {
        return notification.getVersion() == 3 && AmazonReportV3Type.sd_groups == notification.getV3Type();
    }

    @Override
    public void processReport(ReportReadyNotification notification) {
        try (InputStreamReader inputStreamReader = new InputStreamReader(new GZIPInputStream(new ByteArrayInputStream(dataBucketClient.getObjectToBytes(notification.getPath())))); JSONReader jsonReader = new JSONReader(inputStreamReader)) {
            jsonReader.startArray();
            List<SponsoredDisplayAdGroup> reports = Lists.newArrayListWithExpectedSize(500);
            while (jsonReader.hasNext()) {
                jsonReader.startObject();
                SponsoredDisplayAdGroup report = new SponsoredDisplayAdGroup();
                report.readFromJsonReader(jsonReader);
                jsonReader.endObject();
                if (report.getImpressions() != 0) {
                    reports.add(report);
                }
                if (reports.size() >= 500) {
                    dealReport(notification, reports);
                    reports = Lists.newArrayListWithExpectedSize(500);
                }
            }
            jsonReader.endArray();
            if (CollectionUtils.isNotEmpty(reports)) {
                dealReport(notification, reports);
            }
        } catch (Exception e) {
            log.info("报告处理发生错误{}@{} reportType={} countDate={}", notification.getSellerIdentifier(), notification.getMarketplaceIdentifier(), notification.getV3Type(), notification.getV3StartDate(), e);
        }
    }


    private void dealReport(ReportReadyNotification notification, List<SponsoredDisplayAdGroup> reports) {
        Set<String> campaignIds = new HashSet<>();
        List<SponsoredDisplayAdGroup> validReports = reports.stream().filter(item -> BigDecimal.valueOf(item.getImpressions()).compareTo(BigDecimal.ZERO) != 0).peek(e -> {
            campaignIds.add(String.valueOf(e.getCampaignId()));
        }).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(validReports)) {
            return;
        }

        Map<String, AmazonAdCampaignAll> sdCampaignTacticByCampaignIds = amazonAdCampaignAllDao.getSdCampaignByCampaignIds(notification.getSellerIdentifier(), notification.getMarketplaceIdentifier(), new ArrayList<>(campaignIds));
        List<AmazonAdSdGroupReport> poList = getPoBySdReportGroup(notification, validReports, sdCampaignTacticByCampaignIds);
        List<List<AmazonAdSdGroupReport>> partition = Lists.partition(poList, 200);
        for (List<AmazonAdSdGroupReport> amazonAdSdGroupReports : partition) {
            partitionSqlUtil.save(notification.getSellerIdentifier(), amazonAdSdGroupReports, 0, amazonAdSdGroupReportDao::insertOrUpdateList);
            if (DateUtil.checkDateRange(notification.getV3EndDate(), 2L)) {
                amazonAdSdGroupReportDao.insertDorisList(notification.getSellerIdentifier(), amazonAdSdGroupReports);
            }
        }
    }


    private List<AmazonAdSdGroupReport> getPoBySdReportGroup(ReportReadyNotification notification, List<SponsoredDisplayAdGroup> reports, Map<String, AmazonAdCampaignAll> sbCampaigns) {
        List<AmazonAdSdGroupReport> list = Lists.newArrayListWithExpectedSize(reports.size());
        AmazonAdSdGroupReport sdGroupReport;
        for (SponsoredDisplayAdGroup group : reports) {
            if (sbCampaigns == null || sbCampaigns.get(String.valueOf(group.getCampaignId())) == null) {
                log.info("sd campaign id :{},not fund, puid:{}, shopId:{}", group.getCampaignId(), notification.getSellerIdentifier(), notification.getMarketplaceIdentifier());
                continue;
            }
            AmazonAdCampaignAll amazonAdCampaignAll = sbCampaigns.get(String.valueOf(group.getCampaignId()));
            sdGroupReport = new AmazonAdSdGroupReport();
            sdGroupReport.setPuid(notification.getSellerIdentifier());
            sdGroupReport.setShopId(notification.getMarketplaceIdentifier());
            sdGroupReport.setMarketplaceId(notification.getMarketplace().getId());
            sdGroupReport.setBidOptimization(group.getBidOptimization());
            sdGroupReport.setCountDate(group.getDate().format(DateTimeFormatter.ofPattern("yyyyMMdd")));
            sdGroupReport.setTacticType(amazonAdCampaignAll.getTactic());
            sdGroupReport.setCurrency(group.getCampaignBudgetCurrencyCode());
            sdGroupReport.setCampaignName(group.getCampaignName());
            sdGroupReport.setCampaignId(String.valueOf(group.getCampaignId()));
            sdGroupReport.setAdGroupName(group.getAdGroupName());
            sdGroupReport.setAdGroupId(String.valueOf(group.getAdGroupId()));
            sdGroupReport.setClicks(group.getClicks());
            sdGroupReport.setCost(group.getCost());
            sdGroupReport.setConversions14dSameSKU(group.getPurchasesPromotedClicks());
            sdGroupReport.setSales14dSameSKU(group.getSalesPromotedClicks());
            sdGroupReport.setViewImpressions(group.getImpressionsViews());
            sdGroupReport.setImpressions(group.getImpressions());
            sdGroupReport.setCostType(amazonAdCampaignAll.getCostType());

            sdGroupReport.setNewToBrandDetailPageViews(group.getNewToBrandDetailPageViews());
            sdGroupReport.setAddToCart(group.getAddToCart());
            sdGroupReport.setAddToCartRate(group.getAddToCartRate());
            sdGroupReport.setECPAddToCart(group.getECPAddToCart());
            sdGroupReport.setVideoFirstQuartileViews(group.getVideoFirstQuartileViews());
            sdGroupReport.setVideoMidpointViews(group.getVideoMidpointViews());
            sdGroupReport.setVideoThirdQuartileViews(group.getVideoThirdQuartileViews());
            sdGroupReport.setVideoCompleteViews(group.getVideoCompleteViews());
            sdGroupReport.setVideoUnmutes(group.getVideoUnmutes());
            sdGroupReport.setViewabilityRate(group.getViewabilityRate());
            sdGroupReport.setViewClickThroughRate(group.getViewClickThroughRate());
            sdGroupReport.setBrandedSearches(group.getBrandedSearches());
            sdGroupReport.setCumulativeReach(group.getCumulativeReach());
            sdGroupReport.setImpressionsFrequencyAverage(group.getImpressionsFrequencyAverage());

            if (Constants.SD_REPORT_VCPM.equalsIgnoreCase(amazonAdCampaignAll.getCostType())) {
                sdGroupReport.setUnitsOrdered14d(group.getUnitsSold());
                sdGroupReport.setConversions14d(group.getPurchases());
                sdGroupReport.setSales14d(group.getSales());
                sdGroupReport.setDetailPageView14d(group.getDetailPageViews());
                sdGroupReport.setOrdersNewToBrand14d(group.getNewToBrandPurchases());
                sdGroupReport.setSalesNewToBrand14d(group.getNewToBrandSales());
                sdGroupReport.setUnitsOrderedNewToBrand14d(group.getNewToBrandUnitsSold());
            } else {
                sdGroupReport.setUnitsOrdered14d(group.getUnitsSoldClicks());
                sdGroupReport.setConversions14d(group.getPurchasesClicks());
                sdGroupReport.setSales14d(group.getSalesClicks());
                sdGroupReport.setDetailPageView14d(group.getDetailPageViewsClicks());
                sdGroupReport.setOrdersNewToBrand14d(group.getNewToBrandPurchasesClicks());
                sdGroupReport.setSalesNewToBrand14d(group.getNewToBrandSalesClicks());
                sdGroupReport.setUnitsOrderedNewToBrand14d(group.getNewToBrandUnitsSoldClicks());
            }
            list.add(sdGroupReport);
        }
        return list;
    }
}
