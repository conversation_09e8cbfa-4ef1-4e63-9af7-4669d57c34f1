package com.meiyunji.sponsored.service.hotdata.dao;


import com.meiyunji.sponsored.common.springjdbc.IAdBaseDao;
import com.meiyunji.sponsored.service.hotdata.po.AmazonShopHotData;

import java.util.Date;
import java.util.List;

public interface IAmazonShopHotDataDao extends IAdBaseDao<AmazonShopHotData> {

    void insertOrUpdate(List<AmazonShopHotData> list);

    List<AmazonShopHotData> getByShopIdAndType(Integer puid, String marketplaceId, List<Integer> shopIdList, String dataType, Date updateTime);
}