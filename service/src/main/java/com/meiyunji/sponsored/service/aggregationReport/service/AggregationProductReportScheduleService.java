package com.meiyunji.sponsored.service.aggregationReport.service;

import com.meiyunji.sponsored.common.util.ThreadPoolUtil;
import com.meiyunji.sponsored.service.aggregationReport.dao.IAmazonAggregationReportScheduleDao;
import com.meiyunji.sponsored.service.aggregationReport.po.AmazonAggregationReportSchedule;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdProductAggregationReportDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdProductAggregationReport;
import com.meiyunji.sponsored.service.sellfoxApi.IProductApi;
import com.meiyunji.sponsored.service.vo.ProductAdReportVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;

@Service
@Slf4j
public class AggregationProductReportScheduleService {

    private IAmazonAggregationReportScheduleDao amazonAggregationReportScheduleDao;
    private final IAmazonAdProductAggregationReportDao amazonAdProductAggregationReportDao;
    private final IProductApi productApi;

    public AggregationProductReportScheduleService(
            IAmazonAggregationReportScheduleDao amazonAggregationReportScheduleDao,
            IAmazonAdProductAggregationReportDao amazonAdProductAggregationReportDao,
            IProductApi productApi) {
        this.amazonAggregationReportScheduleDao = amazonAggregationReportScheduleDao;
        this.amazonAdProductAggregationReportDao = amazonAdProductAggregationReportDao;
        this.productApi = productApi;
    }


    public void execute(List<AmazonAggregationReportSchedule> needsSyncSchedules, Integer limit) {
        CountDownLatch countDownLatch = new CountDownLatch(needsSyncSchedules.size());
        ThreadPoolExecutor threadExecutor = ThreadPoolUtil.getAggregationProductReportOfParentAsinPool();
        for (AmazonAggregationReportSchedule schedule : needsSyncSchedules) {
            threadExecutor.execute(()-> {
                try {
                    dealProductAggregationReport(schedule, limit);
                } catch (Exception e) {
                    log.info("sync aggregation report for null parentAsin error:", e);
                }
                countDownLatch.countDown();
            });
        }
        while (countDownLatch.getCount() !=0) {
            try {
                countDownLatch.await();
            } catch (InterruptedException e) {
                log.error("error:", e);
            }
        }
    }

    public void noneParentAsinExecute(List<AmazonAggregationReportSchedule> needsSyncSchedules, Integer limit, Integer syncDay) {
        CountDownLatch countDownLatch = new CountDownLatch(needsSyncSchedules.size());
        ThreadPoolExecutor threadExecutor = ThreadPoolUtil.getAggregationProductReportOfNoneParentAsinPool();
        for (AmazonAggregationReportSchedule schedule : needsSyncSchedules) {
            threadExecutor.execute(()-> {
                try {
                    dealNoParentAsinProductAggregationReport(schedule, limit, syncDay);
                    log.info("synced aggregation report for none parentAsin");
                } catch (Exception e) {
                    log.info("sync aggregation report for none parentAsin error:", e);
                }
                countDownLatch.countDown();
            });
        }

        while (countDownLatch.getCount() !=0) {
            try {
                countDownLatch.await();
            } catch (InterruptedException e) {
                log.error("error:", e);
            }
        }
    }

    private void dealProductAggregationReport(AmazonAggregationReportSchedule schedule, Integer limit) {
        List<AmazonAdProductAggregationReport> productAggregationReports =
                amazonAdProductAggregationReportDao.getNeedsSyncParentAsin(
                        schedule.getPuid(), schedule.getShopId(), limit);

        log.info("{}@{} execute product aggregationReport task, need to aggregate product aggregationReport size: {}",
                schedule.getPuid(), schedule.getShopId(), productAggregationReports.size());
        if (CollectionUtils.isNotEmpty(productAggregationReports)) {
            List<String> asinList = productAggregationReports.stream()
                    .map(AmazonAdProductAggregationReport::getAsin)
                    .filter(Objects::nonNull).distinct().collect(Collectors.toList());

            log.info("start request sellfox sponsored product product parent asin api . {}@{}",
                    schedule.getPuid(), schedule.getShopId());
            long t1 = Instant.now().toEpochMilli();
            List<ProductAdReportVo> productAdReportVos = productApi.getParentAsinsByChildAsins(schedule.getPuid(), schedule.getShopId(), asinList);
            log.info("request sellfox sponsored product product parent asin api, spend total time：{}",
                    Instant.now().toEpochMilli() - t1);

            Map<String, String> asinProductAdReportMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(productAdReportVos)) {
                log.info("{}@{} paren_asin size: {}", schedule.getPuid(), schedule.getShopId(), productAdReportVos.size());
                // 去掉重复asin
                for (ProductAdReportVo reportVo : productAdReportVos) {
                    if (!asinProductAdReportMap.containsKey(reportVo.getAsin()) && StringUtils.isNotBlank(reportVo.getParentAsin())) {
                        asinProductAdReportMap.put(reportVo.getAsin(), reportVo.getParentAsin());
                    }
                }
            }
            //TODO 无父asin填充none便于带条件区分加定时跑无父asin数据。
            productAggregationReports.stream().forEach(report -> {
                if (report.getAsin() == null || !asinProductAdReportMap.containsKey(report.getAsin())) {
                    report.setParentAsin("None");
                } else {
                    report.setParentAsin(asinProductAdReportMap.get(report.getAsin()));
                }
            });
            amazonAdProductAggregationReportDao.batchUpdateParentAsin(schedule.getPuid(), productAggregationReports);
        } else {
            schedule.setNextSyncAt(LocalDateTime.now().plusHours(6));
            amazonAggregationReportScheduleDao.updateNextSyncAtById(schedule);
        }

    }

    private void dealNoParentAsinProductAggregationReport(AmazonAggregationReportSchedule schedule, Integer limit, Integer syncDay) {
        List<AmazonAdProductAggregationReport> productAggregationReports =
                amazonAdProductAggregationReportDao.getNeedsSyncNoneParentAsin(
                        schedule.getPuid(), schedule.getShopId(), limit, syncDay);

        log.info("{}@{} execute none parent_asin product aggregationReport task, need to aggregate 'None' parent_asin product aggregationReport size: {}",
                schedule.getPuid(), schedule.getShopId(), productAggregationReports.size());
        if (CollectionUtils.isNotEmpty(productAggregationReports)) {
            List<String> asinList = productAggregationReports.stream()
                    .map(AmazonAdProductAggregationReport::getAsin)
                    .filter(Objects::nonNull).distinct().collect(Collectors.toList());

            log.info("start request sellfox sponsored product product parent asin api for 'None' parentAsin. {}@{}",
                    schedule.getPuid(), schedule.getShopId());
            long t1 = Instant.now().toEpochMilli();
            List<ProductAdReportVo> productAdReportVos = productApi.getParentAsinsByChildAsins(schedule.getPuid(), schedule.getShopId(), asinList);
            log.info("request sellfox sponsored product product parent asin api for 'None' parentAsin, spend total time：{}",
                    Instant.now().toEpochMilli() - t1);

            Map<String, String> asinProductAdReportMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(productAdReportVos)) {
                for (ProductAdReportVo reportVo : productAdReportVos) {
                    if (!asinProductAdReportMap.containsKey(reportVo.getAsin()) && StringUtils.isNotBlank(reportVo.getParentAsin())) {
                        asinProductAdReportMap.put(reportVo.getAsin(), reportVo.getParentAsin());
                    }
                }
            }
            productAggregationReports.stream().filter($-> $.getAsin() != null).forEach(report -> {
                report.setParentAsin(asinProductAdReportMap.getOrDefault(report.getAsin().trim(), "None"));
                report.setSyncNextDate(LocalDateTime.now().plusDays(1));
            });
            List<AmazonAdProductAggregationReport> updateReports = productAggregationReports.stream()
                    .filter(item -> !"None".equalsIgnoreCase(item.getParentAsin())).collect(Collectors.toList());
            amazonAdProductAggregationReportDao.batchUpdateParentAsin(schedule.getPuid(), updateReports);
            amazonAdProductAggregationReportDao.batchUpdateSyncNextDate(schedule.getPuid(), productAggregationReports);
        } else {
            schedule.setNoneNextSyncAt(LocalDateTime.now().plusDays(1));
            amazonAggregationReportScheduleDao.updateNoneNextSyncAtById(schedule);
        }
    }
}
