package com.meiyunji.sponsored.service.kafka;

import com.meiyunji.sponsored.common.util.GZipUtils;
import com.meiyunji.sponsored.common.util.JSONUtil;
import org.springframework.kafka.core.KafkaTemplate;

import java.nio.charset.StandardCharsets;

/**
 * @author: liweibin
 * @email: <EMAIL>
 * @create: 2024-07-17 18:35
 */
public class AdOperationNotesLogTaskKafkaProducer {
    private final String topic;
    private final KafkaTemplate<String, byte[]> kafkaTemplate;

    public AdOperationNotesLogTaskKafkaProducer(String topic, KafkaTemplate<String, byte[]> kafkaTemplate) {
        this.topic = topic;
        this.kafkaTemplate = kafkaTemplate;
    }

    public void send(Object messageContent) throws Exception {
        kafkaTemplate.send(topic, GZipUtils.compressMasterProject(JSONUtil.objectToJson(messageContent),0));
    }
}
