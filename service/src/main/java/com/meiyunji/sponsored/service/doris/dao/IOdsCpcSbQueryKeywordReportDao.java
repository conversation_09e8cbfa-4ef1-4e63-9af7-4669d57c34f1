package com.meiyunji.sponsored.service.doris.dao;

import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.springjdbc.IDorisBaseDao;
import com.meiyunji.sponsored.service.cpc.bo.SearchTermAggregateBO;
import com.meiyunji.sponsored.service.cpc.dto.AdProductReportSearchTermsViewDto;
import com.meiyunji.sponsored.service.cpc.po.CpcSbQueryKeywordReport;
import com.meiyunji.sponsored.service.cpc.vo.AdMetricDto;
import com.meiyunji.sponsored.service.cpc.vo.CpcQueryWordDto;
import com.meiyunji.sponsored.service.cpc.vo.chartVo.AdHomePerformancedto;
import com.meiyunji.sponsored.service.doris.po.OdsCpcSbQueryKeywordReport;
import com.meiyunji.sponsored.service.newDashboard.dto.DashboardAdQueryWordMatrixTopDto;
import com.meiyunji.sponsored.service.newDashboard.enums.DashboardDataFieldEnum;
import com.meiyunji.sponsored.service.newDashboard.enums.DashboardOrderByEnum;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.qo.SearchTermsViewParam;
import com.meiyunji.sponsored.service.wordFrequency.qo.QueryWordTopQo;
import com.meiyunji.sponsored.service.wordFrequency.vo.WordRootTopVo;

import java.util.Collection;
import java.util.List;

/**
 * amazon关键词搜索词报告表(OdsCpcSbQueryKeywordReport)数据库访问层
 *
 * <AUTHOR>
 * @since 2024-03-26 10:46:22
 */
public interface IOdsCpcSbQueryKeywordReportDao extends IDorisBaseDao<OdsCpcSbQueryKeywordReport> {

    /**
     * 广告看板 - 构建查询用户搜索词数据sql
     *
     * @param puid
     * @param marketplaceIdList
     * @param shopIdList
     * @param currency
     * @param startDate
     * @param endDate
     * @param argsList
     * @return
     */
    String buildQueryAdQueryWordChartsSql(Integer puid, List<String> marketplaceIdList, List<Integer> shopIdList, String currency, String startDate, String endDate, List<Object> argsList);


    String buildQueryAdQueryWordTopSimpleInfosSql(Integer puid,
                                                  List<String> marketplaceIdList,
                                                  List<Integer> shopIdList,
                                                  String currency,
                                                  String startDate,
                                                  String endDate,
                                                  List<Object> argsList,
                                                  String innerSelectColumns,
                                                  boolean isOrderByFieldAboutAmount,
                                                  boolean firstQuery,
                                                  List<String> queryWords, List<String> siteToday, Boolean isSiteToday,
                                                  List<String> portfolioIds, List<String> campaignIds,
                                                  Boolean noZero, DashboardDataFieldEnum dashboardDataFieldEnum);

    /**
     * 广告看板，搜索词矩阵
     *
     * @param puid
     * @param marketplaceIdList
     * @param shopIdList
     * @param currency
     * @param startDate
     * @param endDate
     * @param dataField
     * @param keywordIdList
     * @param limit
     * @param orderBy
     * @return
     */
    List<DashboardAdQueryWordMatrixTopDto> queryMatrixInfo(Integer puid, List<String> marketplaceIdList,
                                                           List<Integer> shopIdList, String currency,
                                                           String startDate, String endDate,
                                                           DashboardDataFieldEnum dataField, List<String> keywordIdList,
                                                           Integer limit, DashboardOrderByEnum orderBy, List<String> siteToday, Boolean isSiteToday,
                                                           List<String> portfolioIds, List<String> campaignIds,
                                                           Boolean noZero);

    List<OdsCpcSbQueryKeywordReport> getByQueryIdList(Integer puid, List<Integer> shopIdList, Collection<String> queryIdList);

    /**
     * sb搜索词列表页
     */
    Page<CpcSbQueryKeywordReport> pageList(Integer puid, CpcQueryWordDto dto, Page page);

    /**
     * 求总数量条数
     */
    int countAllList(Integer puid, CpcQueryWordDto dto);

    /**
     * sb搜索词列表页占比总值
     */
    AdMetricDto getSumAdMetricDto(Integer puid, CpcQueryWordDto dto);

    /**
     * 产品透视分析搜索词视图查询搜索词数据
     * @param puid
     * @param param
     * @return
     */
    List<AdProductReportSearchTermsViewDto> selectProductSearchTermsViewInfo(Integer puid, SearchTermsViewParam param);

    /**
     * sb搜索词列表页汇总、图表
     */
    List<AdHomePerformancedto> getQueryKeywordReportAggregate(Integer puid, CpcQueryWordDto dto, boolean isGroupByDate, boolean selCompareData);

    /**
     * sb搜索词列表页获取top词根
     */
    List<WordRootTopVo> getWordRootToplist(QueryWordTopQo dto);

    String buildQueryAdQueryWordPageSql(Integer puid,
                                        List<String> marketplaceIdList,
                                        List<Integer> shopIdList,
                                        String startDate,
                                        String endDate,
                                        List<Object> argsList,
                                        String queryWord, List<String> siteToday, Boolean isSiteToday,
                                        List<String> portfolioIds, List<String> campaignIds,
                                        Boolean noZero, DashboardDataFieldEnum dashboardDataFieldEnum, String matchType);

    List<SearchTermAggregateBO> allSearchTermAggregateDataByQueryList(Integer puid, CpcQueryWordDto dto, String startDate, String endDate,
                                                                      List<String> queryList);
}

