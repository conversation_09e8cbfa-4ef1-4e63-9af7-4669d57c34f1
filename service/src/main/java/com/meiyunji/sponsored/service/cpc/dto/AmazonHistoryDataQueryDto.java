package com.meiyunji.sponsored.service.cpc.dto;

import com.meiyunji.sponsored.service.cpc.po.AmazonAdCampaignAll;
import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @author: sunlinfeng
 * @email: <EMAIL>
 * @date: 2024-12-31  10:06
 */

@Data
public class AmazonHistoryDataQueryDto {

    //sp活动数据
    List<AmazonAdCampaignAll> spCampaignList = new ArrayList<>();

    //sp活动id-名称 map
    Map<String, String> spCampaignNameMap = new HashMap<>();

    //sb活动id-名称 map
    Map<String, String> sbCampaignNameMap = new HashMap<>();

    //sd活动id-名称 map
    Map<String, String> sdCampaignNameMap = new HashMap<>();

    //sp活动id-预算 map
    Map<String, BigDecimal> spBudgetMap = new HashMap<>();

    //sb活动id-预算 map
    Map<String, BigDecimal> sbBudgetMap = new HashMap<>();

    //sp组id-名称 map
    Map<String, String> spAdGroupNameMap = new HashMap<>();

    //sp产品id-asin map
    Map<String, String> spAdProductNameMap = new HashMap<>();

    //sd产品id-asin map
    Map<String, String> sdAdProductNameMap = new HashMap<>();

    //sp自动投放id-自动投放值 map
    Map<String, String> autoTargetingTypeMap = new HashMap<>();
}
