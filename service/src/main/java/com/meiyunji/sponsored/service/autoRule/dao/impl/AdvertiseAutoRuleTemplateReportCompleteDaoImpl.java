package com.meiyunji.sponsored.service.autoRule.dao.impl;

import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.springjdbc.AdBaseDaoImpl;
import com.meiyunji.sponsored.common.util.SqlStringUtil;
import com.meiyunji.sponsored.service.autoRule.dao.AdvertiseAutoRuleTemplateReportCompleteDao;
import com.meiyunji.sponsored.service.autoRule.enums.AutoRuleReportCompleteEnum;
import com.meiyunji.sponsored.service.autoRule.po.AdvertiseAutoRuleTemplateReportComplete;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;


@Repository
public class AdvertiseAutoRuleTemplateReportCompleteDaoImpl extends AdBaseDaoImpl<AdvertiseAutoRuleTemplateReportComplete> implements AdvertiseAutoRuleTemplateReportCompleteDao {

    @Override
    public void insertOrUpdateList(List<AdvertiseAutoRuleTemplateReportComplete> addList) {
        StringBuilder sql = new StringBuilder("INSERT INTO " + getJdbcHelper().getTable() + " (" +
                "`puid`, `shop_id`,`report_complete`, `create_time`, `update_time`) VALUES ");
        List<Object> argsList = Lists.newArrayList();
        for (AdvertiseAutoRuleTemplateReportComplete x : addList) {
            sql.append("(?, ?, ?, now(), now()),");
            argsList.add(x.getPuid());
            argsList.add(x.getShopId());
            argsList.add(x.getReportComplete());
        }
        sql.deleteCharAt(sql.length() - 1);
        sql.append(" on duplicate key update `update_time`=now() ");

        getJdbcTemplate().update(sql.toString(), argsList.toArray());
    }

    @Override
    public void updateCompleteList(List<AdvertiseAutoRuleTemplateReportComplete> updateList) {
        StringBuilder sql = new StringBuilder("update " + getJdbcHelper().getTable() + " set `report_complete`= ?, ")
                .append(" `update_time` = now() where puid = ? and shop_id = ?");
        List<Object[]> batchArgs = Lists.newArrayList();
        Object[] batchArg;
        for (AdvertiseAutoRuleTemplateReportComplete x : updateList) {
            batchArg = new Object[]{x.getReportComplete(), x.getPuid(), x.getShopId()};
            batchArgs.add(batchArg);
        }
        getJdbcTemplate().batchUpdate(sql.toString(), batchArgs);
    }

    @Override
    public AdvertiseAutoRuleTemplateReportComplete getByPuidAndShopId(int puid, int shopId) {
        String sql = "select * from " + getJdbcHelper().getTable() + " where puid = ? and shop_id = ? ";
        List<AdvertiseAutoRuleTemplateReportComplete> list = getJdbcTemplate().query(sql, getRowMapper(), puid, shopId);
        return CollectionUtils.isNotEmpty(list) ? list.get(0) : null;
    }

    @Override
    public List<AdvertiseAutoRuleTemplateReportComplete> getByPuidAndShopIdList(List<Integer> puidList, List<Integer> shopIdList) {
        StringBuilder sql = new StringBuilder();
        sql.append("select * from ");
        sql.append(getJdbcHelper().getTable());
        sql.append(" where ");
        List<Object> argsList = new ArrayList<>();
        sql.append(SqlStringUtil.dealInListNotAnd("puid", puidList, argsList));
        sql.append(SqlStringUtil.dealInList("shop_id", shopIdList, argsList));
        return getJdbcTemplate().query(sql.toString(), getRowMapper(), argsList.toArray());
    }

    @Override
    public List<AdvertiseAutoRuleTemplateReportComplete> selectNotComplete(long startIndex, int limit) {
        String sql = "select * from " + getJdbcHelper().getTable() +
                " where report_complete = " + AutoRuleReportCompleteEnum.NOT_COMPLETE.getValue() +
                " and id > " + startIndex + " order by id limit " + limit;

        return getJdbcTemplate().query(sql, getRowMapper());
    }

    @Override
    public List<AdvertiseAutoRuleTemplateReportComplete> getCompleteByShopIdList(List<Integer> shopIdList) {
        if (CollectionUtils.isEmpty(shopIdList)) {
            return null;
        }
        String sql = "select * from " + getJdbcHelper().getTable() +
                " where report_complete = " + AutoRuleReportCompleteEnum.COMPLETE.getValue() +
                " and shop_id in (" + StringUtils.join(shopIdList, ",") + ")";
        return getJdbcTemplate().query(sql, getRowMapper());
    }
}
