package com.meiyunji.sponsored.service.cpc.dao;

import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.springjdbc.IBaseShardingDao;
import com.meiyunji.sponsored.service.cpc.bo.AllGroupOrderBo;
import com.meiyunji.sponsored.service.cpc.dto.AdGroupReportHourlyDTO;
import com.meiyunji.sponsored.service.cpc.dto.AdReportData;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdSdGroupReport;
import com.meiyunji.sponsored.service.cpc.vo.*;
import com.meiyunji.sponsored.service.cpc.vo.chartVo.AdHomePerformancedto;
import com.meiyunji.sponsored.service.index.po.SponsoredIndexCampaignData;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Created by lm on 2021/5/14.
 *
 */
public interface IAmazonAdSdGroupReportDao extends IBaseShardingDao<AmazonAdSdGroupReport> {

    /**
     * 批量插入、更新
     * @param puid
     * @param list
     */
    void insertOrUpdateList(Integer puid, List<AmazonAdSdGroupReport> list);

    void insertDorisList(Integer puid, List<AmazonAdSdGroupReport> list);
    Page getPageList(int puid, SearchVo search, Page page);

    Page detailPageList(int puid, Integer shopId, String marketplaceId, ReportParam param , Page page);

    AmazonAdSdGroupReport getSumReportByGroupId(int puid, Integer shopId, String marketplaceId, String startStr, String endStr, String groupId);

    List<AmazonAdSdGroupReport> getChartList(int puid, Integer shopId, String marketplaceId, String startStr, String endStr, String groupId);

    List<Map<String, Object>> getAdCampaignMap(int puid, Integer shopId, String marketplaceId);

    List<AmazonAdSdGroupReport> listSumReports(int puid, Integer shopId, String startDate, String endDate, List<String> groupIds);

    // 获取这段时间内每一天的报告
    List<AmazonAdSdGroupReport> listReports(int puid, Integer shopId, String startDate, String endDate, String groupId);

    List<AdHomePerformancedto> getSdReportByDate(Integer puid, Integer shopId, String startStr, String endStr, GroupPageParam param);

    List<AdHomePerformancedto> getSdReportByDate(Integer puid, Integer shopId, String startStr, String endStr, GroupPageParam param, boolean isLatest);

    List<String> idListByGroupPageParamSelAmazonAdGroupReport(Integer puid, GroupPageParam param, List<String> noInGroupId);

    /**
     * 用于获取汇总数据
     * @param puid
     * @param shopId
     * @param startStr
     * @param endStr
     * @param param
     * @return
     */
    AdMetricDto getSumMetric(Integer puid, Integer shopId, String startStr, String endStr, GroupPageParam param);


    List<AdHomePerformancedto> getSdReportByGroupIdList(Integer puid, Integer shopId, String startStr, String endStr, List<String> groupIdList);

    List<AdHomePerformancedto> listSumReportByGroupIds(Integer puid, Integer shopId, String startStr, String endStr, GroupPageParam param, List<String> groupIds);

    List<AdHomePerformancedto> listLatestReports(Integer puid, Integer shopId, List<String> groupIds, boolean isAggregation);

    /**
     *
     * @param puid
     * @param shopId
     * @param date  更新数据前的时间
     * @return
     */
    List<String> getGroupListByUpdateTime(Integer puid, Integer shopId, Date date);

    List<AdGroupReportHourlyDTO> getSdGroupReportByGroupId(Integer puid, Integer shopId, String startDate, String endDate, List<String> adGroupIdList);

    int deleteByPuidAndShopId(Integer puid, Integer shopId, Integer limit);

    List<AllGroupOrderBo> getSdGroupIdAndIndexList(Integer puid, GroupPageParam param);

    List<String> getSdGroupIdListByParam(Integer puid, GroupPageParam param);

    List<GroupInfoPageVo> getSdReportByGroupIds(Integer puid, GroupPageParam param, List<String> groupIdList);

    List<AdHomePerformancedto> getSdReportByGroupIdList(Integer puid, GroupPageParam param, List<String> groupIdList);

    AdMetricDto getSdGroupPageSumMetricDataByCampaignIdList(Integer puid, GroupPageParam param, List<String> groupIdList);

    List<SponsoredIndexCampaignData> listSponsoredIndex(Integer puid, List<Integer> shopIdList, List<String> adGroupIdList, Set<String> fields, String startDate, String endState);

    List<AdReportData> getAllReportByGroupIdsAndDate(Integer puid, Integer shopId, String startStr, String endStr, List<String> groupIdList);
}
