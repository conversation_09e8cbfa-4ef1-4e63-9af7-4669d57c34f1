package com.meiyunji.sponsored.service.batchCreate.po;

import com.meiyunji.sponsored.common.springjdbc.DbColumn;
import com.meiyunji.sponsored.common.springjdbc.DbTable;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @author: sunlinfeng
 * @email: <EMAIL>
 * @date: 2023-11-17  17:43
 */

@Data
@DbTable(value = "t_amazon_ad_batch_nekeyword")
public class AmazonAdBatchNekeyword implements Serializable {

    @DbColumn(value = "id")
    private Long id;

    @DbColumn(value = "puid")
    private Integer puid;

    @DbColumn(value = "shop_id")
    private Integer shopId;

    @DbColumn(value = "profile_id")
    private String profileId;

    @DbColumn(value = "marketplace_id")
    private String marketplaceId;

    @DbColumn(value = "task_id")
    private Long taskId;

    @DbColumn(value = "campaign_id")
    private Long campaignId;

    @DbColumn(value = "group_id")
    private Long groupId;

    @DbColumn(value = "keyword_text")
    private String keywordText;

    @DbColumn(value = "match_type")
    private String matchType;

    @DbColumn(value = "task_status")
    private Byte taskStatus;

    @DbColumn(value = "err_msg")
    private String errMsg;

    @DbColumn(value = "execute_count")
    private Integer executeCount;

    @DbColumn(value = "next_retry_time")
    private Date nextRetryTime;

    @DbColumn(value = "amazon_campaign_id")
    private String amazonCampaignId;

    @DbColumn(value = "amazon_ad_group_id")
    private String amazonAdGroupId;

    @DbColumn(value = "amazon_ad_keyword_id")
    private String amazonAdKeywordId;

    @DbColumn(value = "create_time")
    private Date createTime;

    @DbColumn(value = "create_id")
    private Integer createId;

    @DbColumn(value = "update_time")
    private Date updateTime;

    @DbColumn(value = "update_id")
    private Integer updateId;

}
