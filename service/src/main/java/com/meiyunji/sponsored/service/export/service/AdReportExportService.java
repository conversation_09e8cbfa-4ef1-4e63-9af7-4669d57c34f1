package com.meiyunji.sponsored.service.export.service;

import com.alibaba.fastjson.JSON;
import com.amazon.advertising.mode.PredicateEnum;
import com.google.common.collect.Lists;
import com.google.protobuf.Int32Value;
import com.google.protobuf.ProtocolStringList;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.base.ProcessMsg;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.support.RedisService;
import com.meiyunji.sponsored.common.support.StringRedisService;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.rpc.export.TargetReportDataRequest;
import com.meiyunji.sponsored.rpc.report.export.CommonReportExportRequest;
import com.meiyunji.sponsored.rpc.report.export.UrlResponse;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.config.DynamicRefreshConfiguration;
import com.meiyunji.sponsored.service.cpc.constants.AdTargetTaskMatchTypeEnum;
import com.meiyunji.sponsored.service.cpc.po.TargetTypeEnum;
import com.meiyunji.sponsored.service.cpc.service.*;
import com.meiyunji.sponsored.service.cpc.service2.ICpcFlowReportService;
import com.meiyunji.sponsored.service.cpc.service2.impl.CpcShopDataService;
import com.meiyunji.sponsored.service.cpc.service2.sp.ICpcAsinReportService;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.vo.*;
import com.meiyunji.sponsored.service.dataWarehouse.statsDo.ShopSaleDto;
import com.meiyunji.sponsored.service.download.service.IAdDownloadCenterService;
import com.meiyunji.sponsored.service.enums.AdReportExportAdTypeEnum;
import com.meiyunji.sponsored.service.excel.excelTools.WriteHandlerBuild;
import com.meiyunji.sponsored.service.excel.excelTools.service.IExcelService;
import com.meiyunji.sponsored.service.export.AdReportExportExecutorHelper;
import com.meiyunji.sponsored.service.export.HelperResult;
import com.meiyunji.sponsored.service.export.ThreadPoolDbPageQueryFunction;
import com.meiyunji.sponsored.service.export.constants.AdReportExportTypeEnum;
import com.meiyunji.sponsored.service.export.convert.ReportDataVoExportConvert;
import com.meiyunji.sponsored.service.export.convert.ReportVoExportConvert;
import com.meiyunji.sponsored.service.export.convert.impl.sd.TargetingReportListSdExportConvert;
import com.meiyunji.sponsored.service.export.dto.FlowCacheDto;
import com.meiyunji.sponsored.service.export.task.ReportDataVoExportTask;
import com.meiyunji.sponsored.service.export.task.ReportVoExportTask;
import com.meiyunji.sponsored.service.grpcApi.aadasApi.AadasApi;
import com.meiyunji.sponsored.service.grpcApi.aadasApi.dto.CreateFlowReportTaskDto;
import com.meiyunji.sponsored.service.util.SummaryReportUtil;
import com.meiyunji.sponsored.service.vo.*;
import com.meiyunji.sponsored.service.vo.AdCampaignVo;
import com.meiyunji.sponsored.service.vo.AdProductReportVo;
import io.grpc.stub.StreamObserver;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import java.time.Instant;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.meiyunji.sponsored.service.cpc.util.Constants.SD_TARGET_DOWNLOAD_TYPE;

/**
 * 广告报告导出service
 *
 * @Author: hejh
 * @Date: 2024/5/22 18:54
 */
@Service
@Slf4j
public class AdReportExportService {

    @Autowired
    private IAmazonAdGroupReportService amazonAdGroupReportService;
    @Autowired
    private IAmazonAdProductReportService amazonAdProductReportService;
    @Autowired
    private ICpcTargetingReportService cpcTargetingReportService;
    @Autowired
    private ICpcAsinReportService asinReportService;
    @Autowired
    private ICpcFlowReportService flowReportService;
    @Autowired
    private ICpcQueryKeywordReportService cpcQueryKeywordReportService;
    @Autowired
    private ICpcSbQueryKeywordReportService cpcSbQueryKeywordReportService;
    @Autowired
    private IAmazonAdCampaignAllReportService amazonAdCampaignAllReportService;
    @Autowired
    private IAmazonAdSbKeywordReportService keywordReportService;
    @Autowired
    private IAmazonAdSdGroupReportService groupSdReportService;
    @Autowired
    private IAmazonAdSdProductReportService productSdReportService;
    @Autowired
    private IAmazonAdSdTargetingReportService targetingSdReportService;
    @Autowired
    private CpcShopDataService cpCShopDataService;
    @Autowired
    private IExcelService excelService;
    @Autowired
    private IAdDownloadCenterService adDownloadCenterService;
    @Autowired
    private IAmazonAdSdCampaignMatchedTargetReportService campaignMatchedTargetReportService;
    @Autowired
    private IAmazonAdSdAsinReportService sdAsinReportService;
    @Autowired
    private AdReportExportExecutorHelper exportExecutorHelper;
    @Autowired
    private DynamicRefreshConfiguration dynamicRefreshConfiguration;
    @Autowired
    private Map<String, ReportDataVoExportConvert> dataVoConvertBeanMap;
    @Autowired
    private Map<String, ReportVoExportConvert> voConvertBeanMap;
    @Autowired
    private AadasApi aadasApi;
    @Autowired
    private RedisService redisService;
    @Autowired
    private IScVcShopAuthDao shopAuthDao;
    @Autowired
    private IAmazonAdProductAggregationReportService aggregationReportService;
    @Autowired
    private StringRedisService stringRedisService;
    /**
     * sp广告活动报告导出
     *
     * @param request
     * @param responseObserver
     */
    public void campaignSpReportExport(CommonReportExportRequest request, StreamObserver<UrlResponse> responseObserver) {
        SearchVoExtraParam extraParam = SearchVoExtraParam.builder().type(request.getAdType()).build();
        UrlResponse.Builder builder = doExport4ReportVo(AdReportExportTypeEnum.SP_CAMPAIGN, request, extraParam,
            (a, b, c) -> amazonAdCampaignAllReportService.pageList(a, b, c), AdCampaignVo.class);
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * sp广告位报告导出
     *
     * @param request
     * @param responseObserver
     */
    public void spaceSpReportExport(CommonReportExportRequest request, StreamObserver<UrlResponse> responseObserver, String campaignSite) {
        SearchVoExtraParam extraParam = SearchVoExtraParam.builder().type(AdReportExportAdTypeEnum.sp.getAdType()).campaignSite(campaignSite).build();
        AdReportExportTypeEnum exportType = PredicateEnum.SITEAMAZONBUSINESS.value().equals(campaignSite) ? AdReportExportTypeEnum.AMAZON_BUSINESS : AdReportExportTypeEnum.SP_SPACE;
        UrlResponse.Builder builder = doExport4ReportVo(exportType, request, extraParam,
            (a, b, c) -> amazonAdCampaignAllReportService.pageSpaceList(a, b, c), AdSpaceVo.class);
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * sp广告产品报告导出
     *
     * @param request
     * @param responseObserver
     */
    public void productSpReportExport(CommonReportExportRequest request, StreamObserver<UrlResponse> responseObserver) {
        UrlResponse.Builder builder = doExport4ReportVo(AdReportExportTypeEnum.SP_PRODUCT, request, null,
            (a, b, c) -> amazonAdProductReportService.pageList4DownloadCenterExport(a, b, c), AdProductReportVo.class);
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * sp广告组报告导出
     *
     * @param request
     * @param responseObserver
     */
    public void groupSpReportExport(CommonReportExportRequest request, StreamObserver<UrlResponse> responseObserver) {
        UrlResponse.Builder builder = doExport4ReportVo(AdReportExportTypeEnum.SP_ADGROUP, request, null,
            (a, b, c) -> amazonAdGroupReportService.pageList4DownloadCenterExport(a, b, c), AdSpGroupExportVo.class);
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * sp广告投放报告导出
     *
     * @param request
     * @param responseObserver
     */
    public void targetingSpReportExport(CommonReportExportRequest request, StreamObserver<UrlResponse> responseObserver) {
        UrlResponse.Builder builder = doExport4ReportVo(AdReportExportTypeEnum.SP_TARGETING, request, null,
            (a, b, c) -> cpcTargetingReportService.pageList4DownloadCenterExport(a, b, c), AdExportCommonVo.class);
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * sp搜索词报告导出
     *
     * @param request
     * @param responseObserver
     */
    public void searchTermSpExport(CommonReportExportRequest request, StreamObserver<UrlResponse> responseObserver) {
        UrlResponse.Builder builder = doExport4ReportVo(AdReportExportTypeEnum.SP_SEARCHTERM, request, null,
            (a, b, c) -> cpcQueryKeywordReportService.pageManageListExport(a, b, c), AdSpSearchTermVo.class);
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * sp已购买商品报告导出
     *
     * @param request
     * @param responseObserver
     */
    public void purchasedItemExport(CommonReportExportRequest request, StreamObserver<UrlResponse> responseObserver) {
        UrlResponse.Builder builder = doExport4ReportVo(AdReportExportTypeEnum.SP_PURCHASED, request, null,
            (a, b, c) -> asinReportService.pageList(a, b, c), AdSpAsinReportVo.class);
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * 广告总流量与无效流量报告导出
     *
     * @param request
     */
    public void grossAndInvalidTrafficAllSpReport(CommonReportExportRequest request) throws Exception {
        FlowCacheDto flowCacheDto = new FlowCacheDto();
        flowCacheDto.setReportId(request.getReportId());
        flowCacheDto.setPuid(request.getPuid().getValue());
        flowCacheDto.setTabType(request.getTabType());
        flowCacheDto.setStartDate(request.getStartDate());
        flowCacheDto.setEndDate(request.getEndDate());
        flowCacheDto.setShopIds(request.getShopIdsList());
        flowCacheDto.setAdType(request.getAdType());
        flowCacheDto.setReportName(request.getReportName());
        HashMap<String, Boolean> hashMap = new HashMap<>();
        List<ShopAuth> shopList = shopAuthDao.getAuthShopByShopIdList(request.getPuid().getValue(), request.getShopIdsList());
        for (ShopAuth shop : shopList) {
            CreateFlowReportTaskDto dto = new CreateFlowReportTaskDto();
            dto.setSellerId(shop.getSellingPartnerId());
            dto.setStartDate(request.getStartDate());
            dto.setEndDate(request.getEndDate());
            dto.setMarketplaceId(shop.getMarketplaceId());
            dto.setRegion(shop.getRegion());
            dto.setType(request.getAdType());
            String uuid = aadasApi.createFlowReportTask(dto);
            hashMap.put(uuid, false);
        }
        flowCacheDto.setUuidSyncedMap(hashMap);
        String flowCacheDtoValue = JSON.toJSONString(flowCacheDto);
        hashMap.forEach((uuid, value) -> redisService.set(uuid, request.getReportId(), 2, TimeUnit.HOURS));
        redisService.set(String.valueOf(request.getReportId()), flowCacheDtoValue, 2, TimeUnit.HOURS);
    }

    /**
     * 通知下载中心下载报告
     * @param uuid
     */
    public void notifyReportCenter(String uuid) {
        String reportId = redisService.getString(uuid);
        if (StringUtils.isBlank(reportId)) {
            return;
        }
        FlowCacheDto flowCacheDto = JSON.parseObject( redisService.getString(reportId), FlowCacheDto.class);
        if (Objects.isNull(flowCacheDto) || Objects.isNull(flowCacheDto.getUuidSyncedMap())) {
            return;
        }
        flowCacheDto.getUuidSyncedMap().put(uuid, true);
        //一个任务下有任何一个报告未完成，则继续等待
        if (flowCacheDto.getUuidSyncedMap().values().stream().anyMatch(value -> !value)) {
            redisService.set(reportId, JSON.toJSONString(flowCacheDto), 2, TimeUnit.HOURS);
            return;
        }
        redisService.del(uuid);
        CommonReportExportRequest request = CommonReportExportRequest.newBuilder()
            .setReportId(flowCacheDto.getReportId())
            .setPuid(Int32Value.of(flowCacheDto.getPuid()))
            .setTabType(flowCacheDto.getTabType())
            .setStartDate(flowCacheDto.getStartDate())
            .setEndDate(flowCacheDto.getEndDate())
            .addAllShopIds(flowCacheDto.getShopIds())
            .setReportName(flowCacheDto.getReportName())
            .build();
        doExport4ReportVo(AdReportExportTypeEnum.AD_FLOW, request, null, (a, b, c) -> flowReportService.pageList(a, b, c), AdFlowVo.class);
    }

    /**
     * sb广告活动报告导出
     *
     * @param request
     * @param responseObserver
     */
    public void campaignSbReportExport(CommonReportExportRequest request, StreamObserver<UrlResponse> responseObserver) {
        SearchVoExtraParam extraParam = SearchVoExtraParam.builder().type(AdReportExportAdTypeEnum.sb.getAdType()).build();
        UrlResponse.Builder builder = doExport4ReportDataVo(AdReportExportTypeEnum.SB_CAMPAIGN, request, extraParam,
            (a, b, c) -> amazonAdCampaignAllReportService.pageList(a, b, c), AdSbCampaignVo.class);
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * sb搜索词报告导出
     *
     * @param request
     * @param responseObserver
     */
    public void searchTermSbExport(CommonReportExportRequest request, StreamObserver<UrlResponse> responseObserver) {
        SearchVoExtraParam extraParam = SearchVoExtraParam.builder().build();
        UrlResponse.Builder builder = doExport4ReportDataVo(AdReportExportTypeEnum.SB_SEARCHTERM, request, extraParam,
            (a, b, c) -> cpcSbQueryKeywordReportService.pageManageExportList(a, b, c), AdSbSearchTermVo.class);
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * sb投放报告导出
     *
     * @param request
     * @param responseObserver
     */
    public void targetingSbReportExport(CommonReportExportRequest request, StreamObserver<UrlResponse> responseObserver) {
        SearchVoExtraParam extraParam = SearchVoExtraParam.builder().build();
        UrlResponse.Builder builder = doExport4ReportDataVo(AdReportExportTypeEnum.SB_TARGETING, request, extraParam,
            (a, b, c) -> keywordReportService.sbKeywordTargetingPageList4DownloadCenterExport(a, b, c), AdSbTargetExportVo.class);
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * sb广告位报告导出
     *
     * @param request
     * @param responseObserver
     */
    public void spaceSbReportExport(CommonReportExportRequest request, StreamObserver<UrlResponse> responseObserver) {
        SearchVoExtraParam extraParam = SearchVoExtraParam.builder().type(AdReportExportAdTypeEnum.sb.getAdType()).build();
        UrlResponse.Builder builder = doExport4ReportDataVo(AdReportExportTypeEnum.SB_SPACE, request, extraParam,
            (a, b, c) -> amazonAdCampaignAllReportService.pageSbAndSbvSpaceList(a, b, c), AdSbSpaceVo.class);
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * sd广告活动报告导出
     *
     * @param request
     * @param responseObserver
     */
    public void campaignSdReportExport(CommonReportExportRequest request, StreamObserver<UrlResponse> responseObserver) {
        SearchVoExtraParam extraParam = SearchVoExtraParam.builder().type(AdReportExportAdTypeEnum.sd.getAdType()).build();
        UrlResponse.Builder builder = doExport4ReportDataVo(AdReportExportTypeEnum.SD_CAMPAIGN, request, extraParam,
            (a, b, c) -> amazonAdCampaignAllReportService.pageList(a, b, c), AdSdCampaignVo.class);
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * sd广告组报告导出
     *
     * @param request
     * @param responseObserver
     */
    public void groupSdReportExport(CommonReportExportRequest request, StreamObserver<UrlResponse> responseObserver) {
        UrlResponse.Builder builder = doExport4ReportDataVo(AdReportExportTypeEnum.SD_ADGROUP, request, null,
            (a, b, c) -> groupSdReportService.pageList4DownloadCenterExport(a, b, c), AdSdGroupExportVo.class);
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * sd广告产品报告导出
     *
     * @param request
     * @param responseObserver
     */
    public void productSdReportExport(CommonReportExportRequest request, StreamObserver<UrlResponse> responseObserver) {
        UrlResponse.Builder builder = doExport4ReportDataVo(AdReportExportTypeEnum.SD_PRODUCT, request, null,
            (a, b, c) -> productSdReportService.pageList4DownloadCenterExport(a, b, c), AdSdProductExportVo.class);
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * sd投放报告导出，前端用的是targetReportListSdReportExport，但是openapi可能还在用这个
     *
     * @param request
     * @param responseObserver
     */
    public void targetingSdReportExport(CommonReportExportRequest request, StreamObserver<UrlResponse> responseObserver) {
        UrlResponse.Builder builder = doExport4ReportDataVo(AdReportExportTypeEnum.SD_TARGETING, request, null,
            (a, b, c) -> targetingSdReportService.pageList(a, b, c), AdSdTargetExportVo.class);
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * sd匹配的目标报告导出
     *
     * @param request
     * @param responseObserver
     */
    public void campaignMatchedTargetSdReportExport(CommonReportExportRequest request, StreamObserver<UrlResponse> responseObserver) {
        SearchVoExtraParam extraParam = SearchVoExtraParam.builder().type(AdReportExportAdTypeEnum.sd.getAdType()).build();
        UrlResponse.Builder builder = doExport4ReportDataVo(AdReportExportTypeEnum.SD_MATCHED, request, extraParam,
            (a, b, c) -> campaignMatchedTargetReportService.pageList(a, b, c), AdSdCampaignMatchTargetVo.class);
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * sd已购买商品报告导出
     *
     * @param request
     * @param responseObserver
     */
    public void sdAsinReportExport(CommonReportExportRequest request, StreamObserver<UrlResponse> responseObserver) {
        UrlResponse.Builder builder = doExport4ReportVo(AdReportExportTypeEnum.SD_PURCHASED, request, null,
            (a, b, c) -> sdAsinReportService.pageList(a, b, c), AdSdAsinReportVo.class);
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * sd投放报告数据导出：包含商品投放和受众投放
     *
     * @param request
     * @param responseObserver
     */
    public void targetReportListSdReportExport(CommonReportExportRequest request, StreamObserver<UrlResponse> responseObserver) {
        SearchVoExtraParam extraParam = SearchVoExtraParam.builder().downloadType(SD_TARGET_DOWNLOAD_TYPE).build();
        UrlResponse.Builder builder = doExport4ReportDataVo(AdReportExportTypeEnum.SD_TARGETING_LIST, request, extraParam,
            (a, b, c) -> targetingSdReportService.pageList4DownloadCenterExport(a, b, c), AdSdTargetListExportVo.class);
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    /**
     * 数据库查询为ReportVo的通用查询方法
     *
     * @param exportType 报告类型，枚举，用于取得转换器bean和日志中输出报告名称
     * @param request    前端请求参数
     * @param extraParam 扩展参数
     * @param f          函数，用于数据库查询数据
     * @param clazz      最终写入excel的实体类型
     * @return
     */
    public UrlResponse.Builder doExport4ReportVo(
        AdReportExportTypeEnum exportType,
        CommonReportExportRequest request,
        SearchVoExtraParam extraParam,
        ThreadPoolDbPageQueryFunction<Integer, SearchVo, Page<ReportVo>, Page<ReportVo>> f,
        Class clazz
    ) {
        log.info("{}开始下载, reportId: {}, request: {}", exportType.getReportName(), request.getReportId(), request);
        StopWatch watch = new StopWatch();
        watch.start();
        UrlResponse.Builder urlBuilder = UrlResponse.newBuilder();
        try {

            SearchVo searchVo = convertMessageToSearchVo(request);
            if (checkParam(searchVo) && !request.hasPuid()) {
                urlBuilder.setMsg("请求参数错误");
                urlBuilder.setCode(Int32Value.of(Result.ERROR));
                return urlBuilder;
            }

            //额外参数设置
            if (extraParam != null) {
                if (StringUtils.isNotBlank(extraParam.getType())) {
                    searchVo.setType(extraParam.getType());
                }
                if (StringUtils.isNotBlank(extraParam.getAdFormat())) {
                    searchVo.setAdFormat(extraParam.getAdFormat());
                }
                if (StringUtils.isNotBlank(extraParam.getDownloadType())) {
                    searchVo.setDownloadType(extraParam.getDownloadType());
                }
                searchVo.setCampaignSite(extraParam.getCampaignSite());
            }

            //查询所有门店
            Map<Integer, ShopAuth> shopMap = exportExecutorHelper.getShopMap(request.getPuid().getValue());
            //保存最终的url集合
            List<String> urls = new ArrayList<>();
            int pageSize = dynamicRefreshConfiguration.getReportExportThreadPageSize();
            //数据转换器
            ReportVoExportConvert convert = voConvertBeanMap.get(exportType.getConvertBeanName());
            //主线程查询第1页
            HelperResult helperResult = new ReportVoExportTask(shopMap, request, searchVo, 1, pageSize, f, excelService, exportExecutorHelper, convert, clazz, null).call();
            log.info("{}查询第一页, reportId: {}, 共{}条数据, 共{}页, 每页条数{}", exportType.getReportName(), request.getReportId(), helperResult.getTotalSize(), helperResult.getTotalPage(), pageSize);
            if (StringUtils.isBlank(helperResult.getDownloadUrl())) {
                //生成空表
                String url = exportExecutorHelper.generateBlankExcel(request, clazz);
                urls.add(url);
            } else {
                urls.add(helperResult.getDownloadUrl());
                if (helperResult.getTotalPage() > 1) {
                    //超出1页提交线程池处理
                    List<HelperResult> helperResults = exportExecutorHelper.threadPoolPaginationQuery4ReportVo(shopMap, request, searchVo, helperResult.getTotalPage(), pageSize, f, convert, clazz);
                    urls.addAll(helperResults.stream().filter(Objects::nonNull).map(HelperResult::getDownloadUrl).filter(StringUtils::isNotBlank).collect(Collectors.toList()));
                }
            }

            //更新数据库url
            if (request.hasReportId() && CollectionUtils.isNotEmpty(urls)) {
                adDownloadCenterService.updateDownloadTask(request.getPuid().getValue(), request.getReportId(), StringUtils.join(urls, ","));
            }

            watch.stop();
            log.info("{}下载完成, reportId: {}, 共{}条数据, 共{}页, 每页条数{}, 耗时: {}秒", exportType.getReportName(), request.getReportId(), helperResult.getTotalSize(), helperResult.getTotalPage(), pageSize, (double)watch.getTotalTimeMillis() / 1000);

            urlBuilder.setCode(Int32Value.of(Result.SUCCESS));
            urlBuilder.setMsg("process.msg.sync.success");
            urlBuilder.addAllUrls(urls);
        } catch (Exception e) {
            urlBuilder.setMsg("process.msg.sync.fail");
            urlBuilder.setCode(Int32Value.of(Result.ERROR));
            log.error("{}下载异常, reportId: {}, {}", exportType.getReportName(), request.getReportId(), e);
        }

        return urlBuilder;
    }

    /**
     * 数据库查询为ReportDataVo的通用查询方法
     *
     * @param exportType 报告类型，枚举，用于取得转换器bean和日志中输出报告名称
     * @param request    前端请求参数
     * @param extraParam 扩展参数
     * @param f          函数，用于数据库查询数据
     * @param clazz      最终写入excel的实体类型
     * @return
     */
    private UrlResponse.Builder doExport4ReportDataVo(
        AdReportExportTypeEnum exportType,
        CommonReportExportRequest request,
        SearchVoExtraParam extraParam,
        ThreadPoolDbPageQueryFunction<Integer, SearchVo, Page<ReportDataVo>, Page<ReportDataVo>> f,
        Class clazz
    ) {
        log.info("{}开始下载, reportId: {}, request: {}", exportType.getReportName(), request.getReportId(), request);
        StopWatch watch = new StopWatch();
        watch.start();
        UrlResponse.Builder urlBuilder = UrlResponse.newBuilder();
        try {
            SearchVo searchVo = convertMessageToSearchVo(request);
            if (checkParam(searchVo) && !request.hasPuid()) {
                urlBuilder.setMsg("请求参数错误");
                urlBuilder.setCode(Int32Value.of(Result.ERROR));
                return urlBuilder;
            }

            //这里针对SD_TARGETING_LIST进行额外处理，后续如果其他报告有额外处理，可以使用策略或模板方法进行优化，此处只有一个报告先妥协一下
            ReportDataVoExportConvert convert;
            if (AdReportExportTypeEnum.SD_TARGETING_LIST.getReportType().equals(exportType.getReportType())) {
                //获取全部店铺销售额度
                List<ShopSaleDto> shopSaleDtoList = cpCShopDataService.listDailyShopSaleByDateRange(searchVo.getPuid(), searchVo.getShopIds(),
                    searchVo.getStartDate(), searchVo.getStartDate());
                Map<Integer, List<ShopSaleDto>> shopSaleDtoMap = null;
                if (!CollectionUtils.isEmpty(shopSaleDtoList)) {
                    shopSaleDtoMap = shopSaleDtoList.stream().collect(Collectors.groupingBy(ShopSaleDto::getShopId));
                }
                //创建SD_TARGETING_LIST专属数据转换器
                convert = new TargetingReportListSdExportConvert(exportExecutorHelper, shopSaleDtoMap);
            } else {
                //从beanMap中获取数据转换器
                convert = dataVoConvertBeanMap.get(exportType.getConvertBeanName());
            }

            //额外参数设置
            if (extraParam != null) {
                if (StringUtils.isNotBlank(extraParam.getType())) {
                    searchVo.setType(extraParam.getType());
                }
                if (StringUtils.isNotBlank(extraParam.getAdFormat())) {
                    searchVo.setAdFormat(extraParam.getAdFormat());
                }
                if (StringUtils.isNotBlank(extraParam.getDownloadType())) {
                    searchVo.setDownloadType(extraParam.getDownloadType());
                }
            }

            //查询所有门店
            Map<Integer, ShopAuth> shopMap = exportExecutorHelper.getShopMap(request.getPuid().getValue());
            //保存最终的url集合
            List<String> urls = new ArrayList<>();
            int pageSize = dynamicRefreshConfiguration.getReportExportThreadPageSize();
            //主线程查询第1页
            HelperResult helperResult = new ReportDataVoExportTask(shopMap, request, searchVo, 1, pageSize, f, excelService, exportExecutorHelper, convert, clazz, null).call();
            log.info("{}查询第一页, reportId: {}, 共{}条数据, 共{}页, 每页条数{}", exportType.getReportName(), request.getReportId(), helperResult.getTotalSize(), helperResult.getTotalPage(), pageSize);

            if (StringUtils.isBlank(helperResult.getDownloadUrl())) {
                //生成空表
                String url = exportExecutorHelper.generateBlankExcel(request, clazz);
                urls.add(url);
            } else {
                urls.add(helperResult.getDownloadUrl());
                if (helperResult.getTotalPage() > 1) {
                    //超出1页提交线程池处理
                    List<HelperResult> helperResults = exportExecutorHelper.threadPoolPaginationQuery4ReportDataVo(shopMap, request, searchVo, helperResult.getTotalPage(), pageSize, f, convert, clazz);
                    urls.addAll(helperResults.stream().filter(Objects::nonNull).map(HelperResult::getDownloadUrl).filter(StringUtils::isNotBlank).collect(Collectors.toList()));
                }
            }

            //更新数据库url
            if (request.hasReportId() && CollectionUtils.isNotEmpty(urls)) {
                adDownloadCenterService.updateDownloadTask(request.getPuid().getValue(), request.getReportId(), StringUtils.join(urls, ","));
            }

            watch.stop();
            log.info("{}下载完成, reportId: {}, 共{}条数据, 共{}页, 每页条数{}, 耗时: {}秒", exportType.getReportName(), request.getReportId(), helperResult.getTotalSize(), helperResult.getTotalPage(), pageSize, (double)watch.getTotalTimeMillis() / 1000);

            urlBuilder.setMsg("process.msg.sync.success");
            urlBuilder.setCode(Int32Value.of(Result.SUCCESS));
            urlBuilder.addAllUrls(urls);
        } catch (Exception e) {
            urlBuilder.setMsg("process.msg.sync.fail");
            urlBuilder.setCode(Int32Value.of(Result.ERROR));
            log.error("{}下载异常, reportId: {}, {}", exportType.getReportName(), request.getReportId(), e);
        }
        return urlBuilder;
    }

    private boolean checkParam(SearchVo vo) {
        if (StringUtils.isBlank(vo.getStartDate()) || StringUtils.isBlank(vo.getEndDate())) {
            return true;
        }
        Date start = DateUtil.strToDate(vo.getStartDate(), "yyyy-MM-dd");
        Date end = DateUtil.strToDate(vo.getEndDate(), "yyyy-MM-dd");
        if (start == null || end == null) {
            return true;
        }
        vo.setStart(start);
        vo.setEnd(end);
        return false;
    }

    private SearchVo convertMessageToSearchVo(CommonReportExportRequest request) {
        SearchVo searchVo = new SearchVo();
        searchVo.setPuid(request.getPuid().getValue());
        searchVo.setShopId(request.hasShopId() ? request.getShopId().getValue() : null);
        searchVo.setMarketplaceId(request.getMarketplaceId());
        searchVo.setStartDate(request.getStartDate());
        searchVo.setEndDate(request.getEndDate());
        searchVo.setSearchType(request.getSearchType());
        searchVo.setSearchValue(request.getSearchValue());
        searchVo.setOrderField(request.getOrderField());
        searchVo.setOrderValue(request.getOrderValue());
        searchVo.setAdGroupName(request.getAdGroupName());
        searchVo.setTabType(request.getTabType());
        searchVo.setCampaignId(request.getCampaignId());
        searchVo.setGroupId(request.getGroupId());
        searchVo.setAdType(request.getAdType());
        //处理list
        List<Integer> shopIds = request.getShopIdsList().stream().collect(Collectors.toList());
        searchVo.setShopIds(shopIds);
        //处理list
        searchVo.setSpCampaignType(request.getSpCampaignType());
        ProtocolStringList campaignIdsList = request.getCampaignIdsList();
        List<String> campaignIds = new ArrayList<>(campaignIdsList);
        searchVo.setCampaignIds(campaignIds);
        searchVo.setSpGroupType(request.getSpGroupType());
        //处理list
        ProtocolStringList groupIdsList = request.getGroupIdsList();
        List<String> groupIds = new ArrayList<>(groupIdsList);
        searchVo.setGroupIds(groupIds);
        searchVo.setMatchType(request.getMatchType());
        searchVo.setSpTargetType(request.getSpTargetType());
        searchVo.setReportName(request.getReportName());
        return searchVo;
    }

    @Async("aggregationTargetDataExportTaskExecutor")
    public void aggregationTargetDataExport(List<String> excludeFileds, TargetReportDataRequest request, List<TargetReportSearchVo> childVoList, Map<Integer, String> shopNameMap) {
        String fileName = "";
        int count = 0;
        List<TargetAndQueryReportExcelVo> subVoList;
        List<TargetAndQueryReportExcelVo> voList = Lists.newArrayList();
        // 返回下载的url
        List<String> urls = Lists.newLinkedList();
        if ("target".equals(request.getAsinType())) {
            excludeFileds.add("query");
            fileName = "数据聚合-ASIN-投放";
            Long t1 = Instant.now().toEpochMilli();
            for (TargetReportSearchVo searchVo : childVoList) {
                subVoList = new ArrayList<>();
                Result<List<TargetReportVo>> result = aggregationReportService.getTargetList(request.getPuid(), searchVo);
                subVoList = result.getData().stream().filter(Objects::nonNull)
                        .map(item -> {
                            TargetAndQueryReportExcelVo vo = new TargetAndQueryReportExcelVo();
                            BeanUtils.copyProperties(item, vo);
                            //对匹配类型做映射处理 AdTargetTaskMatchTypeEnum
                            if ("close_match".equals(item.getMatchType())) {
                                vo.setMatchType("紧密匹配");
                            } else if ("loose_match".equals(item.getMatchType())) {
                                vo.setMatchType("宽泛匹配");
                            } else if ("complements".equals(item.getMatchType())) {
                                vo.setMatchType("关联商品");
                            } else if ("substitutes".equals(item.getMatchType())) {
                                vo.setMatchType("同类商品");
                            } else if (AdTargetTaskMatchTypeEnum.ASIN.getCode().equals(item.getMatchType())) {
                                vo.setMatchType("精准");
                            } else if (AdTargetTaskMatchTypeEnum.ASIN_EXPANDED_FROM.getCode().equals(item.getMatchType())) {
                                vo.setMatchType("扩展");
                            } else if (TargetTypeEnum.category.name().equals(item.getMatchType())) {
                                vo.setMatchType("类目");
                            } else if (AdTargetTaskMatchTypeEnum.PHRASE.getCode().equals(item.getMatchType())) {
                                vo.setMatchType("词组");
                            } else if (AdTargetTaskMatchTypeEnum.BROAD.getCode().equals(item.getMatchType())) {
                                vo.setMatchType("广泛");
                            } else if (AdTargetTaskMatchTypeEnum.EXACT.getCode().equals(item.getMatchType())) {
                                vo.setMatchType("精确");
                            } else if (AdTargetTaskMatchTypeEnum.THEME.getCode().equals(item.getMatchType())) {
                                vo.setMatchType("主题");
                            }
                            vo.setCtr(item.getCtr().trim() + "%");
                            vo.setCvr(item.getCvr().trim() + "%");
                            vo.setAcos(item.getAcos().trim() + "%");
                            vo.setAcots(item.getAcots().trim() + "%");
                            vo.setAsots(item.getAsots().trim() + "%");
                            vo.setTarget(item.getQuery());
                            vo.setAsin(searchVo.getAsin());
                            vo.setParentAsin(searchVo.getAsin());
                            vo.setShopName(StringUtils.isNotBlank(shopNameMap.get(item.getShopId())) ? shopNameMap.get(item.getShopId()) : "-");
                            SummaryReportUtil marketplace = SummaryReportUtil.getByMarketplaceId(item.getMarketplaceId());
                            if (marketplace != null) {
                                vo.setMarketplaceCN(marketplace.getMarketplaceCN());
                            }
                            return vo;
                        }).collect(Collectors.toList());
                voList.addAll(subVoList);
            }
            log.info("puid:{} 数据聚合-ASIN-投放数据量 aggregation product report size:{}, 共耗时：{}", request.getPuid(), voList.size(), t1 - Instant.now().toEpochMilli());
        }
        if ("query".equals(request.getAsinType())) {
            excludeFileds.add("target");
            fileName = "数据聚合-ASIN-搜索词";
            Long t1 = Instant.now().toEpochMilli();
            for (TargetReportSearchVo searchVo : childVoList) {
                subVoList = new ArrayList<>();
                QueryReportSearchVo searchVo1 = new QueryReportSearchVo();
                BeanUtils.copyProperties(searchVo, searchVo1);
                Result<List<QueryReportVo>> result = aggregationReportService.getQueryList(request.getPuid(), searchVo1);
                subVoList = result.getData().stream().filter(Objects::nonNull)
                        .map(item -> {
                            TargetAndQueryReportExcelVo vo = new TargetAndQueryReportExcelVo();
                            BeanUtils.copyProperties(item, vo);
                            //对匹配类型做映射处理 AdTargetTaskMatchTypeEnum
                            if ("close_match".equals(item.getMatchType())) {
                                vo.setMatchType("紧密匹配");
                            } else if ("loose_match".equals(item.getMatchType())) {
                                vo.setMatchType("宽泛匹配");
                            } else if ("complements".equals(item.getMatchType())) {
                                vo.setMatchType("关联商品");
                            } else if ("substitutes".equals(item.getMatchType())) {
                                vo.setMatchType("同类商品");
                            } else if (AdTargetTaskMatchTypeEnum.ASIN.getCode().equals(item.getMatchType())) {
                                vo.setMatchType("精准");
                            } else if (AdTargetTaskMatchTypeEnum.ASIN_EXPANDED_FROM.getCode().equals(item.getMatchType())) {
                                vo.setMatchType("扩展");
                            } else if (TargetTypeEnum.category.name().equals(item.getMatchType())) {
                                vo.setMatchType("类目");
                            } else if (AdTargetTaskMatchTypeEnum.PHRASE.getCode().equals(item.getMatchType())) {
                                vo.setMatchType("词组");
                            } else if (AdTargetTaskMatchTypeEnum.BROAD.getCode().equals(item.getMatchType())) {
                                vo.setMatchType("广泛");
                            } else if (AdTargetTaskMatchTypeEnum.EXACT.getCode().equals(item.getMatchType())) {
                                vo.setMatchType("精确");
                            } else if (AdTargetTaskMatchTypeEnum.THEME.getCode().equals(item.getMatchType())) {
                                vo.setMatchType("主题");
                            }
                            vo.setCtr(item.getCtr().trim() + "%");
                            vo.setCvr(item.getCvr().trim() + "%");
                            vo.setAcos(item.getAcos().trim() + "%");
                            vo.setAcots(item.getAcots().trim() + "%");
                            vo.setAsots(item.getAsots().trim() + "%");
                            vo.setTarget(item.getQuery());
                            vo.setAsin(searchVo1.getAsin());
                            vo.setParentAsin(searchVo1.getAsin());
                            vo.setShopName(StringUtils.isNotBlank(shopNameMap.get(item.getShopId())) ? shopNameMap.get(item.getShopId()) : "-");
                            SummaryReportUtil marketplace = SummaryReportUtil.getByMarketplaceId(item.getMarketplaceId());
                            if (marketplace != null) {
                                vo.setMarketplaceCN(marketplace.getMarketplaceCN());
                            }
                            return vo;
                        }).collect(Collectors.toList());
                voList.addAll(subVoList);
            }
            log.info("puid:{} 数据聚合-ASIN-搜索词数据量 aggregation product report size:{}, 共耗时：{}", request.getPuid(), voList.size(), t1 - Instant.now().toEpochMilli());
        }

        // 导出数据为空时
        if (CollectionUtils.isEmpty(voList)) {
            stringRedisService.set(request.getUuid(), new ProcessMsg(-1, 0, "没有可供导出的数据"));
        }
        try {
            // 执行导出处理逻辑
            WriteHandlerBuild build = new WriteHandlerBuild().rate().currencyNew(TargetAndQueryReportExcelVo.class);
            //数据有可能大于5万条,采用分片处理
            List<List<TargetAndQueryReportExcelVo>> partition = Lists.partition(voList, Constants.FILE_MAX_SIZE);
            for (List<TargetAndQueryReportExcelVo> partitionList : partition) {
                if (partitionList.size() > 0) {
                    urls.add(excelService.easyExcelHandlerExport(request.getPuid(), partitionList, fileName + "(" + count++ + ")", TargetAndQueryReportExcelVo.class, build, excludeFileds));
                }
            }
            if (CollectionUtils.isNotEmpty(urls)) {
                stringRedisService.set(request.getUuid(), new ProcessMsg(-1, 0, "导出数据为空"));
            }
            stringRedisService.set(request.getUuid(), new ProcessMsg(1, urls.size(), "导出成功", urls));
        } catch (Exception e) {
            log.error("puid:{} 数据聚合-ASIN-导出异常:{}", request.getPuid(), e);
        }

    }
}
