package com.meiyunji.sponsored.service.cpc.service2.sp.impl;

import com.alibaba.fastjson.JSON;
import com.amazon.advertising.keywords.negative.*;
import com.amazon.advertising.mode.keywords.Keyword;
import com.amazon.advertising.mode.keywords.KeywordResult;
import com.amazon.advertising.spV3.campaign.*;
import com.amazon.advertising.spV3.campaign.entity.*;
import com.amazon.advertising.spV3.enumeration.SpV3NegativeMatchTypeEnum;
import com.amazon.advertising.spV3.enumeration.SpV3State;
import com.amazon.advertising.spV3.enumeration.SpV3StateEnum;
import com.amazon.advertising.spV3.negativekeyword.CreateSpNegativeKeywordV3Response;
import com.amazon.advertising.spV3.negativekeyword.ListSpNegativeKeywordV3Response;
import com.amazon.advertising.spV3.negativekeyword.NegativeKeywordSpV3Client;
import com.amazon.advertising.spV3.negativekeyword.UpdateSpNegativeKeywordV3Response;
import com.amazon.advertising.spV3.negativekeyword.entity.*;
import com.amazon.advertising.spV3.response.ApiResponseV3;
import com.amazon.advertising.spV3.response.ErrorItemResultV3;
import com.amazonservices.mws.sellers.model.ListParticipations;
import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.config.DynamicRefreshNacosConfiguration;
import com.meiyunji.sponsored.common.exception.ServiceException;
import com.meiyunji.sponsored.common.util.AmazonResponseUtil;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.common.util.ResultUtil;
import com.meiyunji.sponsored.common.util.StringUtil;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.account.service.IShopAuthService;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdCampaignNeKeywordsDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdNeKeywordDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdProfileDao;
import com.meiyunji.sponsored.service.cpc.dto.AmazonServingStatusDto;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdCampaignNeKeywords;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdKeyword;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdNeKeyword;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdProfile;
import com.meiyunji.sponsored.service.cpc.service.IAmazonAdKeywordDaoRoutingService;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.util.CpcStatusEnum;
import com.meiyunji.sponsored.service.cpc.vo.BatchNeKeywordVo;
import com.meiyunji.sponsored.service.cpc.vo.BatchResponseVo;
import com.meiyunji.sponsored.service.cpc.vo.SpNeKeywordsVo;
import com.meiyunji.sponsored.service.stream.enums.ManagementStreamResponseStatusEnum;
import com.meiyunji.sponsored.service.util.AmazonAdUtils;
import com.meiyunji.sponsored.service.util.AmazonErrorUtils;
import com.meiyunji.sponsored.service.util.LocalDateTimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static cn.hutool.core.date.DateUtil.now;

/**
 * Created by xp on 2021/4/15.
 * 对接口二次封装，直接和广告接口交互
 */
@Component
@Slf4j
public class CpcNeKeywordsApiService {

    @Autowired
    private IScVcShopAuthDao shopAuthDao;
    @Autowired
    private IShopAuthService shopAuthService;
    @Autowired
    private IAmazonAdProfileDao amazonAdProfileDao;
    @Autowired
    private IAmazonAdCampaignNeKeywordsDao amazonAdCampaignNeKeywordsDao;
    @Resource
    private DynamicRefreshNacosConfiguration dynamicRefreshNacosConfiguration;
    @Autowired
    private IAmazonAdNeKeywordDao amazonAdNekeywordDao;
    @Autowired
    private IAmazonAdKeywordDaoRoutingService amazonAdKeywordDaoRoutingService;

    private static final int DEFAULT_REQUEST_SIZE = 1000;
    /**
     * 同步所有的关键词
     *
     * @param shop：
     */
    public void syncNeKeywords(ShopAuth shop, String campaignId, String groupId, String keywordId) {
        syncNeKeywords(shop, campaignId, groupId, keywordId, false);
    }

    public void syncNeKeywords(ShopAuth shop, String campaignId, String groupId, String keywordId, boolean nullResThrowException) {
        syncNeKeywords(shop, campaignId, groupId, keywordId, null, nullResThrowException, false);
    }

    public void syncNeKeywords(ShopAuth shop, String campaignId, String groupId, String keywordId, List<SpV3State> stateList, boolean nullResThrowException) {
        syncNeKeywords(shop, campaignId, groupId, keywordId, null, nullResThrowException, false);
    }

    public List<AmazonServingStatusDto> listByIds(Integer puid, Integer shopId, String ids){
        List<AmazonAdKeyword> amazons = amazonAdKeywordDaoRoutingService.getByKeywordIds(puid, shopId, StringUtil.stringToList(ids, ","), Constants.NEGATIVE);
        return CollectionUtils.isEmpty(amazons) ? new ArrayList<>() : amazons.stream().map(key -> {
                    key.setServingStatus(key.getServingStatus());
                    return AmazonServingStatusDto.build(key.getKeywordId(), key.getServingStatus(), key.getServingStatusName(), key.getServingStatusDec());
                }
        ).collect(Collectors.toList());
    }


    /**
     * 同步所有的关键词
     *
     * @param shop：
     */
    public void syncNeKeywords(ShopAuth shop, String campaignId, String groupId, String keywordId, List<SpV3State> stateList, boolean nullResThrowException, boolean isProxy) {
        if (shop == null) {
            return;
        }
        if (StringUtils.isBlank(shop.getAdRefreshToken())) {
            log.error("syncAdGroups:店铺{}没有授权广告", shop.getId());
            return;
        }

        //先获取到配置信息
        AmazonAdProfile amazonAdProfile = amazonAdProfileDao.getProfile(shop.getPuid(), shop.getId());
        if (amazonAdProfile == null) {
            log.error("syncAdGroups--配置信息为空");
            return;
        }
        List<String> campaignIdList = null;
        List<String> groupIdList = null;
        List<String> keywordIdList = null;
        if(StringUtils.isNotBlank(campaignId)){
            campaignIdList = StringUtil.splitStr(campaignId,",");
        }
        if(StringUtils.isNotBlank(groupId)){
            groupIdList = StringUtil.splitStr(groupId,",");
        }
        if(StringUtils.isNotBlank(keywordId)){
            keywordIdList = StringUtil.splitStr(keywordId,",");
        }
        //获取活动的基本信息
        NegativeKeywordSpV3Client client = NegativeKeywordSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable());

        if (isProxy) {
            client = NegativeKeywordSpV3Client.getInstance(isProxy);
        }

        int count = 5000;
        ListSpNegativeKeywordV3Response response;
        boolean refreshedToken = false; // 是否刷新了token，保证只刷新一次
        String nextToken = null;
        while (true) {
            response = client.listNegativeKeyword(shopAuthService.getAdToken(shop), amazonAdProfile.getProfileId(), shop.getMarketplaceId(),
                    campaignIdList, groupIdList, keywordIdList, stateList, null,
                    null, null, true, nextToken, count);
            int retry = 1;
            // 出现429，使用指数回避策略重试
            while (response != null && response.getStatusCode().intValue() == AmazonAdUtils.rateLimitingCode) {
                log.info("SP neKeywords rate limiting code: {}", AmazonAdUtils.rateLimitingCode);
                if(retry > AmazonAdUtils.retry) {
                    break;
                }
                response = client.listNegativeKeyword(shopAuthService.getAdToken(shop), amazonAdProfile.getProfileId(), shop.getMarketplaceId(),
                        campaignIdList, groupIdList, keywordIdList, stateList, null,
                        null, null, true, nextToken, count);
                retry++;
            }
            if (response != null
                    && response.getStatusCode() != null
                    && response.getStatusCode() == 401
                    && !refreshedToken) {
                //刷新token
                shopAuthService.refreshCpcAuth(shop);
                refreshedToken = true;
                continue;
            }

            if (AmazonResponseUtil.isError(response) && nullResThrowException) {
                throw new ServiceException("sp syncNeKeywords error");
            }

            if (response == null || response.getData() == null ||  CollectionUtils.isEmpty(response.getData().getNegativeKeywords())) {
                break;
            }

            int size = response.getData().getNegativeKeywords().size();
            AmazonAdKeyword amazonAdKeyword;
            List<AmazonAdKeyword> amazonAdKeywords = new ArrayList<>(size);
            for (NegativeKeywordExtendEntityV3 keyword : response.getData().getNegativeKeywords()) {
                amazonAdKeyword = turnToPO(keyword);
                if (StringUtils.isNotBlank(amazonAdKeyword.getKeywordId())) {
                    amazonAdKeyword.setPuid(shop.getPuid());
                    amazonAdKeyword.setShopId(shop.getId());
                    amazonAdKeyword.setMarketplaceId(shop.getMarketplaceId());
                    amazonAdKeyword.setProfileId(amazonAdProfile.getProfileId());
                    amazonAdKeywords.add(amazonAdKeyword);
                }
            }
            //每500一批入库
            List<List<AmazonAdKeyword>> partition = Lists.partition(amazonAdKeywords, 500);
            for (List<AmazonAdKeyword> adKeywords : partition) {
                amazonAdKeywordDaoRoutingService.insertOnDuplicateKeyUpdate(shop.getPuid(), adKeywords, Constants.NEGATIVE);
            }
            if(StringUtils.isNotBlank(response.getData().getNextToken())){
                nextToken = response.getData().getNextToken();
            } else {
                break;
            }

        }
    }

    /**
     * 同步所有的关键词
     *
     * @param shop：
     */
    public void syncCampaignNeKeywords(ShopAuth shop, String campaignId) {
        syncCampaignNeKeywords(shop, campaignId, null, false);
    }

    public void syncCampaignNeKeywords(ShopAuth shop, String campaignId, String campaignNegativeKeywordIds, boolean nullResThrowException) {
        syncCampaignNeKeywords(shop, campaignId, campaignNegativeKeywordIds, null, nullResThrowException, false);
    }

    public void syncCampaignNeKeywords(ShopAuth shop, String campaignId, String campaignNegativeKeywordIds, List<CampaignV3State> stateList, boolean nullResThrowException) {
        syncCampaignNeKeywords(shop, campaignId, campaignNegativeKeywordIds, null, nullResThrowException, false);
    }

    /**
     * 同步所有的关键词
     *
     * @param shop：
     */
    public void syncCampaignNeKeywords(ShopAuth shop, String campaignId, String campaignNegativeKeywordIds, List<CampaignV3State> stateList, boolean nullResThrowException, boolean isProxy) {
        if (shop == null) {
            return;
        }
        if (StringUtils.isBlank(shop.getAdRefreshToken())) {
            log.error("syncAdGroups:店铺{}没有授权广告", shop.getId());
            return;
        }

        //先获取到配置信息
        AmazonAdProfile amazonAdProfile = amazonAdProfileDao.getProfile(shop.getPuid(), shop.getId());
        if (amazonAdProfile == null) {
            log.error("syncAdGroups--配置信息为空");
            return;
        }

        //获取活动的基本信息
        int startIndex = 0;
        int count = 5000;
        ListSpCampaignNegativeKeywordV3Response response;
        boolean refreshedToken = false; // 是否刷新了token，保证只刷新一次
        List<String> campaignIdList = null;
        if(StringUtils.isNotBlank(campaignId)){
            campaignIdList = StringUtil.splitStr(campaignId,",");
        }
        List<String> campaignNegativeKeywordIdList = null;
        if(StringUtils.isNotBlank(campaignNegativeKeywordIds)){
            campaignNegativeKeywordIdList = StringUtil.splitStr(campaignNegativeKeywordIds,",");
        }
        CampaignNegativeKeywordSpV3Client client = CampaignNegativeKeywordSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable());
        if (isProxy) {
            client = CampaignNegativeKeywordSpV3Client.getInstance(true);
        }
        String nextToken = null;
        while (true) {
            response = client.listCampaignsNegativeKeyword(shopAuthService.getAdToken(shop), amazonAdProfile.getProfileId(),
                    shop.getMarketplaceId(), campaignIdList, campaignNegativeKeywordIdList, stateList, null, null, null, true, nextToken, count);
            int retry = 1;
            // 出现429，使用指数回避策略重试
            while (response != null && response.getStatusCode().intValue() == AmazonAdUtils.rateLimitingCode) {
                log.info("SP campaigns neKeywords rate limiting code: {}", AmazonAdUtils.rateLimitingCode);
                if(retry > AmazonAdUtils.retry) {
                    break;
                }
                response = client.listCampaignsNegativeKeyword(shopAuthService.getAdToken(shop), amazonAdProfile.getProfileId(),
                        shop.getMarketplaceId(), campaignIdList, campaignNegativeKeywordIdList, stateList, null, null, null, true, nextToken, count);
                retry++;
            }
            if (response != null
                    && response.getStatusCode() != null
                    && response.getStatusCode() == 401
                    && !refreshedToken) {
                //刷新token
                shopAuthService.refreshCpcAuth(shop);
                refreshedToken = true;
                continue;
            }

            if (AmazonResponseUtil.isError(response) && nullResThrowException) {
                throw new ServiceException("sp syncCampaignNeKeywords error");
            }

            if (response == null || response.getData() == null ||  CollectionUtils.isEmpty(response.getData().getCampaignNegativeKeywords())) {
                break;
            }

            int size = response.getData().getCampaignNegativeKeywords().size();
            AmazonAdCampaignNeKeywords neKeyword;
            List<AmazonAdCampaignNeKeywords> neKeywordses = new ArrayList<>(size);
            for (CampaignNegativeKeywordExtendEntityV3 keyword : response.getData().getCampaignNegativeKeywords()) {
                neKeyword = turnToCampaignNePO(keyword);
                if (StringUtils.isNotBlank(neKeyword.getKeywordId())) {
                    neKeyword.setPuid(shop.getPuid());
                    neKeyword.setShopId(shop.getId());
                    neKeyword.setMarketplaceId(shop.getMarketplaceId());
                    neKeyword.setProfileId(amazonAdProfile.getProfileId());
                    neKeywordses.add(neKeyword);
                }
            }

            amazonAdCampaignNeKeywordsDao.insertOnDuplicateKeyUpdate(shop.getPuid(), neKeywordses);

            if(StringUtils.isNotBlank(response.getData().getNextToken())){
                nextToken = response.getData().getNextToken();
            } else {
                break;
            }
        }
    }



    public Result<List<AmazonAdKeyword>> update(ArrayList<AmazonAdKeyword> amazonAdKeywords) {
        if (CollectionUtils.isEmpty(amazonAdKeywords)) {
            return ResultUtil.returnErr("请求参数错误");
        }

        AmazonAdKeyword one = amazonAdKeywords.get(0);

        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(one.getShopId(), one.getPuid());
        if (shop == null) {
            return ResultUtil.returnErr("店铺不存在");
        }

        List<String> states = amazonAdKeywords.stream().map(AmazonAdKeyword::getState).distinct().collect(Collectors.toList());
        if(states.size() > 1){
            return ResultUtil.returnErr("修改状态不正确");
        }
        boolean isArchived = SpV3StateEnum.ARCHIVED.value().equalsIgnoreCase(states.get(0));

        List<NegativeKeywordEntityV3> keywords = makePutNegativeKeywordsV3(amazonAdKeywords);
        List<String> keywordIds = amazonAdKeywords.stream().map(AmazonAdKeyword::getKeywordId).collect(Collectors.toList());
        UpdateSpNegativeKeywordV3Response response = null;
        if(isArchived){
            response = NegativeKeywordSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).delNegativeKeywords(shopAuthService.getAdToken(shop),
                    one.getProfileId(), one.getMarketplaceId(), keywordIds,Boolean.TRUE);
        } else {
            response = NegativeKeywordSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).putNegativeKeyword(shopAuthService.getAdToken(shop),
                    one.getProfileId(), one.getMarketplaceId(), keywords,Boolean.TRUE);
        }
        if (response != null
                && response.getStatusCode() != null
                && response.getStatusCode() == 401) {
            // 刷新token重试一次
            shopAuthService.refreshCpcAuth(shop);
            if(isArchived){
                response = NegativeKeywordSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).delNegativeKeywords(shopAuthService.getAdToken(shop),
                        one.getProfileId(), one.getMarketplaceId(), keywordIds,Boolean.TRUE);
            } else {
                response = NegativeKeywordSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).putNegativeKeyword(shopAuthService.getAdToken(shop),
                        one.getProfileId(), one.getMarketplaceId(), keywords,Boolean.TRUE);
            }
        }
        if (response == null) {
            return ResultUtil.returnErr("网络延迟，请稍后重试");
        }

        //处理返回结果中的错误信息
        if (response.getData() != null && response.getData().getNegativeKeywords() != null) {
            StringBuilder error = new StringBuilder();
            List<ErrorItemResultV3> errorItemResultV3 = response.getData().getNegativeKeywords().getError();
            List<NegativeKeywordSuccessResultV3> success = response.getData().getNegativeKeywords().getSuccess();

            int index = 0;
            List<AmazonAdKeyword> succList = new ArrayList<>();
            for(NegativeKeywordSuccessResultV3 keywordResult : success){
                succList.add(amazonAdKeywords.get(index));
            }
            for(ErrorItemResultV3 keywordResult : errorItemResultV3){
                error.append("targetValue:").append(amazonAdKeywords.get(keywordResult.getIndex()).getKeywordText()).append(",desc:").append(AmazonErrorUtils.getError(keywordResult.getErrors().get(0).getErrorMessage())).append(";");
            }

            if (succList.size() > 0) {
                Result<List<AmazonAdKeyword>> result = ResultUtil.returnSucc(succList);
                result.setMsg(error.toString());
                return result;
            }

            return ResultUtil.returnErr(error.toString());
        }

        String msg = "网络延迟，请稍后重试";
        if(StringUtils.isNotBlank(response.getError().getMessage())){
            msg = AmazonErrorUtils.getError(response.getError().getMessage());
        } else if(CollectionUtils.isNotEmpty(response.getError().getErrors())){
            msg = AmazonErrorUtils.getError(response.getError().getErrors().get(0).getErrorMessage());
        }
        return ResultUtil.returnErr(msg);
    }

    /**
     * 否定关键词-归档
     *
     * @param amazonAdKeyword：
     * @return ：
     */
    public Result archive(AmazonAdKeyword amazonAdKeyword) {
        if (amazonAdKeyword == null) {
            return ResultUtil.error("请求参数错误");
        }

        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(amazonAdKeyword.getShopId(), amazonAdKeyword.getPuid());
        if (shop == null) {
            return ResultUtil.error("店铺不存在");
        }

        UpdateSpNegativeKeywordV3Response response = NegativeKeywordSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).delNegativeKeywords(shopAuthService.getAdToken(shop),
                amazonAdKeyword.getProfileId(), shop.getMarketplaceId(), Lists.newArrayList(amazonAdKeyword.getKeywordId()),true);
        if (response != null
                && response.getStatusCode() != null
                && response.getStatusCode() == 401) {
            // 刷新token重试一次
            shopAuthService.refreshCpcAuth(shop);
            response = NegativeKeywordSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).delNegativeKeywords(shopAuthService.getAdToken(shop),
                    amazonAdKeyword.getProfileId(), shop.getMarketplaceId(), Lists.newArrayList(amazonAdKeyword.getKeywordId()),true);
        }
        if (response == null) {
            return ResultUtil.error("网络延迟，请稍后重试");
        }

        if (response.getData() != null && response.getData().getNegativeKeywords() != null && CollectionUtils.isNotEmpty(response.getData().getNegativeKeywords().getSuccess())) {
            return ResultUtil.success();
        }

        //处理返回结果中的错误信息
        String msg = "网络延迟，请稍后重试";
        if (response.getError() != null ) {
            if(StringUtils.isNotBlank(response.getError().getMessage())){
                msg = AmazonErrorUtils.getError(response.getError().getMessage());
            } else if(CollectionUtils.isNotEmpty(response.getError().getErrors())){
                msg = AmazonErrorUtils.getError(response.getError().getErrors().get(0).getErrorMessage());
            }

        }
        return ResultUtil.error(msg);
    }

    /**
     * SP否定关键词批量归档-组
     * @param shop
     * @param amazonAdProfile
     * @param amazonAdNeKeywords
     * @return
     */
    public Result<BatchResponseVo<BatchNeKeywordVo, AmazonAdKeyword>> batchArchive(ShopAuth shop, AmazonAdProfile amazonAdProfile, List<AmazonAdKeyword> amazonAdNeKeywords) {
        BatchResponseVo<BatchNeKeywordVo, AmazonAdKeyword> batchResponseVo = new BatchResponseVo<>();
        List<String> keywordIds = amazonAdNeKeywords.stream().map(AmazonAdKeyword::getKeywordId).collect(Collectors.toList());
        Map<String, AmazonAdKeyword> amazonSpAdNeKeywordMap = amazonAdNeKeywords.stream().collect(Collectors.toMap(AmazonAdKeyword::getKeywordId, e -> e));
        UpdateSpNegativeKeywordV3Response response = NegativeKeywordSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).delNegativeKeywords(shopAuthService.getAdToken(shop),
                amazonAdProfile.getProfileId(), shop.getMarketplaceId(), keywordIds,true);

        List<AmazonAdKeyword> successList = Lists.newArrayList();
        List<BatchNeKeywordVo> errorList  = Lists.newArrayList();

        if (response != null && response.getStatusCode() != null && response.getStatusCode() == 401) {
            // 刷新token重试一次
            shopAuthService.refreshCpcAuth(shop);
            response = NegativeKeywordSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).delNegativeKeywords(shopAuthService.getAdToken(shop),
                    amazonAdProfile.getProfileId(), shop.getMarketplaceId(), keywordIds,true);
        }

        if (response == null) {
            return ResultUtil.error("网络问题，请稍后重试");
        }

        if (response.getData() != null && response.getData().getNegativeKeywords() != null) {


            List<ErrorItemResultV3> errorItemResultV3s = response.getData().getNegativeKeywords().getError();
            List<NegativeKeywordSuccessResultV3> success = response.getData().getNegativeKeywords().getSuccess();
            for (NegativeKeywordSuccessResultV3 spNeKeywordResult : success) {
                AmazonAdKeyword amazonSpAdKeywordSuccess = amazonSpAdNeKeywordMap.remove(String.valueOf(spNeKeywordResult.getNegativeKeywordId()));
                if (amazonSpAdKeywordSuccess != null) {
                    amazonSpAdKeywordSuccess.setState(CpcStatusEnum.archived.name());
                    successList.add(amazonSpAdKeywordSuccess);
                }

            }
            for (ErrorItemResultV3 spNeKeywordResult : errorItemResultV3s) {
                AmazonAdKeyword amazonAdKeyword = amazonAdNeKeywords.get(spNeKeywordResult.getIndex());
                AmazonAdKeyword amazonSpAdKeywordFail = amazonSpAdNeKeywordMap.remove(amazonAdKeyword.getKeywordId());
                if (amazonSpAdKeywordFail != null) {
                    BatchNeKeywordVo spUpdateNeKeywordVoError = new BatchNeKeywordVo();
                    spUpdateNeKeywordVoError.setId(amazonSpAdKeywordFail.getId());
                    spUpdateNeKeywordVoError.setKeywordId(amazonSpAdKeywordFail.getKeywordId());
                    spUpdateNeKeywordVoError.setKeywordText(amazonSpAdKeywordFail.getKeywordText());

                    // 更新失败数据处理
                    if (StringUtils.isNotBlank(spNeKeywordResult.getErrors().get(0).getErrorMessage())) {
                        spUpdateNeKeywordVoError.setFailReason(AmazonErrorUtils.getError(spNeKeywordResult.getErrors().get(0).getErrorMessage()));
                    } else {
                        spUpdateNeKeywordVoError.setFailReason("更新失败，请稍后重试");
                    }
                    errorList.add(spUpdateNeKeywordVoError);
                }
            }

            // 剩余未匹配到的数据是接口未返回KeywordId的数据,一般都是发生了错误
            if (MapUtils.isNotEmpty(amazonSpAdNeKeywordMap)) {
                amazonSpAdNeKeywordMap.forEach((k, v) -> {
                    BatchNeKeywordVo spUpdateNeKeywordVoError = new BatchNeKeywordVo();
                    spUpdateNeKeywordVoError.setId(v.getId());
                    spUpdateNeKeywordVoError.setKeywordId(v.getKeywordId());
                    spUpdateNeKeywordVoError.setKeywordText(v.getKeywordText());
                    spUpdateNeKeywordVoError.setFailReason("更新失败，请稍后重试");
                    errorList.add(spUpdateNeKeywordVoError);
                });
            }
        } else if (response.getError() != null) {
            String msg = "网络异常，请稍后再试";
            if(StringUtils.isNotBlank(response.getError().getMessage())){
                msg = AmazonErrorUtils.getError(response.getError().getMessage());
            } else if(CollectionUtils.isNotEmpty(response.getError().getErrors())){
                msg = AmazonErrorUtils.getError(response.getError().getErrors().get(0).getErrorMessage());
            }
            return ResultUtil.error(msg);
        } else {
            // 所有数据没有执行成功
            if (MapUtils.isNotEmpty(amazonSpAdNeKeywordMap)) {
                amazonSpAdNeKeywordMap.forEach((k, v) -> {
                    BatchNeKeywordVo spUpdateNeKeywordVoError = new BatchNeKeywordVo();
                    spUpdateNeKeywordVoError.setId(v.getId());
                    spUpdateNeKeywordVoError.setKeywordId(v.getKeywordId());
                    spUpdateNeKeywordVoError.setKeywordText(v.getKeywordText());
                    spUpdateNeKeywordVoError.setFailReason("更新失败，请稍后重试");
                    errorList.add(spUpdateNeKeywordVoError);
                });
            }
        }
        batchResponseVo.setCountNum(amazonAdNeKeywords.size());
        batchResponseVo.setFailNum(errorList.size());
        batchResponseVo.setErrorList(errorList);
        batchResponseVo.setSuccessNum(successList.size());
        batchResponseVo.setSuccessList(successList);
        return ResultUtil.success(batchResponseVo);
    }

    /**
     * 创建广告活动否定关键词
     *
     * @param amazonAdKeywords：
     * @return ：Result
     */
    Result createCampaignNegativeKeyword(List<SpNeKeywordsVo> amazonAdKeywords) {
        Result result = ResultUtil.success();
        if (CollectionUtils.isEmpty(amazonAdKeywords)) {
            return ResultUtil.error("请求参数错误");
        }

        SpNeKeywordsVo one = amazonAdKeywords.get(0);

        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(one.getShopId(), one.getPuid());
        if (shop == null) {
            return ResultUtil.error("店铺不存在");
        }

        List<CampaignNegativeKeywordEntityV3> keywords = makeCampaignNegativeKeyword(amazonAdKeywords);
        boolean hasSucc = false;
        StringBuilder error = new StringBuilder();
        String msg = "网络延迟，请稍后重试";

        for (List<CampaignNegativeKeywordEntityV3> neList : Lists.partition(keywords, DEFAULT_REQUEST_SIZE)) {
            //分片调用接口
            CreateSpCampaignNegativeKeywordV3Response response = CampaignNegativeKeywordSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).createCampaignNegativeKeyword(shopAuthService.getAdToken(shop),
                    one.getProfileId(), one.getMarketplaceId(), neList,true);
            if (response != null
                    && response.getStatusCode() != null
                    && response.getStatusCode() == 401) {
                // 刷新token重试一次
                shopAuthService.refreshCpcAuth(shop);
                response = CampaignNegativeKeywordSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).createCampaignNegativeKeyword(shopAuthService.getAdToken(shop),
                        one.getProfileId(), one.getMarketplaceId(), neList,true);
            }
            if (response == null) {
                return ResultUtil.error("网络延迟，请稍后重试");
            }

            //处理返回结果中的错误信息
            if (response.getData() != null && response.getData().getCampaignNegativeKeywords() != null) {
                ApiResponseV3<CampaignNegativeKeywordSuccessResultV3> campaignNegativeKeywords = response.getData().getCampaignNegativeKeywords();
                List<CampaignNegativeKeywordSuccessResultV3> success = campaignNegativeKeywords.getSuccess();
                List<ErrorItemResultV3> campaignNegativeKeywordsError = campaignNegativeKeywords.getError();
                result.setData(campaignNegativeKeywordsError);
                for (CampaignNegativeKeywordSuccessResultV3 keywordResult :  success){
                    amazonAdKeywords.get(keywordResult.getIndex()).setKeywordId(keywordResult.getCampaignNegativeKeywordId());
                    hasSucc = true;
                }
                for (ErrorItemResultV3 keywordResult : campaignNegativeKeywordsError){
                    error.append("targetValue:").append(amazonAdKeywords.get(keywordResult.getIndex()).getKeywordText()).append(",desc:").append(AmazonErrorUtils.getError(keywordResult.getErrors().get(0).getErrorMessage())).append(";");
                }
            }
            if (response.getError() != null ) {
                if(StringUtils.isNotBlank(response.getError().getMessage())){
                    msg = AmazonErrorUtils.getError(response.getError().getMessage());
                } else if(CollectionUtils.isNotEmpty(response.getError().getErrors())){
                    msg = AmazonErrorUtils.getError(response.getError().getErrors().get(0).getErrorMessage());
                }
            }
        }
        if (hasSucc) {
            result.setMsg(error.toString());
            return result;
        } else if (StringUtils.isNotEmpty(error.toString())) {
            return ResultUtil.error(error.toString());
        }

        return ResultUtil.error(msg);
    }

    public Result archiveCampaignNegativeKeyword(AmazonAdCampaignNeKeywords neKeywords) {
        if (neKeywords == null) {
            return ResultUtil.error("请求参数错误");
        }

        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(neKeywords.getShopId(), neKeywords.getPuid());
        if (shop == null) {
            return ResultUtil.error("店铺不存在");
        }

        UpdateSpCampaignNegativeKeywordV3Response response = CampaignNegativeKeywordSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).delCampaignNegativeKeyword(shopAuthService.getAdToken(shop),
                neKeywords.getProfileId(), shop.getMarketplaceId(),Lists.newArrayList(neKeywords.getKeywordId()),true);
        if (response != null
                && response.getStatusCode() != null
                && response.getStatusCode() == 401) {
            // 刷新token重试一次
            shopAuthService.refreshCpcAuth(shop);
            response = CampaignNegativeKeywordSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).delCampaignNegativeKeyword(shopAuthService.getAdToken(shop),
                    neKeywords.getProfileId(), shop.getMarketplaceId(),Lists.newArrayList(neKeywords.getKeywordId()),true);
        }
        if (response == null) {
            return ResultUtil.error("网络延迟，请稍后重试");
        }

        if (response.getData() != null && response.getData().getCampaignNegativeKeywords() != null && CollectionUtils.isNotEmpty(response.getData().getCampaignNegativeKeywords().getSuccess())) {
            return ResultUtil.success();
        }

        //处理返回结果中的错误信息
        String msg = "网络延迟，请稍后重试";
        if (response.getData() != null && CollectionUtils.isNotEmpty(response.getData().getCampaignNegativeKeywords().getError())) {
            msg = AmazonErrorUtils.getError(response.getData().getCampaignNegativeKeywords().getError().get(0).getErrors().get(0).getErrorMessage());
        }
        return ResultUtil.error(msg);
    }


    public Result<BatchResponseVo<BatchNeKeywordVo, AmazonAdCampaignNeKeywords>> batchArchiveCampaignNegativeKeyword(ShopAuth shop, AmazonAdProfile amazonAdProfile, List<AmazonAdCampaignNeKeywords> amazonAdCampaignNeKeywords) {
        BatchResponseVo<BatchNeKeywordVo, AmazonAdCampaignNeKeywords> batchResponseVo = new BatchResponseVo<>();
        Map<String, AmazonAdCampaignNeKeywords> amazonSpAdCampaignNeKeywordsMap = amazonAdCampaignNeKeywords.stream().collect(Collectors.toMap(AmazonAdCampaignNeKeywords::getKeywordId, e -> e));
        List<String> keywordIdList = amazonAdCampaignNeKeywords.stream().map(AmazonAdCampaignNeKeywords::getKeywordId).collect(Collectors.toList());
        UpdateSpCampaignNegativeKeywordV3Response response = CampaignNegativeKeywordSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).delCampaignNegativeKeyword(shopAuthService.getAdToken(shop),
                amazonAdProfile.getProfileId(), shop.getMarketplaceId(), keywordIdList,Boolean.FALSE);

        if (response != null && response.getStatusCode() != null && response.getStatusCode() == 401) {
            // 刷新token重试一次
            shopAuthService.refreshCpcAuth(shop);
            response =CampaignNegativeKeywordSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).delCampaignNegativeKeyword(shopAuthService.getAdToken(shop),
                    amazonAdProfile.getProfileId(), shop.getMarketplaceId(), keywordIdList,Boolean.FALSE);
        }

        List<AmazonAdCampaignNeKeywords> successList = Lists.newArrayList();
        List<BatchNeKeywordVo> errorList = Lists.newArrayList();

        if (response == null) {
            return ResultUtil.error("网络问题，请稍后重试");
        }

        if (response.getData() != null && response.getData().getCampaignNegativeKeywords() != null ) {
            List<CampaignNegativeKeywordSuccessResultV3> success = response.getData().getCampaignNegativeKeywords().getSuccess();
            List<ErrorItemResultV3> error = response.getData().getCampaignNegativeKeywords().getError();

            for(CampaignNegativeKeywordSuccessResultV3 spCampaignKeywordResult: success) {
                AmazonAdCampaignNeKeywords amazonAdCampaignNeKeywordSuccess = amazonSpAdCampaignNeKeywordsMap.remove(spCampaignKeywordResult.getCampaignNegativeKeywordId());

                if (amazonAdCampaignNeKeywordSuccess != null) {
                    amazonAdCampaignNeKeywordSuccess.setState(CpcStatusEnum.archived.name());
                    successList.add(amazonAdCampaignNeKeywordSuccess);
                }
            }
            for (ErrorItemResultV3 spCampaignKeywordResult :error){
                AmazonAdCampaignNeKeywords neKeywords = amazonAdCampaignNeKeywords.get(spCampaignKeywordResult.getIndex());
                AmazonAdCampaignNeKeywords amazonAdCampaignNeKeywordFail = amazonSpAdCampaignNeKeywordsMap.remove(neKeywords.getKeywordId());
                if (amazonAdCampaignNeKeywordFail != null) {
                    BatchNeKeywordVo spUpdateNeKeywordVoError = new BatchNeKeywordVo();
                    spUpdateNeKeywordVoError.setId(Long.valueOf(amazonAdCampaignNeKeywordFail.getId()));
                    spUpdateNeKeywordVoError.setCampaignId(amazonAdCampaignNeKeywordFail.getCampaignId());
                    spUpdateNeKeywordVoError.setKeywordId(amazonAdCampaignNeKeywordFail.getKeywordId());
                    spUpdateNeKeywordVoError.setKeywordText(amazonAdCampaignNeKeywordFail.getKeywordText());

                    // 更新失败数据处理
                    if (CollectionUtils.isNotEmpty(spCampaignKeywordResult.getErrors())) {
                        spUpdateNeKeywordVoError.setFailReason(AmazonErrorUtils.getError(spCampaignKeywordResult.getErrors().get(0).getErrorMessage()));
                    } else {
                        spUpdateNeKeywordVoError.setFailReason("更新失败，请稍后重试");
                    }
                    errorList.add(spUpdateNeKeywordVoError);
                }
            }

            // 剩余未匹配到的数据是接口为返回keywordId的数据，一般都是发生了错误
            if (MapUtils.isNotEmpty(amazonSpAdCampaignNeKeywordsMap)) {
                amazonSpAdCampaignNeKeywordsMap.forEach((k, v) -> {
                    BatchNeKeywordVo spUpdateNeKeywordVoError = new BatchNeKeywordVo();
                    spUpdateNeKeywordVoError.setId(Long.valueOf(v.getId()));
                    spUpdateNeKeywordVoError.setCampaignId(v.getCampaignId());
                    spUpdateNeKeywordVoError.setKeywordId(v.getKeywordId());
                    spUpdateNeKeywordVoError.setKeywordText(v.getKeywordText());
                    spUpdateNeKeywordVoError.setFailReason("更新失败，请稍后重试");
                    errorList.add(spUpdateNeKeywordVoError);
                });
            }
        } else if (response.getError() != null ) {
            String msg = "网络异常，请稍后再试";
            if(StringUtils.isNotBlank(response.getError().getMessage())){
                msg = AmazonErrorUtils.getError(response.getError().getMessage());
            } else if(CollectionUtils.isNotEmpty(response.getError().getErrors())){
                msg = AmazonErrorUtils.getError(response.getError().getErrors().get(0).getErrorMessage());
            }
            return ResultUtil.error(msg);
        } else {
            // 所有数据执行失败
            if (MapUtils.isNotEmpty(amazonSpAdCampaignNeKeywordsMap)) {
                amazonSpAdCampaignNeKeywordsMap.forEach((k, v) -> {
                    BatchNeKeywordVo spUpdateNeKeywordVoError = new BatchNeKeywordVo();
                    spUpdateNeKeywordVoError.setId(Long.valueOf(v.getId()));
                    spUpdateNeKeywordVoError.setCampaignId(v.getCampaignId());
                    spUpdateNeKeywordVoError.setKeywordId(v.getKeywordId());
                    spUpdateNeKeywordVoError.setKeywordText(v.getKeywordText());
                    spUpdateNeKeywordVoError.setFailReason("更新失败，请稍后重试");
                    errorList.add(spUpdateNeKeywordVoError);
                });
            }
        }
        batchResponseVo.setCountNum(amazonAdCampaignNeKeywords.size());
        batchResponseVo.setFailNum(errorList.size());
        batchResponseVo.setErrorList(errorList);
        batchResponseVo.setSuccessNum(successList.size());
        batchResponseVo.setSuccessList(successList);
        return ResultUtil.success(batchResponseVo);
    }


    private List<Keyword> makeKeywords(List<AmazonAdKeyword> amazonAdKeywordList) {
        List<Keyword> list = Lists.newArrayListWithCapacity(amazonAdKeywordList.size());
        Keyword keyword;
        for (AmazonAdKeyword amazonAdKeyword : amazonAdKeywordList) {
            keyword = new Keyword();
            if (StringUtils.isNotBlank(amazonAdKeyword.getKeywordId())) {
                keyword.setKeywordId(Long.valueOf(amazonAdKeyword.getKeywordId()));
            }
            if (StringUtils.isNotBlank(amazonAdKeyword.getCampaignId())) {
                keyword.setCampaignId(Long.valueOf(amazonAdKeyword.getCampaignId()));
            }
            if (StringUtils.isNotBlank(amazonAdKeyword.getAdGroupId())) {
                keyword.setAdGroupId(Long.valueOf(amazonAdKeyword.getAdGroupId()));
            }
            keyword.setKeywordText(amazonAdKeyword.getKeywordText());
            keyword.setMatchType(amazonAdKeyword.getMatchType());
            keyword.setState(amazonAdKeyword.getState());
            keyword.setBid(amazonAdKeyword.getBid());
            list.add(keyword);
        }
        return list;
    }

    private List<CampaignNegativeKeywordEntityV3> makeCampaignNegativeKeyword(List<SpNeKeywordsVo> amazonAdKeywordList) {
        List<CampaignNegativeKeywordEntityV3> list = Lists.newArrayListWithCapacity(amazonAdKeywordList.size());
        CampaignNegativeKeywordEntityV3 keyword;
        for (SpNeKeywordsVo amazonAdKeyword : amazonAdKeywordList) {
            keyword = new CampaignNegativeKeywordEntityV3();
            if (StringUtils.isNotBlank(amazonAdKeyword.getKeywordId())) {
                keyword.setKeywordId(amazonAdKeyword.getKeywordId());
            }
            if (StringUtils.isNotBlank(amazonAdKeyword.getCampaignId())) {
                keyword.setCampaignId(amazonAdKeyword.getCampaignId());
            }
            keyword.setKeywordText(amazonAdKeyword.getKeywordText());
            keyword.setMatchType(SpV3NegativeMatchTypeEnum.getSpV3NegativeMatchTypeEnumByValue(amazonAdKeyword.getMatchType()).valueV3());
            keyword.setState(SpV3StateEnum.ENABLED.valueV3());
            list.add(keyword);
        }
        return list;
    }

    public List<CampaignNegativeKeywordEntityV3> makeCampaignNegativeKeywordList(List<AmazonAdKeyword> amazonAdKeywordList) {
        List<CampaignNegativeKeywordEntityV3> list = Lists.newArrayListWithCapacity(amazonAdKeywordList.size());
        CampaignNegativeKeywordEntityV3 keyword;
        for (AmazonAdKeyword amazonAdKeyword : amazonAdKeywordList) {
            keyword = new CampaignNegativeKeywordEntityV3();
            if (StringUtils.isNotBlank(amazonAdKeyword.getKeywordId())) {
                keyword.setKeywordId(amazonAdKeyword.getKeywordId());
            }
            if (StringUtils.isNotBlank(amazonAdKeyword.getCampaignId())) {
                keyword.setCampaignId(amazonAdKeyword.getCampaignId());
            }
            keyword.setKeywordText(amazonAdKeyword.getKeywordText());
            keyword.setMatchType(SpV3NegativeMatchTypeEnum.getSpV3NegativeMatchTypeEnumByValue(amazonAdKeyword.getMatchType()).valueV3());
            keyword.setState(SpV3StateEnum.ENABLED.valueV3());
            list.add(keyword);
        }
        return list;
    }

    // 广告否定关键词转亚马逊关键词对象
    private List<Keyword> toNeKeywords(List<AmazonAdKeyword> amazonAdNeKeywords) {
        List<Keyword> list = Lists.newArrayListWithCapacity(amazonAdNeKeywords.size());
        Keyword keyword;
        for (AmazonAdKeyword spAdNeKeyword : amazonAdNeKeywords) {
            keyword = new Keyword();
            if (StringUtils.isNotBlank(spAdNeKeyword.getAdGroupId())) {
                keyword.setAdGroupId(Long.valueOf(spAdNeKeyword.getAdGroupId()));
            }

            if (StringUtils.isNotBlank(spAdNeKeyword.getCampaignId())) {
                keyword.setCampaignId(Long.valueOf(spAdNeKeyword.getCampaignId()));
            }

            keyword.setKeywordText(spAdNeKeyword.getKeywordText());
            keyword.setMatchType(spAdNeKeyword.getMatchType());
            keyword.setState(CpcStatusEnum.archived.name());
            keyword.setKeywordId(Long.valueOf(spAdNeKeyword.getKeywordId()));
            list.add(keyword);
        }
        return list;
    }

    // 广告活动否定关键词转亚马逊关键词对象
    private List<Keyword> toCampaignNeKeywords(List<AmazonAdCampaignNeKeywords> amazonAdCampaignNeKeywords) {
        List<Keyword> list = Lists.newArrayListWithCapacity(amazonAdCampaignNeKeywords.size());
        Keyword keyword;
        for (AmazonAdCampaignNeKeywords spAdCampaignNeKeyword : amazonAdCampaignNeKeywords) {
            keyword = new Keyword();
            if (StringUtils.isNotBlank(spAdCampaignNeKeyword.getCampaignId())) {
                keyword.setCampaignId(Long.valueOf(spAdCampaignNeKeyword.getCampaignId()));
            }
            keyword.setKeywordText(spAdCampaignNeKeyword.getKeywordText());
            keyword.setMatchType(spAdCampaignNeKeyword.getMatchType());
            keyword.setState(CpcStatusEnum.deleted.name());
            keyword.setKeywordId(Long.valueOf(spAdCampaignNeKeyword.getKeywordId()));
            list.add(keyword);
        }
        return list;
    }

    private AmazonAdKeyword turnToPO(NegativeKeywordExtendEntityV3 keyword) {
        AmazonAdKeyword amazonAdKeyword = new AmazonAdKeyword();
        if (keyword.getCampaignId() != null) {
            amazonAdKeyword.setCampaignId(keyword.getCampaignId());
        }
        if (keyword.getAdGroupId() != null) {
            amazonAdKeyword.setAdGroupId(keyword.getAdGroupId());
        }
        if (keyword.getKeywordId() != null) {
            amazonAdKeyword.setKeywordId(keyword.getKeywordId());
        }
        amazonAdKeyword.setKeywordText(keyword.getKeywordText());
        SpV3NegativeMatchTypeEnum spV3NegativeMatchTypeEnumByValueV3 = SpV3NegativeMatchTypeEnum.getSpV3NegativeMatchTypeEnumByValueV3(keyword.getMatchType());
        amazonAdKeyword.setMatchType(spV3NegativeMatchTypeEnumByValueV3 == null ? keyword.getMatchType() : spV3NegativeMatchTypeEnumByValueV3.value());
        amazonAdKeyword.setState(keyword.getState().toLowerCase());
        amazonAdKeyword.setServingStatus(keyword.getExtendedData().getServingStatus());
        amazonAdKeyword.setType(Constants.NEGATIVE);
        //平台创建时间
        if (keyword.getExtendedData().getCreationDateTime() != null) {
            amazonAdKeyword.setCreationDate(LocalDateTimeUtil.convertSiteTimeToChina(keyword.getExtendedData().getCreationDateTime()));
        }
        return amazonAdKeyword;
    }

    private AmazonAdCampaignNeKeywords turnToCampaignNePO(CampaignNegativeKeywordExtendEntityV3 keyword) {
        AmazonAdCampaignNeKeywords neKeywords = new AmazonAdCampaignNeKeywords();
        if (keyword.getCampaignId() != null) {
            neKeywords.setCampaignId(keyword.getCampaignId());
        }
        if (keyword.getKeywordId() != null) {
            neKeywords.setKeywordId(keyword.getKeywordId());
        }
        neKeywords.setKeywordText(keyword.getKeywordText());
        SpV3NegativeMatchTypeEnum spV3NegativeMatchTypeEnumByValueV3 = SpV3NegativeMatchTypeEnum.getSpV3NegativeMatchTypeEnumByValueV3(keyword.getMatchType());
        neKeywords.setMatchType(spV3NegativeMatchTypeEnumByValueV3 == null ? keyword.getMatchType() :spV3NegativeMatchTypeEnumByValueV3.value());
        neKeywords.setState(keyword.getState().toLowerCase());
        neKeywords.setServingStatus(keyword.getExtendedData().getServingStatus());
        //平台创建时间
        if (keyword.getExtendedData().getCreationDateTime() != null) {
            neKeywords.setCreationDate(LocalDateTimeUtil.convertSiteTimeToChina(keyword.getExtendedData().getCreationDateTime()));
        }
        return neKeywords;
    }


    /**
     * 创建否定关键词
     *
     * @param amazonAdKeywords：
     * @return ：Result
     */
    Result createNegativeKeywordV3(List<AmazonAdKeyword> amazonAdKeywords) {
        if (CollectionUtils.isEmpty(amazonAdKeywords)) {
            return ResultUtil.error("请求参数错误");
        }

        AmazonAdKeyword one = amazonAdKeywords.get(0);

        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(one.getShopId(), one.getPuid());
        if (shop == null) {
            return ResultUtil.error("店铺不存在");
        }

        List<CreateNegativeKeywordEntityV3> keywords = makeCreateNegativeKeywordsV3(amazonAdKeywords);
        CreateSpNegativeKeywordV3Response response = NegativeKeywordSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).createNegativeKeywords(shopAuthService.getAdToken(shop),
                one.getProfileId(), one.getMarketplaceId(), keywords, Boolean.TRUE);
        if (response != null
                && response.getStatusCode() != null
                && response.getStatusCode() == 401) {
            // 刷新token重试一次
            shopAuthService.refreshCpcAuth(shop);
            response = NegativeKeywordSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).createNegativeKeywords(shopAuthService.getAdToken(shop),
                    one.getProfileId(), one.getMarketplaceId(), keywords, Boolean.TRUE);
        }
        if (response == null) {
            return ResultUtil.error("网络延迟，请稍后重试");
        }

        //处理返回结果中的错误信息
        if (response.getData() != null) {
            boolean hasSucc = false;
            StringBuilder error = new StringBuilder();
            NegativeKeywordApiResponseV3 data = response.getData();
            if(CollectionUtils.isNotEmpty(data.getNegativeKeywords().getSuccess())){
                for (NegativeKeywordSuccessResultV3 successResultV3:data.getNegativeKeywords().getSuccess()){
                    AmazonAdKeyword amazonAdKeyword = amazonAdKeywords.get(successResultV3.getIndex());
                    amazonAdKeyword.setKeywordId(successResultV3.getNegativeKeywordId());
                    LocalDateTime creationDate = LocalDateTimeUtil.convertDateToLDT(DateUtil.stringToDate(now()));
                    amazonAdKeyword.setCreationDate(creationDate);
                }

                hasSucc = true;
            }

            if (CollectionUtils.isNotEmpty(data.getNegativeKeywords().getError())){
                for (ErrorItemResultV3 errorItemResultV3 : data.getNegativeKeywords().getError()){
                    error.append("targetValue:").append(amazonAdKeywords.get(errorItemResultV3.getIndex()).getKeywordText()).append(",desc:").append(AmazonErrorUtils.getError(errorItemResultV3.getErrors().get(0).getErrorMessage())).append(";");
                    AmazonAdKeyword amazonAdKeyword = amazonAdKeywords.get(errorItemResultV3.getIndex());
                    amazonAdKeyword.setError(AmazonErrorUtils.getError(errorItemResultV3.getErrors().get(0).getErrorMessage()));
                }
            }
            if (hasSucc) {
                return ResultUtil.success(error.toString());
            }

            return ResultUtil.error(error.toString());
        }

        String msg = "网络延迟，请稍后重试";
        if (response.getError() != null) {
            msg = AmazonErrorUtils.getError(response.getError().getMessage());
        }
        return ResultUtil.error(msg);

    }

    public List<CreateNegativeKeywordEntityV3> makeCreateNegativeKeywordsV3(List<AmazonAdKeyword> amazonAdKeywordList) {
        List<CreateNegativeKeywordEntityV3> list = Lists.newArrayListWithCapacity(amazonAdKeywordList.size());
        CreateNegativeKeywordEntityV3 keyword;
        for (AmazonAdKeyword amazonAdKeyword : amazonAdKeywordList) {
            keyword = new CreateNegativeKeywordEntityV3();
            keyword.setCampaignId(amazonAdKeyword.getCampaignId());
            keyword.setAdGroupId(amazonAdKeyword.getAdGroupId());
            keyword.setKeywordText(amazonAdKeyword.getKeywordText());
            keyword.setMatchType(SpV3NegativeMatchTypeEnum.getSpV3NegativeMatchTypeEnumByValue(amazonAdKeyword.getMatchType()).valueV3());
            keyword.setState(SpV3StateEnum.getSpV3StateEnumByValue(amazonAdKeyword.getState()).valueV3());
            list.add(keyword);
        }
        return list;
    }


    private List<NegativeKeywordEntityV3> makePutNegativeKeywordsV3(List<AmazonAdKeyword> amazonAdKeywordList) {
        List<NegativeKeywordEntityV3> list = Lists.newArrayListWithCapacity(amazonAdKeywordList.size());
        NegativeKeywordEntityV3 keyword;
        for (AmazonAdKeyword amazonAdKeyword : amazonAdKeywordList) {
            keyword = new NegativeKeywordEntityV3();
            if (StringUtils.isNotBlank(amazonAdKeyword.getKeywordId())) {
                keyword.setKeywordId(amazonAdKeyword.getKeywordId());
            }
            if (StringUtils.isNotBlank(amazonAdKeyword.getCampaignId())) {
                keyword.setCampaignId(amazonAdKeyword.getCampaignId());
            }
            if (StringUtils.isNotBlank(amazonAdKeyword.getAdGroupId())) {
                keyword.setAdGroupId(amazonAdKeyword.getAdGroupId());
            }

            keyword.setState(amazonAdKeyword.getState());
            list.add(keyword);
        }
        return list;
    }

}
