package com.meiyunji.sponsored.service.reportHour.service;

import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.cpc.vo.KeywordHourParam;
import com.meiyunji.sponsored.service.cpc.vo.TargetHourParam;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.qo.TargetViewHourParam;
import com.meiyunji.sponsored.service.reportHour.vo.AdKeywordAndTargetHourVo;
import com.meiyunji.sponsored.service.reportHour.vo.AdKeywordTargetHourOfAdVo;
import com.meiyunji.sponsored.service.reportHour.vo.AdKeywordTargetHourOfPlacementVo;

import java.util.List;

public interface IAmazonAdTargetHourReportService {

    /**
     * 查询投放小时数据
     *
     * @param puid
     * @param param
     * @return
     */
    List<AdKeywordAndTargetHourVo> getList(int puid, TargetHourParam param);

    /**
     * 查询投放竞价小时级数据
     *
     * @param puid
     * @param param
     * @return
     */
    List<AdKeywordAndTargetHourVo> getBidList(int puid, TargetHourParam param);

    /**
     * 查询投放月数据
     *
     * @param puid
     * @param adType
     * @param param
     * @return
     */
    List<AdKeywordAndTargetHourVo> getMonthlyList(int puid, String adType, TargetHourParam param, List<AdKeywordAndTargetHourVo> compares);

    /**
     * 查询投放周数据
     *
     * @param puid
     * @param adType
     * @param param
     * @return
     */
    List<AdKeywordAndTargetHourVo> getWeeklyList(int puid, String adType, TargetHourParam param, List<AdKeywordAndTargetHourVo> compares);

    /**
     * 查询投放天数据
     *
     * @param puid
     * @param adType
     * @param param
     * @return
     */
    List<AdKeywordAndTargetHourVo> getDailyList(int puid, String adType, TargetHourParam param, List<AdKeywordAndTargetHourVo> compares);

    /**
     * 查询产品维度投放小时数据
     *
     * @param puid
     * @param param
     * @return
     */
    List<AdKeywordTargetHourOfAdVo> getListOfAd(int puid, TargetHourParam param);

    /**
     * 查询产品维度投放小时明细数据
     *
     * @param puid
     * @param param
     * @return
     */
    List<AdKeywordAndTargetHourVo> getDetailListOfAd(int puid, TargetHourParam param);

    /**
     * 查询广告位维度投放小时数据
     *
     * @param puid
     * @param param
     * @return
     */
    List<AdKeywordTargetHourOfPlacementVo> getListOfPlacement(int puid, TargetHourParam param);

    /**
     * 查询广告位维度投放小时明细数据
     *
     * @param puid
     * @param param
     * @return
     */
    List<AdKeywordAndTargetHourVo> getDetailListOfPlacement(int puid, TargetHourParam param);

    List<AdKeywordAndTargetHourVo> getAllList(int puid, List<ShopAuth> shopAuths, TargetHourParam param);

    List<AdKeywordAndTargetHourVo> getDailyAllList(int puid, List<ShopAuth> shopAuths, String adType, TargetHourParam param, List<AdKeywordAndTargetHourVo> compares);

    List<AdKeywordAndTargetHourVo> getWeeklyAllList(int puid, List<ShopAuth> shopAuths, String adType, TargetHourParam param, List<AdKeywordAndTargetHourVo> compares);

    List<AdKeywordAndTargetHourVo> getMonthlyAllList(int puid, List<ShopAuth> shopAuths, String adType, TargetHourParam param, List<AdKeywordAndTargetHourVo> compares);

    List<AdKeywordTargetHourOfPlacementVo> getListOfPlacementAll(int puid, List<ShopAuth> shopAuths, TargetHourParam param);

    List<AdKeywordAndTargetHourVo> getDetailListOfPlacementAll(int puid, List<ShopAuth> shopAuths, TargetHourParam param);

    List<AdKeywordAndTargetHourVo> getViewHourListAll(int puid, TargetViewHourParam param);

    List<AdKeywordAndTargetHourVo> getViewHourListAllType(int puid, TargetViewHourParam param);

    List<AdKeywordAndTargetHourVo> getSbAndSdHourList(int puid, TargetHourParam param);

    List<AdKeywordAndTargetHourVo> getSbHourAllList(int puid, List<ShopAuth> shopAuths, TargetHourParam param);

    List<AdKeywordAndTargetHourVo> getSdHourAllList(int puid, List<ShopAuth> shopAuths, TargetHourParam param);

}
