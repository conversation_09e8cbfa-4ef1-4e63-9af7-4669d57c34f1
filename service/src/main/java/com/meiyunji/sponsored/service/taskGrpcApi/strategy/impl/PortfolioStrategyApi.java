package com.meiyunji.sponsored.service.taskGrpcApi.strategy.impl;

import com.meiyunji.sellfox.aadas.api.enumeration.PolicyTypePb;
import com.meiyunji.sellfox.aadas.api.enumeration.ScheduleModePb;
import com.meiyunji.sellfox.aadas.api.service.*;
import com.meiyunji.sellfox.aadas.types.enumeration.Marketplace;
import com.meiyunji.sellfox.aadas.types.enumeration.TaskTimeType;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.account.service.impl.ShopAuthServiceImpl;
import com.meiyunji.sponsored.service.config.DynamicRefreshConfiguration;
import com.meiyunji.sponsored.service.strategy.dao.AdvertiseStrategyTemplateDao;
import com.meiyunji.sponsored.service.strategy.enums.AdStrategyEnableStatusEnum;
import com.meiyunji.sponsored.service.strategy.po.AdvertiseStrategySchedule;
import com.meiyunji.sponsored.service.strategy.po.AdvertiseStrategyTemplate;
import com.meiyunji.sponsored.service.taskGrpcApi.AbstractAdvertiseStrategyApi;
import com.meiyunji.sponsored.service.util.GrpcExceptionUtil;
import com.meiyunji.sponsored.service.util.PbUtil;
import com.meiyunji.sponsored.service.util.ProtoBufUtil;
import io.grpc.ManagedChannel;
import io.grpc.StatusRuntimeException;
import lombok.extern.slf4j.Slf4j;
import org.apache.pulsar.client.api.Producer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

@Component
@Slf4j
public class PortfolioStrategyApi extends AbstractAdvertiseStrategyApi {

    @Autowired
    private AdvertiseStrategyTemplateDao advertiseStrategyTemplateDao;

    protected PortfolioStrategyApi(ShopAuthServiceImpl shopAuthService, ManagedChannel taskManagedChannel, Producer<byte[]> compensationTaskProducer, DynamicRefreshConfiguration dynamicRefreshConfiguration) {
        super(shopAuthService, taskManagedChannel, compensationTaskProducer, dynamicRefreshConfiguration);
    }

    @Override
    public boolean checkValid(TaskTimeType taskType) {
        return taskType == TaskTimeType.portfolioAmount;
    }

    @Override
    public void setSchedule(Long taskId, Long templateId, List<AdvertiseStrategySchedule> strategySchedule, boolean triggerNow) throws Exception {
        AdvertiseStrategySchedule schedule = strategySchedule.get(0);
        ShopAuth shopAuth = shopAuthService.getByIdAndPuid(schedule.getShopId(), schedule.getPuid());
        SetCampaignPortfolioTaskRequestPb.SetCampaignPortfolioTaskRequest request = builderRequest(taskId, shopAuth, strategySchedule, triggerNow, templateId);
        try {
            log.info("新版广告策略-广告组合请求参数: {}", request);
            getStub().setCampaignPortfolioTask(request);
        } catch (StatusRuntimeException e) {
            throw GrpcExceptionUtil.unWrapException(e);
        } finally {
            sendCompensationMessage(taskId, getTaskTimeType(), shopAuth, 5);
        }
    }

    @Override
    public void removeSchedule(Integer puid, Integer shopId, Long taskId, boolean triggerNow) throws Exception {
        if (triggerNow) {
            AdvertiseStrategyTemplate template = advertiseStrategyTemplateDao.getTemplateByTaskId(puid, shopId, taskId);
            if (Objects.isNull(template) || AdStrategyEnableStatusEnum.DISABLED.getCode().equals(template.getStatus())) {
                triggerNow = false;
                log.info("分时策略拦截回调, puid: {}, shopId: {}, taskId: {}", puid, shopId, taskId);
            }
        }
        ShopAuth shopAuth = shopAuthService.getByIdAndPuid(shopId, puid);
        try {
            RemoveCampaignPortfolioTaskRequestPb.RemoveCampaignPortfolioTaskRequest.Builder builder =
                    RemoveCampaignPortfolioTaskRequestPb.RemoveCampaignPortfolioTaskRequest.newBuilder();
            builder.setSellerId(shopAuth.getSellingPartnerId());
            builder.setMarketplace(PbUtil.toPb(Marketplace.fromId(shopAuth.getMarketplaceId())));
            builder.setTaskId(taskId);
            builder.setTriggerNow(triggerNow);
            getStub().removeCampaignPortfolioTask(builder.build());
        } finally {
            sendCompensationMessage(taskId, getTaskTimeType(), shopAuth, 5);
        }
    }



    @Override
    public String builderScheduleRequestJson(Long taskId, List<AdvertiseStrategySchedule> strategySchedule, boolean triggerNow, Long templateId) throws Exception {
        AdvertiseStrategySchedule schedule = strategySchedule.get(0);
        ShopAuth shopAuth = shopAuthService.getByIdAndPuid(schedule.getShopId(), schedule.getPuid());
        return ProtoBufUtil.toJsonStr(builderRequest(taskId, shopAuth, strategySchedule, triggerNow, templateId));
    }

    @Override
    public TaskTimeType getTaskTimeType() {
        return TaskTimeType.portfolioAmount;
    }


    public SetCampaignPortfolioTaskRequestPb.SetCampaignPortfolioTaskRequest builderRequest(Long taskId, ShopAuth shopAuth, List<AdvertiseStrategySchedule> strategySchedule, boolean triggerNow, Long templateId){
        AdvertiseStrategySchedule schedule = strategySchedule.get(0);
        ScheduleModePb.ScheduleMode scheduleMode = null;
        if ("MONTH_DAY".equals(schedule.getType())) {
            scheduleMode = ScheduleModePb.ScheduleMode.Monthly;
        } else if ("WEEKLY".equals(schedule.getType())) {
            scheduleMode = ScheduleModePb.ScheduleMode.Weekly;
        } else if ("DAILY".equals(schedule.getType())) {
            scheduleMode = ScheduleModePb.ScheduleMode.Daily;
        }

        SetCampaignPortfolioTaskRequestPb.SetCampaignPortfolioTaskRequest.Builder list =
                SetCampaignPortfolioTaskRequestPb.SetCampaignPortfolioTaskRequest.newBuilder();
        SetCampaignPortfolioTaskRequestPb.CampaignPortfolio.Builder builder = SetCampaignPortfolioTaskRequestPb.CampaignPortfolio.newBuilder();
        builder.setSellerId(shopAuth.getSellingPartnerId());
        builder.setMarketplace(PbUtil.toPb(Marketplace.fromId(shopAuth.getMarketplaceId())));
        builder.setTaskId(taskId);
        builder.setSchedulerMode(scheduleMode);
        builder.setTriggerNow(triggerNow);
        builder.setPortfolioId(schedule.getItemId());
        if ("noBudget".equals(schedule.getReturnPolicyValue())) {
            builder.setOriginValue("-1");
            builder.setPolicyType(PolicyTypePb.PolicyType.notLimit);
        } else if ("MonthlyRecurring".equals(schedule.getReturnPolicyValue())) {
            builder.setOriginValue(String.valueOf(schedule.getReturnAmountValue()));
            builder.setPolicyType(PolicyTypePb.PolicyType.monthlyRecurring);
        } else if ("dateRange".equals(schedule.getReturnPolicyValue())){
            builder.setOriginValue(String.valueOf(schedule.getReturnAmountValue()));
            builder.setPolicyType(PolicyTypePb.PolicyType.dateRange);
        }
        builder.addAllItem(PbUtil.toPortfolioItem(strategySchedule));
        builder.setTemplateId(templateId);
        list.addList(builder.build());
        SetCampaignPortfolioTaskRequestPb.SetCampaignPortfolioTaskRequest request = list.build();
        return request;
    }
}
