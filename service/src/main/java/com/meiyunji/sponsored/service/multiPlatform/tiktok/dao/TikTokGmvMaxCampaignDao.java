package com.meiyunji.sponsored.service.multiPlatform.tiktok.dao;

import com.alibaba.fastjson2.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.enums.SearchTypeEnum;
import com.meiyunji.sponsored.common.springjdbc.BaseShardingDaoImpl;
import com.meiyunji.sponsored.common.util.JSONUtil;
import com.meiyunji.sponsored.common.util.SqlStringUtil;
import com.meiyunji.sponsored.common.util.StringUtil;
import com.meiyunji.sponsored.service.multiPlatform.tiktok.model.GmvMaxCampaignSyncInfo;
import com.meiyunji.sponsored.service.multiPlatform.tiktok.model.GmvMaxVideoItem;
import com.meiyunji.sponsored.service.multiPlatform.tiktok.model.Identity;
import com.meiyunji.sponsored.service.multiPlatform.tiktok.model.po.TikTokGmvMaxCampaign;
import com.meiyunji.sponsored.service.multiPlatform.tiktok.model.po.TikTokGmvMaxCampaignPage;
import com.meiyunji.sponsored.service.multiPlatform.tiktok.model.request.GmvMaxCampaignListParam;
import com.tiktok.advertising.model.gmv_max.response.GmvMaxCampaignInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Component
public class TikTokGmvMaxCampaignDao extends BaseShardingDaoImpl<TikTokGmvMaxCampaign> {

    private static final Map<String, String> CAMPAIGN_SORT_FIELD_MAP = Maps.newHashMap();
    private static final Map<String, String> REPORT_SORT_FIELD_MAP = Maps.newHashMap();

    static {
        CAMPAIGN_SORT_FIELD_MAP.put("budget", "budget");
        CAMPAIGN_SORT_FIELD_MAP.put("roasBid", "roas_bid");

        REPORT_SORT_FIELD_MAP.put("cost", "cost");
        REPORT_SORT_FIELD_MAP.put("orders", "orders");
        REPORT_SORT_FIELD_MAP.put("grossRevenue", "gross_revenue");
        REPORT_SORT_FIELD_MAP.put("roi", "roi");
        REPORT_SORT_FIELD_MAP.put("netCost", "net_cost");
        REPORT_SORT_FIELD_MAP.put("costPerOrder", "cost_per_order");
    }

    public void add(Integer puid, Integer shopId, GmvMaxCampaignInfo campaignInfo) {
        JdbcTemplate jdbcTemplate = getJdbcTemplate(puid);
        StringBuilder sb = new StringBuilder();
        sb.append(" INSERT INTO t_tiktok_gmv_max_campaign (puid, shop_id, advertiser_id, store_id, store_authorized_bc_id, campaign_id,")
                .append(" campaign_name, operation_status, shopping_ads_type, product_specific_type, ")
                .append(" item_group_ids, optimization_goal, deep_bid_type, roas_bid, budget, ")
                .append(" schedule_type, schedule_start_time, schedule_end_time, placements, location_ids, ")
                .append(" age_groups, product_video_specific_type, identity_list, affiliate_posts_enabled, ")
                .append(" item_list, campaign_custom_anchor_video_id, custom_anchor_video_list, create_in_dxm, create_time, update_time) ")
                .append(" VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 1, NOW(), NOW()) ")
                .append(" ON DUPLICATE KEY UPDATE ")
                .append(" operation_status                = VALUES(operation_status)," +
                        " shopping_ads_type               = VALUES(shopping_ads_type)," +
                        " product_specific_type           = VALUES(product_specific_type)," +
                        " item_group_ids                  = VALUES(item_group_ids)," +
                        " optimization_goal               = VALUES(optimization_goal)," +
                        " deep_bid_type                   = VALUES(deep_bid_type)," +
                        " roas_bid                        = VALUES(roas_bid)," +
                        " budget                          = VALUES(budget)," +
                        " schedule_type                   = VALUES(schedule_type)," +
                        " schedule_start_time             = VALUES(schedule_start_time)," +
                        " schedule_end_time               = VALUES(schedule_end_time)," +
                        " campaign_name                   = VALUES(campaign_name)," +
                        " placements                      = VALUES(placements)," +
                        " location_ids                    = VALUES(location_ids)," +
                        " age_groups                      = VALUES(age_groups)," +
                        " product_video_specific_type     = VALUES(product_video_specific_type)," +
                        " identity_list                   = VALUES(identity_list)," +
                        " affiliate_posts_enabled         = VALUES(affiliate_posts_enabled)," +
                        " item_list                       = VALUES(item_list)," +
                        " campaign_custom_anchor_video_id = VALUES(campaign_custom_anchor_video_id)," +
                        " custom_anchor_video_list        = VALUES(custom_anchor_video_list)," +
                        " update_time                     = NOW();");
        List<Object> argsList = new ArrayList<>();
        argsList.add(puid);
        argsList.add(shopId);
        argsList.add(campaignInfo.getAdvertiserId());
        argsList.add(campaignInfo.getStoreId());
        argsList.add(campaignInfo.getStoreAuthorizedBcId());
        argsList.add(campaignInfo.getCampaignId());
        argsList.add(campaignInfo.getCampaignName());
        argsList.add(campaignInfo.getOperationStatus());
        argsList.add(campaignInfo.getShoppingAdsType());
        argsList.add(campaignInfo.getProductSpecificType());
        argsList.add(JSONUtil.objectToJson(campaignInfo.getItemGroupIds()));
        argsList.add(campaignInfo.getOptimizationGoal());
        argsList.add(campaignInfo.getDeepBidType());
        argsList.add(campaignInfo.getRoasBid());
        argsList.add(campaignInfo.getBudget());
        argsList.add(campaignInfo.getScheduleType());
        argsList.add(campaignInfo.getScheduleStartTime());
        argsList.add(campaignInfo.getScheduleEndTime());
        argsList.add(JSONUtil.objectToJson(campaignInfo.getPlacements()));
        argsList.add(JSONUtil.objectToJson(campaignInfo.getLocationIds()));
        argsList.add(JSONUtil.objectToJson(campaignInfo.getAgeGroups()));
        argsList.add(campaignInfo.getProductVideoSpecificType());
        argsList.add(JSONObject.toJSONString(campaignInfo.getIdentityList() != null ? campaignInfo.getIdentityList().stream().map(Identity::fromTkIdentity).collect(Collectors.toList()) : null));
        argsList.add(campaignInfo.getAffiliatePostsEnabled());
        argsList.add(JSONObject.toJSONString(campaignInfo.getItemList() != null ? campaignInfo.getItemList().stream().map(GmvMaxVideoItem::fromTkGmvMaxVideoItem).collect(Collectors.toList()) : null));
        argsList.add(campaignInfo.getCampaignCustomAnchorVideoId());
        argsList.add(JSONObject.toJSONString(campaignInfo.getCustomAnchorVideoList() != null ? campaignInfo.getCustomAnchorVideoList().stream().map(GmvMaxVideoItem::fromTkGmvMaxVideoItem).collect(Collectors.toList()) : null));
        update(jdbcTemplate, sb.toString(), argsList.toArray());
    }

    public void addOrUpdate(Integer puid, Integer shopId, GmvMaxCampaignInfo campaignInfo) {
        JdbcTemplate jdbcTemplate = getJdbcTemplate(puid);
        StringBuilder sb = new StringBuilder();
        sb.append(" INSERT INTO t_tiktok_gmv_max_campaign (puid, shop_id, advertiser_id, store_id, store_authorized_bc_id, campaign_id,")
                .append(" campaign_name, operation_status, shopping_ads_type, product_specific_type, ")
                .append(" item_group_ids, optimization_goal, deep_bid_type, roas_bid, budget, ")
                .append(" schedule_type, schedule_start_time, schedule_end_time, placements, location_ids, ")
                .append(" age_groups, product_video_specific_type, identity_list, affiliate_posts_enabled, ")
                .append(" item_list, campaign_custom_anchor_video_id, custom_anchor_video_list, create_time, update_time) ")
                .append(" VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW()) ")
                .append(" ON DUPLICATE KEY UPDATE ")
                .append(" operation_status                = VALUES(operation_status)," +
                        " shopping_ads_type               = VALUES(shopping_ads_type)," +
                        " product_specific_type           = VALUES(product_specific_type)," +
                        " item_group_ids                  = VALUES(item_group_ids)," +
                        " optimization_goal               = VALUES(optimization_goal)," +
                        " deep_bid_type                   = VALUES(deep_bid_type)," +
                        " roas_bid                        = VALUES(roas_bid)," +
                        " budget                          = VALUES(budget)," +
                        " schedule_type                   = VALUES(schedule_type)," +
                        " schedule_start_time             = VALUES(schedule_start_time)," +
                        " schedule_end_time               = VALUES(schedule_end_time)," +
                        " campaign_name                   = VALUES(campaign_name)," +
                        " placements                      = VALUES(placements)," +
                        " location_ids                    = VALUES(location_ids)," +
                        " age_groups                      = VALUES(age_groups)," +
                        " product_video_specific_type     = VALUES(product_video_specific_type)," +
                        " identity_list                   = VALUES(identity_list)," +
                        " affiliate_posts_enabled         = VALUES(affiliate_posts_enabled)," +
                        " item_list                       = VALUES(item_list)," +
                        " campaign_custom_anchor_video_id = VALUES(campaign_custom_anchor_video_id)," +
                        " custom_anchor_video_list        = VALUES(custom_anchor_video_list)," +
                        " update_time                     = NOW();");
        List<Object> argsList = new ArrayList<>();
        argsList.add(puid);
        argsList.add(shopId);
        argsList.add(campaignInfo.getAdvertiserId());
        argsList.add(campaignInfo.getStoreId());
        argsList.add(campaignInfo.getStoreAuthorizedBcId());
        argsList.add(campaignInfo.getCampaignId());
        argsList.add(campaignInfo.getCampaignName());
        argsList.add(campaignInfo.getOperationStatus());
        argsList.add(campaignInfo.getShoppingAdsType());
        argsList.add(campaignInfo.getProductSpecificType());
        argsList.add(JSONUtil.objectToJson(campaignInfo.getItemGroupIds()));
        argsList.add(campaignInfo.getOptimizationGoal());
        argsList.add(campaignInfo.getDeepBidType());
        argsList.add(campaignInfo.getRoasBid());
        argsList.add(campaignInfo.getBudget());
        argsList.add(campaignInfo.getScheduleType());
        argsList.add(campaignInfo.getScheduleStartTime());
        argsList.add(campaignInfo.getScheduleEndTime());
        argsList.add(JSONUtil.objectToJson(campaignInfo.getPlacements()));
        argsList.add(JSONUtil.objectToJson(campaignInfo.getLocationIds()));
        argsList.add(JSONUtil.objectToJson(campaignInfo.getAgeGroups()));
        argsList.add(campaignInfo.getProductVideoSpecificType());
        argsList.add(JSONObject.toJSONString(campaignInfo.getIdentityList() != null ? campaignInfo.getIdentityList().stream().map(Identity::fromTkIdentity).collect(Collectors.toList()) : null));
        argsList.add(campaignInfo.getAffiliatePostsEnabled());
        argsList.add(JSONObject.toJSONString(campaignInfo.getItemList() != null ? campaignInfo.getItemList().stream().map(GmvMaxVideoItem::fromTkGmvMaxVideoItem).collect(Collectors.toList()) : null));
        argsList.add(campaignInfo.getCampaignCustomAnchorVideoId());
        argsList.add(JSONObject.toJSONString(campaignInfo.getCustomAnchorVideoList() != null ? campaignInfo.getCustomAnchorVideoList().stream().map(GmvMaxVideoItem::fromTkGmvMaxVideoItem).collect(Collectors.toList()) : null));
        update(jdbcTemplate, sb.toString(), argsList.toArray());
    }

    public void addOrUpdate(Integer puid, Integer shopId, GmvMaxCampaignSyncInfo syncInfo) {
        JdbcTemplate jdbcTemplate = getJdbcTemplate(puid);
        StringBuilder sb = new StringBuilder();
        sb.append(" INSERT INTO t_tiktok_gmv_max_campaign (puid, shop_id, advertiser_id, store_id, store_authorized_bc_id, campaign_id,")
                .append(" campaign_name, operation_status, shopping_ads_type, product_specific_type, ")
                .append(" item_group_ids, optimization_goal, deep_bid_type, roas_bid, budget, ")
                .append(" schedule_type, schedule_start_time, schedule_end_time, placements, location_ids, ")
                .append(" age_groups, product_video_specific_type, identity_list, affiliate_posts_enabled, ")
                .append(" item_list, campaign_custom_anchor_video_id, custom_anchor_video_list, ")
                .append(" create_time_utc, modify_time_utc, objective_type, primary_status, secondary_status, create_time, update_time)")
                .append(" VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW()) ")
                .append(" ON DUPLICATE KEY UPDATE ")
                .append(" operation_status                = VALUES(operation_status)," +
                        " shopping_ads_type               = VALUES(shopping_ads_type)," +
                        " product_specific_type           = VALUES(product_specific_type)," +
                        " item_group_ids                  = VALUES(item_group_ids)," +
                        " optimization_goal               = VALUES(optimization_goal)," +
                        " deep_bid_type                   = VALUES(deep_bid_type)," +
                        " roas_bid                        = VALUES(roas_bid)," +
                        " budget                          = VALUES(budget)," +
                        " schedule_type                   = VALUES(schedule_type)," +
                        " schedule_start_time             = VALUES(schedule_start_time)," +
                        " schedule_end_time               = VALUES(schedule_end_time)," +
                        " campaign_name                   = VALUES(campaign_name)," +
                        " placements                      = VALUES(placements)," +
                        " location_ids                    = VALUES(location_ids)," +
                        " age_groups                      = VALUES(age_groups)," +
                        " product_video_specific_type     = VALUES(product_video_specific_type)," +
                        " identity_list                   = VALUES(identity_list)," +
                        " affiliate_posts_enabled         = VALUES(affiliate_posts_enabled)," +
                        " item_list                       = VALUES(item_list)," +
                        " campaign_custom_anchor_video_id = VALUES(campaign_custom_anchor_video_id)," +
                        " custom_anchor_video_list        = VALUES(custom_anchor_video_list)," +
                        " create_time_utc                 = VALUES(create_time_utc)," +
                        " modify_time_utc                 = VALUES(modify_time_utc)," +
                        " objective_type                  = VALUES(objective_type)," +
                        " primary_status                  = VALUES(primary_status)," +
                        " secondary_status                = VALUES(secondary_status)," +
                        " update_time                     = NOW();");
        List<Object> argsList = new ArrayList<>();
        argsList.add(puid);
        argsList.add(shopId);
        argsList.add(syncInfo.getAdvertiserId());
        argsList.add(syncInfo.getStoreId());
        argsList.add(syncInfo.getStoreAuthorizedBcId());
        argsList.add(syncInfo.getCampaignId());
        argsList.add(syncInfo.getCampaignName());
        argsList.add(syncInfo.getOperationStatus());
        argsList.add(syncInfo.getShoppingAdsType());
        argsList.add(syncInfo.getProductSpecificType());
        argsList.add(JSONUtil.objectToJson(syncInfo.getItemGroupIds()));
        argsList.add(syncInfo.getOptimizationGoal());
        argsList.add(syncInfo.getDeepBidType());
        argsList.add(syncInfo.getRoasBid());
        argsList.add(syncInfo.getBudget());
        argsList.add(syncInfo.getScheduleType());
        argsList.add(syncInfo.getScheduleStartTime());
        argsList.add(syncInfo.getScheduleEndTime());
        argsList.add(JSONUtil.objectToJson(syncInfo.getPlacements()));
        argsList.add(JSONUtil.objectToJson(syncInfo.getLocationIds()));
        argsList.add(JSONUtil.objectToJson(syncInfo.getAgeGroups()));
        argsList.add(syncInfo.getProductVideoSpecificType());
        argsList.add(JSONUtil.objectToJson(syncInfo.getIdentityList() != null ? syncInfo.getIdentityList().stream().map(Identity::fromTkIdentity).collect(Collectors.toList()) : null));
        argsList.add(syncInfo.getAffiliatePostsEnabled());
        argsList.add(JSONUtil.objectToJson(syncInfo.getItemList() != null ? syncInfo.getItemList().stream().map(GmvMaxVideoItem::fromTkGmvMaxVideoItem).collect(Collectors.toList()) : null));
        argsList.add(syncInfo.getCampaignCustomAnchorVideoId());
        argsList.add(JSONUtil.objectToJson(syncInfo.getCustomAnchorVideoList() != null ? syncInfo.getCustomAnchorVideoList().stream().map(GmvMaxVideoItem::fromTkGmvMaxVideoItem).collect(Collectors.toList()) : null));
        argsList.add(syncInfo.getCreateTimeUtc());
        argsList.add(syncInfo.getModifyTimeUtc());
        argsList.add(syncInfo.getObjectiveType());
        argsList.add(syncInfo.getPrimaryStatus());
        argsList.add(syncInfo.getSecondaryStatus());
        update(jdbcTemplate, sb.toString(), argsList.toArray());
    }

    public int batchAddOrUpdate(Integer puid, List<GmvMaxCampaignSyncInfo> list) {
        StringBuilder sb = new StringBuilder();
        sb.append(" INSERT INTO t_tiktok_gmv_max_campaign (puid, shop_id, advertiser_id, store_id, store_authorized_bc_id, campaign_id,")
                .append(" campaign_name, operation_status, shopping_ads_type, product_specific_type, ")
                .append(" item_group_ids, optimization_goal, deep_bid_type, roas_bid, budget, ")
                .append(" schedule_type, schedule_start_time, schedule_end_time, placements, location_ids, ")
                .append(" age_groups, product_video_specific_type, identity_list, affiliate_posts_enabled, ")
                .append(" item_list, campaign_custom_anchor_video_id, custom_anchor_video_list, ")
                .append(" create_time_utc, modify_time_utc, objective_type, primary_status, secondary_status, create_time, update_time) VALUES ");
        List<Object> argsList = new ArrayList<>();
        for (GmvMaxCampaignSyncInfo syncInfo : list) {
            sb.append(" (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW()),");
            argsList.add(puid);
            argsList.add(syncInfo.getShopId());
            argsList.add(syncInfo.getAdvertiserId());
            argsList.add(syncInfo.getStoreId());
            argsList.add(syncInfo.getStoreAuthorizedBcId());
            argsList.add(syncInfo.getCampaignId());
            argsList.add(syncInfo.getCampaignName());
            argsList.add(syncInfo.getOperationStatus());
            argsList.add(syncInfo.getShoppingAdsType());
            argsList.add(syncInfo.getProductSpecificType());
            argsList.add(JSONUtil.objectToJson(syncInfo.getItemGroupIds()));
            argsList.add(syncInfo.getOptimizationGoal());
            argsList.add(syncInfo.getDeepBidType());
            argsList.add(syncInfo.getRoasBid());
            argsList.add(syncInfo.getBudget());
            argsList.add(syncInfo.getScheduleType());
            argsList.add(syncInfo.getScheduleStartTime());
            argsList.add(syncInfo.getScheduleEndTime());
            argsList.add(JSONUtil.objectToJson(syncInfo.getPlacements()));
            argsList.add(JSONUtil.objectToJson(syncInfo.getLocationIds()));
            argsList.add(JSONUtil.objectToJson(syncInfo.getAgeGroups()));
            argsList.add(syncInfo.getProductVideoSpecificType());
            argsList.add(JSONUtil.objectToJson(syncInfo.getIdentityList() != null ? syncInfo.getIdentityList().stream().map(Identity::fromTkIdentity).collect(Collectors.toList()) : null));
            argsList.add(syncInfo.getAffiliatePostsEnabled());
            argsList.add(JSONUtil.objectToJson(syncInfo.getItemList() != null ? syncInfo.getItemList().stream().map(GmvMaxVideoItem::fromTkGmvMaxVideoItem).collect(Collectors.toList()) : null));
            argsList.add(syncInfo.getCampaignCustomAnchorVideoId());
            argsList.add(JSONUtil.objectToJson(syncInfo.getCustomAnchorVideoList() != null ? syncInfo.getCustomAnchorVideoList().stream().map(GmvMaxVideoItem::fromTkGmvMaxVideoItem).collect(Collectors.toList()) : null));
            argsList.add(syncInfo.getCreateTimeUtc());
            argsList.add(syncInfo.getModifyTimeUtc());
            argsList.add(syncInfo.getObjectiveType());
            argsList.add(syncInfo.getPrimaryStatus());
            argsList.add(syncInfo.getSecondaryStatus());
        }
        sb.deleteCharAt(sb.length() - 1);
        sb.append(" ON DUPLICATE KEY UPDATE ")
                .append(" operation_status                = VALUES(operation_status)," +
                        " shopping_ads_type               = VALUES(shopping_ads_type)," +
                        " product_specific_type           = VALUES(product_specific_type)," +
                        " item_group_ids                  = VALUES(item_group_ids)," +
                        " optimization_goal               = VALUES(optimization_goal)," +
                        " deep_bid_type                   = VALUES(deep_bid_type)," +
                        " roas_bid                        = VALUES(roas_bid)," +
                        " budget                          = VALUES(budget)," +
                        " schedule_type                   = VALUES(schedule_type)," +
                        " schedule_start_time             = VALUES(schedule_start_time)," +
                        " schedule_end_time               = VALUES(schedule_end_time)," +
                        " campaign_name                   = VALUES(campaign_name)," +
                        " placements                      = VALUES(placements)," +
                        " location_ids                    = VALUES(location_ids)," +
                        " age_groups                      = VALUES(age_groups)," +
                        " product_video_specific_type     = VALUES(product_video_specific_type)," +
                        " identity_list                   = VALUES(identity_list)," +
                        " affiliate_posts_enabled         = VALUES(affiliate_posts_enabled)," +
                        " item_list                       = VALUES(item_list)," +
                        " campaign_custom_anchor_video_id = VALUES(campaign_custom_anchor_video_id)," +
                        " custom_anchor_video_list        = VALUES(custom_anchor_video_list)," +
                        " create_time_utc                 = VALUES(create_time_utc)," +
                        " modify_time_utc                 = VALUES(modify_time_utc)," +
                        " objective_type                  = VALUES(objective_type)," +
                        " primary_status                  = VALUES(primary_status)," +
                        " secondary_status                = VALUES(secondary_status)," +
                        " update_time                     = NOW()");
        return getJdbcTemplate(puid).update(sb.toString(), argsList.toArray());
    }

    public TikTokGmvMaxCampaign getByCampaignId(Integer puid, Integer shopId, String advertiserId, String campaignId) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sql = new StringBuilder("select * from ").append(this.getJdbcHelper().getTable());
        sql.append(" where puid = ? and shop_id = ? and advertiser_id = ? and campaign_id = ?");
        argsList.add(puid);
        argsList.add(shopId);
        argsList.add(advertiserId);
        argsList.add(campaignId);
        return getJdbcTemplate(puid).queryForObject(sql.toString(), new BeanPropertyRowMapper<>(TikTokGmvMaxCampaign.class), argsList.toArray());
    }

    public void updateOperationStatus(Integer puid, Integer shopId, String advertiserId, String campaignId, String operationStatus) {
        String sql = "update `t_tiktok_gmv_max_campaign` set `operation_status` = ? " +
                " where `puid` =? and `shop_id`=? and `advertiser_id` = ? and `campaign_id` = ? ";
        List<Object> argsList = new ArrayList<>();
        argsList.add(operationStatus);
        argsList.add(puid);
        argsList.add(shopId);
        argsList.add(advertiserId);
        argsList.add(campaignId);
        getJdbcTemplate(puid).update(sql, argsList.toArray());
    }

    public void updatePrimaryStatus(Integer puid, Integer shopId, String advertiserId, String campaignId, String primaryStatus) {
        String sql = "update `t_tiktok_gmv_max_campaign` set `primary_status` = ? " +
                " where `puid` =? and `shop_id`=? and `advertiser_id` = ? and `campaign_id` = ? ";
        List<Object> argsList = new ArrayList<>();
        argsList.add(primaryStatus);
        argsList.add(puid);
        argsList.add(shopId);
        argsList.add(advertiserId);
        argsList.add(campaignId);
        getJdbcTemplate(puid).update(sql, argsList.toArray());
    }

    public Page<TikTokGmvMaxCampaignPage> getPageListWithReport(Integer puid, List<Integer> shopIds, GmvMaxCampaignListParam param) {

        List<Object> argsList = Lists.newArrayList();

        StringBuilder selectSql = new StringBuilder(" select ")
                .append(" c.puid, ")
                .append(" c.shop_id, ")
                .append(" c.campaign_id, ")
                .append(" r.campaign_id is not null 'hasReport', ")
                .append(" c.advertiser_id, ")
                .append(" c.store_authorized_bc_id, ")
                .append(" any_value(c.campaign_name) campaign_name, ")
                .append(" any_value(c.store_id) store_id, ")
                .append(" any_value(c.operation_status) operation_status, ")
                .append(" any_value(c.primary_status) primary_status, ")
                .append(" any_value(c.shopping_ads_type) shopping_ads_type, ")
                .append(" any_value(c.optimization_goal) optimization_goal, ")
                .append(" any_value(c.roas_bid) roas_bid, ")
                .append(" any_value(c.budget) budget, ")
                .append(" ifnull(sum(r.cost), 0) cost, ")
                .append(" ifnull(sum(r.orders), 0) orders, ")
                .append(" ifnull(sum(r.gross_revenue), 0) grossRevenue, ")
                .append(" ifnull(sum(r.net_cost), 0) netCost, ")
                .append(" ifnull(sum(r.cost) / sum(r.orders), 0) costPerOrder, ")
                .append(" ifnull(sum(r.gross_revenue) / sum(r.cost), 0) roi ");

        StringBuilder sql = new StringBuilder()
                .append(" from t_tiktok_gmv_max_campaign c left join t_tiktok_gmv_max_campaign_report r ")
                .append(" on c.puid = r.puid and c.shop_id = r.shop_id and c.campaign_id = r.campaign_id and c.advertiser_id = r.advertiser_id ");

        if (StringUtils.isNotBlank(param.getDateRange().get(0))) {
            sql.append(" and r.stat_time_day >= ? ");
            argsList.add(param.getDateRange().get(0));
        }
        if (CollectionUtils.size(param.getDateRange()) > 1 && StringUtils.isNotBlank(param.getDateRange().get(1))) {
            sql.append(" and r.stat_time_day <=? ");
            argsList.add(param.getDateRange().get(1));
        }

        sql.append(" where c.puid = ? ");
        argsList.add(puid);
        sql.append(SqlStringUtil.dealInList("c.shop_id", shopIds, argsList));
        if (CollectionUtils.isNotEmpty(param.getAdvertiserId())) {
            sql.append(SqlStringUtil.dealInList("c.advertiser_id", param.getAdvertiserId(), argsList));
        }
        if (CollectionUtils.isNotEmpty(param.getPrimaryStatus())) {
            sql.append(SqlStringUtil.dealInList("c.primary_status", param.getPrimaryStatus(), argsList));
        }

        if (StringUtils.isNotBlank(param.getCampaignName())) {
            if (StringUtils.equalsIgnoreCase(param.getSearchType(), SearchTypeEnum.BLUR.getValue())) {
                sql.append(" and c.campaign_name like ? ");
                argsList.add("%" + SqlStringUtil.dealLikeSql(param.getCampaignName()) + "%");
            } else {
                List<String> campaignNames = StringUtil.splitStr(param.getCampaignName(), StringUtil.SPECIAL_COMMA)
                        .stream().filter(StringUtil::isNotEmpty).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(campaignNames)) {
                    sql.append(SqlStringUtil.dealInList("c.campaign_name", campaignNames, argsList));
                }
            }
        }

        sql.append(" group by c.puid, c.shop_id, c.advertiser_id, c.campaign_id ");

        String sortField = "";
        if (StringUtils.isNotBlank(param.getOrderField())) {
            if (CAMPAIGN_SORT_FIELD_MAP.containsKey(param.getOrderField())) {
                sortField = "c." + CAMPAIGN_SORT_FIELD_MAP.get(param.getOrderField()) + " is null, c."+ CAMPAIGN_SORT_FIELD_MAP.get(param.getOrderField());
            }
            if (REPORT_SORT_FIELD_MAP.containsKey(param.getOrderField())) {
                sortField = "hasReport desc, " + REPORT_SORT_FIELD_MAP.get(param.getOrderField());
            }
        }

        if (StringUtils.isNotBlank(sortField) && StringUtils.isNotBlank(param.getOrderValue())) {
            sql.append(" order by ").append(sortField)
                    .append(StringUtils.equalsIgnoreCase(param.getOrderValue(), "desc") ? " desc " : " asc ")
                    .append(", any_value(c.create_time) desc ");
        } else {
            sql.append(" order by any_value(c.create_time) desc ");
        }

        selectSql.append(sql);

        String cSql = "select count(*) from (" + selectSql + ") t";

        Object[] args = argsList.toArray();

        return getPageResultByClass(puid, param.getPageNo(), param.getPageSize(),
                cSql, args, selectSql.toString(), args, TikTokGmvMaxCampaignPage.class);
    }


}
