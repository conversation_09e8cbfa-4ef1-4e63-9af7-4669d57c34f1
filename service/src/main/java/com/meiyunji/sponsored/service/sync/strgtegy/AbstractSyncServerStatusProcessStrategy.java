package com.meiyunji.sponsored.service.sync.strgtegy;

import com.meiyunji.sponsored.common.support.RedisService;
import com.meiyunji.sponsored.common.util.StringUtil;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.config.DynamicRefreshConfiguration;
import com.meiyunji.sponsored.service.stream.enums.AmazonStreamTaskTypeEnum;
import com.meiyunji.sponsored.service.stream.service.IAmazonManagementStreamTaskRetryService;
import com.meiyunji.sponsored.service.syncTask.management.strgtegy.enume.AdProductEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.pulsar.shade.com.google.common.collect.Lists;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
public abstract class AbstractSyncServerStatusProcessStrategy {


    protected IAmazonManagementStreamTaskRetryService amazonManagementStreamTaskRetryService;
    protected DynamicRefreshConfiguration dynamicRefreshConfiguration;
    protected RedisService redisService;

    public AbstractSyncServerStatusProcessStrategy(IAmazonManagementStreamTaskRetryService amazonManagementStreamTaskRetryService,
                                                   DynamicRefreshConfiguration dynamicRefreshConfiguration, RedisService redisService) {
        this.amazonManagementStreamTaskRetryService = amazonManagementStreamTaskRetryService;
        this.dynamicRefreshConfiguration = dynamicRefreshConfiguration;
        this.redisService = redisService;
    }


    public void executeTask(ShopAuth shopAuth, AdProductEnum adProductEnum, AmazonStreamTaskTypeEnum amazonStreamTaskTypeEnum, List<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        ids = ids.stream().filter(e -> redisService.get(shopAuth.getId() + "|" + e) == null).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(ids)) {
            log.info("sync sever status ids empty , puid:{}, shopId:{}, type :{}， ad-type：{}", shopAuth.getPuid(), shopAuth.getId(), amazonStreamTaskTypeEnum.getType(), adProductEnum.getAdType());
            return;
        }
        List<List<String>> partition = Lists.partition(ids, getMaxCount());
        for (List<String> id : partition) {
            execute(shopAuth, adProductEnum, amazonStreamTaskTypeEnum, id);
        }
    }

    private void execute(ShopAuth shopAuth, AdProductEnum adProductEnum, AmazonStreamTaskTypeEnum amazonStreamTaskTypeEnum, List<String> ids) {
        try {
            String idsStr = StringUtil.joinString(ids);
            doExecute(shopAuth, idsStr);
            ids.forEach(e-> redisService.set(shopAuth.getId() + "|" + e, " ", 20, TimeUnit.MINUTES));
        } catch (Exception exception) {
            log.error("sync sever status error, puid:{}, shopId:{}, type :{}， ad-type：{}，error", shopAuth.getPuid(), shopAuth.getId(), amazonStreamTaskTypeEnum.getType(), adProductEnum.getAdType(), exception);
            //amazonManagementStreamTaskRetryService.saveTaskRetry(shopAuth.getPuid(), shopAuth.getId(), adProductEnum, amazonStreamTaskTypeEnum, ids);
        }


    }


    public abstract int getMaxCount();

    public abstract void doExecute(ShopAuth shopAuth, String ids);

}
