package com.meiyunji.sponsored.service.multiPlatform.walmart.service.impl;


import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.common.util.JSONUtil;
import com.meiyunji.sponsored.service.multiPlatform.shop.dao.IMultiPlatformShopAuthDao;
import com.meiyunji.sponsored.service.multiPlatform.shop.po.MultiPlatformShopAuth;
import com.meiyunji.sponsored.service.multiPlatform.walmart.dao.IWalmartAdvertisingItemRecommendationsDao;
import com.meiyunji.sponsored.service.multiPlatform.walmart.po.WalmartAdvertiserAttributes;
import com.meiyunji.sponsored.service.multiPlatform.walmart.po.WalmartAdvertisingItemRecommendations;
import com.meiyunji.sponsored.service.multiPlatform.walmart.po.WalmartAdvertisingSnapshot;
import com.meiyunji.sponsored.service.multiPlatform.walmart.service.IWalmartAdvertiserAttributesService;
import com.meiyunji.sponsored.service.multiPlatform.walmart.service.IWalmartAdvertisingItemRecommendationsService;
import com.meiyunji.sponsored.service.multiPlatform.walmart.service.IWalmartAdvertisingSnapshotService;
import com.meiyunji.sponsored.service.multiPlatform.walmart.util.Constants;
import com.meiyunji.sponsored.service.multiPlatform.walmart.util.ImportCsv;
import com.meiyunji.sponsored.service.multiPlatform.walmart.vo.WalmartAdvertisingItemInsightsVo;
import com.walmart.oms.advertiser.WalmartAdvertiserClient;
import com.walmart.oms.advertiser.base.enums.JobStatusEnum;
import com.walmart.oms.advertiser.model.CreateSnapshotResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @author: pxq
 * @date: 2025/02/24
 * @Description: walmart广告活动产品推荐ervice
 */
@Service
@Slf4j
public class WalmartAdvertisingItemRecommendationsServiceImpl implements IWalmartAdvertisingItemRecommendationsService {

    @Autowired
    private IWalmartAdvertisingItemRecommendationsDao walmartAdvertisingItemRecommendationsDao;
    @Autowired
    private IWalmartAdvertiserAttributesService walmartAdvertiserAttributesService;
    @Autowired
    private IWalmartAdvertisingSnapshotService advertisingSnapshotService;
    @Autowired
    private IMultiPlatformShopAuthDao multiPlatformShopAuthDao;


    @Override
    public Long add(WalmartAdvertisingItemRecommendations itemRecommendations) {
        return null;
    }

    @Override
    public int delete(Integer puid, Long id) {
        return 0;
    }

    @Override
    public int update(WalmartAdvertisingItemRecommendations itemRecommendations) {
        return 0;
    }

    @Override
    public Page getPageList(int puid, int pageNo, int pageSize, Map<String, Object> queryParams) {
        return null;
    }


    @Override
    public WalmartAdvertisingItemRecommendations getByItemId(int puid, Long shopId, String itemId) {
        return null;
    }

    @Override
    public List<WalmartAdvertisingItemInsightsVo> getItemRecommendations(int puid, String ids) {
        return null;
    }

    @Override
    public void syncItemRecommendations(Integer puid, Integer shopId) {
        List<Long> allId = new ArrayList<>();
        if (puid != null && shopId != null) {
            WalmartAdvertiserAttributes attributes = walmartAdvertiserAttributesService.getByShopId(puid, shopId);
            if (attributes != null) {
                allId.add(attributes.getId());
            }
        } else if (puid != null) {
            List<WalmartAdvertiserAttributes> attributesList = walmartAdvertiserAttributesService.getByPuid(puid);
            if (CollectionUtils.isNotEmpty(attributesList)) {
                allId.addAll(attributesList.stream().map(WalmartAdvertiserAttributes::getId).collect(Collectors.toList()));
            }
        } else {
            allId = walmartAdvertiserAttributesService.getAllId();
        }
        executeItemRecommendations(allId);
    }

    private void executeItemRecommendations(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        WalmartAdvertiserClient advertiserClient = new WalmartAdvertiserClient();
        for (Long id : ids) {
            WalmartAdvertiserAttributes attributes = walmartAdvertiserAttributesService.getById(id);
            if (attributes == null) {
                continue;
            }
            int count = advertisingSnapshotService.getUndoneCountByType(attributes.getPuid(), attributes.getShopId(), Constants.SNAPSHORT_TYPE_ITEM_RECOMMENDATIONS);
            if (count > 0) {
                log.error("syncItemRecommendations:::UndoneCount:::" + count);
                return;
            }
            CreateSnapshotResponse response = advertiserClient.createRecommendationsSnapshot(attributes.getAdvertiserId(), Constants.RECOMMENDATION_TYPE_ITEMRECOMMENDATIONS);
            if (response == null) {
                log.info("syncItemRecommendations:::response is null");
                return;
            }
            if (StringUtils.equals("failure", response.getCode()) && StringUtils.isNotBlank(response.getDetailsStr())) {
                log.info("syncItemRecommendations:::Description:::" + response.getDetailsObj().getDescription());
                log.info("syncItemRecommendations:::response:::" + JSONUtil.objectToJson(response));
                return;
            }
            if (response.getDetailsObj() != null && StringUtils.isNotBlank(response.getDetailsObj().getDescription())) {
                log.info("syncItemRecommendations:::Description:::" + response.getDetailsObj().getDescription());
                return;
            }
            if (StringUtils.isBlank(response.getSnapshotId())) {
                log.info("syncItemRecommendations:::SnapshotId is null");
                return;
            }
            WalmartAdvertisingSnapshot snapshot = new WalmartAdvertisingSnapshot();
            snapshot.setPuid(attributes.getPuid());
            snapshot.setShopId(attributes.getShopId());
            snapshot.setSnapshotId(response.getSnapshotId());
            snapshot.setJobStatus(JobStatusEnum.PENDING.getValue());
            snapshot.setType(Constants.SNAPSHORT_TYPE_ITEM_RECOMMENDATIONS);
            snapshot.setState(Constants.STATE_PENDING);
            advertisingSnapshotService.add(snapshot);
        }
    }


    @Override
    public void snapshotExecute(WalmartAdvertisingSnapshot snapshot, String detailsStr, String jobStatus) {
        List<String[]> csvList;
        try {
            csvList = ImportCsv.readCsvFile(detailsStr);
        } catch (Exception e) {
            advertisingSnapshotService.updateState(snapshot.getId(), Constants.STATE_FINISH, jobStatus, "Walmart:::snapshot:::数据解析异常！");
            return;
        }
        if (CollectionUtils.isEmpty(csvList)) {
            advertisingSnapshotService.updateState(snapshot.getId(), Constants.STATE_FINISH, jobStatus, "Walmart:::snapshot:::csv data is null");
            return;
        }
        String[] checkData = csvList.get(0);
        int rowLength = checkData.length;
        if (rowLength < 9) {
            advertisingSnapshotService.updateState(snapshot.getId(), Constants.STATE_FINISH, jobStatus, "Walmart:::snapshot:::csv data rowLength error:::" + (StringUtils.isNotBlank(checkData[0]) ? checkData[0] : ""));
            return;
        }
        String checkReportDate = checkData[0];
        if (StringUtils.isBlank(checkReportDate)) {
            log.info("Walmart:::snapshot:::csv data date error！");
            return;
        }
        //当天数据如存在，先清理
        int count = walmartAdvertisingItemRecommendationsDao.getByReportDate(snapshot.getPuid(), snapshot.getShopId(), checkReportDate);
        if (count > 0) {
            walmartAdvertisingItemRecommendationsDao.deleteByReportDate(snapshot.getPuid(), snapshot.getShopId(), checkReportDate);
        }
        MultiPlatformShopAuth multiPlatformShopAuth = multiPlatformShopAuthDao.getByIdAndPuid(snapshot.getShopId(), snapshot.getPuid());
        if (multiPlatformShopAuth == null) {
            log.error("店铺不存在 puid:{}, shopId:{}", snapshot.getPuid(), snapshot.getShopId());
            return;
        }

        List<WalmartAdvertisingItemRecommendations> list = Lists.newArrayListWithCapacity(csvList.size());
        for (String[] data : csvList) {
            if (rowLength != data.length) {
                continue;
            }
            String reportDate = data[0];
            String itemId = data[1];
            String itemName = data[2];
            String suggestedBid = data[3];
            String brandName = data[4];
            String superDepartName = data[5];
            String departmentName = data[6];
            String category = data[7];
            String subCategory = data[8];
            if (StringUtils.isBlank(reportDate)) {
                continue;
            }
            WalmartAdvertisingItemRecommendations itemRecommendations = new WalmartAdvertisingItemRecommendations();
            itemRecommendations.setPuid(snapshot.getPuid());
            itemRecommendations.setShopId(snapshot.getShopId());
            itemRecommendations.setReportDate(DateUtil.strToDate4(reportDate));
            itemRecommendations.setItemId(itemId);
            itemRecommendations.setItemName(itemName);
            if (StringUtils.isNotBlank(suggestedBid)) {
                itemRecommendations.setSuggestedBid(Double.valueOf(suggestedBid));
            }
            itemRecommendations.setItemBrandName(brandName);
            itemRecommendations.setSuperDepartmentName(superDepartName);
            itemRecommendations.setDepartmentName(departmentName);
            itemRecommendations.setCategory(category);
            itemRecommendations.setSubCategory(subCategory);
            itemRecommendations.setMarketplaceCode(multiPlatformShopAuth.getMarketplaceCode());
            list.add(itemRecommendations);
        }
        if (CollectionUtils.isNotEmpty(list)) {
            walmartAdvertisingItemRecommendationsDao.insertOrUpdate(snapshot.getPuid(), list);
        }
        advertisingSnapshotService.updateState(snapshot.getId(), Constants.STATE_FINISH, jobStatus, "");
    }
}
