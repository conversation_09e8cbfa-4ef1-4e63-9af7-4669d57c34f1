package com.meiyunji.sponsored.service.doris.dao.impl;

import com.meiyunji.sponsored.common.util.SqlStringUtil;
import com.meiyunji.sponsored.service.doris.bo.AdGroupDefaultBidBo;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.stereotype.Repository;
import com.meiyunji.sponsored.common.springjdbc.DorisBaseDaoImpl;
import com.meiyunji.sponsored.service.doris.dao.IOdsAmazonAdGroupSdDao;
import com.meiyunji.sponsored.service.doris.po.OdsAmazonAdGroupSd;

import java.util.ArrayList;
import java.util.List;

/**
 * amazon SD广告组表(OdsAmazonAdGroupSd)数据库访问层
 *
 * <AUTHOR>
 * @since 2024-03-26 10:46:18
 */
@Repository
public class OdsAmazonAdGroupSdDaoImpl extends DorisBaseDaoImpl<OdsAmazonAdGroupSd> implements IOdsAmazonAdGroupSdDao {

    @Override
    public List<AdGroupDefaultBidBo> getBidByGroupIds(Integer puid, Integer shopId, List<String> adGroupIds) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sb = new StringBuilder("select ad_group_id adGroupId, default_bid defaultBid from ")
                .append(this.getJdbcHelper().getTable());
        sb.append(" where puid = ? and shop_id = ? ");
        argsList.add(puid);
        argsList.add(shopId);
        sb.append(SqlStringUtil.dealBitMapDorisInList("ad_group_id", adGroupIds, argsList));
        return getJdbcTemplate().query(sb.toString(), argsList.toArray(), new BeanPropertyRowMapper<>(AdGroupDefaultBidBo.class));
    }

}

