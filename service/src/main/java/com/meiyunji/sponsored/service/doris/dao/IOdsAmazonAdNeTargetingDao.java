package com.meiyunji.sponsored.service.doris.dao;

import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.springjdbc.IDorisBaseDao;
import com.meiyunji.sponsored.rpc.asins.PageListAsinsRequest;
import com.meiyunji.sponsored.service.cpc.dto.NeTargetReportDataDto;
import com.meiyunji.sponsored.service.cpc.dto.NeTargetReportFilterDto;
import com.meiyunji.sponsored.service.cpc.vo.*;
import com.meiyunji.sponsored.service.doris.po.OdsAmazonAdNeTargeting;
import com.meiyunji.sponsored.service.doris.po.OdsAmazonAdTargeting;

import java.util.List;

/**
 * amazon广告投放定位表(OdsAmazonAdNeTargeting)数据库访问层
 *
 * <AUTHOR>
 * @since 2024-03-26 10:46:20
 */
public interface IOdsAmazonAdNeTargetingDao extends IDorisBaseDao<OdsAmazonAdNeTargeting> {
    /**
     * 根据广告组id查询所有的否定投放
     */
    List<OdsAmazonAdNeTargeting> listByGroupIdList(Integer puid, Integer shopId, List<String> groupIds);

    Page<String> pageGroupNeTargetWithoutReportFilter(NeTargetingPageParam param);
    Page<String> page30DaysGroupNeTargetWithReportFilter(NeTargetingPageParam param);
    Page<String> pageGroupNeTargetWithReportFilter(NeTargetingPageParam param);

    List<NeTargetReportDataDto> get30ReportByTargetIds(int puid, int shopId, List<String> keywordIds, NeTargetReportFilterDto neTargetReportFilterDto, String type);
    List<NeTargetReportDataDto> getReportByTargetIds(int puid, int shopId, List<String> keywordIds, NeTargetReportFilterDto neTargetReportFilterDto, String type);


    /**
     * 分页查询否投详情
     * @param puid
     * @param param
     * @return
     */
    Page<AsinLibsDetailVo> getAsinNeTargetList(Integer puid, AsinLibsDetailParam param);

    List<AsinLibsVo> getCountByAsin(Integer puid, PageListAsinsRequest param, List<Integer> shopIdList, List<String> asinList);

    Page<OdsAmazonAdTargeting> listPageByCondition(Integer puid, List<Integer> shopIds, AsinLibsParam param);

    List<OdsAmazonAdTargeting> listAllNeGroupByCondition(Integer puid, List<Integer> shopIds, AsinLibsParam param);
}

