package com.meiyunji.sponsored.service.cpc.service.impl;

import com.meiyunji.sellfox.aadras.types.schedule.AdvertiseRuleTaskExecuteRecordV2Message;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.service.autoRule.vo.AdKeywordTargetAutoRuleParam;
import com.meiyunji.sponsored.service.config.DynamicRefreshConfiguration;
import com.meiyunji.sponsored.service.cpc.bo.AdOrderBo;
import com.meiyunji.sponsored.service.cpc.bo.AdTargetBo;
import com.meiyunji.sponsored.service.cpc.bo.AmazonAdTargetingTypeBo;
import com.meiyunji.sponsored.service.cpc.dao.*;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdNeTargeting;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdTargeting;
import com.meiyunji.sponsored.service.cpc.qo.TargetSuggestBidBatchQo;
import com.meiyunji.sponsored.service.cpc.service.IAmazonAdTargetDaoRoutingService;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.vo.*;
import com.meiyunji.sponsored.service.cpc.vo.pricing.ScheduleTaskFinishedVo;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.qo.DiagnoseCountParam;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.qo.TargetViewHourParam;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.qo.TargetViewParam;
import com.meiyunji.sponsored.service.strategy.vo.AdTargetStrategyParam;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;


/**
 * keyword表路由层，这个service主要是用于上线，后续可以不用这个service
 *
 * <AUTHOR>
 * @date 2024/04/15
 */
@Service
public class AmazonAdTargetDaoRoutingService implements IAmazonAdTargetDaoRoutingService {


    @Autowired
    private IAmazonAdTargetingShardingDao amazonAdTargetingShardingDao;
    @Autowired
    private IAmazonAdNeTargetingDao amazonAdNeTargetingDao;
    @Autowired
    private DynamicRefreshConfiguration dynamicRefreshConfiguration;


    private Boolean shardingQuerySwitch(Integer puid) {
        return dynamicRefreshConfiguration.isSplitTargetQuery(puid);
    }

    @Override
    public void insertOnDuplicateKeyUpdate(Integer puid, List<AmazonAdTargeting> amazonAdTargetings, String type) {

        insertSharding(puid, amazonAdTargetings, type);
    }

    private void insertSharding(Integer puid, List<AmazonAdTargeting> amazonAdTargetings, String type) {
        if (Constants.TARGETING_TYPE_NEGATIVEASIN.equalsIgnoreCase(type)) {
            List<AmazonAdNeTargeting> nes = amazonAdTargetings.stream().map(e -> {
                AmazonAdNeTargeting ne = converToNe(e);
                return ne;
            }).collect(Collectors.toList());
            amazonAdNeTargetingDao.insertOnDuplicateKeyUpdate(puid, nes);
        } else {
            amazonAdTargetingShardingDao.insertOnDuplicateKeyUpdate(puid, amazonAdTargetings);
        }
    }

    @Override
    public Integer countByAdGroupId(int puid, Integer shopId, String adGroupId) {

        return amazonAdTargetingShardingDao.countByAdGroupId(puid, shopId, adGroupId);

    }

    @Override
    public List<String> getTargetIds(Integer puid, Integer shopId, String marketPlaceId, List<String> onlineTargetId) {

        return amazonAdTargetingShardingDao.getTargetIds(puid, shopId, marketPlaceId, onlineTargetId);

    }

    @Override
    public void updateList(Integer puid, List<AmazonAdTargeting> updateList) {

        amazonAdTargetingShardingDao.updateList(puid, updateList);

    }


    @Override
    public List<AmazonAdTargeting> listByAdGroupId(Integer puid, Integer shopId, String adGroupId) {

        return amazonAdTargetingShardingDao.listByAdGroupId(puid, shopId, adGroupId);

    }

    @Override
    public List<AmazonAdTargeting> listAutoByAdGroupId(Integer puid, Integer shopId, String adGroupId) {

        return amazonAdTargetingShardingDao.listAutoByAdGroupId(puid, shopId, adGroupId);

    }

    @Override
    public void updateTargetId(Integer puid, List<AmazonAdTargeting> list) {

        amazonAdTargetingShardingDao.updateTargetId(puid, list);

    }

    @Override
    public int deleteByGroupId(int puid, Integer shopId, String adGroupId) {

        return amazonAdTargetingShardingDao.deleteByGroupId(puid, shopId, adGroupId);

    }

    @Override
    public int deleteFailByGroupId(Integer puid, Integer shopId, String adGroupId) {

        return amazonAdTargetingShardingDao.deleteFailByGroupId(puid, shopId, adGroupId);

    }

    @Override
    public AmazonAdTargeting getByAdTargetId(int puid, Integer shopId, String marketplaceId, String targetId) {

        return amazonAdTargetingShardingDao.getByAdTargetId(puid, shopId, marketplaceId, targetId);

    }

    @Override
    public AmazonAdTargeting getByAdTargetId(int puid, Integer shopId, String targetId) {

        return amazonAdTargetingShardingDao.getByAdTargetId(puid, shopId, targetId);

    }

    @Override
    public Page pageListForTask(int puid, CpcTaskSearchDto dto, Page page) {

        return amazonAdTargetingShardingDao.pageListForTask(puid, dto, page);

    }

    @Override
    public List<AmazonAdTargeting> listTargets(int puid, List<String> targetIdList) {

        return amazonAdTargetingShardingDao.listTargets(puid, targetIdList);

    }

    @Override
    public Page<AmazonAdTargeting> pageList(Integer puid, TargetingPageParam param) {

        return amazonAdTargetingShardingDao.pageList(puid, param);

    }

    @Override
    public List<AmazonAdTargeting> listByCondition(Integer puid, TargetingPageParam param) {

        return amazonAdTargetingShardingDao.listByCondition(puid, param);

    }

    @Override
    public Page<AmazonAdTargeting> neTargetingPageList(Integer puid, NeTargetingPageParam param) {

        Page<AmazonAdNeTargeting> amazonAdNeKeywordPage = amazonAdNeTargetingDao.neTargetingPageList(puid, param);
        Page<AmazonAdTargeting> page = new Page<>();
        page.setPageNo(amazonAdNeKeywordPage.getPageNo());
        page.setTotalPage(amazonAdNeKeywordPage.getTotalPage());
        page.setPageSize(amazonAdNeKeywordPage.getPageSize());
        List<AmazonAdTargeting> rows = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(amazonAdNeKeywordPage.getRows())) {
            rows = amazonAdNeKeywordPage.getRows().stream().map(e -> {
                AmazonAdTargeting keyword = converToPo(e);
                return keyword;
            }).collect(Collectors.toList());
        }
        page.setRows(rows);
        return page;

    }

    @Override
    public Map<String, Integer> statCountByAdGroup(Integer puid, Integer shopId, List<String> status, List<String> adGroupIds) {

        return amazonAdTargetingShardingDao.statCountByAdGroup(puid, shopId, status, adGroupIds);

    }

    @Override
    public AmazonAdTargeting getByTargetId(int puid, Integer shopId, String marketplaceId, String targetId) {

        return amazonAdTargetingShardingDao.getByTargetId(puid, shopId, marketplaceId, targetId);

    }

    @Override
    public List<AmazonAdTargeting> listNoAsinInfo(Integer puid, Integer shopId, Long offset, Integer limit) {

        List<AmazonAdTargeting> result = new ArrayList<>();
        List<AmazonAdTargeting> targetings = amazonAdTargetingShardingDao.listNoAsinInfo(puid, shopId, offset, limit);
        if (CollectionUtils.isNotEmpty(targetings)) {
            result.addAll(targetings);
        }
        List<AmazonAdNeTargeting> amazonAdNeTargetings = amazonAdNeTargetingDao.listNoAsinInfo(puid, shopId, offset, limit);
        if (CollectionUtils.isNotEmpty(amazonAdNeTargetings)) {
            result.addAll(amazonAdNeTargetings.stream().map(e -> {
                AmazonAdTargeting keyword = converToPo(e);
                return keyword;
            }).collect(Collectors.toList()));
        }
        return result;

    }

    @Override
    public void batchSetAsinInfo(Integer puid, List<AmazonAdTargeting> needUpdateList) {

        Map<String, List<AmazonAdTargeting>> targetMap = needUpdateList.stream().collect(Collectors.groupingBy(AmazonAdTargeting::getType));
        targetMap.forEach((k, v) -> {
            if (Constants.TARGETING_TYPE_NEGATIVEASIN.equalsIgnoreCase(k)) {
                List<AmazonAdNeTargeting> collect = v.stream().map(e -> {
                    AmazonAdNeTargeting keyword = converToNe(e);
                    return keyword;
                }).collect(Collectors.toList());
                amazonAdNeTargetingDao.batchSetAsinInfo(puid, collect);
            } else {
                amazonAdTargetingShardingDao.batchSetAsinInfo(puid, needUpdateList);
            }
        });

    }

    @Override
    public int countByGroup(Integer puid, Integer shopId, String adGroupId) {

        return amazonAdTargetingShardingDao.countByGroup(puid, shopId, adGroupId);

    }

    @Override
    public List<AmazonAdTargeting> getByAdTargetIds(int puid, Integer shopId, List<String> targetIds) {

        return amazonAdTargetingShardingDao.getByAdTargetIds(puid, shopId, targetIds);

    }

    @Override
    public List<AmazonAdTargeting> getByTargetSuggestBidBatchQo(int puid, List<TargetSuggestBidBatchQo> targetList) {

        return amazonAdTargetingShardingDao.getByTargetSuggestBidBatchQo(puid, targetList);

    }

    @Override
    public List<AmazonAdTargeting> getByAdTargetIds(int puid, List<String> targetIds, String putType) {

        return amazonAdTargetingShardingDao.getByAdTargetIds(puid, targetIds, putType);

    }

    @Override
    public void batchUpdateSuggestValue(Integer puid, Collection<AmazonAdTargeting> targetings) {

        amazonAdTargetingShardingDao.batchUpdateSuggestValue(puid, targetings);

    }

    @Override
    public List<AmazonAdTargetingDto> listAllTargetingByType(int puid, TargetingPageParam param) {

        return amazonAdTargetingShardingDao.listAllTargetingByType(puid, param);

    }

    @Override
    public Page<AmazonAdNeTargetingDto> listAllNeTargetingByType(int puid, NeTargetingPageParam param) {

        return amazonAdNeTargetingDao.listAllNeTargetingByType(puid, param);

    }

    @Override
    public List<AmazonAdTargeting> listByGroupIdList(Integer puid, Integer shopId, List<String> groupIds) {

        List<AmazonAdTargeting> result = new ArrayList<>();
        List<AmazonAdTargeting> targetings = amazonAdTargetingShardingDao.listByGroupIdList(puid, shopId, groupIds);
        if (CollectionUtils.isNotEmpty(targetings)) {
            result.addAll(targetings);
        }
        List<AmazonAdNeTargeting> amazonAdNeTargetings = amazonAdNeTargetingDao.listByGroupIdList(puid, shopId, groupIds);
        if (CollectionUtils.isNotEmpty(amazonAdNeTargetings)) {
            result.addAll(amazonAdNeTargetings.stream().map(this::converToPo).collect(Collectors.toList()));
        }
        return result;

    }

    @Override
    public void updatePricing(Integer puid, Integer shopId, String targetId, Integer isPricing, Integer pricingState, int updateId) {

        amazonAdTargetingShardingDao.updatePricing(puid, shopId, targetId, isPricing, pricingState, updateId);

    }

    @Override
    public Page getPageList(Integer puid, TargetingPageParam param, Page page) {

        return amazonAdTargetingShardingDao.getPageList(puid, param, page);

    }

    @Override
    public List<AmazonAdTargeting> getList(Integer puid, TargetingPageParam param) {

        return amazonAdTargetingShardingDao.getList(puid, param);

    }

    @Override
    public List<AmazonAdTargeting> getTargetViewList(Integer puid, TargetViewParam param, boolean isAuto) {

        return amazonAdTargetingShardingDao.getTargetViewList(puid, param, isAuto);

    }


    @Override
    public List<String> getTargetViewIdList(Integer puid, TargetViewParam param, boolean isAuto) {

        return amazonAdTargetingShardingDao.getTargetViewIdList(puid, param, isAuto);

    }

    @Override
    public List<String> getTargetIdListByTargetViewHourParam(Integer puid, TargetViewHourParam param, boolean isAuto) {

        return amazonAdTargetingShardingDao.getTargetIdListByTargetViewHourParam(puid, param, isAuto);

    }

    @Override
    public void updateDataUpdateTime(Integer puid, Integer shopId, String targetId, LocalDate localDate) {

        amazonAdTargetingShardingDao.updateDataUpdateTime(puid, shopId, targetId, localDate);

    }

    @Override
    public void updateList(Integer puid, List<AmazonAdTargeting> list, String type, String targetType) {

        if (Constants.TARGETING_TYPE_NEGATIVEASIN.equalsIgnoreCase(targetType)) {
            amazonAdNeTargetingDao.updateList(puid, list, type);
        } else {
            amazonAdTargetingShardingDao.updateList(puid, list, type);
        }

    }

    @Override
    public List<String> getAsinsByCampaignId(Integer puid, Integer shopId, String campaignId) {

        return amazonAdTargetingShardingDao.getAsinsByCampaignId(puid, shopId, campaignId);

    }

    @Override
    public List<String> getArchivedItems(Integer puid, Integer shopId) {

        return amazonAdTargetingShardingDao.getArchivedItems(puid, shopId);

    }

    @Override
    public List<String> getUpdateAfterReportSyncTimeItems(Integer puid, Integer shopId, LocalDateTime syncAt) {

        return amazonAdTargetingShardingDao.getUpdateAfterReportSyncTimeItems(puid, shopId, syncAt);

    }

    @Override
    public List<AmazonAdTargeting> getListByGroupIds(Integer puid, Integer shopId, List<String> adGroupIds) {

        return amazonAdTargetingShardingDao.getListByGroupIds(puid, shopId, adGroupIds);

    }

    @Override
    public List<AmazonAdTargetingTypeBo> listTargetTypeBoByGroupIds(Integer puid, Integer shopId, List<String> adGroupIds) {

        return amazonAdTargetingShardingDao.listTargetTypeBoByGroupIds(puid, shopId, adGroupIds);

    }

    @Override
    public List<String> getTargetListsByKeyword(Integer puid, CpcQueryWordDto dto) {

        return amazonAdTargetingShardingDao.getTargetListsByKeyword(puid, dto);

    }

    @Override
    public List<String> getTargetListsByTarget(Integer puid, TargetingPageParam param) {

        return amazonAdTargetingShardingDao.getTargetListsByTarget(puid, param);

    }

    @Override
    public Integer statSumCountByAdGroupId(Integer puid, Integer shopId, List<String> status, List<String> adGroupIds) {

        return amazonAdTargetingShardingDao.statSumCountByAdGroupId(puid, shopId, status, adGroupIds);

    }

    @Override
    public Integer statSumCountByAdGroupPage(Integer puid, Integer shopId, List<String> status, GroupPageParam param) {

        return amazonAdTargetingShardingDao.statSumCountByAdGroupPage(puid, shopId, status, param);

    }

    @Override
    public List<String> getByTargetIdList(Integer puid, Integer shopId, List<String> campaignIds, List<String> groupIds, String state, String targetType, List<String> targetIdList) {

        return amazonAdTargetingShardingDao.getByTargetIdList(puid, shopId, campaignIds, groupIds, state, targetType, targetIdList);

    }

    @Override
    public Page<AmazonAdTargeting> queryAdSpAutoTarget(AdTargetStrategyParam param) {

        return amazonAdTargetingShardingDao.queryAdSpAutoTarget(param);

    }

    @Override
    public Page<AmazonAdTargeting> queryAdSpCommodityTarget(AdTargetStrategyParam param) {

        return amazonAdTargetingShardingDao.queryAdSpCommodityTarget(param);

    }

    @Override
    public Page<AmazonAdTargeting> queryAutoRuleAdSpAutoTarget(AdKeywordTargetAutoRuleParam adKeywordTargetAutoRuleParam, List<String> itemIds, List<String> similarRuleItemIdList) {

        return amazonAdTargetingShardingDao.queryAutoRuleAdSpAutoTarget(adKeywordTargetAutoRuleParam, itemIds, similarRuleItemIdList);

    }

    @Override
    public Page<AmazonAdTargeting> queryAutoRuleAdSpCommodityTarget(AdKeywordTargetAutoRuleParam adKeywordTargetAutoRuleParam, List<String> itemIds, List<String> similarRuleItemIdList) {

        return amazonAdTargetingShardingDao.queryAutoRuleAdSpCommodityTarget(adKeywordTargetAutoRuleParam, itemIds, similarRuleItemIdList);

    }

    @Override
    public AmazonAdTargeting getAutoByTargetId(int puid, Integer shopId, String marketplaceId, String targetId) {

        return amazonAdTargetingShardingDao.getAutoByTargetId(puid, shopId, marketplaceId, targetId);

    }

    @Override
    public void strategyUpdate(int puid, int shopId, ScheduleTaskFinishedVo message) {

        amazonAdTargetingShardingDao.strategyUpdate(puid, shopId, message);

    }

    @Override
    public void autoUpdate(int puid, int shopId, AdvertiseRuleTaskExecuteRecordV2Message message) {

        amazonAdTargetingShardingDao.autoUpdate(puid, shopId, message);

    }

    @Override
    public List<AmazonAdTargeting> getAutoTargetsByGroupIds(Integer puid, List<String> adGroupIds) {

        return amazonAdTargetingShardingDao.getAutoTargetsByGroupIds(puid, adGroupIds);

    }

    @Override
    public List<AmazonAdTargeting> listByGroupIdsAndTargetText(Integer puid, List<String> groupIds, List<String> targetTexts) {

        return amazonAdTargetingShardingDao.listByGroupIdsAndTargetText(puid, groupIds, targetTexts);

    }

    @Override
    public List<AmazonAdTargeting> listAllTargetByGroupIdsAndTargetText(Integer puid, List<String> adGroupIds, List<String> targetTexts) {

        return amazonAdTargetingShardingDao.listAllTargetByGroupIdsAndTargetText(puid, adGroupIds, targetTexts);

    }

    @Override
    public List<AmazonAdTargeting> autoRuleTarget(int puid, Integer shopId, List<String> campaignIds, List<String> adGroupIds, String state, String matchType, String keywordText, List<String> keywordIds, List<String> servingStatus) {

        return amazonAdTargetingShardingDao.autoRuleTarget(puid, shopId, campaignIds, adGroupIds, state, matchType, keywordText, keywordIds, servingStatus);

    }

    @Override
    public List<String> listTargetNameByTargetIds(Integer puid, Integer shopId, List<String> targetIds) {

        return amazonAdTargetingShardingDao.listTargetNameByTargetIds(puid, shopId, targetIds);

    }

    @Override
    public List<AmazonAdTargeting> getListTargetByTargetIds(Integer puid, List<Integer> shopIds, List<String> targetIds) {

        return amazonAdTargetingShardingDao.getListTargetByTargetIds(puid, shopIds, targetIds);

    }

    @Override
    public List<String> getDiagnoseCountTargetId(DiagnoseCountParam diagnoseParam, boolean auto) {

        return amazonAdTargetingShardingDao.getDiagnoseCountTargetId(diagnoseParam, auto);

    }

    @Override
    public List<AmazonAdTargeting> getListTargetByQuery(Integer puid, Integer shopId, String adGroupId, String targetValue, String selectType, String type) {

        return amazonAdTargetingShardingDao.getListTargetByQuery(puid, shopId, adGroupId, targetValue, selectType, type);

    }

    @Override
    public int deleteByPuidAndShopId(Integer puid, Integer shopId, Integer limit) {

        return amazonAdTargetingShardingDao.deleteByPuidAndShopId(puid, shopId, limit);

    }

    @Override
    public List<AdGroupTargetCountDto> countByGroupIdSet(Integer puid, Integer shopId, Set<String> groupIdSet) {

        return amazonAdTargetingShardingDao.countByGroupIdSet(puid, shopId, groupIdSet);

    }

    @Override
    public Page<TargetPageVo> getTargetPage(Integer puid, TargetingPageParam param) {

        return amazonAdTargetingShardingDao.getTargetPage(puid, param);

    }

    @Override
    public List<TargetPageVo> getTargetPageVoListByTargetIdList(Integer puid, TargetingPageParam param, List<String> targetIdList) {

        return amazonAdTargetingShardingDao.getTargetPageVoListByTargetIdList(puid, param, targetIdList);

    }

    @Override
    public List<String> getTargetIdListByParam(Integer puid, TargetingPageParam param, List<String> targetIdList) {

        return amazonAdTargetingShardingDao.getTargetIdListByParam(puid, param, targetIdList);

    }

    @Override
    public List<AdOrderBo> getTargetIdAndOrderFieldList(Integer puid, TargetingPageParam param, List<String> TargetIdList, String orderField) {

        return amazonAdTargetingShardingDao.getTargetIdAndOrderFieldList(puid, param, TargetIdList, orderField);

    }

    @Override
    public List<AdGroupTargetCountDto> countByGroupIdSet4Auto(Integer puid, Integer shopId, Set<String> groupIdSet) {

        return amazonAdTargetingShardingDao.countByGroupIdSet4Auto(puid, shopId, groupIdSet);

    }

    @Override
    public List<AmazonAdTargeting> listTargetByGroupIdList(Integer puid, Integer shopId, List<String> groupIds) {

        return amazonAdTargetingShardingDao.listTargetByGroupIdList(puid, shopId, groupIds);

    }

    @Override
    public List<AmazonAdTargeting> listTargetByGroupId(Integer puid, Integer shopId, String adGroupId) {

        return amazonAdTargetingShardingDao.listTargetByGroupId(puid, shopId, adGroupId);

    }

    @Override
    public List<AmazonAdTargeting> listByGroupIdAndItemIdList(Integer puid, Integer shopId, String adGroupId, List<String> itemIdList) {

        return amazonAdTargetingShardingDao.listByGroupIdAndItemIdList(puid, shopId, adGroupId, itemIdList);

    }


    @Override
    public AmazonAdTargeting getByPuidAndId(Integer puid, Long id, String type) {

        if (Constants.TARGETING_TYPE_NEGATIVEASIN.equalsIgnoreCase(type)) {
            AmazonAdNeTargeting byPuidAndId = amazonAdNeTargetingDao.getByPuidAndId(puid, id);
            if (byPuidAndId != null) {
                AmazonAdTargeting keyword = converToPo(byPuidAndId);
                return keyword;
            }
        } else {
            return amazonAdTargetingShardingDao.getByPuidAndId(puid, id);
        }

        return null;
    }


    @Override
    public int updateById(Integer puid, AmazonAdTargeting amazonAdTargeting, String type) {

        if (Constants.TARGETING_TYPE_NEGATIVEASIN.equalsIgnoreCase(type)) {
            AmazonAdNeTargeting amazonAdNeTargeting = converToNe(amazonAdTargeting);
            return amazonAdNeTargetingDao.updateById(puid, amazonAdNeTargeting);
        } else {
            return amazonAdTargetingShardingDao.updateById(puid, amazonAdTargeting);
        }

    }


    @Override
    public List<AmazonAdTargeting> getListByLongIdList(Integer puid, List<Long> idList, String type) {

        if (Constants.TARGETING_TYPE_NEGATIVEASIN.equalsIgnoreCase(type)) {
            List<AmazonAdNeTargeting> byPuidAndId = amazonAdNeTargetingDao.getListByLongIdList(puid, idList);
            if (CollectionUtils.isNotEmpty(byPuidAndId)) {
                return byPuidAndId.stream().map(this::converToPo).collect(Collectors.toList());
            }
            return new ArrayList<>();
        } else {
            return amazonAdTargetingShardingDao.getListByLongIdList(puid, idList);
        }

    }

    @Override
    public List<AdTargetBo> getAdTargetBoList(Integer puid, TargetingPageParam param, Integer limit) {

        return amazonAdTargetingShardingDao.getAdTargetBoList(puid, param, limit);

    }


    @Override
    public AmazonAdNeTargeting converToNe(AmazonAdTargeting amazonAdTargeting) {
        AmazonAdNeTargeting ne = new AmazonAdNeTargeting();
        ne.setId(amazonAdTargeting.getId());
        ne.setUniqueKey(amazonAdTargeting.getUniqueKey());
        ne.setPuid(amazonAdTargeting.getPuid());
        ne.setShopId(amazonAdTargeting.getShopId());
        ne.setMarketplaceId(amazonAdTargeting.getMarketplaceId());
        ne.setTargetId(amazonAdTargeting.getTargetId());
        ne.setAdGroupId(amazonAdTargeting.getAdGroupId());
        ne.setDxmGroupId(amazonAdTargeting.getDxmGroupId());
        ne.setCampaignId(amazonAdTargeting.getCampaignId());
        ne.setProfileId(amazonAdTargeting.getProfileId());
        ne.setExpressionType(amazonAdTargeting.getExpressionType());
        ne.setExpression(amazonAdTargeting.getExpression());
        ne.setResolvedExpression(amazonAdTargeting.getResolvedExpression());
        ne.setBid(amazonAdTargeting.getBid());
        ne.setType(amazonAdTargeting.getType());
        ne.setTargetingValue(amazonAdTargeting.getTargetingValue());
        ne.setTitle(amazonAdTargeting.getTitle());
        ne.setImgUrl(amazonAdTargeting.getImgUrl());
        ne.setCategoryPath(amazonAdTargeting.getCategoryPath());
        ne.setState(amazonAdTargeting.getState());
        ne.setServingStatus(amazonAdTargeting.getServingStatus());
        ne.setRangeStart(amazonAdTargeting.getRangeStart());
        ne.setRangeEnd(amazonAdTargeting.getRangeEnd());
        ne.setSuggested(amazonAdTargeting.getSuggested());
        ne.setCreateId(amazonAdTargeting.getCreateId());
        ne.setUpdateId(amazonAdTargeting.getUpdateId());
        ne.setIsPricing(amazonAdTargeting.getIsPricing());
        ne.setPricingState(amazonAdTargeting.getPricingState());
        ne.setDataUpdateTime(amazonAdTargeting.getDataUpdateTime());
        ne.setSelectType(amazonAdTargeting.getSelectType());
        ne.setExpressionList(amazonAdTargeting.getExpressionList());
        ne.setBrand(amazonAdTargeting.getBrand());
        ne.setPriceRange(amazonAdTargeting.getPriceRange());
        ne.setReviewRange(amazonAdTargeting.getReviewRange());
        ne.setIsPrime(amazonAdTargeting.getIsPrime());
        ne.setServingStatusName(amazonAdTargeting.getServingStatusName());
        ne.setServingStatusDec(amazonAdTargeting.getServingStatusDec());
        ne.setError(amazonAdTargeting.getError());
        ne.setIndex(amazonAdTargeting.getIndex());
        ne.setCreateTime(amazonAdTargeting.getCreateTime());
        ne.setUpdateTime(amazonAdTargeting.getUpdateTime());
        ne.setCreationDate(amazonAdTargeting.getCreationDate());
        return ne;
    }

    @Override
    public AmazonAdTargeting converToPo(AmazonAdNeTargeting amazonAdTargeting) {
        AmazonAdTargeting ne = new AmazonAdTargeting();
        ne.setId(amazonAdTargeting.getId());
        ne.setUniqueKey(amazonAdTargeting.getUniqueKey());
        ne.setPuid(amazonAdTargeting.getPuid());
        ne.setShopId(amazonAdTargeting.getShopId());
        ne.setMarketplaceId(amazonAdTargeting.getMarketplaceId());
        ne.setTargetId(amazonAdTargeting.getTargetId());
        ne.setAdGroupId(amazonAdTargeting.getAdGroupId());
        ne.setDxmGroupId(amazonAdTargeting.getDxmGroupId());
        ne.setCampaignId(amazonAdTargeting.getCampaignId());
        ne.setProfileId(amazonAdTargeting.getProfileId());
        ne.setExpressionType(amazonAdTargeting.getExpressionType());
        ne.setExpression(amazonAdTargeting.getExpression());
        ne.setResolvedExpression(amazonAdTargeting.getResolvedExpression());
        ne.setBid(amazonAdTargeting.getBid());
        ne.setType(amazonAdTargeting.getType());
        ne.setTargetingValue(amazonAdTargeting.getTargetingValue());
        ne.setTitle(amazonAdTargeting.getTitle());
        ne.setImgUrl(amazonAdTargeting.getImgUrl());
        ne.setCategoryPath(amazonAdTargeting.getCategoryPath());
        ne.setState(amazonAdTargeting.getState());
        ne.setServingStatus(amazonAdTargeting.getServingStatus());
        ne.setRangeStart(amazonAdTargeting.getRangeStart());
        ne.setRangeEnd(amazonAdTargeting.getRangeEnd());
        ne.setSuggested(amazonAdTargeting.getSuggested());
        ne.setCreateId(amazonAdTargeting.getCreateId());
        ne.setUpdateId(amazonAdTargeting.getUpdateId());
        ne.setIsPricing(amazonAdTargeting.getIsPricing());
        ne.setPricingState(amazonAdTargeting.getPricingState());
        ne.setDataUpdateTime(amazonAdTargeting.getDataUpdateTime());
        ne.setSelectType(amazonAdTargeting.getSelectType());
        ne.setExpressionList(amazonAdTargeting.getExpressionList());
        ne.setBrand(amazonAdTargeting.getBrand());
        ne.setPriceRange(amazonAdTargeting.getPriceRange());
        ne.setReviewRange(amazonAdTargeting.getReviewRange());
        ne.setIsPrime(amazonAdTargeting.getIsPrime());
        ne.setServingStatusName(amazonAdTargeting.getServingStatusName());
        ne.setServingStatusDec(amazonAdTargeting.getServingStatusDec());
        ne.setError(amazonAdTargeting.getError());
        ne.setIndex(amazonAdTargeting.getIndex());
        ne.setCreateTime(amazonAdTargeting.getCreateTime());
        ne.setUpdateTime(amazonAdTargeting.getUpdateTime());
        return ne;
    }


}
