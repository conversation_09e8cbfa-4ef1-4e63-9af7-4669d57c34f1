package com.meiyunji.sponsored.service.multiPlatform.tiktok.model.po;

import com.meiyunji.sponsored.common.base.BasePo;
import com.meiyunji.sponsored.common.springjdbc.DbColumn;
import com.meiyunji.sponsored.common.springjdbc.DbTable;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

@Data
@DbTable(value = "t_tiktok_gmv_max_campaign_report")
public class TikTokGmvMaxCampaignReport extends BasePo {

    @DbColumn(value = "id", autoIncrement = true, key = true)
    private Long id;

    @DbColumn("puid")
    private Integer puid;

    @DbColumn("shop_id")
    private Integer shopId;

    @DbColumn("advertiser_id")
    private String advertiserId;

    @DbColumn("store_id")
    private String storeId;

    @DbColumn("campaign_id")
    private String campaignId;

    @DbColumn("stat_time_day")
    private LocalDate statTimeDay;

    @DbColumn("cost")
    private BigDecimal cost;

    @DbColumn("orders")
    private BigDecimal orders;

    @DbColumn("cost_per_order")
    private BigDecimal costPerOrder;

    @DbColumn("gross_revenue")
    private BigDecimal grossRevenue;

    @DbColumn("roi")
    private BigDecimal roi;

    @DbColumn("net_cost")
    private BigDecimal netCost;

}
