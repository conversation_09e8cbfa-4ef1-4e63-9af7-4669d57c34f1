package com.meiyunji.sponsored.service.productPerspectiveAnalysis.service;

import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.rpc.productPerspectiveAnalysis.diagnoseManage.ListDiagnoseVo;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.vo.*;

/**
 * @author: sun<PERSON>feng
 * @email: <EMAIL>
 * @date: 2023-09-07  15:31
 */
public interface IDiagnoseManageService {

    /**
     * 分页查询
     * @param reqVo
     * @return
     */
    public Page<ListDiagnoseDto> listDiagnose(ListDiagnoseReqVo reqVo);


    /**
     * 保存
     * @param reqVo
     */
    public boolean saveDiagnose(SaveDiagnoseReqVo reqVo);


    /**
     * 删除
     * @param reqVo
     */
    public boolean deleteDiagnose(DeleteDiagnoseReqVo reqVo);

    /**
     * 定位
     * @param reqVo
     * @return
     */
    boolean posotionDiagnose(PosotionDiagnoseReqVo reqVo);
}
