package com.meiyunji.sponsored.service.autoRuleTask.api.strategy;

import com.google.api.client.util.Lists;
import com.meiyunji.sellfox.aadras.api.enumeration.AdvertiseRuleTaskTypePb;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.util.JSONUtil;
import com.meiyunji.sponsored.service.aadrasGrpcApi.AadrasApiFactory;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.autoRule.dao.*;
import com.meiyunji.sponsored.service.autoRule.enums.AutoRuleEnableStatusEnum;
import com.meiyunji.sponsored.service.autoRule.enums.AutoRuleSubmitAdTypeEnum;
import com.meiyunji.sponsored.service.autoRule.po.AdvertiseAutoRuleStatus;
import com.meiyunji.sponsored.service.autoRule.po.AdvertiseAutoRuleTemplate;
import com.meiyunji.sponsored.service.autoRule.vo.AutoRuleConsistencyDto;
import com.meiyunji.sponsored.service.autoRule.vo.PerformOperationJson;
import com.meiyunji.sponsored.service.autoRule.vo.RemoveAutoRuleVo;
import com.meiyunji.sponsored.service.autoRule.vo.UpdateAutoRuleVo;
import com.meiyunji.sponsored.service.autoRuleTask.api.AutoRuleTaskApi;
import com.meiyunji.sponsored.service.autoRuleTask.enums.ChildrenItemType;
import com.meiyunji.sponsored.service.autoRuleTask.vo.DeleteAutoRuleResponseVo;
import com.meiyunji.sponsored.service.autoRuleTask.vo.UpdateAutoRuleResponseVo;
import com.meiyunji.sponsored.service.cpc.dao.*;
import com.meiyunji.sponsored.service.cpc.po.*;
import com.meiyunji.sponsored.service.cpc.service.IAmazonAdKeywordDaoRoutingService;
import com.meiyunji.sponsored.service.enums.AutoRuleItemTypeEnum;
import com.meiyunji.sponsored.service.enums.AutoRuleOperationTypeEnum;
import com.meiyunji.sponsored.service.enums.AutoRuleTargetTypeEnum;
import com.meiyunji.sponsored.service.strategy.vo.OriginValueVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: chenzimeng
 * @email: <EMAIL>
 * @date: 2024-01-17  15:46
 */
@Service
@Slf4j
public class AutoRuleTaskAllApi implements AutoRuleTaskApi {

    @Autowired
    private IAdvertiseAutoRuleStatusDao advertiseAutoRuleStatusDao;
    @Autowired
    private IAmazonAdGroupDao amazonAdGroupDao;
    @Autowired
    private IAmazonSbAdGroupDao amazonSbAdGroupDao;
    @Autowired
    private IAmazonSdAdGroupDao amazonSdAdGroupDao;
    @Autowired
    private AadrasApiFactory aadrasApiFactory;
    @Autowired
    private IAdvertiseAutoRuleStatusDeleteDao advertiseAutoRuleStatusDeleteDao;
    @Autowired
    private IAmazonAdKeywordDaoRoutingService amazonAdKeywordDaoRoutingService;
    @Autowired
    private IAmazonSbAdKeywordDao amazonSbAdKeywordDao;
    @Autowired
    private IAmazonSbAdTargetingDao amazonSbAdTargetingDao;
    @Autowired
    private IAmazonSdAdTargetingDao amazonSdAdTargetingDao;
    @Autowired
    private IAmazonAdTargetingShardingDao amazonAdTargetingShardingDao;
    @Autowired
    private IAmazonAdKeywordShardingDao amazonAdKeywordShardingDao;
    @Autowired
    private IScVcShopAuthDao shopAuthDao;

    @Override
    public List<UpdateAutoRuleResponseVo> updateAutoRule(Integer puid, Integer updateUid, Long templateId, List<UpdateAutoRuleVo> updateAutoRuleVoList, AdvertiseAutoRuleTemplate template, String traceId) {
        //更新受控对象：0 全部更新 1 批量更新
        // 6 批量应用实时竞价 8 批量应用实时预算 (6和8自动化规则暂时没有)
        List<UpdateAutoRuleResponseVo> responseVoList = Lists.newArrayList();
        try {
            //批量查询受控对象
            List<Long> statusIdList = new ArrayList<>();
            updateAutoRuleVoList.forEach(x -> statusIdList.add(x.getStatusId()));
            List<AdvertiseAutoRuleStatus> statusList = advertiseAutoRuleStatusDao.getByStatusIds(puid, statusIdList);
            //转map和收集shopId
            Map<Long, AdvertiseAutoRuleStatus> statusMap = new HashMap<>();
            Set<Integer> shopIdSet = new HashSet<>();
            statusList.forEach(x -> {
                statusMap.put(x.getId(), x);
                shopIdSet.add(x.getShopId());
            });

            List<ShopAuth> shopAuthList = shopAuthDao.listAllByIds(puid, new ArrayList<>(shopIdSet));
            Map<Integer, ShopAuth> shopMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(shopAuthList)) {
                shopMap = shopAuthList.stream().filter(Objects::nonNull).collect(Collectors.toMap(ShopAuth::getId, Function.identity()));
            }

            //遍历处理,可以整体优化成批处理
            for (UpdateAutoRuleVo updateVo: updateAutoRuleVoList) {
                UpdateAutoRuleResponseVo responseVo = new UpdateAutoRuleResponseVo();
                responseVo.setStatusId(updateVo.getStatusId());
                AdvertiseAutoRuleStatus advertiseAutoRuleStatus = statusMap.get(updateVo.getStatusId());
                if (Objects.isNull(advertiseAutoRuleStatus)) {
                    log.info("puid:{} statusId:{} 自动化规则修改受控对象找不到", puid, responseVo.getStatusId());
                    responseVo.setIsRetry(1);
                    responseVo.setMsg("当前受控对象不存在");
                    responseVoList.add(responseVo);
                    continue;
                }

                ShopAuth shopAuth = shopMap.get(advertiseAutoRuleStatus.getShopId());
                if (Objects.isNull(shopAuth)) {
                    log.info("puid:{} shopId:{} statusId:{} 店铺信息不存在", puid, advertiseAutoRuleStatus.getShopId(), responseVo.getStatusId());
                    responseVo.setIsRetry(1);
                    responseVo.setMsg("当前受控对象店铺不存在");
                    responseVoList.add(responseVo);
                    continue;
                }

                //受控对象类型
                String itemType = advertiseAutoRuleStatus.getItemType();

                //如果是启用，需要更新广独受控对象字段并推送到aadras
                if (AutoRuleEnableStatusEnum.ENABLED.getCode().equals(updateVo.getStatus())) {

                    //更新广独受控对象信息
                    boolean b = updateAutoRule4EnableStatus(puid, updateUid, templateId, updateVo, responseVo, advertiseAutoRuleStatus, template);
                    if (!b) {
                        //更新失败，添加到返回
                        responseVoList.add(responseVo);
                        continue;
                    }

                    //补充各受控对象类型（投放和搜索词）数据，并推送至计算服务更新

                    //投放数据
                    if (AutoRuleItemTypeEnum.TARGET.getName().equals(template.getItemType())) {
                        setStatusBaseInfo4Target(advertiseAutoRuleStatus);
                    }

                    //搜索词
                    if (AutoRuleItemTypeEnum.GROUP_SEARCH_QUERY.getName().equals(advertiseAutoRuleStatus.getItemType())) {
                        setStatusBaseInfo4GroupSearchQuery(advertiseAutoRuleStatus, template.getPerformOperation());
                        //搜索词单独受控，需要设置itemType以便通知搜索词单独受控对应的aadras接口更新
                        if (ChildrenItemType.CHILDREN_SEARCH_QUERY.name().equals(advertiseAutoRuleStatus.getChildrenItemType())) {
                            itemType = AdvertiseRuleTaskTypePb.AdvertiseRuleTaskType.SEARCH_QUERY.name();
                        }
                    }

                    //模板属性set
                    advertiseAutoRuleStatus.setTimeType(template.getTimeType());
                    advertiseAutoRuleStatus.setTimeRule(template.getTimeRule());
                    advertiseAutoRuleStatus.setStartDate(template.getStartDate());
                    advertiseAutoRuleStatus.setEndDate(template.getEndDate());
                    advertiseAutoRuleStatus.setRule(template.getRule());
                    advertiseAutoRuleStatus.setPerformOperation(template.getPerformOperation());
                    advertiseAutoRuleStatus.setExecuteType(template.getExecuteType());
                    advertiseAutoRuleStatus.setSetRelation(template.getSetRelation());
                    advertiseAutoRuleStatus.setCallbackState(template.getCallbackState());
                    advertiseAutoRuleStatus.setCallbackOperate(template.getCallbackOperate());
                    advertiseAutoRuleStatus.setExecuteTimeSpaceUnit(template.getExecuteTimeSpaceUnit());
                    advertiseAutoRuleStatus.setExecuteTimeSpaceValue(template.getExecuteTimeSpaceValue());
                    advertiseAutoRuleStatus.setMessageReminderType(template.getMessageReminderType());

                    //推送至计算服务更新
                    aadrasApiFactory.getAadrasApi(AdvertiseRuleTaskTypePb.AdvertiseRuleTaskType.valueOf(itemType)).setAutoRuleTask(advertiseAutoRuleStatus.getTaskId(), advertiseAutoRuleStatus, shopAuth);
                } else if (AutoRuleEnableStatusEnum.DISABLED.getCode().equals(updateVo.getStatus())) {
                    //如果受控对象状态为禁用，更新广独受控对象字段
                    boolean b = updateAutoRule4DisableStatus(puid, updateUid, templateId, updateVo, responseVo, advertiseAutoRuleStatus, template);
                    if (!b) {
                        //更新失败，添加到返回
                        responseVoList.add(responseVo);
                        continue;
                    }
                }
            }
        } catch (Exception e) {
            log.error("traceId:{} puid={} 自动化规则任务修改异常", traceId, puid, e);
        }

        return responseVoList;
    }

    /**
     * 更新受控对象，受控对象为投放set基础信息
     * @param status
     */
    private void setStatusBaseInfo4Target(AdvertiseAutoRuleStatus status) {
        String keywordText = "";
        String campaignId = "";
        String adGroupId = "";
        if (ChildrenItemType.CHILDREN_TARGET_GROUP.name().equals(status.getChildrenItemType())) {
            if (AutoRuleSubmitAdTypeEnum.SD.getCode().equals(status.getAdType())) {
                AmazonSdAdGroup amazonSdAdGroup = amazonSdAdGroupDao.getByGroupId(status.getPuid(), status.getShopId(), status.getItemId());
                if (Objects.nonNull(amazonSdAdGroup)) {
                    campaignId = amazonSdAdGroup.getCampaignId();
                    adGroupId = amazonSdAdGroup.getAdGroupId();
                }
            }
            if (AutoRuleSubmitAdTypeEnum.SP.getCode().equals(status.getAdType())) {
                AmazonAdGroup amazonAdGroup = amazonAdGroupDao.getByAdGroupId(status.getPuid(), status.getShopId(), status.getItemId());
                if (Objects.nonNull(amazonAdGroup)) {
                    campaignId = amazonAdGroup.getCampaignId();
                    adGroupId = amazonAdGroup.getAdGroupId();
                }
            }
            if (AutoRuleSubmitAdTypeEnum.SB.getCode().equals(status.getAdType())) {
                AmazonSbAdGroup amazonSbAdGroup = amazonSbAdGroupDao.getByGroupId(status.getPuid(), status.getShopId(), status.getItemId());
                if (Objects.nonNull(amazonSbAdGroup)) {
                    campaignId = amazonSbAdGroup.getCampaignId();
                    adGroupId = amazonSbAdGroup.getAdGroupId();
                }
            }
        } else {
            if (AutoRuleSubmitAdTypeEnum.SD.getCode().equals(status.getAdType())) {
                AmazonSdAdTargeting target = amazonSdAdTargetingDao.getbyTargetId(status.getPuid(), status.getShopId(), status.getItemId());
                if (Objects.nonNull(target)) {
                    keywordText = target.getTargetText();
                    campaignId = target.getCampaignId();
                    adGroupId = target.getAdGroupId();
                    if (StringUtils.isBlank(campaignId)) {
                        AmazonSdAdGroup amazonSdAdGroup = amazonSdAdGroupDao.getByGroupId(status.getPuid(), status.getShopId(), target.getAdGroupId());
                        if (Objects.nonNull(amazonSdAdGroup)) {
                            campaignId = amazonSdAdGroup.getCampaignId();
                        }
                    }
                }
            } else if (AutoRuleSubmitAdTypeEnum.SB.getCode().equals(status.getAdType())) {
                if (AutoRuleTargetTypeEnum.keywordTarget.getTargetType().equals(status.getTargetType())) {
                    AmazonSbAdKeyword keyword = amazonSbAdKeywordDao.getByKeywordId(status.getPuid(), status.getShopId(), status.getItemId());
                    if (Objects.nonNull(keyword)) {
                        keywordText = keyword.getKeywordText();
                        campaignId = keyword.getCampaignId();
                        adGroupId = keyword.getAdGroupId();
                    }
                } else if (AutoRuleTargetTypeEnum.productTarget.getTargetType().equals(status.getTargetType())) {
                    AmazonSbAdTargeting target = amazonSbAdTargetingDao.getByTargetId(status.getPuid(), status.getShopId(), status.getItemId());
                    if (Objects.nonNull(target)) {
                        keywordText = target.getTargetText();
                        campaignId = target.getCampaignId();
                        adGroupId = target.getAdGroupId();
                    }
                }
            } else if (AutoRuleSubmitAdTypeEnum.SP.getCode().equals(status.getAdType())) {
                if (AutoRuleTargetTypeEnum.keywordTarget.getTargetType().equals(status.getTargetType())) {
                    AmazonAdKeyword keyword = amazonAdKeywordShardingDao.getByKeywordId(status.getPuid(), status.getShopId(), status.getItemId());
                    if (Objects.nonNull(keyword)) {
                        keywordText = keyword.getKeywordText();
                        campaignId = keyword.getCampaignId();
                        adGroupId = keyword.getAdGroupId();
                    }
                } else if (AutoRuleTargetTypeEnum.productTarget.getTargetType().equals(status.getTargetType()) || AutoRuleTargetTypeEnum.autoTarget.getTargetType().equals(status.getTargetType())) {
                    AmazonAdTargeting target = amazonAdTargetingShardingDao.getByAdTargetId(status.getPuid(), status.getShopId(), status.getItemId());
                    if (Objects.nonNull(target)) {
                        keywordText = target.getTargetingValue();
                        campaignId = target.getCampaignId();
                        adGroupId = target.getAdGroupId();
                    }
                }
            }
        }

        //set以下3个属性
        if (StringUtils.isNotBlank(keywordText)) {
            status.setKeywordText(keywordText);
        }
        if (StringUtils.isNotBlank(campaignId)) {
            status.setCampaignId(campaignId);
        }
        if (StringUtils.isNotBlank(adGroupId)) {
            status.setAdGroupId(adGroupId);
        }
    }

    /**
     * 更新受控对象，受控对象为搜索词set基础信息
     * @param status
     */
    private void setStatusBaseInfo4GroupSearchQuery(AdvertiseAutoRuleStatus status, String performOperation) {
        //搜索词单独受控
        if (ChildrenItemType.CHILDREN_SEARCH_QUERY.name().equals(status.getChildrenItemType())) {
            //sp组属性set
            if (AutoRuleSubmitAdTypeEnum.SP.getCode().equals(status.getAdType())) {
                AmazonAdGroup amazonAdGroup = amazonAdGroupDao.getByAdGroupId(status.getPuid(), status.getShopId(), status.getMarketplaceId(), status.getQueryAdGroupId());
                if (amazonAdGroup != null) {
                    status.setGroupType(amazonAdGroup.getAdGroupType());
                    status.setTarGroupType(amazonAdGroup.getAdGroupType());
                    status.setCampaignId(amazonAdGroup.getCampaignId());
                    status.setAdGroupId(amazonAdGroup.getAdGroupId());
                    status.setTargetAdType(AutoRuleSubmitAdTypeEnum.SP.getCode());
                    status.setDefaultBid(BigDecimal.valueOf(amazonAdGroup.getDefaultBid()));
                }
            }
            //sb组属性set
            if (AutoRuleSubmitAdTypeEnum.SB.getCode().equals(status.getAdType())) {
                AmazonSbAdGroup amazonSbAdGroup = amazonSbAdGroupDao.getByGroupId(status.getPuid(), status.getShopId(), status.getQueryAdGroupId());
                if (amazonSbAdGroup != null) {
                    status.setGroupType(amazonSbAdGroup.getAdGroupType());
                    status.setTarGroupType(amazonSbAdGroup.getAdGroupType());
                    status.setCampaignId(amazonSbAdGroup.getCampaignId());
                    status.setAdGroupId(amazonSbAdGroup.getAdGroupId());
                    status.setTargetAdType(AutoRuleSubmitAdTypeEnum.SB.getCode());
                    status.setDefaultBid(amazonSbAdGroup.getBid());
                }
            }
            //执行动作相关set
            if (StringUtils.isNotBlank(performOperation)) {
                PerformOperationJson performOperationJson = JSONUtil.jsonToArray(performOperation, PerformOperationJson.class).get(0);
                status.setOperationType(AutoRuleOperationTypeEnum.getOperationType(performOperationJson.getRuleAction()));
                //添加到指定广告组下投放
                if (AutoRuleOperationTypeEnum.addTarget.getRuleAction().equals(performOperationJson.getRuleAction())
                        && PerformOperationJson.AppointAdGroupTypeEnum.DESIGNATED_AD_GROUP.getValue().equals(performOperationJson.getAppointAdGroupType())) {
                    //查询sp
                    AmazonAdGroup queryAmazonAdGroup = amazonAdGroupDao.getByAdGroupId(status.getPuid(), status.getShopId(), status.getMarketplaceId(), performOperationJson.getAdGroupId());
                    if (queryAmazonAdGroup != null) {
                        status.setTargetAdType(AutoRuleSubmitAdTypeEnum.SP.getCode());
                        status.setTarGroupType(queryAmazonAdGroup.getAdGroupType());
                        status.setDefaultBid(BigDecimal.valueOf(queryAmazonAdGroup.getDefaultBid()));
                    } else {
                        //sp查询不到则查一下sb
                        AmazonSbAdGroup queryAmazonSbAdGroup = amazonSbAdGroupDao.getByGroupId(status.getPuid(), status.getShopId(), performOperationJson.getAdGroupId());
                        if (queryAmazonSbAdGroup != null) {
                            status.setTargetAdType(AutoRuleSubmitAdTypeEnum.SB.getCode());
                            status.setTarGroupType(queryAmazonSbAdGroup.getAdGroupType());
                            status.setDefaultBid(queryAmazonSbAdGroup.getBid());
                        }
                    }
                }
            }
        } else {
            //搜索词组受控
            //sp组属性set
            if (AutoRuleSubmitAdTypeEnum.SP.getCode().equals(status.getAdType())) {
                AmazonAdGroup amazonAdGroup = amazonAdGroupDao.getByAdGroupId(status.getPuid(), status.getShopId(), status.getMarketplaceId(), status.getItemId());
                if (amazonAdGroup != null) {
                    status.setTargetAdType(AutoRuleSubmitAdTypeEnum.SP.getCode());
                    status.setGroupType(amazonAdGroup.getAdGroupType());
                    status.setTarGroupType(amazonAdGroup.getAdGroupType());
                    status.setCampaignId(amazonAdGroup.getCampaignId());
                    status.setAdGroupId(status.getQueryAdGroupId());
                    status.setDefaultBid(BigDecimal.valueOf(amazonAdGroup.getDefaultBid()));
                }
            } else if (AutoRuleSubmitAdTypeEnum.SB.getCode().equals(status.getAdType())) {
                //sb组属性set
                AmazonSbAdGroup amazonSbAdGroup = amazonSbAdGroupDao.getByGroupId(status.getPuid(), status.getShopId(), status.getItemId());
                if (amazonSbAdGroup != null) {
                    status.setTargetAdType(AutoRuleSubmitAdTypeEnum.SB.getCode());
                    status.setGroupType(amazonSbAdGroup.getAdGroupType());
                    status.setTarGroupType(amazonSbAdGroup.getAdGroupType());
                    status.setCampaignId(amazonSbAdGroup.getCampaignId());
                    status.setAdGroupId(status.getQueryAdGroupId());
                    status.setDefaultBid(amazonSbAdGroup.getBid());
                }
            }

            //执行动作相关set
            if (StringUtils.isNotBlank(performOperation)) {
                PerformOperationJson performOperationJson = JSONUtil.jsonToArray(performOperation, PerformOperationJson.class).get(0);
                status.setOperationType(AutoRuleOperationTypeEnum.getOperationType(performOperationJson.getRuleAction()));

                //添加到指定广告组下投放
                if (AutoRuleOperationTypeEnum.addTarget.getRuleAction().equals(performOperationJson.getRuleAction())
                        && PerformOperationJson.AppointAdGroupTypeEnum.DESIGNATED_AD_GROUP.getValue().equals(performOperationJson.getAppointAdGroupType())) {
                    //查询sp
                    AmazonAdGroup queryAmazonAdGroup = amazonAdGroupDao.getByAdGroupId(status.getPuid(), status.getShopId(), status.getMarketplaceId(), performOperationJson.getAdGroupId());
                    if (queryAmazonAdGroup != null) {
                        status.setTargetAdType(AutoRuleSubmitAdTypeEnum.SP.getCode());
                        status.setTarGroupType(queryAmazonAdGroup.getAdGroupType());
                        status.setDefaultBid(BigDecimal.valueOf(queryAmazonAdGroup.getDefaultBid()));
                    } else {
                        //sp查询不到则查一下sb
                        AmazonSbAdGroup queryAmazonSbAdGroup = amazonSbAdGroupDao.getByGroupId(status.getPuid(), status.getShopId(), performOperationJson.getAdGroupId());
                        if (queryAmazonSbAdGroup != null) {
                            status.setTargetAdType(AutoRuleSubmitAdTypeEnum.SB.getCode());
                            status.setTarGroupType(queryAmazonSbAdGroup.getAdGroupType());
                            status.setDefaultBid(queryAmazonSbAdGroup.getBid());
                        }
                    }
                }
            }
        }
    }

    /**
     * 更新受控对象，受控对象状态为启用时
     * @return
     */
    private boolean updateAutoRule4EnableStatus(Integer puid,
                                                Integer updateUid,
                                                Long templateId,
                                                UpdateAutoRuleVo updateVo,
                                                UpdateAutoRuleResponseVo responseVo,
                                                AdvertiseAutoRuleStatus advertiseAutoRuleStatus,
                                                AdvertiseAutoRuleTemplate template) {

        //抢排名
        if (AutoRuleItemTypeEnum.KEYWORD_TARGET.getName().equals(template.getItemType())) {
            AmazonAdKeyword amazonAdKeyword = amazonAdKeywordDaoRoutingService.getByKeywordId(puid, advertiseAutoRuleStatus.getShopId(), advertiseAutoRuleStatus.getItemId());
            if (amazonAdKeyword != null) {
                advertiseAutoRuleStatus.setKeywordText(amazonAdKeyword.getKeywordText());
            }
            advertiseAutoRuleStatus.setAdDataRule(template.getAdDataRule());
            advertiseAutoRuleStatus.setDesiredPosition(template.getDesiredPosition());
            advertiseAutoRuleStatus.setAdDataOperate(template.getAdDataOperate());
            advertiseAutoRuleStatus.setAutoPriceRule(template.getAutoPriceRule());
            advertiseAutoRuleStatus.setAutoPriceOperate(template.getAutoPriceOperate());
            advertiseAutoRuleStatus.setBiddingCallbackOperate(template.getBiddingCallbackOperate());
            advertiseAutoRuleStatus.setCheckFrequency(template.getCheckFrequency());
            advertiseAutoRuleStatus.setPostalCodeSettings(template.getPostalCodeSettings());
            if (updateVo.getBiddingValue() != null) {
                if (updateVo.getBiddingValue().equals(BigDecimal.ZERO)) {
                    log.info("puid:{} statusId:{} 当前竞价值不能为0", puid, responseVo.getStatusId());
                    responseVo.setIsRetry(1);
                    responseVo.setMsg("当前竞价值不能为0");
                    return false;
                }
                OriginValueVo originValueVo = new OriginValueVo();
                originValueVo.setBiddingValue(updateVo.getBiddingValue());
                if (Objects.nonNull(updateVo.getPlacementTopBidRatio())) {
                    originValueVo.setPlacementTopBidRatio(updateVo.getPlacementTopBidRatio());
                }
                advertiseAutoRuleStatus.setOriginValue(JSONUtil.objectToJson(originValueVo));
            }
            advertiseAutoRuleStatus.setVersion(template.getVersion());
            advertiseAutoRuleStatus.setUpdateUid(updateUid);
            advertiseAutoRuleStatus.setChooseTimeType(template.getChooseTimeType());
            advertiseAutoRuleStatusDao.updateKeywordCardStatus(templateId,advertiseAutoRuleStatus);
        } else {
            if (StringUtils.isNotBlank(template.getPerformOperation())) {
                PerformOperationJson performOperationJson = JSONUtil.jsonToArray(template.getPerformOperation(), PerformOperationJson.class).get(0);
                advertiseAutoRuleStatus.setOperationType(AutoRuleOperationTypeEnum.getOperationType(performOperationJson.getRuleAction()));
            }
            if (advertiseAutoRuleStatus.getOperationType() != null) {
                if (advertiseAutoRuleStatusDao.isSimilarRule(puid, advertiseAutoRuleStatus.getItemId(), advertiseAutoRuleStatus.getOperationType(), templateId) > 0) {
                    advertiseAutoRuleStatus.setHasSimilarRule(1);
                } else {
                    advertiseAutoRuleStatus.setHasSimilarRule(0);
                }
            }
            advertiseAutoRuleStatusDao.updateStatus(puid, templateId,
                    updateVo.getStatusId(), updateVo.getStatus(),
                    advertiseAutoRuleStatus.getOperationType(), advertiseAutoRuleStatus.getHasSimilarRule(), template,updateUid);
        }

        return true;
    }


    /**
     * 更新受控对象，受控对象状态为禁用时
     * @param puid
     * @param updateVo
     * @param responseVo
     * @param advertiseAutoRuleStatus
     * @param template
     * @return
     */
    private boolean updateAutoRule4DisableStatus(Integer puid,
                                                 Integer updateUid,
                                                 Long templateId,
                                                 UpdateAutoRuleVo updateVo,
                                                 UpdateAutoRuleResponseVo responseVo,
                                                 AdvertiseAutoRuleStatus advertiseAutoRuleStatus,
                                                 AdvertiseAutoRuleTemplate template) {
        //抢排名
        if (AutoRuleItemTypeEnum.KEYWORD_TARGET.getName().equals(template.getItemType())) {
            advertiseAutoRuleStatus.setTimeType(template.getTimeType());
            advertiseAutoRuleStatus.setTimeRule(template.getTimeRule());
            advertiseAutoRuleStatus.setStartDate(template.getStartDate());
            advertiseAutoRuleStatus.setEndDate(template.getEndDate());
            advertiseAutoRuleStatus.setAdDataRule(template.getAdDataRule());
            advertiseAutoRuleStatus.setDesiredPosition(template.getDesiredPosition());
            advertiseAutoRuleStatus.setAdDataOperate(template.getAdDataOperate());
            advertiseAutoRuleStatus.setAutoPriceRule(template.getAutoPriceRule());
            advertiseAutoRuleStatus.setAutoPriceOperate(template.getAutoPriceOperate());
            advertiseAutoRuleStatus.setBiddingCallbackOperate(template.getBiddingCallbackOperate());
            advertiseAutoRuleStatus.setCheckFrequency(template.getCheckFrequency());
            advertiseAutoRuleStatus.setPostalCodeSettings(template.getPostalCodeSettings());
            if (updateVo.getBiddingValue() != null) {
                if (updateVo.getBiddingValue().equals(BigDecimal.ZERO)) {
                    log.info("puid:{} statusId:{} 当前竞价值不能为0", puid, responseVo.getStatusId());
                    responseVo.setIsRetry(1);
                    responseVo.setMsg("当前竞价值不能为0");
                    return false;
                }
                OriginValueVo originValueVo = new OriginValueVo();
                originValueVo.setBiddingValue(updateVo.getBiddingValue());
                advertiseAutoRuleStatus.setOriginValue(JSONUtil.objectToJson(originValueVo));
            }
            advertiseAutoRuleStatus.setVersion(template.getVersion());
            advertiseAutoRuleStatus.setUpdateUid(updateUid);
            advertiseAutoRuleStatus.setChooseTimeType(template.getChooseTimeType());
            advertiseAutoRuleStatusDao.updateKeywordCardStatus(templateId, advertiseAutoRuleStatus);
        } else {
            //其他受控对象：更新
            if (StringUtils.isNotBlank(template.getPerformOperation())) {
                PerformOperationJson performOperationJson = JSONUtil.jsonToArray(template.getPerformOperation(), PerformOperationJson.class).get(0);
                advertiseAutoRuleStatus.setOperationType(AutoRuleOperationTypeEnum.getOperationType(performOperationJson.getRuleAction()));
            }
            if (advertiseAutoRuleStatus.getOperationType() != null) {
                if (advertiseAutoRuleStatusDao.isSimilarRule(puid, advertiseAutoRuleStatus.getItemId(), advertiseAutoRuleStatus.getOperationType(), templateId) > 0) {
                    advertiseAutoRuleStatus.setHasSimilarRule(1);
                } else {
                    advertiseAutoRuleStatus.setHasSimilarRule(0);
                }
            }
            advertiseAutoRuleStatusDao.updateStatus(puid, templateId,
                    updateVo.getStatusId(), updateVo.getStatus(),
                    advertiseAutoRuleStatus.getOperationType(), advertiseAutoRuleStatus.getHasSimilarRule(), template, updateUid);
        }

        return true;
    }

    @Override
    public List<UpdateAutoRuleResponseVo> updateAutoRuleBid(Integer puid, Integer updateUid, Long templateId, List<UpdateAutoRuleVo> updateAutoRuleVoList, String traceId) {
        List<UpdateAutoRuleResponseVo> responseVoList = Lists.newArrayList();
        try {
            //批量查询受控对象
            List<Long> statusIdList = new ArrayList<>();
            updateAutoRuleVoList.forEach(x -> statusIdList.add(x.getStatusId()));
            List<AdvertiseAutoRuleStatus> statusList = advertiseAutoRuleStatusDao.getByStatusIds(puid, statusIdList);
            Map<Long, AdvertiseAutoRuleStatus> statusMap = new HashMap<>();
            Set<Integer> shopIdSet = new HashSet<>();
            statusList.forEach(x -> {
                statusMap.put(x.getId(), x);
                shopIdSet.add(x.getShopId());
            });

            List<ShopAuth> shopAuthList = shopAuthDao.listAllByIds(puid, new ArrayList<>(shopIdSet));
            Map<Integer, ShopAuth> shopMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(shopAuthList)) {
                shopMap = shopAuthList.stream().filter(Objects::nonNull).collect(Collectors.toMap(ShopAuth::getId, Function.identity()));
            }

            //遍历处理
            for (UpdateAutoRuleVo updateVo: updateAutoRuleVoList) {
                UpdateAutoRuleResponseVo responseVo = new UpdateAutoRuleResponseVo();
                responseVo.setStatusId(updateVo.getStatusId());
                AdvertiseAutoRuleStatus status = statusMap.get(updateVo.getStatusId());

                //数据校验
                if (Objects.isNull(status)) {
                    responseVo.setMsg("当前受控对象不存在");
                    responseVo.setIsRetry(1);
                    responseVoList.add(responseVo);
                    continue;
                }

                ShopAuth shopAuth = shopMap.get(status.getShopId());
                if (Objects.isNull(shopAuth)) {
                    log.info("puid:{} shopId:{} statusId:{} 店铺信息不存在", puid, status.getShopId(), updateVo.getStatusId());
                    responseVo.setMsg("当前受控对象店铺不存在");
                    responseVo.setIsRetry(1);
                    responseVoList.add(responseVo);
                    continue;
                }

                if (BigDecimal.ZERO.equals(updateVo.getBudgetValue())) {
                    responseVo.setMsg("当前预算值不能为0");
                    responseVo.setIsRetry(1);
                    responseVoList.add(responseVo);
                    continue;
                }

                if (BigDecimal.ZERO.equals(updateVo.getDefaultBiddingValue())) {
                    responseVo.setMsg("当前默认竞价值不能为0");
                    responseVo.setIsRetry(1);
                    responseVoList.add(responseVo);
                    continue;
                }

                if (BigDecimal.ZERO.equals(updateVo.getBiddingValue())) {
                    responseVo.setMsg("当前竞价值不能为0");
                    responseVo.setIsRetry(1);
                    responseVoList.add(responseVo);
                    continue;
                }

                //受控对象状态为启用
                if (AutoRuleEnableStatusEnum.ENABLED.getCode().equals(updateVo.getStatus())) {

                    //设置keywordText? 没搞懂为什么要set keywordText,但是targeting又没set
                    //此处应该是用于aadras添加到否定，无需反查
                    if (AutoRuleTargetTypeEnum.keywordTarget.getTargetType().equals(status.getTargetType())) {
                        if (AutoRuleSubmitAdTypeEnum.SP.getCode().equals(status.getAdType())) {
                            AmazonAdKeyword amazonAdKeyword = amazonAdKeywordShardingDao.getByKeywordId(puid, status.getShopId(), status.getItemId());
                            if (amazonAdKeyword != null) {
                                status.setKeywordText(amazonAdKeyword.getKeywordText());
                            }
                        } else if (AutoRuleSubmitAdTypeEnum.SB.getCode().equals(status.getAdType())) {
                            AmazonSbAdKeyword amazonAdKeyword = amazonSbAdKeywordDao.getByKeywordId(puid, status.getShopId(), status.getItemId());
                            if (amazonAdKeyword != null) {
                                status.setKeywordText(amazonAdKeyword.getKeywordText());
                            }
                        }
                    }

                    OriginValueVo originValueVo = new OriginValueVo();
                    originValueVo.setBiddingValue(updateVo.getBiddingValue());
                    originValueVo.setBudgetValue(updateVo.getBudgetValue());
                    originValueVo.setDefaultBiddingValue(updateVo.getDefaultBiddingValue());
                    originValueVo.setPlacementTopBidRatio(updateVo.getPlacementTopBidRatio());
                    status.setOriginValue(JSONUtil.objectToJson(originValueVo));
                    status.setUpdateUid(updateUid);
                    advertiseAutoRuleStatusDao.updateKeywordCardBid(templateId, status);
                    //推送至计算服务更新
                    aadrasApiFactory.getAadrasApi(AdvertiseRuleTaskTypePb.AdvertiseRuleTaskType.valueOf(status.getItemType()))
                            .setAutoRuleTask(status.getTaskId(), status, shopAuth);
                } else if (AutoRuleEnableStatusEnum.DISABLED.getCode().equals(updateVo.getStatus())) {
                    //受控对象状态为禁用
                    OriginValueVo originValueVo = new OriginValueVo();
                    originValueVo.setBiddingValue(updateVo.getBiddingValue());
                    originValueVo.setBudgetValue(updateVo.getBudgetValue());
                    originValueVo.setDefaultBiddingValue(updateVo.getDefaultBiddingValue());
                    status.setOriginValue(JSONUtil.objectToJson(originValueVo));
                    status.setUpdateUid(updateUid);
                    advertiseAutoRuleStatusDao.updateKeywordCardBid(templateId, status);
                }
            }
        } catch (Exception e) {
            log.error("traceId:{} puid={} 自动化规则任务修改异常", traceId, puid, e);
        }
        return responseVoList;
    }

    @Override
    public List<UpdateAutoRuleResponseVo> updateAutoRuleStatus(Integer puid, Integer updateUid, Long templateId, List<Long> statusIdList, String enableStatus, String loginIp, Integer uid, String traceId) {
        //启用/禁用受控对象：12 批量暂停 13 批量开启 14 全部暂停 15 全部开启
        List<UpdateAutoRuleResponseVo> responseVoList = Lists.newArrayList();
        try {
            //批量查询受控对象
            List<AdvertiseAutoRuleStatus> statusList = advertiseAutoRuleStatusDao.getByStatusIds(puid,statusIdList);
            Map<Long, AdvertiseAutoRuleStatus> statusMap = new HashMap<>();
            Set<Integer> shopIdSet = new HashSet<>();
            statusList.forEach(x -> {
                statusMap.put(x.getId(), x);
                shopIdSet.add(x.getShopId());
            });

            List<ShopAuth> shopAuthList = shopAuthDao.listAllByIds(puid, new ArrayList<>(shopIdSet));
            Map<Integer, ShopAuth> shopMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(shopAuthList)) {
                shopMap = shopAuthList.stream().filter(Objects::nonNull).collect(Collectors.toMap(ShopAuth::getId, Function.identity()));
            }

            //遍历处理
            for (Long  statusId : statusIdList) {
                UpdateAutoRuleResponseVo updateAutoRuleResponseVo = new UpdateAutoRuleResponseVo();
                updateAutoRuleResponseVo.setStatusId(statusId);
                AdvertiseAutoRuleStatus autoRuleStatus = statusMap.get(statusId);
                //受控对象不存在
                if (Objects.isNull(autoRuleStatus)) {
                    updateAutoRuleResponseVo.setMsg("当前受控对象不存在");
                    updateAutoRuleResponseVo.setIsRetry(1);
                    responseVoList.add(updateAutoRuleResponseVo);
                    continue;
                }

                ShopAuth shopAuth = shopMap.get(autoRuleStatus.getShopId());
                if (Objects.isNull(shopAuth)) {
                    log.info("puid:{} shopId:{} statusId:{} 店铺信息不存在", puid, autoRuleStatus.getShopId(), statusId);
                    updateAutoRuleResponseVo.setMsg("当前受控对象店铺不存在");
                    updateAutoRuleResponseVo.setIsRetry(1);
                    responseVoList.add(updateAutoRuleResponseVo);
                    continue;
                }

                //受控对象类型
                String itemType = autoRuleStatus.getItemType();

                //启用受控对象
                if (AutoRuleEnableStatusEnum.ENABLED.getCode().equals(enableStatus)) {
                    //修改广独状态
                    advertiseAutoRuleStatusDao.updateStrategyStatusById(puid,updateUid, autoRuleStatus.getId(), enableStatus);
                    //补充各受控对象类型（投放、搜索词、抢排名）数据，并推送至计算服务更新

                    //抢排名补充基础数据
                    if (AutoRuleItemTypeEnum.KEYWORD_TARGET.getName().equals(autoRuleStatus.getItemType())) {
                        AmazonAdKeyword amazonAdKeyword = amazonAdKeywordDaoRoutingService.getByKeywordId(autoRuleStatus.getPuid(), autoRuleStatus.getShopId(), autoRuleStatus.getItemId());
                        autoRuleStatus.setKeywordText(amazonAdKeyword.getKeywordText());
                    }

                    //投放补充基础数据
                    if (AutoRuleItemTypeEnum.TARGET.getName().equals(autoRuleStatus.getItemType())) {
                        setStatusBaseInfo4Target(autoRuleStatus);
                    }

                    //搜索词补充基础数据
                    if (AutoRuleItemTypeEnum.GROUP_SEARCH_QUERY.getName().equals(autoRuleStatus.getItemType())) {
                        setStatusBaseInfo4GroupSearchQuery(autoRuleStatus, autoRuleStatus.getPerformOperation());
                        //搜索词单独受控，需要设置itemType以便通知搜索词单独受控对应的aadras接口更新
                        if (ChildrenItemType.CHILDREN_SEARCH_QUERY.name().equals(autoRuleStatus.getChildrenItemType())) {
                            itemType = AdvertiseRuleTaskTypePb.AdvertiseRuleTaskType.SEARCH_QUERY.name();
                        }
                    }

                    //推送至计算服务插入
                    aadrasApiFactory.getAadrasApi(AdvertiseRuleTaskTypePb.AdvertiseRuleTaskType.valueOf(itemType))
                            .setAutoRuleTask(autoRuleStatus.getTaskId(), autoRuleStatus, shopAuth);
                } else if (AutoRuleEnableStatusEnum.DISABLED.getCode().equals(enableStatus)) {
                    //禁用受控对象
                    //推送至计算服务删除
                    aadrasApiFactory.getAadrasApi(AdvertiseRuleTaskTypePb.AdvertiseRuleTaskType.valueOf(itemType))
                            .removeAutoRuleTask(autoRuleStatus.getTaskId(), autoRuleStatus.getTargetType(),
                            AutoRuleEnableStatusEnum.DISABLED.getCode(), shopAuth);
                    //修改广独状态
                    advertiseAutoRuleStatusDao.updateStrategyStatusById(puid,updateUid, autoRuleStatus.getId(), enableStatus);
                }
            }
        } catch (Exception e) {
            log.error("traceId:{} puid:{} statusId:{} 自动化规则修改状态异常", traceId, puid, statusIdList, e);
        }
        return responseVoList;
    }

    @Override
    public Result<String> transferAutoRule(Integer puid, Integer uid, Long templateId, List<Long> statusIdList, Integer operation, String loginIp, String traceId) {
        return null;
    }

    @Override
    public List<DeleteAutoRuleResponseVo> removeAutoRule(Integer puid, List<RemoveAutoRuleVo> removeAutoRuleVoList, String traceId) {
        List<DeleteAutoRuleResponseVo> deleteAutoRuleResponseVoList = Lists.newArrayList();

        if (CollectionUtils.isEmpty(removeAutoRuleVoList)) {
            return deleteAutoRuleResponseVoList;
        }

        try {
            List<Long> statusIdList = new ArrayList<>(removeAutoRuleVoList.size());
            removeAutoRuleVoList.forEach(x -> statusIdList.add(x.getStatusId()));
            List<AdvertiseAutoRuleStatus> statusList = advertiseAutoRuleStatusDao.getByStatusIds(puid, statusIdList);
            //转map和收集shopId
            Map<Long, AdvertiseAutoRuleStatus> statusMap = new HashMap<>();
            Set<Integer> shopIdSet = new HashSet<>();
            statusList.forEach(x -> {
                statusMap.put(x.getId(), x);
                shopIdSet.add(x.getShopId());
            });

            List<ShopAuth> shopAuthList = shopAuthDao.listAllByIds(puid, new ArrayList<>(shopIdSet));
            Map<Integer, ShopAuth> shopMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(shopAuthList)) {
                shopMap = shopAuthList.stream().filter(Objects::nonNull).collect(Collectors.toMap(ShopAuth::getId, Function.identity()));
            }

            //遍历处理,可以整体优化成批处理
            for (RemoveAutoRuleVo removeAutoRuleVo : removeAutoRuleVoList) {
                DeleteAutoRuleResponseVo deleteAutoRuleResponseVo = new DeleteAutoRuleResponseVo();
                deleteAutoRuleResponseVo.setStatusId(removeAutoRuleVo.getStatusId());

                AdvertiseAutoRuleStatus status = statusMap.get(removeAutoRuleVo.getStatusId());
                if (Objects.isNull(status)) {
                    deleteAutoRuleResponseVo.setMsg("当前受控对象不存在");
                    deleteAutoRuleResponseVo.setIsRetry(1);
                    deleteAutoRuleResponseVoList.add(deleteAutoRuleResponseVo);
                    continue;
                }

                ShopAuth shopAuth = shopMap.get(status.getShopId());
                if (Objects.isNull(shopAuth)) {
                    log.info("puid:{} shopId:{} statusId:{} 店铺信息不存在", puid, status.getShopId(), removeAutoRuleVo.getStatusId());
                    deleteAutoRuleResponseVo.setMsg("当前受控对象店铺不存在");
                    deleteAutoRuleResponseVo.setIsRetry(1);
                    deleteAutoRuleResponseVoList.add(deleteAutoRuleResponseVo);
                    continue;
                }

                String itemType = status.getItemType();
                if (AutoRuleItemTypeEnum.GROUP_SEARCH_QUERY.getName().equals(status.getItemType())
                        && ChildrenItemType.CHILDREN_SEARCH_QUERY.name().equals(status.getChildrenItemType())) {
                    itemType = AdvertiseRuleTaskTypePb.AdvertiseRuleTaskType.SEARCH_QUERY.name();
                }

                //广告服务删除策略
                advertiseAutoRuleStatusDao.deleteAutoRuleStatus(puid, removeAutoRuleVo.getStatusId());

                //插入删除表
                advertiseAutoRuleStatusDeleteDao.insetStrategyStatus(puid, status);

                //推送至计算服务删除
                if (AutoRuleEnableStatusEnum.ENABLED.getCode().equals(status.getStatus())) {
                    aadrasApiFactory.getAadrasApi(AdvertiseRuleTaskTypePb.AdvertiseRuleTaskType.valueOf(itemType))
                            .removeAutoRuleTask(status.getTaskId(), status.getTargetType(), "DELETE", shopAuth);
                }
            }
        } catch (Exception e) {
            log.error("traceId:{} puid{} 自动化规则移除异常:", traceId, puid, e);
        }
        return deleteAutoRuleResponseVoList;
    }
}
