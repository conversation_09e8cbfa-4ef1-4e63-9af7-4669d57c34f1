package com.meiyunji.sponsored.service.multiPlatform.walmart.po;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

public class WalmartBulkInventoryFeed {

    @JsonProperty("InventoryHeader")
    private InventoryHeader inventoryHeader;

    @JsonProperty("Inventory")
    private List<WalmartBulkInventory> inventoryList;

    public List<WalmartBulkInventory> getInventoryList() {
        return inventoryList;
    }

    public void setInventoryList(List<WalmartBulkInventory> inventoryList) {
        this.inventoryList = inventoryList;
    }

    public InventoryHeader getInventoryHeader() {
        return inventoryHeader;
    }

    public void setInventoryHeader(InventoryHeader inventoryHeader) {
        this.inventoryHeader = inventoryHeader;
    }

    public static WalmartBulkInventoryFeed newFeed(List<WalmartBulkInventory> inventoryList) {
        WalmartBulkInventoryFeed bean = new WalmartBulkInventoryFeed();
        bean.setInventoryHeader(new InventoryHeader("1.4"));
        bean.setInventoryList(inventoryList);
        return bean;
    }

    public static class InventoryHeader {

        private String version;

        public InventoryHeader(String version) {
            this.version = version;
        }

        public String getVersion() {
            return version;
        }

        public void setVersion(String version) {
            this.version = version;
        }
    }
}
