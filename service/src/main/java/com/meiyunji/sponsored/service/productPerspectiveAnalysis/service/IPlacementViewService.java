package com.meiyunji.sponsored.service.productPerspectiveAnalysis.service;

import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.qo.DiagnoseCountParam;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.qo.PlacementViewParam;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.qo.ViewBaseParam;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.vo.PlacementAggregateVo;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.vo.PlacementViewAggregateVo;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.vo.PlacementViewVo;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.vo.StreamDataViewVo;

import java.util.List;
import java.util.Map;

/**
 * @author: liweibin
 * @email: <EMAIL>
 * @date: 2023-09-01  13:54
 */
public interface IPlacementViewService {
    /**
     * 获取广告位视图列表页
     * @param puid
     * @param param
     * @return
     */
    Page<PlacementViewVo> getPlacementViewPageVoList(Integer puid, PlacementViewParam param);

    /**
     * 获取广告位视图汇总
     * @param puid
     * @param param
     * @return
     */
    PlacementViewAggregateVo getPlacementViewAggregateVoList(Integer puid, PlacementViewParam param);

    /**
     * 根据活动id获取Feed数据map
     * @param param
     * @param campaignIdList
     * @return
     */
    Map<String, StreamDataViewVo> getStreamDataByCompare(PlacementViewParam param, List<String> campaignIdList);
}
