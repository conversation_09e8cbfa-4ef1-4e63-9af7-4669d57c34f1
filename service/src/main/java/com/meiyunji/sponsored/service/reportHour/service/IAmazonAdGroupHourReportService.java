package com.meiyunji.sponsored.service.reportHour.service;

import com.meiyunji.sponsored.grpc.common.GetGroupHourReportResponsePb;
import com.meiyunji.sponsored.grpc.entry.ReportDateModelPb;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.cpc.vo.GroupAggregateHourVo;
import com.meiyunji.sponsored.service.cpc.vo.GroupHourParam;
import com.meiyunji.sponsored.service.cpc.vo.PlacementHourParam;
import com.meiyunji.sponsored.service.reportHour.vo.AdGroupHourVo;
import com.meiyunji.sponsored.service.reportHour.vo.AdGroupWeekDayVo;
import com.meiyunji.sponsored.service.reportHour.vo.AdPlacementHourVo;
import com.meiyunji.sponsored.service.reportHour.vo.AdPlacementWeekDayVo;

import java.util.List;

/**
 * @author: ys
 * @date: 2023/12/27 21:05
 * @describe:
 */
public interface IAmazonAdGroupHourReportService {
    GetGroupHourReportResponsePb.GetGroupHourReportResponse.GroupHour getAggregateList(int puid, List<String> idsList,
                                                                                       GroupAggregateHourVo param, ReportDateModelPb.ReportDateModel dateModel);

    List<AdGroupHourVo> getAggregateHourList(ShopAuth shopAuth, int puid, List<String> idList, GroupAggregateHourVo param);

    List<AdGroupHourVo> getAggregateHourDayList(int puid, List<String> idsList, GroupAggregateHourVo param, List<AdGroupHourVo> compares);

    List<AdGroupHourVo> getAggregateHourWeekList(int puid, List<String> idsList, GroupAggregateHourVo param, List<AdGroupHourVo> compares);

    List<AdGroupHourVo> getAggregateHourMonthList(int puid, List<String> idsList, GroupAggregateHourVo param, List<AdGroupHourVo> compares);

    List<AdGroupWeekDayVo> getGroupWeeklySuperpositionList(int puid, GroupHourParam param);


}
