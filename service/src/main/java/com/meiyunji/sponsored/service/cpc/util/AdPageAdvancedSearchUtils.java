package com.meiyunji.sponsored.service.cpc.util;

import com.meiyunji.sponsored.service.cpc.vo.AdPageSearchParam;

import java.math.BigDecimal;

/**
 * @author: ys
 * @date: 2024/2/6 11:31
 * @describe:
 */
public class AdPageAdvancedSearchUtils {

    public static boolean isUseReportDataAdvanced(AdPageSearchParam param) {
        if (param.getUseAdvanced()) {
            if (param.getImpressionsMin() == null && param.getImpressionsMax() == null && param.getClicksMin() == null && param.getClicksMax() == null
                    && param.getClickRateMin() == null && param.getClickRateMax() == null && param.getCostMin() == null && param.getCostMax() == null
                    && param.getCpcMin() == null && param.getCpcMax() == null && param.getOrderNumMin() == null && param.getOrderNumMax() == null &&
                    param.getSalesMin() == null && param.getSalesMax() == null && param.getAcosMin() == null && param.getAcosMax() == null &&
                    param.getRoasMin() == null && param.getRoasMax() == null && param.getViewImpressionsMin() == null && param.getViewImpressionsMax() == null &&
                    param.getAdSaleNumMin() == null && param.getAdSaleNumMax() == null && param.getAdOtherOrderNumMin() == null && param.getAdOtherOrderNumMax() == null &&
                    param.getAdSelfSaleNumMin() == null && param.getAdSelfSaleNumMax() == null && param.getAdOtherSaleNumMin() == null && param.getAdOtherSaleNumMax() == null &&
                    param.getAdSalesTotalMin() == null && param.getAdSalesTotalMax() == null && param.getOrdersNewToBrandFTDMin() == null && param.getOrdersNewToBrandFTDMax() == null &&
                    param.getUnitsOrderedNewToBrandFTDMin() == null && param.getUnitsOrderedNewToBrandFTDMax() == null && param.getSalesConversionRateMin() == null && param.getSalesConversionRateMax() == null &&
                    param.getAcotsMin() == null && param.getAcotsMax() == null && param.getAsotsMin() == null && param.getAsotsMax() == null &&
                    param.getAdCostPercentageMin() == null && param.getAdCostPercentageMax() == null && param.getAdSalePercentageMin() == null && param.getAdSalePercentageMax() == null &&
                    param.getAdOrderNumPercentageMin() == null && param.getAdOrderNumPercentageMax() == null && param.getOrderNumPercentageMin() == null && param.getOrderNumPercentageMax() == null &&
                    param.getCpaMin() == null && param.getCpaMax() == null && param.getVcpmMin() == null && param.getVcpmMax() == null &&
                    param.getAdSalesMin() == null && param.getAdSalesMax() == null && param.getAdOtherSalesMin() == null && param.getAdOtherSalesMax() == null &&
                    param.getOrderRateNewToBrandFTDMin() == null && param.getOrderRateNewToBrandFTDMax() == null && param.getSalesNewToBrandFTDMin() == null && param.getSalesNewToBrandFTDMax() == null &&
                    param.getSalesRateNewToBrandFTDMin() == null && param.getSalesRateNewToBrandFTDMax() == null && param.getUnitsOrderedRateNewToBrandFTDMin() == null && param.getUnitsOrderedRateNewToBrandFTDMax() == null
                    && param.getAddToCartMin() == null && param.getAddToCartMax() == null
                    && param.getVideo5SecondViewsMin() == null && param.getVideo5SecondViewsMax() == null
                    && param.getVideoCompleteViewsMin() == null && param.getVideoCompleteViewsMax() == null
                    && param.getViewabilityRateMin() == null && param.getViewabilityRateMax() == null
                    && param.getViewClickThroughRateMin() == null && param.getViewClickThroughRateMax() == null
                    && param.getBrandedSearchesMin() == null && param.getBrandedSearchesMax() == null
                    && param.getAdvertisingUnitPriceMin() == null && param.getAdvertisingUnitPriceMax() == null
            ) {
                return false;
            } else {
                return true;
            }
        } else {
            return false;
        }
    }

    public static boolean isUseReportDataGreaterThanZeroAdvanced(AdPageSearchParam param) {
        if (param.getImpressionsMin() != null && param.getImpressionsMin() > 0
                || param.getClicksMin() != null && param.getClicksMin() > 0
                || param.getClickRateMin() != null && param.getClickRateMin().compareTo(BigDecimal.ZERO) > 0
                || param.getCostMin() != null && param.getCostMin().compareTo(BigDecimal.ZERO) > 0
                || param.getCpcMin() != null && param.getCpcMin().compareTo(BigDecimal.ZERO) > 0
                || param.getOrderNumMin() != null && param.getOrderNumMin() > 0
                || param.getSalesMin() != null && param.getSalesMin().compareTo(BigDecimal.ZERO) > 0
                || param.getAcosMin() != null && param.getAcosMin().compareTo(BigDecimal.ZERO) > 0
                || param.getRoasMin() != null && param.getRoasMin().compareTo(BigDecimal.ZERO) > 0
                || param.getViewImpressionsMin() != null && param.getViewImpressionsMin() > 0
                || param.getAdSaleNumMin() != null && param.getAdSaleNumMin() > 0
                || param.getAdOtherOrderNumMin() != null && param.getAdOtherOrderNumMin() > 0
                || param.getAdSelfSaleNumMin() != null && param.getAdSelfSaleNumMin() > 0
                || param.getAdOtherSaleNumMin() != null && param.getAdOtherSaleNumMin() > 0
                || param.getAdSalesTotalMin() != null && param.getAdSalesTotalMin() > 0
                || param.getOrdersNewToBrandFTDMin() != null && param.getOrdersNewToBrandFTDMin() > 0
                || param.getUnitsOrderedNewToBrandFTDMin() != null && param.getUnitsOrderedNewToBrandFTDMin() > 0
                || param.getSalesConversionRateMin() != null && param.getSalesConversionRateMin().compareTo(BigDecimal.ZERO) > 0
                || param.getAcotsMin() != null && param.getAcotsMin().compareTo(BigDecimal.ZERO) > 0
                || param.getAsotsMin() != null && param.getAsotsMin().compareTo(BigDecimal.ZERO) > 0
                || param.getAdCostPercentageMin() != null && param.getAdCostPercentageMin().compareTo(BigDecimal.ZERO) > 0
                || param.getAdSalePercentageMin() != null && param.getAdSalePercentageMin().compareTo(BigDecimal.ZERO) > 0
                || param.getAdOrderNumPercentageMin() != null && param.getAdOrderNumPercentageMin().compareTo(BigDecimal.ZERO) > 0
                || param.getOrderNumPercentageMin() != null && param.getOrderNumPercentageMin().compareTo(BigDecimal.ZERO) > 0
                || param.getCpaMin() != null && param.getCpaMin().compareTo(BigDecimal.ZERO) > 0
                || param.getVcpmMin() != null && param.getVcpmMin().compareTo(BigDecimal.ZERO) > 0
                || param.getAdSalesMin() != null && param.getAdSalesMin().compareTo(BigDecimal.ZERO) > 0
                || param.getAdOtherSalesMin() != null && param.getAdOtherSalesMin().compareTo(BigDecimal.ZERO) > 0
                || param.getOrderRateNewToBrandFTDMin() != null && param.getOrderRateNewToBrandFTDMin().compareTo(BigDecimal.ZERO) > 0
                || param.getSalesNewToBrandFTDMin() != null && param.getSalesNewToBrandFTDMin().compareTo(BigDecimal.ZERO) > 0
                || param.getSalesRateNewToBrandFTDMin() != null && param.getSalesRateNewToBrandFTDMin().compareTo(BigDecimal.ZERO) > 0
                || param.getUnitsOrderedRateNewToBrandFTDMin() != null && param.getUnitsOrderedRateNewToBrandFTDMin().compareTo(BigDecimal.ZERO) > 0
                || param.getAddToCartMin() != null && param.getAddToCartMin() > 0
                || param.getVideo5SecondViewsMin() != null && param.getVideo5SecondViewsMin() > 0
                || param.getVideoCompleteViewsMin() != null && param.getVideoCompleteViewsMin() > 0
                || param.getViewabilityRateMin() != null && param.getViewabilityRateMin().compareTo(BigDecimal.ZERO) > 0
                || param.getViewClickThroughRateMin() != null && param.getViewClickThroughRateMin().compareTo(BigDecimal.ZERO) > 0
                || param.getBrandedSearchesMin() != null && param.getBrandedSearchesMin() > 0
                || param.getAdvertisingUnitPriceMin() != null && param.getAdvertisingUnitPriceMin().compareTo(BigDecimal.ZERO) > 0
        ) {
            return true;
        } else {
            return false;
        }
    }
}
