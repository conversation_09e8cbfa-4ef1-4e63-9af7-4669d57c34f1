package com.meiyunji.sponsored.service.doris.dao;


import com.meiyunji.sponsored.service.cpc.bo.SearchTermAggregateBO;
import com.meiyunji.sponsored.service.cpc.bo.SearchTermBO;
import com.meiyunji.sponsored.service.cpc.vo.CpcQueryWordDetailDto;
import com.meiyunji.sponsored.service.doris.po.OdsCpcQueryKeywordReport;
import com.meiyunji.sponsored.common.springjdbc.IDorisBaseDao;
import com.meiyunji.sponsored.service.newDashboard.dto.DashboardAdQueryWordDataDto;
import com.meiyunji.sponsored.service.newDashboard.dto.DashboardAdQueryWordDataPageDto;
import com.meiyunji.sponsored.service.newDashboard.dto.DashboardAdQueryWordMatrixTopDto;
import com.meiyunji.sponsored.service.newDashboard.enums.DashboardDataFieldEnum;
import com.meiyunji.sponsored.service.newDashboard.enums.DashboardOrderByEnum;
import com.meiyunji.sponsored.service.newDashboard.enums.DashboardQueryWordTypeEnum;
import java.util.Collection;
import java.util.List;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.service.cpc.vo.AdMetricDto;
import com.meiyunji.sponsored.service.cpc.vo.CpcQueryWordDto;
import com.meiyunji.sponsored.service.cpc.vo.chartVo.AdHomePerformancedto;
import com.meiyunji.sponsored.service.newDashboard.vo.DashboardQueryWordReqVo;
import com.meiyunji.sponsored.service.wordFrequency.qo.QueryWordTopQo;
import com.meiyunji.sponsored.service.wordFrequency.vo.WordRootTopVo;


/**
 * amazon查询关键词报告表(OdsCpcQueryKeywordReport)数据库访问层
 *
 * <AUTHOR>
 * @since 2024-03-26 10:46:21
 */
public interface IOdsCpcQueryKeywordReportDao extends IDorisBaseDao<OdsCpcQueryKeywordReport> {

    /**
     * 广告看板 - 查询用户搜索词数据
     * @param puid
     * @param marketplaceIdList
     * @param shopIdList
     * @param currency
     * @param startDate
     * @param endDate
     * @param orderByField
     * @param orderBy
     * @param limit
     * @return
     */
    List<DashboardAdQueryWordDataDto> queryAdQueryWordCharts(Integer puid, List<String> marketplaceIdList, List<Integer> shopIdList,
                                                             String currency, String startDate, String endDate, String orderByField,
                                                             String orderBy, Integer limit, List<String> campaignIds,
                                                             List<String> portfolioIds, Boolean isSiteToday,
                                                             Boolean noZero);


    List<DashboardAdQueryWordMatrixTopDto> queryMatrixInfo(Integer puid, List<String> marketplaceIdList,
                                                           List<Integer> shopIdList, String currency,
                                                           String startDate, String endDate,
                                                           DashboardDataFieldEnum dataField, List<String> keywordIdList,
                                                           Integer limit, DashboardOrderByEnum orderBy,
                                                           DashboardQueryWordTypeEnum queryWordType, List<String> siteToday, Boolean isSiteToday,
                                                           List<String> portfolioIds, List<String> campaignIds,
                                                           Boolean noZero);

    List<OdsCpcQueryKeywordReport> getByQueryIdList(Integer puid, List<Integer> shopIdList, Collection<String> queryIdList);

    /**
     * 搜索词投放产生关键词列表页
     */
    Page pageManageList(Integer puid, CpcQueryWordDto dto, Page page);

    int pageManageCountAll(Integer puid, CpcQueryWordDto dto);

    /**
     * 搜索词投放产生关键词列表页占比指标总值
     */
    AdMetricDto getSumAdMetricDto(Integer puid, CpcQueryWordDto dto);

    /**
     * 搜索词列表页报告汇总、图表
     */
    List<AdHomePerformancedto> getQueryKeywordReportAggregate(Integer puid, CpcQueryWordDto dto, boolean isGroupByDate, boolean selCompareData);
    /**
     * 获取搜索词页面top词根
     * @param dto
     * @return
     */
    List<WordRootTopVo> getWordRootToplist(QueryWordTopQo dto);

    Page<DashboardAdQueryWordDataPageDto> queryAdQueryWordPage(Integer puid, DashboardQueryWordReqVo vo);

    Page allSearchTermPageList(Integer puid, CpcQueryWordDto dto, Page page);

    List<SearchTermBO> searchTermSourceTargetDetail(CpcQueryWordDto dto);

    SearchTermAggregateBO allSearchTermAggregate(Integer puid, CpcQueryWordDto dto, boolean isGroupByDate);

    /**
     * 获取列表页所有query
     * @param puid
     * @param dto
     * @return
     */
    List<String> queryListAllSearchTermAggregateData(Integer puid, CpcQueryWordDto dto);

    /**
     * 根据query列表查询各个搜索词表的汇总数据
     */
    List<SearchTermAggregateBO> allSearchTermAggregateDataByQueryList(Integer puid, CpcQueryWordDto dto, String startDate, String endDate,
                                                                      List<String> queryList);

    List<SearchTermBO> getDetailList(Integer puid, CpcQueryWordDetailDto dto);

    int listCountSearchTermPageList(Integer puid, CpcQueryWordDto dto, Page page);
}

