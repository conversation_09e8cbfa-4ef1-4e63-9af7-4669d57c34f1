package com.meiyunji.sponsored.service.function;

import java.util.Objects;
import java.util.function.Function;

/**
 *  2参数有返回值的函数接口
 * @param <P>
 * @param <T>
 * @param <R>
 */

@FunctionalInterface
public interface TwFunction<P, T, R> {


    R apply(P p, T t);


    default <V> TwFunction<P, T, V> andThen(Function<? super R, ? extends V> after) {
        Objects.requireNonNull(after);
        return (P p, T t) -> after.apply(apply(p, t));
    }
}
