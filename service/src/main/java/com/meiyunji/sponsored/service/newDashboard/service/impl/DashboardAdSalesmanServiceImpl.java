package com.meiyunji.sponsored.service.newDashboard.service.impl;

import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.util.MathUtil;
import com.meiyunji.sponsored.common.util.ParamCopyUtil;
import com.meiyunji.sponsored.rpc.newDashboard.DashboardAdProductResponseVo;
import com.meiyunji.sponsored.rpc.newDashboard.DashboardAdSalesmanResponseVo;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IVcShopAuthDao;
import com.meiyunji.sponsored.service.account.po.VcShopAuth;
import com.meiyunji.sponsored.service.account.service.IUserService;
import com.meiyunji.sponsored.service.cpc.service2.impl.CpcShopDataService;
import com.meiyunji.sponsored.service.dataWarehouse.statsDo.ShopSaleDto;
import com.meiyunji.sponsored.service.doris.dao.IOdsAmazonAdProductReportDao;
import com.meiyunji.sponsored.service.excel.excelTools.service.IExcelService;
import com.meiyunji.sponsored.service.newDashboard.dto.*;
import com.meiyunji.sponsored.service.newDashboard.dto.base.DashboardAdCalDataDto;
import com.meiyunji.sponsored.service.newDashboard.enums.DashboardDataFieldEnum;
import com.meiyunji.sponsored.service.newDashboard.enums.DashboardOrderByEnum;
import com.meiyunji.sponsored.service.newDashboard.enums.DashboardOrderByRateEnum;
import com.meiyunji.sponsored.service.newDashboard.enums.DashboardQueryFieldEnum;
import com.meiyunji.sponsored.service.newDashboard.service.IDashboardAdSalesmanService;
import com.meiyunji.sponsored.service.newDashboard.util.CalculateAdDataUtil;
import com.meiyunji.sponsored.service.newDashboard.util.CalculateUtil;
import com.meiyunji.sponsored.service.newDashboard.util.PageUtils;
import com.meiyunji.sponsored.service.newDashboard.vo.DashboardAdProductReqVo;
import com.meiyunji.sponsored.service.newDashboard.vo.DashboardAdSalesmanReqVo;
import com.meiyunji.sponsored.service.util.LocalDateTimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @author: ys
 * @date: 2024/4/9 15:52
 * @describe:
 */
@Service
@Slf4j
public class DashboardAdSalesmanServiceImpl implements IDashboardAdSalesmanService {



    @Autowired
    private IExcelService excelService;


    @Resource
    private IOdsAmazonAdProductReportDao odsAmazonAdProductReportDao;



    @Resource
    private IUserService userService;

    @Autowired
    private CpcShopDataService cpCShopDataService;

    @Resource
    private IVcShopAuthDao vcShopAuthDao;



    @Override
    public DashboardAdSalesmanResponseVo queryAdSalesmanCharts(DashboardAdSalesmanReqVo req) {

        if (CollectionUtils.isNotEmpty(req.getShopIdList())) {
            List<VcShopAuth> listByIdList = vcShopAuthDao.getListByIdList(req.getShopIdList());
            req.setShopIdList(Lists.newArrayList(req.getShopIdList()));
            if (CollectionUtils.isNotEmpty(listByIdList)) {
                req.getShopIdList().removeAll(listByIdList.stream().map(VcShopAuth::getId).collect(Collectors.toList()));
            }
            if (CollectionUtils.isEmpty(req.getShopIdList())) {
                log.info("剔除vc店铺后无数据puid:{}", req.getPuid());
                DashboardAdSalesmanResponseVo.Builder response = DashboardAdSalesmanResponseVo.newBuilder();
                response.setPage(getPageInfo(new ArrayList<>(), req.getPageSize(), req.getPageNo()));
                return response.build();
            }
        }
        Map<String, List<DashboardAdSalesmanDto>> adSalesmanTopList = getAdSalesmanTopList(req);
        List<DashboardAdSalesmanDto> resultList = adSalesmanTopList.get("list");
        List<DashboardAdSalesmanDto> summary = adSalesmanTopList.get("summary");
        List<DashboardAdSalesmanDto> totalSummary = adSalesmanTopList.get("totalSummary");
        if (CollectionUtils.isEmpty(resultList)) {
            log.info("get top campaign chars data list is null, dataField:{}", req.getDataField());
            DashboardAdSalesmanResponseVo.Builder response = DashboardAdSalesmanResponseVo.newBuilder();
            response.setPage(getPageInfo(new ArrayList<>(), req.getPageSize(), req.getPageNo()));
            return response.build();

        }

        DashboardAdSalesmanResponseVo.Builder response = DashboardAdSalesmanResponseVo.newBuilder();
        //顶部汇总数据
        if (CollectionUtils.isNotEmpty(summary)) {
            DashboardAdSalesmanDto dashboardAdSalesmanDto = summary.get(0);
            DashboardAdSalesmanResponseVo.Summary.Builder vo =
                    DashboardAdSalesmanResponseVo.Summary.newBuilder();
            BeanUtils.copyProperties(dashboardAdSalesmanDto, vo, ParamCopyUtil.checkPropertiesNullOrEmptySuper(dashboardAdSalesmanDto));
            vo.setCost(CalculateUtil.formatDecimal(dashboardAdSalesmanDto.getCost()));
            Optional.ofNullable(dashboardAdSalesmanDto.getTotalSales()).map(CalculateUtil::formatDecimal).ifPresent(vo::setTotalSales);
            Optional.ofNullable(dashboardAdSalesmanDto.getImpressions()).map(String::valueOf).ifPresent(vo::setImpressions);
            Optional.ofNullable(dashboardAdSalesmanDto.getClicks()).map(String::valueOf).ifPresent(vo::setClicks);
            Optional.ofNullable(dashboardAdSalesmanDto.getOrderNum()).map(String::valueOf).ifPresent(vo::setOrderNum);
            Optional.ofNullable(dashboardAdSalesmanDto.getSaleNum()).map(String::valueOf).ifPresent(vo::setSaleNum);
            Optional.ofNullable(dashboardAdSalesmanDto.getAcos()).map(CalculateUtil::formatPercentNoPercent).ifPresent(vo::setAcos);
            Optional.ofNullable(dashboardAdSalesmanDto.getRoas()).map(CalculateUtil::formatDecimal).ifPresent(vo::setRoas);
            Optional.ofNullable(dashboardAdSalesmanDto.getClickRate()).map(CalculateUtil::formatPercentNoPercent).ifPresent(vo::setClickRate);
            Optional.ofNullable(dashboardAdSalesmanDto.getConversionRate()).map(CalculateUtil::formatPercentNoPercent).ifPresent(vo::setConversionRate);
            Optional.ofNullable(dashboardAdSalesmanDto.getCpc()).map(CalculateUtil::formatDecimal).ifPresent(vo::setCpc);
            Optional.ofNullable(dashboardAdSalesmanDto.getCpa()).map(CalculateUtil::formatDecimal).ifPresent(vo::setCpa);


            Optional.ofNullable(dashboardAdSalesmanDto.getAcosMomValue()).map(CalculateUtil::ratioConversionValue).ifPresent(vo::setAcosMomValue);
            Optional.ofNullable(dashboardAdSalesmanDto.getAcosYoyValue()).map(CalculateUtil::ratioConversionValue).ifPresent(vo::setAcosYoyValue);


            Optional.ofNullable(dashboardAdSalesmanDto.getClickRateMomValue()).map(CalculateUtil::ratioConversionValue).ifPresent(vo::setClickRateMomValue);
            Optional.ofNullable(dashboardAdSalesmanDto.getClickRateYoyValue()).map(CalculateUtil::ratioConversionValue).ifPresent(vo::setClickRateYoyValue);

            Optional.ofNullable(dashboardAdSalesmanDto.getConversionRateMomValue()).map(CalculateUtil::ratioConversionValue).ifPresent(vo::setConversionRateMomValue);
            Optional.ofNullable(dashboardAdSalesmanDto.getConversionRateYoyValue()).map(CalculateUtil::ratioConversionValue).ifPresent(vo::setConversionRateYoyValue);



            Optional.ofNullable(dashboardAdSalesmanDto.getAcosMom()).map(CalculateUtil::formatPercentNoPercent).ifPresent(vo::setAcosMom);
            Optional.ofNullable(dashboardAdSalesmanDto.getAcosYoy()).map(CalculateUtil::formatPercentNoPercent).ifPresent(vo::setAcosYoy);


            Optional.ofNullable(dashboardAdSalesmanDto.getClickRateMom()).map(CalculateUtil::formatPercentNoPercent).ifPresent(vo::setClickRateMom);
            Optional.ofNullable(dashboardAdSalesmanDto.getClickRateYoy()).map(CalculateUtil::formatPercentNoPercent).ifPresent(vo::setClickRateYoy);

            Optional.ofNullable(dashboardAdSalesmanDto.getConversionRateMom()).map(CalculateUtil::formatPercentNoPercent).ifPresent(vo::setConversionRateMom);
            Optional.ofNullable(dashboardAdSalesmanDto.getConversionRateYoy()).map(CalculateUtil::formatPercentNoPercent).ifPresent(vo::setConversionRateYoy);



            response.setSummary(vo.build());
        }
        //所有业务员汇总
        if (CollectionUtils.isNotEmpty(totalSummary)) {
            DashboardAdSalesmanDto dashboardAdSalesmanDto = totalSummary.get(0);
            DashboardAdSalesmanResponseVo.Summary.Builder vo =
                    DashboardAdSalesmanResponseVo.Summary.newBuilder();
            BeanUtils.copyProperties(dashboardAdSalesmanDto, vo, ParamCopyUtil.checkPropertiesNullOrEmptySuper(dashboardAdSalesmanDto));
            vo.setCost(CalculateUtil.formatDecimal(dashboardAdSalesmanDto.getCost()));
            Optional.ofNullable(dashboardAdSalesmanDto.getTotalSales()).map(CalculateUtil::formatDecimal).ifPresent(vo::setTotalSales);
            Optional.ofNullable(dashboardAdSalesmanDto.getImpressions()).map(String::valueOf).ifPresent(vo::setImpressions);
            Optional.ofNullable(dashboardAdSalesmanDto.getClicks()).map(String::valueOf).ifPresent(vo::setClicks);
            Optional.ofNullable(dashboardAdSalesmanDto.getOrderNum()).map(String::valueOf).ifPresent(vo::setOrderNum);
            Optional.ofNullable(dashboardAdSalesmanDto.getSaleNum()).map(String::valueOf).ifPresent(vo::setSaleNum);
            Optional.ofNullable(dashboardAdSalesmanDto.getAcos()).map(CalculateUtil::formatPercentNoPercent).ifPresent(vo::setAcos);
            Optional.ofNullable(dashboardAdSalesmanDto.getRoas()).map(CalculateUtil::formatDecimal).ifPresent(vo::setRoas);
            Optional.ofNullable(dashboardAdSalesmanDto.getClickRate()).map(CalculateUtil::formatPercentNoPercent).ifPresent(vo::setClickRate);
            Optional.ofNullable(dashboardAdSalesmanDto.getConversionRate()).map(CalculateUtil::formatPercentNoPercent).ifPresent(vo::setConversionRate);
            Optional.ofNullable(dashboardAdSalesmanDto.getCpc()).map(CalculateUtil::formatDecimal).ifPresent(vo::setCpc);
            Optional.ofNullable(dashboardAdSalesmanDto.getCpa()).map(CalculateUtil::formatDecimal).ifPresent(vo::setCpa);



            Optional.ofNullable(dashboardAdSalesmanDto.getAcosMomValue()).map(CalculateUtil::ratioConversionValue).ifPresent(vo::setAcosMomValue);
            Optional.ofNullable(dashboardAdSalesmanDto.getAcosYoyValue()).map(CalculateUtil::ratioConversionValue).ifPresent(vo::setAcosYoyValue);


            Optional.ofNullable(dashboardAdSalesmanDto.getClickRateMomValue()).map(CalculateUtil::ratioConversionValue).ifPresent(vo::setClickRateMomValue);
            Optional.ofNullable(dashboardAdSalesmanDto.getClickRateYoyValue()).map(CalculateUtil::ratioConversionValue).ifPresent(vo::setClickRateYoyValue);

            Optional.ofNullable(dashboardAdSalesmanDto.getConversionRateMomValue()).map(CalculateUtil::ratioConversionValue).ifPresent(vo::setConversionRateMomValue);
            Optional.ofNullable(dashboardAdSalesmanDto.getConversionRateYoyValue()).map(CalculateUtil::ratioConversionValue).ifPresent(vo::setConversionRateYoyValue);





            Optional.ofNullable(dashboardAdSalesmanDto.getAcosMom()).map(CalculateUtil::formatPercentNoPercent).ifPresent(vo::setAcosMom);
            Optional.ofNullable(dashboardAdSalesmanDto.getAcosYoy()).map(CalculateUtil::formatPercentNoPercent).ifPresent(vo::setAcosYoy);


            Optional.ofNullable(dashboardAdSalesmanDto.getClickRateMom()).map(CalculateUtil::formatPercentNoPercent).ifPresent(vo::setClickRateMom);
            Optional.ofNullable(dashboardAdSalesmanDto.getClickRateYoy()).map(CalculateUtil::formatPercentNoPercent).ifPresent(vo::setClickRateYoy);

            Optional.ofNullable(dashboardAdSalesmanDto.getConversionRateMom()).map(CalculateUtil::formatPercentNoPercent).ifPresent(vo::setConversionRateMom);
            Optional.ofNullable(dashboardAdSalesmanDto.getConversionRateYoy()).map(CalculateUtil::formatPercentNoPercent).ifPresent(vo::setConversionRateYoy);




            response.setTotalSummary(vo.build());
        }



        //组装数据返回
        List<DashboardAdSalesmanResponseVo.Page.TopList> topList = resultList.stream().map(d -> {
            DashboardAdSalesmanResponseVo.Page.TopList.Builder vo =
                    DashboardAdSalesmanResponseVo.Page.TopList.newBuilder();
            BeanUtils.copyProperties(d, vo, ParamCopyUtil.checkPropertiesNullOrEmptySuper(d));
            vo.setCost(CalculateUtil.formatDecimal(d.getCost()));
            Optional.ofNullable(d.getName()).ifPresent(vo::setName);

            Optional.ofNullable(d.getTotalSales()).map(CalculateUtil::formatDecimal).ifPresent(vo::setTotalSales);
            Optional.ofNullable(d.getImpressions()).map(String::valueOf).ifPresent(vo::setImpressions);
            Optional.ofNullable(d.getClicks()).map(String::valueOf).ifPresent(vo::setClicks);
            Optional.ofNullable(d.getOrderNum()).map(String::valueOf).ifPresent(vo::setOrderNum);
            Optional.ofNullable(d.getSaleNum()).map(String::valueOf).ifPresent(vo::setSaleNum);
            Optional.ofNullable(d.getAcos()).map(CalculateUtil::formatPercentNoPercent).ifPresent(vo::setAcos);
            Optional.ofNullable(d.getRoas()).map(CalculateUtil::formatDecimal).ifPresent(vo::setRoas);
            Optional.ofNullable(d.getClickRate()).map(CalculateUtil::formatPercentNoPercent).ifPresent(vo::setClickRate);
            Optional.ofNullable(d.getConversionRate()).map(CalculateUtil::formatPercentNoPercent).ifPresent(vo::setConversionRate);
            Optional.ofNullable(d.getCpc()).map(CalculateUtil::formatDecimal).ifPresent(vo::setCpc);
            Optional.ofNullable(d.getCpa()).map(CalculateUtil::formatDecimal).ifPresent(vo::setCpa);
            Optional.ofNullable(d.getCostPercent()).ifPresent(vo::setCostPercent);
            Optional.ofNullable(d.getTotalSalesPercent()).ifPresent(vo::setTotalSalesPercent);
            Optional.ofNullable(d.getImpressionsPercent()).ifPresent(vo::setImpressionsPercent);
            Optional.ofNullable(d.getClicksPercent()).ifPresent(vo::setClicksPercent);
            Optional.ofNullable(d.getOrderNumPercent()).ifPresent(vo::setOrderNumPercent);
            Optional.ofNullable(d.getSaleNumPercent()).ifPresent(vo::setSaleNumPercent);



            Optional.ofNullable(d.getAcosMomValue()).map(CalculateUtil::ratioConversionValue).ifPresent(vo::setAcosMomValue);
            Optional.ofNullable(d.getAcosYoyValue()).map(CalculateUtil::ratioConversionValue).ifPresent(vo::setAcosYoyValue);


            Optional.ofNullable(d.getClickRateMomValue()).map(CalculateUtil::ratioConversionValue).ifPresent(vo::setClickRateMomValue);
            Optional.ofNullable(d.getClickRateYoyValue()).map(CalculateUtil::ratioConversionValue).ifPresent(vo::setClickRateYoyValue);

            Optional.ofNullable(d.getConversionRateMomValue()).map(CalculateUtil::ratioConversionValue).ifPresent(vo::setConversionRateMomValue);
            Optional.ofNullable(d.getConversionRateYoyValue()).map(CalculateUtil::ratioConversionValue).ifPresent(vo::setConversionRateYoyValue);




            Optional.ofNullable(d.getAcosMom()).map(CalculateUtil::formatPercentNoPercent).ifPresent(vo::setAcosMom);
            Optional.ofNullable(d.getAcosYoy()).map(CalculateUtil::formatPercentNoPercent).ifPresent(vo::setAcosYoy);


            Optional.ofNullable(d.getClickRateMom()).map(CalculateUtil::formatPercentNoPercent).ifPresent(vo::setClickRateMom);
            Optional.ofNullable(d.getClickRateYoy()).map(CalculateUtil::formatPercentNoPercent).ifPresent(vo::setClickRateYoy);

            Optional.ofNullable(d.getConversionRateMom()).map(CalculateUtil::formatPercentNoPercent).ifPresent(vo::setConversionRateMom);
            Optional.ofNullable(d.getConversionRateYoy()).map(CalculateUtil::formatPercentNoPercent).ifPresent(vo::setConversionRateYoy);



            return vo.build();
        }).collect(Collectors.toList());

        response.setPage(getPageInfo(topList, req.getPageSize(), req.getPageNo()));

        return  response.build();
    }


    private Map<String,List<DashboardAdSalesmanDto>> getAdSalesmanTopList(DashboardAdSalesmanReqVo req) {
        //查询广告组top图表数据，且需要区分sp,sb,sd
        Integer puid = req.getPuid();
        List<String> marketplaceIdList = req.getMarketplaceIdList();
        List<Integer> shopIdList = req.getShopIdList();
        String currency = req.getCurrency();
        DashboardOrderByRateEnum orderField = DashboardOrderByRateEnum.rateMap.get(req.getOrderByField());
        DashboardDataFieldEnum dataField = DashboardDataFieldEnum.fieldMap.get(req.getDataField());
        int limit = Optional.ofNullable(req.getLimit()).orElse(0);
        DashboardOrderByEnum orderByEn = DashboardOrderByEnum.orderByMap.get(req.getOrderBy());
        //先获取sp的图表数据
        Map<String, List<DashboardAdSalesmanDto>> resultMap = queryTopList(req, puid, shopIdList, marketplaceIdList, currency, limit, orderField, dataField);

        List<DashboardAdSalesmanDto> resultList = resultMap.get("list");

        if (CollectionUtils.isEmpty(resultList)) {
            return Collections.emptyMap();
        }

        //还需要对结果进行排序，取前limit行数据
        Class<DashboardAdProductDto> cla = DashboardAdProductDto.class;
        Field[] fields = getAllField(cla);
        Map<String, Field> fieldMap = Arrays.stream(fields).peek(f -> f.setAccessible(true))
                .filter(e-> !e.isSynthetic()).collect(Collectors.toMap(Field::getName, v1 -> v1));
        StringBuilder compareKey = new StringBuilder(dataField.getCode());
        if (DashboardOrderByRateEnum.PERCENT != orderField) {
            compareKey.append(orderField.getSuffix());
        }
        resultList = resultList.stream().sorted((o1, o2) -> {
            try {
                int result = 0;
                Field compareField = fieldMap.get(compareKey.toString());
                if (compareField == null) {
                    compareField = fieldMap.get(dataField.getCode());
                }
                if (Objects.nonNull(compareField)) {
                    Object val1 = compareField.get(o1);
                    Object val2 = compareField.get(o2);
                    if(Objects.isNull(val1) && Objects.isNull(val2) ){
                        result = 0;
                    } else if(Objects.nonNull(val1) && Objects.isNull(val2) ){
                        result = 1;
                    } else if(Objects.isNull(val1)){
                        result = -1;
                    } else {
                        if(String.class.isAssignableFrom(compareField.getType())){
                            String rateValue1 = val1.toString().replace("%", "");
                            String rateValue2 = val2.toString().replace("%", "");
                            if ("--".equalsIgnoreCase(rateValue2) || "--".equalsIgnoreCase(rateValue1)) {
                                return 0;
                            } else if("null".equalsIgnoreCase(rateValue2) && "null".equalsIgnoreCase(rateValue1)) {
                                result = 0;
                            } else if(!"null".equalsIgnoreCase(rateValue1) && "null".equalsIgnoreCase(rateValue2) ){
                                result = 1;
                            } else  if("null".equalsIgnoreCase(rateValue1)){
                                result = -1;
                            } else {
                                result = new BigDecimal(rateValue1).compareTo(new BigDecimal(rateValue2));
                            }
                        }
                        if(Integer.class.isAssignableFrom(compareField.getType())){
                            Integer compareVal1 = (Integer) val1;
                            Integer compareVal2 = (Integer) val2;
                            result = compareVal1.compareTo(compareVal2);
                        }
                        if(BigDecimal.class.isAssignableFrom(compareField.getType())){
                            BigDecimal compareVal1 = (BigDecimal) val1;
                            BigDecimal compareVal2 = (BigDecimal) val2;
                            result = compareVal1.compareTo(compareVal2);
                        }
                        if(Long.class.isAssignableFrom(compareField.getType())){
                            Long compareVal1 = (Long) val1;
                            Long compareVal2 = (Long) val2;
                            result = compareVal1.compareTo(compareVal2);
                        }
                    }
                }
                if (result == 0) {
                    if(Objects.isNull(o1.getDevId()) && Objects.isNull(o2.getDevId()) ){
                        result = 0;
                    }
                    if(Objects.nonNull(o1.getDevId()) && Objects.isNull(o2.getDevId()) ){
                        result = 1;
                    }
                    if(Objects.isNull(o1.getDevId())){
                        result = -1;
                    }
                    if (o1.getDevId() != null && o2.getDevId() != null) {
                        result = o1.getDevId().compareTo(o2.getDevId());
                        if (result > 1) {
                            result = 1;
                        }
                        if (result < 0) {
                            result = -1;
                        }
                    }
                }
                return result;
            } catch (IllegalAccessException e) {
                log.error("compare product list data error", e);
            }
            return 0;
        }).collect(Collectors.toList());
        int subLimit = Math.min(limit, resultList.size());

        //按请求字段进行升降序
        //cpc，cpa， 环比不排序， 值排序，  acos 环比不排序   值排序
        if ( DashboardOrderByEnum.DESC == orderByEn) {
            Collections.reverse(resultList);
        }
        resultList = resultList.subList(0, subLimit);
        resultMap.put("list", resultList);
        return resultMap;
    }


    private List<String> baseHeaderList = Arrays.asList(
            "marketplaceName", "shopName",
            "displayCost", "costPercent", "costMomRate", "costYoyRate",
            "displayTotalSales", "totalSalesPercent", "totalSalesMomRate", "totalSalesYoyRate",
            "displayAcos", "acosMomRate", "acosYoyRate",
//            "displayRoas", "roasMomRate", "roasYoyRate",
            "impressions", "impressionsPercent", "impressionsMomRate", "impressionsYoyRate",
            "clicks", "clicksPercent", "clicksMomRate", "clicksYoyRate",
            "orderNum", "orderNumPercent", "orderNumMomRate", "orderNumYoyRate",
            "displayClickRate", "clickRateMomRate", "clickRateYoyRate",
            "displayConversionRate", "conversionRateMomRate", "conversionRateYoyRate",
            "saleNum", "saleNumPercent", "saleNumMomRate", "saleNumYoyRate",
            "displayShopSales", "shopSalesPercent", "shopSalesMomRate", "shopSalesYoyRate",
            "displayAcots", "acotsMomRate", "acotsYoyRate",
            "displayAsots", "asotsMomRate", "asotsYoyRate",
            "shopSaleNum", "shopSaleNumPercent", "shopSaleNumMomRate", "shopSaleNumYoyRate",
            "displayCpc", "cpcMomRate", "cpcYoyRate",
            "displayCpa", "cpaMomRate", "cpaYoyRate");



    private DashboardAdSalesmanDto convertBasicAndCalData(DashboardAdSalesmanTopDataDto subInfo) {
        DashboardAdSalesmanDto dto = new DashboardAdSalesmanDto();
        dto.setCost(subInfo.getSubCost());
        dto.setTotalSales(subInfo.getSubTotalSales());
        dto.setImpressions(subInfo.getSubImpressions());
        dto.setClicks(subInfo.getSubClicks());
        dto.setOrderNum(subInfo.getSubOrderNum());
        dto.setSaleNum(subInfo.getSubSaleNum());
        dto.setAcos(subInfo.getSubAcos());
        dto.setRoas(subInfo.getSubRoas());
        dto.setClickRate(subInfo.getSubClickRate());
        dto.setConversionRate(subInfo.getSubConversionRate());
        dto.setCpc(subInfo.getSubCpc());
        dto.setCpa(subInfo.getSubCpa());
        return dto;
    }

    private DashboardAdSalesmanDto convertSummaryData(DashboardAdSalesmanTopDataDto subInfo) {
        DashboardAdSalesmanDto dto = new DashboardAdSalesmanDto();
        dto.setCost(subInfo.getAllCost());
        dto.setTotalSales(subInfo.getAllTotalSales());
        Optional.ofNullable(subInfo.getAllImpressions()).map(BigDecimal::longValue).ifPresent(dto::setImpressions);
        Optional.ofNullable(subInfo.getAllClicks()).map(BigDecimal::intValue).ifPresent(dto::setClicks);
        Optional.ofNullable(subInfo.getAllOrderNum()).map(BigDecimal::intValue).ifPresent(dto::setOrderNum);
        Optional.ofNullable(subInfo.getAllSaleNum()).map(BigDecimal::intValue).ifPresent(dto::setSaleNum);
        return dto;
    }


    private Map<String,List<DashboardAdSalesmanDto>> queryTopList(DashboardAdSalesmanReqVo req, Integer puid,
                                                       List<Integer> shopIdList, List<String> marketplaceIdList,
                                                       String currency, int limit,
                                                       DashboardOrderByRateEnum orderField, DashboardDataFieldEnum dataField) {
        List<Object> spArgsListFirst = Lists.newArrayList();
        List<Object> spArgsListSecond = Lists.newArrayList();
        List<String> siteToday = null;
        if (Boolean.TRUE.equals(req.getSiteToday())) {
            siteToday = CalculateAdDataUtil.getSiteToday(marketplaceIdList);
        }
        String spSubSqlA = odsAmazonAdProductReportDao.adProductGroupByDevIdQuerySql(puid, shopIdList, marketplaceIdList, null,
                currency, req.getStartDate(), req.getEndDate(), spArgsListFirst,
                siteToday, req.getSiteToday(), req.getPortfolioIds(), req.getCampaignIds(), req.getNoZero(), dataField, false);
        spArgsListSecond.addAll(spArgsListFirst);
        String spSubSqlB = odsAmazonAdProductReportDao.adProductGroupByDevIdQuerySql(puid, shopIdList, marketplaceIdList, null,
                currency, req.getYoyStartDate(), req.getYoyEndDate(), spArgsListFirst,  null, null, req.getPortfolioIds(), req.getCampaignIds(), null, null, false);
        Supplier<List<DashboardAdSalesmanTopDataDto>> currentAndYoyList = () -> odsAmazonAdProductReportDao.queryAdSalesmanYoyOrMomTop(spSubSqlA, spSubSqlB,
                spArgsListFirst, dataField, orderField, req.getOrderBy(), limit, req.getNoZero());
        List<Integer> devYoyListSup = Lists.newArrayList();
        Supplier<List<DashboardAdSalesmanDto>> momList = () -> odsAmazonAdProductReportDao
                .queryAdSalesmanCharts(puid, shopIdList, marketplaceIdList, devYoyListSup, currency, req.getMomStartDate(),
                        req.getMomEndDate(), null, null, req.getPortfolioIds(), req.getCampaignIds(), null, null, false);
        String spSubSqlC = odsAmazonAdProductReportDao.adProductGroupByDevIdQuerySql(puid, shopIdList, marketplaceIdList, null,
                currency, req.getMomStartDate(),req.getMomEndDate(), spArgsListSecond, null, null, req.getPortfolioIds(), req.getCampaignIds(), null, null, false);
        Supplier<List<DashboardAdSalesmanTopDataDto>> currentAndMomList = () -> odsAmazonAdProductReportDao.queryAdSalesmanYoyOrMomTop(spSubSqlA, spSubSqlC,
                spArgsListSecond, dataField, orderField, req.getOrderBy(), limit, req.getNoZero());
        List<Integer> devMomIdList = Lists.newArrayList();
        //还需要查同比
        Supplier<List<DashboardAdSalesmanDto>> yoyList = () -> odsAmazonAdProductReportDao.queryAdSalesmanCharts(puid, shopIdList,
                marketplaceIdList, devMomIdList, currency, req.getYoyStartDate(),req.getYoyEndDate(), null, null, req.getPortfolioIds(), req.getCampaignIds(), null, null, false);
        Function<List<Integer>, Map<String, String>> devNameMapFunc = devIds -> {
            if (CollectionUtils.isEmpty(devIds)) {
                return new HashMap<>();
            }
            return userService.getUserNameIdMap(devIds);
        };
        return getAdSalesmanTopList(req, req.getPuid(), orderField, currentAndYoyList, devYoyListSup, momList, currentAndMomList,
                devMomIdList, yoyList, req.getYoyOverLimit(), devNameMapFunc);
    }


    private Map<String,List<DashboardAdSalesmanDto>> getAdSalesmanTopList(DashboardAdSalesmanReqVo req, Integer puid, DashboardOrderByRateEnum orderField,
                                                              Supplier<List<DashboardAdSalesmanTopDataDto>> currentAndYoyListSup, List<Integer> devYoyListSup,
                                                              Supplier<List<DashboardAdSalesmanDto>> momListSup, Supplier<List<DashboardAdSalesmanTopDataDto>> currentAndMomListSup,
                                                              List<Integer> devMomListSup, Supplier<List<DashboardAdSalesmanDto>> yoyListSup,
                                                              boolean yoyOverLimit, Function<List<Integer>, Map<String, String>> devNameMapFunc) {

        DashboardAdSalesmanDto momSummary = new DashboardAdSalesmanDto();
        CalculateAdDataUtil.buildZeroAdCalData(momSummary);
        DashboardAdSalesmanDto yoySummary = new DashboardAdSalesmanDto();
        CalculateAdDataUtil.buildZeroAdCalData(yoySummary);
        DashboardAdSalesmanDto summary = new DashboardAdSalesmanDto();
        CalculateAdDataUtil.buildZeroAdCalData(summary);
        DashboardAdSalesmanDto limitSummary = new DashboardAdSalesmanDto();
        CalculateAdDataUtil.buildZeroAdCalData(limitSummary);
        //查询当前日期时间段内广告活动图表信息
        //当前时间段查询sql
        List<DashboardAdSalesmanTopDataDto> currentAndSubList;
        List<DashboardAdSalesmanDto> resultList = Lists.newArrayList();
        //如果是按占比排序，即当前时间段值/汇总值,先计算同比,或按同比排序，即当前时间段值/同比时间段值
        if (DashboardOrderByRateEnum.PERCENT == orderField || Stream.of(DashboardOrderByRateEnum.YOY_RATE,
                DashboardOrderByRateEnum.YOY_VALUE).anyMatch(r -> r == orderField)) {
            //同比sql
            currentAndSubList = currentAndYoyListSup.get();
            List<Integer> devIdList = currentAndSubList.stream().map(DashboardAdSalesmanTopDataDto::getDevId)
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(devIdList)) {
                summary = convertSummaryData(currentAndSubList.get(0));

                devYoyListSup.addAll(devIdList);
                //还需要查询环比
                List<DashboardAdSalesmanDto> momList = momListSup.get();
                Map<Integer, DashboardAdSalesmanDto> momSalesmanDtoMap = momList.parallelStream()
                        .collect(Collectors.toMap(DashboardAdSalesmanDto::getDevId, Function.identity()));
                BigDecimal[] sumShopSale = new BigDecimal[]{new BigDecimal(0)};
                Integer[] sumShopSaleNum = new Integer[]{new Integer(0)};

                resultList = currentAndSubList.stream().map(c -> {
                    //当前查询list已经包含了当期及同比的计算属性和汇总属性
                    //还需要计算环比占比，同比占比
                    DashboardAdSalesmanDto momGroup = Optional.ofNullable(momSalesmanDtoMap.get(c.getDevId())).orElseGet(() -> {
                        DashboardAdSalesmanDto tempDto = new DashboardAdSalesmanDto();
                        CalculateAdDataUtil.buildZeroAdCalData(tempDto);
                        return tempDto;
                    });
                    DashboardAdSalesmanDto currentDto = new DashboardAdSalesmanDto();
                    BeanUtils.copyProperties(c, currentDto, ParamCopyUtil.checkPropertiesNullOrEmptySuper(c));
                    DashboardAdSalesmanDto yoyDto = convertBasicAndCalData(c);
                    summaryDto(yoySummary, yoyDto);
                    summaryDto(limitSummary, currentDto);
                    DashboardAdSalesmanDto summaryDto = convertSummaryData(c);
                    computeData(currentDto, yoyDto, momGroup, summaryDto, yoyOverLimit, sumShopSale, sumShopSaleNum);
                    return currentDto;
                }).collect(Collectors.toList());
                //计算同比汇总
                computeData(summary, yoySummary, momSummary, summary, yoyOverLimit, sumShopSale, sumShopSaleNum);
                computeData(limitSummary, yoySummary, momSummary, summary, yoyOverLimit, sumShopSale, sumShopSaleNum);computeData(limitSummary, yoySummary, momSummary, summary, yoyOverLimit, sumShopSale, sumShopSaleNum);
            }
        }

        //如果是按环比排序，即当前时间段值/环比时间段值
        if (Stream.of(DashboardOrderByRateEnum.MOM_RATE,
                DashboardOrderByRateEnum.MOM_VALUE).anyMatch(r -> r == orderField)) {
            currentAndSubList = currentAndMomListSup.get();
            List<Integer> devIdList = currentAndSubList.stream().map(DashboardAdSalesmanTopDataDto::getDevId)
                    .collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(devIdList)) {
                summary = convertSummaryData(currentAndSubList.get(0));
                CalculateAdDataUtil.buildZeroAdCalData(limitSummary);
                devMomListSup.addAll(devIdList);
                //还需要查同比
                List<DashboardAdSalesmanDto> yoyList = yoyListSup.get();
                BigDecimal[] sumShopSale = new BigDecimal[]{new BigDecimal(0)};
                Integer[] sumShopSaleNum = new Integer[]{new Integer(0)};
                Map<Integer, DashboardAdSalesmanDto> yoyMap = yoyList.parallelStream()
                        .collect(Collectors.toMap(DashboardAdSalesmanDto::getDevId, v1 -> v1));
                resultList = currentAndSubList.stream().map(c -> {
                    DashboardAdSalesmanDto yoyGroup = Optional.ofNullable(yoyMap.get(c.getDevId())).orElseGet(() -> {
                        DashboardAdSalesmanDto tempDto = new DashboardAdSalesmanDto();
                        CalculateAdDataUtil.buildZeroAdCalData(tempDto);
                        return tempDto;
                    });
                    DashboardAdSalesmanDto currentDto = new DashboardAdSalesmanDto();
                    BeanUtils.copyProperties(c, currentDto, ParamCopyUtil.checkPropertiesNullOrEmptySuper(c));
                    summaryDto(limitSummary, currentDto);
                    DashboardAdSalesmanDto monDto = convertBasicAndCalData(c);
                    summaryDto(momSummary, monDto);
                    DashboardAdSalesmanDto summaryDto = convertSummaryData(c);
                    computeData(currentDto, yoyGroup, monDto, summaryDto, yoyOverLimit, sumShopSale, sumShopSaleNum);
                    return currentDto;
                }).collect(Collectors.toList());
                //计算同比汇总
                computeData(summary, yoySummary, momSummary, summary, yoyOverLimit, sumShopSale, sumShopSaleNum);
                computeData(limitSummary, yoySummary, momSummary, summary, yoyOverLimit, sumShopSale, sumShopSaleNum);
            }
        }

        List<Integer> devId = resultList.parallelStream().map(DashboardAdSalesmanDto::getDevId)
                .filter(Objects::nonNull).collect(Collectors.toList());
        Map<String, String> devNameMap = devNameMapFunc.apply(devId);

        resultList.forEach(r -> {
            String name = devNameMap.get(r.getDevId().toString());
            Optional.ofNullable(name).ifPresent(r::setName);
        });
        HashMap<String, List<DashboardAdSalesmanDto>> result = new HashMap<>();
        result.put("list", resultList);
        result.put("summary", Lists.newArrayList(limitSummary));
//        result.put("totalSummary", Lists.newArrayList(summary));
        DashboardAdSalesmanDto totalSummary = getTotalSummary(req, puid, req.getShopIdList(), req.getMarketplaceIdList(), req.getCurrency(), yoyOverLimit);
        result.put("totalSummary", Lists.newArrayList(totalSummary));

        return result;
    }

    private DashboardAdSalesmanDto getTotalSummary(DashboardAdSalesmanReqVo req, Integer puid,
                                                   List<Integer> shopIdList, List<String> marketplaceIdList,
                                                   String currency, boolean yoyOverLimit) {
        List<String> siteToday = null;
        if (Boolean.TRUE.equals(req.getSiteToday())) {
            siteToday = CalculateAdDataUtil.getSiteToday(marketplaceIdList);
        }
        //查出最大范围内的数据， 一次查出所有数据，按日期来分组，最终匹配同比，环比，当期数据
        LocalDate start =LocalDate.parse(req.getStartDate(), DateTimeFormatter.ISO_DATE);
        LocalDate momStart = LocalDate.parse(req.getMomStartDate(), DateTimeFormatter.ISO_DATE);
        LocalDate yoyStart = LocalDate.parse(req.getYoyStartDate(), DateTimeFormatter.ISO_DATE);
        LocalDate end =LocalDate.parse(req.getEndDate(), DateTimeFormatter.ISO_DATE);
        LocalDate momEnd = LocalDate.parse(req.getMomEndDate(), DateTimeFormatter.ISO_DATE);
        LocalDate yoyEnd = LocalDate.parse(req.getYoyEndDate(), DateTimeFormatter.ISO_DATE);
        LocalDate min = Lists.newArrayList(start, momStart, yoyStart).stream().min(LocalDate::compareTo).get();
        LocalDate max = Lists.newArrayList(end, momEnd, yoyEnd).stream().max(LocalDate::compareTo).get();
        List<DashboardAdSalesmanDto> dashboardAdSalesmanDtos = odsAmazonAdProductReportDao.queryAdSalesmanCharts(puid, shopIdList,
                marketplaceIdList, null, currency, min.format(DateTimeFormatter.ISO_DATE), max.format(DateTimeFormatter.ISO_DATE), siteToday, req.getSiteToday(), req.getPortfolioIds(), req.getCampaignIds(), null, null, true);
        //分组算总和
        DashboardAdSalesmanDto momSummary = new DashboardAdSalesmanDto();
        CalculateAdDataUtil.buildZeroAdCalData(momSummary);
        DashboardAdSalesmanDto yoySummary = new DashboardAdSalesmanDto();
        CalculateAdDataUtil.buildZeroAdCalData(yoySummary);
        DashboardAdSalesmanDto summary = new DashboardAdSalesmanDto();
        CalculateAdDataUtil.buildZeroAdCalData(summary);
        dashboardAdSalesmanDtos.forEach(e-> {
            if (LocalDateTimeUtil.isBetween(start, end, e.getCountDay())) {
                summaryDto(summary, e);
            }
            if (LocalDateTimeUtil.isBetween(momStart, momEnd, e.getCountDay())) {
                summaryDto(momSummary, e);
            }
            if (LocalDateTimeUtil.isBetween(yoyStart, yoyEnd, e.getCountDay())) {
                summaryDto(yoySummary, e);
            }
        });
        calAdCalData(summary);
        calAdCalData(momSummary);
        calAdCalData(yoySummary);
        //计算同比汇总
        Integer[] sumShopSaleNum = {0};
        BigDecimal[] sumShopSales = {BigDecimal.ZERO};
        computeData(summary, yoySummary, momSummary, summary, yoyOverLimit, sumShopSales, sumShopSaleNum);
        return summary;
    }


    private <T> Field[] getAllField(Class<T> cla) {
        Class clazz = cla;
        List<Field> fieldList = new ArrayList<>();
        while (clazz != null){
            fieldList.addAll(new ArrayList<>(Arrays.asList(clazz.getDeclaredFields())));
            clazz = clazz.getSuperclass();
        }
        Field[] fields = new Field[fieldList.size()];
        fieldList.toArray(fields);
        return fields;
    }

    private String writeExcelAndUpload(List<DashboardAdProductDto> groupList, DashboardAdProductReqVo reqVo) {
        List<String> headers = new ArrayList<>(baseHeaderList);
        //计算导出列
        if (reqVo.getPercent() != 1) {
            headers = headers.stream().filter(x -> !x.contains("Percent")).collect(Collectors.toList());
        }
        if (reqVo.getMom() != 1) {
            headers = headers.stream().filter(x -> !x.contains("MomRate")).collect(Collectors.toList());
        }
        if (reqVo.getYoy() != 1) {
            headers = headers.stream().filter(x -> !x.contains("YoyRate")).collect(Collectors.toList());
        }

        String fileName = "";
        if (DashboardQueryFieldEnum.SKU_QUERY_TYPE.getCode().equals(reqVo.getQueryField())) {
            headers.add(0, "msku");
            fileName = "MSKU";
        } else if (DashboardQueryFieldEnum.ASIN_QUERY_TYPE.getCode().equals(reqVo.getQueryField())) {
            headers.add(0, "asin");
            fileName = "ASIN";
        } else if (DashboardQueryFieldEnum.PARENT_ASIN_TYPE.getCode().equals(reqVo.getQueryField())) {
            headers.add(0, "parentAsin");
            fileName = "父ASIN";
        }

        String orderBy = Optional.ofNullable(DashboardOrderByEnum.orderByMap.get(reqVo.getOrderBy()))
                .map(DashboardOrderByEnum::getExportDesc).orElse("");
        String limitCnt = Optional.ofNullable(reqVo.getLimit()).map(String::valueOf).orElse("");
        //写excel并上传
        return excelService.easyExcelHandlerDownload(reqVo.getPuid(), groupList, fileName + orderBy + limitCnt,
                DashboardAdProductDto.class, headers, true);

    }

    /**
     * 填充店铺、站点销售额
     */
    private void fillSaleMap(List<Integer> shopIds, DashboardAdProductReqVo req, Map<String, BigDecimal> salesMap,
                             Map<String, Integer> saleNumMap,
                             Map<String, BigDecimal> yoySalesMap, Map<String, Integer> yoySaleNumMap,
                             Map<String, BigDecimal> momSalesMap, Map<String, Integer> momSaleNumMap,
                             BigDecimal[] sumShopSale, Integer[] sumShopSaleNum) {

        List<ShopSaleDto> saleDtos = cpCShopDataService.shopSaleGroupByShop(req.getPuid(), shopIds, null, req.getStartDate(), req.getEndDate(), req.getCurrency());

        List<ShopSaleDto> yoySaleDtos = cpCShopDataService.shopSaleGroupByShop(req.getPuid(), shopIds, null, req.getYoyStartDate(), req.getYoyEndDate(), req.getCurrency());

        List<ShopSaleDto> momSaleDtos = cpCShopDataService.shopSaleGroupByShop(req.getPuid(), shopIds, null, req.getMomStartDate(), req.getMomEndDate(), req.getCurrency());
        saleDtos.forEach(e->{
            salesMap.put(e.getShopId().toString(), e.getSumRange());
            saleNumMap.put(e.getShopId().toString(), e.getSaleNum());
            sumShopSale[0] = MathUtil.sum(sumShopSale[0], e.getSumRange());
            sumShopSaleNum[0] = MathUtil.sum(sumShopSaleNum[0], e.getSaleNum());
        });
        yoySaleDtos.forEach(e->{
            yoySalesMap.put(e.getShopId().toString(), e.getSumRange());
            yoySaleNumMap.put(e.getShopId().toString(), e.getSaleNum());

        });
        momSaleDtos.forEach(e->{
            momSalesMap.put(e.getShopId().toString(), e.getSumRange());
            momSaleNumMap.put(e.getShopId().toString(), e.getSaleNum());
        });

    }



    private void computeData(DashboardAdSalesmanDto dto, DashboardAdSalesmanDto yoyDto,
                             DashboardAdSalesmanDto momDto, DashboardAdSalesmanDto summary,
                             boolean yoyOverLimit, BigDecimal[] sumShopSales, Integer[] sumShopSaleNum) {
        if (Objects.isNull(dto)) {
            return;
        }
        CalculateAdDataUtil.calAdCalData(dto);
        CalculateAdDataUtil.calAdCalShopData(dto);
        if (yoyOverLimit) {
            CalculateAdDataUtil.calAdYoyData(dto, null);
            CalculateAdDataUtil.calAdYoyShopData(dto, null);
        } else if (Objects.nonNull(yoyDto)) {
            CalculateAdDataUtil.calAdCalDataScale4(yoyDto);//同比数据只包含基本数据查结构，需要先计算出计算属性值
            CalculateAdDataUtil.calAdYoyValueReflexAccuracy(dto, yoyDto);//填充环比增长值
            CalculateAdDataUtil.calAdYoyDataNoPercentReflex(dto, yoyDto);//填充环比增长率
            CalculateAdDataUtil.calAdCalShopDataScale4(yoyDto);//同比数据只包含基本数据查结构，需要先计算出计算属性值
            calAdYoyShopValueReflex(dto, yoyDto);//填充环比增长值
            calAdYoyShopDataReflex(dto, yoyDto);//填充环比增长率
            setYoy(dto, yoyDto);
        }
        if (Objects.nonNull(momDto)) {
            CalculateAdDataUtil.calAdCalDataScale4(momDto);//同比数据只包含基本数据查结构，需要先计算出计算属性值
            CalculateAdDataUtil.calAdMomValueReflexAccuracy(dto, momDto);//填充同比增长值
            CalculateAdDataUtil.calAdMomDataNoPercentReflex(dto, momDto);//填充同比增长率
            CalculateAdDataUtil.calAdCalShopDataScale4(momDto);//同比数据只包含基本数据查结构，需要先计算出计算属性值
            calAdMomShopValueReflex(dto, momDto);//填充同比增长值
            calAdMomShopDataReflex(dto, momDto);//填充同比增长率
            setMom(dto, momDto);
        }

        CalculateAdDataUtil.calAdPercentDataNoPercent(dto, summary);
        CalculateAdDataUtil.calAdCalData(dto);
        //设置店铺销售额占比
        log.info("total sales about all shop is:{}", sumShopSales[0]);
        //dto.setShopSalesPercent(CalculateUtil.calPercentStr4Decimal(dto.getShopSales(), sumShopSales[0]));
        //dto.setShopSaleNumPercent(CalculateUtil.calPercentStr4Int(dto.getShopSaleNum(), sumShopSaleNum[0]));
    }

    private void calAdMomShopDataReflex(DashboardAdSalesmanDto cur, DashboardAdSalesmanDto mom) {
        //cur.setShopSalesMomRate(CalculateUtil.calRate4Decimal(cur.getShopSales(), mom.getShopSales()));
        cur.setAcotsMomRate(CalculateUtil.calRate4Decimal(cur.getAcots(), mom.getAcots()));
        cur.setAsotsMomRate(CalculateUtil.calRate4Decimal(cur.getAsots(), mom.getAsots()));
        //cur.setShopSaleNumMomRate(CalculateUtil.calRate4Int(cur.getShopSaleNum(), mom.getShopSaleNum()));
    }


    private void calAdMomShopValueReflex(DashboardAdSalesmanDto cur, DashboardAdSalesmanDto mom) {
        //cur.setShopSalesMomValue(CalculateUtil.calValueStr4Decimal(cur.getShopSales(), mom.getShopSales()));
        cur.setAcotsMomValue(CalculateUtil.calValueStr4Decimal(cur.getAcots(), mom.getAcots()));
        cur.setAcotsMomValue(CalculateUtil.calValueStr4Decimal(cur.getAsots(), mom.getAsots()));
        //cur.setShopSaleNumMomValue(CalculateUtil.calValueStr4Int(cur.getShopSaleNum(), mom.getShopSaleNum()));

    }


    private void calAdYoyShopValueReflex(DashboardAdSalesmanDto cur, DashboardAdSalesmanDto mom) {
        //cur.setShopSalesYoyValue(CalculateUtil.calValueStr4Decimal(cur.getShopSales(), mom.getShopSales()));
        cur.setAcotsYoyValue(CalculateUtil.calValueStr4Decimal(cur.getAcots(), mom.getAcots()));
        cur.setAcotsYoyValue(CalculateUtil.calValueStr4Decimal(cur.getAsots(), mom.getAsots()));
        //cur.setShopSaleNumYoyValue(CalculateUtil.calValueStr4Int(cur.getShopSaleNum(), mom.getShopSaleNum()));

    }


    private void calAdYoyShopDataReflex(DashboardAdSalesmanDto cur, DashboardAdSalesmanDto mom) {
        //cur.setShopSalesYoyRate(CalculateUtil.calRate4Decimal(cur.getShopSales(), mom.getShopSales()));
        cur.setAcotsYoyRate(CalculateUtil.calRate4Decimal(cur.getAcots(), mom.getAcots()));
        cur.setAsotsYoyRate(CalculateUtil.calRate4Decimal(cur.getAsots(), mom.getAsots()));
        //cur.setShopSaleNumYoyRate(CalculateUtil.calRate4Int(cur.getShopSaleNum(), mom.getShopSaleNum()));
    }


    private void summaryDto(DashboardAdSalesmanDto e1, DashboardAdSalesmanDto e2) {
        e1.setClicks(MathUtil.sum(e1.getClicks(), e2.getClicks()));
        e1.setImpressions(MathUtil.sumLong(e1.getImpressions(), e2.getImpressions()));
        e1.setCost(MathUtil.sum(e1.getCost(), e2.getCost()));
        e1.setTotalSales(MathUtil.sum(e1.getTotalSales(), e2.getTotalSales()));
        e1.setOrderNum(MathUtil.sum(e1.getOrderNum(), e2.getOrderNum()));
        e1.setSaleNum(MathUtil.sum(e1.getSaleNum(), e2.getSaleNum()));
    }

    public static void calAdCalData(DashboardAdSalesmanDto calDataDto) {
        calDataDto.setAcos(CalculateUtil.calPercent4Decimal(Optional.ofNullable(calDataDto.getCost())
                        .map(c -> c.setScale(2, RoundingMode.HALF_UP)).orElse(BigDecimal.ZERO),
                Optional.ofNullable(calDataDto.getTotalSales())
                        .map(c -> c.setScale(2, RoundingMode.HALF_UP)).orElse(BigDecimal.ZERO)));
        calDataDto.setRoas(CalculateUtil.calPercent4Decimal(Optional.ofNullable(calDataDto.getTotalSales())
                        .map(c -> c.setScale(2, RoundingMode.HALF_UP)).orElse(BigDecimal.ZERO),
                Optional.ofNullable(calDataDto.getCost())
                        .map(c -> c.setScale(2, RoundingMode.HALF_UP)).orElse(BigDecimal.ZERO)));
        calDataDto.setClickRate(CalculateUtil.calPercent4LongScale4(Long.parseLong(calDataDto.getClicks().toString()), calDataDto.getImpressions()));
        calDataDto.setConversionRate(CalculateUtil.calPercent4IntScale4(calDataDto.getOrderNum(), calDataDto.getClicks()));
        calDataDto.setCpc(CalculateUtil.calPercent4DecimalAndIntScale4(calDataDto.getCost(), calDataDto.getClicks()));
        calDataDto.setCpa(CalculateUtil.calPercent4DecimalAndIntScale4(Optional.ofNullable(calDataDto.getCost())
                .map(c -> c.setScale(2, RoundingMode.HALF_UP)).orElse(BigDecimal.ZERO), calDataDto.getOrderNum()));
    }






    public static DashboardAdSalesmanResponseVo.Page getPageInfo(List<DashboardAdSalesmanResponseVo.Page.TopList> dataList, Integer pageSize,
                                                                         Integer pageNo) {
        //总页数
        // 找出当前要显示页(pageNo)的开始记录号"start"和结束记录号"end",以便只把当前页的数据给找出来 ******/
        int totalSize = dataList.size();
        int totalPage = (totalSize - 1) / pageSize + 1;
        //如果当前页大于总页数,则显示最后一页
        pageNo = Math.min(pageNo, totalPage);
        //如果当前页小于0,则显示第一页
        pageNo = Math.max(pageNo, 1);
        //记录开始值
        int start = pageSize * (pageNo - 1);
        dataList = dataList.subList(start, totalSize > pageSize? pageSize:totalSize);
        DashboardAdSalesmanResponseVo.Page.Builder page = DashboardAdSalesmanResponseVo.Page.newBuilder();
        page.addAllRows(dataList);
        page.setPageNo(pageNo);
        page.setPageSize(pageSize);
        page.setTotalPage(totalPage);
        page.setTotalSize(totalSize);
        return page.build();
    }


    private void setMom(DashboardAdSalesmanDto cur, DashboardAdSalesmanDto mom) {
        cur.setCostMom(coverStr2(mom.getCost()));
        cur.setTotalSalesMom(coverStr2(mom.getTotalSales()));
        cur.setImpressionsMom(coverStr(mom.getImpressions()));
        cur.setClicksMom(coverStr(mom.getClicks()));
        cur.setOrderNumMom(coverStr(mom.getOrderNum()));
        cur.setAcosMom(coverStr5(mom.getAcos()));
        cur.setRoasMom(coverStr2(mom.getRoas()));
        cur.setClickRateMom(coverStr5(mom.getClickRate()));
        cur.setConversionRateMom(coverStr5(mom.getConversionRate()));
        cur.setCpcMom(coverStr2(mom.getCpc()));
        cur.setCpaMom(coverStr2(mom.getCpa()));
        cur.setSaleNumMom(coverStr(mom.getSaleNum()));
    }

    private void setYoy(DashboardAdSalesmanDto cur, DashboardAdSalesmanDto mom) {
        cur.setCostYoy(coverStr2(mom.getCost()));
        cur.setTotalSalesYoy(coverStr2(mom.getTotalSales()));
        cur.setImpressionsYoy(coverStr(mom.getImpressions()));
        cur.setClicksYoy(coverStr(mom.getClicks()));
        cur.setOrderNumYoy(coverStr(mom.getOrderNum()));
        cur.setAcosYoy(coverStr5(mom.getAcos()));
        cur.setRoasYoy(coverStr2(mom.getRoas()));
        cur.setClickRateYoy(coverStr5(mom.getClickRate()));
        cur.setConversionRateYoy(coverStr5(mom.getConversionRate()));
        cur.setCpcYoy(coverStr2(mom.getCpc()));
        cur.setCpaYoy(coverStr2(mom.getCpa()));
        cur.setSaleNumYoy(coverStr(mom.getSaleNum()));
    }

    private String coverStr2(BigDecimal val) {
        if (val == null) {
            return "0";
        }
        return val.setScale(2, RoundingMode.HALF_UP).toString();
    }

    private String coverStr5(BigDecimal val) {
        if (val == null) {
            return "0";
        }
        return val.setScale(5, RoundingMode.HALF_UP).toString();
    }

    private String coverStr(Integer val) {
        if (val == null) {
            return "0";
        }
        return val.toString();
    }
    private String coverStr(Long val) {
        if (val == null) {
            return "0";
        }
        return val.toString();
    }

}
