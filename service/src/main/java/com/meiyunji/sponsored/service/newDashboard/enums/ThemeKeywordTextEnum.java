package com.meiyunji.sponsored.service.newDashboard.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * sb主题投放枚举映射
 */
@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum ThemeKeywordTextEnum {

    KEYWORDS_RELATED_TO_YOUR_BRAND("KEYWORDS_RELATED_TO_YOUR_BRAND","keywords-related-to-your-brand", "与您的品牌相关的关键词"),
    KEYWORDS_RELATED_TO_YOUR_LANDING_PAGES("KEYWORDS_RELATED_TO_YOUR_LANDING_PAGES","keywords-related-to-your-landing-pages", "与您的落地页相关的关键词"),
    ;

    private String code;
    private String text;
    private String textCn;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public String getTextCn() {
        return textCn;
    }

    public void setTextCn(String textCn) {
        this.textCn = textCn;
    }

    public static String getTextByCode(String code) {
        for (ThemeKeywordTextEnum value : ThemeKeywordTextEnum.values()) {
            if (code.equals(value.getCode())) {
                return value.getText();
            }
        }
        return null;
    }

    public static String getCodeByText(String text) {
        for (ThemeKeywordTextEnum value : ThemeKeywordTextEnum.values()) {
            if (text.equals(value.getText())) {
                return value.getCode();
            }
        }
        return null;
    }

    public static ThemeKeywordTextEnum getEnumByCode(String code) {
        for (ThemeKeywordTextEnum value : ThemeKeywordTextEnum.values()) {
            if (code.equals(value.getCode())) {
                return value;
            }
        }
        return null;
    }

}
