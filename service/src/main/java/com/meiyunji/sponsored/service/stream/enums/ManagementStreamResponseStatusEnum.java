package com.meiyunji.sponsored.service.stream.enums;

import lombok.Getter;
import org.apache.http.HttpStatus;

import java.util.Arrays;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @author: ys
 * @date: 2023/12/1 9:12
 * @describe:
 */

@Getter
public enum ManagementStreamResponseStatusEnum {
    INTERNAL_SERVER_ERROR(HttpStatus.SC_INTERNAL_SERVER_ERROR),
    GATEWAY_TIMEOUT(HttpStatus.SC_GATEWAY_TIMEOUT),
    BAD_GATEWAY(HttpStatus.SC_BAD_GATEWAY),
    SERVICE_UNAVAILABLE(HttpStatus.SC_SERVICE_UNAVAILABLE),
    SERVICE_UNAUTHORIZED(HttpStatus.SC_UNAUTHORIZED),
    OTHER_EXCEPTION(-1),
    ;
    private int code;

    public static Set<Integer> statusCodeSet = Arrays.stream(ManagementStreamResponseStatusEnum.values()).map(ManagementStreamResponseStatusEnum::getCode).collect(Collectors.toSet());

    ManagementStreamResponseStatusEnum(int code) {
        this.code = code;
    }


}
