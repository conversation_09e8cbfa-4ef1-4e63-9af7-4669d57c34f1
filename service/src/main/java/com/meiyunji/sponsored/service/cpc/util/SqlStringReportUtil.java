package com.meiyunji.sponsored.service.cpc.util;

import com.meiyunji.sponsored.common.enums.OrderTypeEnum;
import com.meiyunji.sponsored.service.cpc.dto.NeTargetReportAdvanceFiltersDto;
import com.meiyunji.sponsored.service.cpc.qo.ReportAdvancedFilterBaseQo;
import com.meiyunji.sponsored.service.enums.NeTargetReportParamsMappingEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * @author: liweibin
 * @email: <EMAIL>
 * @date: 2023-12-28  11:15
 */
public class SqlStringReportUtil {
    public static final String BID = "bid";
    public static final String ID = "id";
    public static final String DATA_UPDATE_TIME = "data_update_time";
    public static final String DAILYBUDGET = "dailyBudget";
    public static final String NAME = "name";
    public static final String STARTDATE = "startDate";
    public static final String ENDDATE = "endDate";

    @Getter
    @AllArgsConstructor
    public enum TargetReportSumEnum {
        impressions("曝光量", "sum(impressions) impressions"),
        clicks("点击量", "sum(clicks) clicks"),
        cost("花费", "sum(cost) cost"),
        sale_num("广告订单量", "sum(sale_num) sale_num"),
        ad_sale_num("本广告产品订单量", "sum(ad_sale_num) ad_sale_num"),
        total_sales("广告销售额", "sum(total_sales) total_sales"),
        ad_sales("本广告产品销售额", "sum(ad_sales) ad_sales"),
        order_num("广告销量", "sum(order_num) order_num"),
        ad_order_num("本广告产品销量", "sum(ad_order_num) ad_order_num"),
        max_top_of_search_is("搜索结果首页首位IS最大值", "max(top_of_search_is) max_top_of_search_is"),
        min_top_of_search_is("搜索结果首页首位IS最小值", "min(top_of_search_is) min_top_of_search_is"),
        ;

        private String desc;
        private String field;

    }

    public static String getCampaignHavingSqlNew(ReportAdvancedFilterBaseQo param, List<Object> argsList) {
        StringBuilder subWhereSql = new StringBuilder();
        BigDecimal shopSales = param.getShopSales() != null ? param.getShopSales() : BigDecimal.valueOf(0);
        final String SALE_NUM = "if (type = 'sp' ,`units_ordered7d`,if (type = 'sb' ,`units_sold14d`,`units_ordered14d`))";
        final String AD_SALE_NUM = "if (type = 'sp',`units_ordered7d_same_sku`,`units_ordered14d_same_sku`)";
        final String TOTAL_SALES = "if (type = 'sp', sales7d,sales14d)";
        final String AD_SALES = "if (type = 'sp', sales7d_same_sku,sales14d_same_sku)";
        final String ORDER_NUM = "if (type = 'sp', conversions7d,conversions14d)";
        final String AD_ORDER_NUM = "if (type = 'sp' ,`conversions7d_same_sku`,`conversions14d_same_sku`)";
        final String VIEW_IMPRESSIONS = "if (type = 'sb', `viewable_impressions`, `view_impressions`)";

        //高级筛选
        subWhereSql.append(" having 1=1 ");
        //展示量
        if (param.getImpressionsMin() != null) {
            subWhereSql.append(" and ifnull(sum(impressions), 0) >= ?");
            argsList.add(param.getImpressionsMin());
        }
        if (param.getImpressionsMax() != null) {
            subWhereSql.append(" and ifnull(sum(impressions), 0) <= ?");
            argsList.add(param.getImpressionsMax());
        }
        //点击量
        if (param.getClicksMin() != null) {
            subWhereSql.append(" and ifnull(sum(clicks), 0) >= ?");
            argsList.add(param.getClicksMin());
        }
        if (param.getClicksMax() != null) {
            subWhereSql.append(" and ifnull(sum(clicks), 0) <= ?");
            argsList.add(param.getClicksMax());
        }
        //点击率（clicks/impressions）
        if (param.getClickRateMin() != null) {
            subWhereSql.append(" and ROUND(ifnull(sum(clicks)/sum(impressions),0),4) >= ?");
            argsList.add(param.getClickRateMin());
        }
        if (param.getClickRateMax() != null) {
            subWhereSql.append(" and ROUND(ifnull(sum(clicks)/sum(impressions),0),4) <= ?");
            argsList.add(param.getClickRateMax());
        }
        //花费
        if (param.getCostMin() != null) {
            subWhereSql.append(" and ifnull(sum(cost), 0) >= ?");
            argsList.add(param.getCostMin());
        }
        if (param.getCostMax() != null) {
            subWhereSql.append(" and ifnull(sum(cost), 0) <= ?");
            argsList.add(param.getCostMax());
        }
        //cpc  平均点击费用
        if (param.getCpcMin() != null) {
            subWhereSql.append(" and ROUND(ifnull(sum(cost)/sum(clicks),0),2) >= ?");
            argsList.add(param.getCpcMin());
        }
        if (param.getCpcMax() != null) {
            subWhereSql.append(" and ROUND(ifnull(sum(cost)/sum(clicks),0),2) <= ?");
            argsList.add(param.getCpcMax());
        }
        //广告订单量
        if (param.getOrderNumMin() != null) {
            subWhereSql.append(" and ifnull(sum(").append(ORDER_NUM).append("), 0) >= ?");
            argsList.add(param.getOrderNumMin());
        }
        if (param.getOrderNumMax() != null) {
            subWhereSql.append(" and ifnull(sum(").append(ORDER_NUM).append("), 0) <= ?");
            argsList.add(param.getOrderNumMax());
        }
        //广告销售额
        if (param.getSalesMin() != null) {
            subWhereSql.append(" and ifnull(sum(").append(TOTAL_SALES).append("), 0) >= ?");
            argsList.add(param.getSalesMin());
        }
        if (param.getSalesMax() != null) {
            subWhereSql.append(" and ifnull(sum(").append(TOTAL_SALES).append("), 0) <= ?");
            argsList.add(param.getSalesMax());
        }
        //订单转化率
        if (param.getSalesConversionRateMin() != null) {
            subWhereSql.append(" and ROUND(ifnull(sum(").append(ORDER_NUM).append(")/sum(clicks),0),4) >= ?");
            argsList.add(param.getSalesConversionRateMin());
        }
        if (param.getSalesConversionRateMax() != null) {
            subWhereSql.append(" and ROUND(ifnull(sum(").append(ORDER_NUM).append(")/sum(clicks),0),4) <= ?");
            argsList.add(param.getSalesConversionRateMax());
        }
        //acos
        if (param.getAcosMin() != null) {
            subWhereSql.append(" and ROUND(ifnull(sum(cost)/sum(").append(TOTAL_SALES).append("),0),4) >= ?");
            argsList.add(param.getAcosMin());
        }
        if (param.getAcosMax() != null) {
            subWhereSql.append(" and ROUND(ifnull(sum(cost)/sum(").append(TOTAL_SALES).append("),0),4) <= ?");
            argsList.add(param.getAcosMax());
        }
        // roas
        if (param.getRoasMin() != null) {
            subWhereSql.append(" and ROUND(ifnull(sum(").append(TOTAL_SALES).append(")/sum(cost),0),2) >= ?");
            argsList.add(param.getRoasMin());
        }
        // roas
        if (param.getRoasMax() != null) {
            subWhereSql.append(" and ROUND(ifnull(sum(").append(TOTAL_SALES).append(")/sum(cost),0),2) <= ?");
            argsList.add(param.getRoasMax());
        }
        // acots  需要乘以店铺销售额
        if (param.getAcotsMin() != null) {
            if (shopSales.doubleValue() > 0) {
                subWhereSql.append(" and ROUND((ifnull(sum(cost),0) / ").append(shopSales).append("),4) >= ? ");
                argsList.add(param.getAcotsMin());
            } else {
                subWhereSql.append(" and 0 >= ? ");
                argsList.add(param.getAcotsMin());
            }
        }
        // acots  需要乘以店铺销售额
        if (param.getAcotsMax() != null) {
            if (shopSales.doubleValue() > 0) {
                subWhereSql.append(" and ROUND((ifnull(sum(cost),0) / ").append(shopSales).append("),4) <= ? ");
                argsList.add(param.getAcotsMax());
            } else {
                subWhereSql.append(" and 0 <= ? ");
                argsList.add(param.getAcotsMax());
            }
        }
        // asots 需要乘以店铺销售额
        if (param.getAsotsMin() != null) {
            if (shopSales.doubleValue() > 0) {
                subWhereSql.append(" and ROUND((ifnull(sum(").append(TOTAL_SALES).append("),0) / ").append(shopSales).append("),4) >= ? ");
                argsList.add(param.getAsotsMin());
            } else {
                subWhereSql.append(" and 0 >= ? ");
                argsList.add(param.getAsotsMin());
            }
        }
        // asots  需要乘以店铺销售额
        if (param.getAsotsMax() != null) {
            if (shopSales.doubleValue() > 0) {
                subWhereSql.append(" and ROUND((ifnull(sum(").append(TOTAL_SALES).append("),0) / ").append(shopSales).append("),4) <= ? ");
                argsList.add(param.getAsotsMax());
            } else {
                subWhereSql.append(" and 0 <= ? ");
                argsList.add(param.getAsotsMax());
            }
        }
        //CPA
        if (param.getCpaMin() != null) {
            subWhereSql.append(" and ROUND(ifnull(sum(cost)/sum(").append(ORDER_NUM).append("),0), 2) >= ?");
            argsList.add(param.getCpaMin());
        }
        if (param.getCpaMax() != null) {
            subWhereSql.append(" and ROUND(ifnull(sum(cost)/sum(").append(ORDER_NUM).append("),0), 2) <= ?");
            argsList.add(param.getCpaMax());
        }

        //本广告产品订单量
        if (param.getAdSaleNumMin() != null) {
            subWhereSql.append(" and ifnull(sum(").append(AD_ORDER_NUM).append("), 0) >= ?");
            argsList.add(param.getAdSaleNumMin());
        }
        if (param.getAdSaleNumMax() != null) {
            subWhereSql.append(" and ifnull(sum(").append(AD_ORDER_NUM).append("), 0) <= ?");
            argsList.add(param.getAdSaleNumMax());
        }
        //其他产品广告订单量
        if (param.getAdOtherOrderNumMin() != null) {
            subWhereSql.append(" and ifnull(sum(").append(ORDER_NUM).append(") - sum(").append(AD_ORDER_NUM).append("), 0) >= ?");
            argsList.add(param.getAdOtherOrderNumMin());
        }
        if (param.getAdOtherOrderNumMax() != null) {
            subWhereSql.append(" and ifnull(sum(").append(ORDER_NUM).append(") - sum(").append(AD_ORDER_NUM).append("), 0) <= ?");
            argsList.add(param.getAdOtherOrderNumMax());
        }
        //本广告产品销售额
        if (param.getAdSalesMin() != null) {
            subWhereSql.append(" and ifnull(sum(").append(AD_SALES).append("), 0) >= ?");
            argsList.add(param.getAdSalesMin());
        }
        if (param.getAdSalesMax() != null) {
            subWhereSql.append(" and ifnull(sum(").append(AD_SALES).append("), 0) <= ?");
            argsList.add(param.getAdSalesMax());
        }
        //其他产品广告销售额
        if (param.getAdOtherSalesMin() != null) {
            subWhereSql.append(" and ifnull(sum(").append(TOTAL_SALES).append(") - sum(").append(AD_SALES).append("), 0) >= ?");
            argsList.add(param.getAdOtherSalesMin());
        }
        if (param.getAdOtherSalesMax() != null) {
            subWhereSql.append(" and ifnull(sum(").append(TOTAL_SALES).append(") - sum(").append(AD_SALES).append("), 0) <= ?");
            argsList.add(param.getAdOtherSalesMax());
        }
        //广告销量
        if (param.getAdSalesTotalMin() != null) {
            subWhereSql.append(" and ifnull(sum(").append(SALE_NUM).append("), 0) >= ?");
            argsList.add(param.getAdSalesTotalMin());
        }
        if (param.getAdSalesTotalMax() != null) {
            subWhereSql.append(" and ifnull(sum(").append(SALE_NUM).append("), 0) <= ?");
            argsList.add(param.getAdSalesTotalMax());
        }
        //本广告产品销量
        if (param.getAdSelfSaleNumMin() != null) {
            subWhereSql.append(" and ifnull(sum(").append(AD_SALE_NUM).append("), 0) >= ?");
            argsList.add(param.getAdSelfSaleNumMin());
        }
        if (param.getAdSelfSaleNumMax() != null) {
            subWhereSql.append(" and ifnull(sum(").append(AD_SALE_NUM).append("), 0) <= ?");
            argsList.add(param.getAdSelfSaleNumMax());
        }
        //其他产品广告销量
        if (param.getAdOtherSaleNumMin() != null) {
            subWhereSql.append(" and ifnull(sum(").append(SALE_NUM).append(") - sum(").append(AD_SALE_NUM).append("), 0) >= ?");
            argsList.add(param.getAdOtherSaleNumMin());
        }
        if (param.getAdOtherSaleNumMax() != null) {
            subWhereSql.append(" and ifnull(sum(").append(SALE_NUM).append(") - sum(").append(AD_SALE_NUM).append("), 0) <= ?");
            argsList.add(param.getAdOtherSaleNumMax());
        }

        /******************************高级搜索新增查询指标*****************************/
        //可见展示次数
        if (param.getViewImpressionsMin() != null) {
            subWhereSql.append(" and ifnull(sum(" + VIEW_IMPRESSIONS + "), 0) >= ? ");
            argsList.add(param.getViewImpressionsMin());
        }
        if (param.getViewImpressionsMax() != null) {
            subWhereSql.append(" and ifnull(sum(" + VIEW_IMPRESSIONS + "), 0) <= ? ");
            argsList.add(param.getViewImpressionsMax());
        }

        //VCPM= 每千次展现费用=广告花费/可见展现次数x1000
        if (param.getVcpmMin() != null) {
            subWhereSql.append(" and ifnull(ROUND(sum(cost)/sum(" + VIEW_IMPRESSIONS + ")*1000, 2), 0) >= ?");
            argsList.add(param.getVcpmMin());
        }
        if (param.getVcpmMax() != null) {
            subWhereSql.append(" and ifnull(ROUND(sum(cost)/sum(" + VIEW_IMPRESSIONS + ")*1000, 2), 0) <= ?");
            argsList.add(param.getVcpmMax());
        }


        //“品牌新买家”订单量（绝对值） orders_new_to_brand14d
        if (param.getOrdersNewToBrandFTDMin() != null) {
            subWhereSql.append(" and ifnull(sum(orders_new_to_brand14d), 0) >= ? ");
            argsList.add(param.getOrdersNewToBrandFTDMin());
        }
        if (param.getOrdersNewToBrandFTDMax() != null) {
            subWhereSql.append(" and ifnull(sum(orders_new_to_brand14d), 0) <= ? ");
            argsList.add(param.getOrdersNewToBrandFTDMax());
        }

        //“品牌新买家”订单百分比（百分比） orderRateNewToBrandFTDMin
        if (param.getOrderRateNewToBrandFTDMin() != null) {
            subWhereSql.append(" and ROUND(ifnull((sum(orders_new_to_brand14d)/ifnull(sum(").append(ORDER_NUM).append("), 0)), 0), 4) >= ? ");
            argsList.add(param.getOrderRateNewToBrandFTDMin());
        }
        if (param.getOrderRateNewToBrandFTDMax() != null) {
            subWhereSql.append(" and ROUND(ifnull((sum(orders_new_to_brand14d)/ifnull(sum(").append(ORDER_NUM).append("), 0)), 0), 4) <= ? ");
            argsList.add(param.getOrderRateNewToBrandFTDMax());
        }

        //“品牌新买家”销售额（绝对值） salesNewToBrandFTDMin
        if (param.getSalesNewToBrandFTDMin() != null) {
            subWhereSql.append(" and ifnull(sum(sales_new_to_brand14d), 0) >= ? ");
            argsList.add(param.getSalesNewToBrandFTDMin());
        }

        if (param.getSalesNewToBrandFTDMax() != null) {
            subWhereSql.append(" and ifnull(sum(sales_new_to_brand14d), 0) <= ? ");
            argsList.add(param.getSalesNewToBrandFTDMax());
        }


        //“品牌新买家”销售额百分比（百分比） salesRateNewToBrandFTDMin
        if (param.getSalesRateNewToBrandFTDMin() != null) {
            subWhereSql.append(" and ROUND(ifnull((sum(sales_new_to_brand14d)/ifnull(sum(").append(TOTAL_SALES).append("), 0)), 0), 4) >= ? ");
            argsList.add(param.getSalesRateNewToBrandFTDMin());
        }
        if (param.getSalesRateNewToBrandFTDMax() != null) {
            subWhereSql.append(" and ROUND(ifnull((sum(sales_new_to_brand14d)/ifnull(sum(").append(TOTAL_SALES).append("), 0)), 0), 4) <= ? ");
            argsList.add(param.getSalesRateNewToBrandFTDMax());
        }

        //“品牌新买家”销量（绝对值）unitsOrderedNewToBrandFTDMin   units_ordered_new_to_brand14d
        if (param.getUnitsOrderedNewToBrandFTDMin() != null) {
            subWhereSql.append(" and ifnull(sum(units_ordered_new_to_brand14d), 0) >= ? ");
            argsList.add(param.getUnitsOrderedNewToBrandFTDMin());
        }
        if (param.getUnitsOrderedNewToBrandFTDMax() != null) {
            subWhereSql.append(" and ifnull(sum(units_ordered_new_to_brand14d), 0) <= ? ");
            argsList.add(param.getUnitsOrderedNewToBrandFTDMax());
        }


        //“品牌新买家”销量百分比（百分比）unitsOrderedRateNewToBrandFTDMin
        if (param.getUnitsOrderedRateNewToBrandFTDMin() != null) {
            subWhereSql.append(" and ROUND(ifnull((sum(units_ordered_new_to_brand14d)/ifnull(sum(").append(SALE_NUM).append("), 0)), 0), 4) >= ? ");
            argsList.add(param.getUnitsOrderedRateNewToBrandFTDMin());
        }
        if (param.getUnitsOrderedRateNewToBrandFTDMax() != null) {
            subWhereSql.append(" and ROUND(ifnull((sum(units_ordered_new_to_brand14d)/ifnull(sum(").append(SALE_NUM).append("), 0)), 0), 4) <= ? ");
            argsList.add(param.getUnitsOrderedRateNewToBrandFTDMax());
        }

        // 加购次数 筛选
        if (param.getAddToCartMin() != null) {
            subWhereSql.append(" and ifnull(sum(add_to_cart) , 0) >= ? ");
            argsList.add(param.getAddToCartMin());
        }
        if (param.getAddToCartMax() != null ) {
            subWhereSql.append(" and ifnull(sum(add_to_cart) , 0) <= ? ");
            argsList.add(param.getAddToCartMax());
        }

        // 5秒观看次数 筛选
        if (param.getVideo5SecondViewsMin() != null ) {
            subWhereSql.append(" and ifnull(sum(video5second_views) , 0) >= ? ");
            argsList.add(param.getVideo5SecondViewsMin());
        }
        if (param.getVideo5SecondViewsMax() != null) {
            subWhereSql.append(" and ifnull(sum(video5second_views) , 0) <= ? ");
            argsList.add(param.getVideo5SecondViewsMax());
        }

        // 视频完整播放次数 筛选
        if (param.getVideoCompleteViewsMin() != null ) {
            subWhereSql.append(" and ifnull(sum(video_complete_views) , 0) >= ? ");
            argsList.add(param.getVideoCompleteViewsMin());
        }
        if (param.getVideoCompleteViewsMax() != null ) {
            subWhereSql.append(" and ifnull(sum(video_complete_views) , 0) <= ? ");
            argsList.add(param.getVideoCompleteViewsMax());
        }

        // 观看率 筛选
        if (param.getViewabilityRateMin() != null) {
            subWhereSql.append(" and ROUND(ifnull((sum(" + VIEW_IMPRESSIONS + ")/sum(impressions)) * 100, 0), 2) >= ? ");
            argsList.add(param.getViewabilityRateMin());
        }
        if (param.getViewabilityRateMax() != null) {
            subWhereSql.append(" and ROUND(ifnull((sum(" + VIEW_IMPRESSIONS + ")/sum(impressions)) * 100, 0), 2) <= ? ");
            argsList.add(param.getViewabilityRateMax());
        }

        // 观看点击率 筛选
        if (param.getViewClickThroughRateMin() != null) {
            subWhereSql.append(" and ROUND(ifnull((sum(clicks)/sum(" + VIEW_IMPRESSIONS + ")) * 100, 0), 2) >= ? ");
            argsList.add(param.getViewClickThroughRateMin());
        }
        if (param.getViewClickThroughRateMax() != null) {
            subWhereSql.append(" and ROUND(ifnull((sum(clicks)/sum(" + VIEW_IMPRESSIONS + ")) * 100, 0), 2) <= ? ");
            argsList.add(param.getViewClickThroughRateMax());
        }

        // 品牌搜索次数 筛选
        if (param.getBrandedSearchesMin() != null) {
            subWhereSql.append(" and ifnull(sum(branded_searches14d) , 0) >= ? ");
            argsList.add(param.getBrandedSearchesMin()); }
        if (param.getBrandedSearchesMax() != null) {
            subWhereSql.append(" and ifnull(sum(branded_searches14d) , 0) <= ? ");
            argsList.add(param.getBrandedSearchesMax()); }

        // 广告笔单价 筛选
        if (param.getAdvertisingUnitPriceMin() != null) {
            subWhereSql.append(" and ROUND(ifnull(sum(" + TOTAL_SALES + ")/sum(" + ORDER_NUM + "), 0), 2) >= ?");
            argsList.add(param.getAdvertisingUnitPriceMin());
        }
        if (param.getAdvertisingUnitPriceMax() != null) {
            subWhereSql.append(" and ROUND(ifnull(sum(" + TOTAL_SALES + ")/sum(" + ORDER_NUM + "), 0), 2) <= ?");
            argsList.add(param.getAdvertisingUnitPriceMax());
        }

        // 搜索结果首页首位IS
        if (param.getTopImpressionShareMin() != null){
            subWhereSql.append(" and max(top_of_search_is) >= ?");
            argsList.add(param.getTopImpressionShareMin());
        }
        if (param.getTopImpressionShareMax() != null){
            subWhereSql.append(" and min(top_of_search_is) <= ?");
            argsList.add(param.getTopImpressionShareMax());
        }
        return subWhereSql.toString();
    }

    public static String getCampaignHavingSql(ReportAdvancedFilterBaseQo param, List<Object> argsList) {
        StringBuilder subWhereSql = new StringBuilder();
        BigDecimal shopSales = param.getShopSales() != null ? param.getShopSales() : BigDecimal.valueOf(0);
        final String ORDER_NUM = "if (type = 'sp' ,`units_ordered7d`,if (type = 'sb' ,`units_sold14d`,`units_ordered14d`))";
        final String AD_ORDER_NUM = "if (type = 'sp',`units_ordered7d_same_sku`,`units_ordered14d_same_sku`)";
        final String TOTAL_SALES = "if (type = 'sp', sales7d,sales14d)";
        final String AD_SALES = "if (type = 'sp', sales7d_same_sku,sales14d_same_sku)";
        final String SALE_NUM = "if (type = 'sp', conversions7d,conversions14d)";
        final String AD_SALE_NUM = "if (type = 'sp' ,`conversions7d_same_sku`,`conversions14d_same_sku`)";
        //高级筛选
        subWhereSql.append(" having 1=1 ");
        //展示量
        if (param.getImpressionsMin() != null) {
            subWhereSql.append(" and ifnull(sum(impressions), 0) >= ?");
            argsList.add(param.getImpressionsMin());
        }
        if (param.getImpressionsMax() != null) {
            subWhereSql.append(" and ifnull(sum(impressions), 0) <= ?");
            argsList.add(param.getImpressionsMax());
        }
        //点击量
        if (param.getClicksMin() != null) {
            subWhereSql.append(" and ifnull(sum(clicks), 0) >= ?");
            argsList.add(param.getClicksMin());
        }
        if (param.getClicksMax() != null) {
            subWhereSql.append(" and ifnull(sum(clicks), 0) <= ?");
            argsList.add(param.getClicksMax());
        }
        //点击率（clicks/impressions）
        if (param.getClickRateMin() != null) {
            subWhereSql.append(" and ROUND(ifnull(sum(clicks)/sum(impressions),0),4) >= ?");
            argsList.add(param.getClickRateMin());
        }
        if (param.getClickRateMax() != null) {
            subWhereSql.append(" and ROUND(ifnull(sum(clicks)/sum(impressions),0),4) <= ?");
            argsList.add(param.getClickRateMax());
        }
        //花费
        if (param.getCostMin() != null) {
            subWhereSql.append(" and ifnull(sum(cost), 0) >= ?");
            argsList.add(param.getCostMin());
        }
        if (param.getCostMax() != null) {
            subWhereSql.append(" and ifnull(sum(cost), 0) <= ?");
            argsList.add(param.getCostMax());
        }
        //cpc  平均点击费用
        if (param.getCpcMin() != null) {
            subWhereSql.append(" and ROUND(ifnull(sum(cost)/sum(clicks),0),2) >= ?");
            argsList.add(param.getCpcMin());
        }
        if (param.getCpcMax() != null) {
            subWhereSql.append(" and ROUND(ifnull(sum(cost)/sum(clicks),0),2) <= ?");
            argsList.add(param.getCpcMax());
        }
        //广告订单量
        if (param.getOrderNumMin() != null) {
            subWhereSql.append(" and ifnull(sum(").append(ORDER_NUM).append("), 0) >= ?");
            argsList.add(param.getOrderNumMin());
        }
        if (param.getOrderNumMax() != null) {
            subWhereSql.append(" and ifnull(sum(").append(ORDER_NUM).append("), 0) <= ?");
            argsList.add(param.getOrderNumMax());
        }
        //广告销售额
        if (param.getSalesMin() != null) {
            subWhereSql.append(" and ifnull(sum(").append(TOTAL_SALES).append("), 0) >= ?");
            argsList.add(param.getSalesMin());
        }
        if (param.getSalesMax() != null) {
            subWhereSql.append(" and ifnull(sum(").append(TOTAL_SALES).append("), 0) <= ?");
            argsList.add(param.getSalesMax());
        }
        //订单转化率
        if (param.getSalesConversionRateMin() != null) {
            subWhereSql.append(" and ROUND(ifnull(sum(").append(SALE_NUM).append(")/sum(clicks),0),4) >= ?");
            argsList.add(param.getSalesConversionRateMin());
        }
        if (param.getSalesConversionRateMax() != null) {
            subWhereSql.append(" and ROUND(ifnull(sum(").append(SALE_NUM).append(")/sum(clicks),0),4) <= ?");
            argsList.add(param.getSalesConversionRateMax());
        }
        //acos
        if (param.getAcosMin() != null) {
            subWhereSql.append(" and ROUND(ifnull(sum(cost)/sum(").append(TOTAL_SALES).append("),0),4) >= ?");
            argsList.add(param.getAcosMin());
        }
        if (param.getAcosMax() != null) {
            subWhereSql.append(" and ROUND(ifnull(sum(cost)/sum(").append(TOTAL_SALES).append("),0),4) <= ?");
            argsList.add(param.getAcosMax());
        }
        // roas
        if (param.getRoasMin() != null) {
            subWhereSql.append(" and ROUND(ifnull(sum(").append(TOTAL_SALES).append(")/sum(cost),0),2) >= ?");
            argsList.add(param.getRoasMin());
        }
        // roas
        if (param.getRoasMax() != null) {
            subWhereSql.append(" and ROUND(ifnull(sum(").append(TOTAL_SALES).append(")/sum(cost),0),2) <= ?");
            argsList.add(param.getRoasMax());
        }
        // acots  需要乘以店铺销售额
        if (param.getAcotsMin() != null) {
            if (shopSales.doubleValue() > 0) {
                subWhereSql.append(" and ROUND((ifnull(sum(cost),0) / ").append(shopSales).append("),4) >= ? ");
                argsList.add(param.getAcotsMin());
            } else {
                subWhereSql.append(" and 0 >= ? ");
                argsList.add(param.getAcotsMin());
            }
        }
        // acots  需要乘以店铺销售额
        if (param.getAcotsMax() != null) {
            if (shopSales.doubleValue() > 0) {
                subWhereSql.append(" and ROUND((ifnull(sum(cost),0) / ").append(shopSales).append("),4) <= ? ");
                argsList.add(param.getAcotsMax());
            } else {
                subWhereSql.append(" and 0 <= ? ");
                argsList.add(param.getAcotsMax());
            }
        }
        // asots 需要乘以店铺销售额
        if (param.getAsotsMin() != null) {
            if (shopSales.doubleValue() > 0) {
                subWhereSql.append(" and ROUND((ifnull(sum(").append(TOTAL_SALES).append("),0) / ").append(shopSales).append("),4) >= ? ");
                argsList.add(param.getAsotsMin());
            } else {
                subWhereSql.append(" and 0 >= ? ");
                argsList.add(param.getAsotsMin());
            }
        }
        // asots  需要乘以店铺销售额
        if (param.getAsotsMax() != null) {
            if (shopSales.doubleValue() > 0) {
                subWhereSql.append(" and ROUND((ifnull(sum(").append(TOTAL_SALES).append("),0) / ").append(shopSales).append("),4) <= ? ");
                argsList.add(param.getAsotsMax());
            } else {
                subWhereSql.append(" and 0 <= ? ");
                argsList.add(param.getAsotsMax());
            }
        }
        //CPA
        if (param.getCpaMin() != null) {
            subWhereSql.append(" and ROUND(ifnull(sum(cost)/sum(").append(ORDER_NUM).append("),0), 2) >= ?");
            argsList.add(param.getCpaMin());
        }
        if (param.getCpaMax() != null) {
            subWhereSql.append(" and ROUND(ifnull(sum(cost)/sum(").append(ORDER_NUM).append("),0), 2) <= ?");
            argsList.add(param.getCpaMax());
        }

        //本广告产品订单量
        if (param.getAdSaleNumMin() != null) {
            subWhereSql.append(" and ifnull(sum(").append(AD_ORDER_NUM).append("), 0) >= ?");
            argsList.add(param.getAdSaleNumMin());
        }
        if (param.getAdSaleNumMax() != null) {
            subWhereSql.append(" and ifnull(sum(").append(AD_ORDER_NUM).append("), 0) <= ?");
            argsList.add(param.getAdSaleNumMax());
        }
        //其他产品广告订单量
        if (param.getAdOtherOrderNumMin() != null) {
            subWhereSql.append(" and ifnull(sum(").append(ORDER_NUM).append(") - sum(").append(AD_ORDER_NUM).append("), 0) >= ?");
            argsList.add(param.getAdOtherOrderNumMin());
        }
        if (param.getAdOtherOrderNumMax() != null) {
            subWhereSql.append(" and ifnull(sum(").append(ORDER_NUM).append(") - sum(").append(AD_ORDER_NUM).append("), 0) <= ?");
            argsList.add(param.getAdOtherOrderNumMax());
        }
        //本广告产品销售额
        if (param.getAdSalesMin() != null) {
            subWhereSql.append(" and ifnull(sum(").append(AD_SALES).append("), 0) >= ?");
            argsList.add(param.getAdSalesMin());
        }
        if (param.getAdSalesMax() != null) {
            subWhereSql.append(" and ifnull(sum(").append(AD_SALES).append("), 0) <= ?");
            argsList.add(param.getAdSalesMax());
        }
        //其他产品广告销售额
        if (param.getAdOtherSalesMin() != null) {
            subWhereSql.append(" and ifnull(sum(").append(TOTAL_SALES).append(") - sum(").append(AD_SALES).append("), 0) >= ?");
            argsList.add(param.getAdOtherSalesMin());
        }
        if (param.getAdOtherSalesMax() != null) {
            subWhereSql.append(" and ifnull(sum(").append(TOTAL_SALES).append(") - sum(").append(AD_SALES).append("), 0) <= ?");
            argsList.add(param.getAdOtherSalesMax());
        }
        //广告销量
        if (param.getAdSalesTotalMin() != null) {
            subWhereSql.append(" and ifnull(sum(").append(SALE_NUM).append("), 0) >= ?");
            argsList.add(param.getAdSalesTotalMin());
        }
        if (param.getAdSalesTotalMax() != null) {
            subWhereSql.append(" and ifnull(sum(").append(SALE_NUM).append("), 0) <= ?");
            argsList.add(param.getAdSalesTotalMax());
        }
        //本广告产品销量
        if (param.getAdSelfSaleNumMin() != null) {
            subWhereSql.append(" and ifnull(sum(").append(AD_SALE_NUM).append("), 0) >= ?");
            argsList.add(param.getAdSelfSaleNumMin());
        }
        if (param.getAdSelfSaleNumMax() != null) {
            subWhereSql.append(" and ifnull(sum(").append(AD_SALE_NUM).append("), 0) <= ?");
            argsList.add(param.getAdSelfSaleNumMax());
        }
        //其他产品广告销量
        if (param.getAdOtherSaleNumMin() != null) {
            subWhereSql.append(" and ifnull(sum(").append(SALE_NUM).append(") - sum(").append(AD_SALE_NUM).append("), 0) >= ?");
            argsList.add(param.getAdOtherSaleNumMin());
        }
        if (param.getAdOtherSaleNumMax() != null) {
            subWhereSql.append(" and ifnull(sum(").append(SALE_NUM).append(") - sum(").append(AD_SALE_NUM).append("), 0) <= ?");
            argsList.add(param.getAdOtherSaleNumMax());
        }

        /******************************高级搜索新增查询指标*****************************/
        //可见展示次数
        if(param.getViewImpressionsMin() != null){
            subWhereSql.append(" and ifnull(sum(view_impressions), 0) >= ? ");
            argsList.add(param.getViewImpressionsMin());
        }
        if(param.getViewImpressionsMax() != null){
            subWhereSql.append(" and ifnull(sum(view_impressions), 0) <= ? ");
            argsList.add(param.getViewImpressionsMax());
        }

        //VCPM= 每千次展现费用=广告花费/可见展现次数x1000
        if(param.getVcpmMin() != null){
            subWhereSql.append(" and ifnull(ROUND(sum(cost)/sum(view_impressions)*1000, 2), 0) >= ?");
            argsList.add(param.getVcpmMin());
        }
        if (param.getVcpmMax() != null) {
            subWhereSql.append(" and ifnull(ROUND(sum(cost)/sum(view_impressions)*1000, 2), 0) <= ?");
            argsList.add(param.getVcpmMax());
        }


        //“品牌新买家”订单量（绝对值） orders_new_to_brand14d
        if (param.getOrdersNewToBrandFTDMin() != null) {
            subWhereSql.append(" and ifnull(sum(orders_new_to_brand14d), 0) >= ? ");
            argsList.add(param.getOrdersNewToBrandFTDMin());
        }
        if (param.getOrdersNewToBrandFTDMax() != null) {
            subWhereSql.append(" and ifnull(sum(orders_new_to_brand14d), 0) <= ? ");
            argsList.add(param.getOrdersNewToBrandFTDMax());
        }

        //“品牌新买家”订单百分比（百分比） orderRateNewToBrandFTDMin
        if (param.getOrderRateNewToBrandFTDMin() != null) {
            subWhereSql.append(" and ROUND(ifnull((sum(orders_new_to_brand14d)/ifnull(sum(if (type = 'sp', conversions7d,conversions14d)), 0)), 0), 4) >= ? ");
            argsList.add(param.getOrderRateNewToBrandFTDMin());
        }
        if (param.getOrderRateNewToBrandFTDMax() != null) {
            subWhereSql.append(" and ROUND(ifnull((sum(orders_new_to_brand14d)/ifnull(sum(if (type = 'sp', conversions7d,conversions14d)), 0)), 0), 4) <= ? ");
            argsList.add(param.getOrderRateNewToBrandFTDMax());
        }

        //“品牌新买家”销售额（绝对值） salesNewToBrandFTDMin
        if (param.getSalesNewToBrandFTDMin() != null) {
            subWhereSql.append(" and ifnull(sum(sales_new_to_brand14d), 0) >= ? ");
            argsList.add(param.getSalesNewToBrandFTDMin());
        }

        if (param.getSalesNewToBrandFTDMax() != null) {
            subWhereSql.append(" and ifnull(sum(sales_new_to_brand14d), 0) <= ? ");
            argsList.add(param.getSalesNewToBrandFTDMax());
        }


        //“品牌新买家”销售额百分比（百分比） salesRateNewToBrandFTDMin
        if (param.getSalesRateNewToBrandFTDMin() != null) {
            subWhereSql.append(" and ROUND(ifnull((sum(sales_new_to_brand14d)/ifnull(sum(if (type = 'sp', sales7d,sales14d)), 0)), 0), 4) >= ? ");
            argsList.add(param.getSalesRateNewToBrandFTDMin());
        }
        if (param.getSalesRateNewToBrandFTDMax() != null) {
            subWhereSql.append(" and ROUND(ifnull((sum(sales_new_to_brand14d)/ifnull(sum(if (type = 'sp', sales7d,sales14d)), 0)), 0), 4) <= ? ");
            argsList.add(param.getSalesRateNewToBrandFTDMax());
        }

        //“品牌新买家”销量（绝对值）unitsOrderedNewToBrandFTDMin   units_ordered_new_to_brand14d
        if (param.getUnitsOrderedNewToBrandFTDMin() != null) {
            subWhereSql.append(" and ifnull(sum(units_ordered_new_to_brand14d), 0) >= ? ");
            argsList.add(param.getUnitsOrderedNewToBrandFTDMin());
        }
        if (param.getUnitsOrderedNewToBrandFTDMax() != null) {
            subWhereSql.append(" and ifnull(sum(units_ordered_new_to_brand14d), 0) <= ? ");
            argsList.add(param.getUnitsOrderedNewToBrandFTDMax());
        }


        //“品牌新买家”销量百分比（百分比）unitsOrderedRateNewToBrandFTDMin
        if (param.getUnitsOrderedRateNewToBrandFTDMin() != null) {
            subWhereSql.append(" and ROUND(ifnull((sum(units_ordered_new_to_brand14d)/ifnull(sum(if (type = 'sp' ,`units_ordered7d`,if (type = 'sb' ,`units_sold14d`,`units_ordered14d`))), 0)), 0), 4) >= ? ");
            argsList.add(param.getUnitsOrderedRateNewToBrandFTDMin());
        }
        if (param.getUnitsOrderedRateNewToBrandFTDMax() != null) {
            subWhereSql.append(" and ROUND(ifnull((sum(units_ordered_new_to_brand14d)/ifnull(sum(if (type = 'sp' ,`units_ordered7d`,if (type = 'sb' ,`units_sold14d`,`units_ordered14d`))), 0)), 0), 4) <= ? ");
            argsList.add(param.getUnitsOrderedRateNewToBrandFTDMax());
        }

        //todo 明星
        return subWhereSql.toString();
    }

    public static Set<String> getDorisSpTargetPageHavingFieldSet(ReportAdvancedFilterBaseQo qo) {
        Set<String> fieldSet = new HashSet<>();
        BigDecimal shopSales = qo.getShopSales() != null ? qo.getShopSales() : BigDecimal.valueOf(0);
        //展示量
        if (qo.getImpressionsMin() != null) {
            fieldSet.add(TargetReportSumEnum.impressions.getField());
        }
        if (qo.getImpressionsMax() != null) {
            fieldSet.add(TargetReportSumEnum.impressions.getField());
        }
        //点击量
        if (qo.getClicksMin() != null) {
            fieldSet.add(TargetReportSumEnum.clicks.getField());
        }
        if (qo.getClicksMax() != null) {
            fieldSet.add(TargetReportSumEnum.clicks.getField());
        }
        //点击率（clicks/impressions）
        if (qo.getClickRateMin() != null) {
            fieldSet.add(TargetReportSumEnum.clicks.getField());
            fieldSet.add(TargetReportSumEnum.impressions.getField());
        }
        if (qo.getClickRateMax() != null) {
            fieldSet.add(TargetReportSumEnum.clicks.getField());
            fieldSet.add(TargetReportSumEnum.impressions.getField());
        }
        //花费
        if (qo.getCostMin() != null) {
            fieldSet.add(TargetReportSumEnum.cost.getField());
        }
        if (qo.getCostMax() != null) {
            fieldSet.add(TargetReportSumEnum.cost.getField());
        }
        //cpc  平均点击费用
        if (qo.getCpcMin() != null) {
            fieldSet.add(TargetReportSumEnum.cost.getField());
            fieldSet.add(TargetReportSumEnum.clicks.getField());
        }
        if (qo.getCpcMax() != null) {
            fieldSet.add(TargetReportSumEnum.cost.getField());
            fieldSet.add(TargetReportSumEnum.clicks.getField());
        }
        //广告订单量
        if (qo.getOrderNumMin() != null) {
            fieldSet.add(TargetReportSumEnum.sale_num.getField());
        }
        if (qo.getOrderNumMax() != null) {
            fieldSet.add(TargetReportSumEnum.sale_num.getField());
        }
        //广告销售额
        if (qo.getSalesMin() != null) {
            fieldSet.add(TargetReportSumEnum.total_sales.getField());
        }
        if (qo.getSalesMax() != null) {
            fieldSet.add(TargetReportSumEnum.total_sales.getField());
        }
        //订单转化率
        if (qo.getSalesConversionRateMin() != null) {
            fieldSet.add(TargetReportSumEnum.sale_num.getField());
            fieldSet.add(TargetReportSumEnum.clicks.getField());
        }
        if (qo.getSalesConversionRateMax() != null) {
            fieldSet.add(TargetReportSumEnum.sale_num.getField());
            fieldSet.add(TargetReportSumEnum.clicks.getField());
        }
        //acos
        if (qo.getAcosMin() != null) {
            fieldSet.add(TargetReportSumEnum.cost.getField());
            fieldSet.add(TargetReportSumEnum.total_sales.getField());
        }
        if (qo.getAcosMax() != null) {
            fieldSet.add(TargetReportSumEnum.cost.getField());
            fieldSet.add(TargetReportSumEnum.total_sales.getField());
        }
        // roas
        if (qo.getRoasMin() != null) {
            fieldSet.add(TargetReportSumEnum.total_sales.getField());
            fieldSet.add(TargetReportSumEnum.cost.getField());
        }
        // roas
        if (qo.getRoasMax() != null) {
            fieldSet.add(TargetReportSumEnum.total_sales.getField());
            fieldSet.add(TargetReportSumEnum.cost.getField());
        }
        // acots  需要乘以店铺销售额
        if (qo.getAcotsMin() != null) {
            if (shopSales.doubleValue() > 0) {
                fieldSet.add(TargetReportSumEnum.cost.getField());
            }
        }
        // acots  需要乘以店铺销售额
        if (qo.getAcotsMax() != null) {
            if (shopSales.doubleValue() > 0) {
                fieldSet.add(TargetReportSumEnum.cost.getField());
            }
        }
        // asots 需要乘以店铺销售额
        if (qo.getAsotsMin() != null) {
            if (shopSales.doubleValue() > 0) {
                fieldSet.add(TargetReportSumEnum.total_sales.getField());
            }
        }
        // asots  需要乘以店铺销售额
        if (qo.getAsotsMax() != null) {
            if (shopSales.doubleValue() > 0) {
                fieldSet.add(TargetReportSumEnum.total_sales.getField());
            }
        }
        //CPA
        if (qo.getCpaMin() != null) {
            fieldSet.add(TargetReportSumEnum.cost.getField());
            fieldSet.add(TargetReportSumEnum.sale_num.getField());
        }
        if (qo.getCpaMax() != null) {
            fieldSet.add(TargetReportSumEnum.cost.getField());
            fieldSet.add(TargetReportSumEnum.sale_num.getField());
        }
        //本广告产品订单量
        if (qo.getAdSaleNumMin() != null) {
            fieldSet.add(TargetReportSumEnum.ad_sale_num.getField());
        }
        if (qo.getAdSaleNumMax() != null) {
            fieldSet.add(TargetReportSumEnum.ad_sale_num.getField());
        }
        //其他产品广告订单量
        if (qo.getAdOtherOrderNumMin() != null) {
            fieldSet.add(TargetReportSumEnum.sale_num.getField());
            fieldSet.add(TargetReportSumEnum.ad_sale_num.getField());
        }
        if (qo.getAdOtherOrderNumMax() != null) {
            fieldSet.add(TargetReportSumEnum.sale_num.getField());
            fieldSet.add(TargetReportSumEnum.ad_sale_num.getField());
        }
        //本广告产品销售额
        if (qo.getAdSalesMin() != null) {
            fieldSet.add(TargetReportSumEnum.ad_sales.getField());
        }
        if (qo.getAdSalesMax() != null) {
            fieldSet.add(TargetReportSumEnum.ad_sales.getField());
        }
        //其他产品广告销售额
        if (qo.getAdOtherSalesMin() != null) {
            fieldSet.add(TargetReportSumEnum.total_sales.getField());
            fieldSet.add(TargetReportSumEnum.ad_sales.getField());
        }
        if (qo.getAdOtherSalesMax() != null) {
            fieldSet.add(TargetReportSumEnum.total_sales.getField());
            fieldSet.add(TargetReportSumEnum.ad_sales.getField());
        }
        //广告销量
        if (qo.getAdSalesTotalMin() != null) {
            fieldSet.add(TargetReportSumEnum.order_num.getField());
        }
        if (qo.getAdSalesTotalMax() != null) {
            fieldSet.add(TargetReportSumEnum.order_num.getField());
        }
        //本广告产品销量
        if (qo.getAdSelfSaleNumMin() != null) {
            fieldSet.add(TargetReportSumEnum.ad_order_num.getField());
        }
        if (qo.getAdSelfSaleNumMax() != null) {
            fieldSet.add(TargetReportSumEnum.ad_order_num.getField());
        }
        //其他产品广告销量
        if (qo.getAdOtherSaleNumMin() != null) {
            fieldSet.add(TargetReportSumEnum.order_num.getField());
            fieldSet.add(TargetReportSumEnum.ad_order_num.getField());
        }
        if (qo.getAdOtherSaleNumMax() != null) {
            fieldSet.add(TargetReportSumEnum.order_num.getField());
            fieldSet.add(TargetReportSumEnum.ad_order_num.getField());
        }
        // 广告笔单价 筛选
        if (qo.getAdvertisingUnitPriceMin() != null) {
            fieldSet.add(TargetReportSumEnum.total_sales.getField());
            fieldSet.add(TargetReportSumEnum.sale_num.getField());
        }
        if (qo.getAdvertisingUnitPriceMax() != null) {
            fieldSet.add(TargetReportSumEnum.total_sales.getField());
            fieldSet.add(TargetReportSumEnum.sale_num.getField());
        }
        // 搜索结果首页首位IS
        if (qo.getTopImpressionShareMin() != null){
            fieldSet.add(TargetReportSumEnum.max_top_of_search_is.getField());
        }
        if (qo.getTopImpressionShareMax() != null){
            fieldSet.add(TargetReportSumEnum.min_top_of_search_is.getField());
        }
        return fieldSet;
    }

    public static Set<String> getDorisSpTargetPageOrderFieldSet(String orderField) {
        Set<String> fieldSet = new HashSet<>();
        if (StringUtils.isBlank(orderField)) {
            return new HashSet<>();
        }
        switch (orderField) {
            case "adCost":
            case "adCostPercentage":
                fieldSet.add(TargetReportSumEnum.cost.getField());
                break;
            case "impressions":
                fieldSet.add(TargetReportSumEnum.impressions.getField());
                break;
            case "clicks":
                fieldSet.add(TargetReportSumEnum.clicks.getField());
                break;
            //广告订单量
            case "adOrderNum":
            case "adOrderNumPercentage":
                fieldSet.add(TargetReportSumEnum.sale_num.getField());
                break;
            //本广告产品订单量
            case "adSaleNum":
                fieldSet.add(TargetReportSumEnum.ad_sale_num.getField());
                break;
            //广告销售额
            case "adSale":
            case "adSalePercentage":
                fieldSet.add(TargetReportSumEnum.total_sales.getField());
                break;
            //本广告产品销售额
            case "adSales":
                fieldSet.add(TargetReportSumEnum.ad_sales.getField());
                break;
            //广告销量
            case "orderNum":
            case "orderNumPercentage":
                fieldSet.add(TargetReportSumEnum.order_num.getField());
                break;
            //本广告产品销量
            case "adSelfSaleNum":
                fieldSet.add(TargetReportSumEnum.ad_order_num.getField());
                break;
            //广告点击率ctr
            case "ctr":
                fieldSet.add(TargetReportSumEnum.clicks.getField());
                fieldSet.add(TargetReportSumEnum.impressions.getField());
                break;
            //广告转化率cvr
            case "cvr":
                fieldSet.add(TargetReportSumEnum.sale_num.getField());
                fieldSet.add(TargetReportSumEnum.clicks.getField());
                break;
            case "cpa":
                fieldSet.add(TargetReportSumEnum.cost.getField());
                fieldSet.add(TargetReportSumEnum.sale_num.getField());
                break;
            case "adCostPerClick":
                fieldSet.add(TargetReportSumEnum.cost.getField());
                fieldSet.add(TargetReportSumEnum.clicks.getField());
                break;
            case "acos":
                fieldSet.add(TargetReportSumEnum.cost.getField());
                fieldSet.add(TargetReportSumEnum.total_sales.getField());
                break;
            case "roas":
                fieldSet.add(TargetReportSumEnum.total_sales.getField());
                fieldSet.add(TargetReportSumEnum.cost.getField());
                break;
            case "acots":
                fieldSet.add(TargetReportSumEnum.cost.getField());
                break;
            case "asots":
                fieldSet.add(TargetReportSumEnum.total_sales.getField());
                break;
            //其他产品广告订单量
            case "adOtherOrderNum":
                fieldSet.add(TargetReportSumEnum.sale_num.getField());
                fieldSet.add(TargetReportSumEnum.ad_sale_num.getField());
                break;
            //其他产品广告销售额
            case "adOtherSales":
                fieldSet.add(TargetReportSumEnum.total_sales.getField());
                fieldSet.add(TargetReportSumEnum.ad_sales.getField());
                break;
            //其他产品广告销量
            case "adOtherSaleNum":
                fieldSet.add(TargetReportSumEnum.order_num.getField());
                fieldSet.add(TargetReportSumEnum.ad_order_num.getField());
                break;
            //搜索结果首页首位IS
            case "topImpressionShare":
                fieldSet.add(TargetReportSumEnum.max_top_of_search_is.getField());
                break;
            case "advertisingUnitPrice":
                fieldSet.add(TargetReportSumEnum.total_sales.getField());
                fieldSet.add(TargetReportSumEnum.sale_num.getField());
                break;
        }
        return fieldSet;
    }


    public static String getDorisSpTargetPageHavingSql(ReportAdvancedFilterBaseQo qo, List<Object> argsList) {
        StringBuilder sb = new StringBuilder();
        BigDecimal shopSales = qo.getShopSales() != null ? qo.getShopSales() : BigDecimal.valueOf(0);
        sb.append(" having 1=1 ");
        //竞价
        if (qo.getBidMin() != null) {
            sb.append(" and IFNULL(ANY(bid), ANY(default_bid)) >= ? ");
            argsList.add(qo.getBidMin());
        }
        if (qo.getBidMax() != null) {
            sb.append(" and IFNULL(ANY(bid), ANY(default_bid)) <= ? ");
            argsList.add(qo.getBidMax());
        }
        //展示量
        if (qo.getImpressionsMin() != null) {
            sb.append(" and ifnull(sum(impressions), 0) >= ?");
            argsList.add(qo.getImpressionsMin());
        }
        if (qo.getImpressionsMax() != null) {
            sb.append(" and ifnull(sum(impressions), 0) <= ?");
            argsList.add(qo.getImpressionsMax());
        }
        //点击量
        if (qo.getClicksMin() != null) {
            sb.append(" and ifnull(sum(clicks), 0) >= ?");
            argsList.add(qo.getClicksMin());
        }
        if (qo.getClicksMax() != null) {
            sb.append(" and ifnull(sum(clicks), 0) <= ?");
            argsList.add(qo.getClicksMax());
        }
        //点击率（clicks/impressions）
        if (qo.getClickRateMin() != null) {
            sb.append(" and ROUND(ifnull(sum(clicks)/sum(impressions)*100,0),2) >= ?");
            argsList.add(qo.getClickRateMin());
        }
        if (qo.getClickRateMax() != null) {
            sb.append(" and ROUND(ifnull(sum(clicks)/sum(impressions)*100,0),2) <= ?");
            argsList.add(qo.getClickRateMax());
        }
        //花费
        if (qo.getCostMin() != null) {
            sb.append(" and ifnull(sum(cost), 0) >= ?");
            argsList.add(qo.getCostMin());
        }
        if (qo.getCostMax() != null) {
            sb.append(" and ifnull(sum(cost), 0) <= ?");
            argsList.add(qo.getCostMax());
        }
        //cpc  平均点击费用
        if (qo.getCpcMin() != null) {
            sb.append(" and ROUND(ifnull(sum(cost)/sum(clicks),0),2) >= ?");
            argsList.add(qo.getCpcMin());
        }
        if (qo.getCpcMax() != null) {
            sb.append(" and ROUND(ifnull(sum(cost)/sum(clicks),0),2) <= ?");
            argsList.add(qo.getCpcMax());
        }
        //广告订单量
        if (qo.getOrderNumMin() != null) {
            sb.append(" and ifnull(sum(sale_num), 0) >= ?");
            argsList.add(qo.getOrderNumMin());
        }
        if (qo.getOrderNumMax() != null) {
            sb.append(" and ifnull(sum(sale_num), 0) <= ?");
            argsList.add(qo.getOrderNumMax());
        }
        //广告销售额
        if (qo.getSalesMin() != null) {
            sb.append(" and ifnull(sum(total_sales), 0) >= ?");
            argsList.add(qo.getSalesMin());
        }
        if (qo.getSalesMax() != null) {
            sb.append(" and ifnull(sum(total_sales), 0) <= ?");
            argsList.add(qo.getSalesMax());
        }
        //订单转化率
        if (qo.getSalesConversionRateMin() != null) {
            sb.append(" and ROUND(ifnull(sum(sale_num)/sum(clicks)*100,0),2) >= ?");
            argsList.add(qo.getSalesConversionRateMin());
        }
        if (qo.getSalesConversionRateMax() != null) {
            sb.append(" and ROUND(ifnull(sum(sale_num)/sum(clicks)*100,0),2) <= ?");
            argsList.add(qo.getSalesConversionRateMax());
        }
        //acos
        if (qo.getAcosMin() != null) {
            sb.append(" and ROUND(ifnull(sum(cost)/sum(total_sales)*100,0),2) >= ?");
            argsList.add(qo.getAcosMin());
        }
        if (qo.getAcosMax() != null) {
            sb.append(" and ROUND(ifnull(sum(cost)/sum(total_sales)*100,0),2) <= ?");
            argsList.add(qo.getAcosMax());
        }
        // roas
        if (qo.getRoasMin() != null) {
            sb.append(" and ROUND(ifnull(sum(total_sales)/sum(cost),0),2) >= ?");
            argsList.add(qo.getRoasMin());
        }
        // roas
        if (qo.getRoasMax() != null) {
            sb.append(" and ROUND(ifnull(sum(total_sales)/sum(cost),0),2) <= ?");
            argsList.add(qo.getRoasMax());
        }
        // acots  需要乘以店铺销售额
        if (qo.getAcotsMin() != null) {
            if (shopSales.doubleValue() > 0) {
                sb.append(" and ROUND((ifnull(sum(cost),0) / ").append(shopSales).append("*100 ),2) >= ? ");
                argsList.add(qo.getAcotsMin());
            } else {
                sb.append(" and 0 >= ? ");
                argsList.add(qo.getAcotsMin());
            }
        }
        // acots  需要乘以店铺销售额
        if (qo.getAcotsMax() != null) {
            if (shopSales.doubleValue() > 0) {
                sb.append(" and ROUND((ifnull(sum(cost),0) / ").append(shopSales).append("*100 ),2) <= ? ");
                argsList.add(qo.getAcotsMax());
            } else {
                sb.append(" and 0 <= ? ");
                argsList.add(qo.getAcotsMax());
            }
        }
        // asots 需要乘以店铺销售额
        if (qo.getAsotsMin() != null) {
            if (shopSales.doubleValue() > 0) {
                sb.append(" and ROUND((ifnull(sum(total_sales),0) / ").append(shopSales).append("*100 ),2) >= ? ");
                argsList.add(qo.getAsotsMin());
            } else {
                sb.append(" and 0 >= ? ");
                argsList.add(qo.getAsotsMin());
            }
        }
        // asots  需要乘以店铺销售额
        if (qo.getAsotsMax() != null) {
            if (shopSales.doubleValue() > 0) {
                sb.append(" and ROUND((ifnull(sum(total_sales),0) / ").append(shopSales).append("*100 ),2) <= ? ");
                argsList.add(qo.getAsotsMax());
            } else {
                sb.append(" and 0 <= ? ");
                argsList.add(qo.getAsotsMax());
            }
        }
        //CPA
        if (qo.getCpaMin() != null) {
            sb.append(" and ROUND(ifnull(sum(cost)/sum(sale_num),0), 2) >= ?");
            argsList.add(qo.getCpaMin());
        }
        if (qo.getCpaMax() != null) {
            sb.append(" and ROUND(ifnull(sum(cost)/sum(sale_num),0), 2) <= ?");
            argsList.add(qo.getCpaMax());
        }

        //本广告产品订单量
        if (qo.getAdSaleNumMin() != null) {
            sb.append(" and ifnull(sum(ad_sale_num), 0) >= ?");
            argsList.add(qo.getAdSaleNumMin());
        }
        if (qo.getAdSaleNumMax() != null) {
            sb.append(" and ifnull(sum(ad_sale_num), 0) <= ?");
            argsList.add(qo.getAdSaleNumMax());
        }
        //其他产品广告订单量
        if (qo.getAdOtherOrderNumMin() != null) {
            sb.append(" and ifnull(sum(sale_num) - sum(ad_sale_num), 0) >= ?");
            argsList.add(qo.getAdOtherOrderNumMin());
        }
        if (qo.getAdOtherOrderNumMax() != null) {
            sb.append(" and ifnull(sum(sale_num) - sum(ad_sale_num), 0) <= ?");
            argsList.add(qo.getAdOtherOrderNumMax());
        }
        //本广告产品销售额
        if (qo.getAdSalesMin() != null) {
            sb.append(" and ifnull(sum(ad_sales), 0) >= ?");
            argsList.add(qo.getAdSalesMin());
        }
        if (qo.getAdSalesMax() != null) {
            sb.append(" and ifnull(sum(ad_sales), 0) <= ?");
            argsList.add(qo.getAdSalesMax());
        }
        //其他产品广告销售额
        if (qo.getAdOtherSalesMin() != null) {
            sb.append(" and ifnull(sum(total_sales) - sum(ad_sales), 0) >= ?");
            argsList.add(qo.getAdOtherSalesMin());
        }
        if (qo.getAdOtherSalesMax() != null) {
            sb.append(" and ifnull(sum(total_sales) - sum(ad_sales), 0) <= ?");
            argsList.add(qo.getAdOtherSalesMax());
        }
        //广告销量
        if (qo.getAdSalesTotalMin() != null) {
            sb.append(" and ifnull(sum(order_num), 0) >= ?");
            argsList.add(qo.getAdSalesTotalMin());
        }
        if (qo.getAdSalesTotalMax() != null) {
            sb.append(" and ifnull(sum(order_num), 0) <= ?");
            argsList.add(qo.getAdSalesTotalMax());
        }
        //本广告产品销量
        if (qo.getAdSelfSaleNumMin() != null) {
            sb.append(" and ifnull(sum(ad_order_num), 0) >= ?");
            argsList.add(qo.getAdSelfSaleNumMin());
        }
        if (qo.getAdSelfSaleNumMax() != null) {
            sb.append(" and ifnull(sum(ad_order_num), 0) <= ?");
            argsList.add(qo.getAdSelfSaleNumMax());
        }
        //其他产品广告销量
        if (qo.getAdOtherSaleNumMin() != null) {
            sb.append(" and ifnull(sum(order_num) - sum(ad_order_num), 0) >= ?");
            argsList.add(qo.getAdOtherSaleNumMin());
        }
        if (qo.getAdOtherSaleNumMax() != null) {
            sb.append(" and ifnull(sum(order_num) - sum(ad_order_num), 0) <= ?");
            argsList.add(qo.getAdOtherSaleNumMax());
        }

        // 广告笔单价 筛选
        if (qo.getAdvertisingUnitPriceMin() != null) {
            sb.append(" and ROUND(ifnull(sum(total_sales)/sum(sale_num), 0), 2) >= ?");
            argsList.add(qo.getAdvertisingUnitPriceMin());
        }
        if (qo.getAdvertisingUnitPriceMax() != null) {
            sb.append(" and ROUND(ifnull(sum(total_sales)/sum(sale_num), 0), 2) <= ?");
            argsList.add(qo.getAdvertisingUnitPriceMax());
        }

        // 搜索结果首页首位IS
        if (qo.getTopImpressionShareMin() != null){
            sb.append(" and max(max_top_of_search_is) >= ?");
            argsList.add(qo.getTopImpressionShareMin());
        }
        if (qo.getTopImpressionShareMax() != null){
            sb.append(" and min(min_top_of_search_is) <= ?");
            argsList.add(qo.getTopImpressionShareMax());
        }

        if (qo.isSupportRankFilter()) {
            sb.append(" and any(search_frequency_rank) is not null ");
            // 搜索词排名
            if (qo.getSearchFrequencyRankMin() != null) {
                sb.append(" and any(search_frequency_rank) >= ? ");
                argsList.add(qo.getSearchFrequencyRankMin());
            }
            if (qo.getSearchFrequencyRankMax() != null) {
                sb.append(" and any(search_frequency_rank) <= ? ");
                argsList.add(qo.getSearchFrequencyRankMax());
            }
            if (qo.getWeekRatioMin() != null) {
                sb.append(" and any(round(week_ratio*100,2)) >= ? ");
                argsList.add(qo.getWeekRatioMin());
            }
            if (qo.getWeekRatioMax() != null) {
                sb.append(" and any(round(week_ratio*100,2)) <= ? ");
                argsList.add(qo.getWeekRatioMax());
            }

        }
        return sb.toString();
    }

    public static String dealOnlyShowImpressionsWithHavingAnd(Boolean onlyShowImpressionsBool) {
        boolean onlyShowImpressions = Boolean.TRUE.equals(onlyShowImpressionsBool);
        if (onlyShowImpressions) {
            return " and sum_impressions > 0 ";
        }
        return "";
    }

    public static String dealOnlyShowImpressionsWithOnAnd(Boolean onlyShowImpressionsBool) {
        boolean onlyShowImpressions = Boolean.TRUE.equals(onlyShowImpressionsBool);
        if (onlyShowImpressions) {
            return " and ifnull(impressions, 0) > 0 ";
        }
        return "";
    }

    public static String dealOnlyShowImpressionsWithWhereAnd(Boolean onlyShowImpressionsBool) {
        boolean onlyShowImpressions = Boolean.TRUE.equals(onlyShowImpressionsBool);
        if (onlyShowImpressions) {
            return " and r.sum_impressions > 0 ";
        }
        return "";
    }

   /* public static String dealOrderField(NeTargetReportParamsMappingEnum param, String adType) {
        if (adType == null) {
            return "";
        }
        boolean sbAndSdType = Constants.SB.equalsIgnoreCase(adType) || Constants.SD.equalsIgnoreCase(adType);
        switch (param) {
            case ACOS:
                return sbAndSdType ? ",ROUND(ifnull(cost / sales14d * 100, 0), 2) AS sum_acos " : ",ROUND(ifnull(cost / total_sales * 100, 0), 2) AS sum_acos ";
            case AD_ORDER_NUM:
                return sbAndSdType ? ",ifnull(conversions14d, 0) AS sum_ad_order_num " : ",ifnull(sale_num, 0) AS sum_ad_order_num ";
            case CLICKS:
                return ",ifnull(clicks, 0) AS sum_clicks ";
            case IMPRESSIONS:
                return ",ifnull(impressions, 0) AS sum_impressions ";
            case AD_COST:
                return ",ifnull(cost, 0) AS sum_costs ";
            case AD_SALE:
                return sbAndSdType ? ",ifnull(sales14d, 0)  AS sum_adSales " : ",ifnull(total_sales, 0)  AS sum_adSales ";
            case CTR:
                return ",ROUND(ifnull(clicks / impressions * 100, 0), 2) AS sum_ctr ";
            case CVR:
                return sbAndSdType ? ",ROUND(ifnull(conversions14d / clicks * 100, 0), 2) AS sum_cvr " : ",ROUND(ifnull(sale_num / clicks * 100, 0), 2) AS sum_cvr ";
            case ROAS:
                return sbAndSdType ? ",ROUND(ifnull(sales14d / cost, 0), 2) AS sum_roas " : ",ROUND(ifnull(total_sales / cost, 0), 2) AS sum_roas ";
            case AD_COST_PER_CLICK:
                return ",ROUND(ifnull(cost / clicks, 0), 2) AS sum_cpc ";
            case CPA:
                return sbAndSdType ? ",ROUND(ifnull(cost / conversions14d, 0), 2) AS sum_cpa " : ",ROUND(ifnull(cost / sale_num, 0), 2) AS sum_cpa ";
            default:
                return "";
        }
    }*/
  /*  public static String dealOrderFieldSum(NeTargetReportParamsMappingEnum param, String adType) {
        if (adType == null) {
            return "";
        }
        boolean sbAndSdType = Constants.SB.equalsIgnoreCase(adType) || Constants.SD.equalsIgnoreCase(adType);
        switch (param) {
            case ACOS:
                return sbAndSdType ? ",ROUND(ifnull(sum(cost) / sum(sales14d) * 100, 0), 2) AS sum_acos " : ",ROUND(ifnull(sum(cost) / sum(total_sales) * 100, 0), 2) AS sum_acos ";
            case AD_ORDER_NUM:
                return sbAndSdType ? ",ifnull(sum(conversions14d), 0) AS sum_ad_order_num " : ",ifnull(sum(sale_num), 0) AS sum_ad_order_num ";
            case CLICKS:
                return ",ifnull(sum(clicks), 0) AS sum_clicks ";
            case IMPRESSIONS:
                return ",ifnull(sum(impressions), 0) AS sum_impressions ";
            case AD_COST:
                return ",ifnull(sum(cost), 0) AS sum_costs ";
            case AD_SALE:
                return sbAndSdType ? ",ifnull(sum(sales14d), 0)  AS sum_adSales " : ",ifnull(sum(total_sales), 0)  AS sum_adSales ";
            case CTR:
                return ",ROUND(ifnull(sum(clicks) / sum(impressions) * 100, 0), 2) AS sum_ctr ";
            case CVR:
                return sbAndSdType ? ",ROUND(ifnull(sum(conversions14d) / sum(clicks) * 100, 0), 2) AS sum_cvr " : ",ROUND(ifnull(sum(sale_num) / sum(clicks) * 100, 0), 2) AS sum_cvr ";
            case ROAS:
                return sbAndSdType ? ",ROUND(ifnull(sum(sales14d) / sum(cost), 0), 2) AS sum_roas " : ",ROUND(ifnull(sum(total_sales) / sum(cost), 0), 2) AS sum_roas ";
            case AD_COST_PER_CLICK:
                return ",ROUND(ifnull(sum(cost) / sum(clicks), 0), 2) AS sum_cpc ";
            case CPA:
                return sbAndSdType ? ",ROUND(ifnull(sum(cost) / sum(conversions14d), 0), 2) AS sum_cpa " : ",ROUND(ifnull(sum(cost) / sum(sale_num), 0), 2) AS sum_cpa ";
            default:
                return "";
        }
    }*/


    public static String deal30SelectSqlForNeTarget(NeTargetReportAdvanceFiltersDto qo, Boolean doOnlyShowImpressions, String adType, Boolean doAdvancedFilterBoolean, boolean order, NeTargetReportParamsMappingEnum orderParam) {
        if (qo == null) {
            return "";
        }
        boolean sbAndSdType = Constants.SB.equalsIgnoreCase(adType) || Constants.SD.equalsIgnoreCase(adType);
        boolean doAdvancedFilter = Boolean.TRUE.equals(doAdvancedFilterBoolean);
        boolean onlyShowImpressions = Boolean.TRUE.equals(doOnlyShowImpressions);
        //包含高级筛选，排序，只展示有曝光的3种情况
        if (doAdvancedFilter || onlyShowImpressions || order) {
            return dealAdvancedFilter(qo, sbAndSdType, onlyShowImpressions, orderParam);
        }

        return "";
    }
    private static String dealAdvancedFilter(NeTargetReportAdvanceFiltersDto qo, boolean sbAndSdType, Boolean onlyShowImpressions, NeTargetReportParamsMappingEnum orderParam) {
        StringBuilder sb = new StringBuilder();
        //acos
        if (qo.getAcosMin() != null || qo.getAcosMax() != null || NeTargetReportParamsMappingEnum.ACOS.equals(orderParam)) {
            if (sbAndSdType) {
                sb.append(",ROUND(ifnull(cost / sales14d * 100, 0), 2) AS sum_acos ");
            } else {
                sb.append(",ROUND(ifnull(cost / total_sales * 100, 0), 2) AS sum_acos ");
            }
        }

        //广告订单量
        if (qo.getAdOrderNumMin() != null || qo.getAdOrderNumMax() != null || NeTargetReportParamsMappingEnum.AD_ORDER_NUM.equals(orderParam)) {
            if (sbAndSdType) {
                sb.append(",ifnull(conversions14d, 0) AS sum_ad_order_num ");
            } else {
                sb.append(",ifnull(sale_num, 0) AS sum_ad_order_num ");
            }
        }

        //点击量
        if (qo.getClicksMin() != null || qo.getClicksMax() != null || NeTargetReportParamsMappingEnum.CLICKS.equals(orderParam)) {
            sb.append(",ifnull(clicks, 0) AS sum_clicks ");
        }

        //展示量
        if (qo.getImpressionsMin() != null || qo.getImpressionsMax() != null || Boolean.TRUE.equals(onlyShowImpressions) || NeTargetReportParamsMappingEnum.IMPRESSIONS.equals(orderParam)) {
            sb.append(",ifnull(impressions, 0) AS sum_impressions ");
        }

        //花费
        if (qo.getAdCostMin() != null || qo.getAdCostMax() != null || NeTargetReportParamsMappingEnum.AD_COST.equals(orderParam)) {
            sb.append(",ifnull(cost, 0) AS sum_costs ");
        }

        //广告销售额
        if (qo.getAdSaleMin() != null || qo.getAdSaleMax() != null || NeTargetReportParamsMappingEnum.AD_SALE.equals(orderParam)) {
            if (sbAndSdType) {
                sb.append(",ifnull(sales14d, 0)  AS sum_adSales ");
            } else {
                sb.append(",ifnull(total_sales, 0)  AS sum_adSales ");
            }
        }

        //点击率（clicks/impressions）
        if (qo.getCtrMin() != null || qo.getCtrMax() != null || NeTargetReportParamsMappingEnum.CTR.equals(orderParam)) {
            sb.append(",ROUND(ifnull(clicks / impressions * 100, 0), 2) AS sum_ctr ");
        }

        //订单转化率
        if (qo.getCvrMin() != null || qo.getCvrMax() != null || NeTargetReportParamsMappingEnum.CVR.equals(orderParam)) {
            if (sbAndSdType) {
                sb.append(",ROUND(ifnull(conversions14d / clicks * 100, 0), 2) AS sum_cvr ");
            } else {
                sb.append(",ROUND(ifnull(sale_num / clicks * 100, 0), 2) AS sum_cvr ");
            }
        }

        // roas
        if (qo.getRoasMin() != null || qo.getRoasMax() != null || NeTargetReportParamsMappingEnum.ROAS.equals(orderParam)) {
            if (sbAndSdType) {
                sb.append(",ROUND(ifnull(sales14d / cost, 0), 2) AS sum_roas ");
            } else {
                sb.append(",ROUND(ifnull(total_sales / cost, 0), 2) AS sum_roas ");
            }
        }

        //cpc  平均点击费用
        if (qo.getCpcMin() != null || qo.getCpcMax() != null || NeTargetReportParamsMappingEnum.AD_COST_PER_CLICK.equals(orderParam)) {
            sb.append(",ROUND(ifnull(cost / clicks, 0), 2) AS sum_cpc ");
        }

        //CPA
        if (qo.getCpaMin() != null || qo.getCpaMax() != null || NeTargetReportParamsMappingEnum.CPA.equals(orderParam)) {
            if (sbAndSdType) {
                sb.append(",ROUND(ifnull(cost / conversions14d, 0), 2) AS sum_cpa ");
            } else {
                sb.append(",ROUND(ifnull(cost / sale_num, 0), 2) AS sum_cpa ");
            }
        }

        return sb.toString();
    }

    public static String dealSelectSqlForNeTarget(NeTargetReportAdvanceFiltersDto qo, Boolean doOnlyShowImpressions, String adType, Boolean doAdvancedFilterBoolean, boolean order, NeTargetReportParamsMappingEnum orderParam) {
        if (qo == null) {
            return "";
        }
        boolean sbAndSdType = Constants.SB.equalsIgnoreCase(adType) || Constants.SD.equalsIgnoreCase(adType);
        boolean doAdvancedFilter = Boolean.TRUE.equals(doAdvancedFilterBoolean);
        boolean onlyShowImpressions = Boolean.TRUE.equals(doOnlyShowImpressions);
        //包含高级筛选，排序，只展示有曝光的3种情况
        if (doAdvancedFilter || onlyShowImpressions || order) {
            return dealAdvancedFilterSum(qo, sbAndSdType, onlyShowImpressions, orderParam);
        }

        return "";
    }

    private static String dealAdvancedFilterSum(NeTargetReportAdvanceFiltersDto qo, boolean sbAndSdType, boolean onlyShowImpressions, NeTargetReportParamsMappingEnum orderParam) {
        StringBuilder sb = new StringBuilder();
        if (qo == null) {
            return sb.toString();
        }

        //acos
        if (qo.getAcosMin() != null || qo.getAcosMax() != null || NeTargetReportParamsMappingEnum.ACOS.equals(orderParam)) {
            if (sbAndSdType) {
                sb.append(",ROUND(ifnull(sum(cost) / sum(sales14d) * 100, 0), 2) AS sum_acos ");
            } else {
                sb.append(",ROUND(ifnull(sum(cost) / sum(total_sales) * 100, 0), 2) AS sum_acos ");
            }
        }

        //广告订单量
        if (qo.getAdOrderNumMin() != null || qo.getAdOrderNumMax() != null || NeTargetReportParamsMappingEnum.AD_ORDER_NUM.equals(orderParam)) {
            if (sbAndSdType) {
                sb.append(",ifnull(sum(conversions14d), 0) AS sum_ad_order_num ");
            } else {
                sb.append(",ifnull(sum(sale_num), 0) AS sum_ad_order_num ");
            }
        }

        //点击量
        if (qo.getClicksMin() != null || qo.getClicksMax() != null || NeTargetReportParamsMappingEnum.CLICKS.equals(orderParam)) {
            sb.append(",ifnull(sum(clicks), 0) AS sum_clicks ");
        }

        //展示量
        if (qo.getImpressionsMin() != null || qo.getImpressionsMax() != null || Boolean.TRUE.equals(onlyShowImpressions) || NeTargetReportParamsMappingEnum.IMPRESSIONS.equals(orderParam)) {
            sb.append(",ifnull(sum(impressions), 0) AS sum_impressions ");
        }

        //花费
        if (qo.getAdCostMin() != null || qo.getAdCostMax() != null || NeTargetReportParamsMappingEnum.AD_COST.equals(orderParam)) {
            sb.append(",ifnull(sum(cost), 0) AS sum_costs ");
        }

        //广告销售额
        if (qo.getAdSaleMin() != null || qo.getAdSaleMax() != null || NeTargetReportParamsMappingEnum.AD_SALE.equals(orderParam)) {
            if (sbAndSdType) {
                sb.append(",ifnull(sum(sales14d), 0)  AS sum_adSales ");
            } else {
                sb.append(",ifnull(sum(total_sales), 0)  AS sum_adSales ");
            }
        }

        //点击率（clicks/impressions）
        if (qo.getCtrMin() != null || qo.getCtrMax() != null || NeTargetReportParamsMappingEnum.CTR.equals(orderParam)) {
            sb.append(",ROUND(ifnull(sum(clicks) / sum(impressions) * 100, 0), 2) AS sum_ctr ");
        }

        //订单转化率
        if (qo.getCvrMin() != null || qo.getCvrMax() != null || NeTargetReportParamsMappingEnum.CVR.equals(orderParam)) {
            if (sbAndSdType) {
                sb.append(",ROUND(ifnull(sum(conversions14d) / sum(clicks) * 100, 0), 2) AS sum_cvr ");
            } else {
                sb.append(",ROUND(ifnull(sum(sale_num) / sum(clicks) * 100, 0), 2) AS sum_cvr ");
            }
        }

        // roas
        if (qo.getRoasMin() != null || qo.getRoasMax() != null || NeTargetReportParamsMappingEnum.ROAS.equals(orderParam)) {
            if (sbAndSdType) {
                sb.append(",ROUND(ifnull(sum(sales14d) / sum(cost), 0), 2) AS sum_roas ");
            } else {
                sb.append(",ROUND(ifnull(sum(total_sales) / sum(cost), 0), 2) AS sum_roas ");
            }
        }

        //cpc  平均点击费用
        if (qo.getCpcMin() != null || qo.getCpcMax() != null || NeTargetReportParamsMappingEnum.AD_COST_PER_CLICK.equals(orderParam)) {
            sb.append(",ROUND(ifnull(sum(cost) / sum(clicks), 0), 2) AS sum_cpc ");
        }

        //CPA
        if (qo.getCpaMin() != null || qo.getCpaMax() != null || NeTargetReportParamsMappingEnum.CPA.equals(orderParam)) {
            if (sbAndSdType) {
                sb.append(",ROUND(ifnull(sum(cost) / sum(conversions14d), 0), 2) AS sum_cpa ");
            } else {
                sb.append(",ROUND(ifnull(sum(cost) / sum(sale_num), 0), 2) AS sum_cpa ");
            }
        }

        return sb.toString();
    }




    public static String deal30AdvancedFilterForNeTargetWithOnAnd(NeTargetReportAdvanceFiltersDto qo, List<Object> argsList, Boolean onlyShowImpressions, String adType) {
        StringBuilder sb = new StringBuilder();
        if (qo == null) {
            return sb.toString();
        }
        //acos
        if (qo.getAcosMin() != null) {
            if (Constants.SB.equalsIgnoreCase(adType) || Constants.SD.equalsIgnoreCase(adType)) {
                sb.append(" and ROUND(ifnull(cost/sales14d*100,0),2) >= ? ");
            } else {
                sb.append(" and ROUND(ifnull(cost/total_sales*100,0),2) >= ? ");
            }
            argsList.add(qo.getAcosMin());
        }
        if (qo.getAcosMax() != null) {
            if (Constants.SB.equalsIgnoreCase(adType) || Constants.SD.equalsIgnoreCase(adType)) {
                sb.append(" and ROUND(ifnull(cost/sales14d*100,0),2) <= ? ");
            } else {
                sb.append(" and ROUND(ifnull(cost/total_sales*100,0),2) <= ? ");
            }
            argsList.add(qo.getAcosMax());
        }
        //广告订单量
        if (qo.getAdOrderNumMin() != null) {
            if (Constants.SB.equalsIgnoreCase(adType) || Constants.SD.equalsIgnoreCase(adType)) {
                sb.append(" and ifnull(conversions14d, 0) >= ? ");
            } else {
                sb.append(" and ifnull(sale_num, 0) >= ? ");
            }
            argsList.add(qo.getAdOrderNumMin());
        }
        if (qo.getAdOrderNumMax() != null) {
            if (Constants.SB.equalsIgnoreCase(adType) || Constants.SD.equalsIgnoreCase(adType)) {
                sb.append(" and ifnull(conversions14d, 0) <= ? ");
            } else {
                sb.append(" and ifnull(sale_num, 0) <= ? ");
            }
            argsList.add(qo.getAdOrderNumMax());
        }
        //点击量
        if (qo.getClicksMin() != null) {
            sb.append(" and ifnull(clicks, 0) >= ? ");
            argsList.add(qo.getClicksMin());
        }
        if (qo.getClicksMax() != null) {
            sb.append(" and ifnull(clicks, 0) <= ? ");
            argsList.add(qo.getClicksMax());
        }
        //展示量
        if (qo.getImpressionsMin() != null) {
            sb.append(" and ifnull(impressions, 0) >= ? ");
            argsList.add(qo.getImpressionsMin());
        }
        if (qo.getImpressionsMax() != null) {
            sb.append(" and ifnull(impressions, 0) <= ? ");
            argsList.add(qo.getImpressionsMax());
        }
        if (Boolean.TRUE.equals(onlyShowImpressions)) {
            sb.append("and ifnull(impressions, 0) > 0 ");
        }
        //花费
        if (qo.getAdCostMin() != null) {
            sb.append(" and ifnull(cost, 0) >= ?");
            argsList.add(qo.getAdCostMin());
        }
        if (qo.getAdCostMax() != null) {
            sb.append(" and ifnull(cost, 0) <= ?");
            argsList.add(qo.getAdCostMax());
        }
        //广告销售额
        if (qo.getAdSaleMin() != null) {
            if (Constants.SB.equalsIgnoreCase(adType) || Constants.SD.equalsIgnoreCase(adType)) {
                sb.append(" and ifnull(sales14d, 0) >= ?");
            } else {
                sb.append(" and ifnull(total_sales, 0) >= ?");
            }
            argsList.add(qo.getAdSaleMin());
        }
        if (qo.getAdSaleMax() != null) {
            if (Constants.SB.equalsIgnoreCase(adType) || Constants.SD.equalsIgnoreCase(adType)) {
                sb.append(" and ifnull(sales14d, 0) <= ?");
            } else {
                sb.append(" and ifnull(total_sales, 0) <= ?");
            }
            argsList.add(qo.getAdSaleMax());
        }
        //点击率（clicks/impressions）
        if (qo.getCtrMin() != null) {
            sb.append(" and ROUND(ifnull(clicks/impressions*100,0),2) >= ?");
            argsList.add(qo.getCtrMin());
        }
        if (qo.getCtrMax() != null) {
            sb.append(" and ROUND(ifnull(clicks/impressions*100,0),2) <= ?");
            argsList.add(qo.getCtrMax());
        }
        //订单转化率
        if (qo.getCvrMin() != null) {
            if (Constants.SB.equalsIgnoreCase(adType) || Constants.SD.equalsIgnoreCase(adType)) {
                sb.append(" and ROUND(ifnull(conversions14d/clicks*100,0),2) >= ?");
            } else {
                sb.append(" and ROUND(ifnull(sale_num/clicks*100,0),2) >= ?");
            }
            argsList.add(qo.getCvrMin());
        }
        if (qo.getCvrMax() != null) {
            if (Constants.SB.equalsIgnoreCase(adType) || Constants.SD.equalsIgnoreCase(adType)) {
                sb.append(" and ROUND(ifnull(conversions14d/clicks*100,0),2) <= ?");
            } else {
                sb.append(" and ROUND(ifnull(sale_num/clicks*100,0),2) <= ?");
            }
            argsList.add(qo.getCvrMax());
        }
        // roas
        if (qo.getRoasMin() != null) {
            if (Constants.SB.equalsIgnoreCase(adType) || Constants.SD.equalsIgnoreCase(adType)) {
                sb.append(" and ROUND(ifnull(sales14d/cost,0),2) >= ?");
            } else {
                sb.append(" and ROUND(ifnull(total_sales/cost,0),2) >= ?");
            }
            argsList.add(qo.getRoasMin());
        }
        if (qo.getRoasMax() != null) {
            if (Constants.SB.equalsIgnoreCase(adType) || Constants.SD.equalsIgnoreCase(adType)) {
                sb.append(" and ROUND(ifnull(sales14d/cost,0),2) <= ?");
            } else {
                sb.append(" and ROUND(ifnull(total_sales/cost,0),2) <= ?");
            }
            argsList.add(qo.getRoasMax());
        }
        //cpc  平均点击费用
        if (qo.getCpcMin() != null) {
            sb.append(" and ROUND(ifnull(cost/clicks,0),2) >= ?");
            argsList.add(qo.getCpcMin());
        }
        if (qo.getCpcMax() != null) {
            sb.append(" and ROUND(ifnull(cost/clicks,0),2) <= ?");
            argsList.add(qo.getCpcMax());
        }
        //CPA
        if (qo.getCpaMin() != null) {
            if (Constants.SB.equalsIgnoreCase(adType) || Constants.SD.equalsIgnoreCase(adType)) {
                sb.append(" and ROUND(ifnull(cost/conversions14d,0), 2) >= ?");
            } else {
                sb.append(" and ROUND(ifnull(cost/sale_num,0), 2) >= ?");
            }
            argsList.add(qo.getCpaMin());
        }
        if (qo.getCpaMax() != null) {
            sb.append(" and ROUND(ifnull(cost/sale_num,0), 2) <= ?");
            argsList.add(qo.getCpaMax());
        }
        return sb.toString();
    }

    public static String deal30AdvancedFilterForNeTargetWithHavingAnd(NeTargetReportAdvanceFiltersDto qo, List<Object> argsList, Boolean onlyShowImpressions) {
        StringBuilder sb = new StringBuilder();
        if (qo == null) {
            return sb.toString();
        }
        //acos
        if (qo.getAcosMin() != null) {
            sb.append(" and sum_acos >= ? ");
            argsList.add(qo.getAcosMin());
        }
        if (qo.getAcosMax() != null) {
            sb.append(" and sum_acos <= ? ");
            argsList.add(qo.getAcosMax());
        }
        //广告订单量
        if (qo.getAdOrderNumMin() != null) {
            sb.append(" and sum_ad_order_num >= ? ");
            argsList.add(qo.getAdOrderNumMin());
        }
        if (qo.getAdOrderNumMax() != null) {
            sb.append(" and sum_ad_order_num <= ? ");
            argsList.add(qo.getAdOrderNumMax());
        }
        //点击量
        if (qo.getClicksMin() != null) {
            sb.append(" and sum_clicks >= ? ");
            argsList.add(qo.getClicksMin());
        }
        if (qo.getClicksMax() != null) {
            sb.append(" and sum_clicks <= ? ");
            argsList.add(qo.getClicksMax());
        }
        //展示量
        if (qo.getImpressionsMin() != null) {
            sb.append(" and sum_impressions >= ? ");
            argsList.add(qo.getImpressionsMin());
        }
        if (qo.getImpressionsMax() != null) {
            sb.append(" and sum_impressions <= ? ");
            argsList.add(qo.getImpressionsMax());
        }
        if (Boolean.TRUE.equals(onlyShowImpressions)) {
            sb.append("and sum_impressions > 0 ");
        }
        //花费
        if (qo.getAdCostMin() != null) {
            sb.append(" and sum_costs >= ?");
            argsList.add(qo.getAdCostMin());
        }
        if (qo.getAdCostMax() != null) {
            sb.append(" and sum_costs <= ?");
            argsList.add(qo.getAdCostMax());
        }
        //广告销售额
        if (qo.getAdSaleMin() != null) {
            sb.append(" and sum_adSales >= ?");
            argsList.add(qo.getAdSaleMin());
        }
        if (qo.getAdSaleMax() != null) {
            sb.append(" and sum_adSales <= ?");
            argsList.add(qo.getAdSaleMax());
        }
        //点击率（clicks/impressions）
        if (qo.getCtrMin() != null) {
            sb.append(" and sum_ctr >= ?");
            argsList.add(qo.getCtrMin());
        }
        if (qo.getCtrMax() != null) {
            sb.append(" and sum_ctr <= ?");
            argsList.add(qo.getCtrMax());
        }
        //订单转化率
        if (qo.getCvrMin() != null) {
            sb.append(" and sum_cvr >= ?");
            argsList.add(qo.getCvrMin());
        }
        if (qo.getCvrMax() != null) {
            sb.append(" and sum_cvr <= ?");
            argsList.add(qo.getCvrMax());
        }
        // roas
        if (qo.getRoasMin() != null) {
            sb.append(" and sum_roas >= ?");
            argsList.add(qo.getRoasMin());
        }
        if (qo.getRoasMax() != null) {
            sb.append(" and sum_roas <= ?");
            argsList.add(qo.getRoasMax());
        }
        //cpc  平均点击费用
        if (qo.getCpcMin() != null) {
            sb.append(" and sum_cpc >= ?");
            argsList.add(qo.getCpcMin());
        }
        if (qo.getCpcMax() != null) {
            sb.append(" and sum_cpc <= ?");
            argsList.add(qo.getCpcMax());
        }
        //CPA
        if (qo.getCpaMin() != null) {
            sb.append(" and sum_cpa >= ?");
            argsList.add(qo.getCpaMin());
        }
        if (qo.getCpaMax() != null) {
            sb.append(" and sum_cpa <= ?");
            argsList.add(qo.getCpaMax());
        }
        return sb.toString();
    }

    public static String dealAdvancedFilterForNeTargetWithWhereAnd(NeTargetReportAdvanceFiltersDto qo, List<Object> argsList, Boolean onlyShowImpressions) {
        StringBuilder sb = new StringBuilder();
        if (qo == null) {
            return sb.toString();
        }
        //acos
        if (qo.getAcosMin() != null) {
            sb.append(" and ifnull(r.sum_acos, 0) >= ? ");
            argsList.add(qo.getAcosMin());
        }
        if (qo.getAcosMax() != null) {
            sb.append(" and ifnull(r.sum_acos, 0) <= ? ");
            argsList.add(qo.getAcosMax());
        }
        //广告订单量
        if (qo.getAdOrderNumMin() != null) {
            sb.append(" and ifnull(r.sum_ad_order_num, 0) >= ? ");
            argsList.add(qo.getAdOrderNumMin());
        }
        if (qo.getAdOrderNumMax() != null) {
            sb.append(" and ifnull(r.sum_ad_order_num, 0) <= ? ");
            argsList.add(qo.getAdOrderNumMax());
        }
        //点击量
        if (qo.getClicksMin() != null) {
            sb.append(" and ifnull(r.sum_clicks, 0) >= ? ");
            argsList.add(qo.getClicksMin());
        }
        if (qo.getClicksMax() != null) {
            sb.append(" and ifnull(r.sum_clicks, 0) <= ? ");
            argsList.add(qo.getClicksMax());
        }
        //展示量
        if (qo.getImpressionsMin() != null) {
            sb.append(" and ifnull(r.sum_impressions, 0) >= ? ");
            argsList.add(qo.getImpressionsMin());
        }
        if (qo.getImpressionsMax() != null) {
            sb.append(" and ifnull(r.sum_impressions, 0) <= ? ");
            argsList.add(qo.getImpressionsMax());
        }
        if (Boolean.TRUE.equals(onlyShowImpressions)) {
            sb.append(" and ifnull(r.sum_impressions, 0) > 0 ");
        }
        //花费
        if (qo.getAdCostMin() != null) {
            sb.append(" and ifnull(r.sum_costs, 0) >= ?");
            argsList.add(qo.getAdCostMin());
        }
        if (qo.getAdCostMax() != null) {
            sb.append(" and ifnull(r.sum_costs, 0) <= ?");
            argsList.add(qo.getAdCostMax());
        }
        //广告销售额
        if (qo.getAdSaleMin() != null) {
            sb.append(" and ifnull(r.sum_adSales, 0) >= ?");
            argsList.add(qo.getAdSaleMin());
        }
        if (qo.getAdSaleMax() != null) {
            sb.append(" and ifnull(r.sum_adSales, 0) <= ?");
            argsList.add(qo.getAdSaleMax());
        }
        //点击率（clicks/impressions）
        if (qo.getCtrMin() != null) {
            sb.append(" and ifnull(r.sum_ctr, 0) >= ?");
            argsList.add(qo.getCtrMin());
        }
        if (qo.getCtrMax() != null) {
            sb.append(" and ifnull(r.sum_ctr, 0) <= ?");
            argsList.add(qo.getCtrMax());
        }
        //订单转化率
        if (qo.getCvrMin() != null) {
            sb.append(" and ifnull(r.sum_cvr, 0) >= ?");
            argsList.add(qo.getCvrMin());
        }
        if (qo.getCvrMax() != null) {
            sb.append(" and ifnull(r.sum_cvr, 0) <= ?");
            argsList.add(qo.getCvrMax());
        }
        // roas
        if (qo.getRoasMin() != null) {
            sb.append(" and ifnull(r.sum_roas, 0) >= ?");
            argsList.add(qo.getRoasMin());
        }
        if (qo.getRoasMax() != null) {
            sb.append(" and ifnull(r.sum_roas, 0) <= ?");
            argsList.add(qo.getRoasMax());
        }
        //cpc  平均点击费用
        if (qo.getCpcMin() != null) {
            sb.append(" and ifnull(r.sum_cpc, 0) >= ?");
            argsList.add(qo.getCpcMin());
        }
        if (qo.getCpcMax() != null) {
            sb.append(" and ifnull(r.sum_cpc, 0) <= ?");
            argsList.add(qo.getCpcMax());
        }
        //CPA
        if (qo.getCpaMin() != null) {
            sb.append(" and ifnull(r.sum_cpa, 0) >= ?");
            argsList.add(qo.getCpaMin());
        }
        if (qo.getCpaMax() != null) {
            sb.append(" and ifnull(r.sum_cpa, 0) <= ?");
            argsList.add(qo.getCpaMax());
        }
        return sb.toString();
    }
    public static String dealAdvancedFilterForNeTargetWithAnd(NeTargetReportAdvanceFiltersDto qo, List<Object> argsList, Boolean onlyShowImpressions) {
        StringBuilder sb = new StringBuilder();
        if (qo == null) {
            return sb.toString();
        }
        //acos
        if (qo.getAcosMin() != null) {
            sb.append(" and ROUND(ifnull(sum(r.cost)/sum(r.total_sales)*100,0),2) >= ?");
            argsList.add(qo.getAcosMin());
        }
        if (qo.getAcosMax() != null) {
            sb.append(" and ROUND(ifnull(sum(r.cost)/sum(r.total_sales)*100,0),2) <= ?");
            argsList.add(qo.getAcosMax());
        }
        //广告订单量
        if (qo.getAdOrderNumMin() != null) {
            sb.append(" and ifnull(sum(r.sale_num), 0) >= ?");
            argsList.add(qo.getAdOrderNumMin());
        }
        if (qo.getAdOrderNumMax() != null) {
            sb.append(" and ifnull(sum(r.sale_num), 0) <= ?");
            argsList.add(qo.getAdOrderNumMax());
        }
        //点击量
        if (qo.getClicksMin() != null) {
            sb.append(" and ifnull(sum(r.clicks), 0) >= ?");
            argsList.add(qo.getClicksMin());
        }
        if (qo.getClicksMax() != null) {
            sb.append(" and ifnull(sum(r.clicks), 0) <= ?");
            argsList.add(qo.getClicksMax());
        }
        //曝光量
        if (qo.getImpressionsMin() != null) {
            sb.append(" and ifnull(sum(r.impressions), 0) >= ?");
            argsList.add(qo.getImpressionsMin());
        }
        if (qo.getImpressionsMax() != null) {
            sb.append(" and ifnull(sum(r.impressions), 0) <= ?");
            argsList.add(qo.getImpressionsMax());
        }
        if (Boolean.TRUE.equals(onlyShowImpressions)) {
            sb.append("and ifnull(r.impressions, 0) > 0 ");
        }
        //花费
        if (qo.getAdCostMin() != null) {
            sb.append(" and ifnull(sum(r.cost), 0) >= ?");
            argsList.add(qo.getAdCostMin());
        }
        if (qo.getAdCostMax() != null) {
            sb.append(" and ifnull(sum(r.cost), 0) <= ?");
            argsList.add(qo.getAdCostMax());
        }
        //广告销售额
        if (qo.getAdSaleMin() != null) {
            sb.append(" and ifnull(sum(r.total_sales), 0) >= ?");
            argsList.add(qo.getAdSaleMin());
        }
        if (qo.getAdSaleMax() != null) {
            sb.append(" and ifnull(sum(r.total_sales), 0) <= ?");
            argsList.add(qo.getAdSaleMax());
        }
        //点击率（clicks/impressions）
        if (qo.getCtrMin() != null) {
            sb.append(" and ROUND(ifnull(sum(r.clicks)/sum(r.impressions)*100,0),2) >= ?");
            argsList.add(qo.getCtrMin());
        }
        if (qo.getCtrMax() != null) {
            sb.append(" and ROUND(ifnull(sum(r.clicks)/sum(r.impressions)*100,0),2) <= ?");
            argsList.add(qo.getCtrMax());
        }
        //订单转化率
        if (qo.getCvrMin() != null) {
            sb.append(" and ROUND(ifnull(sum(r.sale_num)/sum(r.clicks)*100,0),2) >= ?");
            argsList.add(qo.getCvrMin());
        }
        if (qo.getCvrMax() != null) {
            sb.append(" and ROUND(ifnull(sum(r.sale_num)/sum(r.clicks)*100,0),2) <= ?");
            argsList.add(qo.getCvrMax());
        }
        // roas
        if (qo.getRoasMin() != null) {
            sb.append(" and ROUND(ifnull(sum(r.total_sales)/sum(r.cost),0),2) >= ?");
            argsList.add(qo.getRoasMin());
        }
        if (qo.getRoasMax() != null) {
            sb.append(" and ROUND(ifnull(sum(r.total_sales)/sum(r.cost),0),2) <= ?");
            argsList.add(qo.getRoasMax());
        }
        //cpc  平均点击费用
        if (qo.getCpcMin() != null) {
            sb.append(" and ROUND(ifnull(sum(r.cost)/sum(r.clicks),0),2) >= ?");
            argsList.add(qo.getCpcMin());
        }
        if (qo.getCpcMax() != null) {
            sb.append(" and ROUND(ifnull(sum(r.cost)/sum(r.clicks),0),2) <= ?");
            argsList.add(qo.getCpcMax());
        }
        //CPA
        if (qo.getCpaMin() != null) {
            sb.append(" and ROUND(ifnull(sum(r.cost)/sum(r.sale_num),0), 2) >= ?");
            argsList.add(qo.getCpaMin());
        }
        if (qo.getCpaMax() != null) {
            sb.append(" and ROUND(ifnull(sum(r.cost)/sum(r.sale_num),0), 2) <= ?");
            argsList.add(qo.getCpaMax());
        }
        return sb.toString();
    }


    public static String getDorisSbTargetPageHavingSql(ReportAdvancedFilterBaseQo qo, List<Object> argsList) {
        StringBuilder sb = new StringBuilder();
        BigDecimal shopSales = qo.getShopSales() != null ? qo.getShopSales() : BigDecimal.valueOf(0);
        sb.append(" having 1=1 ");
        //竞价
        if (qo.getBidMin() != null) {
            sb.append(" and IFNULL(ANY(k.bid), ANY(g.bid)) >= ? ");
            argsList.add(qo.getBidMin());
        }
        if (qo.getBidMax() != null) {
            sb.append(" and IFNULL(ANY(k.bid), ANY(g.bid)) <= ? ");
            argsList.add(qo.getBidMax());
        }
        //展示量
        if (qo.getImpressionsMin() != null) {
            sb.append(" and ifnull(sum(impressions), 0) >= ?");
            argsList.add(qo.getImpressionsMin());
        }
        if (qo.getImpressionsMax() != null) {
            sb.append(" and ifnull(sum(impressions), 0) <= ?");
            argsList.add(qo.getImpressionsMax());
        }
        //点击量
        if (qo.getClicksMin() != null) {
            sb.append(" and ifnull(sum(clicks), 0) >= ?");
            argsList.add(qo.getClicksMin());
        }
        if (qo.getClicksMax() != null) {
            sb.append(" and ifnull(sum(clicks), 0) <= ?");
            argsList.add(qo.getClicksMax());
        }
        //点击率（clicks/impressions）
        if (qo.getClickRateMin() != null) {
            sb.append(" and ROUND(ifnull(sum(clicks)/sum(impressions)*100,0),2) >= ?");
            argsList.add(qo.getClickRateMin());
        }
        if (qo.getClickRateMax() != null) {
            sb.append(" and ROUND(ifnull(sum(clicks)/sum(impressions)*100,0),2) <= ?");
            argsList.add(qo.getClickRateMax());
        }
        //花费
        if (qo.getCostMin() != null) {
            sb.append(" and ifnull(sum(cost), 0) >= ?");
            argsList.add(qo.getCostMin());
        }
        if (qo.getCostMax() != null) {
            sb.append(" and ifnull(sum(cost), 0) <= ?");
            argsList.add(qo.getCostMax());
        }
        //cpc  平均点击费用
        if (qo.getCpcMin() != null) {
            sb.append(" and ROUND(ifnull(sum(cost)/sum(clicks),0),2) >= ?");
            argsList.add(qo.getCpcMin());
        }
        if (qo.getCpcMax() != null) {
            sb.append(" and ROUND(ifnull(sum(cost)/sum(clicks),0),2) <= ?");
            argsList.add(qo.getCpcMax());
        }
        //广告订单量
        if (qo.getOrderNumMin() != null) {
            sb.append(" and ifnull(sum(conversions14d), 0) >= ?");
            argsList.add(qo.getOrderNumMin());
        }
        if (qo.getOrderNumMax() != null) {
            sb.append(" and ifnull(sum(conversions14d), 0) <= ?");
            argsList.add(qo.getOrderNumMax());
        }
        //广告销售额
        if (qo.getSalesMin() != null) {
            sb.append(" and ifnull(sum(sales14d), 0) >= ?");
            argsList.add(qo.getSalesMin());
        }
        if (qo.getSalesMax() != null) {
            sb.append(" and ifnull(sum(sales14d), 0) <= ?");
            argsList.add(qo.getSalesMax());
        }
        //订单转化率
        if (qo.getSalesConversionRateMin() != null) {
            sb.append(" and ROUND(ifnull(sum(conversions14d)/sum(clicks)*100,0),2) >= ?");
            argsList.add(qo.getSalesConversionRateMin());
        }
        if (qo.getSalesConversionRateMax() != null) {
            sb.append(" and ROUND(ifnull(sum(conversions14d)/sum(clicks)*100,0),2) <= ?");
            argsList.add(qo.getSalesConversionRateMax());
        }
        //acos
        if (qo.getAcosMin() != null) {
            sb.append(" and ROUND(ifnull(sum(cost)/sum(sales14d)*100,0),2) >= ?");
            argsList.add(qo.getAcosMin());
        }
        if (qo.getAcosMax() != null) {
            sb.append(" and ROUND(ifnull(sum(cost)/sum(sales14d)*100,0),2) <= ?");
            argsList.add(qo.getAcosMax());
        }
        // roas
        if (qo.getRoasMin() != null) {
            sb.append(" and ROUND(ifnull(sum(sales14d)/sum(cost),0),2) >= ?");
            argsList.add(qo.getRoasMin());
        }
        // roas
        if (qo.getRoasMax() != null) {
            sb.append(" and ROUND(ifnull(sum(sales14d)/sum(cost),0),2) <= ?");
            argsList.add(qo.getRoasMax());
        }
        // acots  需要乘以店铺销售额
        if (qo.getAcotsMin() != null) {
            if (shopSales.doubleValue() > 0) {
                sb.append(" and ROUND((ifnull(sum(cost),0) / ").append(shopSales).append("*100 ),2) >= ? ");
                argsList.add(qo.getAcotsMin());
            } else {
                sb.append(" and 0 >= ? ");
                argsList.add(qo.getAcotsMin());
            }
        }
        // acots  需要乘以店铺销售额
        if (qo.getAcotsMax() != null) {
            if (shopSales.doubleValue() > 0) {
                sb.append(" and ROUND((ifnull(sum(cost),0) / ").append(shopSales).append("*100 ),2) <= ? ");
                argsList.add(qo.getAcotsMax());
            } else {
                sb.append(" and 0 <= ? ");
                argsList.add(qo.getAcotsMax());
            }
        }
        // asots 需要乘以店铺销售额
        if (qo.getAsotsMin() != null) {
            if (shopSales.doubleValue() > 0) {
                sb.append(" and ROUND((ifnull(sum(sales14d),0) / ").append(shopSales).append("*100 ),2) >= ? ");
                argsList.add(qo.getAsotsMin());
            } else {
                sb.append(" and 0 >= ? ");
                argsList.add(qo.getAsotsMin());
            }
        }
        // asots  需要乘以店铺销售额
        if (qo.getAsotsMax() != null) {
            if (shopSales.doubleValue() > 0) {
                sb.append(" and ROUND((ifnull(sum(sales14d),0) / ").append(shopSales).append("*100 ),2) <= ? ");
                argsList.add(qo.getAsotsMax());
            } else {
                sb.append(" and 0 <= ? ");
                argsList.add(qo.getAsotsMax());
            }
        }
        //CPA
        if (qo.getCpaMin() != null) {
            sb.append(" and ROUND(ifnull(sum(cost)/sum(conversions14d),0), 2) >= ?");
            argsList.add(qo.getCpaMin());
        }
        if (qo.getCpaMax() != null) {
            sb.append(" and ROUND(ifnull(sum(cost)/sum(conversions14d),0), 2) <= ?");
            argsList.add(qo.getCpaMax());
        }

        //本广告产品订单量
        if (qo.getAdSaleNumMin() != null) {
            sb.append(" and ifnull(sum(conversions14d_same_sku), 0) >= ?");
            argsList.add(qo.getAdSaleNumMin());
        }
        if (qo.getAdSaleNumMax() != null) {
            sb.append(" and ifnull(sum(conversions14d_same_sku), 0) <= ?");
            argsList.add(qo.getAdSaleNumMax());
        }
        //其他产品广告订单量
        if (qo.getAdOtherOrderNumMin() != null) {
            sb.append(" and ifnull(sum(conversions14d) - sum(conversions14d_same_sku), 0) >= ?");
            argsList.add(qo.getAdOtherOrderNumMin());
        }
        if (qo.getAdOtherOrderNumMax() != null) {
            sb.append(" and ifnull(sum(conversions14d) - sum(conversions14d_same_sku), 0) <= ?");
            argsList.add(qo.getAdOtherOrderNumMax());
        }
        //本广告产品销售额
        if (qo.getAdSalesMin() != null) {
            sb.append(" and ifnull(sum(sales14d_same_sku), 0) >= ?");
            argsList.add(qo.getAdSalesMin());
        }
        if (qo.getAdSalesMax() != null) {
            sb.append(" and ifnull(sum(sales14d_same_sku), 0) <= ?");
            argsList.add(qo.getAdSalesMax());
        }
        //其他产品广告销售额
        if (qo.getAdOtherSalesMin() != null) {
            sb.append(" and ifnull(sum(sales14d) - sum(sales14d_same_sku), 0) >= ?");
            argsList.add(qo.getAdOtherSalesMin());
        }
        if (qo.getAdOtherSalesMax() != null) {
            sb.append(" and ifnull(sum(sales14d) - sum(sales14d_same_sku), 0) <= ?");
            argsList.add(qo.getAdOtherSalesMax());
        }
        //广告销量
        if (qo.getAdSalesTotalMin() != null) {
            sb.append(" and ifnull(sum(units_sold14d), 0) >= ?");
            argsList.add(qo.getAdSalesTotalMin());
        }
        if (qo.getAdSalesTotalMax() != null) {
            sb.append(" and ifnull(sum(units_sold14d), 0) <= ?");
            argsList.add(qo.getAdSalesTotalMax());
        }
        //品牌新买家订单转化率
        if (qo.getBrandNewBuyerOrderConversionRateMin() != null) {
            sb.append(" and ifnull(ROUND(sum(orders_new_to_brand14d)/sum(clicks)*100, 2), 0) >= ?");
            argsList.add(qo.getBrandNewBuyerOrderConversionRateMin());
        }
        if (qo.getBrandNewBuyerOrderConversionRateMax() != null) {
            sb.append(" and ifnull(ROUND(sum(orders_new_to_brand14d)/sum(clicks)*100, 2), 0) <= ?");
            argsList.add(qo.getBrandNewBuyerOrderConversionRateMax());
        }
        //品牌新买家订单量
        if (qo.getOrdersNewToBrandFTDMin() != null) {
            sb.append(" and ifnull(sum(orders_new_to_brand14d), 0) >= ?");
            argsList.add(qo.getOrdersNewToBrandFTDMin());
        }
        if (qo.getOrdersNewToBrandFTDMax() != null) {
            sb.append(" and ifnull(sum(orders_new_to_brand14d), 0) <= ?");
            argsList.add(qo.getOrdersNewToBrandFTDMax());
        }
        //品牌新买家订单量百分比
        if (qo.getOrderRateNewToBrandFTDMin() != null) {
            sb.append(" and ifnull(ROUND(sum(orders_new_to_brand14d)/sum(conversions14d)*100, 2), 0) >= ?");
            argsList.add(qo.getOrderRateNewToBrandFTDMin());
        }
        if (qo.getOrderRateNewToBrandFTDMax() != null) {
            sb.append(" and ifnull(ROUND(sum(orders_new_to_brand14d)/sum(conversions14d)*100, 2), 0) <= ?");
            argsList.add(qo.getOrderRateNewToBrandFTDMax());
        }
        //品牌新买家销售额
        if (qo.getSalesNewToBrandFTDMin() != null) {
            sb.append(" and ifnull(sum(sales_new_to_brand14d), 0) >= ?");
            argsList.add(qo.getSalesNewToBrandFTDMin());
        }
        if (qo.getSalesNewToBrandFTDMax() != null) {
            sb.append(" and ifnull(sum(sales_new_to_brand14d), 0) <= ?");
            argsList.add(qo.getSalesNewToBrandFTDMax());
        }
        //品牌新买家销售额百分比
        if (qo.getSalesRateNewToBrandFTDMin() != null) {
            sb.append(" and ifnull(ROUND(sum(sales_new_to_brand14d)/sum(sales14d)*100, 2), 0) >= ?");
            argsList.add(qo.getSalesRateNewToBrandFTDMin());
        }
        if (qo.getSalesRateNewToBrandFTDMax() != null) {
            sb.append(" and ifnull(ROUND(sum(sales_new_to_brand14d)/sum(sales14d)*100, 2), 0) <= ?");
            argsList.add(qo.getSalesRateNewToBrandFTDMax());
        }
//        //本广告产品销量
//        if (qo.getAdSelfSaleNumMin() != null) {
//            sb.append(" and ifnull(sum(ad_order_num), 0) >= ?");
//            argsList.add(qo.getAdSelfSaleNumMin());
//        }
//        if (qo.getAdSelfSaleNumMax() != null) {
//            sb.append(" and ifnull(sum(ad_order_num), 0) <= ?");
//            argsList.add(qo.getAdSelfSaleNumMax());
//        }
//        //其他产品广告销量
//        if (qo.getAdOtherSaleNumMin() != null) {
//            sb.append(" and ifnull(sum(units_sold14d) - sum(ad_order_num), 0) >= ?");
//            argsList.add(qo.getAdOtherSaleNumMin());
//        }
//        if (qo.getAdOtherSaleNumMax() != null) {
//            sb.append(" and ifnull(sum(units_sold14d) - sum(ad_order_num), 0) <= ?");
//            argsList.add(qo.getAdOtherSaleNumMax());
//        }

        // 5秒观看次数 筛选
        if (qo.getVideo5SecondViewsMin() != null ) {
            sb.append(" and ifnull(sum(video5second_views) , 0) >= ? ");
            argsList.add(qo.getVideo5SecondViewsMin());
        }
        if (qo.getVideo5SecondViewsMax() != null) {
            sb.append(" and ifnull(sum(video5second_views) , 0) <= ? ");
            argsList.add(qo.getVideo5SecondViewsMax());
        }

        // 视频完整播放次数 筛选
        if (qo.getVideoCompleteViewsMin() != null ) {
            sb.append(" and ifnull(sum(video_complete_views) , 0) >= ? ");
            argsList.add(qo.getVideoCompleteViewsMin());
        }
        if (qo.getVideoCompleteViewsMax() != null ) {
            sb.append(" and ifnull(sum(video_complete_views) , 0) <= ? ");
            argsList.add(qo.getVideoCompleteViewsMax());
        }

        //可见展示次数
        if (qo.getViewImpressionsMin() != null) {
            sb.append(" and sum(viewable_impressions) >= ?");
            argsList.add(qo.getViewImpressionsMin());
        }
        if (qo.getViewImpressionsMax() != null) {
            sb.append(" and sum(viewable_impressions) <= ?");
            argsList.add(qo.getViewImpressionsMax());
        }

        // 观看率 筛选
        if (qo.getViewabilityRateMin() != null) {
            sb.append(" and ROUND(ifnull((sum(viewable_impressions)/sum(impressions)) * 100, 0), 2) >= ? ");
            argsList.add(qo.getViewabilityRateMin());
        }
        if (qo.getViewabilityRateMax() != null) {
            sb.append(" and ROUND(ifnull((sum(viewable_impressions)/sum(impressions)) * 100, 0), 2) <= ? ");
            argsList.add(qo.getViewabilityRateMax());
        }

        // 观看点击率 筛选
        if (qo.getViewClickThroughRateMin() != null) {
            sb.append(" and ROUND(ifnull((sum(clicks)/sum(viewable_impressions)) * 100, 0), 2) >= ? ");
            argsList.add(qo.getViewClickThroughRateMin());
        }
        if (qo.getViewClickThroughRateMax() != null) {
            sb.append(" and ROUND(ifnull((sum(clicks)/sum(viewable_impressions)) * 100, 0), 2) <= ? ");
            argsList.add(qo.getViewClickThroughRateMax());
        }

        // 品牌搜索次数 筛选
        if (qo.getBrandedSearchesMin() != null) {
            sb.append(" and ifnull(sum(branded_searches14d) , 0) >= ? ");
            argsList.add(qo.getBrandedSearchesMin()); }
        if (qo.getBrandedSearchesMax() != null) {
            sb.append(" and ifnull(sum(branded_searches14d) , 0) <= ? ");
            argsList.add(qo.getBrandedSearchesMax()); }

        // 广告笔单价 筛选
        if (qo.getAdvertisingUnitPriceMin() != null) {
            sb.append(" and ROUND(ifnull(sum(sales14d)/sum(conversions14d), 0), 2) >= ?");
            argsList.add(qo.getAdvertisingUnitPriceMin());
        }
        if (qo.getAdvertisingUnitPriceMax() != null) {
            sb.append(" and ROUND(ifnull(sum(sales14d)/sum(conversions14d), 0), 2) <= ?");
            argsList.add(qo.getAdvertisingUnitPriceMax());
        }

        // 搜索结果首页首位IS
        if (qo.getTopImpressionShareMin() != null){
            sb.append(" and max(top_of_search_is) >= ?");
            argsList.add(qo.getTopImpressionShareMin());
        }
        if (qo.getTopImpressionShareMax() != null){
            sb.append(" and min(top_of_search_is) <= ?");
            argsList.add(qo.getTopImpressionShareMax());
        }

        return sb.toString();
    }

    public static String getSpTargetPageHavingSql(ReportAdvancedFilterBaseQo qo, List<Object> argsList) {
        StringBuilder sb = new StringBuilder();
        BigDecimal shopSales = qo.getShopSales() != null ? qo.getShopSales() : BigDecimal.valueOf(0);
        sb.append(" having 1=1 ");
        //展示量
        if (qo.getImpressionsMin() != null) {
            sb.append(" and ifnull(sum(impressions), 0) >= ?");
            argsList.add(qo.getImpressionsMin());
        }
        if (qo.getImpressionsMax() != null) {
            sb.append(" and ifnull(sum(impressions), 0) <= ?");
            argsList.add(qo.getImpressionsMax());
        }
        //点击量
        if (qo.getClicksMin() != null) {
            sb.append(" and ifnull(sum(clicks), 0) >= ?");
            argsList.add(qo.getClicksMin());
        }
        if (qo.getClicksMax() != null) {
            sb.append(" and ifnull(sum(clicks), 0) <= ?");
            argsList.add(qo.getClicksMax());
        }
        //点击率（clicks/impressions）
        if (qo.getClickRateMin() != null) {
            sb.append(" and ROUND(ifnull(sum(clicks)/sum(impressions)*100,0),2) >= ?");
            argsList.add(qo.getClickRateMin());
        }
        if (qo.getClickRateMax() != null) {
            sb.append(" and ROUND(ifnull(sum(clicks)/sum(impressions)*100,0),2) <= ?");
            argsList.add(qo.getClickRateMax());
        }
        //花费
        if (qo.getCostMin() != null) {
            sb.append(" and ifnull(sum(cost), 0) >= ?");
            argsList.add(qo.getCostMin());
        }
        if (qo.getCostMax() != null) {
            sb.append(" and ifnull(sum(cost), 0) <= ?");
            argsList.add(qo.getCostMax());
        }
        //cpc  平均点击费用
        if (qo.getCpcMin() != null) {
            sb.append(" and ROUND(ifnull(sum(cost)/sum(clicks),0),2) >= ?");
            argsList.add(qo.getCpcMin());
        }
        if (qo.getCpcMax() != null) {
            sb.append(" and ROUND(ifnull(sum(cost)/sum(clicks),0),2) <= ?");
            argsList.add(qo.getCpcMax());
        }
        //广告订单量
        if (qo.getOrderNumMin() != null) {
            sb.append(" and ifnull(sum(sale_num), 0) >= ?");
            argsList.add(qo.getOrderNumMin());
        }
        if (qo.getOrderNumMax() != null) {
            sb.append(" and ifnull(sum(sale_num), 0) <= ?");
            argsList.add(qo.getOrderNumMax());
        }
        //广告销售额
        if (qo.getSalesMin() != null) {
            sb.append(" and ifnull(sum(total_sales), 0) >= ?");
            argsList.add(qo.getSalesMin());
        }
        if (qo.getSalesMax() != null) {
            sb.append(" and ifnull(sum(total_sales), 0) <= ?");
            argsList.add(qo.getSalesMax());
        }
        //订单转化率
        if (qo.getSalesConversionRateMin() != null) {
            sb.append(" and ROUND(ifnull(sum(sale_num)/sum(clicks)*100,0),2) >= ?");
            argsList.add(qo.getSalesConversionRateMin());
        }
        if (qo.getSalesConversionRateMax() != null) {
            sb.append(" and ROUND(ifnull(sum(sale_num)/sum(clicks)*100,0),2) <= ?");
            argsList.add(qo.getSalesConversionRateMax());
        }
        //acos
        if (qo.getAcosMin() != null) {
            sb.append(" and ROUND(ifnull(sum(cost)/sum(total_sales)*100,0),2) >= ?");
            argsList.add(qo.getAcosMin());
        }
        if (qo.getAcosMax() != null) {
            sb.append(" and ROUND(ifnull(sum(cost)/sum(total_sales)*100,0),2) <= ?");
            argsList.add(qo.getAcosMax());
        }
        // roas
        if (qo.getRoasMin() != null) {
            sb.append(" and ROUND(ifnull(sum(total_sales)/sum(cost),0),2) >= ?");
            argsList.add(qo.getRoasMin());
        }
        // roas
        if (qo.getRoasMax() != null) {
            sb.append(" and ROUND(ifnull(sum(total_sales)/sum(cost),0),2) <= ?");
            argsList.add(qo.getRoasMax());
        }
        // acots  需要乘以店铺销售额
        if (qo.getAcotsMin() != null) {
            if (shopSales.doubleValue() > 0) {
                sb.append(" and ROUND((ifnull(sum(cost),0) / ").append(shopSales).append("*100 ),2) >= ? ");
                argsList.add(qo.getAcotsMin());
            } else {
                sb.append(" and 0 >= ? ");
                argsList.add(qo.getAcotsMin());
            }
        }
        // acots  需要乘以店铺销售额
        if (qo.getAcotsMax() != null) {
            if (shopSales.doubleValue() > 0) {
                sb.append(" and ROUND((ifnull(sum(cost),0) / ").append(shopSales).append("*100 ),2) <= ? ");
                argsList.add(qo.getAcotsMax());
            } else {
                sb.append(" and 0 <= ? ");
                argsList.add(qo.getAcotsMax());
            }
        }
        // asots 需要乘以店铺销售额
        if (qo.getAsotsMin() != null) {
            if (shopSales.doubleValue() > 0) {
                sb.append(" and ROUND((ifnull(sum(total_sales),0) / ").append(shopSales).append("*100 ),2) >= ? ");
                argsList.add(qo.getAsotsMin());
            } else {
                sb.append(" and 0 >= ? ");
                argsList.add(qo.getAsotsMin());
            }
        }
        // asots  需要乘以店铺销售额
        if (qo.getAsotsMax() != null) {
            if (shopSales.doubleValue() > 0) {
                sb.append(" and ROUND((ifnull(sum(total_sales),0) / ").append(shopSales).append("*100 ),2) <= ? ");
                argsList.add(qo.getAsotsMax());
            } else {
                sb.append(" and 0 <= ? ");
                argsList.add(qo.getAsotsMax());
            }
        }
        //CPA
        if (qo.getCpaMin() != null) {
            sb.append(" and ROUND(ifnull(sum(cost)/sum(sale_num),0), 2) >= ?");
            argsList.add(qo.getCpaMin());
        }
        if (qo.getCpaMax() != null) {
            sb.append(" and ROUND(ifnull(sum(cost)/sum(sale_num),0), 2) <= ?");
            argsList.add(qo.getCpaMax());
        }

        //本广告产品订单量
        if (qo.getAdSaleNumMin() != null) {
            sb.append(" and ifnull(sum(ad_sale_num), 0) >= ?");
            argsList.add(qo.getAdSaleNumMin());
        }
        if (qo.getAdSaleNumMax() != null) {
            sb.append(" and ifnull(sum(ad_sale_num), 0) <= ?");
            argsList.add(qo.getAdSaleNumMax());
        }
        //其他产品广告订单量
        if (qo.getAdOtherOrderNumMin() != null) {
            sb.append(" and ifnull(sum(sale_num) - sum(ad_sale_num), 0) >= ?");
            argsList.add(qo.getAdOtherOrderNumMin());
        }
        if (qo.getAdOtherOrderNumMax() != null) {
            sb.append(" and ifnull(sum(sale_num) - sum(ad_sale_num), 0) <= ?");
            argsList.add(qo.getAdOtherOrderNumMax());
        }
        //本广告产品销售额
        if (qo.getAdSalesMin() != null) {
            sb.append(" and ifnull(sum(ad_sales), 0) >= ?");
            argsList.add(qo.getAdSalesMin());
        }
        if (qo.getAdSalesMax() != null) {
            sb.append(" and ifnull(sum(ad_sales), 0) <= ?");
            argsList.add(qo.getAdSalesMax());
        }
        //其他产品广告销售额
        if (qo.getAdOtherSalesMin() != null) {
            sb.append(" and ifnull(sum(total_sales) - sum(ad_sales), 0) >= ?");
            argsList.add(qo.getAdOtherSalesMin());
        }
        if (qo.getAdOtherSalesMax() != null) {
            sb.append(" and ifnull(sum(total_sales) - sum(ad_sales), 0) <= ?");
            argsList.add(qo.getAdOtherSalesMax());
        }
        //广告销量
        if (qo.getAdSalesTotalMin() != null) {
            sb.append(" and ifnull(sum(order_num), 0) >= ?");
            argsList.add(qo.getAdSalesTotalMin());
        }
        if (qo.getAdSalesTotalMax() != null) {
            sb.append(" and ifnull(sum(order_num), 0) <= ?");
            argsList.add(qo.getAdSalesTotalMax());
        }
        //本广告产品销量
        if (qo.getAdSelfSaleNumMin() != null) {
            sb.append(" and ifnull(sum(ad_order_num), 0) >= ?");
            argsList.add(qo.getAdSelfSaleNumMin());
        }
        if (qo.getAdSelfSaleNumMax() != null) {
            sb.append(" and ifnull(sum(ad_order_num), 0) <= ?");
            argsList.add(qo.getAdSelfSaleNumMax());
        }
        //其他产品广告销量
        if (qo.getAdOtherSaleNumMin() != null) {
            sb.append(" and ifnull(sum(order_num) - sum(ad_order_num), 0) >= ?");
            argsList.add(qo.getAdOtherSaleNumMin());
        }
        if (qo.getAdOtherSaleNumMax() != null) {
            sb.append(" and ifnull(sum(order_num) - sum(ad_order_num), 0) <= ?");
            argsList.add(qo.getAdOtherSaleNumMax());
        }

        // 广告笔单价 筛选
        if (qo.getAdvertisingUnitPriceMin() != null) {
            sb.append(" and ROUND(ifnull(sum(total_sales)/sum(sale_num), 0), 2) >= ?");
            argsList.add(qo.getAdvertisingUnitPriceMin());
        }
        if (qo.getAdvertisingUnitPriceMax() != null) {
            sb.append(" and ROUND(ifnull(sum(total_sales)/sum(sale_num), 0), 2) <= ?");
            argsList.add(qo.getAdvertisingUnitPriceMax());
        }

        // 搜索结果首页首位IS
        if (qo.getTopImpressionShareMin() != null){
            sb.append(" and max(top_of_search_is) >= ?");
            argsList.add(qo.getTopImpressionShareMin());
        }
        if (qo.getTopImpressionShareMax() != null){
            sb.append(" and min(top_of_search_is) <= ?");
            argsList.add(qo.getTopImpressionShareMax());
        }

        return sb.toString();
    }

    public static String getSbTargetPageHavingSql(ReportAdvancedFilterBaseQo qo, List<Object> argsList) {
        StringBuilder sb = new StringBuilder();
        BigDecimal shopSales = qo.getShopSales() != null ? qo.getShopSales() : BigDecimal.valueOf(0);
        sb.append(" having 1=1 ");
        //展示量
        if (qo.getImpressionsMin() != null) {
            sb.append(" and ifnull(sum(impressions), 0) >= ?");
            argsList.add(qo.getImpressionsMin());
        }
        if (qo.getImpressionsMax() != null) {
            sb.append(" and ifnull(sum(impressions), 0) <= ?");
            argsList.add(qo.getImpressionsMax());
        }
        //点击量
        if (qo.getClicksMin() != null) {
            sb.append(" and ifnull(sum(clicks), 0) >= ?");
            argsList.add(qo.getClicksMin());
        }
        if (qo.getClicksMax() != null) {
            sb.append(" and ifnull(sum(clicks), 0) <= ?");
            argsList.add(qo.getClicksMax());
        }
        //点击率（clicks/impressions）
        if (qo.getClickRateMin() != null) {
            sb.append(" and ROUND(ifnull(sum(clicks)/sum(impressions)*100,0),2) >= ?");
            argsList.add(qo.getClickRateMin());
        }
        if (qo.getClickRateMax() != null) {
            sb.append(" and ROUND(ifnull(sum(clicks)/sum(impressions)*100,0),2) <= ?");
            argsList.add(qo.getClickRateMax());
        }
        //花费
        if (qo.getCostMin() != null) {
            sb.append(" and ifnull(sum(cost), 0) >= ?");
            argsList.add(qo.getCostMin());
        }
        if (qo.getCostMax() != null) {
            sb.append(" and ifnull(sum(cost), 0) <= ?");
            argsList.add(qo.getCostMax());
        }
        //cpc  平均点击费用
        if (qo.getCpcMin() != null) {
            sb.append(" and ROUND(ifnull(sum(cost)/sum(clicks),0),2) >= ?");
            argsList.add(qo.getCpcMin());
        }
        if (qo.getCpcMax() != null) {
            sb.append(" and ROUND(ifnull(sum(cost)/sum(clicks),0),2) <= ?");
            argsList.add(qo.getCpcMax());
        }
        //广告订单量
        if (qo.getOrderNumMin() != null) {
            sb.append(" and ifnull(sum(conversions14d), 0) >= ?");
            argsList.add(qo.getOrderNumMin());
        }
        if (qo.getOrderNumMax() != null) {
            sb.append(" and ifnull(sum(conversions14d), 0) <= ?");
            argsList.add(qo.getOrderNumMax());
        }
        //广告销售额
        if (qo.getSalesMin() != null) {
            sb.append(" and ifnull(sum(sales14d), 0) >= ?");
            argsList.add(qo.getSalesMin());
        }
        if (qo.getSalesMax() != null) {
            sb.append(" and ifnull(sum(sales14d), 0) <= ?");
            argsList.add(qo.getSalesMax());
        }
        //订单转化率
        if (qo.getSalesConversionRateMin() != null) {
            sb.append(" and ROUND(ifnull(sum(conversions14d)/sum(clicks)*100,0),2) >= ?");
            argsList.add(qo.getSalesConversionRateMin());
        }
        if (qo.getSalesConversionRateMax() != null) {
            sb.append(" and ROUND(ifnull(sum(conversions14d)/sum(clicks)*100,0),2) <= ?");
            argsList.add(qo.getSalesConversionRateMax());
        }
        //acos
        if (qo.getAcosMin() != null) {
            sb.append(" and ROUND(ifnull(sum(cost)/sum(sales14d)*100,0),2) >= ?");
            argsList.add(qo.getAcosMin());
        }
        if (qo.getAcosMax() != null) {
            sb.append(" and ROUND(ifnull(sum(cost)/sum(sales14d)*100,0),2) <= ?");
            argsList.add(qo.getAcosMax());
        }
        // roas
        if (qo.getRoasMin() != null) {
            sb.append(" and ROUND(ifnull(sum(sales14d)/sum(cost),0),2) >= ?");
            argsList.add(qo.getRoasMin());
        }
        // roas
        if (qo.getRoasMax() != null) {
            sb.append(" and ROUND(ifnull(sum(sales14d)/sum(cost),0),2) <= ?");
            argsList.add(qo.getRoasMax());
        }
        // acots  需要乘以店铺销售额
        if (qo.getAcotsMin() != null) {
            if (shopSales.doubleValue() > 0) {
                sb.append(" and ROUND((ifnull(sum(cost),0) / ").append(shopSales).append("*100 ),2) >= ? ");
                argsList.add(qo.getAcotsMin());
            } else {
                sb.append(" and 0 >= ? ");
                argsList.add(qo.getAcotsMin());
            }
        }
        // acots  需要乘以店铺销售额
        if (qo.getAcotsMax() != null) {
            if (shopSales.doubleValue() > 0) {
                sb.append(" and ROUND((ifnull(sum(cost),0) / ").append(shopSales).append("*100 ),2) <= ? ");
                argsList.add(qo.getAcotsMax());
            } else {
                sb.append(" and 0 <= ? ");
                argsList.add(qo.getAcotsMax());
            }
        }
        // asots 需要乘以店铺销售额
        if (qo.getAsotsMin() != null) {
            if (shopSales.doubleValue() > 0) {
                sb.append(" and ROUND((ifnull(sum(sales14d),0) / ").append(shopSales).append("*100 ),2) >= ? ");
                argsList.add(qo.getAsotsMin());
            } else {
                sb.append(" and 0 >= ? ");
                argsList.add(qo.getAsotsMin());
            }
        }
        // asots  需要乘以店铺销售额
        if (qo.getAsotsMax() != null) {
            if (shopSales.doubleValue() > 0) {
                sb.append(" and ROUND((ifnull(sum(sales14d),0) / ").append(shopSales).append("*100 ),2) <= ? ");
                argsList.add(qo.getAsotsMax());
            } else {
                sb.append(" and 0 <= ? ");
                argsList.add(qo.getAsotsMax());
            }
        }
        //CPA
        if (qo.getCpaMin() != null) {
            sb.append(" and ROUND(ifnull(sum(cost)/sum(conversions14d),0), 2) >= ?");
            argsList.add(qo.getCpaMin());
        }
        if (qo.getCpaMax() != null) {
            sb.append(" and ROUND(ifnull(sum(cost)/sum(conversions14d),0), 2) <= ?");
            argsList.add(qo.getCpaMax());
        }

        //本广告产品订单量
        if (qo.getAdSaleNumMin() != null) {
            sb.append(" and ifnull(sum(conversions14d_same_sku), 0) >= ?");
            argsList.add(qo.getAdSaleNumMin());
        }
        if (qo.getAdSaleNumMax() != null) {
            sb.append(" and ifnull(sum(conversions14d_same_sku), 0) <= ?");
            argsList.add(qo.getAdSaleNumMax());
        }
        //其他产品广告订单量
        if (qo.getAdOtherOrderNumMin() != null) {
            sb.append(" and ifnull(sum(conversions14d) - sum(conversions14d_same_sku), 0) >= ?");
            argsList.add(qo.getAdOtherOrderNumMin());
        }
        if (qo.getAdOtherOrderNumMax() != null) {
            sb.append(" and ifnull(sum(conversions14d) - sum(conversions14d_same_sku), 0) <= ?");
            argsList.add(qo.getAdOtherOrderNumMax());
        }
        //本广告产品销售额
        if (qo.getAdSalesMin() != null) {
            sb.append(" and ifnull(sum(sales14d_same_sku), 0) >= ?");
            argsList.add(qo.getAdSalesMin());
        }
        if (qo.getAdSalesMax() != null) {
            sb.append(" and ifnull(sum(sales14d_same_sku), 0) <= ?");
            argsList.add(qo.getAdSalesMax());
        }
        //其他产品广告销售额
        if (qo.getAdOtherSalesMin() != null) {
            sb.append(" and ifnull(sum(sales14d) - sum(sales14d_same_sku), 0) >= ?");
            argsList.add(qo.getAdOtherSalesMin());
        }
        if (qo.getAdOtherSalesMax() != null) {
            sb.append(" and ifnull(sum(sales14d) - sum(sales14d_same_sku), 0) <= ?");
            argsList.add(qo.getAdOtherSalesMax());
        }
        //广告销量
        if (qo.getAdSalesTotalMin() != null) {
            sb.append(" and ifnull(sum(units_sold14d), 0) >= ?");
            argsList.add(qo.getAdSalesTotalMin());
        }
        if (qo.getAdSalesTotalMax() != null) {
            sb.append(" and ifnull(sum(units_sold14d), 0) <= ?");
            argsList.add(qo.getAdSalesTotalMax());
        }
        //品牌新买家订单转化率
        if (qo.getBrandNewBuyerOrderConversionRateMin() != null) {
            sb.append(" and ifnull(ROUND(sum(orders_new_to_brand14d)/sum(clicks)*100, 2), 0) >= ?");
            argsList.add(qo.getBrandNewBuyerOrderConversionRateMin());
        }
        if (qo.getBrandNewBuyerOrderConversionRateMax() != null) {
            sb.append(" and ifnull(ROUND(sum(orders_new_to_brand14d)/sum(clicks)*100, 2), 0) <= ?");
            argsList.add(qo.getBrandNewBuyerOrderConversionRateMax());
        }
        //品牌新买家订单量
        if (qo.getOrdersNewToBrandFTDMin() != null) {
            sb.append(" and ifnull(sum(orders_new_to_brand14d), 0) >= ?");
            argsList.add(qo.getOrdersNewToBrandFTDMin());
        }
        if (qo.getOrdersNewToBrandFTDMax() != null) {
            sb.append(" and ifnull(sum(orders_new_to_brand14d), 0) <= ?");
            argsList.add(qo.getOrdersNewToBrandFTDMax());
        }
        //品牌新买家订单量百分比
        if (qo.getOrderRateNewToBrandFTDMin() != null) {
            sb.append(" and ifnull(ROUND(sum(orders_new_to_brand14d)/sum(conversions14d)*100, 2), 0) >= ?");
            argsList.add(qo.getOrderRateNewToBrandFTDMin());
        }
        if (qo.getOrderRateNewToBrandFTDMax() != null) {
            sb.append(" and ifnull(ROUND(sum(orders_new_to_brand14d)/sum(conversions14d)*100, 2), 0) <= ?");
            argsList.add(qo.getOrderRateNewToBrandFTDMax());
        }
        //品牌新买家销售额
        if (qo.getSalesNewToBrandFTDMin() != null) {
            sb.append(" and ifnull(sum(sales_new_to_brand14d), 0) >= ?");
            argsList.add(qo.getSalesNewToBrandFTDMin());
        }
        if (qo.getSalesNewToBrandFTDMax() != null) {
            sb.append(" and ifnull(sum(sales_new_to_brand14d), 0) <= ?");
            argsList.add(qo.getSalesNewToBrandFTDMax());
        }
        //品牌新买家销售额百分比
        if (qo.getSalesRateNewToBrandFTDMin() != null) {
            sb.append(" and ifnull(ROUND(sum(sales_new_to_brand14d)/sum(sales14d)*100, 2), 0) >= ?");
            argsList.add(qo.getSalesRateNewToBrandFTDMin());
        }
        if (qo.getSalesRateNewToBrandFTDMax() != null) {
            sb.append(" and ifnull(ROUND(sum(sales_new_to_brand14d)/sum(sales14d)*100, 2), 0) <= ?");
            argsList.add(qo.getSalesRateNewToBrandFTDMax());
        }
//        //本广告产品销量
//        if (qo.getAdSelfSaleNumMin() != null) {
//            sb.append(" and ifnull(sum(ad_order_num), 0) >= ?");
//            argsList.add(qo.getAdSelfSaleNumMin());
//        }
//        if (qo.getAdSelfSaleNumMax() != null) {
//            sb.append(" and ifnull(sum(ad_order_num), 0) <= ?");
//            argsList.add(qo.getAdSelfSaleNumMax());
//        }
//        //其他产品广告销量
//        if (qo.getAdOtherSaleNumMin() != null) {
//            sb.append(" and ifnull(sum(units_sold14d) - sum(ad_order_num), 0) >= ?");
//            argsList.add(qo.getAdOtherSaleNumMin());
//        }
//        if (qo.getAdOtherSaleNumMax() != null) {
//            sb.append(" and ifnull(sum(units_sold14d) - sum(ad_order_num), 0) <= ?");
//            argsList.add(qo.getAdOtherSaleNumMax());
//        }

        // 5秒观看次数 筛选
        if (qo.getVideo5SecondViewsMin() != null ) {
            sb.append(" and ifnull(sum(video5second_views) , 0) >= ? ");
            argsList.add(qo.getVideo5SecondViewsMin());
        }
        if (qo.getVideo5SecondViewsMax() != null) {
            sb.append(" and ifnull(sum(video5second_views) , 0) <= ? ");
            argsList.add(qo.getVideo5SecondViewsMax());
        }

        // 视频完整播放次数 筛选
        if (qo.getVideoCompleteViewsMin() != null ) {
            sb.append(" and ifnull(sum(video_complete_views) , 0) >= ? ");
            argsList.add(qo.getVideoCompleteViewsMin());
        }
        if (qo.getVideoCompleteViewsMax() != null ) {
            sb.append(" and ifnull(sum(video_complete_views) , 0) <= ? ");
            argsList.add(qo.getVideoCompleteViewsMax());
        }

        //可见展示次数
        if (qo.getViewImpressionsMin() != null) {
            sb.append(" and sum(viewable_impressions) >= ?");
            argsList.add(qo.getViewImpressionsMin());
        }
        if (qo.getViewImpressionsMax() != null) {
            sb.append(" and sum(viewable_impressions) <= ?");
            argsList.add(qo.getViewImpressionsMax());
        }

        // 观看率 筛选
        if (qo.getViewabilityRateMin() != null) {
            sb.append(" and ROUND(ifnull((sum(viewable_impressions)/sum(impressions)) * 100, 0), 2) >= ? ");
            argsList.add(qo.getViewabilityRateMin());
        }
        if (qo.getViewabilityRateMax() != null) {
            sb.append(" and ROUND(ifnull((sum(viewable_impressions)/sum(impressions)) * 100, 0), 2) <= ? ");
            argsList.add(qo.getViewabilityRateMax());
        }

        // 观看点击率 筛选
        if (qo.getViewClickThroughRateMin() != null) {
            sb.append(" and ROUND(ifnull((sum(clicks)/sum(viewable_impressions)) * 100, 0), 2) >= ? ");
            argsList.add(qo.getViewClickThroughRateMin());
        }
        if (qo.getViewClickThroughRateMax() != null) {
            sb.append(" and ROUND(ifnull((sum(clicks)/sum(viewable_impressions)) * 100, 0), 2) <= ? ");
            argsList.add(qo.getViewClickThroughRateMax());
        }

        // 品牌搜索次数 筛选
        if (qo.getBrandedSearchesMin() != null) {
            sb.append(" and ifnull(sum(branded_searches14d) , 0) >= ? ");
            argsList.add(qo.getBrandedSearchesMin()); }
        if (qo.getBrandedSearchesMax() != null) {
            sb.append(" and ifnull(sum(branded_searches14d) , 0) <= ? ");
            argsList.add(qo.getBrandedSearchesMax()); }

        // 广告笔单价 筛选
        if (qo.getAdvertisingUnitPriceMin() != null) {
            sb.append(" and ROUND(ifnull(sum(sales14d)/sum(conversions14d), 0), 2) >= ?");
            argsList.add(qo.getAdvertisingUnitPriceMin());
        }
        if (qo.getAdvertisingUnitPriceMax() != null) {
            sb.append(" and ROUND(ifnull(sum(sales14d)/sum(conversions14d), 0), 2) <= ?");
            argsList.add(qo.getAdvertisingUnitPriceMax());
        }

        // 搜索结果首页首位IS
        if (qo.getTopImpressionShareMin() != null){
            sb.append(" and max(top_of_search_is) >= ?");
            argsList.add(qo.getTopImpressionShareMin());
        }
        if (qo.getTopImpressionShareMax() != null){
            sb.append(" and min(top_of_search_is) <= ?");
            argsList.add(qo.getTopImpressionShareMax());
        }

        return sb.toString();
    }

    public static String getSdTargetPageHavingSql(ReportAdvancedFilterBaseQo qo, List<Object> argsList) {
        StringBuilder sb = new StringBuilder();
        BigDecimal shopSales = qo.getShopSales() != null ? qo.getShopSales() : BigDecimal.valueOf(0);
        sb.append(" having 1=1 ");
        //展示量
        if (qo.getImpressionsMin() != null) {
            sb.append(" and ifnull(sum(impressions), 0) >= ?");
            argsList.add(qo.getImpressionsMin());
        }
        if (qo.getImpressionsMax() != null) {
            sb.append(" and ifnull(sum(impressions), 0) <= ?");
            argsList.add(qo.getImpressionsMax());
        }
        //点击量
        if (qo.getClicksMin() != null) {
            sb.append(" and ifnull(sum(clicks), 0) >= ?");
            argsList.add(qo.getClicksMin());
        }
        if (qo.getClicksMax() != null) {
            sb.append(" and ifnull(sum(clicks), 0) <= ?");
            argsList.add(qo.getClicksMax());
        }
        //点击率（clicks/impressions）
        if (qo.getClickRateMin() != null) {
            sb.append(" and ROUND(ifnull(sum(clicks)/sum(impressions)*100,0),2) >= ?");
            argsList.add(qo.getClickRateMin());
        }
        if (qo.getClickRateMax() != null) {
            sb.append(" and ROUND(ifnull(sum(clicks)/sum(impressions)*100,0),2) <= ?");
            argsList.add(qo.getClickRateMax());
        }
        //花费
        if (qo.getCostMin() != null) {
            sb.append(" and ifnull(sum(cost), 0) >= ?");
            argsList.add(qo.getCostMin());
        }
        if (qo.getCostMax() != null) {
            sb.append(" and ifnull(sum(cost), 0) <= ?");
            argsList.add(qo.getCostMax());
        }
        //cpc  平均点击费用
        if (qo.getCpcMin() != null) {
            sb.append(" and ROUND(ifnull(sum(cost)/sum(clicks),0),2) >= ?");
            argsList.add(qo.getCpcMin());
        }
        if (qo.getCpcMax() != null) {
            sb.append(" and ROUND(ifnull(sum(cost)/sum(clicks),0),2) <= ?");
            argsList.add(qo.getCpcMax());
        }
        //广告订单量
        if (qo.getOrderNumMin() != null) {
            sb.append(" and ifnull(sum(conversions14d), 0) >= ?");
            argsList.add(qo.getOrderNumMin());
        }
        if (qo.getOrderNumMax() != null) {
            sb.append(" and ifnull(sum(conversions14d), 0) <= ?");
            argsList.add(qo.getOrderNumMax());
        }
        //广告销售额
        if (qo.getSalesMin() != null) {
            sb.append(" and ifnull(sum(sales14d), 0) >= ?");
            argsList.add(qo.getSalesMin());
        }
        if (qo.getSalesMax() != null) {
            sb.append(" and ifnull(sum(sales14d), 0) <= ?");
            argsList.add(qo.getSalesMax());
        }
        //订单转化率
        if (qo.getSalesConversionRateMin() != null) {
            sb.append(" and ROUND(ifnull(sum(conversions14d)/sum(clicks)*100,0),2) >= ?");
            argsList.add(qo.getSalesConversionRateMin());
        }
        if (qo.getSalesConversionRateMax() != null) {
            sb.append(" and ROUND(ifnull(sum(conversions14d)/sum(clicks)*100,0),2) <= ?");
            argsList.add(qo.getSalesConversionRateMax());
        }
        //acos
        if (qo.getAcosMin() != null) {
            sb.append(" and ROUND(ifnull(sum(cost)/sum(sales14d)*100,0),2) >= ?");
            argsList.add(qo.getAcosMin());
        }
        if (qo.getAcosMax() != null) {
            sb.append(" and ROUND(ifnull(sum(cost)/sum(sales14d)*100,0),2) <= ?");
            argsList.add(qo.getAcosMax());
        }
        // roas
        if (qo.getRoasMin() != null) {
            sb.append(" and ROUND(ifnull(sum(sales14d)/sum(cost),0),2) >= ?");
            argsList.add(qo.getRoasMin());
        }
        // roas
        if (qo.getRoasMax() != null) {
            sb.append(" and ROUND(ifnull(sum(sales14d)/sum(cost),0),2) <= ?");
            argsList.add(qo.getRoasMax());
        }
        // acots  需要乘以店铺销售额
        if (qo.getAcotsMin() != null) {
            if (shopSales.doubleValue() > 0) {
                sb.append(" and ROUND((ifnull(sum(cost),0) / ").append(shopSales).append("*100 ),2) >= ? ");
                argsList.add(qo.getAcotsMin());
            } else {
                sb.append(" and 0 >= ? ");
                argsList.add(qo.getAcotsMin());
            }
        }
        // acots  需要乘以店铺销售额
        if (qo.getAcotsMax() != null) {
            if (shopSales.doubleValue() > 0) {
                sb.append(" and ROUND((ifnull(sum(cost),0) / ").append(shopSales).append("*100 ),2) <= ? ");
                argsList.add(qo.getAcotsMax());
            } else {
                sb.append(" and 0 <= ? ");
                argsList.add(qo.getAcotsMax());
            }
        }
        // asots 需要乘以店铺销售额
        if (qo.getAsotsMin() != null) {
            if (shopSales.doubleValue() > 0) {
                sb.append(" and ROUND((ifnull(sum(sales14d),0) / ").append(shopSales).append("*100 ),2) >= ? ");
                argsList.add(qo.getAsotsMin());
            } else {
                sb.append(" and 0 >= ? ");
                argsList.add(qo.getAsotsMin());
            }
        }
        // asots  需要乘以店铺销售额
        if (qo.getAsotsMax() != null) {
            if (shopSales.doubleValue() > 0) {
                sb.append(" and ROUND((ifnull(sum(sales14d),0) / ").append(shopSales).append("*100 ),2) <= ? ");
                argsList.add(qo.getAsotsMax());
            } else {
                sb.append(" and 0 <= ? ");
                argsList.add(qo.getAsotsMax());
            }
        }
        //CPA
        if (qo.getCpaMin() != null) {
            sb.append(" and ROUND(ifnull(sum(cost)/sum(conversions14d),0), 2) >= ?");
            argsList.add(qo.getCpaMin());
        }
        if (qo.getCpaMax() != null) {
            sb.append(" and ROUND(ifnull(sum(cost)/sum(conversions14d),0), 2) <= ?");
            argsList.add(qo.getCpaMax());
        }

        //本广告产品订单量
        if (qo.getAdSaleNumMin() != null) {
            sb.append(" and ifnull(sum(conversions14d_same_sku), 0) >= ?");
            argsList.add(qo.getAdSaleNumMin());
        }
        if (qo.getAdSaleNumMax() != null) {
            sb.append(" and ifnull(sum(conversions14d_same_sku), 0) <= ?");
            argsList.add(qo.getAdSaleNumMax());
        }
        //其他产品广告订单量
        if (qo.getAdOtherOrderNumMin() != null) {
            sb.append(" and ifnull(sum(conversions14d) - sum(conversions14d_same_sku), 0) >= ?");
            argsList.add(qo.getAdOtherOrderNumMin());
        }
        if (qo.getAdOtherOrderNumMax() != null) {
            sb.append(" and ifnull(sum(conversions14d) - sum(conversions14d_same_sku), 0) <= ?");
            argsList.add(qo.getAdOtherOrderNumMax());
        }
        //本广告产品销售额
        if (qo.getAdSalesMin() != null) {
            sb.append(" and ifnull(sum(sales14d_same_sku), 0) >= ?");
            argsList.add(qo.getAdSalesMin());
        }
        if (qo.getAdSalesMax() != null) {
            sb.append(" and ifnull(sum(sales14d_same_sku), 0) <= ?");
            argsList.add(qo.getAdSalesMax());
        }
        //其他产品广告销售额
        if (qo.getAdOtherSalesMin() != null) {
            sb.append(" and ifnull(sum(sales14d) - sum(sales14d_same_sku), 0) >= ?");
            argsList.add(qo.getAdOtherSalesMin());
        }
        if (qo.getAdOtherSalesMax() != null) {
            sb.append(" and ifnull(sum(sales14d) - sum(sales14d_same_sku), 0) <= ?");
            argsList.add(qo.getAdOtherSalesMax());
        }
        //广告销量
        if (qo.getAdSalesTotalMin() != null) {
            sb.append(" and ifnull(sum(units_ordered14d), 0) >= ?");
            argsList.add(qo.getAdSalesTotalMin());
        }
        if (qo.getAdSalesTotalMax() != null) {
            sb.append(" and ifnull(sum(units_ordered14d), 0) <= ?");
            argsList.add(qo.getAdSalesTotalMax());
        }
        //品牌新买家订单转化率
        if (qo.getBrandNewBuyerOrderConversionRateMin() != null) {
            sb.append(" and ifnull(ROUND(sum(orders_new_to_brand14d)/sum(clicks)*100, 2), 0) >= ?");
            argsList.add(qo.getBrandNewBuyerOrderConversionRateMin());
        }
        if (qo.getBrandNewBuyerOrderConversionRateMax() != null) {
            sb.append(" and ifnull(ROUND(sum(orders_new_to_brand14d)/sum(clicks)*100, 2), 0) <= ?");
            argsList.add(qo.getBrandNewBuyerOrderConversionRateMax());
        }
        //品牌新买家订单量
        if (qo.getOrdersNewToBrandFTDMin() != null) {
            sb.append(" and ifnull(sum(orders_new_to_brand14d), 0) >= ?");
            argsList.add(qo.getOrdersNewToBrandFTDMin());
        }
        if (qo.getOrdersNewToBrandFTDMax() != null) {
            sb.append(" and ifnull(sum(orders_new_to_brand14d), 0) <= ?");
            argsList.add(qo.getOrdersNewToBrandFTDMax());
        }
        //品牌新买家订单量百分比
        if (qo.getOrderRateNewToBrandFTDMin() != null) {
            sb.append(" and ifnull(ROUND(sum(orders_new_to_brand14d)/sum(conversions14d)*100, 2), 0) >= ?");
            argsList.add(qo.getOrderRateNewToBrandFTDMin());
        }
        if (qo.getOrderRateNewToBrandFTDMax() != null) {
            sb.append(" and ifnull(ROUND(sum(orders_new_to_brand14d)/sum(conversions14d)*100, 2), 0) <= ?");
            argsList.add(qo.getOrderRateNewToBrandFTDMax());
        }
        //品牌新买家销售额
        if (qo.getSalesNewToBrandFTDMin() != null) {
            sb.append(" and ifnull(sum(sales_new_to_brand14d), 0) >= ?");
            argsList.add(qo.getSalesNewToBrandFTDMin());
        }
        if (qo.getSalesNewToBrandFTDMax() != null) {
            sb.append(" and ifnull(sum(sales_new_to_brand14d), 0) <= ?");
            argsList.add(qo.getSalesNewToBrandFTDMax());
        }
        //品牌新买家销售额百分比
        if (qo.getSalesRateNewToBrandFTDMin() != null) {
            sb.append(" and ifnull(ROUND(sum(sales_new_to_brand14d)/sum(sales14d)*100, 2), 0) >= ?");
            argsList.add(qo.getSalesRateNewToBrandFTDMin());
        }
        if (qo.getSalesRateNewToBrandFTDMax() != null) {
            sb.append(" and ifnull(ROUND(sum(sales_new_to_brand14d)/sum(sales14d)*100, 2), 0) <= ?");
            argsList.add(qo.getSalesRateNewToBrandFTDMax());
        }
        //可见展示次数
        if (qo.getViewImpressionsMin() != null) {
            sb.append(" and ifnull(sum(view_impressions), 0) >= ?");
            argsList.add(qo.getViewImpressionsMin());
        }
        if (qo.getViewImpressionsMax() != null) {
            sb.append(" and ifnull(sum(view_impressions), 0) <= ?");
            argsList.add(qo.getViewImpressionsMax());
        }
        //vcpm
        if (qo.getVcpmMin() != null) {
            sb.append(" and ifnull(ROUND(sum(cost)/sum(view_impressions)*1000, 2), 0) >= ?");
            argsList.add(qo.getVcpmMin());
        }
        if (qo.getVcpmMax() != null) {
            sb.append(" and ifnull(ROUND(sum(cost)/sum(view_impressions)*1000, 2), 0) <= ?");
            argsList.add(qo.getVcpmMax());
        }
//        //本广告产品销量
//        if (qo.getAdSelfSaleNumMin() != null) {
//            sb.append(" and ifnull(sum(ad_order_num), 0) >= ?");
//            argsList.add(qo.getAdSelfSaleNumMin());
//        }
//        if (qo.getAdSelfSaleNumMax() != null) {
//            sb.append(" and ifnull(sum(ad_order_num), 0) <= ?");
//            argsList.add(qo.getAdSelfSaleNumMax());
//        }
//        //其他产品广告销量
//        if (qo.getAdOtherSaleNumMin() != null) {
//            sb.append(" and ifnull(sum(units_sold14d) - sum(ad_order_num), 0) >= ?");
//            argsList.add(qo.getAdOtherSaleNumMin());
//        }
//        if (qo.getAdOtherSaleNumMax() != null) {
//            sb.append(" and ifnull(sum(units_sold14d) - sum(ad_order_num), 0) <= ?");
//            argsList.add(qo.getAdOtherSaleNumMax());
//        }

        // 加购次数 筛选
        if (qo.getAddToCartMin() != null) {
            sb.append(" and ifnull(sum(add_to_cart) , 0) >= ? ");
            argsList.add(qo.getAddToCartMin());
        }
        if (qo.getAddToCartMax() != null ) {
            sb.append(" and ifnull(sum(add_to_cart) , 0) <= ? ");
            argsList.add(qo.getAddToCartMax());
        }

        // 视频完整播放次数 筛选
        if (qo.getVideoCompleteViewsMin() != null ) {
            sb.append(" and ifnull(sum(video_complete_views) , 0) >= ? ");
            argsList.add(qo.getVideoCompleteViewsMin());
        }
        if (qo.getVideoCompleteViewsMax() != null ) {
            sb.append(" and ifnull(sum(video_complete_views) , 0) <= ? ");
            argsList.add(qo.getVideoCompleteViewsMax());
        }

        // 观看率 筛选
        if (qo.getViewabilityRateMin() != null) {
            sb.append(" and ROUND(ifnull((sum(view_impressions)/sum(impressions)) * 100, 0), 2) >= ? ");
            argsList.add(qo.getViewabilityRateMin());
        }
        if (qo.getViewabilityRateMax() != null) {
            sb.append(" and ROUND(ifnull((sum(view_impressions)/sum(impressions)) * 100, 0), 2) <= ? ");
            argsList.add(qo.getViewabilityRateMax());
        }

        // 观看点击率 筛选
        if (qo.getViewClickThroughRateMin() != null) {
            sb.append(" and ROUND(ifnull((sum(clicks)/sum(view_impressions)) * 100, 0), 2) >= ? ");
            argsList.add(qo.getViewClickThroughRateMin());
        }
        if (qo.getViewClickThroughRateMax() != null) {
            sb.append(" and ROUND(ifnull((sum(clicks)/sum(view_impressions)) * 100, 0), 2) <= ? ");
            argsList.add(qo.getViewClickThroughRateMax());
        }

        // 品牌搜索次数 筛选
        if (qo.getBrandedSearchesMin() != null) {
            sb.append(" and ifnull(sum(branded_searches14d) , 0) >= ? ");
            argsList.add(qo.getBrandedSearchesMin()); }
        if (qo.getBrandedSearchesMax() != null) {
            sb.append(" and ifnull(sum(branded_searches14d) , 0) <= ? ");
            argsList.add(qo.getBrandedSearchesMax()); }

        // 广告笔单价 筛选
        if (qo.getAdvertisingUnitPriceMin() != null) {
            sb.append(" and ROUND(ifnull(sum(sales14d)/sum(conversions14d), 0), 2) >= ?");
            argsList.add(qo.getAdvertisingUnitPriceMin());
        }
        if (qo.getAdvertisingUnitPriceMax() != null) {
            sb.append(" and ROUND(ifnull(sum(sales14d)/sum(conversions14d), 0), 2) <= ?");
            argsList.add(qo.getAdvertisingUnitPriceMax());
        }

        // 搜索结果首页首位IS
        if (qo.getTopImpressionShareMin() != null){
            sb.append(" and max(top_of_search_is) >= ?");
            argsList.add(qo.getTopImpressionShareMin());
        }
        if (qo.getTopImpressionShareMax() != null){
            sb.append(" and min(top_of_search_is) <= ?");
            argsList.add(qo.getTopImpressionShareMax());
        }

        return sb.toString();
    }

    public static String getPageOrderBySql(String orderField, String orderType, String defaultOrderBy, String type) {
        StringBuilder sb = new StringBuilder();
        if (StringUtils.isNotBlank(orderField) && StringUtils.isNotBlank(orderType)) {
            String field = "";
            if (Constants.SP.equals(type)) {
                field = getSpOrderField(orderField);
            } else if (Constants.SB.equals(type)) {
                field = getSbOrderField(orderField);
            } else if (Constants.SD.equals(type)) {
                field = getSdOrderField(orderField);
            }
            if (StringUtils.isNotBlank(field)) {
                sb.append(" order by ").append(field);
                if (OrderTypeEnum.desc.getType().equals(orderType)) {
                    sb.append(" desc ");
                }
                sb.append(" , id desc ");
            }
        }
        if (sb.length() == 0) {
            sb.append(" order by ").append(defaultOrderBy);
        }
        return sb.toString();
    }

    public static String getDorisTargetPageOrderBySql(String orderField, String orderType, String defaultOrderBy, String type) {
        return getDorisTargetPageOrderBySql(orderField, orderType, defaultOrderBy, type, false);
    }

    public static String getDorisTargetPageOrderBySql(String orderField, String orderType, String defaultOrderBy, String type, boolean supportAbaRankOrder) {
        StringBuilder sb = new StringBuilder();
        if (StringUtils.isNotBlank(orderField) && StringUtils.isNotBlank(orderType)) {
            String field = "";
            if (Constants.SP.equals(type)) {
                field = getDorisSpOrderField(orderField, supportAbaRankOrder);
            } else if (Constants.SB.equals(type)) {
                field = getDorisSbOrderField(orderField, supportAbaRankOrder);
            } else if (Constants.SD.equals(type)) {
//                field = getSdOrderField(orderField);
            }
            if (StringUtils.isNotBlank(field)) {
                sb.append(" order by ").append(field);
                if (OrderTypeEnum.desc.getType().equals(orderType)) {
                    sb.append(" desc ");
                }
            }
        }
        if (sb.length() == 0) {
            sb.append(" order by ").append(defaultOrderBy);
        }
        return sb.toString();
    }

    public static String getAllCampaignOrderField(String field) {

        final String SALE_NUM = "if (type = 'sp' ,`units_ordered7d`,if (type = 'sb' ,`units_sold14d`,`units_ordered14d`))";
        final String AD_SALE_NUM = "if (type = 'sp',`units_ordered7d_same_sku`,`units_ordered14d_same_sku`)";
        final String TOTAL_SALES = "if (type = 'sp', sales7d,sales14d)";
        final String AD_SALES = "if (type = 'sp', sales7d_same_sku,sales14d_same_sku)";
        final String ORDER_NUM = "if (type = 'sp', conversions7d,conversions14d)";
        final String AD_ORDER_NUM = "if (type = 'sp' ,`conversions7d_same_sku`,`conversions14d_same_sku`)";
        final String VIEW_IMPRESSIONS = "if (type = 'sb', `viewable_impressions`, `view_impressions`)";

        switch (field) {
            case "id":
                return "id";
            case "dailyBudget":
                return "budget";
            case "name":
                return "name";
            case "adCost":
            case "adCostPercentage":
                return " sum(cost) ";
            //广告订单量
            case "adOrderNum":
            case "adOrderNumPercentage":
                return " sum(" + ORDER_NUM + ") ";
            //广告销售额
            case "adSale":
            case "adSalePercentage":
                return " sum(" + TOTAL_SALES + ") ";
            case "orderNumPercentage":
                return " sum(" + SALE_NUM + ") ";
            //广告点击率ctr
            case "ctr":
                return " ifnull(sum(clicks)/sum(impressions),0) ";
            //广告转化率cvr
            case "cvr":
                return " ifnull(sum(" + ORDER_NUM + ")/sum(clicks),0) ";
            case "adCostPerClick":
                return " ifnull(sum(cost)/sum(clicks),0) ";
            case "acots":
                return " sum(cost) ";
            case "asots":
                return " sum( " + TOTAL_SALES + " ) ";
            case "impressions":
                return " sum(impressions) ";
            //点击量
            case "clicks":
                return " sum(clicks) ";
            //点击率（clicks/impressions）
            case "clickRate":
                return " ROUND(ifnull(sum(clicks)/sum(impressions),0),4) ";
            //花费
            case "cost":
                return " sum(cost) ";
            //cpc  平均点击费用
            case "cpc":
                return " ROUND(ifnull(sum(cost)/sum(clicks),0),2) ";
            //广告销量
            case "orderNum":
                return " sum( " + SALE_NUM + " ) ";
            //广告销售额
            case "sales":
                return " sum(if (type = 'sp', sales7d,sales14d)) ";
            //订单转化率
            case "salesConversionRate":
                return " ROUND(ifnull(sum(if (type = 'sp' , conversions7d,conversions14d))/sum(clicks),0),4) ";
            //acos
            case "acos":
                return " ROUND(ifnull(sum(cost)/sum(" + TOTAL_SALES + "),0),4) ";
            // roas
            case "roas":
                return " ROUND(ifnull(sum(" + TOTAL_SALES + ")/sum(cost),0),2) ";
            /******************************高级搜索新增查询指标*****************************/
//            //可见展示次数
//            case "viewImpressions":
//                return " sum(view_impressions) ";
            //广告销量
            case "adSalesTotal":
                return " sum(if (type = 'sp' ,`units_ordered7d`,if (type = 'sb' ,`units_sold14d`,`units_ordered14d`))) ";
            //CPA
            case "cpa":
                return " ifnull(sum(cost)/sum(" + ORDER_NUM + "),0) ";
            //VCPM= 每千次展现费用=广告花费/可见展现次数x1000
            case "vcpm":
                return " ROUND(ifnull((sum(cost)/sum(" + VIEW_IMPRESSIONS + ")) * 1000, 0), 4) ";
            //本广告产品订单量（绝对值）adSaleNumMin
            case "adSaleNum":
                return " ifnull(sum(" + AD_ORDER_NUM + "), 0) ";
            //其他产品广告订单量（绝对值） adOtherOrderNumMin
            case "adOtherOrderNum":
                return " ifnull(sum(" + ORDER_NUM + ") - sum(" + AD_ORDER_NUM + "), 0) ";
            case "adSales":
                return " ifnull(sum(" + AD_SALES + "), 0) ";
            //其他产品广告销售额（绝对值）adOtherSalesMin
            case "adOtherSales":
                return " ifnull(sum(" + TOTAL_SALES + ") - sum(" + AD_SALES + "), 0) ";
            //本广告产品销量（绝对值）adSelfSaleNumMin units_ordered7d_same_sku（sb、sd无该指标，排序按0排）
            case "adSelfSaleNum":
                return " ifnull(sum(" + AD_SALE_NUM + "), 0) ";
            //其他产品广告销量（绝对值）adOtherSaleNumMin（sb、sd无该指标，排序按0排）
            case "adOtherSaleNum":
                return " ifnull(sum(" + SALE_NUM + ") - sum(" + AD_SALE_NUM + "), 0) ";
            //“品牌新买家”订单量（绝对值） orders_new_to_brand14d
            case "ordersNewToBrandFTD":
                return " ifnull(sum(orders_new_to_brand14d), 0) ";
            //“品牌新买家”订单百分比（百分比） orderRateNewToBrandFTDMin
            case "orderRateNewToBrandFTD":
                return " ROUND(ROUND(ifnull((sum(orders_new_to_brand14d)/sum(" + ORDER_NUM + ")) * 100, 0), 4), 2) ";
            //“品牌新买家”销售额（绝对值） salesNewToBrandFTDMin
            case "salesNewToBrandFTD":
                return " ifnull(sum(sales_new_to_brand14d), 0) ";
            //“品牌新买家”销售额百分比（百分比） salesRateNewToBrandFTDMin
            case "salesRateNewToBrandFTD":
                return " ROUND(ROUND(ifnull((sum(sales_new_to_brand14d)/sum(" + TOTAL_SALES + ")) * 100, 0), 4), 2) ";
            //“品牌新买家”销量（绝对值）unitsOrderedNewToBrandFTDMin   units_ordered_new_to_brand14d
            case "unitsOrderedNewToBrandFTD":
                return " ifnull(sum(units_ordered_new_to_brand14d), 0) ";
            //“品牌新买家”销量百分比（百分比）unitsOrderedRateNewToBrandFTDMin
            case "unitsOrderedRateNewToBrandFTD":
                return " ROUND(ROUND(ifnull((sum(units_ordered_new_to_brand14d)/sum(" + SALE_NUM + ")) * 100, 0), 4), 2) ";
            case "topImpressionShare":
                return "MAX(top_of_search_is)";

            case "newToBrandDetailPageViews":
                return "ifnull(sum(new_to_brand_detail_page_views), 0)";
            case "addToCart":
                return "ifnull(sum(add_to_cart), 0)";
            case "addToCartRate":
                return "ifnull(sum(add_to_cart)/sum(impressions), 0)";
            case "eCPAddToCart":
                return "ifnull(sum(cost)/sum(add_to_cart), 0)";
            case "video5SecondViews":
                return "ifnull(sum(video5second_views), 0)";
            case "video5SecondViewRate":
                return "ifnull(sum(video5second_views)/sum(impressions), 0)";
            case "videoFirstQuartileViews":
                return "ifnull(sum(video_first_quartile_views), 0)";
            case "videoMidpointViews":
                return "ifnull(sum(video_Midpoint_Views), 0)";
            case "videoThirdQuartileViews":
                return "ifnull(sum(video_third_quartile_views), 0)";
            case "videoCompleteViews":
                return "ifnull(sum(video_complete_views), 0)";
            case "videoUnmutes":
                return "ifnull(sum(video_unmutes), 0)";
            case "viewImpressions":
                return "ifnull(sum(" + VIEW_IMPRESSIONS + "), 0)";
            case "viewabilityRate":
                return "ifnull(sum(" + VIEW_IMPRESSIONS + ")/sum(impressions), 0)";
            case "viewClickThroughRate":
                return "ifnull(sum(clicks)/sum(" + VIEW_IMPRESSIONS + "), 0)";
            case "brandedSearches":
                return "ifnull(sum(branded_searches14d), 0)";
            case "detailPageViews":
                return "ifnull(sum(detail_page_view14d), 0)";
            case "cumulativeReach":
                return "ifnull(max(cumulative_reach), 0)";
            case "impressionsFrequencyAverage":
                return "ifnull(max(impressions_frequency_average), 0)";
            case "advertisingUnitPrice":
                return "ifnull(sum(" + TOTAL_SALES + ")/sum(" + ORDER_NUM + "), 0)";
            default:
                return null;
        }
    }

    public static String getGroupSpOrderField(String field) {
        switch (field) {
            case "adCost":
            case "adCostPercentage":
                return " sum(cost) ";
            case "impressions":
                return " sum(impressions) ";
            case "clicks":
                return " sum(clicks) ";
            //广告订单量
            case "adOrderNum":
            case "adOrderNumPercentage":
                return " sum(sale_num) ";
            //本广告产品订单量
            case "adSaleNum":
                return " sum(ad_sale_num) ";
            //广告销售额
            case "adSale":
            case "adSalePercentage":
                return " sum(total_sales) ";
            //本广告产品销售额
            case "adSales":
                return " sum(ad_sales) ";
            //广告销量
            case "orderNum":
            case "orderNumPercentage":
                return " sum(order_num) ";
            //本广告产品销量
            case "adSelfSaleNum":
                return " sum(ad_order_num) ";
            //广告点击率ctr
            case "ctr":
                return " ifnull(sum(clicks)/sum(impressions),0) ";
            //广告转化率cvr
            case "cvr":
                return " ifnull(sum(sale_num)/sum(clicks),0) ";
            case "cpa":
                return " ifnull(sum(cost)/sum(sale_num),0) ";
            case "adCostPerClick":
                return " ifnull(sum(cost)/sum(clicks),0) ";
            case "acos":
                return " ifnull(sum(cost)/sum(total_sales),0) ";
            case "roas":
                return " ifnull(sum(total_sales)/sum(cost),0) ";
            case "acots":
                return " sum(cost) ";
            case "asots":
                return " sum(total_sales) ";
            //其他产品广告订单量
            case "adOtherOrderNum":
                return " ifnull(sum(sale_num) - sum(ad_sale_num),0) ";
            //其他产品广告销售额
            case "adOtherSales":
                return " ifnull(sum(total_sales) - sum(ad_sales),0) ";
            //其他产品广告销量
            case "adOtherSaleNum":
                return " ifnull(sum(order_num) - sum(ad_order_num),0) ";
            //搜索结果首页首位IS
            case "topImpressionShare":
                return "MAX(top_of_search_is)";
            // 广告笔单价
            case "advertisingUnitPrice":
                return "ifnull(sum(total_sales)/sum(sale_num), 0)";
            default:
                return null;
        }
    }

    public static String getGroupSdOrderField(String field) {
        final String SALE_NUM = "`units_ordered14d`";
        final String AD_SALE_NUM = "'0'";
        final String TOTAL_SALES = "`sales14d`";
        final String AD_SALES = "`sales14d_same_sku`";
        final String ORDER_NUM = "`conversions14d`";
        final String AD_ORDER_NUM = "`conversions14d_same_sku`";
        final String VIEW_IMPRESSIONS = "`view_impressions`";

        switch (field) {
            case "adCost":
            case "adCostPercentage":
                return " sum(cost) ";
            case "impressions":
                return " sum(impressions) ";
            case "clicks":
                return " sum(clicks) ";
            //广告订单量
            case "adOrderNum":
            case "adOrderNumPercentage":
                return " sum("+ORDER_NUM+") ";
            //本广告产品订单量
            case "adSaleNum":
                return " sum("+AD_ORDER_NUM+") ";
            //广告销售额
            case "adSale":
            case "adSalePercentage":
                return " sum("+TOTAL_SALES+") ";
            //本广告产品销售额
            case "adSales":
                return " sum("+AD_SALES+") ";
            //广告销量
            case "orderNum":
            case "orderNumPercentage":
                return " sum("+SALE_NUM+") ";
            //本广告产品销量
            case "adSelfSaleNum":
                return " sum("+AD_SALE_NUM+") ";
            //广告点击率ctr
            case "ctr":
                return " ifnull(sum(clicks)/sum(impressions),0) ";
            //广告转化率cvr
            case "cvr":
                return " ifnull(sum("+ORDER_NUM+")/sum(clicks),0) ";
            case "cpa":
                return " ifnull(sum(cost)/sum("+ORDER_NUM+"),0) ";
            case "adCostPerClick":
                return " ifnull(sum(cost)/sum(clicks),0) ";
            case "acos":
                return " ifnull(sum(cost)/sum("+TOTAL_SALES+"),0) ";
            case "roas":
                return " ifnull(sum("+TOTAL_SALES+")/sum(cost),0) ";
            case "acots":
                return " sum(cost) ";
            case "asots":
                return " sum("+TOTAL_SALES+") ";
            //其他产品广告订单量
            case "adOtherOrderNum":
                return " ifnull(sum("+ORDER_NUM+") - sum("+AD_ORDER_NUM+"),0) ";
            //其他产品广告销售额
            case "adOtherSales":
                return " ifnull(sum("+TOTAL_SALES+") - sum("+AD_SALES+"),0) ";
            //其他产品广告销量
            case "adOtherSaleNum":
                return " ifnull(sum("+SALE_NUM+") - sum("+AD_SALE_NUM+"),0) ";
            //搜索结果首页首位IS
            case "topImpressionShare":
                return "MAX(top_of_search_is)";

            case "vcpm":
                return "ifnull(sum(cost)/sum(view_impressions), 0)";
            case "viewImpressions":
                return "ifnull(sum(" + VIEW_IMPRESSIONS + "), 0)";
            //“品牌新买家”相关指标
            case "ordersNewToBrandFTD":
                return " ifnull(sum(orders_new_to_brand14d), 0) ";
            //“品牌新买家”订单百分比（百分比） orderRateNewToBrandFTDMin
            case "orderRateNewToBrandFTD":
                return " ROUND(ROUND(ifnull((sum(orders_new_to_brand14d)/sum(" + ORDER_NUM + ")) * 100, 0), 4), 2) ";
            //“品牌新买家”销售额（绝对值） salesNewToBrandFTDMin
            case "salesNewToBrandFTD":
                return " ifnull(sum(sales_new_to_brand14d), 0) ";
            //“品牌新买家”销售额百分比（百分比） salesRateNewToBrandFTDMin
            case "salesRateNewToBrandFTD":
                return " ROUND(ROUND(ifnull((sum(sales_new_to_brand14d)/sum(" + TOTAL_SALES + ")) * 100, 0), 4), 2) ";
            //“品牌新买家”销量（绝对值）unitsOrderedNewToBrandFTDMin   units_ordered_new_to_brand14d
            case "unitsOrderedNewToBrandFTD":
                return " ifnull(sum(units_ordered_new_to_brand14d), 0) ";
            //“品牌新买家”销量百分比（百分比）unitsOrderedRateNewToBrandFTDMin
            case "unitsOrderedRateNewToBrandFTD":
                return " ROUND(ROUND(ifnull((sum(units_ordered_new_to_brand14d)/sum(" + SALE_NUM + ")) * 100, 0), 4), 2) ";

            case "newToBrandDetailPageViews":
                return "ifnull(sum(new_to_brand_detail_page_views), 0)";
            case "addToCart":
                return "ifnull(sum(add_to_cart), 0)";
            case "addToCartRate":
                return "ifnull(sum(add_to_cart)/sum(impressions), 0)";
            case "eCPAddToCart":
                return "ifnull(sum(cost)/sum(add_to_cart), 0)";
            case "videoFirstQuartileViews":
                return "ifnull(sum(video_first_quartile_views), 0)";
            case "videoMidpointViews":
                return "ifnull(sum(video_Midpoint_Views), 0)";
            case "videoThirdQuartileViews":
                return "ifnull(sum(video_third_quartile_views), 0)";
            case "videoCompleteViews":
                return "ifnull(sum(video_complete_views), 0)";
            case "videoUnmutes":
                return "ifnull(sum(video_unmutes), 0)";
            case "viewabilityRate":
                return "ifnull(sum(" + VIEW_IMPRESSIONS + ")/sum(impressions), 0)";
            case "viewClickThroughRate":
                return "ifnull(sum(clicks)/sum(" + VIEW_IMPRESSIONS + "), 0)";
            case "brandedSearches":
                return "ifnull(sum(branded_searches14d), 0)";
            case "detailPageViews":
                return "ifnull(sum(detail_page_view14d), 0)";
            case "cumulativeReach":
                return "ifnull(max(cumulative_reach), 0)";
            case "impressionsFrequencyAverage":
                return "ifnull(max(impressions_frequency_average), 0)";
            case "advertisingUnitPrice":
                return "ifnull(sum(" + TOTAL_SALES + ")/sum(" + ORDER_NUM + "), 0)";
            default:
                return null;
        }
    }

    public static String getGroupSbOrderField(String field) {
        final String SALE_NUM = "`units_sold14d`";
        final String AD_SALE_NUM = "'0'";
        final String TOTAL_SALES = "`sales14d`";
        final String AD_SALES = "`sales14d_same_sku`";
        final String ORDER_NUM = "`conversions14d`";
        final String AD_ORDER_NUM = "`conversions14d_same_sku`";
        final String VIEW_IMPRESSIONS = "`viewable_impressions`";

        switch (field) {
            case "adCost":
            case "adCostPercentage":
                return " sum(cost) ";
            case "impressions":
                return " sum(impressions) ";
            case "clicks":
                return " sum(clicks) ";
            //广告订单量
            case "adOrderNum":
            case "adOrderNumPercentage":
                return " sum("+ORDER_NUM+") ";
            //本广告产品订单量
            case "adSaleNum":
                return " sum("+AD_ORDER_NUM+") ";
            //广告销售额
            case "adSale":
            case "adSalePercentage":
                return " sum("+TOTAL_SALES+") ";
            //本广告产品销售额
            case "adSales":
                return " sum("+AD_SALES+") ";
            //广告销量
            case "orderNum":
            case "orderNumPercentage":
                return " sum("+SALE_NUM+") ";
            //本广告产品销量
            case "adSelfSaleNum":
                return " sum("+AD_SALE_NUM+") ";
            //广告点击率ctr
            case "ctr":
                return " ifnull(sum(clicks)/sum(impressions),0) ";
            //广告转化率cvr
            case "cvr":
                return " ifnull(sum("+ORDER_NUM+")/sum(clicks),0) ";
            case "cpa":
                return " ifnull(sum(cost)/sum("+ORDER_NUM+"),0) ";
            case "adCostPerClick":
                return " ifnull(sum(cost)/sum(clicks),0) ";
            case "acos":
                return " ifnull(sum(cost)/sum("+TOTAL_SALES+"),0) ";
            case "roas":
                return " ifnull(sum("+TOTAL_SALES+")/sum(cost),0) ";
            case "acots":
                return " sum(cost) ";
            case "asots":
                return " sum("+TOTAL_SALES+") ";
            //其他产品广告订单量
            case "adOtherOrderNum":
                return " ifnull(sum("+ORDER_NUM+") - sum("+AD_ORDER_NUM+"),0) ";
            //其他产品广告销售额
            case "adOtherSales":
                return " ifnull(sum("+TOTAL_SALES+") - sum("+AD_SALES+"),0) ";
            //其他产品广告销量
            case "adOtherSaleNum":
                return " ifnull(sum("+SALE_NUM+") - sum("+AD_SALE_NUM+"),0) ";
            //搜索结果首页首位IS
            case "topImpressionShare":
                return "MAX(top_of_search_is)";

            case "video5SecondViews":
                return "ifnull(sum(video5second_views), 0)";
            case "video5SecondViewRate":
                return "ifnull(sum(video5second_views)/sum(impressions), 0)";
            case "videoFirstQuartileViews":
                return "ifnull(sum(video_first_quartile_views), 0)";
            case "videoMidpointViews":
                return "ifnull(sum(video_Midpoint_Views), 0)";
            case "videoThirdQuartileViews":
                return "ifnull(sum(video_third_quartile_views), 0)";
            case "videoCompleteViews":
                return "ifnull(sum(video_complete_views), 0)";
            case "videoUnmutes":
                return "ifnull(sum(video_unmutes), 0)";
            case "viewImpressions":
                return "ifnull(sum(" + VIEW_IMPRESSIONS + "), 0)";
            case "viewabilityRate":
                return "ifnull(sum(" + VIEW_IMPRESSIONS + ")/sum(impressions), 0)";
            case "viewClickThroughRate":
                return "ifnull(sum(clicks)/sum(" + VIEW_IMPRESSIONS + "), 0)";
            case "brandedSearches":
                return "ifnull(sum(branded_searches14d), 0)";
            case "advertisingUnitPrice":
                return "ifnull(sum(" + TOTAL_SALES + ")/sum(" + ORDER_NUM + "), 0)";
            default:
                return null;
        }
    }

    public static String getSpOrderField(String field) {
        switch (field) {
            case "adCost":
            case "adCostPercentage":
                return " sum(cost) ";
            case "impressions":
                return " sum(impressions) ";
            case "clicks":
                return " sum(clicks) ";
            //广告订单量
            case "adOrderNum":
            case "adOrderNumPercentage":
                return " sum(sale_num) ";
            //本广告产品订单量
            case "adSaleNum":
                return " sum(ad_sale_num) ";
            //广告销售额
            case "adSale":
            case "adSalePercentage":
                return " sum(total_sales) ";
            //本广告产品销售额
            case "adSales":
                return " sum(ad_sales) ";
            //广告销量
            case "orderNum":
            case "orderNumPercentage":
                return " sum(order_num) ";
            //本广告产品销量
            case "adSelfSaleNum":
                return " sum(ad_order_num) ";
            //广告点击率ctr
            case "ctr":
                return " ifnull(sum(clicks)/sum(impressions),0) ";
            //广告转化率cvr
            case "cvr":
                return " ifnull(sum(sale_num)/sum(clicks),0) ";
            case "cpa":
                return " ifnull(sum(cost)/sum(sale_num),0) ";
            case "adCostPerClick":
                return " ifnull(sum(cost)/sum(clicks),0) ";
            case "acos":
                return " ifnull(sum(cost)/sum(total_sales),0) ";
            case "roas":
                return " ifnull(sum(total_sales)/sum(cost),0) ";
            case "acots":
                return " sum(cost) ";
            case "asots":
                return " sum(total_sales) ";
            //其他产品广告订单量
            case "adOtherOrderNum":
                return " ifnull(sum(sale_num) - sum(ad_sale_num),0) ";
            //其他产品广告销售额
            case "adOtherSales":
                return " ifnull(sum(total_sales) - sum(ad_sales),0) ";
            //其他产品广告销量
            case "adOtherSaleNum":
                return " ifnull(sum(order_num) - sum(ad_order_num),0) ";
            //搜索结果首页首位IS
            case "topImpressionShare":
                return "MAX(top_of_search_is)";
            case "advertisingUnitPrice":
                return "ifnull(sum(total_sales)/sum(sale_num), 0)";
            default:
                return null;
        }
    }

    public static String getSbOrderField(String field) {
        switch (field) {
            case "bid":
                return "bid";
            case "adCost":
            case "adCostPercentage":
                return " sum(cost) ";
            case "impressions":
                return " sum(impressions) ";
            case "clicks":
                return " sum(clicks) ";
            //广告订单量
            case "adOrderNum":
            case "adOrderNumPercentage":
                return " sum(conversions14d) ";
            //本广告产品订单量
            case "adSaleNum":
                return " sum(conversions14d_same_sku) ";
            //广告销售额
            case "adSale":
            case "adSalePercentage":
                return " sum(sales14d) ";
            //本广告产品销售额
            case "adSales":
                return " sum(sales14d_same_sku) ";
            //广告销量
            case "orderNum":
            case "orderNumPercentage":
                return " sum(units_sold14d) ";
            //广告点击率ctr
            case "ctr":
                return " ifnull(sum(clicks)/sum(impressions),0) ";
            //广告转化率cvr
            case "cvr":
                return " ifnull(sum(conversions14d)/sum(clicks),0) ";
            case "cpa":
                return " ifnull(sum(cost)/sum(conversions14d),0) ";
            case "adCostPerClick":
                return " ifnull(sum(cost)/sum(clicks),0) ";
            case "acos":
                return " ifnull(sum(cost)/sum(sales14d),0) ";
            case "roas":
                return " ifnull(sum(sales14d)/sum(cost),0) ";
            case "acots":
                return " sum(cost) ";
            case "asots":
                return " sum(sales14d) ";
            //搜索结果首页首位IS
            case "topImpressionShare":
                return "MAX(top_of_search_is)";
            //品牌新买家订单转化率
            case "ordersNewToBrandPercentageFTD":
                return "ifnull(sum(orders_new_to_brand14d)/sum(clicks), 0)";
            //品牌新买家订单量
            case "ordersNewToBrandFTD":
                return "ifnull(sum(orders_new_to_brand14d), 0)";
            //品牌新买家订单量百分比
            case "orderRateNewToBrandFTD":
                return "ifnull(sum(orders_new_to_brand14d)/sum(conversions14d), 0)";
            //品牌新买家销售额
            case "salesNewToBrandFTD":
                return "ifnull(sum(sales_new_to_brand14d), 0)";
            //品牌新买家销售额百分比
            case "salesRateNewToBrandFTD":
                return "ifnull(sum(sales_new_to_brand14d)/sum(sales14d), 0)";

//            //本广告产品销量
//            case "adSelfSaleNum":
//                return " sum(ad_order_num) ";
//            //其他产品广告订单量
//            case "adOtherOrderNum":
//                return " ifnull(sum(conversions14d) - sum(conversions14d_same_sku),0) ";
//            //其他产品广告销售额
//            case "adOtherSales":
//                return " ifnull(sum(sales14d) - sum(sales14d_same_sku),0) ";
//            //其他产品广告销量
//            case "adOtherSaleNum":
//                return " ifnull(sum(units_sold14d) - sum(ad_order_num),0) ";

            case "video5SecondViews":
                return "ifnull(sum(video5second_views), 0)";
            case "video5SecondViewRate":
                return "ifnull(sum(video5second_views)/sum(impressions), 0)";
            case "videoFirstQuartileViews":
                return "ifnull(sum(video_first_quartile_views), 0)";
            case "videoMidpointViews":
                return "ifnull(sum(video_Midpoint_Views), 0)";
            case "videoThirdQuartileViews":
                return "ifnull(sum(video_third_quartile_views), 0)";
            case "videoCompleteViews":
                return "ifnull(sum(video_complete_views), 0)";
            case "videoUnmutes":
                return "ifnull(sum(video_unmutes), 0)";
            case "viewImpressions":
                return "ifnull(sum(viewable_impressions), 0)";
            case "viewabilityRate":
                return "ifnull(sum(viewable_impressions)/sum(impressions), 0)";
            case "viewClickThroughRate":
                return "ifnull(sum(clicks)/sum(viewable_impressions), 0)";
            case "brandedSearches":
                return "ifnull(sum(branded_searches14d), 0)";
            case "advertisingUnitPrice":
                return "ifnull(sum(sales14d)/sum(conversions14d), 0)";

            default:
                return null;
        }
    }

    public static String getSdOrderField(String field) {
        switch (field) {
            case "bid":
                return "bid";
            case "adCost":
            case "adCostPercentage":
                return " sum(cost) ";
            case "impressions":
                return " sum(impressions) ";
            case "clicks":
                return " sum(clicks) ";
            //广告订单量
            case "adOrderNum":
            case "adOrderNumPercentage":
                return " sum(conversions14d) ";
            //本广告产品订单量
            case "adSaleNum":
                return " sum(conversions14d_same_sku) ";
            //广告销售额
            case "adSale":
            case "adSalePercentage":
                return " sum(sales14d) ";
            //本广告产品销售额
            case "adSales":
                return " sum(sales14d_same_sku) ";
            //广告销量
            case "orderNum":
            case "orderNumPercentage":
                return " sum(units_ordered14d) ";
            //广告点击率ctr
            case "ctr":
                return " ifnull(sum(clicks)/sum(impressions),0) ";
            //广告转化率cvr
            case "cvr":
                return " ifnull(sum(conversions14d)/sum(clicks),0) ";
            case "cpa":
                return " ifnull(sum(cost)/sum(conversions14d),0) ";
            case "adCostPerClick":
                return " ifnull(sum(cost)/sum(clicks),0) ";
            case "acos":
                return " ifnull(sum(cost)/sum(sales14d),0) ";
            case "roas":
                return " ifnull(sum(sales14d)/sum(cost),0) ";
            case "acots":
                return " sum(cost) ";
            case "asots":
                return " sum(sales14d) ";
            //搜索结果首页首位IS
            case "topImpressionShare":
                return "MAX(top_of_search_is)";
            //品牌新买家订单转化率
            case "ordersNewToBrandPercentageFTD":
                return "ifnull(sum(orders_new_to_brand14d)/sum(clicks), 0)";
            //品牌新买家订单量
            case "ordersNewToBrandFTD":
                return "ifnull(sum(orders_new_to_brand14d), 0)";
            //品牌新买家订单量百分比
            case "orderRateNewToBrandFTD":
                return "ifnull(sum(orders_new_to_brand14d)/sum(conversions14d), 0)";
            //品牌新买家销售额
            case "salesNewToBrandFTD":
                return "ifnull(sum(sales_new_to_brand14d), 0)";
            //品牌新买家销售额百分比
            case "salesRateNewToBrandFTD":
                return "ifnull(sum(sales_new_to_brand14d)/sum(sales14d), 0)";
            //可见展示次数
            case "viewImpressions":
                return "ifnull(sum(view_impressions), 0)";
            //vcpm
            case "vcpm":
                return "ifnull(sum(cost)/sum(view_impressions), 0)";

//            //本广告产品销量
//            case "adSelfSaleNum":
//                return " sum(ad_order_num) ";
//            //其他产品广告订单量
//            case "adOtherOrderNum":
//                return " ifnull(sum(conversions14d) - sum(conversions14d_same_sku),0) ";
//            //其他产品广告销售额
//            case "adOtherSales":
//                return " ifnull(sum(sales14d) - sum(sales14d_same_sku),0) ";
//            //其他产品广告销量
//            case "adOtherSaleNum":
//                return " ifnull(sum(units_sold14d) - sum(ad_order_num),0) ";
            case "newToBrandDetailPageViews":
                return "ifnull(sum(new_to_brand_detail_page_views), 0)";
            case "addToCart":
                return "ifnull(sum(add_to_cart), 0)";
            case "addToCartRate":
                return "ifnull(sum(add_to_cart)/sum(impressions), 0)";
            case "eCPAddToCart":
                return "ifnull(sum(cost)/sum(add_to_cart), 0)";
            case "videoFirstQuartileViews":
                return "ifnull(sum(video_first_quartile_views), 0)";
            case "videoMidpointViews":
                return "ifnull(sum(video_Midpoint_Views), 0)";
            case "videoThirdQuartileViews":
                return "ifnull(sum(video_third_quartile_views), 0)";
            case "videoCompleteViews":
                return "ifnull(sum(video_complete_views), 0)";
            case "videoUnmutes":
                return "ifnull(sum(video_unmutes), 0)";
            case "viewabilityRate":
                return "ifnull(sum(view_impressions)/sum(impressions), 0)";
            case "viewClickThroughRate":
                return "ifnull(sum(clicks)/sum(view_impressions), 0)";
            case "brandedSearches":
                return "ifnull(sum(branded_searches14d), 0)";
            case "detailPageViews":
                return "ifnull(sum(detail_page_view14d), 0)";
            case "cumulativeReach":
                return "ifnull(max(cumulative_reach), 0)";
            case "impressionsFrequencyAverage":
                return "ifnull(max(impressions_frequency_average), 0)";
            case "advertisingUnitPrice":
                return "ifnull(sum(sales14d)/sum(conversions14d), 0)";

            default:
                return null;
        }
    }

    public static String getDorisSpOrderField(String field, boolean supportAbaRankOrder) {
        switch (field) {
            case "bid":
                return " IFNULL(ANY(bid), ANY(g.default_bid)) ";
            case "adCost":
            case "adCostPercentage":
                return " SUM(cost) ";
            case "impressions":
                return " SUM(impressions) ";
            case "clicks":
                return " SUM(clicks) ";
            //广告订单量
            case "adOrderNum":
            case "adOrderNumPercentage":
                return " SUM(sale_num) ";
            //本广告产品订单量
            case "adSaleNum":
                return " SUM(ad_sale_num) ";
            //广告销售额
            case "adSale":
            case "adSalePercentage":
                return " SUM(total_sales) ";
            //本广告产品销售额
            case "adSales":
                return " SUM(ad_sales) ";
            //广告销量
            case "orderNum":
            case "orderNumPercentage":
                return " SUM(order_num) ";
            //本广告产品销量
            case "adSelfSaleNum":
                return " SUM(ad_order_num) ";
            //广告点击率ctr
            case "ctr":
                return " ifnull(SUM(clicks)/SUM(impressions),0) ";
            //广告转化率cvr
            case "cvr":
                return " ifnull(SUM(sale_num)/SUM(clicks),0) ";
            case "cpa":
                return " ifnull(SUM(cost)/SUM(sale_num),0) ";
            case "adCostPerClick":
                return " ifnull(SUM(cost)/SUM(clicks),0) ";
            case "acos":
                return " ifnull(SUM(cost)/SUM(total_sales),0) ";
            case "roas":
                return " ifnull(SUM(total_sales)/SUM(cost),0) ";
            case "acots":
                return " SUM(cost) ";
            case "asots":
                return " SUM(total_sales) ";
            //其他产品广告订单量
            case "adOtherOrderNum":
                return " ifnull(SUM(sale_num) - SUM(ad_sale_num),0) ";
            //其他产品广告销售额
            case "adOtherSales":
                return " ifnull(SUM(total_sales) - SUM(ad_sales),0) ";
            //其他产品广告销量
            case "adOtherSaleNum":
                return " ifnull(SUM(order_num) - SUM(ad_order_num),0) ";
            //搜索结果首页首位IS
            case "topImpressionShare":
                return "MAX(max_top_of_search_is)";
            case "advertisingUnitPrice":
                return "ifnull(SUM(total_sales)/SUM(sale_num), 0)";
            case "searchFrequencyRank" :
                return supportAbaRankOrder ? " searchFrequencyRank " : null;
            case "weekRatio" :
                return supportAbaRankOrder ? " weekRatio " : null;
            default:
                return null;
        }
    }

    public static String getDorisSbOrderField(String field, boolean supportAbaRankOrder) {
        switch (field) {
            case "bid":
                return "IFNULL(ANY(k.bid), ANY(g.bid))";
            case "adCost":
            case "adCostPercentage":
                return " sum(cost) ";
            case "impressions":
                return " sum(impressions) ";
            case "clicks":
                return " sum(clicks) ";
            //广告订单量
            case "adOrderNum":
            case "adOrderNumPercentage":
                return " sum(conversions14d) ";
            //本广告产品订单量
            case "adSaleNum":
                return " sum(conversions14d_same_sku) ";
            //广告销售额
            case "adSale":
            case "adSalePercentage":
                return " sum(sales14d) ";
            //本广告产品销售额
            case "adSales":
                return " sum(sales14d_same_sku) ";
            //广告销量
            case "orderNum":
            case "orderNumPercentage":
                return " sum(units_sold14d) ";
            //广告点击率ctr
            case "ctr":
                return " ifnull(sum(clicks)/sum(impressions),0) ";
            //广告转化率cvr
            case "cvr":
                return " ifnull(sum(conversions14d)/sum(clicks),0) ";
            case "cpa":
                return " ifnull(sum(cost)/sum(conversions14d),0) ";
            case "adCostPerClick":
                return " ifnull(sum(cost)/sum(clicks),0) ";
            case "acos":
                return " ifnull(sum(cost)/sum(sales14d),0) ";
            case "roas":
                return " ifnull(sum(sales14d)/sum(cost),0) ";
            case "acots":
                return " sum(cost) ";
            case "asots":
                return " sum(sales14d) ";
            //搜索结果首页首位IS
            case "topImpressionShare":
                return "MAX(top_of_search_is)";
            //品牌新买家订单转化率
            case "ordersNewToBrandPercentageFTD":
                return "ifnull(sum(orders_new_to_brand14d)/sum(clicks), 0)";
            //品牌新买家订单量
            case "ordersNewToBrandFTD":
                return "ifnull(sum(orders_new_to_brand14d), 0)";
            //品牌新买家订单量百分比
            case "orderRateNewToBrandFTD":
                return "ifnull(sum(orders_new_to_brand14d)/sum(conversions14d), 0)";
            //品牌新买家销售额
            case "salesNewToBrandFTD":
                return "ifnull(sum(sales_new_to_brand14d), 0)";
            //品牌新买家销售额百分比
            case "salesRateNewToBrandFTD":
                return "ifnull(sum(sales_new_to_brand14d)/sum(sales14d), 0)";
            case "video5SecondViews":
                return "ifnull(sum(video5second_views), 0)";
            case "video5SecondViewRate":
                return "ifnull(sum(video5second_views)/sum(impressions), 0)";
            case "videoFirstQuartileViews":
                return "ifnull(sum(video_first_quartile_views), 0)";
            case "videoMidpointViews":
                return "ifnull(sum(video_Midpoint_Views), 0)";
            case "videoThirdQuartileViews":
                return "ifnull(sum(video_third_quartile_views), 0)";
            case "videoCompleteViews":
                return "ifnull(sum(video_complete_views), 0)";
            case "videoUnmutes":
                return "ifnull(sum(video_unmutes), 0)";
            case "viewImpressions":
                return "ifnull(sum(viewable_impressions), 0)";
            case "viewabilityRate":
                return "ifnull(sum(viewable_impressions)/sum(impressions), 0)";
            case "viewClickThroughRate":
                return "ifnull(sum(clicks)/sum(viewable_impressions), 0)";
            case "brandedSearches":
                return "ifnull(sum(branded_searches14d), 0)";
            case "advertisingUnitPrice":
                return "ifnull(sum(sales14d)/sum(conversions14d), 0)";

            default:
                return null;
        }
    }

    public static boolean isLatest(String field, String type) {
        return ("cumulativeReach".equalsIgnoreCase(field) || "impressionsFrequencyAverage".equalsIgnoreCase(field))
                && OrderTypeEnum.typeSet().contains(type);
    }

    public static String getSpGroupPageHavingSql(ReportAdvancedFilterBaseQo qo, List<Object> argsList) {

        StringBuilder sb = new StringBuilder();
        BigDecimal shopSales = qo.getShopSales() != null ? qo.getShopSales() : BigDecimal.valueOf(0);
        sb.append(" having 1=1 ");
        //展示量
        if (qo.getImpressionsMin() != null) {
            sb.append(" and ifnull(sum(impressions), 0) >= ?");
            argsList.add(qo.getImpressionsMin());
        }
        if (qo.getImpressionsMax() != null) {
            sb.append(" and ifnull(sum(impressions), 0) <= ?");
            argsList.add(qo.getImpressionsMax());
        }
        //点击量
        if (qo.getClicksMin() != null) {
            sb.append(" and ifnull(sum(clicks), 0) >= ?");
            argsList.add(qo.getClicksMin());
        }
        if (qo.getClicksMax() != null) {
            sb.append(" and ifnull(sum(clicks), 0) <= ?");
            argsList.add(qo.getClicksMax());
        }
        //点击率（clicks/impressions）
        if (qo.getClickRateMin() != null) {
            sb.append(" and ROUND(ifnull(sum(clicks)/sum(impressions),0),4) >= ?");
            argsList.add(qo.getClickRateMin());
        }
        if (qo.getClickRateMax() != null) {
            sb.append(" and ROUND(ifnull(sum(clicks)/sum(impressions),0),4) <= ?");
            argsList.add(qo.getClickRateMax());
        }
        //花费
        if (qo.getCostMin() != null) {
            sb.append(" and ifnull(sum(cost), 0) >= ?");
            argsList.add(qo.getCostMin());
        }
        if (qo.getCostMax() != null) {
            sb.append(" and ifnull(sum(cost), 0) <= ?");
            argsList.add(qo.getCostMax());
        }
        //cpc  平均点击费用
        if (qo.getCpcMin() != null) {
            sb.append(" and ROUND(ifnull(sum(cost)/sum(clicks),0),2) >= ?");
            argsList.add(qo.getCpcMin());
        }
        if (qo.getCpcMax() != null) {
            sb.append(" and ROUND(ifnull(sum(cost)/sum(clicks),0),2) <= ?");
            argsList.add(qo.getCpcMax());
        }
        //广告订单量
        if (qo.getOrderNumMin() != null) {
            sb.append(" and ifnull(sum(sale_num), 0) >= ?");
            argsList.add(qo.getOrderNumMin());
        }
        if (qo.getOrderNumMax() != null) {
            sb.append(" and ifnull(sum(sale_num), 0) <= ?");
            argsList.add(qo.getOrderNumMax());
        }
        //广告销售额
        if (qo.getSalesMin() != null) {
            sb.append(" and ifnull(sum(total_sales), 0) >= ?");
            argsList.add(qo.getSalesMin());
        }
        if (qo.getSalesMax() != null) {
            sb.append(" and ifnull(sum(total_sales), 0) <= ?");
            argsList.add(qo.getSalesMax());
        }
        //订单转化率
        if (qo.getSalesConversionRateMin() != null) {
            sb.append(" and ROUND(ifnull(sum(sale_num)/sum(clicks),0),4) >= ?");
            argsList.add(qo.getSalesConversionRateMin());
        }
        if (qo.getSalesConversionRateMax() != null) {
            sb.append(" and ROUND(ifnull(sum(sale_num)/sum(clicks),0),4) <= ?");
            argsList.add(qo.getSalesConversionRateMax());
        }
        //acos
        if (qo.getAcosMin() != null) {
            sb.append(" and ROUND(ifnull(sum(cost)/sum(total_sales),0),4) >= ?");
            argsList.add(qo.getAcosMin());
        }
        if (qo.getAcosMax() != null) {
            sb.append(" and ROUND(ifnull(sum(cost)/sum(total_sales),0),4) <= ?");
            argsList.add(qo.getAcosMax());
        }
        // roas
        if (qo.getRoasMin() != null) {
            sb.append(" and ROUND(ifnull(sum(total_sales)/sum(cost),0),2) >= ?");
            argsList.add(qo.getRoasMin());
        }
        // roas
        if (qo.getRoasMax() != null) {
            sb.append(" and ROUND(ifnull(sum(total_sales)/sum(cost),0),2) <= ?");
            argsList.add(qo.getRoasMax());
        }
        // acots  需要乘以店铺销售额
        if (qo.getAcotsMin() != null) {
            if (shopSales.doubleValue() > 0) {
                sb.append(" and ROUND((ifnull(sum(cost),0) / ").append(shopSales).append(" ),4) >= ? ");
                argsList.add(qo.getAcotsMin());
            } else {
                sb.append(" and 0 >= ? ");
                argsList.add(qo.getAcotsMin());
            }
        }
        // acots  需要乘以店铺销售额
        if (qo.getAcotsMax() != null) {
            if (shopSales.doubleValue() > 0) {
                sb.append(" and ROUND((ifnull(sum(cost),0) / ").append(shopSales).append(" ),4) <= ? ");
                argsList.add(qo.getAcotsMax());
            } else {
                sb.append(" and 0 <= ? ");
                argsList.add(qo.getAcotsMax());
            }
        }
        // asots 需要乘以店铺销售额
        if (qo.getAsotsMin() != null) {
            if (shopSales.doubleValue() > 0) {
                sb.append(" and ROUND((ifnull(sum(total_sales),0) / ").append(shopSales).append(" ),4) >= ? ");
                argsList.add(qo.getAsotsMin());
            } else {
                sb.append(" and 0 >= ? ");
                argsList.add(qo.getAsotsMin());
            }
        }
        // asots  需要乘以店铺销售额
        if (qo.getAsotsMax() != null) {
            if (shopSales.doubleValue() > 0) {
                sb.append(" and ROUND((ifnull(sum(total_sales),0) / ").append(shopSales).append(" ),4) <= ? ");
                argsList.add(qo.getAsotsMax());
            } else {
                sb.append(" and 0 <= ? ");
                argsList.add(qo.getAsotsMax());
            }
        }

        //CPA
        if (qo.getCpaMin() != null) {
            sb.append(" and ROUND(ifnull(sum(cost)/sum(sale_num),0), 2) >= ?");
            argsList.add(qo.getCpaMin());
        }
        if (qo.getCpaMax() != null) {
            sb.append(" and ROUND(ifnull(sum(cost)/sum(sale_num),0), 2) <= ?");
            argsList.add(qo.getCpaMax());
        }

        //本广告产品订单量
        if (qo.getAdSaleNumMin() != null) {
            sb.append(" and ifnull(sum(ad_sale_num), 0) >= ?");
            argsList.add(qo.getAdSaleNumMin());
        }
        if (qo.getAdSaleNumMax() != null) {
            sb.append(" and ifnull(sum(ad_sale_num), 0) <= ?");
            argsList.add(qo.getAdSaleNumMax());
        }
        //其他产品广告订单量
        if (qo.getAdOtherOrderNumMin() != null) {
            sb.append(" and ifnull(sum(sale_num) - sum(ad_sale_num), 0) >= ?");
            argsList.add(qo.getAdOtherOrderNumMin());
        }
        if (qo.getAdOtherOrderNumMax() != null) {
            sb.append(" and ifnull(sum(sale_num) - sum(ad_sale_num), 0) <= ?");
            argsList.add(qo.getAdOtherOrderNumMax());
        }
        //本广告产品销售额
        if (qo.getAdSalesMin() != null) {
            sb.append(" and ifnull(sum(ad_sales), 0) >= ?");
            argsList.add(qo.getAdSalesMin());
        }
        if (qo.getAdSalesMax() != null) {
            sb.append(" and ifnull(sum(ad_sales), 0) <= ?");
            argsList.add(qo.getAdSalesMax());
        }
        //其他产品广告销售额
        if (qo.getAdOtherSalesMin() != null) {
            sb.append(" and ifnull(sum(total_sales) - sum(ad_sales), 0) >= ?");
            argsList.add(qo.getAdOtherSalesMin());
        }
        if (qo.getAdOtherSalesMax() != null) {
            sb.append(" and ifnull(sum(total_sales) - sum(ad_sales), 0) <= ?");
            argsList.add(qo.getAdOtherSalesMax());
        }
        //广告销量
        if (qo.getAdSalesTotalMin() != null) {
            sb.append(" and ifnull(sum(order_num), 0) >= ?");
            argsList.add(qo.getAdSalesTotalMin());
        }
        if (qo.getAdSalesTotalMax() != null) {
            sb.append(" and ifnull(sum(order_num), 0) <= ?");
            argsList.add(qo.getAdSalesTotalMax());
        }
        //本广告产品销量
        if (qo.getAdSelfSaleNumMin() != null) {
            sb.append(" and ifnull(sum(ad_order_num), 0) >= ?");
            argsList.add(qo.getAdSelfSaleNumMin());
        }
        if (qo.getAdSelfSaleNumMax() != null) {
            sb.append(" and ifnull(sum(ad_order_num), 0) <= ?");
            argsList.add(qo.getAdSelfSaleNumMax());
        }
        //其他产品广告销量
        if (qo.getAdOtherSaleNumMin() != null) {
            sb.append(" and ifnull(sum(order_num) - sum(ad_order_num), 0) >= ?");
            argsList.add(qo.getAdOtherSaleNumMin());
        }
        if (qo.getAdOtherSaleNumMax() != null) {
            sb.append(" and ifnull(sum(order_num) - sum(ad_order_num), 0) <= ?");
            argsList.add(qo.getAdOtherSaleNumMax());
        }

        // 广告笔单价 筛选
        if (qo.getAdvertisingUnitPriceMin() != null) {
            sb.append(" and ROUND(ifnull(sum(total_sales)/sum(sale_num), 0), 2) >= ?");
            argsList.add(qo.getAdvertisingUnitPriceMin());
        }
        if (qo.getAdvertisingUnitPriceMax() != null) {
            sb.append(" and ROUND(ifnull(sum(total_sales)/sum(sale_num), 0), 2) <= ?");
            argsList.add(qo.getAdvertisingUnitPriceMax());
        }

        // 搜索结果首页首位IS
        if (qo.getTopImpressionShareMin() != null){
            sb.append(" and max(top_of_search_is) >= ?");
            argsList.add(qo.getTopImpressionShareMin());
        }
        if (qo.getTopImpressionShareMax() != null){
            sb.append(" and min(top_of_search_is) <= ?");
            argsList.add(qo.getTopImpressionShareMax());
        }

        return sb.toString();
    }

    public static String getSdGroupPageHavingSql(ReportAdvancedFilterBaseQo qo, List<Object> argsList) {
        final String SALE_NUM = "`units_ordered14d`";
        final String AD_SALE_NUM = "'0'";
        final String TOTAL_SALES = "`sales14d`";
        final String AD_SALES = "`sales14d_same_sku`";
        final String ORDER_NUM = "`conversions14d`";
        final String AD_ORDER_NUM = "`conversions14d_same_sku`";
        final String VIEW_IMPRESSIONS = "`view_impressions`";

        StringBuilder sb = new StringBuilder();
        BigDecimal shopSales = qo.getShopSales() != null ? qo.getShopSales() : BigDecimal.valueOf(0);
        sb.append(" having 1=1 ");
        //展示量
        if (qo.getImpressionsMin() != null) {
            sb.append(" and ifnull(sum(impressions), 0) >= ?");
            argsList.add(qo.getImpressionsMin());
        }
        if (qo.getImpressionsMax() != null) {
            sb.append(" and ifnull(sum(impressions), 0) <= ?");
            argsList.add(qo.getImpressionsMax());
        }
        //点击量
        if (qo.getClicksMin() != null) {
            sb.append(" and ifnull(sum(clicks), 0) >= ?");
            argsList.add(qo.getClicksMin());
        }
        if (qo.getClicksMax() != null) {
            sb.append(" and ifnull(sum(clicks), 0) <= ?");
            argsList.add(qo.getClicksMax());
        }
        //点击率（clicks/impressions）
        if (qo.getClickRateMin() != null) {
            sb.append(" and ROUND(ifnull(sum(clicks)/sum(impressions),0),4) >= ?");
            argsList.add(qo.getClickRateMin());
        }
        if (qo.getClickRateMax() != null) {
            sb.append(" and ROUND(ifnull(sum(clicks)/sum(impressions),0),4) <= ?");
            argsList.add(qo.getClickRateMax());
        }
        //花费
        if (qo.getCostMin() != null) {
            sb.append(" and ifnull(sum(cost), 0) >= ?");
            argsList.add(qo.getCostMin());
        }
        if (qo.getCostMax() != null) {
            sb.append(" and ifnull(sum(cost), 0) <= ?");
            argsList.add(qo.getCostMax());
        }
        //cpc  平均点击费用
        if (qo.getCpcMin() != null) {
            sb.append(" and ROUND(ifnull(sum(cost)/sum(clicks),0),2) >= ?");
            argsList.add(qo.getCpcMin());
        }
        if (qo.getCpcMax() != null) {
            sb.append(" and ROUND(ifnull(sum(cost)/sum(clicks),0),2) <= ?");
            argsList.add(qo.getCpcMax());
        }
        //广告订单量
        if (qo.getOrderNumMin() != null) {
            sb.append(" and ifnull(sum(" +ORDER_NUM+ "), 0) >= ?");
            argsList.add(qo.getOrderNumMin());
        }
        if (qo.getOrderNumMax() != null) {
            sb.append(" and ifnull(sum(" +ORDER_NUM+ "), 0) <= ?");
            argsList.add(qo.getOrderNumMax());
        }
        //广告销售额
        if (qo.getSalesMin() != null) {
            sb.append(" and ifnull(sum("+TOTAL_SALES+"), 0) >= ?");
            argsList.add(qo.getSalesMin());
        }
        if (qo.getSalesMax() != null) {
            sb.append(" and ifnull(sum("+TOTAL_SALES+"), 0) <= ?");
            argsList.add(qo.getSalesMax());
        }
        //订单转化率
        if (qo.getSalesConversionRateMin() != null) {
            sb.append(" and ROUND(ifnull(sum(" +ORDER_NUM+ ")/sum(clicks),0),4) >= ?");
            argsList.add(qo.getSalesConversionRateMin());
        }
        if (qo.getSalesConversionRateMax() != null) {
            sb.append(" and ROUND(ifnull(sum(" +ORDER_NUM+ ")/sum(clicks),0),4) <= ?");
            argsList.add(qo.getSalesConversionRateMax());
        }
        //acos
        if (qo.getAcosMin() != null) {
            sb.append(" and ROUND(ifnull(sum(cost)/sum("+TOTAL_SALES+"),0),4) >= ?");
            argsList.add(qo.getAcosMin());
        }
        if (qo.getAcosMax() != null) {
            sb.append(" and ROUND(ifnull(sum(cost)/sum("+TOTAL_SALES+"),0),4) <= ?");
            argsList.add(qo.getAcosMax());
        }
        // roas
        if (qo.getRoasMin() != null) {
            sb.append(" and ROUND(ifnull(sum("+TOTAL_SALES+")/sum(cost),0),2) >= ?");
            argsList.add(qo.getRoasMin());
        }
        // roas
        if (qo.getRoasMax() != null) {
            sb.append(" and ROUND(ifnull(sum("+TOTAL_SALES+")/sum(cost),0),2) <= ?");
            argsList.add(qo.getRoasMax());
        }
        // acots  需要乘以店铺销售额
        if (qo.getAcotsMin() != null) {
            if (shopSales.doubleValue() > 0) {
                sb.append(" and ROUND((ifnull(sum(cost),0) / ").append(shopSales).append(" ),4) >= ? ");
                argsList.add(qo.getAcotsMin());
            } else {
                sb.append(" and 0 >= ? ");
                argsList.add(qo.getAcotsMin());
            }
        }
        // acots  需要乘以店铺销售额
        if (qo.getAcotsMax() != null) {
            if (shopSales.doubleValue() > 0) {
                sb.append(" and ROUND((ifnull(sum(cost),0) / ").append(shopSales).append(" ),4) <= ? ");
                argsList.add(qo.getAcotsMax());
            } else {
                sb.append(" and 0 <= ? ");
                argsList.add(qo.getAcotsMax());
            }
        }
        // asots 需要乘以店铺销售额
        if (qo.getAsotsMin() != null) {
            if (shopSales.doubleValue() > 0) {
                sb.append(" and ROUND((ifnull(sum("+TOTAL_SALES+"),0) / ").append(shopSales).append(" ),4) >= ? ");
                argsList.add(qo.getAsotsMin());
            } else {
                sb.append(" and 0 >= ? ");
                argsList.add(qo.getAsotsMin());
            }
        }
        // asots  需要乘以店铺销售额
        if (qo.getAsotsMax() != null) {
            if (shopSales.doubleValue() > 0) {
                sb.append(" and ROUND((ifnull(sum("+TOTAL_SALES+"),0) / ").append(shopSales).append(" ),4) <= ? ");
                argsList.add(qo.getAsotsMax());
            } else {
                sb.append(" and 0 <= ? ");
                argsList.add(qo.getAsotsMax());
            }
        }

        //CPA
        if (qo.getCpaMin() != null) {
            sb.append(" and ROUND(ifnull(sum(cost)/sum(" +ORDER_NUM+ "),0), 2) >= ?");
            argsList.add(qo.getCpaMin());
        }
        if (qo.getCpaMax() != null) {
            sb.append(" and ROUND(ifnull(sum(cost)/sum(" +ORDER_NUM+ "),0), 2) <= ?");
            argsList.add(qo.getCpaMax());
        }

        //本广告产品订单量
        if (qo.getAdSaleNumMin() != null) {
            sb.append(" and ifnull(sum("+AD_ORDER_NUM+"), 0) >= ?");
            argsList.add(qo.getAdSaleNumMin());
        }
        if (qo.getAdSaleNumMax() != null) {
            sb.append(" and ifnull(sum("+AD_ORDER_NUM+"), 0) <= ?");
            argsList.add(qo.getAdSaleNumMax());
        }
        //其他产品广告订单量
        if (qo.getAdOtherOrderNumMin() != null) {
            sb.append(" and ifnull(sum("+ORDER_NUM+") - sum("+AD_ORDER_NUM+"), 0) >= ?");
            argsList.add(qo.getAdOtherOrderNumMin());
        }
        if (qo.getAdOtherOrderNumMax() != null) {
            sb.append(" and ifnull(sum("+ORDER_NUM+") - sum("+AD_ORDER_NUM+"), 0) <= ?");
            argsList.add(qo.getAdOtherOrderNumMax());
        }
        //本广告产品销售额
        if (qo.getAdSalesMin() != null) {
            sb.append(" and ifnull(sum("+AD_SALES+"), 0) >= ?");
            argsList.add(qo.getAdSalesMin());
        }
        if (qo.getAdSalesMax() != null) {
            sb.append(" and ifnull(sum("+AD_SALES+"), 0) <= ?");
            argsList.add(qo.getAdSalesMax());
        }
        //其他产品广告销售额
        if (qo.getAdOtherSalesMin() != null) {
            sb.append(" and ifnull(sum("+TOTAL_SALES+") - sum("+AD_SALES+"), 0) >= ?");
            argsList.add(qo.getAdOtherSalesMin());
        }
        if (qo.getAdOtherSalesMax() != null) {
            sb.append(" and ifnull(sum("+TOTAL_SALES+") - sum("+AD_SALES+"), 0) <= ?");
            argsList.add(qo.getAdOtherSalesMax());
        }
        //广告销量
        if (qo.getAdSalesTotalMin() != null) {
            sb.append(" and ifnull(sum("+SALE_NUM+"), 0) >= ?");
            argsList.add(qo.getAdSalesTotalMin());
        }
        if (qo.getAdSalesTotalMax() != null) {
            sb.append(" and ifnull(sum("+SALE_NUM+"), 0) <= ?");
            argsList.add(qo.getAdSalesTotalMax());
        }
        //本广告产品销量
        if (qo.getAdSelfSaleNumMin() != null) {
            sb.append(" and ifnull(sum("+AD_SALE_NUM+"), 0) >= ?");
            argsList.add(qo.getAdSelfSaleNumMin());
        }
        if (qo.getAdSelfSaleNumMax() != null) {
            sb.append(" and ifnull(sum("+AD_SALE_NUM+"), 0) <= ?");
            argsList.add(qo.getAdSelfSaleNumMax());
        }
        //其他产品广告销量
        if (qo.getAdOtherSaleNumMin() != null) {
            sb.append(" and 0 >= ?");
            argsList.add(qo.getAdOtherSaleNumMin());
        }
        if (qo.getAdOtherSaleNumMax() != null) {
            sb.append(" and 0 <= ?");
            argsList.add(qo.getAdOtherSaleNumMax());
        }

        //“品牌新买家”订单量（绝对值） orders_new_to_brand14d
        if (qo.getOrdersNewToBrandFTDMin() != null) {
            sb.append(" and ifnull(sum(orders_new_to_brand14d), 0) >= ? ");
            argsList.add(qo.getOrdersNewToBrandFTDMin());
        }
        if (qo.getOrdersNewToBrandFTDMax() != null) {
            sb.append(" and ifnull(sum(orders_new_to_brand14d), 0) <= ? ");
            argsList.add(qo.getOrdersNewToBrandFTDMax());
        }

        //“品牌新买家”订单百分比（百分比） orderRateNewToBrandFTDMin
        if (qo.getOrderRateNewToBrandFTDMin() != null) {
            sb.append(" and ROUND(ifnull((sum(orders_new_to_brand14d)/ifnull(sum(").append(ORDER_NUM).append("), 0)), 0), 4) >= ? ");
            argsList.add(qo.getOrderRateNewToBrandFTDMin());
        }
        if (qo.getOrderRateNewToBrandFTDMax() != null) {
            sb.append(" and ROUND(ifnull((sum(orders_new_to_brand14d)/ifnull(sum(").append(ORDER_NUM).append("), 0)), 0), 4) <= ? ");
            argsList.add(qo.getOrderRateNewToBrandFTDMax());
        }

        //“品牌新买家”销售额（绝对值） salesNewToBrandFTDMin
        if (qo.getSalesNewToBrandFTDMin() != null) {
            sb.append(" and ifnull(sum(sales_new_to_brand14d), 0) >= ? ");
            argsList.add(qo.getSalesNewToBrandFTDMin());
        }

        if (qo.getSalesNewToBrandFTDMax() != null) {
            sb.append(" and ifnull(sum(sales_new_to_brand14d), 0) <= ? ");
            argsList.add(qo.getSalesNewToBrandFTDMax());
        }

        //“品牌新买家”销售额百分比（百分比） salesRateNewToBrandFTDMin
        if (qo.getSalesRateNewToBrandFTDMin() != null) {
            sb.append(" and ROUND(ifnull((sum(sales_new_to_brand14d)/ifnull(sum(").append(TOTAL_SALES).append("), 0)), 0), 4) >= ? ");
            argsList.add(qo.getSalesRateNewToBrandFTDMin());
        }
        if (qo.getSalesRateNewToBrandFTDMax() != null) {
            sb.append(" and ROUND(ifnull((sum(sales_new_to_brand14d)/ifnull(sum(").append(TOTAL_SALES).append("), 0)), 0), 4) <= ? ");
            argsList.add(qo.getSalesRateNewToBrandFTDMax());
        }

        //“品牌新买家”销量（绝对值）unitsOrderedNewToBrandFTDMin   units_ordered_new_to_brand14d
        if (qo.getUnitsOrderedNewToBrandFTDMin() != null) {
            sb.append(" and ifnull(sum(units_ordered_new_to_brand14d), 0) >= ? ");
            argsList.add(qo.getUnitsOrderedNewToBrandFTDMin());
        }
        if (qo.getUnitsOrderedNewToBrandFTDMax() != null) {
            sb.append(" and ifnull(sum(units_ordered_new_to_brand14d), 0) <= ? ");
            argsList.add(qo.getUnitsOrderedNewToBrandFTDMax());
        }

        //“品牌新买家”销量百分比（百分比）unitsOrderedRateNewToBrandFTDMin
        if (qo.getUnitsOrderedRateNewToBrandFTDMin() != null) {
            sb.append(" and ROUND(ifnull((sum(units_ordered_new_to_brand14d)/ifnull(sum(").append(SALE_NUM).append("), 0)), 0), 4) >= ? ");
            argsList.add(qo.getUnitsOrderedRateNewToBrandFTDMin());
        }
        if (qo.getUnitsOrderedRateNewToBrandFTDMax() != null) {
            sb.append(" and ROUND(ifnull((sum(units_ordered_new_to_brand14d)/ifnull(sum(").append(SALE_NUM).append("), 0)), 0), 4) <= ? ");
            argsList.add(qo.getUnitsOrderedRateNewToBrandFTDMax());
        }

        //可见展示次数
        if(qo.getViewImpressionsMin() != null){
            sb.append(" and ifnull(sum(" + VIEW_IMPRESSIONS + "), 0) >= ? ");
            argsList.add(qo.getViewImpressionsMin());
        }
        if(qo.getViewImpressionsMax() != null){
            sb.append(" and ifnull(sum(" + VIEW_IMPRESSIONS + "), 0) <= ? ");
            argsList.add(qo.getViewImpressionsMax());
        }

        //VCPM= 每千次展现费用=广告花费/可见展现次数x1000
        if(qo.getVcpmMin() != null){
            sb.append(" and ifnull(ROUND(sum(cost)/sum(" + VIEW_IMPRESSIONS + ")*1000, 2), 0) >= ?");
            argsList.add(qo.getVcpmMin());
        }
        if (qo.getVcpmMax() != null) {
            sb.append(" and ifnull(ROUND(sum(cost)/sum(" + VIEW_IMPRESSIONS + ")*1000, 2), 0) <= ?");
            argsList.add(qo.getVcpmMax());
        }

        // 加购次数 筛选
        if (qo.getAddToCartMin() != null) {
            sb.append(" and ifnull(sum(add_to_cart) , 0) >= ? ");
            argsList.add(qo.getAddToCartMin());
        }
        if (qo.getAddToCartMax() != null ) {
            sb.append(" and ifnull(sum(add_to_cart) , 0) <= ? ");
            argsList.add(qo.getAddToCartMax());
        }

        // 视频完整播放次数 筛选
        if (qo.getVideoCompleteViewsMin() != null ) {
            sb.append(" and ifnull(sum(video_complete_views) , 0) >= ? ");
            argsList.add(qo.getVideoCompleteViewsMin());
        }
        if (qo.getVideoCompleteViewsMax() != null ) {
            sb.append(" and ifnull(sum(video_complete_views) , 0) <= ? ");
            argsList.add(qo.getVideoCompleteViewsMax());
        }

        // 观看率 筛选
        if (qo.getViewabilityRateMin() != null) {
            sb.append(" and ROUND(ifnull((sum(" + VIEW_IMPRESSIONS + ")/sum(impressions)) * 100, 0), 2) >= ? ");
            argsList.add(qo.getViewabilityRateMin());
        }
        if (qo.getViewabilityRateMax() != null) {
            sb.append(" and ROUND(ifnull((sum(" + VIEW_IMPRESSIONS + ")/sum(impressions)) * 100, 0), 2) <= ? ");
            argsList.add(qo.getViewabilityRateMax());
        }

        // 观看点击率 筛选
        if (qo.getViewClickThroughRateMin() != null) {
            sb.append(" and ROUND(ifnull((sum(clicks)/sum(" + VIEW_IMPRESSIONS + ")) * 100, 0), 2) >= ? ");
            argsList.add(qo.getViewClickThroughRateMin());
        }
        if (qo.getViewClickThroughRateMax() != null) {
            sb.append(" and ROUND(ifnull((sum(clicks)/sum(" + VIEW_IMPRESSIONS + ")) * 100, 0), 2) <= ? ");
            argsList.add(qo.getViewClickThroughRateMax());
        }

        // 品牌搜索次数 筛选
        if (qo.getBrandedSearchesMin() != null) {
            sb.append(" and ifnull(sum(branded_searches14d) , 0) >= ? ");
            argsList.add(qo.getBrandedSearchesMin()); }
        if (qo.getBrandedSearchesMax() != null) {
            sb.append(" and ifnull(sum(branded_searches14d) , 0) <= ? ");
            argsList.add(qo.getBrandedSearchesMax()); }

        // 广告笔单价 筛选
        if (qo.getAdvertisingUnitPriceMin() != null) {
            sb.append(" and ROUND(ifnull(sum(" + TOTAL_SALES + ")/sum(" + ORDER_NUM + "), 0), 2) >= ?");
            argsList.add(qo.getAdvertisingUnitPriceMin());
        }
        if (qo.getAdvertisingUnitPriceMax() != null) {
            sb.append(" and ROUND(ifnull(sum(" + TOTAL_SALES + ")/sum(" + ORDER_NUM + "), 0), 2) <= ?");
            argsList.add(qo.getAdvertisingUnitPriceMax());
        }

        // 搜索结果首页首位IS
        if (qo.getTopImpressionShareMin() != null){
            sb.append(" and max(top_of_search_is) >= ?");
            argsList.add(qo.getTopImpressionShareMin());
        }
        if (qo.getTopImpressionShareMax() != null){
            sb.append(" and min(top_of_search_is) <= ?");
            argsList.add(qo.getTopImpressionShareMax());
        }
        return sb.toString();
    }

    public static String getSbGroupPageHavingSql(ReportAdvancedFilterBaseQo qo, List<Object> argsList) {
        final String SALE_NUM = "`units_sold14d`";
        final String AD_SALE_NUM = "'0'";
        final String TOTAL_SALES = "`sales14d`";
        final String AD_SALES = "`sales14d_same_sku`";
        final String ORDER_NUM = "`conversions14d`";
        final String AD_ORDER_NUM = "`conversions14d_same_sku`";
        final String VIEW_IMPRESSIONS = "`viewable_impressions`";

        StringBuilder sb = new StringBuilder();
        BigDecimal shopSales = qo.getShopSales() != null ? qo.getShopSales() : BigDecimal.valueOf(0);
        sb.append(" having 1=1 ");
        //展示量
        if (qo.getImpressionsMin() != null) {
            sb.append(" and ifnull(sum(impressions), 0) >= ?");
            argsList.add(qo.getImpressionsMin());
        }
        if (qo.getImpressionsMax() != null) {
            sb.append(" and ifnull(sum(impressions), 0) <= ?");
            argsList.add(qo.getImpressionsMax());
        }
        //点击量
        if (qo.getClicksMin() != null) {
            sb.append(" and ifnull(sum(clicks), 0) >= ?");
            argsList.add(qo.getClicksMin());
        }
        if (qo.getClicksMax() != null) {
            sb.append(" and ifnull(sum(clicks), 0) <= ?");
            argsList.add(qo.getClicksMax());
        }
        //点击率（clicks/impressions）
        if (qo.getClickRateMin() != null) {
            sb.append(" and ROUND(ifnull(sum(clicks)/sum(impressions),0),4) >= ?");
            argsList.add(qo.getClickRateMin());
        }
        if (qo.getClickRateMax() != null) {
            sb.append(" and ROUND(ifnull(sum(clicks)/sum(impressions),0),4) <= ?");
            argsList.add(qo.getClickRateMax());
        }
        //花费
        if (qo.getCostMin() != null) {
            sb.append(" and ifnull(sum(cost), 0) >= ?");
            argsList.add(qo.getCostMin());
        }
        if (qo.getCostMax() != null) {
            sb.append(" and ifnull(sum(cost), 0) <= ?");
            argsList.add(qo.getCostMax());
        }
        //cpc  平均点击费用
        if (qo.getCpcMin() != null) {
            sb.append(" and ROUND(ifnull(sum(cost)/sum(clicks),0),2) >= ?");
            argsList.add(qo.getCpcMin());
        }
        if (qo.getCpcMax() != null) {
            sb.append(" and ROUND(ifnull(sum(cost)/sum(clicks),0),2) <= ?");
            argsList.add(qo.getCpcMax());
        }
        //广告订单量
        if (qo.getOrderNumMin() != null) {
            sb.append(" and ifnull(sum(" +ORDER_NUM+ "), 0) >= ?");
            argsList.add(qo.getOrderNumMin());
        }
        if (qo.getOrderNumMax() != null) {
            sb.append(" and ifnull(sum(" +ORDER_NUM+ "), 0) <= ?");
            argsList.add(qo.getOrderNumMax());
        }
        //广告销售额
        if (qo.getSalesMin() != null) {
            sb.append(" and ifnull(sum("+TOTAL_SALES+"), 0) >= ?");
            argsList.add(qo.getSalesMin());
        }
        if (qo.getSalesMax() != null) {
            sb.append(" and ifnull(sum("+TOTAL_SALES+"), 0) <= ?");
            argsList.add(qo.getSalesMax());
        }
        //订单转化率
        if (qo.getSalesConversionRateMin() != null) {
            sb.append(" and ROUND(ifnull(sum(" +ORDER_NUM+ ")/sum(clicks),0),4) >= ?");
            argsList.add(qo.getSalesConversionRateMin());
        }
        if (qo.getSalesConversionRateMax() != null) {
            sb.append(" and ROUND(ifnull(sum(" +ORDER_NUM+ ")/sum(clicks),0),4) <= ?");
            argsList.add(qo.getSalesConversionRateMax());
        }
        //acos
        if (qo.getAcosMin() != null) {
            sb.append(" and ROUND(ifnull(sum(cost)/sum("+TOTAL_SALES+"),0),4) >= ?");
            argsList.add(qo.getAcosMin());
        }
        if (qo.getAcosMax() != null) {
            sb.append(" and ROUND(ifnull(sum(cost)/sum("+TOTAL_SALES+"),0),4) <= ?");
            argsList.add(qo.getAcosMax());
        }
        // roas
        if (qo.getRoasMin() != null) {
            sb.append(" and ROUND(ifnull(sum("+TOTAL_SALES+")/sum(cost),0),2) >= ?");
            argsList.add(qo.getRoasMin());
        }
        // roas
        if (qo.getRoasMax() != null) {
            sb.append(" and ROUND(ifnull(sum("+TOTAL_SALES+")/sum(cost),0),2) <= ?");
            argsList.add(qo.getRoasMax());
        }
        // acots  需要乘以店铺销售额
        if (qo.getAcotsMin() != null) {
            if (shopSales.doubleValue() > 0) {
                sb.append(" and ROUND((ifnull(sum(cost),0) / ").append(shopSales).append(" ),4) >= ? ");
                argsList.add(qo.getAcotsMin());
            } else {
                sb.append(" and 0 >= ? ");
                argsList.add(qo.getAcotsMin());
            }
        }
        // acots  需要乘以店铺销售额
        if (qo.getAcotsMax() != null) {
            if (shopSales.doubleValue() > 0) {
                sb.append(" and ROUND((ifnull(sum(cost),0) / ").append(shopSales).append(" ),4) <= ? ");
                argsList.add(qo.getAcotsMax());
            } else {
                sb.append(" and 0 <= ? ");
                argsList.add(qo.getAcotsMax());
            }
        }
        // asots 需要乘以店铺销售额
        if (qo.getAsotsMin() != null) {
            if (shopSales.doubleValue() > 0) {
                sb.append(" and ROUND((ifnull(sum("+TOTAL_SALES+"),0) / ").append(shopSales).append(" ),4) >= ? ");
                argsList.add(qo.getAsotsMin());
            } else {
                sb.append(" and 0 >= ? ");
                argsList.add(qo.getAsotsMin());
            }
        }
        // asots  需要乘以店铺销售额
        if (qo.getAsotsMax() != null) {
            if (shopSales.doubleValue() > 0) {
                sb.append(" and ROUND((ifnull(sum("+TOTAL_SALES+"),0) / ").append(shopSales).append(" ),4) <= ? ");
                argsList.add(qo.getAsotsMax());
            } else {
                sb.append(" and 0 <= ? ");
                argsList.add(qo.getAsotsMax());
            }
        }



        //CPA
        if (qo.getCpaMin() != null) {
            sb.append(" and ROUND(ifnull(sum(cost)/sum(" +ORDER_NUM+ "),0), 2) >= ?");
            argsList.add(qo.getCpaMin());
        }
        if (qo.getCpaMax() != null) {
            sb.append(" and ROUND(ifnull(sum(cost)/sum(" +ORDER_NUM+ "),0), 2) <= ?");
            argsList.add(qo.getCpaMax());
        }

        //本广告产品订单量
        if (qo.getAdSaleNumMin() != null) {
            sb.append(" and ifnull(sum("+AD_ORDER_NUM+"), 0) >= ?");
            argsList.add(qo.getAdSaleNumMin());
        }
        if (qo.getAdSaleNumMax() != null) {
            sb.append(" and ifnull(sum("+AD_ORDER_NUM+"), 0) <= ?");
            argsList.add(qo.getAdSaleNumMax());
        }
        //其他产品广告订单量
        if (qo.getAdOtherOrderNumMin() != null) {
            sb.append(" and ifnull(sum("+ORDER_NUM+") - sum("+AD_ORDER_NUM+"), 0) >= ?");
            argsList.add(qo.getAdOtherOrderNumMin());
        }
        if (qo.getAdOtherOrderNumMax() != null) {
            sb.append(" and ifnull(sum("+ORDER_NUM+") - sum("+AD_ORDER_NUM+"), 0) <= ?");
            argsList.add(qo.getAdOtherOrderNumMax());
        }
        //本广告产品销售额
        if (qo.getAdSalesMin() != null) {
            sb.append(" and ifnull(sum("+AD_SALES+"), 0) >= ?");
            argsList.add(qo.getAdSalesMin());
        }
        if (qo.getAdSalesMax() != null) {
            sb.append(" and ifnull(sum("+AD_SALES+"), 0) <= ?");
            argsList.add(qo.getAdSalesMax());
        }
        //其他产品广告销售额
        if (qo.getAdOtherSalesMin() != null) {
            sb.append(" and ifnull(sum("+TOTAL_SALES+") - sum("+AD_SALES+"), 0) >= ?");
            argsList.add(qo.getAdOtherSalesMin());
        }
        if (qo.getAdOtherSalesMax() != null) {
            sb.append(" and ifnull(sum("+TOTAL_SALES+") - sum("+AD_SALES+"), 0) <= ?");
            argsList.add(qo.getAdOtherSalesMax());
        }
        //广告销量
        if (qo.getAdSalesTotalMin() != null) {
            sb.append(" and ifnull(sum("+SALE_NUM+"), 0) >= ?");
            argsList.add(qo.getAdSalesTotalMin());
        }
        if (qo.getAdSalesTotalMax() != null) {
            sb.append(" and ifnull(sum("+SALE_NUM+"), 0) <= ?");
            argsList.add(qo.getAdSalesTotalMax());
        }
        //本广告产品销量
        if (qo.getAdSelfSaleNumMin() != null) {
            sb.append(" and ifnull(sum("+AD_SALE_NUM+"), 0) >= ?");
            argsList.add(qo.getAdSelfSaleNumMin());
        }
        if (qo.getAdSelfSaleNumMax() != null) {
            sb.append(" and ifnull(sum("+AD_SALE_NUM+"), 0) <= ?");
            argsList.add(qo.getAdSelfSaleNumMax());
        }
        //其他产品广告销量
        if (qo.getAdOtherSaleNumMin() != null) {
            sb.append(" and ifnull(sum(" +SALE_NUM+ ") - sum("+AD_SALE_NUM+"), 0) >= ?");
            argsList.add(qo.getAdOtherSaleNumMin());
        }
        if (qo.getAdOtherSaleNumMax() != null) {
            sb.append(" and ifnull(sum(" +SALE_NUM+ ") - sum("+AD_SALE_NUM+"), 0) <= ?");
            argsList.add(qo.getAdOtherSaleNumMax());
        }

        // 5秒观看次数 筛选
        if (qo.getVideo5SecondViewsMin() != null ) {
            sb.append(" and ifnull(sum(video5second_views) , 0) >= ? ");
            argsList.add(qo.getVideo5SecondViewsMin());
        }
        if (qo.getVideo5SecondViewsMax() != null) {
            sb.append(" and ifnull(sum(video5second_views) , 0) <= ? ");
            argsList.add(qo.getVideo5SecondViewsMax());
        }

        // 视频完整播放次数 筛选
        if (qo.getVideoCompleteViewsMin() != null ) {
            sb.append(" and ifnull(sum(video_complete_views) , 0) >= ? ");
            argsList.add(qo.getVideoCompleteViewsMin());
        }
        if (qo.getVideoCompleteViewsMax() != null ) {
            sb.append(" and ifnull(sum(video_complete_views) , 0) <= ? ");
            argsList.add(qo.getVideoCompleteViewsMax());
        }

        //可见展示次数
        if (qo.getViewImpressionsMin() != null) {
            sb.append(" and ifnull(sum(" + VIEW_IMPRESSIONS + "), 0) >= ? ");
            argsList.add(qo.getViewImpressionsMin());
        }
        if (qo.getViewImpressionsMax() != null) {
            sb.append(" and ifnull(sum(" + VIEW_IMPRESSIONS + "), 0) <= ? ");
            argsList.add(qo.getViewImpressionsMax());
        }

        // 观看率 筛选
        if (qo.getViewabilityRateMin() != null) {
            sb.append(" and ROUND(ifnull((sum(" + VIEW_IMPRESSIONS + ")/sum(impressions)) * 100, 0), 2) >= ? ");
            argsList.add(qo.getViewabilityRateMin());
        }
        if (qo.getViewabilityRateMax() != null) {
            sb.append(" and ROUND(ifnull((sum(" + VIEW_IMPRESSIONS + ")/sum(impressions)) * 100, 0), 2) <= ? ");
            argsList.add(qo.getViewabilityRateMax());
        }

        // 观看点击率 筛选
        if (qo.getViewClickThroughRateMin() != null) {
            sb.append(" and ROUND(ifnull((sum(clicks)/sum(" + VIEW_IMPRESSIONS + ")) * 100, 0), 2) >= ? ");
            argsList.add(qo.getViewClickThroughRateMin());
        }
        if (qo.getViewClickThroughRateMax() != null) {
            sb.append(" and ROUND(ifnull((sum(clicks)/sum(" + VIEW_IMPRESSIONS + ")) * 100, 0), 2) <= ? ");
            argsList.add(qo.getViewClickThroughRateMax());
        }

        // 品牌搜索次数 筛选
        if (qo.getBrandedSearchesMin() != null) {
            sb.append(" and ifnull(sum(branded_searches14d) , 0) >= ? ");
            argsList.add(qo.getBrandedSearchesMin()); }
        if (qo.getBrandedSearchesMax() != null) {
            sb.append(" and ifnull(sum(branded_searches14d) , 0) <= ? ");
            argsList.add(qo.getBrandedSearchesMax());
        }

        // 搜索结果首页首位IS
        if (qo.getTopImpressionShareMin() != null){
            sb.append(" and max(top_of_search_is) >= ?");
            argsList.add(qo.getTopImpressionShareMin());
        }
        if (qo.getTopImpressionShareMax() != null){
            sb.append(" and min(top_of_search_is) <= ?");
            argsList.add(qo.getTopImpressionShareMax());
        }

        // 广告笔单价 筛选
        if (qo.getAdvertisingUnitPriceMin() != null) {
            sb.append(" and ROUND(ifnull(sum(" + TOTAL_SALES + ")/sum(" + ORDER_NUM + "), 0), 2) >= ?");
            argsList.add(qo.getAdvertisingUnitPriceMin());
        }
        if (qo.getAdvertisingUnitPriceMax() != null) {
            sb.append(" and ROUND(ifnull(sum(" + TOTAL_SALES + ")/sum(" + ORDER_NUM + "), 0), 2) <= ?");
            argsList.add(qo.getAdvertisingUnitPriceMax());
        }

        return sb.toString();
    }
}
