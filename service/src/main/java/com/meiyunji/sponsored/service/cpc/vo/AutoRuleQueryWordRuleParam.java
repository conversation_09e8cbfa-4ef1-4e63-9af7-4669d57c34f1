package com.meiyunji.sponsored.service.cpc.vo;

import com.meiyunji.sellfox.aadras.types.enumeration.RuleIndexType;
import com.meiyunji.sellfox.aadras.types.enumeration.RuleOperatorType;
import com.meiyunji.sellfox.aadras.types.enumeration.RuleStatisticalModeType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Data
@ApiModel
public class AutoRuleQueryWordRuleParam {

   private RuleOperatorType comparator;
   private RuleStatisticalModeType ruleStatisticalModeType;
   private RuleIndexType ruleIndex;
   private String value;
   private Integer day;
   private String afterValue;


}
