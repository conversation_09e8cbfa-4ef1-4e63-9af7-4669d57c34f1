package com.meiyunji.sponsored.service.enums;

public enum LogModuleEnum {
    SYSTEM("system","log.module.system"),
    ORDER("order","log.module.order"),
    CPC("cpc","log.module.cpc"),
    VCPM("vcpm","log.module.vcpm"),
    PRODUCT("product","log.module.product"),
    COMMODITY("commodity","log.module.commodity"),
    STATS("stats","log.module.stats"),
    MONITOR("rank", "log.module.monitor"),
    FBA("fba","log.module.fba"),
    WARE("ware","log.module.ware"),
    PURCHASE("purchase","log.module.purchase"),
    OVERSEA("oversea","log.module.oversea"),
    ;




    private String module;
    private String languageCode;

    LogModuleEnum(String module, String languageCode) {
        this.module = module;
        this.languageCode = languageCode;
    }

    public String getModule() {
        return module;
    }

    public String getLanguageCode() {
        return languageCode;
    }

    public String getLanguageCode(String module) {
        for (LogModuleEnum en : LogModuleEnum.values()) {
            if (en.getModule().equals(module)) {
                return en.languageCode;
            }
        }
        return null;
    }
}
