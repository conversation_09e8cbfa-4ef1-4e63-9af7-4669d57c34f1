package com.meiyunji.sponsored.service.syncTask.report.strategy;

import com.meiyunji.sellfox.aadas.types.message.notification.ReportReadyNotification;

/**
 * @author: wade
 * @date: 2021/12/28 16:30
 * @describe: 报告处理策略类
 */
public interface IReportProcessStrategy {

    /**
     * strategy assistive method
     * @param notification
     * @return
     */
    Boolean checkValid(ReportReadyNotification notification);

    /**
     * process advertise report data
     * @param notification
     */
    void processReport(ReportReadyNotification notification) throws Exception;
}
