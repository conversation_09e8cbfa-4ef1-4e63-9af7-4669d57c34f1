package com.meiyunji.sponsored.service.reportImport.model;

import com.alibaba.excel.annotation.ExcelProperty;
import com.meiyunji.sponsored.service.reportImport.listener.converter.CustomStringNumberConverter;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class LxAdGroupReport extends BaseLxReport {

    /**
     * 广告组
     */
    @ExcelProperty(value = "广告组", converter = CustomStringNumberConverter.class)
    private String adGroupName;

    /**
     * 花费-本币
     */
    @ExcelProperty(value = "花费-本币", converter = CustomStringNumberConverter.class)
    private String adCost;

    /**
     * 曝光量
     */
    @ExcelProperty(value = "曝光量", converter = CustomStringNumberConverter.class)
    private String impressions;

    /**
     * 点击量
     */
    @ExcelProperty(value = "点击", converter = CustomStringNumberConverter.class)
    private String clicks;

    /**
     * 广告订单
     */
    @ExcelProperty(value = "广告订单", converter = CustomStringNumberConverter.class)
    private String adOrder;

    /**
     * 直接成交订单
     */
    @ExcelProperty(value = "直接成交订单", converter = CustomStringNumberConverter.class)
    private String adSelfOrder;

    /**
     * 间接成交订单
     */
    @ExcelProperty(value = "间接成交订单", converter = CustomStringNumberConverter.class)
    private String adOtherOrder;

    /**
     * 销售额-本币
     */
    @ExcelProperty(value = "销售额-本币", converter = CustomStringNumberConverter.class)
    private String adSales;

    /**
     * 直接成交销售额-本币
     */
    @ExcelProperty(value = "直接成交销售额-本币", converter = CustomStringNumberConverter.class)
    private String adSelfSales;

    /**
     * 间接成交销售额-本币
     */
    @ExcelProperty(value = "间接成交销售额-本币", converter = CustomStringNumberConverter.class)
    private String adOtherSales;

    /**
     * 广告销量
     */
    @ExcelProperty(value = "广告销量", converter = CustomStringNumberConverter.class)
    private String adSaleNum;

    /**
     * 直接成交销量
     */
    @ExcelProperty(value = "直接成交销量", converter = CustomStringNumberConverter.class)
    private String adSelfSaleNum;

    /**
     * 间接成交销量
     */
    @ExcelProperty(value = "间接成交销量", converter = CustomStringNumberConverter.class)
    private String adOtherSaleNum;


    /**
     * 可见展示次数
     */
    @ExcelProperty(value = "可见展示次数", converter = CustomStringNumberConverter.class)
    private String viewImpressions;


    /**
     * DPV
     */
    @ExcelProperty(value = "DPV", converter = CustomStringNumberConverter.class)
    private String dpv;


    /**
     * vCTR(SB)
     */
    @ExcelProperty(value = "vCTR", converter = CustomStringNumberConverter.class)
    private String vctr;

    /**
     * VTR(SB)
     */
    @ExcelProperty(value = "VTR", converter = CustomStringNumberConverter.class)
    private String vtr;


    /**
     * 5秒观看次数
     */
    @ExcelProperty(value = "5秒观看次数", converter = CustomStringNumberConverter.class)
    private String video5SecondViews;


    /**
     * 5秒观看率
     */
    @ExcelProperty(value = "5秒观看率", converter = CustomStringNumberConverter.class)
    private String video5SecondViewRate;

    /**
     * 视频完整播放的次数
     */
    @ExcelProperty(value = "视频完整播放的次数", converter = CustomStringNumberConverter.class)
    private String videoCompleteViews;

    /**
     * 视频播放至四分之一的次数
     */
   @ExcelProperty(value = "视频播放至四分之一的次数", converter = CustomStringNumberConverter.class)
    private String videoFirstQuartileViews;

    /**
     * 视频播放至一半的次数
     */
    @ExcelProperty(value = "视频播放至一半的次数", converter = CustomStringNumberConverter.class)
    private String videoMidpointViews;

    /**
     * 视频播放至四分之三的次数
     */
    @ExcelProperty(value = "视频播放至四分之三的次数", converter = CustomStringNumberConverter.class)
    private String videoThirdQuartileViews;


    /**
     * 视频取消静音的次数
     */
    @ExcelProperty(value = "视频取消静音的次数", converter = CustomStringNumberConverter.class)
    private String videoUnmutes;


}
