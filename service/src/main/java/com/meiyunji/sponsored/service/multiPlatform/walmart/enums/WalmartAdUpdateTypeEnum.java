package com.meiyunji.sponsored.service.multiPlatform.walmart.enums;

import lombok.Getter;

/**
 * @author: ys
 * @date: 2025/3/10 13:54
 * @describe:
 */
@Getter
public enum WalmartAdUpdateTypeEnum {
    UPDATE_STATE(0, "更新状态"),
    UPDATE_BUDGET(1, "更新预算"),
    UPDATE_DATE(2, "更新日期"),
    UPDATE_NAME(3, "更新名称"),
    UPDATE_BID(4, "更新竞价"),
    ;
    private Integer code;
    private String msg;

    WalmartAdUpdateTypeEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public static WalmartAdUpdateTypeEnum getUpdateTypeByCode (int code) {
        for(WalmartAdUpdateTypeEnum en : WalmartAdUpdateTypeEnum.values()) {
            if (code == en.getCode()) {
                return en;
            }
        }
        return null;
    }
}
