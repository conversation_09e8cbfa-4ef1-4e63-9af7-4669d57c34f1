package com.meiyunji.sponsored.service.cpc.service2.impl.targettask;

import com.amazon.advertising.mode.targeting.Expression;
import com.amazon.advertising.mode.targeting.ExpressionEnum;
import com.amazon.advertising.sb.entity.targeting.*;
import com.amazon.advertising.sb.mode.targeting.SBExpression;
import com.amazon.advertising.sb.mode.targeting.Targeting;
import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.config.DynamicRefreshNacosConfiguration;
import com.meiyunji.sponsored.common.util.JSONUtil;
import com.meiyunji.sponsored.common.util.ResultUtil;
import com.meiyunji.sponsored.common.util.ThreadPoolUtil;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.account.service.IShopAuthService;
import com.meiyunji.sponsored.service.cpc.constants.AdTargetObjectTypeEnum;
import com.meiyunji.sponsored.service.cpc.constants.AdTargetTaskConstant;
import com.meiyunji.sponsored.service.cpc.constants.AdTargetTaskStatusEnum;
import com.meiyunji.sponsored.service.cpc.constants.TargetingTypeEnum;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdProfileDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonSbAdGroupDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonSbAdTargetingDao;
import com.meiyunji.sponsored.service.cpc.dto.AdTargetDetailDto;
import com.meiyunji.sponsored.service.cpc.dto.CommonAmazonAdTargeting;
import com.meiyunji.sponsored.service.cpc.po.*;
import com.meiyunji.sponsored.service.cpc.service.ICommonAmazonAdTargetingService;
import com.meiyunji.sponsored.service.cpc.service2.sb.ICpcSbGroupService;
import com.meiyunji.sponsored.service.cpc.service2.sb.ICpcSbTargetService;
import com.meiyunji.sponsored.service.cpc.service2.sb.impl.CpcSbTargetApiService;
import com.meiyunji.sponsored.service.cpc.util.CpcApiHelper;
import com.meiyunji.sponsored.service.log.enums.OperationLogResultEnum;
import com.meiyunji.sponsored.service.log.po.AdManageOperationLog;
import com.meiyunji.sponsored.service.log.service.IAdManageOperationLogService;
import com.meiyunji.sponsored.service.util.AmazonErrorUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: nongwenhua
 * @email: <EMAIL>
 * @create: 2024-05-08 19:23
 */
@Service(AdTargetTaskConstant.SB_TARGET_HANDLER)
@Slf4j
public class SbTargetHandler implements TargetTaskHandler {

    @Autowired
    private ICommonAmazonAdTargetingService commonAmazonAdTargetingService;
    @Autowired
    private IAmazonSbAdTargetingDao amazonSbAdTargetingDao;
    @Autowired
    private TargetTaskComponent targetTaskComponent;
    @Autowired
    private IAmazonSbAdGroupDao amazonSbAdGroupDao;
    @Autowired
    private IAmazonAdProfileDao amazonAdProfileDao;
    @Autowired
    private IScVcShopAuthDao shopAuthDao;
    @Autowired
    private CpcApiHelper cpcApiHelper;
    @Autowired
    private DynamicRefreshNacosConfiguration dynamicRefreshNacosConfiguration;
    @Autowired
    private CpcSbTargetApiService cpcSbTargetApiService;
    @Autowired
    private IShopAuthService shopAuthService;
    @Autowired
    private ICpcSbTargetService cpcSbTargetService;
    @Autowired
    private ICpcSbGroupService cpcSbGroupService;
    @Autowired
    private IAdManageOperationLogService adOperationLogService;

    private static int MAX_SYNC_BATCH_CAMPAIGN_ID_LIMIT = 100;

    @Override
    public void handle(AdTargetTask adTargetTask, List<AdTargetTaskDetail> adTargetTaskDetails) {
        //处理业务返回结果
        createTargeting(adTargetTask, adTargetTaskDetails);
    }

    private void createTargeting(AdTargetTask adTargetTask, List<AdTargetTaskDetail> adTargetTaskDetails) {
        List<AdTargetTaskDetail> needUpdateDetailList = new ArrayList<>();
        if (CollectionUtils.isEmpty(adTargetTaskDetails)) {
            targetTaskComponent.updateTaskStatus(adTargetTask, needUpdateDetailList);
            return;
        }

        // 排除已存在的关键词
        Set<String> adGroupIdSet = new HashSet<>();
        Map<Long, AdTargetTaskDetail> adTargetTaskDetailMap = new HashMap<>();
        for (AdTargetTaskDetail targetTaskDetail : adTargetTaskDetails) {
            adGroupIdSet.add(targetTaskDetail.getAdGroupId());
            adTargetTaskDetailMap.put(targetTaskDetail.getId(), targetTaskDetail);
        }

        if (CollectionUtils.isEmpty(adGroupIdSet)) {
            targetTaskComponent.updateTaskStatus(adTargetTask, needUpdateDetailList);
            return;
        }

        List<AmazonSbAdGroup> amazonSbAdGroups = amazonSbAdGroupDao.getAdGroupByIds(adTargetTask.getPuid(), adTargetTask.getShopId(), new ArrayList<>(adGroupIdSet));
        Map<String, AmazonSbAdGroup> amazonAdGroupMap = amazonSbAdGroups.stream().collect(Collectors.toMap(AmazonSbAdGroup::getAdGroupId, Function.identity(), (newVal, oldVal) -> newVal));

        int originalTaskDetailSize = adTargetTaskDetails.size();
        List<AmazonSbAdTargeting> amazonAdTargetings = convertAddTargetingVoToPO(
                adTargetTask.getUid(), amazonAdGroupMap, adTargetTaskDetails, needUpdateDetailList, adTargetTask.getTargetingType());
        if (CollectionUtils.isEmpty(amazonAdTargetings)) {
            targetTaskComponent.updateTaskStatus(adTargetTask, needUpdateDetailList);
            return;
        }

        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(adTargetTask.getShopId(), adTargetTask.getPuid());
        if (shop == null) {
            adTargetTaskDetails.forEach(each -> {
                each.setFailureReason("店铺不存在");
                each.setStatus(AdTargetTaskStatusEnum.FAILURE.getCode());
                needUpdateDetailList.add(each);
            });
            targetTaskComponent.updateTaskStatus(adTargetTask, needUpdateDetailList);
            return;
        }

        AmazonAdProfile profile = amazonAdProfileDao.getProfile(adTargetTask.getPuid(), adTargetTask.getShopId());
        if (profile == null) {
            adTargetTaskDetails.forEach(each -> {
                each.setFailureReason("没有站点对应的配置信息");
                each.setStatus(AdTargetTaskStatusEnum.FAILURE.getCode());
                needUpdateDetailList.add(each);
            });
            targetTaskComponent.updateTaskStatus(adTargetTask, needUpdateDetailList);
            return;
        }

        int failureNum = 0;
        List<List<AmazonSbAdTargeting>> amazonAdTargetingPartition = Lists.partition(amazonAdTargetings, AdTargetTaskConstant.MAX_SB_TARGET_SIZE);
        for (List<AmazonSbAdTargeting> amazonAdTargetingList : amazonAdTargetingPartition) {
            Result result = create(amazonAdTargetingList, adTargetTaskDetailMap, shop, profile);
            logSbTargetCreate(amazonAdTargetingList, adTargetTask.getLoginIp(), result);
            for (AmazonSbAdTargeting amazonSbAdTargeting : amazonAdTargetingList) {
                AdTargetTaskDetail adTargetTaskDetail = adTargetTaskDetailMap.get(amazonSbAdTargeting.getTargetTaskDetailId());
                needUpdateDetailList.add(adTargetTaskDetail);
                if (AdTargetTaskStatusEnum.FAILURE.getCode() == adTargetTaskDetail.getStatus()) {
                    failureNum++;
                }
            }
            if (result.success()) {
                List<AmazonSbAdTargeting> succList = amazonAdTargetingList.stream().filter(e -> StringUtils.isNotBlank(e.getTargetId()))
                        .collect(Collectors.toList());
                if (succList.size() > 0) {
                    // 有可能已经添加过了
                    List<String> existInDB = amazonSbAdTargetingDao.listByTargetId(adTargetTask.getPuid(), adTargetTask.getShopId(), succList.stream()
                                    .map(AmazonSbAdTargeting::getTargetId).collect(Collectors.toList())).stream()
                            .map(AmazonSbAdTargeting::getTargetId).collect(Collectors.toList());

                    // 排除掉已有的
                    if (CollectionUtils.isNotEmpty(existInDB)) {
                        succList = succList.stream().filter(e -> !existInDB.contains(e.getTargetId())).collect(Collectors.toList());
                    }

                    // 入库
                    try {
                        amazonSbAdTargetingDao.batchAdd(adTargetTask.getPuid(), succList);
                    } catch (Exception e) {
                        log.error("createSbTargeting:", e);
                    }

                    //创建成功, 需要在同步 获取投放状态
                    Map<String, List<String>> adCampaignId2TargetIds = new HashMap<>();
                    for (AmazonSbAdTargeting amazonSbAdTargeting : succList) {
                        String adGroupId = amazonSbAdTargeting.getAdGroupId();
                        if (StringUtils.isBlank(adGroupId)) {
                            continue;
                        }
                        AmazonSbAdGroup amazonSbAdGroup = amazonAdGroupMap.get(adGroupId);
                        if (amazonSbAdGroup == null) {
                            continue;
                        }
                        String campaignId = amazonSbAdGroup.getCampaignId();
                        List<String> targetIds = adCampaignId2TargetIds.get(campaignId);
                        if (CollectionUtils.isEmpty(targetIds)) {
                            targetIds = new ArrayList<>();
                        }
                        targetIds.add(amazonSbAdTargeting.getTargetId());
                        adCampaignId2TargetIds.put(campaignId, targetIds);
                    }
                    List<String> adCampaignIds = new ArrayList<>(adCampaignId2TargetIds.keySet());
                    for (List<String> subAdCampaignIds : Lists.partition(adCampaignIds, MAX_SYNC_BATCH_CAMPAIGN_ID_LIMIT)) {
                        ThreadPoolExecutor pool = ThreadPoolUtil.getSbTargetSyncPool();
                        pool.execute(() -> {
                            cpcSbTargetApiService.syncTargets(shop, String.join(",", subAdCampaignIds));
                            List<String> targetIds = subAdCampaignIds.stream().map(adCampaignId2TargetIds::get).flatMap(Collection::stream).collect(Collectors.toList());
                            cpcSbTargetService.saveDoris(adTargetTask.getPuid(), adTargetTask.getShopId(), targetIds);
                        });
                    }
                    //同步广告组投放类型字段
                    try {
                        List<AmazonSbAdGroup> sbAdGroups = succList.stream().map(AmazonSbAdTargeting::getAdGroupId).distinct().map(amazonAdGroupMap::get)
                                .filter(each -> StringUtils.isBlank(each.getAdGroupType())).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(sbAdGroups)) {
                            sbAdGroups.forEach(each -> each.setAdGroupType("product"));
                            amazonSbAdGroupDao.batchUpdateAdGroupType(adTargetTask.getPuid(), sbAdGroups);
                            cpcSbGroupService.saveDoris(null, sbAdGroups);
                        }
                    } catch (Exception e) {
                        log.error("updateGroupTargetType:", e);
                    }
                }
            }
            targetTaskComponent.updateTaskDetailStatus(adTargetTask, needUpdateDetailList);
            needUpdateDetailList.clear();
        }

        failureNum += originalTaskDetailSize - amazonAdTargetings.size();
        int adTargetTaskStatus = targetTaskComponent.getAdTargetTaskStatus(originalTaskDetailSize, failureNum);
        targetTaskComponent.updateTaskStatus(adTargetTask, adTargetTaskStatus);
    }

    private void logSbTargetCreate(List<AmazonSbAdTargeting> targetList, String ip, Result result) {
        try {
            if (CollectionUtils.isEmpty(targetList)) {
                return;
            }
            List<AdManageOperationLog> operationLogs = Lists.newArrayList();
            for (AmazonSbAdTargeting targeting : targetList) {
                AdManageOperationLog targetLog = adOperationLogService.getSbTargetLog(null, targeting);
                targetLog.setIp(ip);
                if (StringUtils.isNotBlank(targeting.getTargetId())) {
                    targetLog.setResult(OperationLogResultEnum.SUCCESS.getResultValue());
                } else {
                    targetLog.setResult(OperationLogResultEnum.FAIL.getResultValue());
                    if (StringUtils.isNotBlank(targeting.getErrMsg())) {
                        targetLog.setResultInfo(targeting.getErrMsg());
                    } else {
                        targetLog.setResultInfo(result.getMsg());
                    }
                }
                operationLogs.add(targetLog);
            }
            adOperationLogService.batchLogsMergeByAdGroup(operationLogs);
        } catch (Exception e) {
            log.error("SB商品投放创建日志异常", e);
        }
    }

    private Result create(List<AmazonSbAdTargeting> amazonSbAdTargetings, Map<Long, AdTargetTaskDetail> adTargetTaskDetailMap, ShopAuth shop, AmazonAdProfile profile) {
        List<Targeting> targetingList = amazonSbAdTargetings.stream().map(e -> {
            Targeting targeting = new Targeting();
            if (StringUtils.isNotBlank(e.getAdGroupId())) {
                targeting.setAdGroupId(Long.valueOf(e.getAdGroupId()));
            }
            if (StringUtils.isNotBlank(e.getCampaignId())) {
                targeting.setCampaignId(Long.valueOf(e.getCampaignId()));
            }
            if (e.getBid() != null) {
                targeting.setBid(e.getBid().doubleValue());
            }
            if (StringUtils.isNotBlank(e.getExpression())) {
                targeting.setExpressions(JSONUtil.jsonToArray(e.getExpression(), SBExpression.class));
            }
            return targeting;
        }).collect(Collectors.toList());

        CreateTargetResponse response = cpcApiHelper.call(shop, () -> TargetClient.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).create(shopAuthService.getAdToken(shop), profile.getProfileId(),
                shop.getMarketplaceId(), targetingList));

        if (response == null) {
            for (AmazonSbAdTargeting amazonAdTargeting : amazonSbAdTargetings) {
                AdTargetTaskDetail adTargetTaskDetail = adTargetTaskDetailMap.get(amazonAdTargeting.getTargetTaskDetailId());
                adTargetTaskDetail.setFailureReason("网络延迟，请稍后重试");
                adTargetTaskDetail.setStatus(AdTargetTaskStatusEnum.FAILURE.getCode());
            }
            return ResultUtil.returnErr("网络延迟，请稍后重试");
        }

        //处理返回结果中的错误信息
        String errMsg = "网络延迟，请稍后重试";
        String formatErrMsg = errMsg;
        SbCreateTargetResult result = response.getResult();
        if (result != null) {
            List<CreateTargetSuccessResults> succList = result.getCreateTargetSuccessResults();
            List<CreateTargetErrorResults> failList = result.getCreateTargetErrorResults();

            if (CollectionUtils.isEmpty(succList) && CollectionUtils.isEmpty(failList)) {
                String returnErrMsg = errMsg;
                if (StringUtils.isNotBlank(result.getDetails())) {
                    returnErrMsg = response.getResult().getDetails();
                } else if (StringUtils.isNotBlank(response.getResult().getDescription())) {
                    returnErrMsg = response.getResult().getDescription();
                }
                errMsg = AmazonErrorUtils.getError(returnErrMsg);
                formatErrMsg = targetTaskComponent.getError(errMsg, returnErrMsg);
            } else {
                if (CollectionUtils.isNotEmpty(succList)) {
                    for (CreateTargetSuccessResults results : succList) {
                        Integer index = results.getTargetRequestIndex();
                        AmazonSbAdTargeting amazonAdTargeting = amazonSbAdTargetings.get(index);
                        amazonAdTargeting.setTargetId(String.valueOf(results.getTargetId()));
                        AdTargetTaskDetail adTargetTaskDetail = adTargetTaskDetailMap.get(amazonAdTargeting.getTargetTaskDetailId());
                        adTargetTaskDetail.setStatus(AdTargetTaskStatusEnum.SUCCESS.getCode());
                    }
                }
                if (CollectionUtils.isNotEmpty(failList)) {
                    for (CreateTargetErrorResults results : failList) {
                        Integer index = results.getTargetRequestIndex();
                        String returnErrorMsg = StringUtils.defaultIfBlank(results.getDetails(), results.getDescription());
                        String errorMsg = AmazonErrorUtils.getError(returnErrorMsg);
                        AmazonSbAdTargeting amazonAdTargeting = amazonSbAdTargetings.get(index);
                        amazonAdTargeting.setErrMsg("第" + (index + 1) + "个:" + errorMsg);
                        AdTargetTaskDetail adTargetTaskDetail = adTargetTaskDetailMap.get(amazonAdTargeting.getTargetTaskDetailId());
                        adTargetTaskDetail.setFailureReason(targetTaskComponent.getError(errorMsg, returnErrorMsg));
                        adTargetTaskDetail.setFailureReasonDetail(returnErrorMsg);
                        adTargetTaskDetail.setStatus(AdTargetTaskStatusEnum.FAILURE.getCode());
                    }
                }
                return ResultUtil.success();
            }

        }
        for (AmazonSbAdTargeting amazonAdTargeting : amazonSbAdTargetings) {
            AdTargetTaskDetail adTargetTaskDetail = adTargetTaskDetailMap.get(amazonAdTargeting.getTargetTaskDetailId());
            adTargetTaskDetail.setFailureReason(formatErrMsg);
            adTargetTaskDetail.setFailureReasonDetail(errMsg);
            adTargetTaskDetail.setStatus(AdTargetTaskStatusEnum.FAILURE.getCode());
        }

        return ResultUtil.returnErr(errMsg);
    }

    private List<AmazonSbAdTargeting> convertAddTargetingVoToPO(Integer uid, Map<String, AmazonSbAdGroup> amazonAdGroupMap, List<AdTargetTaskDetail> adTargetTaskDetails, List<AdTargetTaskDetail> needUpdateDetailList, String targetingType) {
        List<AmazonSbAdTargeting> amazonAdTargetList = new ArrayList<>(adTargetTaskDetails.size());
        AmazonSbAdTargeting target;

        Expression expression;
        Iterator<AdTargetTaskDetail> it = adTargetTaskDetails.iterator();
        AdTargetTaskDetail adTargetTaskDetail;
        List<String> targetIds = adTargetTaskDetails.stream().map(AdTargetTaskDetail::getTargetId).distinct().collect(Collectors.toList());
        AdTargetTaskDetail first = adTargetTaskDetails.get(0);
        List<Integer> sourceShopIds = adTargetTaskDetails.stream().map(AdTargetTaskDetail::getSourceShopId).distinct().collect(Collectors.toList());
        Map<String, AdTargetDetailDto> targetDetailMap = buildTargetDetailMap(first.getPuid(), targetIds, sourceShopIds, targetingType);
        while (it.hasNext()) {
            adTargetTaskDetail = it.next();
            AmazonSbAdGroup amazonSbAdGroup = amazonAdGroupMap.get(adTargetTaskDetail.getAdGroupId());
            if (amazonSbAdGroup == null) {
                adTargetTaskDetail.setStatus(AdTargetTaskStatusEnum.FAILURE.getCode());
                adTargetTaskDetail.setFailureReason("广告组不存在");
                needUpdateDetailList.add(adTargetTaskDetail);
                it.remove();
            } else {
                String type = AdTargetObjectTypeEnum.getTargetTypeByCode(adTargetTaskDetail.getTargetObjectType());
                target = new AmazonSbAdTargeting();
                amazonAdTargetList.add(target);

                target.setPuid(amazonSbAdGroup.getPuid());
                target.setShopId(amazonSbAdGroup.getShopId());
                target.setMarketplaceId(amazonSbAdGroup.getMarketplaceId());
                target.setProfileId(amazonSbAdGroup.getProfileId());
                target.setAdGroupId(amazonSbAdGroup.getAdGroupId());
                target.setCampaignId(amazonSbAdGroup.getCampaignId());
                target.setType(type);
                target.setBid(adTargetTaskDetail.getBid());
                target.setCreateId(uid);
                target.setCreateInAmzup(1);
                target.setTargetTaskDetailId(adTargetTaskDetail.getId());

                if (adTargetTaskDetail.getSuggested() != null) {
                    target.setSuggested(adTargetTaskDetail.getSuggested());
                }
                if (adTargetTaskDetail.getRangeStart() != null) {
                    target.setRangeStart(adTargetTaskDetail.getRangeStart());
                }
                if (adTargetTaskDetail.getRangeEnd() != null) {
                    target.setRangeEnd(adTargetTaskDetail.getRangeEnd());
                }

                List<Expression> expressions = new ArrayList<>();
                if (SdTargetTypeEnum.asin.name().equals(type)) {
                    expression = new Expression();
                    expression.setType(ExpressionEnum.asinSameAs.value());
                    expressions.add(expression);
                    if (StringUtils.isEmpty(adTargetTaskDetail.getTargetId())) {
                        expression.setValue(adTargetTaskDetail.getTargetObject());
                        target.setTargetText(adTargetTaskDetail.getTargetObject());
                        target.setImgUrl(adTargetTaskDetail.getImgUrl());
                        target.setTitle(adTargetTaskDetail.getTargetObjectDesc());
                    } else {
                        AdTargetDetailDto adTargetDetail = targetDetailMap.get(adTargetTaskDetail.getTargetId());
                        expression.setValue(adTargetDetail.getTargetObject());
                        target.setTargetText(adTargetDetail.getTargetObject());
                        target.setImgUrl(adTargetDetail.getImgUrl());
                        target.setTitle(adTargetDetail.getTargetObjectDesc());
                    }
                } else {
                    // 类目投放任务肯定包含targetId
                    AdTargetDetailDto adTargetDetail = targetDetailMap.get(adTargetTaskDetail.getTargetId());
                    expression = new Expression();
                    expression.setType(ExpressionEnum.asinCategorySameAs.value());
                    expression.setValue(adTargetDetail.getCategoryId());
                    expressions.add(expression);
                    target.setTargetText(adTargetDetail.getTargetObject());
                    target.setCategoryName(adTargetDetail.getCategoryName());
                    target.setBrandName(adTargetDetail.getBrandName());
                    target.setMinPrice(adTargetDetail.getMinPrice());
                    target.setMaxPrice(adTargetDetail.getMaxPrice());
                    if (StringUtils.isNotBlank(adTargetDetail.getMinReviewRating())) {
                        target.setMinReviewRating(Integer.valueOf(adTargetDetail.getMinReviewRating()));
                    }
                    if (StringUtils.isNotBlank(adTargetDetail.getMaxReviewRating())) {
                        target.setMaxReviewRating(Integer.valueOf(adTargetDetail.getMaxReviewRating()));
                    }

                    if (StringUtils.isNotBlank(adTargetDetail.getBrandId())) {
                        expression = new Expression();
                        expression.setType(ExpressionEnum.asinBrandSameAs.value());
                        expression.setValue(adTargetDetail.getBrandId());
                        expressions.add(expression);
                    }
                    if (StringUtils.isNotBlank(adTargetDetail.getMinPrice()) || StringUtils.isNotBlank(adTargetDetail.getMaxPrice())) {
                        expression = new Expression();
                        if (StringUtils.isBlank(adTargetDetail.getMinPrice())) {
                            expression.setType(ExpressionEnum.asinPriceLessThan.value());
                            expression.setValue(adTargetDetail.getMaxPrice());
                            expressions.add(expression);
                        } else if (StringUtils.isBlank(adTargetDetail.getMaxPrice())) {
                            expression.setType(ExpressionEnum.asinPriceGreaterThan.value());
                            expression.setValue(adTargetDetail.getMinPrice());
                            expressions.add(expression);
                        } else {
                            expression.setType(ExpressionEnum.asinPriceBetween.value());
                            expression.setValue(adTargetDetail.getMinPrice() + "-" + adTargetDetail.getMaxPrice());
                            expressions.add(expression);
                        }
                    }
                    if (adTargetDetail.getMinReviewRating() != null || adTargetDetail.getMaxReviewRating() != null) {
                        expression = new Expression();
                        if (adTargetDetail.getMinReviewRating() == null) {
                            expression.setType(ExpressionEnum.asinReviewRatingLessThan.value());
                            expression.setValue(String.valueOf(adTargetDetail.getMaxReviewRating()));
                            expressions.add(expression);
                        } else if (adTargetDetail.getMaxReviewRating() == null) {
                            expression.setType(ExpressionEnum.asinReviewRatingGreaterThan.value());
                            expression.setValue(String.valueOf(adTargetDetail.getMinReviewRating()));
                            expressions.add(expression);
                        } else {
                            expression.setType(ExpressionEnum.asinReviewRatingBetween.value());
                            expression.setValue(adTargetDetail.getMinReviewRating() + "-" + adTargetDetail.getMaxReviewRating());
                            expressions.add(expression);
                        }
                    }
                    if (adTargetDetail.getAsinIsPrimeShippingEligible() != null) {
                        expression = new Expression();
                        expression.setType(ExpressionEnum.asinIsPrimeShippingEligible.value());
                        expression.setValue(adTargetDetail.getAsinIsPrimeShippingEligible());
                        expressions.add(expression);
                    }
                }
                target.setExpression(JSONUtil.objectToJson(expressions));

            }
        }

        return amazonAdTargetList;
    }

    @Override
    public Map<String, AdTargetDetailDto> buildTargetDetailMap(Integer puid, List<String> targetIds, List<Integer> sourceShopIds, String targetingType) {
        List<CommonAmazonAdTargeting> list = commonAmazonAdTargetingService.listByTargetIds(targetingType, puid, sourceShopIds, targetIds);
        return list.stream().map(each -> {
            AdTargetDetailDto adTargetDetail = new AdTargetDetailDto();
            adTargetDetail.setTargetId(each.getTargetId());
            String type = each.getType();
            if (TargetTypeEnum.asin.name().equals(type)) {
                adTargetDetail.setTargetObject(each.getTargetingValue());
                adTargetDetail.setTargetObjectDesc(each.getTitle());
                adTargetDetail.setImgUrl(each.getImgUrl());
            } else if (TargetTypeEnum.category.name().equals(type)) {
                adTargetDetail.setTargetObject(each.getTargetingValue());
                boolean isSp = TargetingTypeEnum.SP_TARGETING_CODE_LIST.contains(targetingType);
                targetTaskComponent.fillCategoryDetail(adTargetDetail, each, isSp);
            }
            return adTargetDetail;
        }).collect(Collectors.toMap(AdTargetDetailDto::getTargetId, Function.identity(), (newVal, oldVal) -> newVal));
    }
}
