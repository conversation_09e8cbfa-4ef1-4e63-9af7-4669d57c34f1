package com.meiyunji.sponsored.service.multiPlatform.walmart.po;


import java.io.Serializable;
import java.util.Date;

/**
 * @author: pxq
 * @date: 2025/02/24
 * @Description: walmart广告活动产品高级洞察
 */
public class WalmartAdvertisingItemInsights implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;

    /**
     * 商户uid
     */
    private Integer puid;

    /**
     * 店铺ID
     */
    private Long shopId;

    /**
     * 报告日期
     */
    private Date reportDate;

    /**
     * 产品id
     */
    private String catalogItemId;

    /**
     * 指示是否可以将产品添加到购物车
     */
    private String availabilityStatus;

    /**
     * 平均评分
     */
    private Double averageRating;

    /**
     * 赢得BuyBox的价格
     */
    private Double buyboxWinnerPrice;

    /**
     * 产品可用于交易的页面浏览百分比
     */
    private String pageViewBasedAvailabilityRate;

    /**
     * 部门名称
     */
    private String pageViewBasedBuyboxWinRate;

    /**
     * 评论数量
     */
    private Integer reviewCount;

    private String itemImageUrl;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    public String getIdStr() {
        return id == null ? null : id.toString();
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getPuid() {
        return puid;
    }

    public void setPuid(Integer puid) {
        this.puid = puid;
    }

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public Date getReportDate() {
        return reportDate;
    }

    public void setReportDate(Date reportDate) {
        this.reportDate = reportDate;
    }

    public String getCatalogItemId() {
        return catalogItemId;
    }

    public void setCatalogItemId(String catalogItemId) {
        this.catalogItemId = catalogItemId;
    }

    public String getAvailabilityStatus() {
        return availabilityStatus;
    }

    public void setAvailabilityStatus(String availabilityStatus) {
        this.availabilityStatus = availabilityStatus;
    }

    public Double getAverageRating() {
        return averageRating;
    }

    public void setAverageRating(Double averageRating) {
        this.averageRating = averageRating;
    }

    public Double getBuyboxWinnerPrice() {
        return buyboxWinnerPrice;
    }

    public void setBuyboxWinnerPrice(Double buyboxWinnerPrice) {
        this.buyboxWinnerPrice = buyboxWinnerPrice;
    }

    public String getPageViewBasedAvailabilityRate() {
        return pageViewBasedAvailabilityRate;
    }

    public void setPageViewBasedAvailabilityRate(String pageViewBasedAvailabilityRate) {
        this.pageViewBasedAvailabilityRate = pageViewBasedAvailabilityRate;
    }

    public String getPageViewBasedBuyboxWinRate() {
        return pageViewBasedBuyboxWinRate;
    }

    public void setPageViewBasedBuyboxWinRate(String pageViewBasedBuyboxWinRate) {
        this.pageViewBasedBuyboxWinRate = pageViewBasedBuyboxWinRate;
    }

    public Integer getReviewCount() {
        return reviewCount;
    }

    public void setReviewCount(Integer reviewCount) {
        this.reviewCount = reviewCount;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getItemImageUrl() {
        return itemImageUrl;
    }

    public void setItemImageUrl(String itemImageUrl) {
        this.itemImageUrl = itemImageUrl;
    }
}
