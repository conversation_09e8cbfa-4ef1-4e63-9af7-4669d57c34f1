package com.meiyunji.sponsored.service.reportImport.enums;

public enum ReportImportErrType {
    //无效表头
    HEAD_INVALID,
    //未知异常
    EXCEPTION,
    //重复活动
    DUPLICATION_CAMPAIGN,
    //店铺名称匹配错误
    SHOP_ERROR;


    public static ReportImportErrType getByValue(String value) {
        for (ReportImportErrType reportImportErrType : values()) {
            if (reportImportErrType.name().equals(value)) {
                return reportImportErrType;
            }
        }
        return null;
    }
}
