package com.meiyunji.sponsored.service.multiPlatform.walmart.po;

import com.walmart.oms.model.WalmartPricing;

import java.util.List;

public class WalmartBulkPrice {

    private String sku;

    private List<WalmartPricing> pricing;

    public WalmartBulkPrice() {
    }

    public WalmartBulkPrice(String sku, List<WalmartPricing> pricing) {
        this.sku = sku;
        this.pricing = pricing;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public List<WalmartPricing> getPricing() {
        return pricing;
    }

    public void setPricing(List<WalmartPricing> pricing) {
        this.pricing = pricing;
    }
}
