package com.meiyunji.sponsored.service.cpc.service2.targetable.impl;

import com.amazon.advertising.sd.constant.TargetableTypeEnum;
import com.amazon.advertising.sd.entity.creatives.CreativesResult;
import com.amazon.advertising.sd.targeting.targetable.ListTargetableEntitiesResponse;
import com.amazon.advertising.sd.targeting.targetable.ListTargetableEntitiyPathsResponse;
import com.amazon.advertising.sd.targeting.targetable.TargetableEntities;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.util.ParamCopyUtil;
import com.meiyunji.sponsored.common.util.ResultUtil;
import com.meiyunji.sponsored.rpc.targetable.targetableEntities.*;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdProfileDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdProfile;
import com.meiyunji.sponsored.service.cpc.service2.TargetableEntitiesApiService;
import com.meiyunji.sponsored.service.cpc.service2.targetable.CpcTargetableService;
import com.meiyunji.sponsored.service.enums.TargetableCampaignTypeEnum;
import com.meiyunji.sponsored.service.enums.TargetableTargetTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: ys
 * @date: 2024/9/4 19:16
 * @describe:
 */
@Service
@Slf4j
public class CpcTargetableServiceImpl implements CpcTargetableService {

    @Autowired
    private TargetableEntitiesApiService targetableEntitiesApiService;

    @Autowired
    private IScVcShopAuthDao shopAuthDao;

    @Autowired
    private IAmazonAdProfileDao amazonAdProfileDao;

    @Override
    public Result<TargetableEntitiesListVo> getSearchList(Integer shopId, Integer puid,
                                                          String queryWord, String campaignType,
                                                          List<Integer> targetType, List<List<String>> pathFilterList) {
        if (shopId == 0 || puid == 0
                || StringUtils.isBlank(campaignType)) {
            return ResultUtil.returnErr("请求参数错误");
        }
        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(shopId, puid);

        if (shop == null) {
            return ResultUtil.returnErr("没有CPC授权");
        }

        AmazonAdProfile profile = amazonAdProfileDao.getProfile(puid, shopId);
        if (profile == null) {
            return ResultUtil.returnErr("没有站点对应的配置信息");
        }
        //获取广告类型数据
        TargetableCampaignTypeEnum campaignTypeEnum = TargetableCampaignTypeEnum.getTargetableCampaignTypeEnumByCode(campaignType);
        //获取投放类型数据
        List<TargetableTypeEnum> targetTypeEnumList = Optional.ofNullable(targetType).filter(CollectionUtils::isNotEmpty)
                .map(l -> l.stream().map(TargetableTargetTypeEnum::getTargetableTargetTypeEnumByCode)
                        .filter(Objects::nonNull).map(TargetableTargetTypeEnum::getType).collect(Collectors.toList())).orElse(null);
        //处理查询路径
        List<List<String>> pathFilters = null;
        if (CollectionUtils.isNotEmpty(pathFilterList)) {
            pathFilters = pathFilterList;
        }
        Result<ListTargetableEntitiesResponse> result = targetableEntitiesApiService.getSearchList(shop, profile,
                queryWord, Optional.ofNullable(campaignTypeEnum).map(TargetableCampaignTypeEnum::getVal).orElse(null), targetTypeEnumList, pathFilters);
        if (!result.success()) {
            return ResultUtil.returnErr(result.getMsg());
        }

        //组织返回数据
        TargetableEntitiesListVo.Builder builder = TargetableEntitiesListVo.newBuilder();
        ListTargetableEntitiesResponse targetableResult = result.getData();
        if (Objects.nonNull(targetableResult)) {
            Integer totalResultSize = targetableResult.getTotalResults();
            List<TargetableEntities> entitiesList = targetableResult.getTargetableEntities();
            String nextToken = targetableResult.getNextToken();
            if (CollectionUtils.isNotEmpty(entitiesList)) {
                builder.addAllTargetableEntities(entitiesList.stream().map(e -> {
                    com.meiyunji.sponsored.rpc.targetable.targetableEntities.TargetableEntities.Builder tBuilder =
                            com.meiyunji.sponsored.rpc.targetable.targetableEntities.TargetableEntities.newBuilder();
                    BeanUtils.copyProperties(e, tBuilder, ParamCopyUtil.checkPropertiesNullOrEmpty(e));
                    tBuilder.addAllPath(e.getPath());
                    return tBuilder.build();
                }).collect(Collectors.toList()));
            }
            Optional.ofNullable(totalResultSize).ifPresent(builder::setTotalResults);
            Optional.ofNullable(nextToken).ifPresent(builder::setNextToken);
        }

        return ResultUtil.returnSucc(builder.build());
    }

    @Override
    public Result<TargetableEntityPathListVo> getPathList(Integer shopId, Integer puid,
                                                          String campaignType, List<List<String>> pathFilterList) {
        if (shopId == 0 || puid == 0
                || StringUtils.isBlank(campaignType)) {
            return ResultUtil.returnErr("请求参数错误");
        }
        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(shopId, puid);

        if (shop == null) {
            return ResultUtil.returnErr("没有CPC授权");
        }

        AmazonAdProfile profile = amazonAdProfileDao.getProfile(puid, shopId);
        if (profile == null) {
            return ResultUtil.returnErr("没有站点对应的配置信息");
        }
        //获取广告类型数据
        TargetableCampaignTypeEnum campaignTypeEnum = TargetableCampaignTypeEnum.getTargetableCampaignTypeEnumByCode(campaignType);

        //处理查询路径
        List<List<String>> pathFilters = null;
        if (CollectionUtils.isNotEmpty(pathFilterList)) {
            pathFilters = pathFilterList;
        }
        Result<ListTargetableEntitiyPathsResponse> result = targetableEntitiesApiService.getPathList(shop, profile,
                Optional.ofNullable(campaignTypeEnum).map(TargetableCampaignTypeEnum::getVal).orElse(null), pathFilters);
        if (!result.success()) {
            return ResultUtil.returnErr(result.getMsg());
        }

        //组织返回数据
        TargetableEntityPathListVo.Builder builder = TargetableEntityPathListVo.newBuilder();
        ListTargetableEntitiyPathsResponse pathListResult = result.getData();
        if (Objects.nonNull(pathListResult)) {
            Integer totalResultSize = pathListResult.getTotalResults();
            List<List<String>> pathList = pathListResult.getPaths();
            if (CollectionUtils.isNotEmpty(pathList)) {
                builder.addAllCategories(pathList.stream().filter(CollectionUtils::isNotEmpty).map(e -> {
                    TargetableEntityPath.Builder tBuilder =
                            TargetableEntityPath.newBuilder();
                    tBuilder.addAllPath(e);
                    return tBuilder.build();
                }).collect(Collectors.toList()));
            }
            Optional.ofNullable(totalResultSize).ifPresent(builder::setTotalResults);
        }

        return ResultUtil.returnSucc(builder.build());
    }
}
