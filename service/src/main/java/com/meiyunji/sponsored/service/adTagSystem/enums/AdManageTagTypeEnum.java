package com.meiyunji.sponsored.service.adTagSystem.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @author: liweibin
 * @email: <EMAIL>
 * @date: 2024-10-23  15:54
 */
@AllArgsConstructor
@Getter
public enum AdManageTagTypeEnum {
    CAMPAIGN(0, "campaign","广告活动");

    private Integer code;
    private String type;
    private String desc;

    public static Integer getCodeByType(String type) {
        for (AdManageTagTypeEnum e : AdManageTagTypeEnum.values()) {
            if (e.type.equals(type)) {
                return e.code;
            }
        }
        return null;
    }
}
