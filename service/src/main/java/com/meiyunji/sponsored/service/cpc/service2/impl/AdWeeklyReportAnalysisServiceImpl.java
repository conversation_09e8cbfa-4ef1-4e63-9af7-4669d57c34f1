package com.meiyunji.sponsored.service.cpc.service2.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.amazon.advertising.mode.MarketTimezoneAndCurrencyEnum;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.protobuf.Int32Value;
import com.meiyunji.sellfox.ams.api.service.*;
import com.meiyunji.sponsored.common.config.DynamicRefreshNacosConfiguration;
import com.meiyunji.sponsored.common.exception.BizServiceException;
import com.meiyunji.sponsored.common.exception.SponsoredBizException;
import com.meiyunji.sponsored.common.util.*;
import com.meiyunji.sponsored.grpc.common.AdHourReportRequest;
import com.meiyunji.sponsored.grpc.common.AdWeekReportResponsePb;
import com.meiyunji.sponsored.grpc.common.HourlyWeeklySuperpositionCommonReq;
import com.meiyunji.sponsored.grpc.common.HourlyWeeklySuperpositionTargetReq;
import com.meiyunji.sponsored.rpc.adAggregateHour.AdPageBasicData;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.config.nacos.AdManageLimitConfig;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdCampaignAllDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonMarketingStreamDataDao;
import com.meiyunji.sponsored.service.cpc.dto.CampaignHourlyReportSelectDto;
import com.meiyunji.sponsored.service.cpc.dto.CommonHourlyReportSelectDto;
import com.meiyunji.sponsored.service.cpc.dto.FeedHourlySelectDTO;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdCampaignAll;
import com.meiyunji.sponsored.service.cpc.po.AmazonMarketingStreamData;
import com.meiyunji.sponsored.service.cpc.service2.IAdWeeklyReportAnalysisService;
import com.meiyunji.sponsored.service.cpc.service2.IAmazonAdFeedReportService;
import com.meiyunji.sponsored.service.cpc.service2.handlers.CpcPageIdsHandler;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.vo.AdMetricDto;
import com.meiyunji.sponsored.service.dataWarehouse.service.DWShopSalesService;
import com.meiyunji.sponsored.service.dataWarehouse.statsDo.ShopSaleDto;
import com.meiyunji.sponsored.service.doris.dao.IDwsSaleProfitShopDayDao;
import com.meiyunji.sponsored.service.enums.AdHourlyLevelTypeEnum;
import com.meiyunji.sponsored.service.excel.excelTools.WriteHandlerBuild;
import com.meiyunji.sponsored.service.excel.excelTools.service.IExcelService;
import com.meiyunji.sponsored.service.function.ThFunction;
import com.meiyunji.sponsored.service.multiple.common.utils.MultipleUtils;
import com.meiyunji.sponsored.service.reportHour.constants.HourConvert;
import com.meiyunji.sponsored.service.reportHour.service.IAmazonAdTargetHourReportService;
import com.meiyunji.sponsored.service.reportHour.utils.ReportChartUtil;
import com.meiyunji.sponsored.service.reportHour.vo.*;
import com.meiyunji.sponsored.service.util.HourlyAndWeeklyDataHandler;
import com.meiyunji.sponsored.service.util.PbUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: ys
 * @date: 2023/12/26 20:29
 * @describe:
 */
@Service
@Slf4j
public class AdWeeklyReportAnalysisServiceImpl implements IAdWeeklyReportAnalysisService {

    @Autowired
    private IScVcShopAuthDao shopAuthDao;
    @Autowired
    private IAmazonAdCampaignAllDao amazonAdCampaignAllDao;
    @Autowired
    private IAmazonAdFeedReportService amazonAdFeedReportService;
    @Autowired
    private IAmazonAdTargetHourReportService amazonAdTargetHourService;
    @Autowired
    private CpcShopDataService cpcShopDataService;
    @Autowired
    private DWShopSalesService dwShopSalesService;
    @Resource
    private CpcPageIdsHandler cpcPageIdsHandler;
    @Autowired
    private IExcelService excelService;
    @Autowired
    private IAmazonMarketingStreamDataDao amazonMarketingStreamDataDao;
    @Autowired
    MultiThreadQueryAndMergeUtil multiThreadQueryAndMergeUtil;
    @Resource
    private DynamicRefreshNacosConfiguration dynamicRefreshNacosConfiguration;
    @Resource
    private IDwsSaleProfitShopDayDao dwsSaleProfitShopDayDao;
    @Resource
    private AdManageLimitConfig adManageLimitConfig;

    @Override
    public List<AdReportWeeklyDayVO> getSuperpositionList(AdHourReportRequest param, Function<AdReportWeeklyRequestPb.AdReportWeeklyRequest, AdReportWeeklyResponsePb.AdReportWeeklyResponse> weeklyFunc) {
        AdPageBasicData pageBasic = param.getPageBasic();
        Integer puid = Optional.of(pageBasic.getPuid()).map(Int32Value::getValue).orElse(null);
        Integer shopId = Optional.of(pageBasic.getShopId()).map(Int32Value::getValue).orElse(null);
        //校验店铺信息
        ShopAuth shopAuth = shopAuthDao.getScAndVcByIdAndPuid(shopId, puid);
        if (shopAuth == null) {
            return null;
        }

        String campaignType = Constants.SP;
        String costType = Constants.SD_REPORT_CPC;
        AmazonAdCampaignAll amazonAdCampaignAll = amazonAdCampaignAllDao.getByCampaignId(puid, pageBasic.getShopId().getValue(), param.getCampaignId());
        if (amazonAdCampaignAll != null) {
            campaignType = amazonAdCampaignAll.getType();
            if (StringUtils.isNotBlank(amazonAdCampaignAll.getCostType())) {
                costType = amazonAdCampaignAll.getCostType();
            }
        }
        if (Constants.SD.equalsIgnoreCase(campaignType) || Constants.SB.equalsIgnoreCase(campaignType)) {
            return amazonAdFeedReportService.listAdReportWeeklyDayVOByAdHourReportRequest(shopAuth, param, campaignType, costType);
        }
        //获取小时级数据
        AdReportWeeklyRequestPb.AdReportWeeklyRequest.Builder builder =
                AdReportWeeklyRequestPb.AdReportWeeklyRequest.newBuilder();
        builder.setSellerId(shopAuth.getSellingPartnerId());
        builder.setMarketplaceId(shopAuth.getMarketplaceId());
        builder.setStartDate(pageBasic.getStartDate());
        builder.setEndDate(pageBasic.getEndDate());
        if (StringUtils.isNotBlank(param.getCampaignId())) {
            builder.addCampaignId(param.getCampaignId());
        }
        if (StringUtils.isNotBlank(param.getGroupId())) {
            builder.addAdGroupId(param.getGroupId());
        }
        AdReportWeeklyResponsePb.AdReportWeeklyResponse response =
                weeklyFunc.apply(builder.build());

        //pb对象转vo对象
        List<AdReportWeeklyResponsePb.HourlyReportWeekData> dataList = response.getDataList();
        List<AdReportHourlyVO> voList = dataList.stream().map(this::convertTo).collect(Collectors.toList());
        //填充无数据的时间段
        List<Integer> allWeeks = HourConvert.weeKs;
        List<Integer> weekList = voList.stream().map(AdReportHourlyVO::getWeekDay).collect(Collectors.toList());
        Map<Integer, List<AdReportHourlyVO>> voMap =
                voList.stream().collect(Collectors.groupingBy(AdReportHourlyVO::getWeekDay));
        List<AdReportHourlyVO> DummyVO = Lists.newArrayList();
        //为不存在数据的星期填充占位数据
        allWeeks.parallelStream().filter(item -> !weekList.contains(item)).forEach(e -> voMap.put(e, DummyVO));
        //所有时间
        Collection<String> allHours = HourConvert.hourMap.values();
        voMap.forEach((k, v) -> {
            List<String> hourList = v.stream().map(AdReportHourlyVO::getLabel).collect(Collectors.toList());
            allHours.stream()
                    .filter(item -> !hourList.contains(item)).forEach(e -> {
                        AdReportHourlyVO vo = new AdReportHourlyVO();
                        vo.setLabel(e);
                        vo.setHour(Integer.valueOf(e.split("-")[0]));
                        vo.setWeekDay(k);
                        voList.add(vo);
                    });
        });

        voList.stream().sorted(Comparator.comparingInt(AdReportHourlyVO::getWeekDay)
                        .thenComparingInt(o -> Integer.parseInt(o.getLabel().split("-")[0])))
                .collect(Collectors.toList());

        return convertToHourOfWeekDayVos(voList);
    }

    public List<AdReportWeeklyDayVO> getSuperpositionListFromDoris(AdHourReportRequest param, Function<CampaignHourlyReportSelectDto, List<AmazonMarketingStreamData>> weeklyFunc) {
        AdPageBasicData pageBasic = param.getPageBasic();
        Integer puid = Optional.of(pageBasic.getPuid()).map(Int32Value::getValue).orElse(null);
        Integer shopId = Optional.of(pageBasic.getShopId()).map(Int32Value::getValue).orElse(null);
        //校验店铺信息
        ShopAuth shopAuth = shopAuthDao.getScAndVcByIdAndPuid(shopId, puid);
        if (shopAuth == null) {
            return null;
        }

        String campaignType = Constants.SP;
        String costType = Constants.SD_REPORT_CPC;
        AmazonAdCampaignAll amazonAdCampaignAll = amazonAdCampaignAllDao.getByCampaignId(puid, pageBasic.getShopId().getValue(), param.getCampaignId());
        if (amazonAdCampaignAll != null) {
            campaignType = amazonAdCampaignAll.getType();
            if (StringUtils.isNotBlank(amazonAdCampaignAll.getCostType())) {
                costType = amazonAdCampaignAll.getCostType();
            }
        }
        if (Constants.SD.equalsIgnoreCase(campaignType) || Constants.SB.equalsIgnoreCase(campaignType)) {
            return amazonAdFeedReportService.listAdReportWeeklyDayVOByAdHourReportRequest(shopAuth, param, campaignType, costType);
        }
        CampaignHourlyReportSelectDto queryDto = new CampaignHourlyReportSelectDto();
        queryDto.setSellerIds(Collections.singletonList(shopAuth.getSellingPartnerId()));
        queryDto.setMarketplaceId(shopAuth.getMarketplaceId());
        queryDto.setStartDate(amazonAdFeedReportService.getDataStartTime(shopAuth.getSellingPartnerId(), shopAuth.getMarketplaceId(), pageBasic.getStartDate()));
        queryDto.setEndDate(pageBasic.getEndDate());
        if (StringUtils.isNotBlank(param.getCampaignId())) {
            queryDto.setCampaignIds(Collections.singletonList(param.getCampaignId()));
        }
        if (StringUtils.isNotBlank(param.getGroupId())) {
            queryDto.setAdGroupList(Collections.singletonList(param.getGroupId()));
        }

        //pb对象转vo对象
        List<AmazonMarketingStreamData> dataList = weeklyFunc.apply(queryDto);
        List<AdReportHourlyVO> voList = dataList.stream().map(this::convertTo).collect(Collectors.toList());

        // 对比
        Map<Integer, List<AdReportHourlyVO>> compareMap = Maps.newHashMap();
        if (pageBasic.getIsCompare().getValue() == 1) {
            queryDto.setStartDate(amazonAdFeedReportService.getDataStartTime(shopAuth.getSellingPartnerId(), shopAuth.getMarketplaceId(), pageBasic.getStartDateCompare()));
            queryDto.setEndDate(pageBasic.getEndDateCompare());
            List<AmazonMarketingStreamData> compareDataList = weeklyFunc.apply(queryDto);
            List<AdReportHourlyVO> compareList = compareDataList.stream().map(this::convertTo).collect(Collectors.toList());
            compareMap.putAll(StreamUtil.groupingBy(compareList, AdReportHourlyVO::getWeekDay));
        }

        //填充无数据的时间段
        List<Integer> allWeeks = HourConvert.weeKs;
        List<Integer> weekList = voList.stream().map(AdReportHourlyVO::getWeekDay).collect(Collectors.toList());
        Map<Integer, List<AdReportHourlyVO>> voMap =
                voList.stream().collect(Collectors.groupingBy(AdReportHourlyVO::getWeekDay));
        List<AdReportHourlyVO> DummyVO = Lists.newArrayList();
        //为不存在数据的星期填充占位数据
        allWeeks.parallelStream().filter(item -> !weekList.contains(item)).forEach(e -> voMap.put(e, DummyVO));
        //所有时间
        Collection<String> allHours = HourConvert.hourMap.values();
        voMap.forEach((k, v) -> {
            List<AdReportHourlyVO> compareHourlyVOS = compareMap.get(k);
            Map<String, AdReportHourlyVO> compareHourMap = StreamUtil.toMap(compareHourlyVOS, AdReportHourlyVO::getLabel);

            List<String> hourList = v.stream().map(AdReportHourlyVO::getLabel).collect(Collectors.toList());
            allHours.stream()
                    .filter(item -> !hourList.contains(item)).forEach(e -> {
                        AdReportHourlyVO vo = new AdReportHourlyVO();
                        vo.setLabel(e);
                        vo.setHour(Integer.valueOf(e.split("-")[0]));
                        vo.setWeekDay(k);
                        voList.add(vo);
                        if (compareHourMap.containsKey(e)) {
                            vo.compareDataSet(compareHourMap.get(e));
                        }
                    });
            for (AdReportHourlyVO vo : v) {
                if (compareHourMap.containsKey(vo.getLabel())) {
                    vo.compareDataSet(compareHourMap.get(vo.getLabel()));
                }
            }
        });

        voList.stream().sorted(Comparator.comparingInt(AdReportHourlyVO::getWeekDay)
                        .thenComparingInt(o -> Integer.parseInt(o.getLabel().split("-")[0])))
                .collect(Collectors.toList());

        return convertToHourOfWeekDayVos(voList);
    }

    @Override
    public AdWeekReportResponsePb.AdWeekReportResponse.AdWeek getSuperpositionReport(AdHourReportRequest param, Function<AdReportWeeklyRequestPb.AdReportWeeklyRequest,
            AdReportWeeklyResponsePb.AdReportWeeklyResponse> weeklyFunc) {
        AdPageBasicData pageBasic = param.getPageBasic();
        List<AdReportWeeklyDayVO> weeklyList = getSuperpositionList(param, weeklyFunc);

        BigDecimal shopSalesByDate = cpcShopDataService.getShopSalesByDate(pageBasic.getShopId().getValue(), pageBasic.getStartDate().replace("-", ""), pageBasic.getEndDate().replace("-", ""));

        AdWeekReportResponsePb.AdWeekReportResponse.AdWeek.Builder reportBuilder =
                AdWeekReportResponsePb.AdWeekReportResponse.AdWeek.newBuilder();
        if (CollectionUtils.isNotEmpty(weeklyList)) {
            for (AdReportWeeklyDayVO vo : weeklyList) {
                vo.setAcots(shopSalesByDate.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdCost().multiply(new BigDecimal("100")).divide(shopSalesByDate, 4, RoundingMode.HALF_UP));
                vo.setAsots(shopSalesByDate.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdSale().multiply(new BigDecimal("100")).divide(shopSalesByDate, 4, RoundingMode.HALF_UP));
            }

            boolean isSorted = StringUtils.isNotBlank(pageBasic.getOrderField()) &&
                    Constants.isADOrderField(pageBasic.getOrderField(), AdCampaignWeekDayVo.class);
            if (isSorted) {
                PageUtil.sortedByOrderField(weeklyList, pageBasic.getOrderField(), pageBasic.getOrderType());
            }

            reportBuilder.addAllList(weeklyList.stream().filter(Objects::nonNull)
                    .map(key -> PbUtil.toAdWeekPb(key, shopSalesByDate)).collect(Collectors.toList()));
            reportBuilder.setSummary(PbUtil.toAdWeekPb(summaryCampaignOfWeekVo(weeklyList), shopSalesByDate));
            reportBuilder.addAllChart(ReportChartUtil.getAdWeekChartData(weeklyList, false));
        }
        return reportBuilder.build();
    }

    @Override
    public AdWeekReportResponsePb.AdWeekReportResponse.AdWeek getSuperpositionReportFromDoris(AdHourReportRequest param, Function<CampaignHourlyReportSelectDto, List<AmazonMarketingStreamData>> weeklyFunc) {
        AdPageBasicData pageBasic = param.getPageBasic();
        List<AdReportWeeklyDayVO> weeklyList = getSuperpositionListFromDoris(param, weeklyFunc);

        weeklyList.forEach(adReportWeeklyDayVO ->
                adReportWeeklyDayVO.getDetails().sort(Comparator.comparing(k -> Integer.parseInt(k.getLabel().split("-")[0]))));

        BigDecimal shopSalesByDate = cpcShopDataService.getShopSalesByDate(pageBasic.getShopId().getValue(), pageBasic.getStartDate().replace("-", ""), pageBasic.getEndDate().replace("-", ""));

        AdWeekReportResponsePb.AdWeekReportResponse.AdWeek.Builder reportBuilder =
                AdWeekReportResponsePb.AdWeekReportResponse.AdWeek.newBuilder();
        if (CollectionUtils.isNotEmpty(weeklyList)) {
            for (AdReportWeeklyDayVO vo : weeklyList) {
                vo.setAcots(shopSalesByDate.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdCost().multiply(new BigDecimal("100")).divide(shopSalesByDate, 4, RoundingMode.HALF_UP));
                vo.setAsots(shopSalesByDate.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdSale().multiply(new BigDecimal("100")).divide(shopSalesByDate, 4, RoundingMode.HALF_UP));
            }

            boolean isSorted = StringUtils.isNotBlank(pageBasic.getOrderField()) &&
                    Constants.isADOrderField(pageBasic.getOrderField(), AdCampaignWeekDayVo.class);
            if (isSorted) {
                PageUtil.sortedByOrderField(weeklyList, pageBasic.getOrderField(), pageBasic.getOrderType());
            }

            reportBuilder.addAllList(weeklyList.stream().filter(Objects::nonNull)
                    .map(key -> PbUtil.toAdWeekPb(key, shopSalesByDate)).collect(Collectors.toList()));
            reportBuilder.setSummary(PbUtil.toAdWeekPb(summaryCampaignOfWeekVo(weeklyList), shopSalesByDate));
            reportBuilder.addAllChart(ReportChartUtil.getAdWeekChartData(weeklyList, false));
            if (pageBasic.getIsCompare().getValue() == 1) {
                List<AdReportWeeklyDayVO> compareList = weeklyList.stream().map(item -> {
                    AdReportWeeklyDayVO vo = new AdReportWeeklyDayVO();
                    vo.setWeekDay(item.getWeekDay());
                    vo.setClicks(item.getClicksCompare());
                    vo.setImpressions(item.getImpressionsCompare());
                    vo.setAdCost(item.getAdCostCompare());
                    vo.setAdSale(item.getAdSaleCompare());
                    vo.setAdOrderNum(item.getAdOrderNumCompare());
                    vo.setAdSaleNum(item.getAdSaleNumCompare());
                    vo.setAdCostPerClick(item.getAdCostPerClickCompare());
                    vo.setAcos(item.getAcosCompare());
                    vo.setRoas(item.getRoasCompare());
                    vo.setCvr(item.getCvrCompare());
                    vo.setCtr(item.getCtrCompare());
                    return vo;
                }).collect(Collectors.toList());
                reportBuilder.addAllChart(ReportChartUtil.getAdWeekChartData(compareList, true));
            }
        }
        return reportBuilder.build();
    }

    @Override
    public AdWeekReportResponsePb.AdWeekReportResponse.AdWeek getCommonWeeklySuperpositionReport(HourlyWeeklySuperpositionCommonReq param) {
        int shopId = param.getShopId();

        // 校验店铺信息
        ShopAuth shopAuth = shopAuthDao.getScAndVcById(shopId);
        if (shopAuth == null) {
            log.error("getCommonWeeklyReport error, shopAuth is null, shopId: {}", shopId);
            throw new BizServiceException("shop is null");
        }

        // 获取每周报告
        List<AdCommonWeekDayVo> weeklyReportList = getReportWeeklyDayFromDoris(shopAuth, param);

        // 获取店铺销售额
        List<ShopSaleDto> shopSalesList = dwShopSalesService.listShopSaleByDateRange(
            shopAuth.getPuid(),
            Collections.singletonList(shopId),
            param.getStartDate().replace("-", ""),
            param.getEndDate().replace("-", "")
        );

        BigDecimal shopSalesTotal = CollectionUtils.isEmpty(shopSalesList) ? BigDecimal.ZERO : shopSalesList.get(0).getSumRange();

        // 组装结果数据
        AdWeekReportResponsePb.AdWeekReportResponse.AdWeek.Builder reportBuilder = AdWeekReportResponsePb.AdWeekReportResponse.AdWeek.newBuilder();

        if (CollectionUtils.isNotEmpty(weeklyReportList)) {
            for (AdCommonWeekDayVo vo : weeklyReportList) {
                BigDecimal adCostPercentage = shopSalesTotal.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO :
                    vo.getAdCost().multiply(new BigDecimal("100")).divide(shopSalesTotal, 4, RoundingMode.HALF_UP);
                vo.setAcots(adCostPercentage);

                BigDecimal adSalePercentage = shopSalesTotal.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO :
                    vo.getAdSale().multiply(new BigDecimal("100")).divide(shopSalesTotal, 4, RoundingMode.HALF_UP);
                vo.setAsots(adSalePercentage);
            }

            if (shouldSort(param)) {
                PageUtil.sortedByOrderField(weeklyReportList, param.getOrderField(), param.getOrderType());
            }

            reportBuilder.addAllList(weeklyReportList.stream()
                .filter(Objects::nonNull)
                .map(vo -> PbUtil.toCommonPb(vo, shopSalesTotal))
                .collect(Collectors.toList()));

            reportBuilder.setSummary(PbUtil.toCommonPb(summaryAllWeekVo(weeklyReportList), shopSalesTotal));
            reportBuilder.addAllChart(ReportChartUtil.getCommonWeekChartData(weeklyReportList, false));
            if (param.getIsCompare() == 1) {
                List<AdCommonWeekDayVo> compareList =  weeklyReportList.stream().map(item-> {
                    AdCommonWeekDayVo vo = new AdCommonWeekDayVo();
                    vo.setWeekDay(item.getWeekDay());
                    vo.setClicks(item.getClicksCompare());
                    vo.setImpressions(item.getImpressionsCompare());
                    vo.setAdCost(item.getAdCostCompare());
                    vo.setAdSale(item.getAdSaleCompare());
                    vo.setAdOrderNum(item.getAdOrderNumCompare());
                    vo.setAdSaleNum(item.getAdSaleNumCompare());
                    vo.setAdCostPerClick(item.getAdCostPerClickCompare());
                    vo.setAcos(item.getAcosCompare());
                    vo.setRoas(item.getRoasCompare());
                    vo.setCtr(item.getCtrCompare());
                    vo.setCvr(item.getCvrCompare());
                   return vo;
                }).collect(Collectors.toList());
                reportBuilder.addAllChart(ReportChartUtil.getCommonWeekChartData(compareList, true));
            }
        }

        return reportBuilder.build();
    }

    @Override
    public AdWeekReportResponsePb.AdWeekReportResponse.AdWeek getTargetWeeklySuperpositionReport(HourlyWeeklySuperpositionTargetReq param) {
        // 店铺状态校验
        List<ShopAuth> shopAuths = shopAuthDao.listValidShopByIds(param.getPuid(), param.getShopIdListList());
        if (CollectionUtils.isEmpty(shopAuths)) {
            throw new BizServiceException("no shop found");
        }
        // 获取每周报告
        List<AdCommonWeekDayVo> weeklyReportList = this.getReportWeeklyDayFromDoris(shopAuths, param);
        // 获取店铺销售额
        BigDecimal shopSalesTotal = dwsSaleProfitShopDayDao.sumShopSaleByDateRange(param.getPuid(), param.getShopIdListList(), param.getStartDate(), param.getEndDate(), MultipleUtils.changeRate(shopAuths));
        // 组装结果数据
        AdWeekReportResponsePb.AdWeekReportResponse.AdWeek.Builder reportBuilder = AdWeekReportResponsePb.AdWeekReportResponse.AdWeek.newBuilder();
        if (CollectionUtils.isNotEmpty(weeklyReportList)) {
            for (AdCommonWeekDayVo vo : weeklyReportList) {
                BigDecimal adCostPercentage = shopSalesTotal.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO :
                        vo.getAdCost().multiply(new BigDecimal("100")).divide(shopSalesTotal, 4, RoundingMode.HALF_UP);
                vo.setAcots(adCostPercentage);

                BigDecimal adSalePercentage = shopSalesTotal.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO :
                        vo.getAdSale().multiply(new BigDecimal("100")).divide(shopSalesTotal, 4, RoundingMode.HALF_UP);
                vo.setAsots(adSalePercentage);
            }

            if (this.shouldSort(param)) {
                PageUtil.sortedByOrderField(weeklyReportList, param.getOrderField(), param.getOrderType());
            }

            reportBuilder.addAllList(weeklyReportList.stream()
                    .filter(Objects::nonNull)
                    .map(vo -> PbUtil.toCommonPb(vo, shopSalesTotal))
                    .collect(Collectors.toList()));

            reportBuilder.setSummary(PbUtil.toCommonPb(summaryAllWeekVo(weeklyReportList), shopSalesTotal));
            reportBuilder.addAllChart(ReportChartUtil.getCommonWeekChartData(weeklyReportList, false));
            reportBuilder.setCurrency(MultipleUtils.getCurrency(shopAuths));
            if (param.getIsCompare() == 1) {
                List<AdCommonWeekDayVo> compareList =  weeklyReportList.stream().map(item-> {
                    AdCommonWeekDayVo vo = new AdCommonWeekDayVo();
                    vo.setWeekDay(item.getWeekDay());
                    vo.setClicks(item.getClicksCompare());
                    vo.setImpressions(item.getImpressionsCompare());
                    vo.setAdCost(item.getAdCostCompare());
                    vo.setAdSale(item.getAdSaleCompare());
                    vo.setAdOrderNum(item.getAdOrderNumCompare());
                    vo.setAdSaleNum(item.getAdSaleNumCompare());
                    vo.setAdCostPerClick(item.getAdCostPerClickCompare());
                    vo.setAcos(item.getAcosCompare());
                    vo.setRoas(item.getRoasCompare());
                    vo.setCtr(item.getCtrCompare());
                    vo.setCvr(item.getCvrCompare());
                    return vo;
                }).collect(Collectors.toList());
                reportBuilder.addAllChart(ReportChartUtil.getCommonWeekChartData(compareList, true));
            }
        }

        return reportBuilder.build();
    }

    private boolean shouldSort(HourlyWeeklySuperpositionCommonReq param) {
        return StringUtils.isNotBlank(param.getOrderField()) &&
            Constants.isADOrderField(param.getOrderField(), AdCampaignWeekDayVo.class);
    }

    private boolean shouldSort(HourlyWeeklySuperpositionTargetReq param) {
        return StringUtils.isNotBlank(param.getOrderField()) &&
            Constants.isADOrderField(param.getOrderField(), AdCampaignWeekDayVo.class);
    }

    /**
     * 汇总所有周数据
     * @param list
     * @return
     */
    private static AdCommonWeekDayVo summaryAllWeekVo(List<AdCommonWeekDayVo> list) {
        AdCommonWeekDayVo summaryVo = new AdCommonWeekDayVo();
        if (CollectionUtils.isEmpty(list)) {
            return summaryVo;
        }

        BigDecimal totalVcpmCost = BigDecimal.ZERO;
        long totalVcpmImpressions = 0L;
        long totalImpressions = 0L;
        long totalClicks = 0L;
        BigDecimal totalAdSelfSale = BigDecimal.ZERO;
        BigDecimal totalAdSale = BigDecimal.ZERO;

        for (AdCommonWeekDayVo ad : list) {
            summaryVo.setClicks(MathUtil.add(ad.getClicks(), summaryVo.getClicks()));
            summaryVo.setAdOrderNum(MathUtil.add(ad.getAdOrderNum(), summaryVo.getAdOrderNum()));
            summaryVo.setSelfAdOrderNum(MathUtil.add(ad.getSelfAdOrderNum(), summaryVo.getSelfAdOrderNum()));
            summaryVo.setOtherAdOrderNum(MathUtil.add(ad.getOtherAdOrderNum(), summaryVo.getOtherAdOrderNum()));
            summaryVo.setAdSale(MathUtil.add(ad.getAdSale(), summaryVo.getAdSale()));
            summaryVo.setAdSelfSale(MathUtil.add(ad.getAdSelfSale(), summaryVo.getAdSelfSale()));
            summaryVo.setAdOtherSale(MathUtil.add(ad.getAdOtherSale(), summaryVo.getAdOtherSale()));
            summaryVo.setAdSaleNum(MathUtil.add(ad.getAdSaleNum(), summaryVo.getAdSaleNum()));
            summaryVo.setAdSelfSaleNum(MathUtil.add(ad.getAdSelfSaleNum(), summaryVo.getAdSelfSaleNum()));
            summaryVo.setAdOtherSaleNum(MathUtil.add(ad.getAdOtherSaleNum(), summaryVo.getAdOtherSaleNum()));
            summaryVo.setImpressions(MathUtil.add(summaryVo.getImpressions(), ad.getImpressions()));
            summaryVo.setAdCost(MathUtil.add(ad.getAdCost(), summaryVo.getAdCost()));
            summaryVo.setAcots(MathUtil.add(ad.getAcots(), summaryVo.getAcots()));
            summaryVo.setAsots(MathUtil.add(ad.getAsots(), summaryVo.getAsots()));
            summaryVo.setAdCostPercentage(MathUtil.add(summaryVo.getAdCostPercentage(), ad.getAdCostPercentage()));
            summaryVo.setAdSalePercentage(MathUtil.add(summaryVo.getAdSalePercentage(), ad.getAdSalePercentage()));
            summaryVo.setAdOrderNumPercentage(MathUtil.add(summaryVo.getAdOrderNumPercentage(), ad.getAdOrderNumPercentage()));
            summaryVo.setOrderNumPercentage(MathUtil.add(summaryVo.getOrderNumPercentage(), ad.getOrderNumPercentage()));
            summaryVo.setViewableImpressions(MathUtil.add(summaryVo.getViewableImpressions(), ad.getViewableImpressions()));
            summaryVo.setOrdersNewToBrand(MathUtil.add(summaryVo.getOrdersNewToBrand(), ad.getOrdersNewToBrand()));
            summaryVo.setUnitsOrderedNewToBrand(MathUtil.add(summaryVo.getUnitsOrderedNewToBrand(), ad.getUnitsOrderedNewToBrand()));
            summaryVo.setSalesNewToBrand(MathUtil.add(summaryVo.getSalesNewToBrand(), ad.getSalesNewToBrand()));

            summaryVo.setAdCostCompare(MathUtil.add(summaryVo.getAdCostCompare(), ad.getAdCostCompare()));
            summaryVo.setClicksCompare(MathUtil.add(summaryVo.getClicksCompare(), ad.getClicksCompare()));
            summaryVo.setImpressionsCompare(MathUtil.add(summaryVo.getImpressionsCompare(), ad.getImpressionsCompare()));
            summaryVo.setAdSaleNumCompare(MathUtil.add(summaryVo.getAdSaleNumCompare(), ad.getAdSaleNumCompare()));
            summaryVo.setAdSaleCompare(MathUtil.add(summaryVo.getAdSaleCompare(), ad.getAdSaleCompare()));
            summaryVo.setAdOrderNumCompare(MathUtil.add(summaryVo.getAdOrderNumCompare(), ad.getAdOrderNumCompare()));

            if (ad.getVcpmCost() != null) {
                totalVcpmCost = totalVcpmCost.add(ad.getVcpmCost());
            }
            if (ad.getVcpmImpressions() != null) {
                totalVcpmImpressions += ad.getVcpmImpressions();
            }
            if (ad.getTotalClicks() != null) {
                totalClicks += ad.getTotalClicks();
            }
            if (ad.getTotalImpressions() != null) {
                totalImpressions += ad.getTotalImpressions();
            }
            if (ad.getTotalAdSale() != null) {
                totalAdSale = totalAdSale.add(ad.getTotalAdSale());
            }
            if (ad.getTotalAdSelfSale() != null) {
                totalAdSelfSale = totalAdSelfSale.add(ad.getTotalAdSelfSale());
            }
        }
        summaryVo.setAdCostPerClick(calculateAdCostPerClick(summaryVo.getAdCost(), summaryVo.getClicks()));
        summaryVo.setCpa(calculateCpa(summaryVo.getAdCost(), summaryVo.getAdOrderNum()));
        summaryVo.setAcos(calculateAcos(summaryVo.getAdCost(), summaryVo.getAdSale()));
        summaryVo.setRoas(calculateRoas(summaryVo.getAdSale(), summaryVo.getAdCost()));
        summaryVo.setCtr(calculateCtr(summaryVo.getClicks(), summaryVo.getImpressions()));
        summaryVo.setCvr(calculateCvr(summaryVo.getAdOrderNum(), summaryVo.getClicks()));
        summaryVo.setAdCostPercentage(calculatePercentage(summaryVo.getAdCostPercentage()));
        summaryVo.setAdSalePercentage(calculatePercentage(summaryVo.getAdSalePercentage()));
        summaryVo.setAdOrderNumPercentage(calculatePercentage(summaryVo.getAdOrderNumPercentage()));
        summaryVo.setOrderNumPercentage(calculatePercentage(summaryVo.getOrderNumPercentage()));
        summaryVo.setVrt(calculateVrt(summaryVo.getViewableImpressions(), totalImpressions));
        summaryVo.setVCtr(calculateVCtr(totalClicks, summaryVo.getViewableImpressions()));
        summaryVo.setVcpm(calculateVcpm(totalVcpmCost, totalVcpmImpressions));
        summaryVo.setAdvertisingUnitPrice(calculateUnitPrice(summaryVo.getAdSale(), summaryVo.getAdOrderNum()));
        summaryVo.setAdvertisingProductUnitPrice(calculateUnitPrice(totalAdSelfSale, summaryVo.getSelfAdOrderNum()));
        summaryVo.setAdvertisingOtherProductUnitPrice(calculateUnitPrice(totalAdSale.subtract(totalAdSelfSale), summaryVo.getOtherAdOrderNum()));
        summaryVo.setOrdersNewToBrandPercentage(calculateOrdersNewToBrandPercentage(summaryVo.getOrdersNewToBrand(), summaryVo.getAdOrderNum()));
        summaryVo.setUnitsOrderedNewToBrandPercentage(calculateUnitsOrderedNewToBrandPercentage(summaryVo.getUnitsOrderedNewToBrand(), summaryVo.getAdSaleNum()));
        summaryVo.setSalesNewToBrandPercentage(calculateSalesNewToBrandPercentage(summaryVo.getSalesNewToBrand(), summaryVo.getAdSale()));

        summaryVo.setAdCostPerClickCompare(calculateAdCostPerClick(summaryVo.getAdCostCompare(), summaryVo.getClicksCompare()));
        summaryVo.setAcosCompare(calculateAcos(summaryVo.getAdCostCompare(), summaryVo.getAdSaleCompare()));
        summaryVo.setRoasCompare(calculateRoas(summaryVo.getAdSaleCompare(), summaryVo.getAdCostCompare()));
        summaryVo.setCtrCompare(calculateCtr(summaryVo.getClicksCompare(), summaryVo.getImpressionsCompare()));
        summaryVo.setCvrCompare(calculateCvr(summaryVo.getAdOrderNumCompare(), summaryVo.getClicksCompare()));
        summaryVo.afterPropertiesSet();//为各对比率属性设值

        return summaryVo;
    }

    @Override
    public String exportCommonWeekData(HourlyWeeklySuperpositionCommonReq param) {
        int shopId = param.getShopId();

        // 校验店铺信息
        ShopAuth shopAuth = validateShopAuth(shopId);

        // 获取每周报告
        List<AdCommonWeekDayVo> weeklyList = getReportWeeklyDayFromDoris(shopAuth, param);

        // 获取店铺销售额
        BigDecimal shopSalesByDate = getShopSalesByDate(shopAuth, param);

        // 处理每小时报告详情
        List<AdCommonHourVo> commonHourVos = getCommonHourVos(weeklyList);

        // 获取货币代码
        String currencyCode = MarketTimezoneAndCurrencyEnum.getByMarketplaceId(shopAuth.getMarketplaceId()).getCurrencyCode();

        // 准备导出数据
        List<AdCommonWeekExportVo> exportVos = prepareExportVos(commonHourVos, shopSalesByDate, currencyCode);

        // 导出Excel
        return exportToExcel(param, shopAuth, exportVos);
    }

    @Override
    public String exportTargetWeekData(HourlyWeeklySuperpositionTargetReq param) {
        // 校验店铺信息
        List<ShopAuth> shopAuths = validateShopAuths(param.getPuid(), param.getShopIdListList());

        // 获取每周报告
        List<AdCommonWeekDayVo> weeklyList = this.getReportWeeklyDayFromDoris(shopAuths, param);

        // 获取店铺销售额
        BigDecimal shopSalesByDate = dwsSaleProfitShopDayDao.sumShopSaleByDateRange(param.getPuid(), param.getShopIdListList(), param.getStartDate(), param.getEndDate(), MultipleUtils.changeRate(shopAuths));

        // 处理每小时报告详情
        List<AdCommonHourVo> commonHourVos = this.getCommonHourVos(weeklyList);

        // 获取货币代码
        String currencyCode = MultipleUtils.getCurrency(shopAuths);

        // 准备导出数据
        List<AdCommonWeekExportVo> exportVos = this.prepareExportVos(commonHourVos, shopSalesByDate, currencyCode);

        // 导出Excel
        return this.exportToExcel(param, shopAuths, exportVos);
    }

    private ShopAuth validateShopAuth(int shopId) {
        ShopAuth shopAuth = shopAuthDao.getScAndVcById(shopId);
        if (shopAuth == null) {
            log.error("getCommonWeeklyReport error, shopAuth is null, shopId: {}", shopId);
            throw new BizServiceException("shop is null");
        }
        return shopAuth;
    }

    private List<ShopAuth> validateShopAuths(Integer puid, List<Integer> shopIds) {
        List<ShopAuth> shopAuths = shopAuthDao.listValidShopByIds(puid, shopIds);
        if (CollectionUtils.isEmpty(shopAuths)) {
            throw new BizServiceException("no shop found");
        }
        return shopAuths;
    }

    private BigDecimal getShopSalesByDate(ShopAuth shopAuth, HourlyWeeklySuperpositionCommonReq param) {
        List<ShopSaleDto> shopSalesList = dwShopSalesService.listShopSaleByDateRange(
            shopAuth.getPuid(),
            Collections.singletonList(shopAuth.getId()),
            param.getStartDate().replace("-", ""),
            param.getEndDate().replace("-", "")
        );
        return CollectionUtils.isEmpty(shopSalesList) ? BigDecimal.ZERO : shopSalesList.get(0).getSumRange();
    }

    private List<AdCommonHourVo> getCommonHourVos(List<AdCommonWeekDayVo> weeklyList) {
        List<AdCommonHourVo> commonHourVos = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(weeklyList)) {
            weeklyList.forEach(e -> commonHourVos.addAll(e.getDetails()));
        }
        return commonHourVos;
    }

    private List<AdCommonWeekExportVo> prepareExportVos(List<AdCommonHourVo> commonHourVos, BigDecimal shopSalesByDate, String currencyCode) {
        List<AdCommonWeekExportVo> exportVos = new ArrayList<>();
        for (AdCommonHourVo vo : commonHourVos) {
            vo.setAcots(MathUtil.divideIntegerByOneHundred(vo.getAdCost(), shopSalesByDate));
            vo.setAsots(MathUtil.divideIntegerByOneHundred(vo.getAdSale(), shopSalesByDate));
            AdCommonWeekExportVo exVo = new AdCommonWeekExportVo(currencyCode, vo);
            exportVos.add(exVo);
        }
        return exportVos;
    }

    private String exportToExcel(HourlyWeeklySuperpositionCommonReq param, ShopAuth shopAuth, List<AdCommonWeekExportVo> exportVos) {
        WriteHandlerBuild build = new WriteHandlerBuild().rate();
        String shopName = shopAuth.getName();
        AdHourlyLevelTypeEnum levelTypeEnum = AdHourlyLevelTypeEnum.getByDesc(param.getLevelType());
        String fileName = String.format("%s_%s小时数据_%s_%s", shopName, levelTypeEnum.getCnDesc(), param.getStartDate(), param.getEndDate());
        List<String> excludeFields = Lists.newArrayList();
        if (param.getIsCompare() == 0) {
            excludeFields.addAll(Lists.newArrayList("costCompare", "costCompareRate",
                    "impressionsCompare", "impressionsCompareRate",
                    "clicksCompare", "clicksCompareRate",
                    "adSalesCompare", "adSalesCompareRate",
                    "adOrderNumCompare", "adOrderNumCompareRate",
                    "adSaleNumCompare", "adSaleNumCompareRate",
                    "cpcCompare", "cpcCompareRate",
                    "clickRateCompare", "clickRateCompareRate",
                    "salesConversionRateCompare", "salesConversionRateCompareRate",
                    "acosCompare", "acosCompareRate",
                    "roasCompare", "roasCompareRate"));
        }
        return excelService.easyExcelHandlerExport(param.getPuid(), exportVos,
            fileName + "(0)", AdCommonWeekExportVo.class,
            build.currencyNew(AdCommonWeekExportVo.class), excludeFields);
    }

    private String exportToExcel(HourlyWeeklySuperpositionTargetReq param, List<ShopAuth> shopAuths, List<AdCommonWeekExportVo> exportVos) {
        WriteHandlerBuild build = new WriteHandlerBuild().rate();
        String shopName = MultipleUtils.getExportShopName(shopAuths);
        AdHourlyLevelTypeEnum levelTypeEnum = AdHourlyLevelTypeEnum.getByDesc(param.getLevelType());
        String fileName = String.format("%s_%s小时数据_%s_%s", shopName, levelTypeEnum.getCnDesc(), param.getStartDate(), param.getEndDate());
        List<String> excludeFields = Lists.newArrayList();
        if (param.getIsCompare() == 0) {
            excludeFields.addAll(Lists.newArrayList("costCompare", "costCompareRate",
                    "impressionsCompare", "impressionsCompareRate",
                    "clicksCompare", "clicksCompareRate",
                    "adSalesCompare", "adSalesCompareRate",
                    "adOrderNumCompare", "adOrderNumCompareRate",
                    "adSaleNumCompare", "adSaleNumCompareRate",
                    "cpcCompare", "cpcCompareRate",
                    "clickRateCompare", "clickRateCompareRate",
                    "salesConversionRateCompare", "salesConversionRateCompareRate",
                    "acosCompare", "acosCompareRate",
                    "roasCompare", "roasCompareRate"));
        }
        //SP不需要销量字段
        if (Constants.SP.equalsIgnoreCase(param.getType())) {
            excludeFields.addAll(Lists.newArrayList( "adCostPercentage","acots","asots","adOrderNumPercentage","adSalePercentage","orderNumPercentage","viewableImpressions","vrt",
                    "vcpm","vctr","ordersNewToBrand","unitsOrderedNewToBrand","salesNewToBrand","advertisingUnitPrice","advertisingProductUnitPrice","advertisingOtherProductUnitPrice","" +
                            "ordersNewToBrandPercentage","unitsOrderedNewToBrandPercentage","salesNewToBrandPercentage"));
        }
        return excelService.easyExcelHandlerExport(param.getPuid(), exportVos,
            fileName + "(0)", AdCommonWeekExportVo.class,
            build.currencyNew(AdCommonWeekExportVo.class), excludeFields);
    }

    /**
     * 获取广告活动、广告组合、投放的周叠加数据
     *
     * @param shopAuth 店铺认证信息
     * @param param 请求参数
     * @return 周叠加数据列表
     */
    private List<AdCommonWeekDayVo> getReportWeeklyDayFromDoris(ShopAuth shopAuth, HourlyWeeklySuperpositionCommonReq param) {

        List<String> commonIds ;
        if (param.getAggregated()) {
            commonIds = cpcPageIdsHandler.getTemporaryAggregateIds(param.getPageSign(), "");
        } else {
            commonIds = Collections.singletonList(param.getCommonId());
        }

        FeedHourlySelectDTO feedHourlySelectDTO = new FeedHourlySelectDTO();
        feedHourlySelectDTO.setSellerId(shopAuth.getSellingPartnerId());
        feedHourlySelectDTO.setMarketplaceId(shopAuth.getMarketplaceId());
        feedHourlySelectDTO.setStart(LocalDate.parse(param.getStartDate()));
        feedHourlySelectDTO.setEnd(LocalDate.parse(param.getEndDate()));
        feedHourlySelectDTO.setWeekdayList(StringUtil.splitInt(param.getWeeks()));
        feedHourlySelectDTO.setType(param.getType());
        feedHourlySelectDTO.setPuid(param.getPuid());
        feedHourlySelectDTO.setShopId(param.getShopId());
        feedHourlySelectDTO.setIsCompare(param.getIsCompare());
        if (StringUtils.isNotBlank(param.getStartDateCompare())) {
            feedHourlySelectDTO.setStartCompare(LocalDate.parse(param.getStartDateCompare()));
        }
        if (StringUtils.isNotBlank(param.getEndDateCompare())) {
            feedHourlySelectDTO.setEndCompare(LocalDate.parse(param.getEndDateCompare()));
        }
        List<AdCommonHourVo> commonHourVos ;
        switch (AdHourlyLevelTypeEnum.getByDesc(param.getLevelType())) {
            case CAMPAIGN:
                feedHourlySelectDTO = AmazonAdFeedReportServiceImpl.buildMultiple(LocalDate.parse(param.getStartDate()),
                        LocalDate.parse(param.getEndDate()),
                        null,
                        param.getWeeks(),
                        CollectionUtil.newArrayList(shopAuth)
                        );
                List<List<AdCampaignHourVo>> adCampaignHourVoLists = multiThreadQueryAndMergeUtil.multiThreadQuery(ThreadPoolUtil.getWeeklyCampaignExecutorForHour(),
                    () -> commonIds, feedHourlySelectDTO, amazonAdFeedReportService::multiThreadQueryWeekAms);
                //将得到的所有小时级记录，合并成 7 * 24 条，同一天的同一个小时的数据相加
                commonHourVos = ReportChartUtil.getCommonWeekReportVosFromCampaign(adCampaignHourVoLists);
                break;
            case GROUP:
                List<List<AdGroupHourVo>> adGroupHourVoLists = multiThreadQueryAndMergeUtil.multiThreadQuery(ThreadPoolUtil.getWeeklyGroupExecutorForHour(),
                    () -> commonIds, feedHourlySelectDTO, amazonAdFeedReportService::multiThreadQueryAmsForGroup);
                //将得到的所有小时级记录，合并成 7 * 24 条，同一天的同一个小时的数据相加
                commonHourVos = ReportChartUtil.getCommonWeekReportVosFromGroup(adGroupHourVoLists);
                break;
            case KEYWORD_TARGETING:
                CommonHourlyReportSelectDto queryDto = new CommonHourlyReportSelectDto();
                queryDto.setSellerId(shopAuth.getSellingPartnerId());
                queryDto.setMarketplaceId(shopAuth.getMarketplaceId());
                queryDto.setType(param.getType());
                queryDto.setStartDate(param.getStartDate());
                queryDto.setEndDate(param.getEndDate());
                queryDto.setWeekdayList(
                    Arrays.stream(param.getWeeks().split(","))
                        .map(Integer::parseInt)
                        .collect(Collectors.toList())
                );
                queryDto.setIsCompare(param.getIsCompare());
                queryDto.setStartDateCompare(param.getStartDateCompare());
                queryDto.setEndDateCompare(param.getEndDateCompare());
                List<List<AdKeywordAndTargetHourVo>> adKeywordAndTargetHourVoLists = multiThreadQueryAndMergeUtil.multiThreadQuery(ThreadPoolUtil.getWeeklyTargerExecutorForHour(),
                    () -> commonIds, queryDto, amazonAdFeedReportService::multiThreadQueryAmsForTarget);
                //将得到的所有小时级记录，合并成 7 * 24 条，同一天的同一个小时的数据相加
                commonHourVos = ReportChartUtil.getCommonWeekReportVosFromTarget(adKeywordAndTargetHourVoLists);
                break;
            default:
                throw new BizServiceException("error param, LevelType: " + param.getLevelType());
        }
        // 将小时级记录转化为7天的记录并返回
        return convertToWeekDayVos(commonHourVos);
    }

    private List<AdCommonWeekDayVo> getReportWeeklyDayFromDoris(List<ShopAuth> shopAuths, HourlyWeeklySuperpositionTargetReq param) {
        List<String> commonIds;
        if (param.getAggregated()) {
            commonIds = cpcPageIdsHandler.getTemporaryAggregateIds(param.getPageSign(), "");
        } else {
            commonIds = Collections.singletonList(param.getCommonId());
        }
        // 多店鋪,超过限制投放数量返回，防止doris cpu过高
        if((shopAuths.size() > 1 && commonIds.size() >= adManageLimitConfig.getTargetHourLimit()) || shopAuths.size() > 40){
            if(shopAuths.size() > 40){
                throw new SponsoredBizException("当前店铺数量超过40个，请减少后重新查询!");
            }else{
                throw new SponsoredBizException("当前所选数据量过大，请减少店铺查询!");
            }
        }
        FeedHourlySelectDTO feedHourlySelectDTO = AmazonAdFeedReportServiceImpl.buildMultipleTargetSelectDto(shopAuths, param);
        List<List<AdKeywordAndTargetHourVo>> adKeywordAndTargetHourVoLists = multiThreadQueryAndMergeUtil.multiThreadQuery(ThreadPoolUtil.getWeeklyTargerExecutorForHour(),
                () -> commonIds, feedHourlySelectDTO, amazonAdFeedReportService::multiThreadQueryAmsForTarget);
        //将得到的所有小时级记录，合并成 7 * 24 条，同一天的同一个小时的数据相加
        List<AdCommonHourVo> commonHourVos = ReportChartUtil.getCommonWeekReportVosFromTarget(adKeywordAndTargetHourVoLists);
        // 将小时级记录转化为7天的记录并返回
        return convertToWeekDayVos(commonHourVos);
    }



    /**
     * 使用多线程查询广告数据
     *
     * @param shopAuth 店铺授权信息
     * @param param    请求参数
     * @return 查询结果列表
     */
    public List<List<AdCommonHourVo>> multiThreadQuery(ShopAuth shopAuth, HourlyWeeklySuperpositionCommonReq param) {
        ThreadPoolExecutor threadPool = ThreadPoolUtil.getCpcAggregateIdsSyncPool();
        List<String> commonIds;
        if (param.getAggregated()) {
            commonIds = cpcPageIdsHandler.getTemporaryAggregateIds(param.getPageSign(), "");
        } else {
            commonIds = Collections.singletonList(param.getCommonId());
        }

        // 校验 commonIds 是否为空
        if (CollectionUtils.isEmpty(commonIds)) {
            return Collections.emptyList();
        }

        // 将 commonIds 分成多个批次
        List<List<String>> queryListPartition = Lists.partition(commonIds, dynamicRefreshNacosConfiguration.getMAX_CAMPAIGN_SIZE());

        CompletableFuture<List<AdCommonHourVo>>[] futures = queryListPartition.stream()
            .map(queryList -> CompletableFuture.supplyAsync(() -> queryHourlyReportByCondition(shopAuth, queryList, param), threadPool))
            .toArray(CompletableFuture[]::new);

        CompletableFuture<Void> allOf = CompletableFuture.allOf(futures);
        try {
            // 等待所有 CompletableFuture 完成
            allOf.join();
        } catch (CompletionException e) {
            log.error("Error occurred while waiting for CompletableFuture completion", e);
        }

        // 收集所有 CompletableFuture 的结果
        List<List<AdCommonHourVo>> resultList = Arrays.stream(futures)
            .map(CompletableFuture::join)
            .collect(Collectors.toList());

        return resultList;
    }

    private List<AdCommonHourVo> queryHourlyReportByCondition(ShopAuth shopAuth, List<String> commonIds, HourlyWeeklySuperpositionCommonReq param) {
        CommonHourlyReportSelectDto queryDto = new CommonHourlyReportSelectDto();
        queryDto.setSellerId(shopAuth.getSellingPartnerId());
        queryDto.setMarketplaceId(shopAuth.getMarketplaceId());
        queryDto.setType(param.getType());
        queryDto.setStartDate(param.getStartDate());
        queryDto.setEndDate(param.getEndDate());
        queryDto.setWeekdayList(
            Arrays.stream(param.getWeeks().split(","))
                .map(Integer::parseInt)
                .collect(Collectors.toList())
        );
        if (CollectionUtils.isEmpty(commonIds)) {
            log.info("commonId is empty, levelType: {}, pageSign: {}, param.commonId: {}", param.getLevelType(), param.getPageSign(), param.getCommonId());
            return Collections.emptyList();
        }
        switch (AdHourlyLevelTypeEnum.getByDesc(param.getLevelType())) {
            case CAMPAIGN:
                queryDto.setCampaignIds(commonIds);
                break;
            case GROUP:
                queryDto.setGroupIds(commonIds);
                break;
            case KEYWORD_TARGETING:
                queryDto.setKeywordTargetingIds(commonIds);
                break;
            default:
                throw new BizServiceException("error param, LevelType: " + param.getLevelType());
        }
        List<AmazonMarketingStreamData> streamData = amazonMarketingStreamDataDao.statisticsByWeek(queryDto);
        List<AdCommonHourVo> voList = new ArrayList<>();
        Map<Integer, List<AmazonMarketingStreamData>> streamDataMap = streamData.stream().collect(Collectors.groupingBy(AmazonMarketingStreamData::getWeekday));
        HourConvert.weeKs.forEach(key -> {
            Map<String, AmazonMarketingStreamData> streamData1 = streamDataMap.getOrDefault(key, new ArrayList<>()).stream().collect(Collectors.toMap(AmazonMarketingStreamData::getTime, Function.identity()));
            HourConvert.HOUR_FEED_SORT_MAP.forEach((k, value) -> {
                AdCommonHourVo vo = buildAdCampaignWeekVoSp(key, streamData1.getOrDefault(k, new AmazonMarketingStreamData()), value);
                voList.add(vo);
            });
        });

        return voList;
    }

    private AdCommonHourVo buildAdCampaignWeekVoSp(Integer key, AmazonMarketingStreamData streamData, Integer hour) {
        return AdCommonHourVo.builder()
            .hour(hour)
            .weekDay(key)
            .adSale(getDefaultBigDecimal(streamData.getAttributedSales7d()))
            .adSelfSale(getDefaultBigDecimal(streamData.getAttributedSales7dSameSku()))
            .adOrderNum(getDefaultInteger(streamData.getAttributedConversions7d()))
            .selfAdOrderNum(getDefaultInteger(streamData.getAttributedConversions7dSameSku()))
            .adSaleNum(getDefaultInteger(streamData.getAttributedUnitsOrdered7d()))
            .adSelfSaleNum(getDefaultInteger(streamData.getAttributedUnitsOrdered7dSameSku()))
            .adOtherSaleNum(getDefaultInteger(streamData.getAttributedUnitsOrdered7d()) - getDefaultInteger(streamData.getAttributedUnitsOrdered7dSameSku()))
            .adCost(getDefaultBigDecimal(streamData.getCost()))
            .clicks(getDefaultLong(streamData.getClicks()))
            .impressions(getDefaultLong(streamData.getImpressions()))
            .viewableImpressions(0)
            .ordersNewToBrand(0)
            .unitsOrderedNewToBrand(0)
            .salesNewToBrand(BigDecimal.ZERO)
            .otherAdOrderNum(MathUtil.subtractInteger(streamData.getAttributedConversions7d(), streamData.getAttributedConversions7dSameSku()))
            .vcpmImpressions(0L)
            .vcpmCost(BigDecimal.ZERO)
            .totalImpressions(getDefaultLong(streamData.getImpressions()))
            .totalClicks(getDefaultLong(streamData.getClicks()))
            .totalAdSale(getDefaultBigDecimal(streamData.getAttributedSales7d()))
            .totalAdSelfSale(getDefaultBigDecimal(streamData.getAttributedSales7dSameSku()))
            .build();
    }

    private BigDecimal getDefaultBigDecimal(BigDecimal bigDecimal) {
        return bigDecimal == null ? BigDecimal.ZERO : bigDecimal.setScale(2, RoundingMode.HALF_UP);
    }
    private Integer getDefaultInteger(Integer number) {
        return number == null ? 0 : number;
    }
    private Long getDefaultLong(Long number) {
        return number == null ? 0 : number;
    }
    private BigDecimal getDefaultBigDecimal(Double bigDecimal) {
        return bigDecimal == null ? BigDecimal.ZERO : BigDecimal.valueOf(bigDecimal).setScale(2, RoundingMode.HALF_UP);
    }


    private void buildAggregateWeek(List<AdCampaignHourVo> voList, FeedHourlySelectDTO feedHourlySelectDTO, ThFunction<Integer, AmazonMarketingStreamData, Integer, AdCampaignHourVo> hourlyFunc) {
        List<AmazonMarketingStreamData> streamData = amazonMarketingStreamDataDao.listWeekByHourly(feedHourlySelectDTO);
        Map<Integer, List<AmazonMarketingStreamData>> streamDataMap = streamData.stream().collect(Collectors.groupingBy(AmazonMarketingStreamData::getWeekday));
        HourConvert.weeKs.forEach(key -> {
            Map<String, AmazonMarketingStreamData> streamData1 = streamDataMap.getOrDefault(key, new ArrayList<>()).stream().collect(Collectors.toMap(AmazonMarketingStreamData::getTime, Function.identity()));
            HourConvert.HOUR_FEED_SORT_MAP.forEach((k, value) -> {
                AdCampaignHourVo vo = hourlyFunc.apply(key, streamData1.getOrDefault(k, new AmazonMarketingStreamData()), value);
                voList.add(vo);
            });
        });
    }

    /**
     * 7 * 24条变成7条，每条中有个属性表示24条；
     * @param hourVos
     * @return
     */
    private List<AdCommonWeekDayVo> convertToWeekDayVos(List<AdCommonHourVo> hourVos) {
        List<AdCommonWeekDayVo> adCommonWeekDayVos = new ArrayList<>(7);
        //按周汇总数据，变成7条
        Map<Integer, List<AdCommonHourVo>> map = hourVos.stream().collect(Collectors.groupingBy(AdCommonHourVo::getWeekDay));
        map.forEach((key, value) -> {
            //新增占比数据指标
            AdMetricDto adMetricDto = new AdMetricDto();
            //计算某天指标数据总和，并放入adMetricDto中
            computeMetricDataForDaySum(value, adMetricDto);
            //将占比数据(某个小时在某天的占比)填充进value属性中
            fillProportionDataForHour(value, adMetricDto);
            value.sort(Comparator.comparing(AdCommonHourVo::getHour));
            AdCommonWeekDayVo adCommonWeekDayVo = new AdCommonWeekDayVo();
            adCommonWeekDayVo.setDetails(value);
            adCommonWeekDayVo.setWeekDay(key);
            adCommonWeekDayVo.staticsFromHourVos(value);
            adCommonWeekDayVo.afterPropertiesSet();
            BigDecimal vcpmCost = value.stream().map(AdCommonHourVo::getVcpmCost).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            Long vcpmImpressionsSum = value.stream().mapToLong(AdCommonHourVo::getVcpmImpressions).sum();
            //Long impressionsSum = value.stream().mapToLong(AdCommonHourVo::getImpressions).sum();
            Long totalImpressionsSum = value.stream().mapToLong(AdCommonHourVo::getTotalImpressions).sum();
            //Long clicksSum = value.stream().mapToLong(AdCommonHourVo::getClicks).sum();
            Long totalClicksSum = value.stream().mapToLong(AdCommonHourVo::getTotalClicks).sum();
            BigDecimal adSaleSum = value.stream().map(AdCommonHourVo::getAdSale).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            BigDecimal totalAdSaleSum = value.stream().map(AdCommonHourVo::getTotalAdSale).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            //BigDecimal adSelfSaleSum = value.stream().map(AdCommonHourVo::getAdSelfSale).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            BigDecimal totalAdSelfSaleSum = value.stream().map(AdCommonHourVo::getTotalAdSelfSale).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            long adOrderNumSum = value.stream().mapToLong(AdCommonHourVo::getAdOrderNum).sum();
            long adSaleNumSum = value.stream().mapToLong(AdCommonHourVo::getAdSaleNum).sum();
            long selfAdOrderNumSum = value.stream().mapToLong(AdCommonHourVo::getSelfAdOrderNum).sum();
            long otherAdOrderNumSum = value.stream().mapToLong(AdCommonHourVo::getOtherAdOrderNum).sum();
            long ordersNewToBrandSum = value.stream().mapToLong(AdCommonHourVo::getOrdersNewToBrand).sum();
            long unitsOrderedNewToBrandSum = value.stream().mapToLong(AdCommonHourVo::getUnitsOrderedNewToBrand).sum();
            Long viewableImpressionsSum = value.stream().mapToLong(AdCommonHourVo::getViewableImpressions).sum();
            BigDecimal salesNewToBrand = value.stream().map(AdCommonHourVo::getSalesNewToBrand).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);

            BigDecimal vCpm = MathUtil.divideByThousand(vcpmCost, viewableImpressionsSum);
            BigDecimal vrt = MathUtil.divideIntegerByOneHundred(viewableImpressionsSum, totalImpressionsSum);
            BigDecimal vCtr = MathUtil.divideIntegerByOneHundred(totalClicksSum, viewableImpressionsSum);
            BigDecimal advertisingUnitPrice = MathUtil.divideByZero(adSaleSum, BigDecimal.valueOf(adOrderNumSum));
            BigDecimal advertisingProductUnitPrice = MathUtil.divideByZero(totalAdSelfSaleSum, BigDecimal.valueOf(selfAdOrderNumSum));
            BigDecimal advertisingOtherProductUnitPrice = MathUtil.divideByZero(totalAdSaleSum.subtract(totalAdSelfSaleSum), BigDecimal.valueOf(otherAdOrderNumSum));
            BigDecimal ordersNewToBrandPercentage = MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(ordersNewToBrandSum), BigDecimal.valueOf(100)),BigDecimal.valueOf(adOrderNumSum));
            BigDecimal unitsOrderedNewToBrandPercentage = MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(unitsOrderedNewToBrandSum), BigDecimal.valueOf(100)),BigDecimal.valueOf(adSaleNumSum));
            BigDecimal salesNewToBrandPercentage = MathUtil.divideByZero(MathUtil.multiply(salesNewToBrand, BigDecimal.valueOf(100)), adSaleSum);

            adCommonWeekDayVo.setViewableImpressions(viewableImpressionsSum.intValue());
            adCommonWeekDayVo.setTotalAdSale(totalAdSaleSum);
            adCommonWeekDayVo.setTotalAdSelfSale(totalAdSelfSaleSum);
            adCommonWeekDayVo.setTotalImpressions(totalImpressionsSum);
            adCommonWeekDayVo.setTotalClicks(totalClicksSum);
            adCommonWeekDayVo.setVcpmCost(vcpmCost);
            adCommonWeekDayVo.setVcpmImpressions(vcpmImpressionsSum);
            adCommonWeekDayVo.setVcpm(vCpm);
            adCommonWeekDayVo.setVrt(vrt);
            adCommonWeekDayVo.setVCtr(vCtr);
            adCommonWeekDayVo.setAdvertisingUnitPrice(advertisingUnitPrice);
            adCommonWeekDayVo.setAdvertisingProductUnitPrice(advertisingProductUnitPrice);
            adCommonWeekDayVo.setAdvertisingOtherProductUnitPrice(advertisingOtherProductUnitPrice);
            adCommonWeekDayVo.setOrdersNewToBrandPercentage(ordersNewToBrandPercentage);
            adCommonWeekDayVo.setUnitsOrderedNewToBrandPercentage(unitsOrderedNewToBrandPercentage);
            adCommonWeekDayVo.setSalesNewToBrandPercentage(salesNewToBrandPercentage);
            adCommonWeekDayVos.add(adCommonWeekDayVo);
        });
        if (CollectionUtils.isNotEmpty(adCommonWeekDayVos)) {
            AdMetricDto adMetricDto = new AdMetricDto();
            computeMetricDataForWeekSum(adCommonWeekDayVos, adMetricDto);
            fillProportionDataForDay(adCommonWeekDayVos, adMetricDto);
            adCommonWeekDayVos.sort(Comparator.comparing(AdCommonWeekDayVo::getWeekDay));
        }
        return adCommonWeekDayVos;
    }

    /**
     * 计算某天指标数据总和，并放入adMetricDto中
     * @param voList voList
     * @param adMetricDto 保存结果dto
     */
    private void computeMetricDataForDaySum(List<AdCommonHourVo> voList, AdMetricDto adMetricDto) {
        if (CollectionUtils.isEmpty(voList)) {
            return;
        }
        for (AdCommonHourVo vo : voList) {
            Optional.ofNullable(vo).filter(item -> Objects.nonNull(item.getAdCost())).
                map(AdCommonHourVo::getAdCost).ifPresent(v -> adMetricDto.setSumCost(Optional.ofNullable(adMetricDto.getSumCost()).orElse(BigDecimal.ZERO).add(v)));
            Optional.ofNullable(vo).filter(item -> Objects.nonNull(item.getAdSale())).
                map(AdCommonHourVo::getAdSale).ifPresent(v -> adMetricDto.setSumAdSale(Optional.ofNullable(adMetricDto.getSumAdSale()).orElse(BigDecimal.ZERO).add(v)));
            Optional.ofNullable(vo).filter(item -> Objects.nonNull(item.getAdOrderNum())).
                map(AdCommonHourVo::getAdOrderNum).map(BigDecimal::valueOf).ifPresent(v -> adMetricDto.setSumAdOrderNum(Optional.ofNullable(adMetricDto.getSumAdOrderNum()).orElse(BigDecimal.ZERO).add(v)));
            Optional.ofNullable(vo).filter(item -> Objects.nonNull(item.getAdSaleNum())).
                map(AdCommonHourVo::getAdSaleNum).map(BigDecimal::valueOf).ifPresent(v -> adMetricDto.setSumOrderNum(Optional.ofNullable(adMetricDto.getSumOrderNum()).orElse(BigDecimal.ZERO).add(v)));
        }
    }

    /**
     * 将占比数据(某个小时在某天的占比)填充进value属性中
     * @param voList  voList
     * @param adMetricDto 指标总和数据
     */
    private void fillProportionDataForHour(List<AdCommonHourVo> voList, AdMetricDto adMetricDto) {
        for (AdCommonHourVo vo : voList) {
            if (adMetricDto == null) {
                vo.setAdCostPercentage(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
                vo.setAdSalePercentage(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
                vo.setAdOrderNumPercentage(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
                vo.setOrderNumPercentage(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
                continue;
            }
            computeAndSetProportionDataForHour(adMetricDto, vo);
        }
    }

    /**
     * 计算 周 指标数据总和，并放入adMetricDto中
     * @param voList
     * @param adMetricDto
     */
    private void computeMetricDataForWeekSum(List<? extends AdCommonWeekDayVo> voList, AdMetricDto adMetricDto) {
        if (CollectionUtils.isEmpty(voList)) {
            return;
        }
        adMetricDto.setSumCost(voList.stream().filter(item -> item != null && item.getAdCost() != null).map(AdCommonWeekDayVo::getAdCost).reduce(BigDecimal.ZERO, BigDecimal::add));
        adMetricDto.setSumAdSale(voList.stream().filter(item -> item != null && item.getAdSale() != null).map(AdCommonWeekDayVo::getAdSale).reduce(BigDecimal.ZERO, BigDecimal::add));
        adMetricDto.setSumAdOrderNum(BigDecimal.valueOf(voList.stream().filter(item -> item != null && item.getAdOrderNum() != null).mapToInt(AdCommonWeekDayVo::getAdOrderNum).sum()));
        adMetricDto.setSumOrderNum(BigDecimal.valueOf(voList.stream().filter(item -> item != null && item.getAdSaleNum() != null).mapToInt(AdCommonWeekDayVo::getAdSaleNum).sum()));
    }

    /**
     * 将占比数据(某天在周的占比)填充进voList中
     * @param voList
     * @param adMetricDto
     */
    private void fillProportionDataForDay(List<? extends AdCommonWeekDayVo> voList, AdMetricDto adMetricDto) {
        for (AdCommonWeekDayVo vo : voList) {
            if (adMetricDto == null) {
                vo.setAdCostPercentage(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
                vo.setAdSalePercentage(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
                vo.setAdOrderNumPercentage(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
                vo.setOrderNumPercentage(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
                continue;
            }
            computeAndSetProportionDataForDay(adMetricDto, vo);
        }
    }

    /**
     * 将占比数据(某天在周的占比)填充进vo属性中
     * @param adMetricDto
     * @param vo
     */
    private void computeAndSetProportionDataForDay(AdMetricDto adMetricDto, AdCommonWeekDayVo vo) {
        // 花费占比：单条广告的花费占所查询结果中所有广告花费之和的比例 单个广告花费/查询结果广告花费之和*100%
        if (vo.getAdCost() != null
            && adMetricDto.getSumCost() != null
            && adMetricDto.getSumCost().doubleValue() > 0) {
            vo.setAdCostPercentage(new BigDecimal(MathUtil.multiply(MathUtil.divide(vo.getAdCost().toString(), adMetricDto.getSumCost().toString()), "100")).setScale(2, RoundingMode.HALF_UP));
        }

        // 销售额占比：单条广告的销售额占查询结果中所有广告销售额之和的比例 单个广告销售额/查询结果广告销售额之和*100%
        if (vo.getAdSale() != null
            && adMetricDto.getSumAdSale() != null
            && adMetricDto.getSumAdSale().doubleValue() > 0) {
            vo.setAdSalePercentage(new BigDecimal(MathUtil.multiply(MathUtil.divide(vo.getAdSale().toString(), adMetricDto.getSumAdSale().toString()), "100")).setScale(2, RoundingMode.HALF_UP));
        }

        // 广告订单数占比：单条广告的订单数占查询结果中所有广告订单数的比例 单个广告订单数/查询结果广告订单数之和*100%
        if (vo.getAdOrderNum() != null
            && adMetricDto.getSumAdOrderNum() != null
            && adMetricDto.getSumAdOrderNum().doubleValue() > 0) {
            vo.setAdOrderNumPercentage(new BigDecimal(MathUtil.multiply(MathUtil.divide(vo.getAdOrderNum().toString(), adMetricDto.getSumAdOrderNum().toString()), "100")).setScale(2, RoundingMode.HALF_UP));
        }

        // 广告销量占比：单条广告的销量占比查询结果中所有广告销量之和的比例 单个广告组合广告销量/查询结果广告组合广告销量之和*100%
        if (vo.getAdSaleNum() != null
            && adMetricDto.getSumOrderNum() != null
            && adMetricDto.getSumOrderNum().doubleValue() > 0) {
            vo.setOrderNumPercentage(new BigDecimal(MathUtil.multiply(MathUtil.divide(vo.getAdSaleNum().toString(), adMetricDto.getSumOrderNum().toString()), "100")).setScale(2, RoundingMode.HALF_UP));
        }
    }

    private void computeAndSetProportionDataForHour(AdMetricDto adMetricDto, AdCommonHourVo vo) {
        // 花费占比：单条广告的花费占所查询结果中所有广告花费之和的比例 单个广告花费/查询结果广告花费之和*100%
        if (vo.getAdCost() != null
            && adMetricDto.getSumCost() != null
            && adMetricDto.getSumCost().doubleValue() > 0) {
            vo.setAdCostPercentage(new BigDecimal(MathUtil.multiply(MathUtil.divide(vo.getAdCost().toString(), adMetricDto.getSumCost().toString()), "100")).setScale(2, RoundingMode.HALF_UP));
        }

        // 销售额占比：单条广告的销售额占查询结果中所有广告销售额之和的比例 单个广告销售额/查询结果广告销售额之和*100%
        if (vo.getAdSale() != null
            && adMetricDto.getSumAdSale() != null
            && adMetricDto.getSumAdSale().doubleValue() > 0) {
            vo.setAdSalePercentage(new BigDecimal(MathUtil.multiply(MathUtil.divide(vo.getAdSale().toString(), adMetricDto.getSumAdSale().toString()), "100")).setScale(2, RoundingMode.HALF_UP));
        }

        // 广告订单数占比：单条广告的订单数占查询结果中所有广告订单数的比例 单个广告订单数/查询结果广告订单数之和*100%
        if (vo.getAdOrderNum() != null
            && adMetricDto.getSumAdOrderNum() != null
            && adMetricDto.getSumAdOrderNum().doubleValue() > 0) {
            vo.setAdOrderNumPercentage(new BigDecimal(MathUtil.multiply(MathUtil.divide(vo.getAdOrderNum().toString(), adMetricDto.getSumAdOrderNum().toString()), "100")).setScale(2, RoundingMode.HALF_UP));
        }

        // 广告销量占比：单条广告的销量占比查询结果中所有广告销量之和的比例 单个广告组合广告销量/查询结果广告组合广告销量之和*100%
        if (vo.getAdSaleNum() != null
            && adMetricDto.getSumOrderNum() != null
            && adMetricDto.getSumOrderNum().doubleValue() > 0) {
            vo.setOrderNumPercentage(new BigDecimal(MathUtil.multiply(MathUtil.divide(vo.getAdSaleNum().toString(), adMetricDto.getSumOrderNum().toString()), "100")).setScale(2, RoundingMode.HALF_UP));
        }
    }


    private AdReportHourlyVO convertTo(AmazonMarketingStreamData data) {
        AdReportHourlyVO vo = new AdReportHourlyVO();
        if (data == null) {
            return vo;
        }
        vo.setWeekDay(data.getWeekday());
        vo.setClicks(data.getClicks());
        vo.setAdOrderNum(data.getAttributedConversions7d());
        vo.setSelfAdOrderNum(data.getAttributedConversions7dSameSku());
        vo.setOtherAdOrderNum(MathUtil.subtractInteger(data.getAttributedConversions7d(), data.getAttributedConversions7dSameSku()));
        vo.setAdSale(data.getAttributedSales7d().setScale(4, RoundingMode.HALF_UP));
        vo.setAdSelfSale(data.getAttributedSales7dSameSku().setScale(4, RoundingMode.HALF_UP));
        vo.setAdOtherSale(MathUtil.subtract(data.getAttributedSales7d(), data.getAttributedSales7dSameSku()).setScale(4, RoundingMode.HALF_UP));
        vo.setAdSaleNum(data.getAttributedUnitsOrdered7d());
        vo.setAdSelfSaleNum(data.getAttributedUnitsOrdered7dSameSku());
        vo.setAdOtherSaleNum(MathUtil.subtractInteger(data.getAttributedUnitsOrdered7d(), data.getAttributedUnitsOrdered7dSameSku()));
        vo.setImpressions(data.getImpressions());
        LocalTime localTime = LocalTime.parse(data.getTime(), DateTimeFormatter.ISO_TIME);
        vo.setAdCost(BigDecimal.valueOf(data.getCost()));
        vo.setLabel(HourConvert.hourMap.get(localTime.getHour()));
        vo.setAdCostPerClick(MathUtil.divideByZero(vo.getAdCost(), BigDecimal.valueOf(vo.getClicks())));
        vo.setCpa(vo.getAdOrderNum() == 0 ? BigDecimal.ZERO : vo.getAdCost().divide(BigDecimal.valueOf(vo.getAdOrderNum()), 4, RoundingMode.HALF_UP));
        vo.setAcos(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdCost().multiply(new BigDecimal("100")).divide(vo.getAdSale(), 4, RoundingMode.HALF_UP));
        vo.setRoas(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdSale().divide(vo.getAdCost(), 4, RoundingMode.HALF_UP));
        vo.setCtr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getClicks()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getImpressions())));
        vo.setCvr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getAdOrderNum()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getClicks())));
        return vo;
    }

    private AdReportHourlyVO convertTo(AdReportWeeklyResponsePb.HourlyReportWeekData data) {
        AdReportHourlyVO vo = new AdReportHourlyVO();
        if (data == null) {
            return vo;
        }
        vo.setWeekDay(data.getWeekday());
        vo.setClicks(data.getClicks());
        vo.setAdOrderNum(data.getAttributedConversions7D());
        vo.setSelfAdOrderNum(data.getAttributedConversions7DSameSku());
        vo.setOtherAdOrderNum(MathUtil.subtractInteger(data.getAttributedConversions7D(), data.getAttributedConversions7DSameSku()));
        vo.setAdSale(BigDecimal.valueOf(data.getAttributedSales7D()).setScale(4, RoundingMode.HALF_UP));
        vo.setAdSelfSale(BigDecimal.valueOf(data.getAttributedSales7DSameSku()).setScale(4, RoundingMode.HALF_UP));
        vo.setAdOtherSale(MathUtil.subtract(BigDecimal.valueOf(data.getAttributedSales7D()), BigDecimal.valueOf(data.getAttributedSales7DSameSku())).setScale(4, RoundingMode.HALF_UP));
        vo.setAdSaleNum(data.getAttributedUnitsOrdered7D());
        vo.setAdSelfSaleNum(data.getAttributedUnitsOrdered7DSameSku());
        vo.setAdOtherSaleNum(MathUtil.subtractInteger(data.getAttributedUnitsOrdered7D(), data.getAttributedUnitsOrdered7DSameSku()));
        vo.setImpressions(data.getImpressions());
        LocalTime localTime = LocalTime.parse(data.getTime(), DateTimeFormatter.ISO_TIME);
        vo.setAdCost(BigDecimal.valueOf(data.getCost()));
        vo.setLabel(HourConvert.hourMap.get(localTime.getHour()));
        vo.setAdCostPerClick(MathUtil.divideByZero(vo.getAdCost(), BigDecimal.valueOf(vo.getClicks())));
        vo.setCpa(vo.getAdOrderNum() == 0 ? BigDecimal.ZERO : vo.getAdCost().divide(BigDecimal.valueOf(vo.getAdOrderNum()), 4, RoundingMode.HALF_UP));
        vo.setAcos(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdCost().multiply(new BigDecimal("100")).divide(vo.getAdSale(), 4, RoundingMode.HALF_UP));
        vo.setRoas(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdSale().divide(vo.getAdCost(), 4, RoundingMode.HALF_UP));
        vo.setCtr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getClicks()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getImpressions())));
        vo.setCvr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getAdOrderNum()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getClicks())));
        return vo;
    }

    private List<AdReportWeeklyDayVO> convertToHourOfWeekDayVos(List<AdReportHourlyVO> hourVos) {
        //按周汇总数据
        Map<Integer, List<AdReportHourlyVO>> hourVoMap =
                hourVos.stream().filter(Objects::nonNull).collect(Collectors.groupingBy(AdReportHourlyVO::getWeekDay));

        List<AdReportWeeklyDayVO> adReportWeekDayVos = new ArrayList<>(7);

        for (Map.Entry<Integer, List<AdReportHourlyVO>> entry : hourVoMap.entrySet()) {
            AdReportWeeklyDayVO adWeekDayVo = new AdReportWeeklyDayVO();
            List<AdReportHourlyVO> asinHourVos = entry.getValue();
            //新增占比数据指标
            AdMetricDto adMetricDto = new AdMetricDto();
            HourlyAndWeeklyDataHandler.filterSumMetricData(asinHourVos, adMetricDto);
            HourlyAndWeeklyDataHandler.filterMetricData(asinHourVos, adMetricDto);
            adWeekDayVo.setDetails(asinHourVos);
            adWeekDayVo.staticsFromHourVos(asinHourVos);
            adWeekDayVo.calculateCompareRate();
            adWeekDayVo.setWeekDay(entry.getKey());
            adReportWeekDayVos.add(adWeekDayVo);
        }
        AdMetricDto adMetricDto1 = new AdMetricDto();
        sumMetricData(adReportWeekDayVos, adMetricDto1);
        metricData(adReportWeekDayVos, adMetricDto1);
        return adReportWeekDayVos.stream().collect(Collectors.toList());
    }

    private void sumMetricData(List<? extends AdReportWeeklyDayVO> voList, AdMetricDto adMetricDto) {
        if (CollectionUtils.isEmpty(voList)) {
            return;
        }
        adMetricDto.setSumCost(voList.stream().filter(item -> item != null && item.getAdCost() != null).map(AdReportWeeklyDayVO::getAdCost).reduce(BigDecimal.ZERO, BigDecimal::add));
        adMetricDto.setSumAdSale(voList.stream().filter(item -> item != null && item.getAdSale() != null).map(AdReportWeeklyDayVO::getAdSale).reduce(BigDecimal.ZERO, BigDecimal::add));
        adMetricDto.setSumAdOrderNum(BigDecimal.valueOf(voList.stream().filter(item -> item != null && item.getAdOrderNum() != null).mapToInt(AdReportWeeklyDayVO::getAdOrderNum).sum()));
        adMetricDto.setSumOrderNum(BigDecimal.valueOf(voList.stream().filter(item -> item != null && item.getAdSaleNum() != null).mapToInt(AdReportWeeklyDayVO::getAdSaleNum).sum()));
    }

    // 填充指标占比数据
    private void metricData(List<? extends AdReportWeeklyDayVO> voList, AdMetricDto adMetricDto) {
        for (AdReportWeeklyDayVO vo : voList) {
            if (adMetricDto == null) {
                vo.setAdCostPercentage(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
                vo.setAdSalePercentage(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
                vo.setAdOrderNumPercentage(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
                vo.setOrderNumPercentage(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
                continue;
            }
            metricData(adMetricDto, vo);
        }
    }

    private void metricData(AdMetricDto adMetricDto, AdReportWeeklyDayVO vo) {
        // 花费占比：单条广告的花费占所查询结果中所有广告花费之和的比例 单个广告花费/查询结果广告花费之和*100%
        if (vo.getAdCost() != null
                && adMetricDto.getSumCost() != null
                && adMetricDto.getSumCost().doubleValue() > 0) {
            vo.setAdCostPercentage(new BigDecimal(MathUtil.multiply(MathUtil.divide(vo.getAdCost().toString(), adMetricDto.getSumCost().toString()), "100")).setScale(2, RoundingMode.HALF_UP));
        }

        // 销售额占比：单条广告的销售额占查询结果中所有广告销售额之和的比例 单个广告销售额/查询结果广告销售额之和*100%
        if (vo.getAdSale() != null
                && adMetricDto.getSumAdSale() != null
                && adMetricDto.getSumAdSale().doubleValue() > 0) {
            vo.setAdSalePercentage(new BigDecimal(MathUtil.multiply(MathUtil.divide(vo.getAdSale().toString(), adMetricDto.getSumAdSale().toString()), "100")).setScale(2, RoundingMode.HALF_UP));
        }

        // 广告订单数占比：单条广告的订单数占查询结果中所有广告订单数的比例 单个广告订单数/查询结果广告订单数之和*100%
        if (vo.getAdOrderNum() != null
                && adMetricDto.getSumAdOrderNum() != null
                && adMetricDto.getSumAdOrderNum().doubleValue() > 0) {
            vo.setAdOrderNumPercentage(new BigDecimal(MathUtil.multiply(MathUtil.divide(vo.getAdOrderNum().toString(), adMetricDto.getSumAdOrderNum().toString()), "100")).setScale(2, RoundingMode.HALF_UP));
        }

        // 广告销量占比：单条广告的销量占比查询结果中所有广告销量之和的比例 单个广告组合广告销量/查询结果广告组合广告销量之和*100%
        if (vo.getAdSaleNum() != null
                && adMetricDto.getSumOrderNum() != null
                && adMetricDto.getSumOrderNum().doubleValue() > 0) {
            vo.setOrderNumPercentage(new BigDecimal(MathUtil.multiply(MathUtil.divide(vo.getAdSaleNum().toString(), adMetricDto.getSumOrderNum().toString()), "100")).setScale(2, RoundingMode.HALF_UP));
        }
    }

    public static AdReportWeeklyDayVO summaryCampaignOfWeekVo(List<AdReportWeeklyDayVO> list) {
        AdReportWeeklyDayVO vo = new AdReportWeeklyDayVO();
        if (CollectionUtils.isEmpty(list)) {
            return vo;
        }
        for (AdReportWeeklyDayVO ad : list) {
            vo.setClicks(MathUtil.add(ad.getClicks(), vo.getClicks()));
            vo.setAdOrderNum(MathUtil.add(ad.getAdOrderNum(), vo.getAdOrderNum()));
            vo.setSelfAdOrderNum(MathUtil.add(ad.getSelfAdOrderNum(), vo.getSelfAdOrderNum()));
            vo.setOtherAdOrderNum(MathUtil.add(ad.getOtherAdOrderNum(), vo.getOtherAdOrderNum()));
            vo.setAdSale(MathUtil.add(ad.getAdSale(), vo.getAdSale()));
            vo.setAdSelfSale(MathUtil.add(ad.getAdSelfSale(), vo.getAdSelfSale()));
            vo.setAdOtherSale(MathUtil.add(ad.getAdOtherSale(), vo.getAdOtherSale()));
            vo.setAdSaleNum(MathUtil.add(ad.getAdSaleNum(), vo.getAdSaleNum()));
            vo.setAdSelfSaleNum(MathUtil.add(ad.getAdSelfSaleNum(), vo.getAdSelfSaleNum()));
            vo.setAdOtherSaleNum(MathUtil.add(ad.getAdOtherSaleNum(), vo.getAdOtherSaleNum()));
            vo.setImpressions(MathUtil.add(vo.getImpressions(), ad.getImpressions()));
            vo.setAdCost(MathUtil.add(ad.getAdCost(), vo.getAdCost()));
            vo.setAdCostPercentage(MathUtil.add(vo.getAdCostPercentage(), ad.getAdCostPercentage()));
            vo.setAdSalePercentage(MathUtil.add(vo.getAdSalePercentage(), ad.getAdSalePercentage()));
            vo.setAdOrderNumPercentage(MathUtil.add(vo.getAdOrderNumPercentage(), ad.getAdOrderNumPercentage()));
            vo.setOrderNumPercentage(MathUtil.add(vo.getOrderNumPercentage(), ad.getOrderNumPercentage()));
            vo.setViewableImpressions(MathUtil.add(vo.getViewableImpressions(), ad.getViewableImpressions()));
            vo.setOrdersNewToBrand(MathUtil.add(vo.getOrdersNewToBrand(), ad.getOrdersNewToBrand()));
            vo.setUnitsOrderedNewToBrand(MathUtil.add(vo.getUnitsOrderedNewToBrand(), ad.getUnitsOrderedNewToBrand()));
            vo.setSalesNewToBrand(MathUtil.add(vo.getSalesNewToBrand(), ad.getSalesNewToBrand()));

            vo.setAdCostCompare(MathUtil.add(vo.getAdCostCompare(), ad.getAdCostCompare()));
            vo.setClicksCompare(MathUtil.add(vo.getClicksCompare(), ad.getClicksCompare()));
            vo.setImpressionsCompare(MathUtil.add(vo.getImpressionsCompare(), ad.getImpressionsCompare()));
            vo.setAdSaleNumCompare(MathUtil.add(vo.getAdSaleNumCompare(), ad.getAdSaleNumCompare()));
            vo.setAdSaleCompare(MathUtil.add(vo.getAdSaleCompare(), ad.getAdSaleCompare()));
            vo.setAdOrderNumCompare(MathUtil.add(vo.getAdOrderNumCompare(), ad.getAdOrderNumCompare()));

        }
        vo.setAdCostPerClick(MathUtil.divideByZero(vo.getAdCost(), BigDecimal.valueOf(vo.getClicks())));
        vo.setCpa(vo.getAdOrderNum() == 0 ? BigDecimal.ZERO : vo.getAdCost().divide(BigDecimal.valueOf(vo.getAdOrderNum()), 4, RoundingMode.HALF_UP));
        vo.setAcos(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdCost().multiply(new BigDecimal("100")).divide(vo.getAdSale(), 4, RoundingMode.HALF_UP));
        vo.setRoas(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdSale().divide(vo.getAdCost(), 4, RoundingMode.HALF_UP));
        vo.setCtr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getClicks()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getImpressions())));
        vo.setCvr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getAdOrderNum()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getClicks())));
        vo.setAdCostPerClickCompare(MathUtil.divideByZero(vo.getAdCostCompare(), BigDecimal.valueOf(vo.getClicksCompare())));
        vo.setCvrCompare(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getAdOrderNumCompare()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getClicksCompare())));
        vo.setCtrCompare(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getClicksCompare()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getImpressionsCompare())));
        vo.setRoasCompare(vo.getAdSaleCompare().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCostCompare().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdSaleCompare().divide(vo.getAdCostCompare(), 4, RoundingMode.HALF_UP));
        vo.setAcosCompare(vo.getAdSaleCompare().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCostCompare().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdCostCompare().multiply(new BigDecimal("100")).divide(vo.getAdSaleCompare(), 4, RoundingMode.HALF_UP));
        vo.calculateCompareRate();//为对比率各属性设值
        if (vo.getAdCostPercentage() == null || BigDecimal.ZERO.compareTo(vo.getAdCostPercentage()) == 0) {
            vo.setAdCostPercentage(BigDecimal.ZERO.setScale(2));
        } else {
            vo.setAdCostPercentage(BigDecimal.valueOf(100).setScale(2));
        }
        if (vo.getAdSalePercentage() == null || BigDecimal.ZERO.compareTo(vo.getAdSalePercentage()) == 0) {
            vo.setAdSalePercentage(BigDecimal.ZERO.setScale(2));
        } else {
            vo.setAdSalePercentage(BigDecimal.valueOf(100).setScale(2));
        }
        if (vo.getAdOrderNumPercentage() == null || BigDecimal.ZERO.compareTo(vo.getAdOrderNumPercentage()) == 0) {
            vo.setAdOrderNumPercentage(BigDecimal.ZERO.setScale(2));
        } else {
            vo.setAdOrderNumPercentage(BigDecimal.valueOf(100).setScale(2));
        }
        if (vo.getOrderNumPercentage() == null || BigDecimal.ZERO.compareTo(vo.getOrderNumPercentage()) == 0) {
            vo.setOrderNumPercentage(BigDecimal.ZERO.setScale(2));
        } else {
            vo.setOrderNumPercentage(BigDecimal.valueOf(100).setScale(2));
        }

        vo.setVcpm(MathUtil.divideByThousand(vo.getAdCost(), vo.getImpressions()));
        vo.setVrt(MathUtil.divideIntegerByOneHundred(vo.getViewableImpressions(), vo.getImpressions()));
        vo.setVCtr(MathUtil.divideIntegerByOneHundred(vo.getClicks(), vo.getViewableImpressions()));
        vo.setAdvertisingUnitPrice(MathUtil.divideByZero(vo.getAdSale(), BigDecimal.valueOf(vo.getAdOrderNum())));
        vo.setAdvertisingProductUnitPrice(MathUtil.divideByZero(vo.getAdSelfSale(), BigDecimal.valueOf(vo.getSelfAdOrderNum())));
        vo.setAdvertisingOtherProductUnitPrice(MathUtil.divideByZero(vo.getAdSale().subtract(vo.getAdSelfSale()), BigDecimal.valueOf(vo.getAdOrderNum() - vo.getSelfAdOrderNum())));
        vo.setOrdersNewToBrandPercentage(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getOrdersNewToBrand()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getAdOrderNum())));
        vo.setUnitsOrderedNewToBrandPercentage(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getUnitsOrderedNewToBrand()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getAdSaleNum())));
        vo.setSalesNewToBrandPercentage(MathUtil.divideByZero(MathUtil.multiply(vo.getSalesNewToBrand(), BigDecimal.valueOf(100)), vo.getAdSale()));

        return vo;
    }

    private static BigDecimal calculateAdCostPerClick(BigDecimal adCost, long clicks) {
        return MathUtil.divideByZero(adCost, BigDecimal.valueOf(clicks));
    }

    private static BigDecimal calculateCpa(BigDecimal adCost, int adOrderNum) {
        return adOrderNum == 0 ? BigDecimal.ZERO : adCost.divide(BigDecimal.valueOf(adOrderNum), 4, RoundingMode.HALF_UP);
    }

    private static BigDecimal calculateAcos(BigDecimal adCost, BigDecimal adSale) {
        return adSale.compareTo(BigDecimal.ZERO) == 0 || adCost.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : adCost.multiply(BigDecimal.valueOf(100)).divide(adSale, 4, RoundingMode.HALF_UP);
    }

    private static BigDecimal calculateRoas(BigDecimal adSale, BigDecimal adCost) {
        return adSale.compareTo(BigDecimal.ZERO) == 0 || adCost.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : adSale.divide(adCost, 4, RoundingMode.HALF_UP);
    }

    private static BigDecimal calculateCtr(long clicks, long impressions) {
        return MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(clicks), BigDecimal.valueOf(100)), BigDecimal.valueOf(impressions));
    }

    private static BigDecimal calculateCvr(int adOrderNum, long clicks) {
        return MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(adOrderNum), BigDecimal.valueOf(100)), BigDecimal.valueOf(clicks));
    }

    private static BigDecimal calculatePercentage(BigDecimal value) {
        return value == null || BigDecimal.ZERO.compareTo(value) == 0 ? BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP) : BigDecimal.valueOf(100).setScale(2, RoundingMode.HALF_UP);
    }

    private static BigDecimal calculateVrt(int viewableImpressions, long totalImpressions) {
        return MathUtil.divideIntegerByOneHundred(viewableImpressions, totalImpressions);
    }

    private static BigDecimal calculateVCtr(long totalClicks, int viewableImpressions) {
        return MathUtil.divideIntegerByOneHundred(totalClicks, viewableImpressions);
    }

    private static BigDecimal calculateVcpm(BigDecimal vcpmCost, long vcpmImpressions) {
        return MathUtil.divideByThousand(vcpmCost, vcpmImpressions);
    }

    private static BigDecimal calculateUnitPrice(BigDecimal sale, int orderNum) {
        return MathUtil.divideByZero(sale, BigDecimal.valueOf(orderNum));
    }

    private static BigDecimal calculateOrdersNewToBrandPercentage(int ordersNewToBrand, int adOrderNum) {
        return MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(ordersNewToBrand), BigDecimal.valueOf(100)), BigDecimal.valueOf(adOrderNum));
    }

    private static BigDecimal calculateUnitsOrderedNewToBrandPercentage(int unitsOrderedNewToBrand, int adSaleNum) {
        return MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(unitsOrderedNewToBrand), BigDecimal.valueOf(100)), BigDecimal.valueOf(adSaleNum));
    }

    private static BigDecimal calculateSalesNewToBrandPercentage(BigDecimal salesNewToBrand, BigDecimal adSale) {
        return MathUtil.divideByZero(MathUtil.multiply(salesNewToBrand, BigDecimal.valueOf(100)), adSale);
    }

}
