package com.meiyunji.sponsored.service.cpc.dao.impl;


import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.springjdbc.BaseShardingDaoImpl;
import com.meiyunji.sponsored.common.springjdbc.ConditionBuilder;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdSearchTagDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdGroup;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdSearchTag;
import com.meiyunji.sponsored.service.cpc.po.AmazonSdAdTargeting;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;

@Repository
public class AmazonAdSearchTagDaoImpl extends BaseShardingDaoImpl<AmazonAdSearchTag> implements IAmazonAdSearchTagDao {

    @Override
    public List<AmazonAdSearchTag> getListByUserIdAndType(Integer puid, Integer userId, String type) {
        String sql = "select * from t_amazon_ad_search_tag where puid=? and uid=? and `type`=? and del_flag = 0 ";
        return getJdbcTemplate(puid).query(sql, new Object[]{puid, userId, type}, getMapper());
    }

    @Override
    public boolean isExistByName(Integer puid, Integer userId, String type, String name) {
        String sql = "select count(*) from t_amazon_ad_search_tag where puid=? and uid=? and `type`=? and `name`=? and del_flag = 0 ";
        Integer count = getJdbcTemplate(puid).queryForObject(sql, new Object[]{puid, userId, type, name}, Integer.class);
        return count != null && count > 0;
    }

    @Override
    public int deleteById(Long id, Integer puid, Integer userId) {
        String sql = "update t_amazon_ad_search_tag set del_flag = 1 WHERE puid = ? AND uid = ? and `id`=?";
        return getJdbcTemplate(puid).update(sql, puid, userId, id);
    }

    @Override
    public int count(Integer puid, Integer userId, String type) {
        String sql = "select count(*) from t_amazon_ad_search_tag where puid=? and uid = ? and `type`=? and del_flag = 0 ";
        return getJdbcTemplate(puid).queryForObject(sql, new Object[]{puid, userId, type}, Integer.class);
    }

    @Override
    public Integer getMaxColumnSort(Integer puid, Integer userId, String type) {
        String sql = "select max(column_sort) from t_amazon_ad_search_tag where puid=? and uid = ? and `type`=? and del_flag = 0 ";
        Integer max = getJdbcTemplate(puid).queryForObject(sql, new Object[]{puid, userId, type}, Integer.class);
        return max == null ? 0 : max;
    }

    @Override
    public AmazonAdSearchTag getByIdAndPuidAndUid(Integer puid, Integer userId, Long id) {
        ConditionBuilder conditionBuilder = new ConditionBuilder.Builder()
                .equalTo("id", id)
                .equalTo("puid", puid)
                .equalTo("uid", userId).build();
        return getByCondition(puid, conditionBuilder);
    }

    @Override
    public int updateName(Integer puid, Long id, String name) {
        return this.getJdbcTemplate(puid).update("update t_amazon_ad_search_tag set name = ?  where id = ? and puid = ?", name, id, puid);
    }

    @Override
    public int updateFilterField(Integer puid, Long id, String name) {
        return this.getJdbcTemplate(puid).update("update t_amazon_ad_search_tag set filter_field = ?  where id = ? and puid = ?", name, id, puid);
    }

    @Override
    public int updateFixedColumn(Integer puid, Long id, Boolean bool) {
        return this.getJdbcTemplate(puid).update("update t_amazon_ad_search_tag set fixed_column = ?  where id = ? and puid = ?", bool, id, puid);
    }

    @Override
    public void batchUpdateSort(Integer puid, List<AmazonAdSearchTag> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        List<Object[]> argList = new ArrayList<>(list.size());
        List<Object> arg;
        for (AmazonAdSearchTag po : list) {
            arg = new ArrayList<>(3);
            arg.add(po.getColumnSort());
            arg.add(po.getId());
            arg.add(po.getPuid());
            argList.add(arg.toArray());
        }
        String sql = "update t_amazon_ad_search_tag set column_sort = ?  where id = ? and puid = ? ";
        getJdbcTemplate(puid).batchUpdate(sql, argList);
    }

    @Override
    public List<AmazonAdSearchTag> listById(Integer puid, Integer uid, List<Long> ids) {
        ConditionBuilder conditionBuilder = new ConditionBuilder.Builder()
                .equalTo("uid", uid)
                .equalTo("puid", puid)
                .equalTo("del_flag", 0)
                .in("id", ids.toArray())
                .build();
        return listByCondition(puid, conditionBuilder);
    }

    @Override
    public int countFixedColumn(Integer puid, Integer userId, String type) {
        String sql = "select count(*) from t_amazon_ad_search_tag where puid=? and uid = ? and `type`=? and del_flag = 0 and fixed_column = 1";
        return getJdbcTemplate(puid).queryForObject(sql, new Object[]{puid, userId, type}, Integer.class);
    }

    @Override
    public int countAll(Integer puid, Integer userId, String type, String date) {
        String sql = "select count(*) from t_amazon_ad_search_tag where puid=? and uid = ? and `type`=? and create_time >= ? ";
        return getJdbcTemplate(puid).queryForObject(sql, new Object[]{puid, userId, type, date}, Integer.class);
    }

    @Override
    public void batchInsert(Integer puid, List<AmazonAdSearchTag> searchTags) {
        StringBuilder sql = new StringBuilder("INSERT INTO `t_amazon_ad_search_tag` (`puid`,`uid`,`name`,`filter_field`,")
                .append("`type`,`column_sort`,`del_flag`,`fixed_column`,create_time, update_time) VALUES ");
        List<Object> argsList = Lists.newArrayList();
        for (AmazonAdSearchTag amazonAdSearchTag : searchTags) {
            sql.append("(?,?,?,?,?,?,?,?,now(),now()),");
            argsList.add(puid);
            argsList.add(amazonAdSearchTag.getUid());
            argsList.add(amazonAdSearchTag.getName());
            argsList.add(amazonAdSearchTag.getFilterField());
            argsList.add(amazonAdSearchTag.getType());
            argsList.add(amazonAdSearchTag.getColumnSort());
            argsList.add(amazonAdSearchTag.getDelFlag());
            argsList.add(amazonAdSearchTag.getFixedColumn());
        }
        sql.deleteCharAt(sql.length() - 1);
        getJdbcTemplate(puid).update(sql.toString(), argsList.toArray());
    }
}
