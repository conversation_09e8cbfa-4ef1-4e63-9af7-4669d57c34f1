package com.meiyunji.sponsored.service.adTagSystem.service;

import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.grpc.adTagSystem.AdTagCampaignData;
import com.meiyunji.sponsored.service.adTagSystem.param.CampaignTagDataParam;
import com.meiyunji.sponsored.service.adTagSystem.param.TagAdCampaignListParam;
import com.meiyunji.sponsored.service.adTagSystem.req.QueryShopInfoReq;
import com.meiyunji.sponsored.service.adTagSystem.resp.QueryShopInfoResp;
import com.meiyunji.sponsored.service.adTagSystem.vo.CampaignTagAggregateDataVo;

import java.util.List;

/**
 * @author: liweibin
 * @email: <EMAIL>
 * @date: 2024-10-29  09:38
 */
public interface IAdManageTagPageService {
    /**
     * 广告活动标签列表页
     */
    CampaignTagAggregateDataVo getCampaignTagData(Integer puid, CampaignTagDataParam param);

    /**
     * 导出广告活动标签
     */
    void exportCampaignTagData(Integer puid, String uuid, CampaignTagDataParam param);

    Page<AdTagCampaignData> campaignList4AdTag(TagAdCampaignListParam param);

    List<QueryShopInfoResp.ShopVo> getShopInfo(QueryShopInfoReq req);
}
