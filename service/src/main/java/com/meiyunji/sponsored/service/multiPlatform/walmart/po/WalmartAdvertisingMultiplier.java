package com.meiyunji.sponsored.service.multiPlatform.walmart.po;

import com.meiyunji.sponsored.common.springjdbc.DbColumn;
import com.meiyunji.sponsored.common.springjdbc.DbTable;
import com.meiyunji.sponsored.service.annotation.AdLogFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * @author: ys
 * @date: 2025/2/21 11:02
 * @describe:
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@DbTable(value = "t_walmart_advertising_multiplier")
public class WalmartAdvertisingMultiplier implements Serializable {
    private static final long serialVersionUID = 862017304401565766L;

    /**
     * id
     */
    private Long id;

    /**
     * 商户uid
     */
    private Integer puid;

    /**
     * 店铺ID
     */
    @DbColumn(value = "shop_id")
    private Integer shopId;


    /**
     * 活动id
     */
    @DbColumn(value = "campaign_id")
    private String campaignId;

    /**
     * 类型
     */
    @AdLogFormat(value = "类型", methodStr = "getType")
    private String type;

    /**
     * 是否是平台类型0不是1是
     */
    @DbColumn(value = "is_platform")
    @AdLogFormat(value = "平台", methodStr = "getIsPlatform")
    private Integer isPlatform;

    /**
     * 原始投标修改的百分比值
     */
    @AdLogFormat(value = "竞价倍数", methodStr = "getMultiplier")
    private Double multiplier;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新的时间
     */
    private Date updateTime;
}
