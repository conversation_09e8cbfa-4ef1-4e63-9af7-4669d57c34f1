package com.meiyunji.sponsored.service.batchCreate.dao;

import com.meiyunji.sponsored.common.springjdbc.IBaseShardingDao;
import com.meiyunji.sponsored.service.batchCreate.po.AmazonAdBatchBaseInfo;

import java.util.List;

/**
 * @author: sun<PERSON><PERSON>
 * @email: sun<PERSON><PERSON>@dianxiaomi.com
 * @date: 2023-11-16  09:30
 */
public interface IAmazonAdBatchBaseInfoDao extends IBaseShardingDao<AmazonAdBatchBaseInfo> {

    void insertList(int puid, List<AmazonAdBatchBaseInfo> baseInfoList);

    int deleteByPuidAndShopId(Integer puid, Integer shopId, Integer limit);

    AmazonAdBatchBaseInfo getByPuidAndTaskId(Integer puid, Long taskId);
}
