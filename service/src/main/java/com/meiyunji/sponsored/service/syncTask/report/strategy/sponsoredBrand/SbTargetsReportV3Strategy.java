package com.meiyunji.sponsored.service.syncTask.report.strategy.sponsoredBrand;

import com.alibaba.fastjson.JSONReader;
import com.amazon.advertising.mode.MarketTimezoneAndCurrencyEnum;
import com.amazon.advertising.sb.mode.report.SbReportKeyword;
import com.amazon.advertising.sb.mode.report.SbReportTargets;
import com.google.common.collect.Lists;
import com.meiyunji.sellfox.aadas.types.enumeration.AmazonReportType;
import com.meiyunji.sellfox.aadas.types.enumeration.AmazonReportV3Type;
import com.meiyunji.sellfox.aadas.types.message.notification.ReportReadyNotification;
import com.meiyunji.sponsored.common.support.RedisConstant;
import com.meiyunji.sponsored.common.support.RedisService;
import com.meiyunji.sponsored.service.config.CosBucketClient;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdCampaignAllDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdSbKeywordReportDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdSbTargetingReportDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonSbAdsDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdSbKeywordReport;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdSbTargetingReport;
import com.meiyunji.sponsored.service.syncTask.entity.SponsoredProductTargeting;
import com.meiyunji.sponsored.service.syncTask.entity.sb.SbReportV3Targets;
import com.meiyunji.sponsored.service.syncTask.report.strategy.AbstractReportProcessStrategy;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.pulsar.client.api.Producer;
import org.apache.pulsar.client.api.PulsarClientException;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.math.BigDecimal;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.zip.GZIPInputStream;

/**
 * @author: wade
 * @date: 2021/12/28 16:32
 * @describe: sb 投放报告处理类
 */
@Component
@Slf4j
@ConditionalOnProperty(name = "aadas.scheduler.executors.reports-consumer.enabled", havingValue = "true")
public class SbTargetsReportV3Strategy extends AbstractReportProcessStrategy {

    private final IAmazonAdSbTargetingReportDao amazonAdSbTargetingReportDao;
    private final IAmazonAdCampaignAllDao amazonAdCampaignAllDao;
    private final RedisService redisService;
    private final Producer<ReportReadyNotification> reportReadyProducer;
    private final IAmazonSbAdsDao amazonSbAdsDao;
    private final IAmazonAdSbKeywordReportDao amazonAdSbKeywordReportDao;

    public SbTargetsReportV3Strategy(
            CosBucketClient dataBucketClient,
            IAmazonAdSbTargetingReportDao amazonAdSbTargetingReportDao,
            IAmazonAdCampaignAllDao amazonAdCampaignAllDao,IAmazonSbAdsDao amazonSbAdsDao,
            RedisService redisService,
            Producer<ReportReadyNotification> reportReadyProducer,IAmazonAdSbKeywordReportDao amazonAdSbKeywordReportDao) {
        super(dataBucketClient);
        this.amazonAdSbTargetingReportDao = amazonAdSbTargetingReportDao;
        this.amazonAdCampaignAllDao = amazonAdCampaignAllDao;
        this.redisService = redisService;
        this.reportReadyProducer = reportReadyProducer;
        this.amazonSbAdsDao = amazonSbAdsDao;
        this.amazonAdSbKeywordReportDao = amazonAdSbKeywordReportDao;
    }

    @Override
    public Boolean checkValid(ReportReadyNotification notification) {
        return notification.getVersion() == 3 && AmazonReportV3Type.sb_targeting == notification.getV3Type();
    }

    @Override
    public void processReport(ReportReadyNotification notification) throws IOException {
        try (InputStreamReader inputStreamReader = new InputStreamReader(
                new GZIPInputStream(new ByteArrayInputStream(
                        dataBucketClient.getObjectToBytes(notification.getPath()))))) {
            JSONReader jsonReader = new JSONReader(inputStreamReader);
            jsonReader.startArray();
            List<SbReportV3Targets> reports = Lists.newArrayListWithExpectedSize(500);
            while (jsonReader.hasNext()) {
                jsonReader.startObject();
                SbReportV3Targets report = new SbReportV3Targets();
                report.readFromJsonReader(jsonReader);
                jsonReader.endObject();
                if (report.getImpressions() != 0) {
                    reports.add(report);
                }

                if (reports.size() >= 500) {
                    dealReports(notification, reports);
                    reports = Lists.newArrayListWithExpectedSize(500);
                }
            }
            jsonReader.endArray();
            jsonReader.close();
            if (CollectionUtils.isNotEmpty(reports)) {
                dealReports(notification, reports);
            }
        } catch (Exception e) {
            log.info("报告处理发生错误{}@{} reportType={} countDate={}", notification.getSellerIdentifier()
                    , notification.getMarketplaceIdentifier(), notification.getV3Type(), notification.getV3StartDate(), e);
            throw e;
        }
    }

    private void dealReports(ReportReadyNotification notification, List<SbReportV3Targets> reports) {
        //处理target报告
        List<SbReportV3Targets> targetReports = reports.stream().filter(o -> o.getKeywordType()
                != null && Arrays.asList("TARGETING_EXPRESSION", "TARGETING_EXPRESSION_PREDEFINED")
                .contains(o.getKeywordType())).collect(Collectors.toList());
        dealTargetReports(notification, targetReports);
        //处理keyword报告
        List<SbReportV3Targets> keywordReports = reports.stream().filter(o -> o.getKeywordType()
                != null && Arrays.asList("BROAD", "PHRASE", "EXACT")
                .contains(o.getKeywordType())).collect(Collectors.toList());

        dealKeywordReports(notification, keywordReports);
    }

    private void dealKeywordReports(ReportReadyNotification notification, List<SbReportV3Targets> reports) {
        List<SbReportV3Targets> validReports = reports.stream()
                .filter(item -> BigDecimal.valueOf(item.getImpressions())
                        .compareTo(BigDecimal.ZERO) != 0).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(validReports)) {
            return;
        }
        List<AmazonAdSbKeywordReport> poList = getPoBySbReportKeywords(notification, validReports);
        List<List<AmazonAdSbKeywordReport>> partition = Lists.partition(poList, 200);
        for (List<AmazonAdSbKeywordReport> amazonAdSbKeywordReports : partition) {
            amazonAdSbKeywordReportDao.insertOrUpdateList(notification.getSellerIdentifier(), amazonAdSbKeywordReports);
        }
    }


    private void dealTargetReports(ReportReadyNotification notification, List<SbReportV3Targets> reports) {
        List<SbReportV3Targets> validReports = reports.stream().filter(item -> BigDecimal.valueOf(item.getImpressions()).compareTo(BigDecimal.ZERO) != 0).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(validReports)) {
            return;
        }
        List<AmazonAdSbTargetingReport> poList = getPoBySbReportTargets(notification, validReports);
        List<List<AmazonAdSbTargetingReport>> partition = Lists.partition(poList, 200);
        for (List<AmazonAdSbTargetingReport> amazonAdSbTargetingReports : partition) {
            amazonAdSbTargetingReportDao.insertOrUpdateList(notification.getSellerIdentifier(), amazonAdSbTargetingReports);
        }
    }

    private List<AmazonAdSbKeywordReport> getPoBySbReportKeywords(ReportReadyNotification notification, List<SbReportV3Targets> reports) {
        //获取货币单位
        String currencyCode = MarketTimezoneAndCurrencyEnum
                .getByMarketplaceId(notification.getMarketplace().getId()).getCurrencyCode();
        List<AmazonAdSbKeywordReport> list = Lists.newArrayListWithExpectedSize(reports.size());
        AmazonAdSbKeywordReport sbKeywordReport;
        boolean needRetry = false;
        for (SbReportV3Targets keyword : reports) {
            //查询sb_format
            String adFormat = amazonSbAdsDao.getSbFormatByGroupId(notification.getSellerIdentifier(),
                    notification.getMarketplaceIdentifier(), keyword.getAdGroupId().toString());
            if (StringUtils.isBlank(adFormat)) {
                log.info("广告活动管理数据未成功匹配成功,30分钟后再解析报告. puid:{} shopId : {} campaignId: {}",
                        notification.getSellerIdentifier(), notification.getMarketplaceIdentifier(), keyword.getCampaignId());
                needRetry = true;
                continue;
            }
            sbKeywordReport = new AmazonAdSbKeywordReport();
            sbKeywordReport.setPuid(notification.getSellerIdentifier());
            sbKeywordReport.setShopId(notification.getMarketplaceIdentifier());
            sbKeywordReport.setMarketplaceId(notification.getMarketplace().getId());
            sbKeywordReport.setCountDate(keyword.getDate().format(DateTimeFormatter.ofPattern("yyyyMMdd")));
            sbKeywordReport.setCurrency(currencyCode);
            sbKeywordReport.setAdFormat(adFormat);
            sbKeywordReport.setCampaignName(keyword.getCampaignName());
            sbKeywordReport.setCampaignId(keyword.getCampaignId().toString());
            sbKeywordReport.setCampaignStatus(keyword.getCampaignStatus());
            sbKeywordReport.setCampaignBudget(keyword.getCampaignBudgetAmount() != null ? keyword.getCampaignBudgetAmount().doubleValue() : null);
            sbKeywordReport.setCampaignBudgetType(keyword.getCampaignBudgetType());
//            sbKeywordReport.setCampaignRuleBasedBudget(keyword.getCampaignRuleBasedBudget());
//            sbKeywordReport.setApplicableBudgetRuleId(keyword.getApplicableBudgetRuleId());
//            sbKeywordReport.setApplicableBudgetRuleName(keyword.getApplicableBudgetRuleName());
            sbKeywordReport.setAdGroupName(keyword.getAdGroupName());
            sbKeywordReport.setAdGroupId(keyword.getAdGroupId().toString());
            sbKeywordReport.setKeywordText(keyword.getKeywordText());
            sbKeywordReport.setKeywordBid(keyword.getKeywordBid() != null ? keyword.getKeywordBid().doubleValue() : null);
            sbKeywordReport.setKeywordStatus(keyword.getAdKeywordStatus());
            sbKeywordReport.setKeywordId(keyword.getKeywordId().toString());
            sbKeywordReport.setTargetId(keyword.getTargetingId() != null ? keyword.getTargetingId().toString() : "");
            sbKeywordReport.setTargetingExpression(keyword.getTargetingExpression());
            sbKeywordReport.setTargetingText(keyword.getTargetingText());
            sbKeywordReport.setTargetingType(keyword.getTargetingType());
            sbKeywordReport.setMatchType(keyword.getMatchType());
            sbKeywordReport.setImpressions(keyword.getImpressions());
            sbKeywordReport.setClicks(keyword.getClicks());
            sbKeywordReport.setCost(keyword.getCost() != null ? keyword.getCost() : null);
            sbKeywordReport.setSales14d(keyword.getSalesClicks() != null ? keyword.getSalesClicks() : null);
            sbKeywordReport.setSales14dSameSKU(keyword.getSalesPromoted() != null ? keyword.getSalesPromoted() : null);
            sbKeywordReport.setConversions14d(keyword.getPurchasesClicks());
            sbKeywordReport.setConversions14dSameSKU(keyword.getPurchasesPromoted());
            sbKeywordReport.setDetailPageViewsClicks14d(keyword.getDetailPageViewsClicks());
            sbKeywordReport.setOrdersNewToBrand14d(keyword.getNewToBrandPurchasesClicks());
            sbKeywordReport.setOrdersNewToBrandPercentage14d(keyword.getNewToBrandPurchasesPercentage() != null ? keyword.getNewToBrandPurchasesPercentage().doubleValue() : null);
            sbKeywordReport.setOrderRateNewToBrand14d(keyword.getNewToBrandPurchasesRate() != null ? keyword.getNewToBrandPurchasesRate().doubleValue() : null);
            sbKeywordReport.setSalesNewToBrand14d(keyword.getNewToBrandSalesClicks() != null ? keyword.getNewToBrandSalesClicks() : null);
            sbKeywordReport.setSalesNewToBrandPercentage14d(keyword.getNewToBrandSalesPercentage() != null ? keyword.getNewToBrandSalesPercentage().doubleValue() : null);
            sbKeywordReport.setUnitsOrderedNewToBrand14d(keyword.getNewToBrandUnitsSoldClicks());
            sbKeywordReport.setUnitsOrderedNewToBrandPercentage14d(keyword.getNewToBrandUnitsSoldPercentage() != null ? keyword.getNewToBrandUnitsSoldPercentage().doubleValue() : null);
            sbKeywordReport.setUnitsSold14d(keyword.getUnitsSold());
//            sbKeywordReport.setDpv14d(keyword.getDpv14d());
//            sbKeywordReport.setVctr(keyword.getVctr());
            sbKeywordReport.setVideo5SecondViewRate(keyword.getVideo5SecondViewRate() != null ? keyword.getVideo5SecondViewRate().doubleValue(): null);
            sbKeywordReport.setVideo5SecondViews(keyword.getVideo5SecondViews());
            sbKeywordReport.setVideoFirstQuartileViews(keyword.getVideoFirstQuartileViews());
            sbKeywordReport.setVideoMidpointViews(keyword.getVideoMidpointViews());
            sbKeywordReport.setVideoThirdQuartileViews(keyword.getVideoThirdQuartileViews());
            sbKeywordReport.setVideoUnmutes(keyword.getVideoUnmutes());
            sbKeywordReport.setViewableImpressions(keyword.getViewableImpressions());
            sbKeywordReport.setVideoCompleteViews(keyword.getVideoCompleteViews());
//            sbKeywordReport.setVtr(keyword.getVtr());
            sbKeywordReport.setTopOfSearchIs(Optional.ofNullable(keyword.getTopOfSearchImpressionShare()).orElse(null));
            list.add(sbKeywordReport);
        }
        String cacheKey = String.format(RedisConstant.SELLFOX_AD_SB_DAILY_KEYWORD_REPORT,notification.getSellerIdentifier(),
                notification.getMarketplaceIdentifier(), notification.getV3Type().name(),
                notification.getV3StartDate().format(DateTimeFormatter.ofPattern("yyyyMMdd")));
        if (needRetry) {
            Long value = redisService.incr(cacheKey, 1L);
            if (value <= 1) {
                redisService.expire(cacheKey, 3600);
                try {
                    reportReadyProducer.newMessage().value(notification)
                            .deliverAfter(30, TimeUnit.MINUTES).send();
                } catch (PulsarClientException e) {
                    log.error("Pulsar send message with an error.", e);
                }
            }
        }
        return list;
    }


    private List<AmazonAdSbTargetingReport> getPoBySbReportTargets(ReportReadyNotification notification,
                                                                   List<SbReportV3Targets> reports) {
        //获取货币单位
        String currencyCode = MarketTimezoneAndCurrencyEnum.getByMarketplaceId(notification.getMarketplace().getId()).getCurrencyCode();
        //增加报告类型字段
        List<AmazonAdSbTargetingReport> list = Lists.newArrayListWithExpectedSize(reports.size());
        AmazonAdSbTargetingReport sbTargetReport;
        //如果无法查询到ad_format需要隔半个小时重新解析文件,重试三次
        boolean needRetry = false;
        for (SbReportV3Targets target : reports) {
            //查询sb_format
            String adFormat = amazonAdCampaignAllDao.getSbFormatByCampaignId(notification.getSellerIdentifier(),
                    notification.getMarketplaceIdentifier(), target.getCampaignId().toString());
            if (StringUtils.isBlank(adFormat)) {
                log.info("广告活动管理数据未成功匹配成功,30分钟后再解析报告. shopId : {} campaignId: {}",
                        notification.getMarketplaceIdentifier(), target.getCampaignId());
                needRetry = true;
                continue;
            }
            sbTargetReport = new AmazonAdSbTargetingReport();
            sbTargetReport.setPuid(notification.getSellerIdentifier());
            sbTargetReport.setShopId(notification.getMarketplaceIdentifier());
            sbTargetReport.setMarketplaceId(notification.getMarketplace().getId());
            sbTargetReport.setCountDate(target.getDate().format(DateTimeFormatter.ofPattern("yyyyMMdd")));
            sbTargetReport.setCurrency(currencyCode);
            sbTargetReport.setCampaignName(target.getCampaignName());
            sbTargetReport.setCampaignId(target.getCampaignId().toString());
            sbTargetReport.setCampaignStatus(target.getCampaignStatus());
            sbTargetReport.setCampaignBudget(target.getCampaignBudgetAmount() != null ? target.getCampaignBudgetAmount().doubleValue() : null);
            sbTargetReport.setCampaignBudgetType(target.getCampaignBudgetType());
            sbTargetReport.setAdGroupName(target.getAdGroupName());
            sbTargetReport.setAdGroupId(target.getAdGroupId().toString());
            sbTargetReport.setTargetId(target.getTargetingId().toString());
            sbTargetReport.setTargetingExpression(target.getTargetingExpression());
            sbTargetReport.setTargetingText(target.getTargetingText());
            sbTargetReport.setTargetingType(target.getTargetingType());
            sbTargetReport.setMatchType(target.getMatchType());
            sbTargetReport.setImpressions(target.getImpressions());
            sbTargetReport.setClicks(target.getClicks());
            sbTargetReport.setCost(target.getCost() != null ? target.getCost() : null);
            sbTargetReport.setSales14d(target.getSalesClicks() != null ? target.getSalesClicks() : null);
            sbTargetReport.setSales14dSameSKU(target.getSalesPromoted() != null ? target.getSalesPromoted() : null);
            sbTargetReport.setConversions14d(target.getPurchasesClicks());
            sbTargetReport.setConversions14dSameSKU(target.getPurchasesPromoted());
            sbTargetReport.setDetailPageViewsClicks14d(target.getDetailPageViewsClicks());
            sbTargetReport.setOrdersNewToBrand14d(target.getNewToBrandPurchasesClicks());
            sbTargetReport.setOrdersNewToBrandPercentage14d(target.getNewToBrandPurchasesPercentage() != null ? target.getNewToBrandPurchasesPercentage().doubleValue() : null);
            sbTargetReport.setOrderRateNewToBrand14d(target.getNewToBrandPurchasesRate() != null ? target.getNewToBrandPurchasesRate().doubleValue() : null);
            sbTargetReport.setSalesNewToBrand14d(target.getNewToBrandSalesClicks() != null ?  target.getNewToBrandSalesClicks() : null);
            sbTargetReport.setSalesNewToBrandPercentage14d(target.getNewToBrandSalesPercentage() != null ? target.getNewToBrandSalesPercentage().doubleValue() : null);
            sbTargetReport.setUnitsOrderedNewToBrand14d(target.getNewToBrandUnitsSoldClicks());
            sbTargetReport.setUnitsOrderedNewToBrandPercentage14d(target.getNewToBrandUnitsSoldPercentage() != null ? target.getNewToBrandUnitsSoldPercentage().doubleValue() : null);
            sbTargetReport.setUnitsSold14d(target.getUnitsSold());
//            sbTargetReport.setDpv14d(target.getDpv14d());
//            //商品投放新增sbv类型报告
//            sbTargetReport.setVctr(target.getVctr());
            sbTargetReport.setVideo5SecondViewRate(target.getVideo5SecondViewRate() == null ? null : target.getVideo5SecondViewRate().doubleValue());
            sbTargetReport.setVideo5SecondViews(target.getVideo5SecondViews());
            sbTargetReport.setVideoCompleteViews(target.getVideoCompleteViews());
            sbTargetReport.setVideoFirstQuartileViews(target.getVideoFirstQuartileViews());
            sbTargetReport.setVideoMidpointViews(target.getVideoMidpointViews());
            sbTargetReport.setVideoThirdQuartileViews(target.getVideoThirdQuartileViews());
            sbTargetReport.setVideoUnmutes(target.getVideoUnmutes());
            sbTargetReport.setViewableImpressions(target.getViewableImpressions());
//            sbTargetReport.setVtr(target.getVtr());
            sbTargetReport.setAdFormat(adFormat);
            sbTargetReport.setTopOfSearchIs(Optional.ofNullable(target.getTopOfSearchImpressionShare()).orElse(null));
            list.add(sbTargetReport);
        }
        String cacheKey = String.format(RedisConstant.SELLFOX_AD_SB_DAILY_TARGET_REPORT, notification.getSellerIdentifier(),
                notification.getMarketplaceIdentifier(), notification.getV3Type().name(), notification.getV3StartDate().format(DateTimeFormatter.ofPattern("yyyyMMdd")));
        if (needRetry) {
            Long value = redisService.incr(cacheKey, 1L);
            if (value <= 1) {
                redisService.expire(cacheKey, 3600);
                try {
                    reportReadyProducer.newMessage().value(notification)
                            .deliverAfter(30, TimeUnit.MINUTES).send();
                } catch (PulsarClientException e) {
                    log.error("Pulsar send message with an error.", e);
                }
            }
        }
        return list;
    }

}
