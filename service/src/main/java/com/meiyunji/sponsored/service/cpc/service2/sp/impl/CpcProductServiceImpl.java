package com.meiyunji.sponsored.service.cpc.service2.sp.impl;

import com.alibaba.fastjson.JSON;
import com.amazon.advertising.spV3.enumeration.SpV3StateEnum;
import com.amazon.advertising.spV3.product.CreateSpProductV3Response;
import com.amazon.advertising.spV3.product.ProductSpV3Client;
import com.amazon.advertising.spV3.product.entity.ProductApiResponseV3;
import com.amazon.advertising.spV3.product.entity.ProductEntityV3;
import com.amazon.advertising.spV3.product.entity.ProductSuccessResultV3;
import com.amazon.advertising.spV3.response.ErrorItemResultV3;
import com.google.common.collect.Lists;
import com.google.protobuf.Int32Value;
import com.google.protobuf.Int64Value;
import com.meiyunji.amazon.mws.base.AmznEndpoint;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.config.DynamicRefreshNacosConfiguration;
import com.meiyunji.sponsored.common.enums.OrderTypeEnum;
import com.meiyunji.sponsored.common.exception.BizServiceException;
import com.meiyunji.sponsored.common.util.*;
import com.meiyunji.sponsored.rpc.adCommon.*;
import com.meiyunji.sponsored.rpc.sp.campaign.ProductInfoResp;
import com.meiyunji.sponsored.rpc.vo.AdTagVo;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.account.service.IShopAuthService;
import com.meiyunji.sponsored.service.cpc.dao.*;
import com.meiyunji.sponsored.service.cpc.dto.*;
import com.meiyunji.sponsored.service.cpc.manager.CpcSpTargetingManager;
import com.meiyunji.sponsored.service.cpc.manager.DataDomainManager;
import com.meiyunji.sponsored.service.cpc.po.*;
import com.meiyunji.sponsored.service.cpc.service.IAmazonAdProductMetadataService;
import com.meiyunji.sponsored.service.cpc.service.impl.AdChartDataProcess;
import com.meiyunji.sponsored.service.cpc.service2.handlers.CpcPageIdsHandler;
import com.meiyunji.sponsored.service.cpc.service2.impl.CpcShopDataService;
import com.meiyunji.sponsored.service.cpc.service2.sp.ICpcProductService;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.util.CpcStatusEnum;
import com.meiyunji.sponsored.service.cpc.util.SqlStringReportUtil;
import com.meiyunji.sponsored.service.cpc.vo.*;
import com.meiyunji.sponsored.service.cpc.vo.chartVo.AdHomePerformancedto;
import com.meiyunji.sponsored.service.dataWarehouse.statsDo.ShopSaleDto;
import com.meiyunji.sponsored.service.doris.dao.IOdsAmazonAdProductDao;
import com.meiyunji.sponsored.service.doris.dao.IOdsAmazonSbAdsDao;
import com.meiyunji.sponsored.service.doris.dao.IOdsProductDao;
import com.meiyunji.sponsored.service.doris.po.OdsAmazonAdProduct;
import com.meiyunji.sponsored.service.doris.po.OdsAmazonSbAds;
import com.meiyunji.sponsored.service.doris.po.OdsProduct;
import com.meiyunji.sponsored.service.doris.service.IDorisService;
import com.meiyunji.sponsored.service.enums.*;
import com.meiyunji.sponsored.service.log.enums.OperationLogResultEnum;
import com.meiyunji.sponsored.service.log.po.AdManageOperationLog;
import com.meiyunji.sponsored.service.log.service.IAdManageOperationLogService;
import com.meiyunji.sponsored.service.sellfoxApi.IProductApi;
import com.meiyunji.sponsored.service.util.AmazonErrorUtils;
import com.meiyunji.sponsored.service.vo.ProductAdReportVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.meiyunji.sponsored.service.cpc.util.Constants.EXPORT_MAX_SIZE;

/**
 * Created by xp on 2021/3/30.
 * 广告产品-实现类
 */
@Service
@Slf4j
public class CpcProductServiceImpl implements ICpcProductService {
    @Value("${blacklist.shopIds}")
    private String blackShopIds;

    @Autowired
    private IAmazonAdProductDao amazonAdProductDao;
    @Autowired
    private IAmazonSdAdProductDao sdAdProductDao;
    @Autowired
    private IAmazonAdGroupDao amazonAdGroupDao;
    @Autowired
    private IScVcShopAuthDao shopAuthDao;
    @Autowired
    private IShopAuthService shopAuthService;
    @Autowired
    private IProductApi productDao;
    @Autowired
    private IAmazonAdProductReportDao amazonAdProductReportDao;
    @Autowired
    private IAmazonAdSdProductReportDao amazonAdSdProductReportDao;
    @Autowired
    private CpcCommService cpcCommService;
    @Autowired
    private CpcShopDataService CpCShopDataService;
    @Autowired
    private CpcAdProductApiService cpcAdProductApiService;

    @Autowired
    private AdChartDataProcess adChartDataProcess;
    @Autowired
    private IAmazonAdCampaignDao amazonAdCampaignDao;
    @Autowired
    private IAmazonSdAdCampaignDao amazonSdAdCampaignDao;
    @Autowired
    private IAmazonSdAdGroupDao amazonSdAdGroupDao;
    @Autowired
    private IAmazonAdPortfolioDao portfolioDao;
    @Autowired
    private IAmazonAdProductMetadataService amazonAdProductMetadataService;
    @Autowired
    private IAdManageOperationLogService manageOperationLogService;
    @Autowired
    private IAdMarkupTagDao adMarkupTagDao;
    @Autowired
    private IAdTagDao adTagDao;
    @Resource
    private CpcPageIdsHandler cpcPageIdsHandler;

    @Autowired
    private IDorisService dorisService;

    @Resource
    private IAmazonSpAdProductDorisDao amazonSpAdProductDorisDao;
    @Autowired
    private DynamicRefreshNacosConfiguration dynamicRefreshNacosConfiguration;
    @Autowired
    private DataDomainManager dataDomainManager;
    @Autowired
    private IOdsAmazonAdProductDao spProductDorisDao;
    @Autowired
    private IOdsAmazonSbAdsDao sbAdsDorisDao;
    @Autowired
    private IOdsProductDao odsProductDao;
    @Autowired
    CpcSpTargetingManager spTargetingManager;

    @Override
    public Result<List<AdProductPageVo>> showAdd(Integer puid, Integer shopId, Long dxmGroupId) {
        AmazonAdGroup amazonAdGroup = amazonAdGroupDao.getByPuidAndId(puid, dxmGroupId);
        if (amazonAdGroup == null) {
            return ResultUtil.returnErr("对象不存在");
        }

        List<AdProductPageVo> voList = null;
        List<AmazonAdProduct> amazonAdProducts = amazonAdProductDao.listValidByGroupId(puid, shopId, amazonAdGroup.getAdGroupId());
        if (CollectionUtils.isNotEmpty(amazonAdProducts)) {
            voList = new ArrayList<>(amazonAdProducts.size());
            AdProductPageVo vo;
            for (AmazonAdProduct amazonAdProduct : amazonAdProducts) {
                vo = new AdProductPageVo();
                voList.add(vo);
                vo.setId(amazonAdProduct.getId());
                vo.setAsin(amazonAdProduct.getAsin());
                vo.setSku(amazonAdProduct.getSku());
                vo.setImgUrl("");
            }
        }

        return ResultUtil.returnSucc(voList);
    }

    @Override
    public Result addProduct(AddAdProductVo addAdProductVo, String loginIp) {
        int puid = addAdProductVo.getPuid();
        Integer shopId = addAdProductVo.getShopId();
        String adGroupId = addAdProductVo.getGroupId();
        List<ProductVo> products = addAdProductVo.getProducts();

        AmazonAdGroup amazonAdGroup = amazonAdGroupDao.getByAdGroupId(puid, shopId, adGroupId);
        if (amazonAdGroup == null) {
            buildProductAddFailInfo(addAdProductVo, "广告组不存在", null);
            return ResultUtil.error("对象不存在");
        }
        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(amazonAdGroup.getShopId(), puid);
        if (shop == null) {
            buildProductAddFailInfo(addAdProductVo, "店铺不存在", amazonAdGroup.getName());
            return ResultUtil.returnErr("店铺不存在");
        }
        boolean isVc = ShopTypeEnum.VC.getCode().equals(shop.getType());
        if (isVc) {
            List<String> asinList = products.stream().map(ProductVo::getAsin).collect(Collectors.toList());
            List<String> onlineAsins = amazonAdProductDao.checkRepeatedAsins(puid, amazonAdGroup.getShopId(), amazonAdGroup.getCampaignId(), amazonAdGroup.getAdGroupId(), asinList);
            if (onlineAsins != null && onlineAsins.size() > 0) {
                Set<String> sku = new HashSet<>(asinList);
                onlineAsins = onlineAsins.stream().filter(sku::contains).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(onlineAsins)) {
                    buildProductAddFailInfoForAsin(addAdProductVo, onlineAsins, "目标广告组 " + amazonAdGroup.getName() + " 中已有该ASIN，无法添加", amazonAdGroup.getName());
                    return ResultUtil.error("广告产品已存在" + JSONUtil.objectToJson(onlineAsins));
                }
            }
        } else {
            List<String> skuList = products.stream().map(ProductVo::getSku).collect(Collectors.toList());
            List<String> onlineSkus = amazonAdProductDao.checkRepeatedSkus(puid, amazonAdGroup.getShopId(), amazonAdGroup.getCampaignId(), amazonAdGroup.getAdGroupId(), skuList);
            if (onlineSkus != null && onlineSkus.size() > 0) {
                Set<String> sku = new HashSet<>(skuList);
                onlineSkus = onlineSkus.stream().filter(sku::contains).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(onlineSkus)) {
                    buildProductAddFailInfo(addAdProductVo, onlineSkus, "目标广告组 " + amazonAdGroup.getName() + " 中已有该MSKU，无法添加", amazonAdGroup.getName(), true);
                    return ResultUtil.error("广告产品已存在" + JSONUtil.objectToJson(onlineSkus));
                }
            }
        }



        // vo->po
        List<AmazonAdProduct> amazonAdProducts = new ArrayList<>(products.size());
        products.forEach(e -> {
            AmazonAdProduct amazonAdProduct = new AmazonAdProduct();
//            amazonAdProduct.setUniqueKey("");
            amazonAdProduct.setPuid(puid);
            amazonAdProduct.setShopId(amazonAdGroup.getShopId());
            amazonAdProduct.setMarketplaceId(amazonAdGroup.getMarketplaceId());
            amazonAdProduct.setAdGroupId(amazonAdGroup.getAdGroupId());
            amazonAdProduct.setDxmGroupId(amazonAdGroup.getId());
            amazonAdProduct.setCampaignId(amazonAdGroup.getCampaignId());
            amazonAdProduct.setProfileId(amazonAdGroup.getProfileId());
            amazonAdProduct.setSku(e.getSku());
            amazonAdProduct.setAsin(e.getAsin());
            amazonAdProduct.setState(Constants.ENABLED);
            amazonAdProduct.setCreateId(addAdProductVo.getUid());
            amazonAdProducts.add(amazonAdProduct);
        });

        // po->接口数据
        List<ProductEntityV3> adProducts = Lists.newArrayList();
        ProductEntityV3 productAd;
        for (AmazonAdProduct amazonAdProduct : amazonAdProducts) {
            productAd = new ProductEntityV3();
            productAd.setCampaignId(amazonAdProduct.getCampaignId());
            productAd.setAdGroupId(amazonAdProduct.getAdGroupId());
            //seller只能用sku，vendor才可以用asin
            if (isVc) {
                productAd.setAsin(amazonAdProduct.getAsin());
            } else {
                productAd.setSku(amazonAdProduct.getSku());
            }
            productAd.setState(SpV3StateEnum.ENABLED.valueV3());
            adProducts.add(productAd);
        }


        CreateSpProductV3Response response = ProductSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).createProductAd(shopAuthService.getAdToken(shop), amazonAdGroup.getProfileId(),
                amazonAdGroup.getMarketplaceId(), adProducts, true);
        // token过期再重试一次
        if (response != null && response.getStatusCode() != null && response.getStatusCode() == 401) {
            //刷新token
            shopAuthService.refreshCpcAuth(shop);
            response = ProductSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).createProductAd(shopAuthService.getAdToken(shop), amazonAdGroup.getProfileId(),
                    amazonAdGroup.getMarketplaceId(), adProducts, true);
        }
        if (response == null) {
            buildProductAddFailInfo(addAdProductVo, "网络延迟，请稍后重试", amazonAdGroup.getName());
            return ResultUtil.returnErr("网络延迟，请稍后重试");
        }

        //处理返回结果中的错误信息
        String errMsg = "创建广告产品失败";
        List<AdManageOperationLog> succLog = new ArrayList<>(amazonAdProducts.size());
        if (response.getData() != null && response.getData().getProductAds() != null) {
            List<AmazonAdProduct> succList = new ArrayList<>(amazonAdProducts.size());
            StringBuilder error = new StringBuilder();
            StringBuilder partError = new StringBuilder();
            List<ProductSuccessResultV3> success = response.getData().getProductAds().getSuccess();
            List<ErrorItemResultV3> errorItemResultV3s = response.getData().getProductAds().getError();

            for (ProductSuccessResultV3 productAdResult : success) {
                amazonAdProducts.get(productAdResult.getIndex()).setAdId(productAdResult.getAdId());
                succList.add(amazonAdProducts.get(productAdResult.getIndex()));
            }
            for (ErrorItemResultV3 productAdResult : errorItemResultV3s) {
                if (shop.getType().equals(ShopTypeEnum.VC.getCode())) {
                    error.append("asin:").append(amazonAdProducts.get(productAdResult.getIndex()).getAsin()).append(",desc:").append(AmazonErrorUtils.getError(productAdResult.getErrors().get(0).getErrorMessage())).append(";");
                    partError.append(amazonAdProducts.get(productAdResult.getIndex()).getAsin()).append(",");
                    fillDisplayErrorMsg(addAdProductVo, amazonAdProducts.get(productAdResult.getIndex()), AmazonErrorUtils.getError(productAdResult.getErrors().get(0).getErrorMessage()), amazonAdGroup.getName());
                } else {
                    error.append("sku:").append(amazonAdProducts.get(productAdResult.getIndex()).getSku()).append(",desc:").append(AmazonErrorUtils.getError(productAdResult.getErrors().get(0).getErrorMessage())).append(";");
                    partError.append(amazonAdProducts.get(productAdResult.getIndex()).getSku()).append(",");
                    fillDisplayErrorMsg(addAdProductVo, amazonAdProducts.get(productAdResult.getIndex()), AmazonErrorUtils.getError(productAdResult.getErrors().get(0).getErrorMessage()), amazonAdGroup.getName());
                }
                amazonAdProducts.get(productAdResult.getIndex()).setFailReason(productAdResult.getErrors().get(0).getErrorMessage());
                buildProductAddFailInfo(addAdProductVo, Lists.newArrayList(amazonAdProducts.get(productAdResult.getIndex()).getSku()), AmazonErrorUtils.getError(productAdResult.getErrors().get(0).getErrorMessage()), amazonAdGroup.getName(), false);
            }

            if (succList.size() > 0) {
                amazonAdProductDao.insertOnDuplicateKeyUpdate(puid, succList);
                //写入doris
                saveDoris(succList, true, true);
            }

            succLog = succList.stream().map(e -> {
                AdManageOperationLog productLog = manageOperationLogService.getProductLog(null, e);
                productLog.setIp(loginIp);
                productLog.setResult(0);
                return productLog;
            }).collect(Collectors.toList());
            if (error.length() == 0) {
                manageOperationLogService.batchLogsMergeByAdGroup(succLog);
                return ResultUtil.success();
            }
            errMsg = error.toString();
            //移除成功剩下全部是失败的数据；
            amazonAdProducts.removeAll(succList);
            if (succList.size() > 0 && succList.size() < products.size()) {
                collectFailedLog(amazonAdProducts, errMsg, loginIp);
                manageOperationLogService.batchLogsMergeByAdGroup(succLog);
                Result<String> result = ResultUtil.success();
                result.setData(partError.substring(0, partError.length() - 1));
                return result;
            }
        } else if (response.getError() != null) {
            if (StringUtils.isNotBlank(response.getError().getMessage())) {
                errMsg = AmazonErrorUtils.getError(response.getError().getMessage());
            } else if (CollectionUtils.isNotEmpty(response.getError().getErrors())) {
                errMsg = AmazonErrorUtils.getError(response.getError().getErrors().get(0).getErrorMessage());
            }

        }

        String finalErrMsg = errMsg;
        collectFailedLog(amazonAdProducts, finalErrMsg, loginIp);
        manageOperationLogService.batchLogsMergeByAdGroup(succLog);
        buildProductAddFailInfo(addAdProductVo, errMsg, amazonAdGroup.getName(), false);
        return ResultUtil.error(errMsg);
    }

    private void fillDisplayErrorMsg(AddAdProductVo addAdProductVo, AmazonAdProduct amazonAdProduct, String error, String adGroupName) {
        fillDisplayErrorMsg(addAdProductVo, amazonAdProduct.getAsin(), amazonAdProduct.getSku(), error, adGroupName);
    }

    private void fillDisplayErrorMsg(AddAdProductVo addAdProductVo, String asin, String sku, String error, String adGroupName) {
        List<AddAdProductVo.DisplayErrorMsg> displayErrorMsgList = addAdProductVo.getDisplayErrorMsgList();
        if (displayErrorMsgList == null) {
            return;
        }
        AddAdProductVo.DisplayErrorMsg displayErrorMsg = new AddAdProductVo.DisplayErrorMsg();
        displayErrorMsg.setAsin(asin);
        displayErrorMsg.setMsku(sku);
        displayErrorMsg.setAdGroupName(adGroupName);
        displayErrorMsg.setError(error);
        displayErrorMsgList.add(displayErrorMsg);
    }

    @Override
    public Result<List<ProductInfoResp>> addProductToGroup(ShopAuth shop, String campaignId, String adGroupId, List<CpcProductDto> productDtoList, String loginIp, Integer uid, AmazonAdProfile targetAmazonAdProfile) {
        if (CollectionUtils.isEmpty(productDtoList)) {
            return ResultUtil.success(Collections.emptyList());
        }
        Integer puid = shop.getPuid();
        Integer shopId = shop.getId();
        //1，校验广告组
        AmazonAdGroup amazonAdGroup = amazonAdGroupDao.getByAdGroupId(puid, shopId, adGroupId);
        if (amazonAdGroup == null) {
            return ResultUtil.returnErr("广告组不存在");
        }
        if (!amazonAdGroup.getShopId().equals(shopId) || !amazonAdGroup.getCampaignId().equals(campaignId)) {
            String err = String.format("广告组不存在于此店铺广告活动下，puid：%d, shopId:%d, campaignId:%s, adGroupId:%s", puid, shopId, campaignId, adGroupId);
            return ResultUtil.returnErr(err);
        }

        //2,vo -> po
        List<AmazonAdProduct> amazonAdProducts = convertAddProductsVoToPo(uid, amazonAdGroup, productDtoList);

        //3，调用亚马逊接口
        Result<ProductApiResponseV3> productAdsV3 = spTargetingManager.createProductAdsV3(amazonAdProducts, shop, targetAmazonAdProfile);
        if (!productAdsV3.success()) {
            return ResultUtil.returnErr(productAdsV3.getMsg());
        }

        //4，处理结果
        List<ProductInfoResp> productInfoRespList = new ArrayList<>();
        for (ProductSuccessResultV3 productSuccessResultV3 : productAdsV3.getData().getProductAds().getSuccess()) {
            ProductInfoResp.Builder productSuccessResp = ProductInfoResp.newBuilder();
            productSuccessResp.setCode(Result.SUCCESS);
            productSuccessResp.setIndex(productSuccessResultV3.getIndex());
            productSuccessResp.setProductId(productSuccessResultV3.getAdId());
            productInfoRespList.add(productSuccessResp.build());
            //后续操作根据 adId是否为null 判断是否创建投放成功
            amazonAdProducts.get(productSuccessResultV3.getIndex()).setAdId(productSuccessResultV3.getAdId());
        }
        for (ErrorItemResultV3 errorItemResultV3 : productAdsV3.getData().getProductAds().getError()) {
            ProductInfoResp.Builder productErrResp = ProductInfoResp.newBuilder();
            productErrResp.setCode(Result.ERROR);
            productErrResp.setIndex(errorItemResultV3.getIndex());
            productErrResp.setErrMsg(errorItemResultV3.getErrors().get(0).getErrorMessage());
            if ("duplicateValueError".equalsIgnoreCase(errorItemResultV3.getErrors().get(0).getErrorType())) {
                productErrResp.setErrMsg("此产品已存在");
            }
            productInfoRespList.add(productErrResp.build());
        }
        //排序
        productInfoRespList.sort(Comparator.comparingInt(ProductInfoResp::getIndex));

        //5，记录日志
        List<AdManageOperationLog> productLogs = Lists.newArrayListWithExpectedSize(amazonAdProducts.size());
        for (int i = 0; i < amazonAdProducts.size(); i++) {
            AmazonAdProduct product = amazonAdProducts.get(i);
            AdManageOperationLog productLog = manageOperationLogService.getProductLog(null, product);
            productLog.setIp(loginIp);
            if (StringUtils.isNotBlank(product.getAdId())) {
                productLog.setResult(OperationLogResultEnum.SUCCESS.getResultValue());
            } else {
                productLog.setResult(OperationLogResultEnum.FAIL.getResultValue());
                productLog.setResultInfo(productInfoRespList.get(i).getErrMsg());
            }
            productLogs.add(productLog);
        }

        //批量操作(先根据result成功/失败分组,再根据广告活动分组，最后根据广告组分组合并一条日志)
        CompletableFuture.runAsync(() -> manageOperationLogService.batchLogsMergeByAdGroup(productLogs), ThreadPoolUtil.getCreateAdForEsExecutor()).exceptionally(e -> {
            log.error("添加商品, add log to es error:", e);
            return null;
        });

        //将创建成功的产品保存进mysql和doris
        if (productAdsV3.success()) {
            amazonAdProducts = amazonAdProducts.stream().filter(e -> StringUtils.isNotBlank(e.getAdId())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(amazonAdProducts)) {
                return ResultUtil.success(productInfoRespList);
            }
            try {
                //写入mysql
                amazonAdProductDao.insertOnDuplicateKeyUpdate(puid, amazonAdProducts);
                //写入doris
                saveDoris(amazonAdProducts, true, true);
            } catch (Exception e) {
                log.error("保存新增商品失败，amazonAdProducts：{}, group:{}", JSON.toJSONString(amazonAdProducts), JSON.toJSONString(amazonAdGroup), e);
            }
        }
        return ResultUtil.success(productInfoRespList);
    }

    private List<AmazonAdProduct> convertAddProductsVoToPo(Integer uid, AmazonAdGroup amazonAdGroup, List<CpcProductDto> products) {
        List<AmazonAdProduct> amazonAdProducts = new ArrayList<>(products.size());
        products.forEach(e -> {
            AmazonAdProduct amazonAdProduct = new AmazonAdProduct();
            amazonAdProduct.setPuid(amazonAdGroup.getPuid());
            amazonAdProduct.setShopId(amazonAdGroup.getShopId());
            amazonAdProduct.setMarketplaceId(amazonAdGroup.getMarketplaceId());
            amazonAdProduct.setAdGroupId(amazonAdGroup.getAdGroupId());
            amazonAdProduct.setDxmGroupId(amazonAdGroup.getId());
            amazonAdProduct.setCampaignId(amazonAdGroup.getCampaignId());
            amazonAdProduct.setProfileId(amazonAdGroup.getProfileId());
            amazonAdProduct.setSku(e.getSku());
            amazonAdProduct.setAsin(e.getAsin());
            amazonAdProduct.setState(Constants.ENABLED);
            amazonAdProduct.setCreateId(uid);
            amazonAdProducts.add(amazonAdProduct);
        });
        return amazonAdProducts;
    }

    private void buildProductAddFailInfo(AddAdProductVo addAdProductVo, String errorMsg, String adGroupName) {
        buildProductAddFailInfo(addAdProductVo, errorMsg, adGroupName, true);
    }

    private void buildProductAddFailInfo(AddAdProductVo addAdProductVo, String errorMsg, String adGroupName, boolean fillDisplayErrorMsg) {
        try {
            if (Objects.isNull(addAdProductVo.getFailInfoList()) && addAdProductVo.getDisplayErrorMsgList() == null) {
                return;
            }
            addAdProductVo.getProducts().forEach(e -> {
                ProductAddResponse.AddFailInfo failInfo = ProductAddResponse.AddFailInfo.newBuilder()
                    .setAsin(e.getAsin())
                    .setMsku(e.getSku())
                        .setMsg(errorMsg)
                        .build();
                if (!Objects.isNull(addAdProductVo.getFailInfoList())) {
                    addAdProductVo.getFailInfoList().add(failInfo);
                }
                if (fillDisplayErrorMsg) {
                    fillDisplayErrorMsg(addAdProductVo, e.getAsin(), e.getSku(), errorMsg, adGroupName);
                }
            });
        } catch (Exception e) {
            log.error("sp添加广告产品，失败信息记录 addAdProductVo {}, {}", addAdProductVo, errorMsg, e);
        }
    }

    private void buildProductAddFailInfo(AddAdProductVo addAdProductVo, List<String> skus, String errorMsg, String adGroupName, boolean fillDisplayErrorMsg) {
        try {
            if (Objects.isNull(addAdProductVo.getFailInfoList()) && addAdProductVo.getDisplayErrorMsgList() == null) {
                return;
            }
            // 需要记录失败信息的场景，只会传一个asin
            String asin = addAdProductVo.getProducts().get(0).getAsin();
            Map<String, String> sku2asin = StreamUtil.toMap(addAdProductVo.getProducts(), ProductVo::getSku, ProductVo::getAsin);
            skus.forEach(sku -> {
                ProductAddResponse.AddFailInfo failInfo = ProductAddResponse.AddFailInfo.newBuilder()
                        .setAsin(asin)
                        .setMsku(sku)
                        .setMsg(errorMsg)
                        .build();
                if (!Objects.isNull(addAdProductVo.getFailInfoList())) {
                    addAdProductVo.getFailInfoList().add(failInfo);
                }
                if (fillDisplayErrorMsg) {
                    fillDisplayErrorMsg(addAdProductVo, sku2asin.get(sku), sku, errorMsg, adGroupName);
                }
            });
        } catch (Exception e) {
            log.error("sp添加广告产品，失败信息记录 addAdProductVo {}, skus={}, {}", addAdProductVo, skus, errorMsg, e);
        }
    }

    private void buildProductAddFailInfoForAsin(AddAdProductVo addAdProductVo, List<String> asins, String errorMsg, String adGroupName) {
        try {
            if (Objects.isNull(addAdProductVo.getFailInfoList()) && addAdProductVo.getDisplayErrorMsgList() == null) {
                return;
            }
            asins.forEach(asin -> {
                ProductAddResponse.AddFailInfo failInfo = ProductAddResponse.AddFailInfo.newBuilder()
                        .setAsin(asin)
                        .setMsg(errorMsg)
                        .build();
                if (!Objects.isNull(addAdProductVo.getFailInfoList())) {
                    addAdProductVo.getFailInfoList().add(failInfo);
                }
                fillDisplayErrorMsg(addAdProductVo, asin, null, errorMsg, adGroupName);
            });
        } catch (Exception e) {
            log.error("sp添加广告产品，失败信息记录 addAdProductVo {}, asins={}, {}", addAdProductVo, asins, errorMsg, e);
        }
    }

    private void collectFailedLog(List<AmazonAdProduct> amazonAdProducts, String finalErrMsg, String loginIp) {
        List<AdManageOperationLog> failCollect = amazonAdProducts.stream().map(e -> {
            AdManageOperationLog productLog = manageOperationLogService.getProductLog(null, e);
            productLog.setIp(loginIp);
            productLog.setResult(1);
            productLog.setResultInfo(finalErrMsg);
            return productLog;
        }).collect(Collectors.toList());
        manageOperationLogService.batchLogsMergeByAdGroup(failCollect);
    }

    @Override
    public Result updateState(Integer puid, Integer uid, Long id, String state, String loginIp) {
        AmazonAdProduct amazonAdProduct = amazonAdProductDao.getByPuidAndId(puid, id);
        if (amazonAdProduct == null) {
            return ResultUtil.error("对象不存在");
        }
        AmazonAdProduct oldAmazonAdProduct = new AmazonAdProduct();
        BeanUtils.copyProperties(amazonAdProduct, oldAmazonAdProduct);

        String oldState = amazonAdProduct.getState();
        if (oldState.equals(state)) {
            return ResultUtil.success();
        }

        amazonAdProduct.setState(state);
        Result<List<AmazonAdProduct>> result = cpcAdProductApiService.update(Lists.newArrayList(amazonAdProduct));
        amazonAdProduct.setUpdateId(uid);
        AdManageOperationLog productLog = manageOperationLogService.getProductLog(oldAmazonAdProduct, amazonAdProduct);
        productLog.setIp(loginIp);
        if (result.success()) {

            List<AmazonAdProduct> succList = result.getData();
            for (AmazonAdProduct amazonAdProduct1 : succList) {
                productLog.setResult(0);
                amazonAdProductDao.updateById(puid, amazonAdProduct1);
                saveDoris(Collections.singletonList(amazonAdProduct1), false, true);
            }


            // 可能部分成功
            if (StringUtils.isNotBlank(result.getMsg())) {
                productLog.setResult(0);
                productLog.setResultInfo(result.getMsg());
                manageOperationLogService.printAdOperationLog(Lists.newArrayList(productLog));
                return ResultUtil.returnErr(result.getMsg());
            }

        } else {
            productLog.setResult(1);
            productLog.setResultInfo(result.getMsg());
        }
        manageOperationLogService.printAdOperationLog(Lists.newArrayList(productLog));
        return result;
    }

    @Override
    public Result archive(Integer puid, Integer uid, Long id, String loginIp) {
        AmazonAdProduct amazonAdProduct = amazonAdProductDao.getByPuidAndId(puid, id);
        if (amazonAdProduct == null) {
            return ResultUtil.error("对象不存在");
        }
        AmazonAdProduct oldAmazonAdProduct = new AmazonAdProduct();
        BeanUtils.copyProperties(amazonAdProduct, oldAmazonAdProduct);
        amazonAdProduct.setUpdateId(uid);
        amazonAdProduct.setState(CpcStatusEnum.archived.name());
        Result result = cpcAdProductApiService.archive(amazonAdProduct);
        AdManageOperationLog productLog = manageOperationLogService.getProductLog(oldAmazonAdProduct, amazonAdProduct);
        productLog.setIp(loginIp);
        if (result.success()) {
            productLog.setResult(0);
            amazonAdProduct.setUpdateId(uid);
            amazonAdProduct.setState(CpcStatusEnum.archived.name());
            amazonAdProductDao.updateById(puid, amazonAdProduct);
            saveDoris(Collections.singletonList(amazonAdProduct), false, true);
        } else {
            productLog.setResult(1);
            productLog.setResultInfo(result.getMsg());
        }
        manageOperationLogService.printAdOperationLog(Lists.newArrayList(productLog));
        return result;
    }

    @Override
    public Result showAdPerformance(int puid, AdPerformanceParam param) {
        if (param.getShopId() == null
                || StringUtils.isBlank(param.getCpcProductId())) {
            return ResultUtil.returnErr("请求参数错误");
        }

        AmazonAdProduct amazonAdProduct = amazonAdProductDao.getByAdId(puid, param.getShopId(), param.getCpcProductId());
        if (amazonAdProduct == null) {
            return ResultUtil.returnErr("没有广告产品信息");
        }

        // 拼装返回的数据VO
        AdPerformanceVo adPerformanceVo = new AdPerformanceVo();
        adPerformanceVo.setShopId(amazonAdProduct.getShopId());
        adPerformanceVo.setAdId(amazonAdProduct.getAdId());

        // 初始化每天数据，应前端要求保证日期是连续的
        Map<String, CpcCommPageVo> map = new LinkedHashMap<>();
        adPerformanceVo.setMap(map);
        LocalDate startLocalDate = LocalDate.parse(param.getStartDate(), DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD));
        LocalDate endLocalDate = LocalDate.parse(param.getEndDate(), DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD));
        while (startLocalDate.equals(endLocalDate) || startLocalDate.isBefore(endLocalDate)) {
            map.put(startLocalDate.format(DateTimeFormatter.ofPattern(DateUtil.PATTERN)), new CpcCommPageVo());
            startLocalDate = startLocalDate.plusDays(1);
        }

        List<AmazonAdProductReport> reports = amazonAdProductReportDao.listReports(puid, param.getShopId(), param.getStartDate(), param.getEndDate(), param.getCpcProductId());

        if (CollectionUtils.isNotEmpty(reports)) {
            // 取店铺销售额
            ShopSaleDto shopSaleDto = CpCShopDataService.getShopSaleData(param.getShopId(), param.getStartDate(), param.getEndDate());

            ShopSaleDto shopSaleDto1 = shopSaleDto;

            Map<String, CpcCommPageVo> resultMap = reports.stream().collect(Collectors.toMap(
                    e -> LocalDate.parse(e.getCountDate(), DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD)).format(DateTimeFormatter.ofPattern(DateUtil.PATTERN)),
                    e -> {
                        CpcCommPageVo campaignPageVo = new CpcCommPageVo();

                        // 填充报告数据
                        cpcCommService.fillReportDataIntoPageVo(campaignPageVo, e, shopSaleDto1);
                        return campaignPageVo;

                    }, (p1, p2) -> p1));

            adPerformanceVo.getMap().putAll(resultMap);
        }

        return ResultUtil.returnSucc(adPerformanceVo);
    }

    /**
     * 广告产品(sb sd)
     *
     * @param puid
     * @param param
     * @return
     */
    @Override
    public AllProductDataResponse.AdProductHomeVo getAllProductData(int puid, AdProductPageParam param) {
        Page<AdProductPageVo> voPage = new Page<>();
        voPage.setPageSize(param.getPageSize());
        voPage.setPageNo(param.getPageNo());
        //查询所有数据(sb sd)

        ShopAuth shopAuth = shopAuthDao.getScAndVcById(param.getShopId());
        if (shopAuth == null) {
            AssertUtil.fail("店铺未授权");
        }

        // 取店铺销售额
        BigDecimal shopSalesByDate = CpCShopDataService.getShopSalesByDate(shopAuth.getId(), param.getStartDate(), param.getEndDate());
        if (shopSalesByDate == null) {
            shopSalesByDate = BigDecimal.ZERO;
        }
        param.setShopSales(shopSalesByDate);

        if (StringUtils.isBlank(param.getType())) {
            param.setType("sp");
        }

        //勾选正在投放按钮移除其它
        if (StringUtils.isNotBlank(param.getServingStatus())) {
            List<String> list = StringUtil.splitStr(param.getServingStatus());
            if (list.contains("enabled")) {
                param.setServingStatus("enabled");
            }
        }

        //获取不同类型数据 sp、sd
        if (Constants.SP.equalsIgnoreCase(param.getType())) {
            getSpProductVoList(shopAuth, puid, param, voPage, false);
        } else {
            getSdProductVoList(shopAuth, puid, param, voPage, false);
        }

        //填充标签数据
        long t1 = Instant.now().toEpochMilli();
        fillAdTagData(puid, param.getShopId(), param, voPage.getRows());
        log.info("标签数据填充花费时间 {}", Instant.now().toEpochMilli() - t1);


        //分页后,填充活动,广告组,asin信息
        fillinAdInfoForVo(shopAuth, voPage.getRows(), false);

        //处理分页
        AllProductDataResponse.AdProductHomeVo.Page.Builder pageBuilder = AllProductDataResponse.AdProductHomeVo.Page.newBuilder();
        pageBuilder.setPageNo(Int32Value.of(voPage.getPageNo()));
        pageBuilder.setPageSize(Int32Value.of(voPage.getPageSize()));
        pageBuilder.setTotalPage(Int32Value.of(voPage.getTotalPage()));
        pageBuilder.setTotalSize(Int32Value.of(voPage.getTotalSize()));
        List<AdProductPageVo> rows = voPage.getRows();
        if (CollectionUtils.isNotEmpty(rows)) {
            //对比时无须高级搜索条件
            param.setUseAdvanced(false);
            //环比数据
            BigDecimal shopSalesByDateCompare = BigDecimal.ZERO;
            Map<String, AdProductPageVo> compareProductMap = null;
            if (param.getIsCompare()) {
                // 取店铺销售额
                shopSalesByDateCompare = CpCShopDataService.getShopSalesByDate(shopAuth.getId(),
                        param.getCompareStartDate(), param.getCompareEndDate());
                if (shopSalesByDateCompare == null) {
                    shopSalesByDateCompare = BigDecimal.ZERO;
                }
                param.setShopSales(shopSalesByDateCompare);
                param.setStartDate(param.getCompareStartDate());
                param.setEndDate(param.getCompareEndDate());
                param.setAdIds(rows.stream().map(AdProductPageVo::getAdId).collect(Collectors.toList()));

                List<AdProductPageVo> productPageVoList = new ArrayList<>();
                //获取不同类型数据 sp、sd
                if (Constants.SP.equalsIgnoreCase(param.getType())) {
                    productPageVoList = getSpProductPageVoList(puid, param);
                } else {
                    productPageVoList = getSdProductPageVoList(puid, param);
                }

                //list转map
                compareProductMap = productPageVoList.stream()
                        .collect(Collectors.toMap(AdProductPageVo::getAdId, Function.identity(), (a, b) -> a));

            }

            Map<String, AdProductCommonInfoDto> fbaInventory = getAdProductFbaInfo(rows, puid);
            Map<String, AdProductPageVo> finalCompareProductMap = compareProductMap;
            boolean isVc = ShopTypeEnum.VC.getCode().equals(shopAuth.getType());
            List<AllProductDataResponse.AdProductHomeVo.Page.AdProductPageVo> rpcVos = rows.stream()
                    .filter(Objects::nonNull)
                    .filter(r -> StringUtils.isNotEmpty(r.getAsin()) || StringUtils.isNotEmpty(r.getSku()))
                    .map(item -> {
                        AllProductDataResponse.AdProductHomeVo.Page.AdProductPageVo.Builder voBuilder = AllProductDataResponse.AdProductHomeVo.Page.AdProductPageVo.newBuilder();
                        voBuilder.setType(item.getType());
                        voBuilder.setId(Int64Value.of(item.getId()));
                        voBuilder.setShopId(Int32Value.of(item.getShopId()));
                        if (shopAuth != null) {
                            voBuilder.setMarketplaceId(shopAuth.getMarketplaceId());
                        }
                        if (CollectionUtils.isNotEmpty(item.getAdTags())) {
                            item.getAdTags().forEach(e -> {
                                com.meiyunji.sponsored.rpc.vo.AdTagVo.Builder builder = com.meiyunji.sponsored.rpc.vo.AdTagVo.newBuilder();
                                AdTagVo tagVo = builder.setId(Int64Value.of(e.getId())).setColor(e.getColor()).setName(e.getName()).build();
                                voBuilder.addAdTags(tagVo);
                            });
                        }

                        if (fbaInventory.containsKey(item.getShopId() + item.getSku())) {
                            builderAdProductCommonInfo(fbaInventory.get(item.getShopId() + item.getSku()), voBuilder);
                        }

                        if (item.getDxmAdGroupId() != null) {
                            voBuilder.setDxmAdGroupId(Int64Value.of(item.getDxmAdGroupId()));
                        }
                        if (StringUtils.isNotBlank(item.getCampaignId())) {
                            voBuilder.setCampaignId(item.getCampaignId());
                        }
                        if (StringUtils.isNotBlank(item.getCampaignState())) {
                            voBuilder.setCampaignState(item.getCampaignState());
                        }
                        if (StringUtils.isNotBlank(item.getCampaignName())) {
                            voBuilder.setCampaignName(item.getCampaignName());
                        }
                        if (StringUtils.isNotBlank(item.getCampaignTargetingType())) {
                            voBuilder.setCampaignTargetingType(item.getCampaignTargetingType());
                        }
                        if (StringUtils.isNotBlank(item.getAdGroupId())) {
                            voBuilder.setAdGroupId(item.getAdGroupId());
                        }
                        if (StringUtils.isNotBlank(item.getAdGroupType())) {
                            voBuilder.setAdGroupType(item.getAdGroupType());
                        }
                        if (StringUtils.isNotBlank(item.getAdGroupName())) {
                            voBuilder.setAdGroupName(item.getAdGroupName());
                        }
                        if (StringUtils.isNotBlank(item.getAdGroupState())) {
                            voBuilder.setAdGroupState(item.getAdGroupState());
                        }
                        if (StringUtils.isNotBlank(item.getAdId())) {
                            voBuilder.setAdId(item.getAdId());
                        }
                        if (StringUtils.isNotBlank(item.getState())) {
                            voBuilder.setState(item.getState());
                        }
                        if (StringUtils.isNotBlank(item.getAsin())) {
                            voBuilder.setAsin(item.getAsin());
                        }
                        if (StringUtils.isNotBlank(item.getSku())) {
                            voBuilder.setSku(item.getSku());
                        }
                        if (StringUtils.isNotBlank(item.getTitle())) {
                            voBuilder.setTitle(item.getTitle());
                        }
                        if (StringUtils.isNotBlank(item.getImgUrl())) {
                            if (item.getImgUrl().endsWith("S60_.jpg")) {
                                item.setImgUrl(item.getImgUrl().replace("S60_.jpg", "S600_.jpg"));
                            }
                            voBuilder.setImgUrl(item.getImgUrl());
                        }
                        if (StringUtils.isNotBlank(item.getPrice())) {
                            voBuilder.setPrice(item.getPrice());
                        }
                        if (StringUtils.isNotBlank(item.getDomain())) {
                            voBuilder.setDomain(item.getDomain());
                        }
                        if (StringUtils.isNotBlank(item.getPortfolioId())) {
                            voBuilder.setPortfolioId(item.getPortfolioId());
                        }
                        if (StringUtils.isNotBlank(item.getPortfolioName())) {
                            voBuilder.setPortfolioName(item.getPortfolioName());
                        }
                        if (item.getIsHidden() != null) {
                            voBuilder.setIsHidden(item.getIsHidden());
                        }

                        if (item.getServingStatus() != null) {
                            voBuilder.setServingStatus(item.getServingStatus());
                        }
                        if (item.getServingStatusDec() != null) {
                            voBuilder.setServingStatusDec(item.getServingStatusDec());
                        }
                        if (item.getServingStatusName() != null) {
                            voBuilder.setServingStatusName(item.getServingStatusName());
                        }

                        voBuilder.setImpressions(Int32Value.of(Optional.ofNullable(item.getImpressions()).orElse(0)));
                        voBuilder.setClicks(Int32Value.of(Optional.ofNullable(item.getClicks()).orElse(0)));
                        voBuilder.setAdOrderNum(Int32Value.of(Optional.ofNullable(item.getAdOrderNum()).orElse(0)));
                        voBuilder.setAdCostPerClick(StringUtils.isNotBlank(item.getAdCostPerClick()) ? item.getAdCostPerClick() : "0");
                        voBuilder.setCtr(StringUtils.isNotBlank(item.getCtr()) ? item.getCtr() : "0");
                        voBuilder.setCvr(StringUtils.isNotBlank(item.getCvr()) ? item.getCvr() : "0");
                        voBuilder.setAcos(StringUtils.isNotBlank(item.getAcos()) ? item.getAcos() : "0");
                        voBuilder.setRoas(StringUtils.isNotBlank(item.getRoas()) ? item.getRoas() : "0");
                        voBuilder.setAdCost(StringUtils.isNotBlank(item.getAdCost()) ? item.getAdCost() : "0");
                        voBuilder.setAcots(StringUtils.isNotBlank(item.getAcots()) ? item.getAcots() : "0");
                        voBuilder.setAsots(StringUtils.isNotBlank(item.getAsots()) ? item.getAsots() : "0");
                        voBuilder.setAdSale(StringUtils.isNotBlank(item.getAdSale()) ? item.getAdSale() : "0");
                        /**
                         * TODO 广告报告重构
                         * sd广告vcpm类型报告特殊字段。
                         */
                        //可见展示次数(VCPM专用)
                        voBuilder.setViewImpressions(Int32Value.of(Optional.ofNullable(item.getViewImpressions()).orElse(0)));
                        //每笔订单花费
                        voBuilder.setCpa(StringUtils.isNotBlank(item.getCpa()) ? item.getCpa() : "0");
                        //vcpm
                        if(SBCampaignCostTypeEnum.VCPM.getCode().equals(item.getCostType())){
                            voBuilder.setVcpm(StringUtils.isNotBlank(item.getVcpm()) ? item.getVcpm() : "0");
                        }else{
                            voBuilder.setVcpm("-");
                        }
                        //本广告产品订单量
                        voBuilder.setAdSaleNum(Int32Value.of(Optional.ofNullable(item.getAdSaleNum()).orElse(0)));
                        //其他产品广告订单量
                        voBuilder.setAdOtherOrderNum(Int32Value.of(Optional.ofNullable(item.getAdOtherOrderNum()).orElse(0)));
                        //本广告产品销售额
                        voBuilder.setAdSales(StringUtils.isNotBlank(item.getAdSales()) ? item.getAdSales() : "0");
                        //其他产品广告销售额
                        voBuilder.setAdOtherSales(StringUtils.isNotBlank(item.getAdOtherSales()) ? item.getAdOtherSales() : "0");
                        //广告销量
                        voBuilder.setOrderNum(Int32Value.of(Optional.ofNullable(item.getOrderNum()).orElse(0)));
                        //本广告产品销量
                        voBuilder.setAdSelfSaleNum(Int32Value.of(Optional.ofNullable(item.getAdSelfSaleNum()).orElse(0)));
                        //其他产品广告销量
                        voBuilder.setAdOtherSaleNum(Int32Value.of(Optional.ofNullable(item.getAdOtherSaleNum()).orElse(0)));
                        //“品牌新买家”订单量
                        voBuilder.setOrdersNewToBrandFTD(Int32Value.of(Optional.ofNullable(item.getOrdersNewToBrandFTD()).orElse(0)));
                        //“品牌新买家”订单百分比
                        voBuilder.setOrderRateNewToBrandFTD(StringUtils.isNotBlank(item.getOrderRateNewToBrandFTD()) ? item.getOrderRateNewToBrandFTD() : "0");
                        //“品牌新买家”销售额
                        voBuilder.setSalesNewToBrandFTD(StringUtils.isNotBlank(item.getSalesNewToBrandFTD()) ? item.getSalesNewToBrandFTD() : "0");
                        //“品牌新买家”销售额百分比
                        voBuilder.setSalesRateNewToBrandFTD(StringUtils.isNotBlank(item.getSalesRateNewToBrandFTD()) ? item.getSalesRateNewToBrandFTD() : "0");
                        //“品牌新买家”销量
                        voBuilder.setUnitsOrderedNewToBrandFTD(Int32Value.of(Optional.ofNullable(item.getUnitsOrderedNewToBrandFTD()).orElse(0)));
                        //“品牌新买家”销量百分比
                        voBuilder.setUnitsOrderedRateNewToBrandFTD(StringUtils.isNotBlank(item.getUnitsOrderedRateNewToBrandFTD()) ? item.getUnitsOrderedRateNewToBrandFTD() : "0");
                        // 花费占比
                        voBuilder.setAdCostPercentage(StringUtils.isNotBlank(item.getAdCostPercentage()) ? item.getAdCostPercentage() : "0");
                        // 销售额占比
                        voBuilder.setAdSalePercentage(StringUtils.isNotBlank(item.getAdSalePercentage()) ? item.getAdSalePercentage() : "0");
                        // 订单量占比
                        voBuilder.setAdOrderNumPercentage(StringUtils.isNotBlank(item.getAdOrderNumPercentage()) ? item.getAdOrderNumPercentage() : "0");
                        // 销量占比
                        voBuilder.setOrderNumPercentage(StringUtils.isNotBlank(item.getOrderNumPercentage()) ? item.getOrderNumPercentage() : "0");

                        voBuilder.setNewToBrandDetailPageViews(Optional.ofNullable(item.getNewToBrandDetailPageViews()).map(String::valueOf).orElse("0"));
                        voBuilder.setAddToCart(Optional.ofNullable(item.getAddToCart()).map(String::valueOf).orElse("0"));
                        voBuilder.setAddToCartRate(Optional.ofNullable(item.getAddToCartRate()).map(String::valueOf).orElse("0"));
                        voBuilder.setECPAddToCart(Optional.ofNullable(item.getECPAddToCart()).map(String::valueOf).orElse("0"));
                        voBuilder.setVideoFirstQuartileViews(Optional.ofNullable(item.getVideoFirstQuartileViews()).map(String::valueOf).orElse("0"));
                        voBuilder.setVideoMidpointViews(Optional.ofNullable(item.getVideoMidpointViews()).map(String::valueOf).orElse("0"));
                        voBuilder.setVideoThirdQuartileViews(Optional.ofNullable(item.getVideoThirdQuartileViews()).map(String::valueOf).orElse("0"));
                        voBuilder.setVideoCompleteViews(Optional.ofNullable(item.getVideoCompleteViews()).map(String::valueOf).orElse("0"));
                        voBuilder.setVideoUnmutes(Optional.ofNullable(item.getVideoUnmutes()).map(String::valueOf).orElse("0"));
                        voBuilder.setViewabilityRate(Optional.ofNullable(item.getViewabilityRate()).map(String::valueOf).orElse("0"));
                        voBuilder.setViewClickThroughRate(Optional.ofNullable(item.getViewClickThroughRate()).map(String::valueOf).orElse("0"));
                        voBuilder.setBrandedSearches(Optional.ofNullable(item.getBrandedSearches()).map(String::valueOf).orElse("0"));
                        voBuilder.setDetailPageViews(Optional.ofNullable(item.getDetailPageViews()).map(String::valueOf).orElse("0"));
                        voBuilder.setCumulativeReach(Optional.ofNullable(item.getCumulativeReach()).map(String::valueOf).orElse("0"));
                        voBuilder.setImpressionsFrequencyAverage(Optional.ofNullable(item.getImpressionsFrequencyAverage()).map(String::valueOf).orElse("0"));
                        voBuilder.setAdvertisingUnitPrice(Optional.ofNullable(item.getAdvertisingUnitPrice()).map(String::valueOf).orElse("0"));


                        //环比指标数据
                        if (MapUtils.isNotEmpty(finalCompareProductMap)) {
                            if (finalCompareProductMap.containsKey(item.getAdId())) {
                                AdProductPageVo compareItem = finalCompareProductMap.get(item.getAdId());

                                voBuilder.setCompareViewImpressions(Int32Value.of(Optional.ofNullable(compareItem.getViewImpressions()).orElse(0)));
                                //可见展示环比值
                                int viewImpressionDiff = voBuilder.getViewImpressions().getValue() - voBuilder.getCompareViewImpressions().getValue();
                                voBuilder.setCompareViewImpressionsRate(voBuilder.getCompareViewImpressions().getValue() == 0 ? "-" :
                                        new BigDecimal(viewImpressionDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareViewImpressions().getValue()),
                                                2, RoundingMode.HALF_UP).toString());

                                voBuilder.setCompareImpressions(Int32Value.of(Optional.ofNullable(compareItem.getImpressions()).orElse(0)));
                                //曝光环比值
                                int impressionDiff = voBuilder.getImpressions().getValue() - voBuilder.getCompareImpressions().getValue();
                                voBuilder.setCompareImpressionsRate(voBuilder.getCompareImpressions().getValue() == 0 ? "-" :
                                        new BigDecimal(impressionDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareImpressions().getValue()),
                                                2, RoundingMode.HALF_UP).toString());

                                voBuilder.setCompareClicks(Int32Value.of(Optional.ofNullable(compareItem.getClicks()).orElse(0)));
                                //点击量环比值
                                int clicksDiff = voBuilder.getClicks().getValue() - voBuilder.getCompareClicks().getValue();
                                voBuilder.setCompareClicksRate(voBuilder.getCompareClicks().getValue() == 0 ? "-" :
                                        new BigDecimal(clicksDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareClicks().getValue()),
                                                2, RoundingMode.HALF_UP).toString());

                                voBuilder.setCompareCtr(StringUtils.isNotBlank(compareItem.getCtr()) ? compareItem.getCtr() : "0");
                                //ctr环比值
                                BigDecimal ctrDiff = new BigDecimal(voBuilder.getCtr()).subtract(new BigDecimal(voBuilder.getCompareCtr()));
                                voBuilder.setCompareCtrRate(new BigDecimal(voBuilder.getCompareCtr()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                        ctrDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareCtr()), 2, RoundingMode.HALF_UP).toString());

                                voBuilder.setCompareCvr(StringUtils.isNotBlank(compareItem.getCvr()) ? compareItem.getCvr() : "0");
                                //cvr环比值
                                BigDecimal cvrDiff = new BigDecimal(voBuilder.getCvr()).subtract(new BigDecimal(voBuilder.getCompareCvr()));
                                voBuilder.setCompareCvrRate(new BigDecimal(voBuilder.getCompareCvr()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                        cvrDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareCvr()), 2, RoundingMode.HALF_UP).toString());

                                voBuilder.setCompareAcos(StringUtils.isNotBlank(compareItem.getAcos()) ? compareItem.getAcos() : "0");
                                //Acos环比值
                                BigDecimal acosDiff = new BigDecimal(voBuilder.getAcos()).subtract(new BigDecimal(voBuilder.getCompareAcos()));
                                voBuilder.setCompareAcosRate(new BigDecimal(voBuilder.getCompareAcos()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                        acosDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAcos()), 2, RoundingMode.HALF_UP).toString());

                                voBuilder.setCompareAcots(StringUtils.isNotBlank(compareItem.getAcots()) ? compareItem.getAcots() : "0");
                                //Acots环比值
                                BigDecimal acotsDiff = new BigDecimal(voBuilder.getAcots()).subtract(new BigDecimal(voBuilder.getCompareAcots()));
                                voBuilder.setCompareAcotsRate(new BigDecimal(voBuilder.getCompareAcots()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                        acotsDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAcots()), 2, RoundingMode.HALF_UP).toString());

                                voBuilder.setCompareAsots(StringUtils.isNotBlank(compareItem.getAsots()) ? compareItem.getAsots() : "0");
                                //Asots环比值
                                BigDecimal asotsDiff = new BigDecimal(voBuilder.getAsots()).subtract(new BigDecimal(voBuilder.getCompareAsots()));
                                voBuilder.setCompareAsotsRate(new BigDecimal(voBuilder.getCompareAsots()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                        asotsDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAsots()), 2, RoundingMode.HALF_UP).toString());

                                voBuilder.setCompareAdOrderNum(Int32Value.of(Optional.ofNullable(compareItem.getAdOrderNum()).orElse(0)));
                                //AdOrderNum环比值
                                int adOrderNumDiff = voBuilder.getAdOrderNum().getValue() - voBuilder.getCompareAdOrderNum().getValue();
                                voBuilder.setCompareAdOrderNumRate(voBuilder.getCompareAdOrderNum().getValue() == 0 ? "-" :
                                        new BigDecimal(adOrderNumDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdOrderNum().getValue()),
                                                2, RoundingMode.HALF_UP).toString());

                                voBuilder.setCompareAdCost(StringUtils.isNotBlank(compareItem.getAdCost()) ? compareItem.getAdCost() : "0");
                                //AdCost环比值
                                BigDecimal adCostDiff = new BigDecimal(voBuilder.getAdCost()).subtract(new BigDecimal(voBuilder.getCompareAdCost()));
                                voBuilder.setCompareAdCostRate(new BigDecimal(voBuilder.getCompareAdCost()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                        adCostDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdCost()), 2, RoundingMode.HALF_UP).toString());

                                voBuilder.setCompareAdCostPerClick(StringUtils.isNotBlank(compareItem.getAdCostPerClick()) ? compareItem.getAdCostPerClick() : "0");
                                //AdCostPerClick环比值
                                BigDecimal adCostPerClickDiff = new BigDecimal(voBuilder.getAdCostPerClick()).subtract(new BigDecimal(voBuilder.getCompareAdCostPerClick()));
                                voBuilder.setCompareAdCostPerClickRate(new BigDecimal(voBuilder.getCompareAdCostPerClick()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                        adCostPerClickDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdCostPerClick()), 2, RoundingMode.HALF_UP).toString());

                                voBuilder.setCompareAdSale(StringUtils.isNotBlank(compareItem.getAdSale()) ? compareItem.getAdSale() : "0");
                                //AdSale环比值
                                BigDecimal adSaleDiff = new BigDecimal(voBuilder.getAdSale()).subtract(new BigDecimal(voBuilder.getCompareAdSale()));
                                voBuilder.setCompareAdSaleRate(new BigDecimal(voBuilder.getCompareAdSale()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                        adSaleDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdSale()), 2, RoundingMode.HALF_UP).toString());

                                voBuilder.setCompareRoas(StringUtils.isNotBlank(compareItem.getRoas()) ? compareItem.getRoas() : "0");
                                //Roas环比值
                                BigDecimal roasDiff = new BigDecimal(voBuilder.getRoas()).subtract(new BigDecimal(voBuilder.getCompareRoas()));
                                voBuilder.setCompareRoasRate(new BigDecimal(voBuilder.getCompareRoas()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                        roasDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareRoas()), 2, RoundingMode.HALF_UP).toString());

                                voBuilder.setCompareCpa(StringUtils.isNotBlank(compareItem.getCpa()) ? compareItem.getCpa() : "0");
                                //Cpa环比值
                                BigDecimal CpaDiff = new BigDecimal(voBuilder.getCpa()).subtract(new BigDecimal(voBuilder.getCompareCpa()));
                                voBuilder.setCompareCpaRate(new BigDecimal(voBuilder.getCompareCpa()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                        CpaDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareCpa()), 2, RoundingMode.HALF_UP).toString());

                                voBuilder.setCompareVcpm(StringUtils.isNotBlank(compareItem.getVcpm()) ? compareItem.getVcpm() : "0");
                                //Vcpm环比值
                                if(SBCampaignCostTypeEnum.VCPM.getCode().equals(item.getCostType())){
                                    BigDecimal vcpmDiff = new BigDecimal(voBuilder.getVcpm()).subtract(new BigDecimal(voBuilder.getCompareVcpm()));
                                    voBuilder.setCompareVcpmRate(new BigDecimal(voBuilder.getCompareVcpm()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                            vcpmDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareVcpm()), 2, RoundingMode.HALF_UP).toString());
                                }else{
                                    voBuilder.setCompareVcpmRate("-");
                                }

                                voBuilder.setCompareAdSaleNum(Int32Value.of(Optional.ofNullable(compareItem.getAdSaleNum()).orElse(0)));
                                //AdSaleNum比值
                                int adSaleNumDiff = voBuilder.getAdSaleNum().getValue() - voBuilder.getCompareAdSaleNum().getValue();
                                voBuilder.setCompareAdSaleNumRate(voBuilder.getCompareAdSaleNum().getValue() == 0 ? "-" :
                                        new BigDecimal(adSaleNumDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdSaleNum().getValue()),
                                                2, RoundingMode.HALF_UP).toString());

                                voBuilder.setCompareAdOtherOrderNum(Int32Value.of(Optional.ofNullable(compareItem.getAdOtherOrderNum()).orElse(0)));
                                //AdOtherOrderNum比值
                                int adOtherOrderNumDiff = voBuilder.getAdOtherOrderNum().getValue() - voBuilder.getCompareAdOtherOrderNum().getValue();
                                voBuilder.setCompareAdOtherOrderNumRate(voBuilder.getCompareAdOtherOrderNum().getValue() == 0 ? "-" :
                                        new BigDecimal(adOtherOrderNumDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdOtherOrderNum().getValue()),
                                                2, RoundingMode.HALF_UP).toString());

                                voBuilder.setCompareAdSales(StringUtils.isNotBlank(compareItem.getAdSales()) ? compareItem.getAdSales() : "0");
                                //AdSales环比值
                                BigDecimal adSalesDiff = new BigDecimal(voBuilder.getAdSales()).subtract(new BigDecimal(voBuilder.getCompareAdSales()));
                                voBuilder.setCompareAdSalesRate(new BigDecimal(voBuilder.getCompareAdSales()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                        adSalesDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdSales()), 2, RoundingMode.HALF_UP).toString());


                                voBuilder.setCompareAdOtherSales(StringUtils.isNotBlank(compareItem.getAdOtherSales()) ? compareItem.getAdOtherSales() : "0");
                                //AdOtherSales环比值
                                BigDecimal adOtherSalesDiff = new BigDecimal(voBuilder.getAdOtherSales()).subtract(new BigDecimal(voBuilder.getCompareAdOtherSales()));
                                voBuilder.setCompareAdOtherSalesRate(new BigDecimal(voBuilder.getCompareAdOtherSales()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                        adOtherSalesDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdOtherSales()), 2, RoundingMode.HALF_UP).toString());

                                voBuilder.setCompareOrderNum(Int32Value.of(Optional.ofNullable(compareItem.getOrderNum()).orElse(0)));
                                //OrderNum比值
                                int orderNumDiff = voBuilder.getOrderNum().getValue() - voBuilder.getCompareOrderNum().getValue();
                                voBuilder.setCompareOrderNumRate(voBuilder.getCompareOrderNum().getValue() == 0 ? "-" :
                                        new BigDecimal(orderNumDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareOrderNum().getValue()),
                                                2, RoundingMode.HALF_UP).toString());

                                voBuilder.setCompareAdSelfSaleNum(Int32Value.of(Optional.ofNullable(compareItem.getAdSelfSaleNum()).orElse(0)));
                                //AdSelfSaleNum比值
                                int adSelfSaleNumDiff = voBuilder.getAdSelfSaleNum().getValue() - voBuilder.getCompareAdSelfSaleNum().getValue();
                                voBuilder.setCompareAdSelfSaleNumRate(voBuilder.getCompareAdSelfSaleNum().getValue() == 0 ? "-" :
                                        new BigDecimal(adSelfSaleNumDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdSelfSaleNum().getValue()),
                                                2, RoundingMode.HALF_UP).toString());

                                voBuilder.setCompareAdOtherSaleNum(Int32Value.of(Optional.ofNullable(compareItem.getAdOtherSaleNum()).orElse(0)));
                                //AdOtherSaleNum比值
                                int adOtherSaleNumDiff = voBuilder.getAdOtherSaleNum().getValue() - voBuilder.getCompareAdOtherSaleNum().getValue();
                                voBuilder.setCompareAdOtherSaleNumRate(voBuilder.getCompareAdOtherSaleNum().getValue() == 0 ? "-" :
                                        new BigDecimal(adOtherSaleNumDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdOtherSaleNum().getValue()),
                                                2, RoundingMode.HALF_UP).toString());

                                voBuilder.setCompareOrdersNewToBrandFTD(Int32Value.of(Optional.ofNullable(compareItem.getOrdersNewToBrandFTD()).orElse(0)));
                                //OrdersNewToBrandFTD比值
                                int ordersNewToBrandFTDDiff = voBuilder.getOrdersNewToBrandFTD().getValue() - voBuilder.getCompareOrdersNewToBrandFTD().getValue();
                                voBuilder.setCompareOrdersNewToBrandFTDRate(voBuilder.getCompareOrdersNewToBrandFTD().getValue() == 0 ? "-" :
                                        new BigDecimal(ordersNewToBrandFTDDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareOrdersNewToBrandFTD().getValue()),
                                                2, RoundingMode.HALF_UP).toString());

                                voBuilder.setCompareOrderRateNewToBrandFTD(StringUtils.isNotBlank(compareItem.getOrderRateNewToBrandFTD()) ?
                                        compareItem.getOrderRateNewToBrandFTD() : "0");
                                //OrderRateNewToBrandFTD环比值
                                BigDecimal orderRateNewToBrandFTDDiff = new BigDecimal(voBuilder.getOrderRateNewToBrandFTD()).subtract(new BigDecimal(voBuilder.getCompareOrderRateNewToBrandFTD()));
                                voBuilder.setCompareOrderRateNewToBrandFTDRate(new BigDecimal(voBuilder.getCompareOrderRateNewToBrandFTD()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                        orderRateNewToBrandFTDDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareOrderRateNewToBrandFTD()), 2, RoundingMode.HALF_UP).toString());

                                voBuilder.setCompareSalesNewToBrandFTD(StringUtils.isNotBlank(compareItem.getSalesNewToBrandFTD()) ?
                                        compareItem.getSalesNewToBrandFTD() : "0");
                                //SalesNewToBrandFTD环比值
                                BigDecimal salesNewToBrandFTDDiff = new BigDecimal(voBuilder.getSalesNewToBrandFTD()).subtract(new BigDecimal(voBuilder.getCompareSalesNewToBrandFTD()));
                                voBuilder.setCompareSalesNewToBrandFTDRate(new BigDecimal(voBuilder.getCompareSalesNewToBrandFTD()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                        salesNewToBrandFTDDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareSalesNewToBrandFTD()), 2, RoundingMode.HALF_UP).toString());

                                voBuilder.setCompareSalesRateNewToBrandFTD(StringUtils.isNotBlank(compareItem.getSalesRateNewToBrandFTD()) ?
                                        compareItem.getSalesRateNewToBrandFTD() : "0");
                                //SalesRateNewToBrandFTD环比值
                                BigDecimal salesRateNewToBrandFTDDiff = new BigDecimal(voBuilder.getSalesRateNewToBrandFTD()).subtract(new BigDecimal(voBuilder.getCompareSalesRateNewToBrandFTD()));
                                voBuilder.setCompareSalesRateNewToBrandFTDRate(new BigDecimal(voBuilder.getCompareSalesRateNewToBrandFTD()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                        salesRateNewToBrandFTDDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareSalesRateNewToBrandFTD()), 2, RoundingMode.HALF_UP).toString());

                                voBuilder.setCompareUnitsOrderedNewToBrandFTD(Int32Value.of(Optional.ofNullable(compareItem.getUnitsOrderedNewToBrandFTD()).orElse(0)));
                                //UnitsOrderedNewToBrandFTD比值
                                int unitsOrderedNewToBrandFTDDiff = voBuilder.getUnitsOrderedNewToBrandFTD().getValue() - voBuilder.getCompareUnitsOrderedNewToBrandFTD().getValue();
                                voBuilder.setCompareUnitsOrderedNewToBrandFTDRate(voBuilder.getCompareUnitsOrderedNewToBrandFTD().getValue() == 0 ? "-" :
                                        new BigDecimal(unitsOrderedNewToBrandFTDDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareUnitsOrderedNewToBrandFTD().getValue()),
                                                2, RoundingMode.HALF_UP).toString());

                                voBuilder.setCompareUnitsOrderedRateNewToBrandFTD(StringUtils.isNotBlank(compareItem.getUnitsOrderedRateNewToBrandFTD()) ?
                                        compareItem.getUnitsOrderedRateNewToBrandFTD() : "0");
                                //UnitsOrderedRateNewToBrandFTD环比值
                                BigDecimal UnitsOrderedRateNewToBrandFTDDiff = new BigDecimal(voBuilder.getUnitsOrderedRateNewToBrandFTD()).subtract(new BigDecimal(voBuilder.getCompareUnitsOrderedRateNewToBrandFTD()));
                                voBuilder.setCompareUnitsOrderedRateNewToBrandFTDRate(new BigDecimal(voBuilder.getCompareUnitsOrderedRateNewToBrandFTD()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                        UnitsOrderedRateNewToBrandFTDDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareUnitsOrderedRateNewToBrandFTD()), 2, RoundingMode.HALF_UP).toString());

                                voBuilder.setCompareAdCostPercentage(StringUtils.isNotBlank(compareItem.getAdCostPercentage()) ?
                                        compareItem.getAdCostPercentage() : "0");
                                //AdCostPercentage环比值
                                BigDecimal adCostPercentageDiff = new BigDecimal(voBuilder.getAdCostPercentage()).subtract(new BigDecimal(voBuilder.getCompareAdCostPercentage()));
                                voBuilder.setCompareAdCostPercentageRate(new BigDecimal(voBuilder.getCompareAdCostPercentage()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                        adCostPercentageDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdCostPercentage()), 2, RoundingMode.HALF_UP).toString());

                                voBuilder.setCompareAdSalePercentage(StringUtils.isNotBlank(compareItem.getAdSalePercentage()) ? compareItem.getAdSalePercentage() : "0");
                                //AdSalePercentage环比值
                                BigDecimal adSalePercentageDiff = new BigDecimal(voBuilder.getAdSalePercentage()).subtract(new BigDecimal(voBuilder.getCompareAdSalePercentage()));
                                voBuilder.setCompareAdSalePercentageRate(new BigDecimal(voBuilder.getCompareAdSalePercentage()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                        adSalePercentageDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdSalePercentage()), 2, RoundingMode.HALF_UP).toString());

                                voBuilder.setCompareAdOrderNumPercentage(StringUtils.isNotBlank(compareItem.getAdOrderNumPercentage()) ? compareItem.getAdOrderNumPercentage() : "0");
                                //AdOrderNumPercentage环比值
                                BigDecimal adOrderNumPercentageDiff = new BigDecimal(voBuilder.getAdOrderNumPercentage()).subtract(new BigDecimal(voBuilder.getCompareAdOrderNumPercentage()));
                                voBuilder.setCompareAdOrderNumPercentageRate(new BigDecimal(voBuilder.getCompareAdOrderNumPercentage()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                        adOrderNumPercentageDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdOrderNumPercentage()), 2, RoundingMode.HALF_UP).toString());

                                voBuilder.setCompareOrderNumPercentage(StringUtils.isNotBlank(compareItem.getOrderNumPercentage()) ? compareItem.getOrderNumPercentage() : "0");
                                //OrderNumPercentage环比值
                                BigDecimal orderNumPercentageDiff = new BigDecimal(voBuilder.getOrderNumPercentage()).subtract(new BigDecimal(voBuilder.getCompareOrderNumPercentage()));
                                voBuilder.setCompareOrderNumPercentageRate(new BigDecimal(voBuilder.getCompareOrderNumPercentage()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                        orderNumPercentageDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareOrderNumPercentage()), 2, RoundingMode.HALF_UP).toString());

                                voBuilder.setCompareNewToBrandDetailPageViews(Optional.ofNullable(compareItem.getNewToBrandDetailPageViews()).map(String::valueOf).orElse("0"));
                                voBuilder.setCompareNewToBrandDetailPageViewsRate(MathUtil.isNullOrZero(voBuilder.getCompareNewToBrandDetailPageViews()) ? "-" :
                                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getNewToBrandDetailPageViews(), voBuilder.getCompareNewToBrandDetailPageViews(), 4), 100)));
                                voBuilder.setCompareAddToCart(Optional.ofNullable(compareItem.getAddToCart()).map(String::valueOf).orElse("0"));
                                voBuilder.setCompareAddToCartRates(MathUtil.isNullOrZero(voBuilder.getCompareAddToCart()) ? "-" :
                                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getAddToCart(), voBuilder.getCompareAddToCart(), 4), 100)));
                                voBuilder.setCompareAddToCartRate(Optional.ofNullable(compareItem.getAddToCartRate()).map(String::valueOf).orElse("0"));
                                voBuilder.setCompareAddToCartRateRate(MathUtil.isNullOrZero(voBuilder.getCompareAddToCartRate()) ? "-" :
                                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getAddToCartRate(), voBuilder.getCompareAddToCartRate(), 4), 100)));
                                voBuilder.setCompareECPAddToCart(Optional.ofNullable(compareItem.getECPAddToCart()).map(String::valueOf).orElse("0"));
                                voBuilder.setCompareECPAddToCartRate(MathUtil.isNullOrZero(voBuilder.getCompareECPAddToCart()) ? "-" :
                                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getECPAddToCart(), voBuilder.getCompareECPAddToCart(), 4), 100)));
                                voBuilder.setCompareVideoFirstQuartileViews(Optional.ofNullable(compareItem.getVideoFirstQuartileViews()).map(String::valueOf).orElse("0"));
                                voBuilder.setCompareVideoFirstQuartileViewsRate(MathUtil.isNullOrZero(voBuilder.getCompareVideoFirstQuartileViews()) ? "-" :
                                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getVideoFirstQuartileViews(), voBuilder.getCompareVideoFirstQuartileViews(), 4), 100)));
                                voBuilder.setCompareVideoMidpointViews(Optional.ofNullable(compareItem.getVideoMidpointViews()).map(String::valueOf).orElse("0"));
                                voBuilder.setCompareVideoMidpointViewsRate(MathUtil.isNullOrZero(voBuilder.getCompareVideoMidpointViews()) ? "-" :
                                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getVideoMidpointViews(), voBuilder.getCompareVideoMidpointViews(), 4), 100)));
                                voBuilder.setCompareVideoThirdQuartileViews(Optional.ofNullable(compareItem.getVideoThirdQuartileViews()).map(String::valueOf).orElse("0"));
                                voBuilder.setCompareVideoThirdQuartileViewsRate(MathUtil.isNullOrZero(voBuilder.getCompareVideoThirdQuartileViews()) ? "-" :
                                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getVideoThirdQuartileViews(), voBuilder.getCompareVideoThirdQuartileViews(), 4), 100)));
                                voBuilder.setCompareVideoCompleteViews(Optional.ofNullable(compareItem.getVideoCompleteViews()).map(String::valueOf).orElse("0"));
                                voBuilder.setCompareVideoCompleteViewsRate(MathUtil.isNullOrZero(voBuilder.getCompareVideoCompleteViews()) ? "-" :
                                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getVideoCompleteViews(), voBuilder.getCompareVideoCompleteViews(), 4), 100)));
                                voBuilder.setCompareVideoUnmutes(Optional.ofNullable(compareItem.getVideoUnmutes()).map(String::valueOf).orElse("0"));
                                voBuilder.setCompareVideoUnmutesRate(MathUtil.isNullOrZero(voBuilder.getCompareVideoUnmutes()) ? "-" :
                                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getVideoUnmutes(), voBuilder.getCompareVideoUnmutes(), 4), 100)));
                                voBuilder.setCompareViewabilityRate(Optional.ofNullable(compareItem.getViewabilityRate()).map(String::valueOf).orElse("0"));
                                voBuilder.setCompareViewabilityRateRate(MathUtil.isNullOrZero(voBuilder.getCompareViewabilityRate()) ? "-" :
                                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getViewabilityRate(), voBuilder.getCompareViewabilityRate(), 4), 100)));
                                voBuilder.setCompareViewClickThroughRate(Optional.ofNullable(compareItem.getViewClickThroughRate()).map(String::valueOf).orElse("0"));
                                voBuilder.setCompareViewClickThroughRateRate(MathUtil.isNullOrZero(voBuilder.getCompareViewClickThroughRate()) ? "-" :
                                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getViewClickThroughRate(), voBuilder.getCompareViewClickThroughRate(), 4), 100)));
                                voBuilder.setCompareBrandedSearches(Optional.ofNullable(compareItem.getBrandedSearches()).map(String::valueOf).orElse("0"));
                                voBuilder.setCompareBrandedSearchesRate(MathUtil.isNullOrZero(voBuilder.getCompareBrandedSearches()) ? "-" :
                                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getBrandedSearches(), voBuilder.getCompareBrandedSearches(), 4), 100)));
                                voBuilder.setCompareDetailPageViews(Optional.ofNullable(compareItem.getDetailPageViews()).map(String::valueOf).orElse("0"));
                                voBuilder.setCompareDetailPageViewsRate(MathUtil.isNullOrZero(voBuilder.getCompareDetailPageViews()) ? "-" :
                                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getDetailPageViews(), voBuilder.getCompareDetailPageViews(), 4), 100)));
                                voBuilder.setCompareImpressionsFrequencyAverage(Optional.ofNullable(compareItem.getImpressionsFrequencyAverage()).map(String::valueOf).orElse("0"));
                                voBuilder.setCompareImpressionsFrequencyAverageRate(MathUtil.isNullOrZero(voBuilder.getCompareImpressionsFrequencyAverage()) ? "-" :
                                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getImpressionsFrequencyAverage(), voBuilder.getCompareImpressionsFrequencyAverage(), 4), 100)));
                                voBuilder.setCompareAdvertisingUnitPrice(Optional.ofNullable(compareItem.getAdvertisingUnitPrice()).map(String::valueOf).orElse("0"));
                                voBuilder.setCompareAdvertisingUnitPriceRate(MathUtil.isNullOrZero(voBuilder.getCompareAdvertisingUnitPrice()) ? "-" :
                                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getAdvertisingUnitPrice(), voBuilder.getCompareAdvertisingUnitPrice(), 4), 100)));

                            }
                        }
                        if (isVc) {
                            voBuilder.setAcots("-");
                            voBuilder.setCompareAcotsRate("-");
                            voBuilder.setCompareAcots("-");
                            voBuilder.setAsots("-");
                            voBuilder.setCompareAsotsRate("-");
                            voBuilder.setCompareAsots("-");
                        }

                        return voBuilder.build();

                    }).collect(Collectors.toList());
            pageBuilder.addAllRows(rpcVos);
        }

        return AllProductDataResponse.AdProductHomeVo.newBuilder()
                .setPage(pageBuilder.build())
                .build();
    }

    private Map<String, AdProductCommonInfoDto> mockData(List<AdProductPageVo> rows) {
        Map<String, AdProductCommonInfoDto> map = new HashMap<>(rows.size());
        for (AdProductPageVo adProductPageVo : rows) {
            AdProductCommonInfoDto adProductCommonInfoDto = new AdProductCommonInfoDto();
            adProductCommonInfoDto.setPrice("88.88");
            adProductCommonInfoDto.setRating(5.0);
            adProductCommonInfoDto.setRatingCount("88888");

            FbaInventoryDto fbaInventoryDto = new FbaInventoryDto();
            fbaInventoryDto.setSku(adProductPageVo.getSku());
            fbaInventoryDto.setAvailable(8888);
            fbaInventoryDto.setReservedTransfer(1000);
            fbaInventoryDto.setReservedProcessing(100);
            fbaInventoryDto.setInboundReceiving(11);
            fbaInventoryDto.setSubtotal(fbaInventoryDto.getAvailable() + fbaInventoryDto.getReservedTransfer() + fbaInventoryDto.getReservedProcessing() + fbaInventoryDto.getInboundReceiving());
            fbaInventoryDto.setReservedCustomerOrders(20000);
            fbaInventoryDto.setInboundWorking(20000);
            fbaInventoryDto.setInboundShipped(20000);
            fbaInventoryDto.setUnfulfillable(20000);
            fbaInventoryDto.setResearch(10000);
            fbaInventoryDto.setTotalInventory(fbaInventoryDto.getSubtotal() + fbaInventoryDto.getReservedCustomerOrders() +
                    fbaInventoryDto.getInboundWorking() + fbaInventoryDto.getInboundShipped() + fbaInventoryDto.getUnfulfillable() + fbaInventoryDto.getResearch());

            adProductCommonInfoDto.setFbaInventoryDto(fbaInventoryDto);
            map.put(adProductPageVo.getSku(), adProductCommonInfoDto);
        }
        return map;
    }

    /**
     * 查询广告产品的fba可售信息
     * 只会返回差得到的
     *
     * @param rows
     * @param puid
     * @return key：shopId + msku，value：可售信息
     */
    @Override
    public Map<String, AdProductCommonInfoDto> getAdProductFbaInfo(List<AdProductPageVo> rows, int puid) {
        if (CollectionUtils.isEmpty(rows)) {
            return Collections.emptyMap();
        }
        List<GetFbaReviewInfoReq.MskuShopId> mskuShopIds = new ArrayList<>(rows.size());
        Set<String> repeatMskuShopId = new HashSet<>();
        for (AdProductPageVo adProductPageVo : rows) {
            if (repeatMskuShopId.contains(adProductPageVo.getShopId() + adProductPageVo.getSku())) {
                continue;
            }
            repeatMskuShopId.add(adProductPageVo.getShopId() + adProductPageVo.getSku());
            GetFbaReviewInfoReq.MskuShopId mskuShopId = new GetFbaReviewInfoReq.MskuShopId();
            mskuShopId.setMsku(adProductPageVo.getSku());
            mskuShopId.setShopId(adProductPageVo.getShopId());
            mskuShopIds.add(mskuShopId);
        }
        List<GetFbaReviewInfoResp.FbaInfo> fbaReviewInfos = dataDomainManager.getFbaReviewInfo(puid, mskuShopIds);
        Map<String, AdProductCommonInfoDto> result = new HashMap<>();
        for (GetFbaReviewInfoResp.FbaInfo fbaInfo : fbaReviewInfos) {
            if (fbaInfo == null) {
                continue;
            }
            AdProductCommonInfoDto adProductCommonInfoDto = new AdProductCommonInfoDto();
            adProductCommonInfoDto.setPrice(fbaInfo.getPrice() != null ? Double.toString(fbaInfo.getPrice()) : null);
            adProductCommonInfoDto.setRating(StringUtils.isNotBlank(fbaInfo.getRating()) ? Double.valueOf(fbaInfo.getRating()) : null);
            adProductCommonInfoDto.setRatingCount(fbaInfo.getRatingCount() != null ? fbaInfo.getRatingCount().toString() : null);

            FbaInventoryDto fbaInventoryDto = new FbaInventoryDto();
            fbaInventoryDto.setSku(fbaInfo.getMsku());
            fbaInventoryDto.setAvailable(fbaInfo.getAvailable());
            fbaInventoryDto.setReservedTransfer(fbaInfo.getReservedTransfer());
            fbaInventoryDto.setReservedProcessing(fbaInfo.getReservedProcessing());
            fbaInventoryDto.setInboundReceiving(fbaInfo.getInboundReceiving());
            if (fbaInfo.getAvailable() != null || fbaInfo.getReservedTransfer() != null || fbaInfo.getReservedProcessing() != null || fbaInfo.getInboundReceiving() != null) {
                fbaInventoryDto.setSubtotal(Optional.ofNullable(fbaInventoryDto.getAvailable()).orElse(0) + Optional.ofNullable(fbaInventoryDto.getReservedTransfer()).orElse(0)
                        + Optional.ofNullable(fbaInventoryDto.getReservedProcessing()).orElse(0) + Optional.ofNullable(fbaInventoryDto.getInboundReceiving()).orElse(0));
            }
            fbaInventoryDto.setReservedCustomerOrders(fbaInfo.getReservedCustomerorders());
            fbaInventoryDto.setInboundWorking(fbaInfo.getInboundWorking());
            fbaInventoryDto.setInboundShipped(fbaInfo.getInboundShipped());
            fbaInventoryDto.setUnfulfillable(fbaInfo.getUnfulfillable());
            fbaInventoryDto.setResearch(fbaInfo.getResearch());
            fbaInventoryDto.setTotalInventory(fbaInfo.getTotalInventory());

            adProductCommonInfoDto.setFbaInventoryDto(fbaInventoryDto);

            result.put(fbaInfo.getShopId() + fbaInfo.getMsku(), adProductCommonInfoDto);
        }

        return result;
    }

    private void builderAdProductCommonInfo(AdProductCommonInfoDto adProductCommonInfoDto, AllProductDataResponse.AdProductHomeVo.Page.AdProductPageVo.Builder voBuilder) {
        FbaInventoryDto fbaInventoryDto = adProductCommonInfoDto.getFbaInventoryDto();
        FbaInventoryManageDto.Builder fbaInventoryBuilder = FbaInventoryManageDto.newBuilder();
        if (fbaInventoryDto.getAvailable() != null) {
            fbaInventoryBuilder.setAvailable(fbaInventoryDto.getAvailable());
        }
        if (fbaInventoryDto.getReservedTransfer() != null) {
            fbaInventoryBuilder.setReservedTransfer(fbaInventoryDto.getReservedTransfer());
        }
        if (fbaInventoryDto.getReservedProcessing() != null) {
            fbaInventoryBuilder.setReservedProcessing(fbaInventoryDto.getReservedProcessing());
        }
        if (fbaInventoryDto.getInboundReceiving() != null) {
            fbaInventoryBuilder.setInboundReceiving(fbaInventoryDto.getInboundReceiving());
        }
        if (fbaInventoryDto.getSubtotal() != null) {
            fbaInventoryBuilder.setSubtotal(fbaInventoryDto.getSubtotal());
        }
        if (fbaInventoryDto.getReservedCustomerOrders() != null) {
            fbaInventoryBuilder.setReservedCustomerOrders(fbaInventoryDto.getReservedCustomerOrders());
        }
        if (fbaInventoryDto.getInboundWorking() != null) {
            fbaInventoryBuilder.setInboundWorking(fbaInventoryDto.getInboundWorking());
        }
        if (fbaInventoryDto.getInboundShipped() != null) {
            fbaInventoryBuilder.setInboundShipped(fbaInventoryDto.getInboundShipped());
        }
        if (fbaInventoryDto.getUnfulfillable() != null) {
            fbaInventoryBuilder.setUnfulfillable(fbaInventoryDto.getUnfulfillable());
        }
        if (fbaInventoryDto.getResearch() != null) {
            fbaInventoryBuilder.setResearch(fbaInventoryDto.getResearch());
        }
        if (fbaInventoryDto.getTotalInventory() != null) {
            fbaInventoryBuilder.setTotalInventory(fbaInventoryDto.getTotalInventory());
        }
        voBuilder.setInventoryManage(fbaInventoryBuilder.build());

        if (StringUtils.isNotBlank(adProductCommonInfoDto.getPrice())) {
            voBuilder.setPrice(adProductCommonInfoDto.getPrice());
        }
        if (adProductCommonInfoDto.getRating() != null) {
            voBuilder.setRating(adProductCommonInfoDto.getRating());
        }
        if (StringUtils.isNotBlank(adProductCommonInfoDto.getRatingCount())) {
            voBuilder.setRatingCount(adProductCommonInfoDto.getRatingCount());
        }
    }

    @Override
    public AllProductDataResponse.AdProductHomeVo getAllSpProductDorisData(int puid, AdProductPageParam param) {
        Page<AdProductPageVo> voPage = new Page<>();
        voPage.setPageSize(param.getPageSize());
        voPage.setPageNo(param.getPageNo());
        //查询所有数据(sb sd)

        ShopAuth shopAuth = shopAuthDao.getScAndVcById(param.getShopId());
        if (shopAuth == null) {
            AssertUtil.fail("店铺未授权");
        }

        // 取店铺销售额
        BigDecimal shopSalesByDate = CpCShopDataService.getShopSalesByDate(shopAuth.getId(), param.getStartDate(), param.getEndDate());
        if (shopSalesByDate == null) {
            shopSalesByDate = BigDecimal.ZERO;
        }
        param.setShopSales(shopSalesByDate);

        //勾选正在投放按钮移除其它
        if (StringUtils.isNotBlank(param.getServingStatus())) {
            List<String> list = StringUtil.splitStr(param.getServingStatus());
            if (list.contains("enabled")) {
                param.setServingStatus("enabled");
            }
        }

        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        voPage = getSpProductDorisVoList(shopAuth, puid, param);
        stopWatch.stop();
        log.info("getAllSpProductDorisData : getSpProductDorisVoList time:{}", stopWatch.getLastTaskTimeMillis());

        //获取不同类型数据 sp、sd
        stopWatch.start();
        fillAdTagData(puid, param.getShopId(), param, voPage.getRows());
        stopWatch.stop();
        log.info("getAllSpProductDorisData : 标签数据填充花费 time{}", stopWatch.getLastTaskTimeMillis());
        fillinAdInfoForVo(shopAuth, voPage.getRows(), false);

        AllProductDataResponse.AdProductHomeVo.Page.Builder pageBuilder = AllProductDataResponse.AdProductHomeVo.Page.newBuilder();
        pageBuilder.setPageNo(Int32Value.of(voPage.getPageNo()));
        pageBuilder.setPageSize(Int32Value.of(voPage.getPageSize()));
        pageBuilder.setTotalPage(Int32Value.of(voPage.getTotalPage()));
        pageBuilder.setTotalSize(Int32Value.of(voPage.getTotalSize()));
        List<AdProductPageVo> rows = voPage.getRows();
        if (CollectionUtils.isNotEmpty(rows)) {
            //对比时无须高级搜索条件
            param.setUseAdvanced(false);
            //环比数据
            BigDecimal shopSalesByDateCompare = BigDecimal.ZERO;
            Map<String, AdProductPageVo> compareProductMap = null;
            if (param.getIsCompare()) {
                stopWatch.start();
                // 取店铺销售额
                shopSalesByDateCompare = CpCShopDataService.getShopSalesByDate(shopAuth.getId(),
                        param.getCompareStartDate(), param.getCompareEndDate());
                if (shopSalesByDateCompare == null) {
                    shopSalesByDateCompare = BigDecimal.ZERO;
                }
                param.setShopSales(shopSalesByDateCompare);
                param.setStartDate(param.getCompareStartDate());
                param.setEndDate(param.getCompareEndDate());
                param.setAdIds(rows.stream().map(AdProductPageVo::getAdId).collect(Collectors.toList()));

                List<AdProductPageVo> productPageVoList = getSpProductDorisVoList(shopAuth, puid, param).getRows();

                //list转map
                compareProductMap = productPageVoList.stream()
                        .collect(Collectors.toMap(AdProductPageVo::getAdId, Function.identity(), (a, b) -> a));
                stopWatch.stop();
                log.info("getAllSpProductDorisData : 对比数据花费 time{}", stopWatch.getLastTaskTimeMillis());
            }
            stopWatch.start();
            Map<String, AdProductCommonInfoDto> fbaInventory = getAdProductFbaInfo(rows, puid);
            stopWatch.stop();
            log.info("getAllSpProductDorisData : getAdProductFbaInfo time:{}", stopWatch.getLastTaskTimeMillis());
            Map<String, AdProductPageVo> finalCompareProductMap = compareProductMap;
            boolean isVc = ShopTypeEnum.VC.getCode().equals(shopAuth.getType());
            List<AllProductDataResponse.AdProductHomeVo.Page.AdProductPageVo> rpcVos = rows.stream()
                    .filter(Objects::nonNull)
                    .filter(r -> StringUtils.isNotEmpty(r.getAsin()) || StringUtils.isNotEmpty(r.getSku()))
                    .map(item -> {
                        AllProductDataResponse.AdProductHomeVo.Page.AdProductPageVo.Builder voBuilder = AllProductDataResponse.AdProductHomeVo.Page.AdProductPageVo.newBuilder();
                        voBuilder.setType(item.getType());
                        voBuilder.setId(Int64Value.of(item.getId()));
                        voBuilder.setShopId(Int32Value.of(item.getShopId()));
                        if (shopAuth != null) {
                            voBuilder.setMarketplaceId(shopAuth.getMarketplaceId());
                        }
                        if (CollectionUtils.isNotEmpty(item.getAdTags())) {
                            item.getAdTags().forEach(e -> {
                                com.meiyunji.sponsored.rpc.vo.AdTagVo.Builder builder = com.meiyunji.sponsored.rpc.vo.AdTagVo.newBuilder();
                                AdTagVo tagVo = builder.setId(Int64Value.of(e.getId())).setColor(e.getColor()).setName(e.getName()).build();
                                voBuilder.addAdTags(tagVo);
                            });
                        }

                        if (fbaInventory.containsKey(item.getShopId() + item.getSku())) {
                            builderAdProductCommonInfo(fbaInventory.get(item.getShopId() + item.getSku()), voBuilder);
                        }

                        if (item.getDxmAdGroupId() != null) {
                            voBuilder.setDxmAdGroupId(Int64Value.of(item.getDxmAdGroupId()));
                        }
                        if (StringUtils.isNotBlank(item.getCampaignId())) {
                            voBuilder.setCampaignId(item.getCampaignId());
                        }
                        if (StringUtils.isNotBlank(item.getCampaignName())) {
                            voBuilder.setCampaignName(item.getCampaignName());
                        }
                        if (StringUtils.isNotBlank(item.getCampaignTargetingType())) {
                            voBuilder.setCampaignTargetingType(item.getCampaignTargetingType());
                        }
                        if (StringUtils.isNotBlank(item.getCampaignState())) {
                            voBuilder.setCampaignState(item.getCampaignState());
                        }
                        if (StringUtils.isNotBlank(item.getAdGroupId())) {
                            voBuilder.setAdGroupId(item.getAdGroupId());
                        }
                        if (StringUtils.isNotBlank(item.getAdGroupType())) {
                            voBuilder.setAdGroupType(item.getAdGroupType());
                        }
                        if (StringUtils.isNotBlank(item.getAdGroupName())) {
                            voBuilder.setAdGroupName(item.getAdGroupName());
                        }
                        if (StringUtils.isNotBlank(item.getAdGroupState())) {
                            voBuilder.setAdGroupState(item.getAdGroupState());
                        }
                        if (StringUtils.isNotBlank(item.getAdId())) {
                            voBuilder.setAdId(item.getAdId());
                        }
                        if (StringUtils.isNotBlank(item.getState())) {
                            voBuilder.setState(item.getState());
                        }
                        if (StringUtils.isNotBlank(item.getAsin())) {
                            voBuilder.setAsin(item.getAsin());
                        }
                        if (StringUtils.isNotBlank(item.getSku())) {
                            voBuilder.setSku(item.getSku());
                        }
                        if (StringUtils.isNotBlank(item.getTitle())) {
                            voBuilder.setTitle(item.getTitle());
                        }
                        if (StringUtils.isNotBlank(item.getImgUrl())) {
                            if (item.getImgUrl().endsWith("S60_.jpg")) {
                                item.setImgUrl(item.getImgUrl().replace("S60_.jpg", "S600_.jpg"));
                            }
                            voBuilder.setImgUrl(item.getImgUrl());
                        }
                        if (StringUtils.isNotBlank(item.getPrice())) {
                            voBuilder.setPrice(item.getPrice());
                        }
                        if (StringUtils.isNotBlank(item.getDomain())) {
                            voBuilder.setDomain(item.getDomain());
                        }
                        if (StringUtils.isNotBlank(item.getPortfolioId())) {
                            voBuilder.setPortfolioId(item.getPortfolioId());
                        }
                        if (StringUtils.isNotBlank(item.getPortfolioName())) {
                            voBuilder.setPortfolioName(item.getPortfolioName());
                        }
                        if (item.getIsHidden() != null) {
                            voBuilder.setIsHidden(item.getIsHidden());
                        }

                        if (item.getServingStatus() != null) {
                            voBuilder.setServingStatus(item.getServingStatus());
                        }
                        if (item.getServingStatusDec() != null) {
                            voBuilder.setServingStatusDec(item.getServingStatusDec());
                        }
                        if (item.getServingStatusName() != null) {
                            voBuilder.setServingStatusName(item.getServingStatusName());
                        }

                        voBuilder.setImpressions(Int32Value.of(Optional.ofNullable(item.getImpressions()).orElse(0)));
                        voBuilder.setClicks(Int32Value.of(Optional.ofNullable(item.getClicks()).orElse(0)));
                        voBuilder.setAdOrderNum(Int32Value.of(Optional.ofNullable(item.getAdOrderNum()).orElse(0)));
                        voBuilder.setAdCostPerClick(StringUtils.isNotBlank(item.getAdCostPerClick()) ? item.getAdCostPerClick() : "0");
                        voBuilder.setCtr(StringUtils.isNotBlank(item.getCtr()) ? item.getCtr() : "0");
                        voBuilder.setCvr(StringUtils.isNotBlank(item.getCvr()) ? item.getCvr() : "0");
                        voBuilder.setAcos(StringUtils.isNotBlank(item.getAcos()) ? item.getAcos() : "0");
                        voBuilder.setRoas(StringUtils.isNotBlank(item.getRoas()) ? item.getRoas() : "0");
                        voBuilder.setAdCost(StringUtils.isNotBlank(item.getAdCost()) ? item.getAdCost() : "0");
                        voBuilder.setAcots(StringUtils.isNotBlank(item.getAcots()) ? item.getAcots() : "0");
                        voBuilder.setAsots(StringUtils.isNotBlank(item.getAsots()) ? item.getAsots() : "0");
                        voBuilder.setAdSale(StringUtils.isNotBlank(item.getAdSale()) ? item.getAdSale() : "0");
                        /**
                         * TODO 广告报告重构
                         * sd广告vcpm类型报告特殊字段。
                         */
                        //可见展示次数(VCPM专用)
                        voBuilder.setViewImpressions(Int32Value.of(Optional.ofNullable(item.getViewImpressions()).orElse(0)));
                        //每笔订单花费
                        voBuilder.setCpa(StringUtils.isNotBlank(item.getCpa()) ? item.getCpa() : "0");
                        //vcpm
                        voBuilder.setVcpm(StringUtils.isNotBlank(item.getVcpm()) ? item.getVcpm() : "0");
                        //本广告产品订单量
                        voBuilder.setAdSaleNum(Int32Value.of(Optional.ofNullable(item.getAdSaleNum()).orElse(0)));
                        //其他产品广告订单量
                        voBuilder.setAdOtherOrderNum(Int32Value.of(Optional.ofNullable(item.getAdOtherOrderNum()).orElse(0)));
                        //本广告产品销售额
                        voBuilder.setAdSales(StringUtils.isNotBlank(item.getAdSales()) ? item.getAdSales() : "0");
                        //其他产品广告销售额
                        voBuilder.setAdOtherSales(StringUtils.isNotBlank(item.getAdOtherSales()) ? item.getAdOtherSales() : "0");
                        //广告销量
                        voBuilder.setOrderNum(Int32Value.of(Optional.ofNullable(item.getOrderNum()).orElse(0)));
                        //本广告产品销量
                        voBuilder.setAdSelfSaleNum(Int32Value.of(Optional.ofNullable(item.getAdSelfSaleNum()).orElse(0)));
                        //其他产品广告销量
                        voBuilder.setAdOtherSaleNum(Int32Value.of(Optional.ofNullable(item.getAdOtherSaleNum()).orElse(0)));
                        //“品牌新买家”订单量
                        voBuilder.setOrdersNewToBrandFTD(Int32Value.of(Optional.ofNullable(item.getOrdersNewToBrandFTD()).orElse(0)));
                        //“品牌新买家”订单百分比
                        voBuilder.setOrderRateNewToBrandFTD(StringUtils.isNotBlank(item.getOrderRateNewToBrandFTD()) ? item.getOrderRateNewToBrandFTD() : "0");
                        //“品牌新买家”销售额
                        voBuilder.setSalesNewToBrandFTD(StringUtils.isNotBlank(item.getSalesNewToBrandFTD()) ? item.getSalesNewToBrandFTD() : "0");
                        //“品牌新买家”销售额百分比
                        voBuilder.setSalesRateNewToBrandFTD(StringUtils.isNotBlank(item.getSalesRateNewToBrandFTD()) ? item.getSalesRateNewToBrandFTD() : "0");
                        //“品牌新买家”销量
                        voBuilder.setUnitsOrderedNewToBrandFTD(Int32Value.of(Optional.ofNullable(item.getUnitsOrderedNewToBrandFTD()).orElse(0)));
                        //“品牌新买家”销量百分比
                        voBuilder.setUnitsOrderedRateNewToBrandFTD(StringUtils.isNotBlank(item.getUnitsOrderedRateNewToBrandFTD()) ? item.getUnitsOrderedRateNewToBrandFTD() : "0");
                        // 花费占比
                        voBuilder.setAdCostPercentage(StringUtils.isNotBlank(item.getAdCostPercentage()) ? item.getAdCostPercentage() : "0");
                        // 销售额占比
                        voBuilder.setAdSalePercentage(StringUtils.isNotBlank(item.getAdSalePercentage()) ? item.getAdSalePercentage() : "0");
                        // 订单量占比
                        voBuilder.setAdOrderNumPercentage(StringUtils.isNotBlank(item.getAdOrderNumPercentage()) ? item.getAdOrderNumPercentage() : "0");
                        // 销量占比
                        voBuilder.setOrderNumPercentage(StringUtils.isNotBlank(item.getOrderNumPercentage()) ? item.getOrderNumPercentage() : "0");

                        voBuilder.setNewToBrandDetailPageViews(Optional.ofNullable(item.getNewToBrandDetailPageViews()).map(String::valueOf).orElse("0"));
                        voBuilder.setAddToCart(Optional.ofNullable(item.getAddToCart()).map(String::valueOf).orElse("0"));
                        voBuilder.setAddToCartRate(Optional.ofNullable(item.getAddToCartRate()).map(String::valueOf).orElse("0"));
                        voBuilder.setECPAddToCart(Optional.ofNullable(item.getECPAddToCart()).map(String::valueOf).orElse("0"));
                        voBuilder.setVideoFirstQuartileViews(Optional.ofNullable(item.getVideoFirstQuartileViews()).map(String::valueOf).orElse("0"));
                        voBuilder.setVideoMidpointViews(Optional.ofNullable(item.getVideoMidpointViews()).map(String::valueOf).orElse("0"));
                        voBuilder.setVideoThirdQuartileViews(Optional.ofNullable(item.getVideoThirdQuartileViews()).map(String::valueOf).orElse("0"));
                        voBuilder.setVideoCompleteViews(Optional.ofNullable(item.getVideoCompleteViews()).map(String::valueOf).orElse("0"));
                        voBuilder.setVideoUnmutes(Optional.ofNullable(item.getVideoUnmutes()).map(String::valueOf).orElse("0"));
                        voBuilder.setViewabilityRate(Optional.ofNullable(item.getViewabilityRate()).map(String::valueOf).orElse("0"));
                        voBuilder.setViewClickThroughRate(Optional.ofNullable(item.getViewClickThroughRate()).map(String::valueOf).orElse("0"));
                        voBuilder.setBrandedSearches(Optional.ofNullable(item.getBrandedSearches()).map(String::valueOf).orElse("0"));
                        voBuilder.setDetailPageViews(Optional.ofNullable(item.getDetailPageViews()).map(String::valueOf).orElse("0"));
                        voBuilder.setCumulativeReach(Optional.ofNullable(item.getCumulativeReach()).map(String::valueOf).orElse("0"));
                        voBuilder.setImpressionsFrequencyAverage(Optional.ofNullable(item.getImpressionsFrequencyAverage()).map(String::valueOf).orElse("0"));
                        voBuilder.setAdvertisingUnitPrice(Optional.ofNullable(item.getAdvertisingUnitPrice()).map(String::valueOf).orElse("0"));


                        //环比指标数据
                        if (MapUtils.isNotEmpty(finalCompareProductMap)) {
                            if (finalCompareProductMap.containsKey(item.getAdId())) {
                                AdProductPageVo compareItem = finalCompareProductMap.get(item.getAdId());

                                voBuilder.setCompareViewImpressions(Int32Value.of(Optional.ofNullable(compareItem.getViewImpressions()).orElse(0)));
                                //可见展示环比值
                                int viewImpressionDiff = voBuilder.getViewImpressions().getValue() - voBuilder.getCompareViewImpressions().getValue();
                                voBuilder.setCompareViewImpressionsRate(voBuilder.getCompareViewImpressions().getValue() == 0 ? "-" :
                                        new BigDecimal(viewImpressionDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareViewImpressions().getValue()),
                                                2, RoundingMode.HALF_UP).toString());

                                voBuilder.setCompareImpressions(Int32Value.of(Optional.ofNullable(compareItem.getImpressions()).orElse(0)));
                                //曝光环比值
                                int impressionDiff = voBuilder.getImpressions().getValue() - voBuilder.getCompareImpressions().getValue();
                                voBuilder.setCompareImpressionsRate(voBuilder.getCompareImpressions().getValue() == 0 ? "-" :
                                        new BigDecimal(impressionDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareImpressions().getValue()),
                                                2, RoundingMode.HALF_UP).toString());

                                voBuilder.setCompareClicks(Int32Value.of(Optional.ofNullable(compareItem.getClicks()).orElse(0)));
                                //点击量环比值
                                int clicksDiff = voBuilder.getClicks().getValue() - voBuilder.getCompareClicks().getValue();
                                voBuilder.setCompareClicksRate(voBuilder.getCompareClicks().getValue() == 0 ? "-" :
                                        new BigDecimal(clicksDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareClicks().getValue()),
                                                2, RoundingMode.HALF_UP).toString());

                                voBuilder.setCompareCtr(StringUtils.isNotBlank(compareItem.getCtr()) ? compareItem.getCtr() : "0");
                                //ctr环比值
                                BigDecimal ctrDiff = new BigDecimal(voBuilder.getCtr()).subtract(new BigDecimal(voBuilder.getCompareCtr()));
                                voBuilder.setCompareCtrRate(new BigDecimal(voBuilder.getCompareCtr()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                        ctrDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareCtr()), 2, RoundingMode.HALF_UP).toString());

                                voBuilder.setCompareCvr(StringUtils.isNotBlank(compareItem.getCvr()) ? compareItem.getCvr() : "0");
                                //cvr环比值
                                BigDecimal cvrDiff = new BigDecimal(voBuilder.getCvr()).subtract(new BigDecimal(voBuilder.getCompareCvr()));
                                voBuilder.setCompareCvrRate(new BigDecimal(voBuilder.getCompareCvr()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                        cvrDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareCvr()), 2, RoundingMode.HALF_UP).toString());

                                voBuilder.setCompareAcos(StringUtils.isNotBlank(compareItem.getAcos()) ? compareItem.getAcos() : "0");
                                //Acos环比值
                                BigDecimal acosDiff = new BigDecimal(voBuilder.getAcos()).subtract(new BigDecimal(voBuilder.getCompareAcos()));
                                voBuilder.setCompareAcosRate(new BigDecimal(voBuilder.getCompareAcos()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                        acosDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAcos()), 2, RoundingMode.HALF_UP).toString());

                                voBuilder.setCompareAcots(StringUtils.isNotBlank(compareItem.getAcots()) ? compareItem.getAcots() : "0");
                                //Acots环比值
                                BigDecimal acotsDiff = new BigDecimal(voBuilder.getAcots()).subtract(new BigDecimal(voBuilder.getCompareAcots()));
                                voBuilder.setCompareAcotsRate(new BigDecimal(voBuilder.getCompareAcots()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                        acotsDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAcots()), 2, RoundingMode.HALF_UP).toString());

                                voBuilder.setCompareAsots(StringUtils.isNotBlank(compareItem.getAsots()) ? compareItem.getAsots() : "0");
                                //Asots环比值
                                BigDecimal asotsDiff = new BigDecimal(voBuilder.getAsots()).subtract(new BigDecimal(voBuilder.getCompareAsots()));
                                voBuilder.setCompareAsotsRate(new BigDecimal(voBuilder.getCompareAsots()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                        asotsDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAsots()), 2, RoundingMode.HALF_UP).toString());

                                voBuilder.setCompareAdOrderNum(Int32Value.of(Optional.ofNullable(compareItem.getAdOrderNum()).orElse(0)));
                                //AdOrderNum环比值
                                int adOrderNumDiff = voBuilder.getAdOrderNum().getValue() - voBuilder.getCompareAdOrderNum().getValue();
                                voBuilder.setCompareAdOrderNumRate(voBuilder.getCompareAdOrderNum().getValue() == 0 ? "-" :
                                        new BigDecimal(adOrderNumDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdOrderNum().getValue()),
                                                2, RoundingMode.HALF_UP).toString());

                                voBuilder.setCompareAdCost(StringUtils.isNotBlank(compareItem.getAdCost()) ? compareItem.getAdCost() : "0");
                                //AdCost环比值
                                BigDecimal adCostDiff = new BigDecimal(voBuilder.getAdCost()).subtract(new BigDecimal(voBuilder.getCompareAdCost()));
                                voBuilder.setCompareAdCostRate(new BigDecimal(voBuilder.getCompareAdCost()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                        adCostDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdCost()), 2, RoundingMode.HALF_UP).toString());

                                voBuilder.setCompareAdCostPerClick(StringUtils.isNotBlank(compareItem.getAdCostPerClick()) ? compareItem.getAdCostPerClick() : "0");
                                //AdCostPerClick环比值
                                BigDecimal adCostPerClickDiff = new BigDecimal(voBuilder.getAdCostPerClick()).subtract(new BigDecimal(voBuilder.getCompareAdCostPerClick()));
                                voBuilder.setCompareAdCostPerClickRate(new BigDecimal(voBuilder.getCompareAdCostPerClick()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                        adCostPerClickDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdCostPerClick()), 2, RoundingMode.HALF_UP).toString());

                                voBuilder.setCompareAdSale(StringUtils.isNotBlank(compareItem.getAdSale()) ? compareItem.getAdSale() : "0");
                                //AdSale环比值
                                BigDecimal adSaleDiff = new BigDecimal(voBuilder.getAdSale()).subtract(new BigDecimal(voBuilder.getCompareAdSale()));
                                voBuilder.setCompareAdSaleRate(new BigDecimal(voBuilder.getCompareAdSale()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                        adSaleDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdSale()), 2, RoundingMode.HALF_UP).toString());

                                voBuilder.setCompareRoas(StringUtils.isNotBlank(compareItem.getRoas()) ? compareItem.getRoas() : "0");
                                //Roas环比值
                                BigDecimal roasDiff = new BigDecimal(voBuilder.getRoas()).subtract(new BigDecimal(voBuilder.getCompareRoas()));
                                voBuilder.setCompareRoasRate(new BigDecimal(voBuilder.getCompareRoas()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                        roasDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareRoas()), 2, RoundingMode.HALF_UP).toString());

                                voBuilder.setCompareCpa(StringUtils.isNotBlank(compareItem.getCpa()) ? compareItem.getCpa() : "0");
                                //Cpa环比值
                                BigDecimal CpaDiff = new BigDecimal(voBuilder.getCpa()).subtract(new BigDecimal(voBuilder.getCompareCpa()));
                                voBuilder.setCompareCpaRate(new BigDecimal(voBuilder.getCompareCpa()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                        CpaDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareCpa()), 2, RoundingMode.HALF_UP).toString());

                                voBuilder.setCompareVcpm(StringUtils.isNotBlank(compareItem.getVcpm()) ? compareItem.getVcpm() : "0");
                                //Vcpm环比值
                                BigDecimal vcpmDiff = new BigDecimal(voBuilder.getVcpm()).subtract(new BigDecimal(voBuilder.getCompareVcpm()));
                                voBuilder.setCompareVcpmRate(new BigDecimal(voBuilder.getCompareVcpm()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                        vcpmDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareVcpm()), 2, RoundingMode.HALF_UP).toString());


                                voBuilder.setCompareAdSaleNum(Int32Value.of(Optional.ofNullable(compareItem.getAdSaleNum()).orElse(0)));
                                //AdSaleNum比值
                                int adSaleNumDiff = voBuilder.getAdSaleNum().getValue() - voBuilder.getCompareAdSaleNum().getValue();
                                voBuilder.setCompareAdSaleNumRate(voBuilder.getCompareAdSaleNum().getValue() == 0 ? "-" :
                                        new BigDecimal(adSaleNumDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdSaleNum().getValue()),
                                                2, RoundingMode.HALF_UP).toString());

                                voBuilder.setCompareAdOtherOrderNum(Int32Value.of(Optional.ofNullable(compareItem.getAdOtherOrderNum()).orElse(0)));
                                //AdOtherOrderNum比值
                                int adOtherOrderNumDiff = voBuilder.getAdOtherOrderNum().getValue() - voBuilder.getCompareAdOtherOrderNum().getValue();
                                voBuilder.setCompareAdOtherOrderNumRate(voBuilder.getCompareAdOtherOrderNum().getValue() == 0 ? "-" :
                                        new BigDecimal(adOtherOrderNumDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdOtherOrderNum().getValue()),
                                                2, RoundingMode.HALF_UP).toString());

                                voBuilder.setCompareAdSales(StringUtils.isNotBlank(compareItem.getAdSales()) ? compareItem.getAdSales() : "0");
                                //AdSales环比值
                                BigDecimal adSalesDiff = new BigDecimal(voBuilder.getAdSales()).subtract(new BigDecimal(voBuilder.getCompareAdSales()));
                                voBuilder.setCompareAdSalesRate(new BigDecimal(voBuilder.getCompareAdSales()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                        adSalesDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdSales()), 2, RoundingMode.HALF_UP).toString());


                                voBuilder.setCompareAdOtherSales(StringUtils.isNotBlank(compareItem.getAdOtherSales()) ? compareItem.getAdOtherSales() : "0");
                                //AdOtherSales环比值
                                BigDecimal adOtherSalesDiff = new BigDecimal(voBuilder.getAdOtherSales()).subtract(new BigDecimal(voBuilder.getCompareAdOtherSales()));
                                voBuilder.setCompareAdOtherSalesRate(new BigDecimal(voBuilder.getCompareAdOtherSales()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                        adOtherSalesDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdOtherSales()), 2, RoundingMode.HALF_UP).toString());

                                voBuilder.setCompareOrderNum(Int32Value.of(Optional.ofNullable(compareItem.getOrderNum()).orElse(0)));
                                //OrderNum比值
                                int orderNumDiff = voBuilder.getOrderNum().getValue() - voBuilder.getCompareOrderNum().getValue();
                                voBuilder.setCompareOrderNumRate(voBuilder.getCompareOrderNum().getValue() == 0 ? "-" :
                                        new BigDecimal(orderNumDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareOrderNum().getValue()),
                                                2, RoundingMode.HALF_UP).toString());

                                voBuilder.setCompareAdSelfSaleNum(Int32Value.of(Optional.ofNullable(compareItem.getAdSelfSaleNum()).orElse(0)));
                                //AdSelfSaleNum比值
                                int adSelfSaleNumDiff = voBuilder.getAdSelfSaleNum().getValue() - voBuilder.getCompareAdSelfSaleNum().getValue();
                                voBuilder.setCompareAdSelfSaleNumRate(voBuilder.getCompareAdSelfSaleNum().getValue() == 0 ? "-" :
                                        new BigDecimal(adSelfSaleNumDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdSelfSaleNum().getValue()),
                                                2, RoundingMode.HALF_UP).toString());

                                voBuilder.setCompareAdOtherSaleNum(Int32Value.of(Optional.ofNullable(compareItem.getAdOtherSaleNum()).orElse(0)));
                                //AdOtherSaleNum比值
                                int adOtherSaleNumDiff = voBuilder.getAdOtherSaleNum().getValue() - voBuilder.getCompareAdOtherSaleNum().getValue();
                                voBuilder.setCompareAdOtherSaleNumRate(voBuilder.getCompareAdOtherSaleNum().getValue() == 0 ? "-" :
                                        new BigDecimal(adOtherSaleNumDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdOtherSaleNum().getValue()),
                                                2, RoundingMode.HALF_UP).toString());

                                voBuilder.setCompareOrdersNewToBrandFTD(Int32Value.of(Optional.ofNullable(compareItem.getOrdersNewToBrandFTD()).orElse(0)));
                                //OrdersNewToBrandFTD比值
                                int ordersNewToBrandFTDDiff = voBuilder.getOrdersNewToBrandFTD().getValue() - voBuilder.getCompareOrdersNewToBrandFTD().getValue();
                                voBuilder.setCompareOrdersNewToBrandFTDRate(voBuilder.getCompareOrdersNewToBrandFTD().getValue() == 0 ? "-" :
                                        new BigDecimal(ordersNewToBrandFTDDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareOrdersNewToBrandFTD().getValue()),
                                                2, RoundingMode.HALF_UP).toString());

                                voBuilder.setCompareOrderRateNewToBrandFTD(StringUtils.isNotBlank(compareItem.getOrderRateNewToBrandFTD()) ?
                                        compareItem.getOrderRateNewToBrandFTD() : "0");
                                //OrderRateNewToBrandFTD环比值
                                BigDecimal orderRateNewToBrandFTDDiff = new BigDecimal(voBuilder.getOrderRateNewToBrandFTD()).subtract(new BigDecimal(voBuilder.getCompareOrderRateNewToBrandFTD()));
                                voBuilder.setCompareOrderRateNewToBrandFTDRate(new BigDecimal(voBuilder.getCompareOrderRateNewToBrandFTD()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                        orderRateNewToBrandFTDDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareOrderRateNewToBrandFTD()), 2, RoundingMode.HALF_UP).toString());

                                voBuilder.setCompareSalesNewToBrandFTD(StringUtils.isNotBlank(compareItem.getSalesNewToBrandFTD()) ?
                                        compareItem.getSalesNewToBrandFTD() : "0");
                                //SalesNewToBrandFTD环比值
                                BigDecimal salesNewToBrandFTDDiff = new BigDecimal(voBuilder.getSalesNewToBrandFTD()).subtract(new BigDecimal(voBuilder.getCompareSalesNewToBrandFTD()));
                                voBuilder.setCompareSalesNewToBrandFTDRate(new BigDecimal(voBuilder.getCompareSalesNewToBrandFTD()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                        salesNewToBrandFTDDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareSalesNewToBrandFTD()), 2, RoundingMode.HALF_UP).toString());

                                voBuilder.setCompareSalesRateNewToBrandFTD(StringUtils.isNotBlank(compareItem.getSalesRateNewToBrandFTD()) ?
                                        compareItem.getSalesRateNewToBrandFTD() : "0");
                                //SalesRateNewToBrandFTD环比值
                                BigDecimal salesRateNewToBrandFTDDiff = new BigDecimal(voBuilder.getSalesRateNewToBrandFTD()).subtract(new BigDecimal(voBuilder.getCompareSalesRateNewToBrandFTD()));
                                voBuilder.setCompareSalesRateNewToBrandFTDRate(new BigDecimal(voBuilder.getCompareSalesRateNewToBrandFTD()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                        salesRateNewToBrandFTDDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareSalesRateNewToBrandFTD()), 2, RoundingMode.HALF_UP).toString());

                                voBuilder.setCompareUnitsOrderedNewToBrandFTD(Int32Value.of(Optional.ofNullable(compareItem.getUnitsOrderedNewToBrandFTD()).orElse(0)));
                                //UnitsOrderedNewToBrandFTD比值
                                int unitsOrderedNewToBrandFTDDiff = voBuilder.getUnitsOrderedNewToBrandFTD().getValue() - voBuilder.getCompareUnitsOrderedNewToBrandFTD().getValue();
                                voBuilder.setCompareUnitsOrderedNewToBrandFTDRate(voBuilder.getCompareUnitsOrderedNewToBrandFTD().getValue() == 0 ? "-" :
                                        new BigDecimal(unitsOrderedNewToBrandFTDDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareUnitsOrderedNewToBrandFTD().getValue()),
                                                2, RoundingMode.HALF_UP).toString());

                                voBuilder.setCompareUnitsOrderedRateNewToBrandFTD(StringUtils.isNotBlank(compareItem.getUnitsOrderedRateNewToBrandFTD()) ?
                                        compareItem.getUnitsOrderedRateNewToBrandFTD() : "0");
                                //UnitsOrderedRateNewToBrandFTD环比值
                                BigDecimal UnitsOrderedRateNewToBrandFTDDiff = new BigDecimal(voBuilder.getUnitsOrderedRateNewToBrandFTD()).subtract(new BigDecimal(voBuilder.getCompareUnitsOrderedRateNewToBrandFTD()));
                                voBuilder.setCompareUnitsOrderedRateNewToBrandFTDRate(new BigDecimal(voBuilder.getCompareUnitsOrderedRateNewToBrandFTD()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                        UnitsOrderedRateNewToBrandFTDDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareUnitsOrderedRateNewToBrandFTD()), 2, RoundingMode.HALF_UP).toString());

                                voBuilder.setCompareAdCostPercentage(StringUtils.isNotBlank(compareItem.getAdCostPercentage()) ?
                                        compareItem.getAdCostPercentage() : "0");
                                //AdCostPercentage环比值
                                BigDecimal adCostPercentageDiff = new BigDecimal(voBuilder.getAdCostPercentage()).subtract(new BigDecimal(voBuilder.getCompareAdCostPercentage()));
                                voBuilder.setCompareAdCostPercentageRate(new BigDecimal(voBuilder.getCompareAdCostPercentage()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                        adCostPercentageDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdCostPercentage()), 2, RoundingMode.HALF_UP).toString());

                                voBuilder.setCompareAdSalePercentage(StringUtils.isNotBlank(compareItem.getAdSalePercentage()) ? compareItem.getAdSalePercentage() : "0");
                                //AdSalePercentage环比值
                                BigDecimal adSalePercentageDiff = new BigDecimal(voBuilder.getAdSalePercentage()).subtract(new BigDecimal(voBuilder.getCompareAdSalePercentage()));
                                voBuilder.setCompareAdSalePercentageRate(new BigDecimal(voBuilder.getCompareAdSalePercentage()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                        adSalePercentageDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdSalePercentage()), 2, RoundingMode.HALF_UP).toString());

                                voBuilder.setCompareAdOrderNumPercentage(StringUtils.isNotBlank(compareItem.getAdOrderNumPercentage()) ? compareItem.getAdOrderNumPercentage() : "0");
                                //AdOrderNumPercentage环比值
                                BigDecimal adOrderNumPercentageDiff = new BigDecimal(voBuilder.getAdOrderNumPercentage()).subtract(new BigDecimal(voBuilder.getCompareAdOrderNumPercentage()));
                                voBuilder.setCompareAdOrderNumPercentageRate(new BigDecimal(voBuilder.getCompareAdOrderNumPercentage()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                        adOrderNumPercentageDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdOrderNumPercentage()), 2, RoundingMode.HALF_UP).toString());

                                voBuilder.setCompareOrderNumPercentage(StringUtils.isNotBlank(compareItem.getOrderNumPercentage()) ? compareItem.getOrderNumPercentage() : "0");
                                //OrderNumPercentage环比值
                                BigDecimal orderNumPercentageDiff = new BigDecimal(voBuilder.getOrderNumPercentage()).subtract(new BigDecimal(voBuilder.getCompareOrderNumPercentage()));
                                voBuilder.setCompareOrderNumPercentageRate(new BigDecimal(voBuilder.getCompareOrderNumPercentage()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                        orderNumPercentageDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareOrderNumPercentage()), 2, RoundingMode.HALF_UP).toString());

                                voBuilder.setCompareNewToBrandDetailPageViews(Optional.ofNullable(compareItem.getNewToBrandDetailPageViews()).map(String::valueOf).orElse("0"));
                                voBuilder.setCompareNewToBrandDetailPageViewsRate(MathUtil.isNullOrZero(voBuilder.getCompareNewToBrandDetailPageViews()) ? "-" :
                                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getNewToBrandDetailPageViews(), voBuilder.getCompareNewToBrandDetailPageViews(), 4), 100)));
                                voBuilder.setCompareAddToCart(Optional.ofNullable(compareItem.getAddToCart()).map(String::valueOf).orElse("0"));
                                voBuilder.setCompareAddToCartRates(MathUtil.isNullOrZero(voBuilder.getCompareAddToCart()) ? "-" :
                                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getAddToCart(), voBuilder.getCompareAddToCart(), 4), 100)));
                                voBuilder.setCompareAddToCartRate(Optional.ofNullable(compareItem.getAddToCartRate()).map(String::valueOf).orElse("0"));
                                voBuilder.setCompareAddToCartRateRate(MathUtil.isNullOrZero(voBuilder.getCompareAddToCartRate()) ? "-" :
                                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getAddToCartRate(), voBuilder.getCompareAddToCartRate(), 4), 100)));
                                voBuilder.setCompareECPAddToCart(Optional.ofNullable(compareItem.getECPAddToCart()).map(String::valueOf).orElse("0"));
                                voBuilder.setCompareECPAddToCartRate(MathUtil.isNullOrZero(voBuilder.getCompareECPAddToCart()) ? "-" :
                                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getECPAddToCart(), voBuilder.getCompareECPAddToCart(), 4), 100)));
                                voBuilder.setCompareVideoFirstQuartileViews(Optional.ofNullable(compareItem.getVideoFirstQuartileViews()).map(String::valueOf).orElse("0"));
                                voBuilder.setCompareVideoFirstQuartileViewsRate(MathUtil.isNullOrZero(voBuilder.getCompareVideoFirstQuartileViews()) ? "-" :
                                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getVideoFirstQuartileViews(), voBuilder.getCompareVideoFirstQuartileViews(), 4), 100)));
                                voBuilder.setCompareVideoMidpointViews(Optional.ofNullable(compareItem.getVideoMidpointViews()).map(String::valueOf).orElse("0"));
                                voBuilder.setCompareVideoMidpointViewsRate(MathUtil.isNullOrZero(voBuilder.getCompareVideoMidpointViews()) ? "-" :
                                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getVideoMidpointViews(), voBuilder.getCompareVideoMidpointViews(), 4), 100)));
                                voBuilder.setCompareVideoThirdQuartileViews(Optional.ofNullable(compareItem.getVideoThirdQuartileViews()).map(String::valueOf).orElse("0"));
                                voBuilder.setCompareVideoThirdQuartileViewsRate(MathUtil.isNullOrZero(voBuilder.getCompareVideoThirdQuartileViews()) ? "-" :
                                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getVideoThirdQuartileViews(), voBuilder.getCompareVideoThirdQuartileViews(), 4), 100)));
                                voBuilder.setCompareVideoCompleteViews(Optional.ofNullable(compareItem.getVideoCompleteViews()).map(String::valueOf).orElse("0"));
                                voBuilder.setCompareVideoCompleteViewsRate(MathUtil.isNullOrZero(voBuilder.getCompareVideoCompleteViews()) ? "-" :
                                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getVideoCompleteViews(), voBuilder.getCompareVideoCompleteViews(), 4), 100)));
                                voBuilder.setCompareVideoUnmutes(Optional.ofNullable(compareItem.getVideoUnmutes()).map(String::valueOf).orElse("0"));
                                voBuilder.setCompareVideoUnmutesRate(MathUtil.isNullOrZero(voBuilder.getCompareVideoUnmutes()) ? "-" :
                                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getVideoUnmutes(), voBuilder.getCompareVideoUnmutes(), 4), 100)));
                                voBuilder.setCompareViewabilityRate(Optional.ofNullable(compareItem.getViewabilityRate()).map(String::valueOf).orElse("0"));
                                voBuilder.setCompareViewabilityRateRate(MathUtil.isNullOrZero(voBuilder.getCompareViewabilityRate()) ? "-" :
                                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getViewabilityRate(), voBuilder.getCompareViewabilityRate(), 4), 100)));
                                voBuilder.setCompareViewClickThroughRate(Optional.ofNullable(compareItem.getViewClickThroughRate()).map(String::valueOf).orElse("0"));
                                voBuilder.setCompareViewClickThroughRateRate(MathUtil.isNullOrZero(voBuilder.getCompareViewClickThroughRate()) ? "-" :
                                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getViewClickThroughRate(), voBuilder.getCompareViewClickThroughRate(), 4), 100)));
                                voBuilder.setCompareBrandedSearches(Optional.ofNullable(compareItem.getBrandedSearches()).map(String::valueOf).orElse("0"));
                                voBuilder.setCompareBrandedSearchesRate(MathUtil.isNullOrZero(voBuilder.getCompareBrandedSearches()) ? "-" :
                                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getBrandedSearches(), voBuilder.getCompareBrandedSearches(), 4), 100)));
                                voBuilder.setCompareDetailPageViews(Optional.ofNullable(compareItem.getDetailPageViews()).map(String::valueOf).orElse("0"));
                                voBuilder.setCompareDetailPageViewsRate(MathUtil.isNullOrZero(voBuilder.getCompareDetailPageViews()) ? "-" :
                                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getDetailPageViews(), voBuilder.getCompareDetailPageViews(), 4), 100)));
                                voBuilder.setCompareImpressionsFrequencyAverage(Optional.ofNullable(compareItem.getImpressionsFrequencyAverage()).map(String::valueOf).orElse("0"));
                                voBuilder.setCompareImpressionsFrequencyAverageRate(MathUtil.isNullOrZero(voBuilder.getCompareImpressionsFrequencyAverage()) ? "-" :
                                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getImpressionsFrequencyAverage(), voBuilder.getCompareImpressionsFrequencyAverage(), 4), 100)));
                                voBuilder.setCompareAdvertisingUnitPrice(Optional.ofNullable(compareItem.getAdvertisingUnitPrice()).map(String::valueOf).orElse("0"));
                                voBuilder.setCompareAdvertisingUnitPriceRate(MathUtil.isNullOrZero(voBuilder.getCompareAdvertisingUnitPrice()) ? "-" :
                                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getAdvertisingUnitPrice(), voBuilder.getCompareAdvertisingUnitPrice(), 4), 100)));
                            }
                        }
                        if (isVc) {
                            voBuilder.setAcots("-");
                            voBuilder.setCompareAcotsRate("-");
                            voBuilder.setCompareAcots("-");
                            voBuilder.setAsots("-");
                            voBuilder.setCompareAsotsRate("-");
                            voBuilder.setCompareAsots("-");
                        }
                        return voBuilder.build();
                    }).collect(Collectors.toList());
            pageBuilder.addAllRows(rpcVos);
        }
        return AllProductDataResponse.AdProductHomeVo.newBuilder()
                .setPage(pageBuilder.build())
                .build();
    }

    private void fillAdTagData(Integer puid, Integer shopId, AdProductPageParam param, List<AdProductPageVo> rows) {
        if (CollectionUtils.isEmpty(rows)) {
            return;
        }
        List<String> groups = rows.stream().map(AdProductPageVo::getAdId).distinct().collect(Collectors.toList());
        List<AdMarkupTagVo> relationVos = adMarkupTagDao.getRelationVos(puid, shopId, AdTagTypeEnum.PRODUCT.getType(), param.getType(), null, null, groups);
        if (CollectionUtils.isEmpty(relationVos)) {
            return;
        }
        List<Long> collect = relationVos.stream().map(AdMarkupTagVo::getTagIds).flatMap(Collection::stream).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(collect)) {
            return;
        }
        List<AdTag> byLongIdList = adTagDao.getListByLongIdList(puid, collect);
        if (CollectionUtils.isEmpty(byLongIdList)) {
            return;
        }
        Map<Long, AdTag> adTagMap = byLongIdList.stream().collect(Collectors.toMap(AdTag::getId, e -> e, (e1, e2) -> e2));
        Map<String, AdMarkupTagVo> adMarkupTagVoMap = relationVos.stream().collect(Collectors.toMap(AdMarkupTagVo::getRelationId, e -> e, (e1, e2) -> e2));
        for (AdProductPageVo vo : rows) {
            AdMarkupTagVo adMarkupTagVo = adMarkupTagVoMap.get(vo.getAdId());
            if (adMarkupTagVo == null) {
                continue;
            }
            List<Long> tagIds = adMarkupTagVo.getTagIds();
            if (tagIds == null) {
                continue;
            }
            List<AdTag> collect1 = tagIds.stream().map(e -> adTagMap.get(e)).collect(Collectors.toList());
            vo.setAdTags(collect1);
        }
    }

    @Override
    public AllProductAggregateDataResponse.AdProductHomeVo getAllSpProductDorisAggregateData(int puid, AdProductPageParam param) {
        ShopAuth shopAuth = shopAuthDao.getScAndVcById(param.getShopId());
        if (shopAuth == null) {
            AssertUtil.fail("店铺未授权");
        }
        boolean isVc = ShopTypeEnum.VC.getCode().equals(shopAuth.getType());
        // 取店铺销售额
        BigDecimal shopSalesByDate = CpCShopDataService.getShopSalesByDate(shopAuth.getId(), param.getStartDate(), param.getEndDate());
        if (shopSalesByDate == null) {
            shopSalesByDate = BigDecimal.ZERO;
        }
        param.setShopSales(shopSalesByDate);

        //获取不同类型数据 sp、sd
        List<AdHomePerformancedto> reportList;
        // 最新的数据，没有环比
        List<AdHomePerformancedto> latestReports = Collections.emptyList();
        //对比周期报告数据
        List<AdHomePerformancedto> reportListCompare = new ArrayList<>();
        List<AdHomePerformancedto> reportDayList = null;  //每日汇总数据
        List<String> adIdList = new ArrayList<>();

        //兼容其他接口共用该方法时，缺少searchDataType导致的npe
        int searchDataType = Optional.ofNullable(param.getSearchDataType()).orElse(SearchDataTypeEnum.ALL.getCode());
        //是否查询汇总数据
        boolean searchAggregateData = SearchDataTypeEnum.containsAggregate(searchDataType);
        //是否查询图表数据
        boolean searchChartData = SearchDataTypeEnum.containsChart(searchDataType);

        AllProductAggregateDataResponse.AdProductHomeVo.Builder builder = AllProductAggregateDataResponse.AdProductHomeVo.newBuilder();
        //勾选正在投放按钮移除其它
        if (StringUtils.isNotBlank(param.getServingStatus())) {
            List<String> list = StringUtil.splitStr(param.getServingStatus());
            if (list.contains("enabled")) {
                param.setServingStatus("enabled");
            }
        }

        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        boolean isNull = false;  // 查询的数据为空
        if (param.getAdTagId() != null) {
            List<String> relationIds = adMarkupTagDao.getRelationIds(param.getPuid(), param.getShopId(), AdTagTypeEnum.PRODUCT.getType(), param.getAdTagId(), param.getType(), null);
            if (CollectionUtils.isNotEmpty(relationIds)) {
                param.setAdIds(relationIds);
            } else {
                isNull = true;
            }
        }

        if (CollectionUtils.isNotEmpty(param.getAdTagIdList())) {
            List<String> relationIds = adMarkupTagDao.getRelationIds(param.getPuid(), param.getShopId(), AdTagTypeEnum.PRODUCT.getType(), param.getAdTagIdList(), param.getType(), null);
            if (CollectionUtils.isNotEmpty(relationIds)) {
                param.setAdIds(relationIds);
            } else {
                isNull = true;
            }
        }
        stopWatch.stop();
        log.info("getAllSpProductDorisAggregateData : adMarkupTagDao.getRelationIds time:{}", stopWatch.getLastTaskTimeMillis());

        if (isNull) {
            reportList = new ArrayList<>();
            reportDayList = new ArrayList<>();
        } else {
            stopWatch.start();
            reportList = amazonSpAdProductDorisDao.listTotalAmazonSpAdProductGroupAdId(puid, param);
            if (param.getIsCompare()) {
                reportListCompare.add(amazonSpAdProductDorisDao.listTotalAmazonSpAdProductCompareData(puid, param, param.getCompareStartDate(), param.getCompareEndDate()));
            }
            adIdList = reportList.stream().map(AdHomePerformancedto::getAdId).distinct().collect(Collectors.toList());
            //查询图表数据
            if (searchChartData) {
                if (adIdList.size() > 9000) {
                    log.info("汇总开启多线程查询");
                    reportDayList = this.getDorisThreadAdSpProductAggregate(puid, param, adIdList);
                } else {
                    reportDayList = amazonSpAdProductDorisDao.listTotalAmazonSpAdProductGroupDateById(puid, param, adIdList);
                }
            }
            stopWatch.stop();
            log.info("getAllSpProductDorisAggregateData : 查询报告数据 time:{}", stopWatch.getLastTaskTimeMillis());
        }

        if (CollectionUtils.isNotEmpty(adIdList)) {
            List<String> finalAdIdList = adIdList;
            ThreadPoolUtil.getCpcTargetAggregateIdsSyncPool().execute(() -> {
                try {
                    List<String> adId = amazonAdProductReportDao.getAdIdsByProduct(puid, param.getShopId(), shopAuth.getMarketplaceId(), LocalDate.now().minusDays(63).format(DateTimeFormatter.BASIC_ISO_DATE), finalAdIdList);
                    cpcPageIdsHandler.addIdsTemporarySynchronize(puid, adId, param.getPageSign(), "");
                } catch (Exception exception) {
                    log.error("puid： {}， shopId： {}， 过滤有效adId报错", puid, param.getShopId(), exception);
                }
            });
        } else {
            cpcPageIdsHandler.addIdsTemporarySync(puid, adIdList, param.getPageSign(), "");
        }

        BigDecimal shopSalesByDateCompare = BigDecimal.ZERO;
        if (param.getIsCompare()) {
            // 取店铺销售额
            shopSalesByDateCompare = CpCShopDataService.getShopSalesByDate(shopAuth.getId(), param.getCompareStartDate(), param.getCompareEndDate());
            if (shopSalesByDateCompare == null) {
                shopSalesByDateCompare = BigDecimal.ZERO;
            }
        }
        if (searchChartData) {
            //查询货币类型
            String currency = AmznEndpoint.getByMarketplaceId(shopAuth.getMarketplaceId()).getCurrencyCode().value();
            //获取chart数据
            List<AdHomeChartRpcVo> weekPerformanceVos = adChartDataProcess.getWeekPerformanceVos(currency, param.getStartDate(), param.getEndDate(), reportDayList, shopSalesByDate);
            List<AdHomeChartRpcVo> dayPerformanceVos = adChartDataProcess.getDayPerformanceVos(currency, reportDayList, shopSalesByDate);
            List<AdHomeChartRpcVo> monthPerformanceVos = adChartDataProcess.getMonthPerformanceVos(currency, reportDayList, shopSalesByDate);
            builder.addAllDay(dayPerformanceVos)
                    .addAllMonth(monthPerformanceVos)
                    .addAllWeek(weekPerformanceVos);
        }
        //汇总指标数据
        AdHomeAggregateDataRpcVo groupAggregateDataVo = getAdProductAggregateDataVo(reportList, reportListCompare, shopSalesByDate, shopSalesByDateCompare, latestReports, isVc);

        return builder
                .setAggregateDataVo(groupAggregateDataVo)
                .build();
    }

    /**
     * 多线程汇总查询
     */
    private List<AdHomePerformancedto> getDorisThreadAdSpProductAggregate(int puid, AdProductPageParam param, List<String> list) {
        ThreadPoolExecutor campaignReportPool = ThreadPoolUtil.getAdProductDorisPagePool();
        List<List<String>> campaignIdPartitionList = Lists.partition(list, 9000);
        List<AdHomePerformancedto> reportResult = Collections.synchronizedList(new ArrayList<>());
        try {
            List<CompletableFuture<List<AdHomePerformancedto>>> reportFutures = new ArrayList<>(campaignIdPartitionList.size());
            campaignIdPartitionList.forEach(k -> reportFutures.add(CompletableFuture.supplyAsync(() -> amazonSpAdProductDorisDao.listTotalAmazonSpAdProductGroupDateById(puid, param, k), campaignReportPool)));
            CompletableFuture<Void> all = CompletableFuture.allOf(reportFutures.toArray(new CompletableFuture[0]));
            CompletableFuture<List<List<AdHomePerformancedto>>> results = all.thenApply(v -> reportFutures.stream().map(CompletableFuture::join).collect(Collectors.toList()));
            results.get().stream().filter(Objects::nonNull).forEach(reportResult::addAll);
        } catch (Exception e) {
            log.error("query ad product report partition error", e);
            throw new BizServiceException("查询getDorisThreadAdSpProductAggregate数据异常，请联系管理员");
        }
        return reportResult;
    }

    @Override
    public AllProductAggregateDataResponse.AdProductHomeVo getAllProductAggregateData(int puid, AdProductPageParam param) {
        ShopAuth shopAuth = shopAuthDao.getScAndVcById(param.getShopId());
        if (shopAuth == null) {
            AssertUtil.fail("店铺未授权");
        }
        boolean isVc = ShopTypeEnum.VC.getCode().equals(shopAuth.getType());
        // 取店铺销售额
        BigDecimal shopSalesByDate = CpCShopDataService.getShopSalesByDate(shopAuth.getId(), param.getStartDate(), param.getEndDate());
        if (shopSalesByDate == null) {
            shopSalesByDate = BigDecimal.ZERO;
        }
        param.setShopSales(shopSalesByDate);

        //获取不同类型数据 sp、sd
        List<AdHomePerformancedto> reportList;
        // 最新的数据，没有环比
        List<AdHomePerformancedto> latestReports = Collections.emptyList();
        //对比周期报告数据
        List<AdHomePerformancedto> reportListCompare = new ArrayList<>();
        List<AdHomePerformancedto> reportDayList = null;  //每日汇总数据
        List<String> adIdList = new ArrayList<>();

        //勾选正在投放按钮移除其它
        if (StringUtils.isNotBlank(param.getServingStatus())) {
            List<String> list = StringUtil.splitStr(param.getServingStatus());
            if (list.contains("enabled")) {
                param.setServingStatus("enabled");
            }
        }

        //兼容其他接口共用该方法时，缺少searchDataType导致的npe
        int searchDataType = Optional.ofNullable(param.getSearchDataType()).orElse(SearchDataTypeEnum.ALL.getCode());
        //是否查询汇总数据
        boolean searchAggregateData = SearchDataTypeEnum.containsAggregate(searchDataType);
        //是否查询图表数据
        boolean searchChartData = SearchDataTypeEnum.containsChart(searchDataType);

        AllProductAggregateDataResponse.AdProductHomeVo.Builder builder = AllProductAggregateDataResponse.AdProductHomeVo.newBuilder();

        boolean isNull = false;  // 查询的数据为空

        if (Constants.SP.equalsIgnoreCase(param.getType())) {
            //标签筛选
            if (param.getAdTagId() != null) {
                List<String> relationIds = adMarkupTagDao.getRelationIds(param.getPuid(), param.getShopId(), AdTagTypeEnum.PRODUCT.getType(), param.getAdTagId(), param.getType(), null);
                if (CollectionUtils.isNotEmpty(relationIds)) {
                    param.setAdIds(relationIds);
                } else {
                    isNull = true;
                }
            }

            if (CollectionUtils.isNotEmpty(param.getAdTagIdList())) {
                List<String> relationIds = adMarkupTagDao.getRelationIds(param.getPuid(), param.getShopId(), AdTagTypeEnum.PRODUCT.getType(), param.getAdTagIdList(), param.getType(), null);
                if (CollectionUtils.isNotEmpty(relationIds)) {
                    param.setAdIds(relationIds);
                } else {
                    isNull = true;
                }
            }

            if (!isNull) {
                if (StringUtils.isNotBlank(param.getPortfolioId())) {  //广告组合id不为空
                    List<String> campaignIds = amazonAdCampaignDao.getCampaignIdsByPortfolioId(puid, param.getShopId(), param.getPortfolioId());
                    if (CollectionUtils.isNotEmpty(campaignIds)) {  // 用户选了广告组合又筛选了活动  从广告组合里面的活动列表在做一个过滤
                        param.setCampaignIdList(campaignIds);
                    } else {
                        isNull = true;
                    }
                }
            }

            if (isNull) {
                reportList = new ArrayList<>();
                reportDayList = new ArrayList<>();
            } else {
                reportList = amazonAdProductReportDao.getSpReportByDate(puid, param.getShopId(), param.getStartDate(), param.getEndDate(), param);
                if (param.getIsCompare()) {
                    reportListCompare = amazonAdProductReportDao.getSpReportByDate(puid, param.getShopId(), param.getCompareStartDate(), param.getCompareEndDate(), param);
                }
                //查询图表数据
                if (searchChartData) {
                    adIdList = reportList.stream().map(AdHomePerformancedto::getAdId).collect(Collectors.toList());

                    reportDayList = amazonAdProductReportDao.getSpReportByAdIdList(puid, param.getShopId(), param.getStartDate(), param.getEndDate(), adIdList);
                }
            }
        } else {
            //标签筛选
            if (param.getAdTagId() != null) {
                List<String> relationIds = adMarkupTagDao.getRelationIds(param.getPuid(), param.getShopId(), AdTagTypeEnum.PRODUCT.getType(), param.getAdTagId(), param.getType(), null);
                if (CollectionUtils.isNotEmpty(relationIds)) {
                    param.setAdIds(relationIds);
                } else {
                    isNull = true;
                }
            }

            if (CollectionUtils.isNotEmpty(param.getAdTagIdList())) {
                List<String> relationIds = adMarkupTagDao.getRelationIds(param.getPuid(), param.getShopId(), AdTagTypeEnum.PRODUCT.getType(), param.getAdTagIdList(), param.getType(), null);
                if (CollectionUtils.isNotEmpty(relationIds)) {
                    param.setAdIds(relationIds);
                } else {
                    isNull = true;
                }
            }

            if (!isNull) {
                if (StringUtils.isNotBlank(param.getPortfolioId())) {  //广告组合id不为空
                    List<String> campaignIds = amazonSdAdCampaignDao.getCampaignIdsByPortfolioId(puid, param.getShopId(), param.getPortfolioId());
                    if (CollectionUtils.isNotEmpty(campaignIds)) {  // 用户选了广告组合又筛选了活动  从广告组合里面的活动列表在做一个过滤
                        param.setCampaignIdList(campaignIds);
                    } else {
                        isNull = true;
                    }
                }
            }


            if (isNull) {
                reportList = new ArrayList<>();
                reportDayList = new ArrayList<>();
            } else {

                boolean isLatest = SqlStringReportUtil.isLatest(param.getOrderField(), param.getOrderType());

                reportList = amazonAdSdProductReportDao.getSdReportByDate(puid, param.getShopId(), shopAuth.getMarketplaceId(), param.getStartDate(), param.getEndDate(), param, isLatest);
                param.setAdIds(reportList.stream().map(AdHomePerformancedto::getAdId).collect(Collectors.toList()));
                latestReports = amazonAdSdProductReportDao.getSdReportByDate(puid, param.getShopId(), shopAuth.getMarketplaceId(), param.getStartDate(), param.getEndDate(), param, !isLatest);
                if (isLatest) {
                    List<AdHomePerformancedto> tmp = reportList;
                    reportList = latestReports;
                    latestReports = tmp;
                }
                adIdList = reportList.stream().map(AdHomePerformancedto::getAdId).collect(Collectors.toList());
                //查询图表数据
                if (searchChartData) {
                    reportDayList = amazonAdSdProductReportDao.getSdReportByAdIdList(puid, param.getShopId(), param.getStartDate(), param.getEndDate(), adIdList);
                }

            }
        }

        if (CollectionUtils.isNotEmpty(adIdList)) {
            List<String> finalAdIdList = adIdList;
            ThreadPoolUtil.getCpcTargetAggregateIdsSyncPool().execute(() -> {
                try {
                    List<String> adId = amazonAdProductReportDao.getAdIdsByProduct(puid, param.getShopId(), shopAuth.getMarketplaceId(), LocalDate.now().minusDays(63).format(DateTimeFormatter.BASIC_ISO_DATE), finalAdIdList);
                    cpcPageIdsHandler.addIdsTemporarySynchronize(puid, adId, param.getPageSign(), "");
                } catch (Exception exception) {
                    log.error("puid： {}， shopId： {}， 过滤有效adId报错", puid, param.getShopId(), exception);
                }
            });
        } else {
            cpcPageIdsHandler.addIdsTemporarySync(puid, adIdList, param.getPageSign(), "");
        }

        //环比数据
        BigDecimal shopSalesByDateCompare = BigDecimal.ZERO;
        if (param.getIsCompare()) {
            // 取店铺销售额
            shopSalesByDateCompare = CpCShopDataService.getShopSalesByDate(shopAuth.getId(), param.getCompareStartDate(), param.getCompareEndDate());
            if (shopSalesByDateCompare == null) {
                shopSalesByDateCompare = BigDecimal.ZERO;
            }
        }
        if (searchChartData) {
            //查询货币类型
            String currency = AmznEndpoint.getByMarketplaceId(shopAuth.getMarketplaceId()).getCurrencyCode().value();
            //获取chart数据
            List<AdHomeChartRpcVo> weekPerformanceVos = adChartDataProcess.getWeekPerformanceVos(currency, param.getStartDate(), param.getEndDate(), reportDayList, shopSalesByDate);
            List<AdHomeChartRpcVo> dayPerformanceVos = adChartDataProcess.getDayPerformanceVos(currency, reportDayList, shopSalesByDate);
            List<AdHomeChartRpcVo> monthPerformanceVos = adChartDataProcess.getMonthPerformanceVos(currency, reportDayList, shopSalesByDate);
            builder.addAllDay(dayPerformanceVos)
                    .addAllMonth(monthPerformanceVos)
                    .addAllWeek(weekPerformanceVos);
        }
        //汇总指标数据
        AdHomeAggregateDataRpcVo groupAggregateDataVo = getAdProductAggregateDataVo(reportList, reportListCompare, shopSalesByDate, shopSalesByDateCompare, latestReports, isVc);

        return builder
                .setAggregateDataVo(groupAggregateDataVo)
                .build();
    }

    /**
     * 填充广告信息
     *
     * @param shopAuth 门店对象
     */
    private void fillinAdInfoForVo(ShopAuth shopAuth, List<AdProductPageVo> rows, boolean isExport) {
        long startTime = System.currentTimeMillis();
        if (CollectionUtils.isNotEmpty(rows)) {

            Map<String, AmazonAdCampaignAll> spCampaignMap = null;
            Map<String, AmazonAdCampaignAll> sdCampaignMap = null;
            Map<String, AmazonAdGroup> spGroupMap = null;
            Map<String, AmazonSdAdGroup> sdGroupMap = null;
            Map<String, List<ProductAdReportVo>> asinImageMap = null;
            Map<String, List<AmazonAdProductMetadata>> metadataMap = null;
            //分组获取广告活动和广告组IDS
            Map<String, Set<String>> allTypeCamMap = rows.stream().filter(Objects::nonNull).collect(Collectors.groupingBy(AdProductPageVo::getType, Collectors.mapping(AdProductPageVo::getCampaignId, Collectors.toSet())));
            Map<String, Set<String>> allTypeGroupMap = rows.stream().filter(Objects::nonNull).collect(Collectors.groupingBy(AdProductPageVo::getType, Collectors.mapping(AdProductPageVo::getAdGroupId, Collectors.toSet())));


            //sp广告活动
            if (MapUtils.isNotEmpty(allTypeCamMap) && allTypeCamMap.containsKey(Constants.SP) && CollectionUtils.isNotEmpty(allTypeCamMap.get(Constants.SP))) {
                List<String> spCampaignIds = Arrays.asList(allTypeCamMap.get(Constants.SP).toArray(new String[]{}));
                List<AmazonAdCampaignAll> spAdCampaigns = amazonAdCampaignDao.getByCampaignIds(shopAuth.getPuid(), shopAuth.getId(), shopAuth.getMarketplaceId(), spCampaignIds);
                if (CollectionUtils.isNotEmpty(spAdCampaigns)) {
                    spCampaignMap = spAdCampaigns.stream().filter(Objects::nonNull).collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, e -> e));
                }
            }
            log.info("============================== 查询所有广告产品未分页数据 step1 ,并转vo花费时间 {} ==============================", System.currentTimeMillis() - startTime);


            //sd广告活动
            if (MapUtils.isNotEmpty(allTypeCamMap) && allTypeCamMap.containsKey(Constants.SD) && CollectionUtils.isNotEmpty(allTypeCamMap.get(Constants.SD))) {
                List<String> sdCampaignIds = Arrays.asList(allTypeCamMap.get(Constants.SD).toArray(new String[]{}));
                List<AmazonAdCampaignAll> sdCampaignList = amazonSdAdCampaignDao.getByCampaignIds(shopAuth.getPuid(), shopAuth.getId(), sdCampaignIds);
                if (CollectionUtils.isNotEmpty(sdCampaignList)) {
                    sdCampaignMap = sdCampaignList.stream().filter(Objects::nonNull).collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, e -> e));
                }
            }
            log.info("============================== 查询所有广告产品未分页数据 step2 ,并转vo花费时间 {} ==============================", System.currentTimeMillis() - startTime);


            //sp广告组
            if (MapUtils.isNotEmpty(allTypeGroupMap) && allTypeGroupMap.containsKey(Constants.SP) && CollectionUtils.isNotEmpty(allTypeGroupMap.get(Constants.SP))) {
                List<String> spGroupIds = Arrays.asList(allTypeGroupMap.get(Constants.SP).toArray(new String[]{}));
                List<AmazonAdGroup> spGroupList = amazonAdGroupDao.getAdGroupByIds(shopAuth.getPuid(), shopAuth.getId(), shopAuth.getMarketplaceId(), spGroupIds);
                if (CollectionUtils.isNotEmpty(spGroupList)) {
                    spGroupMap = spGroupList.stream().filter(Objects::nonNull).collect(Collectors.toMap(AmazonAdGroup::getAdGroupId, e -> e));
                }
            }
            log.info("============================== 查询所有广告产品未分页数据 step3 ,并转vo花费时间 {} ==============================", System.currentTimeMillis() - startTime);

            //sd广告组
            if (MapUtils.isNotEmpty(allTypeGroupMap) && allTypeGroupMap.containsKey(Constants.SD) && CollectionUtils.isNotEmpty(allTypeGroupMap.get(Constants.SD))) {
                List<String> sdGroupIds = Arrays.asList(allTypeGroupMap.get(Constants.SD).toArray(new String[]{}));
                List<AmazonSdAdGroup> sdGroupList = amazonSdAdGroupDao.getByGroupIds(shopAuth.getPuid(), shopAuth.getId(), sdGroupIds);
                if (CollectionUtils.isNotEmpty(sdGroupList)) {
                    sdGroupMap = sdGroupList.stream().filter(Objects::nonNull).collect(Collectors.toMap(AmazonSdAdGroup::getAdGroupId, e -> e));
                }
            }
            log.info("============================== 查询所有广告产品未分页数据 step4 ,并转vo花费时间 {} ==============================", System.currentTimeMillis() - startTime);


            // 导出不需要取图片需要取价格
            // 到产品表取asin图片
            List<String> skus = rows.stream().filter(Objects::nonNull).map(AdProductPageVo::getSku).collect(Collectors.toList());
            Map<String, List<ProductAdReportVo>> hashMap = new HashMap<>();
            Map<String, List<AmazonAdProductMetadata>> map = new HashMap<>();
            if (CollectionUtils.isNotEmpty(skus)) {
                List<AmazonAdProductMetadata> amazonAdProductMetadataList = amazonAdProductMetadataService.getAsinBySkus(shopAuth.getPuid(), shopAuth.getId(), skus, null);
                if (CollectionUtils.isNotEmpty(amazonAdProductMetadataList)) {
                    map.putAll(amazonAdProductMetadataList.stream().filter(Objects::nonNull).collect(Collectors.groupingBy(AmazonAdProductMetadata::getSku)));
                    metadataMap = map;
                }
                // 兼容老代码逻辑
                List<List<String>> partition = Lists.partition(skus, 100);
                for (List<String> batchSkus : partition) {
                    List<ProductAdReportVo> asinBySkus = productDao.getAsinBySkus(shopAuth.getPuid(), shopAuth.getId(), "", batchSkus);
                    if (CollectionUtils.isNotEmpty(asinBySkus)) {
                        hashMap.putAll(asinBySkus.stream().filter(Objects::nonNull).collect(Collectors.groupingBy(ProductAdReportVo::getSku)));
                    }
                }
                asinImageMap = hashMap;
            }
            log.info("============================== 查询所有广告产品未分页数据 step5 ,并转vo花费时间 {} ==============================", System.currentTimeMillis() - startTime);

            Map<String, AmazonSdAdGroup> finalSdGroupMap = sdGroupMap;
            Map<String, AmazonAdCampaignAll> finalSdCampaignMap = sdCampaignMap;
            Map<String, AmazonAdGroup> finalSpGroupMap = spGroupMap;
            Map<String, AmazonAdCampaignAll> finalSpCampaignMap = spCampaignMap;
            Map<String, List<ProductAdReportVo>> finalAsinImageMap = asinImageMap;
            Map<String, List<AmazonAdProductMetadata>> finalMetadataMap = metadataMap;

            Map<String, AmazonAdPortfolio> portfolioMap = new HashMap<>(); //广告组合信息

            rows.stream().filter(Objects::nonNull).forEach(vo -> {
                // asin图片
                vo.setAsin(vo.getAsin());
                if (MapUtils.isNotEmpty(finalMetadataMap) && finalMetadataMap.containsKey(vo.getSku())) {
                    AmazonAdProductMetadata metadata = finalMetadataMap.get(vo.getSku()).get(0);
                    vo.setTitle(metadata.getTitle());
                    vo.setImgUrl(metadata.getImageUrl());
                    if (metadata.getPriceToPayAmount() != null) {
                        vo.setPrice((metadata.getPriceToPayAmount().toString()));
                    }
                }
                if (StringUtils.isBlank(vo.getTitle()) || StringUtils.isBlank(vo.getImgUrl()) || StringUtils.isBlank(vo.getPrice())) {
                    if (MapUtils.isNotEmpty(finalAsinImageMap) && finalAsinImageMap.containsKey(vo.getSku())) {
                        ProductAdReportVo product = finalAsinImageMap.get(vo.getSku()).get(0);
                        vo.setTitle(product.getTitle());
                        vo.setImgUrl(product.getMainImage());
                        if (product.getStandardPrice() != null) {
                            vo.setPrice(product.getStandardPrice().toString());
                        }
                    }
                }

                if (Constants.SP.equalsIgnoreCase(vo.getType())) {
                    //广告信息填充
                    if (MapUtils.isNotEmpty(finalSpCampaignMap) && finalSpCampaignMap.containsKey(vo.getCampaignId())) {
                        AmazonAdCampaignAll campaign = finalSpCampaignMap.get(vo.getCampaignId());
                        vo.setCampaignName(campaign.getName());
                        vo.setCampaignTargetingType(campaign.getTargetingType());
                        vo.setCampaignState(campaign.getState());
                        if (StringUtils.isNotBlank(campaign.getPortfolioId())) {
                            vo.setPortfolioId(campaign.getPortfolioId());
                            if (portfolioMap.containsKey(campaign.getPortfolioId())) {
                                AmazonAdPortfolio amazonAdPortfolio = portfolioMap.get(campaign.getPortfolioId());
                                vo.setPortfolioName(amazonAdPortfolio.getName());
                                vo.setIsHidden(amazonAdPortfolio.getIsHidden());
                            } else {
                                AmazonAdPortfolio amazonAdPortfolio = portfolioDao.getByPortfolioId(campaign.getPuid(), campaign.getShopId(), campaign.getPortfolioId());
                                if (amazonAdPortfolio != null) {
                                    vo.setPortfolioName(amazonAdPortfolio.getName());
                                    portfolioMap.put(campaign.getPortfolioId(), amazonAdPortfolio);
                                    vo.setIsHidden(amazonAdPortfolio.getIsHidden());
                                } else {
                                    vo.setPortfolioName("广告组合待同步");
                                }
                            }
                        } else {
                            vo.setPortfolioName("-");
                        }

                    }
                    //广告组名称填充
                    if (MapUtils.isNotEmpty(finalSpGroupMap) && finalSpGroupMap.containsKey(vo.getAdGroupId())) {
                        vo.setAdGroupName(finalSpGroupMap.get(vo.getAdGroupId()).getName());
                        vo.setAdGroupType(finalSpGroupMap.get(vo.getAdGroupId()).getAdGroupType());
                        vo.setAdGroupState(finalSpGroupMap.get(vo.getAdGroupId()).getState());
                    }

                }

                if (Constants.SD.equalsIgnoreCase(vo.getType())) {
                    //广告信息填充
                    if (MapUtils.isNotEmpty(finalSdCampaignMap) && finalSdCampaignMap.containsKey(vo.getCampaignId())) {
                        AmazonAdCampaignAll amazonSdAdCampaign = finalSdCampaignMap.get(vo.getCampaignId());

                        vo.setCampaignName(amazonSdAdCampaign.getName());
                        vo.setCampaignTargetingType(amazonSdAdCampaign.getTactic());
                        vo.setCostType(amazonSdAdCampaign.getCostType());
                        vo.setCampaignState(amazonSdAdCampaign.getState());
                        if (StringUtils.isNotBlank(amazonSdAdCampaign.getPortfolioId())) {
                            vo.setPortfolioId(amazonSdAdCampaign.getPortfolioId());
                            if (portfolioMap.containsKey(amazonSdAdCampaign.getPortfolioId())) {
                                AmazonAdPortfolio amazonAdPortfolio = portfolioMap.get(amazonSdAdCampaign.getPortfolioId());
                                vo.setPortfolioName(amazonAdPortfolio.getName());
                                vo.setIsHidden(amazonAdPortfolio.getIsHidden());
                            } else {
                                AmazonAdPortfolio amazonAdPortfolio = portfolioDao.getByPortfolioId(amazonSdAdCampaign.getPuid(), amazonSdAdCampaign.getShopId(), amazonSdAdCampaign.getPortfolioId());
                                if (amazonAdPortfolio != null) {
                                    vo.setPortfolioName(amazonAdPortfolio.getName());
                                    portfolioMap.put(amazonSdAdCampaign.getPortfolioId(), amazonAdPortfolio);
                                    vo.setIsHidden(amazonAdPortfolio.getIsHidden());
                                } else {
                                    vo.setPortfolioName("广告组合待同步");
                                }
                            }
                        } else {
                            vo.setPortfolioName("-");
                        }
                    }
                    //广告组名称填充
                    if (MapUtils.isNotEmpty(finalSdGroupMap) && finalSdGroupMap.containsKey(vo.getAdGroupId())) {
                        vo.setAdGroupName(finalSdGroupMap.get(vo.getAdGroupId()).getName());
                        vo.setAdGroupState(finalSdGroupMap.get(vo.getAdGroupId()).getState());
                    }
                }
            });
        }
    }


    /**
     * 处理统计信息
     *
     * @param rows
     * @return
     */
    private AdHomeAggregateDataRpcVo getAdProductAggregateDataVo(List<AdHomePerformancedto> rows, List<AdHomePerformancedto> rowsCompare, BigDecimal shopSales, BigDecimal shopSalesCompare, List<AdHomePerformancedto> latestReports, boolean isVc) {

        //点击量
        int sumClicks = rows.stream().filter(item -> item != null && item.getClicks() != null).mapToInt(AdHomePerformancedto::getClicks).sum();
        //广告订单数
        int sumAdOrderNum = rows.stream().filter(item -> item != null && item.getAdOrderNum() != null).mapToInt(AdHomePerformancedto::getAdOrderNum).sum();
        //曝光量
        int sumImpressions = rows.stream().filter(item -> item != null && item.getImpressions() != null).mapToInt(AdHomePerformancedto::getImpressions).sum();
        //广告销售额
        BigDecimal sumAdSale = rows.stream().filter(item -> item != null && item.getAdSale() != null).map(e -> e.getAdSale()).reduce(BigDecimal.ZERO, BigDecimal::add);
        //广告花费
        BigDecimal sumAdcost = rows.stream().filter(item -> item != null && item.getAdCost() != null).map(e -> e.getAdCost()).reduce(BigDecimal.ZERO, BigDecimal::add);
        /**
         * TODO 广告报告重构
         * 可见展示次数(VCPM专用)
         * 兼容 sb
         */
        int sumViewImpressions = rows.stream()
                .filter(Objects::nonNull)
                .map(e -> CampaignTypeEnum.sb.getCampaignType().equals(e.getType()) ? e.getViewableImpressions() : e.getViewImpressions())
                .filter(Objects::nonNull)
                .reduce(0, Integer::sum);
        //本广告产品订单量
        int sumAdSaleNum = rows.stream().filter(item -> item != null && item.getAdSaleNum() != null).mapToInt(AdHomePerformancedto::getAdSaleNum).sum();
        //本广告产品销售额
        BigDecimal sumAdSales = rows.stream().filter(item -> item != null && item.getAdSales() != null).map(e -> e.getAdSales()).reduce(BigDecimal.ZERO, BigDecimal::add);
        //CPC,VCPM广告销量
        int sumSalesNum = rows.stream().filter(item -> item != null && item.getSalesNum() != null).mapToInt(AdHomePerformancedto::getSalesNum).sum();
        //CPC,VCPM-“品牌新买家”订单量
        int sumOrdersNewToBrand14d = rows.stream().filter(item -> item != null && item.getOrdersNewToBrand14d() != null).mapToInt(AdHomePerformancedto::getOrdersNewToBrand14d).sum();
        //CPC,VCPM-“品牌新买家”销售额
        BigDecimal sumSalesNewToBrand14d = rows.stream().filter(item -> item != null && item.getSalesNewToBrand14d() != null).map(e -> e.getSalesNewToBrand14d()).reduce(BigDecimal.ZERO, BigDecimal::add);
        //CPC,VCPM-“品牌新买家”销量
        int sumUnitsOrderedNewToBrand14d = rows.stream().filter(item -> item != null && item.getUnitsOrderedNewToBrand14d() != null).mapToInt(AdHomePerformancedto::getUnitsOrderedNewToBrand14d).sum();
        //每笔订单花费
        BigDecimal sumCpa = sumAdOrderNum == 0 ? BigDecimal.ZERO : sumAdcost.divide(new BigDecimal(sumAdOrderNum), 2, BigDecimal.ROUND_HALF_UP);
        //vcpm
        //BigDecimal sumVcpm = sumViewImpressions == 0 ? BigDecimal.ZERO : sumAdcost.divide(new BigDecimal(sumViewImpressions).multiply(new BigDecimal("1000")), 2, BigDecimal.ROUND_HALF_UP);
        BigDecimal sumVcpm = sumViewImpressions == 0 ? BigDecimal.ZERO : MathUtil.multiplyZero(MathUtil.divideForScale(sumAdcost, new BigDecimal(sumViewImpressions), 6), BigDecimal.valueOf(1000));

        //其他产品广告订单量
        int sumAdOtherOrderNum = sumAdOrderNum - sumAdSaleNum;
        //其他产品广告销售额
        BigDecimal sumAdOtherSales = sumAdSale.subtract(sumAdSales);
        //本广告产品销量
        int sumOrderNum = rows.stream().filter(item -> item != null && item.getOrderNum() != null).mapToInt(AdHomePerformancedto::getOrderNum).sum();
        //其他产品广告销量
        int sumAdOtherSaleNum = sumSalesNum - sumOrderNum;
        //“品牌新买家”订单百分比
        BigDecimal sumOrderRateNewToBrand14d = sumAdOrderNum == 0 ? BigDecimal.ZERO : new BigDecimal(sumOrdersNewToBrand14d).multiply(new BigDecimal("100")).divide(new BigDecimal(sumAdOrderNum), 2, BigDecimal.ROUND_HALF_UP);
        //“品牌新买家”销售额百分比
        BigDecimal sumSalesRateNewToBrand14d = sumAdSale.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : sumSalesNewToBrand14d.multiply(new BigDecimal("100")).divide(sumAdSale, 2, BigDecimal.ROUND_HALF_UP);
        //“品牌新买家”销量百分比
        BigDecimal sumUnitsOrderedRateNewToBrand14d = sumSalesNum == 0 ? BigDecimal.ZERO : new BigDecimal(sumUnitsOrderedNewToBrand14d).multiply(new BigDecimal("100")).divide(new BigDecimal(sumSalesNum), 2, BigDecimal.ROUND_HALF_UP);
        //ACoS
        BigDecimal sumAcos = sumAdSale.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : sumAdcost.multiply(new BigDecimal("100")).divide(sumAdSale, 2, BigDecimal.ROUND_HALF_UP);
        //平均点击费
        BigDecimal sumAdCostPerClick = sumClicks == 0 ? BigDecimal.ZERO : sumAdcost.divide(new BigDecimal(sumClicks), 2, BigDecimal.ROUND_HALF_UP);
        //订单转化率
        BigDecimal sumCVr = sumClicks == 0 ? BigDecimal.ZERO : new BigDecimal(sumAdOrderNum).multiply(new BigDecimal("100")).divide(new BigDecimal(sumClicks), 2, BigDecimal.ROUND_HALF_UP);
        //点击率（CTR）
        BigDecimal sumCtr = sumImpressions == 0 ? BigDecimal.ZERO : new BigDecimal(sumClicks).multiply(new BigDecimal("100")).divide(new BigDecimal(sumImpressions), 2, BigDecimal.ROUND_HALF_UP);
        //roas
        BigDecimal roas = sumAdcost.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : sumAdSale.divide(sumAdcost, 2, BigDecimal.ROUND_HALF_UP);
        //acots
        BigDecimal acots = shopSales.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : sumAdcost.multiply(new BigDecimal("100")).divide(shopSales, 2, BigDecimal.ROUND_HALF_UP);//asots
        BigDecimal asots = shopSales.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : sumAdSale.multiply(new BigDecimal("100")).divide(shopSales, 2, BigDecimal.ROUND_HALF_UP);
        String sumAdCostPercentage = sumAdcost.compareTo(BigDecimal.ZERO) == 0 ? "0" : "100.0000";
        String sumAdSalePercentage = sumAdSale.compareTo(BigDecimal.ZERO) == 0 ? "0" : "100.0000";
        String sumAdOrderNumPercentage = sumAdOrderNum == 0 ? "0" : "100.0000";
        String sumOrderNumPercentage = sumSalesNum == 0 ? "0" : "100.0000";

        // “品牌新买家”观看量
        int sumNewToBrandDetailPageViews = rows.stream().filter(Objects::nonNull).map(AdHomePerformancedto::getNewToBrandDetailPageViews).filter(Objects::nonNull).reduce(0, Integer::sum);
        // 加购次数
        int sumAddToCart = rows.stream().filter(Objects::nonNull).map(AdHomePerformancedto::getAddToCart).filter(Objects::nonNull).reduce(0, Integer::sum);
        // 加购率
        BigDecimal sumAddToCartRate = sumImpressions == 0 ? BigDecimal.ZERO : MathUtil.divideOfObject(MathUtil.multiplyOfObject(sumAddToCart, 100), sumImpressions, 2);
        // 单次加购花费
        BigDecimal sumECPAddToCart = sumAddToCart == 0 ? BigDecimal.ZERO : MathUtil.divideOfObject(sumAdcost, sumAddToCart, 2);
        // 视频播至1/4次数
        int sumVideoFirstQuartileViews = rows.stream().filter(Objects::nonNull).map(AdHomePerformancedto::getVideoFirstQuartileViews).filter(Objects::nonNull).reduce(0, Integer::sum);
        // 视频播至1/2次数
        int sumVideoMidpointViews = rows.stream().filter(Objects::nonNull).map(AdHomePerformancedto::getVideoMidpointViews).filter(Objects::nonNull).reduce(0, Integer::sum);
        // 视频播至3/4次数
        int sumVideoThirdQuartileViews = rows.stream().filter(Objects::nonNull).map(AdHomePerformancedto::getVideoThirdQuartileViews).filter(Objects::nonNull).reduce(0, Integer::sum);
        // 视频完整播放次数
        int sumVideoCompleteViews = rows.stream().filter(Objects::nonNull).map(AdHomePerformancedto::getVideoCompleteViews).filter(Objects::nonNull).reduce(0, Integer::sum);
        // 视频取消静音
        int sumVideoUnmutes = rows.stream().filter(Objects::nonNull).map(AdHomePerformancedto::getVideoUnmutes).filter(Objects::nonNull).reduce(0, Integer::sum);
        // 观看率
        BigDecimal sumViewabilityRate = sumImpressions == 0 ? BigDecimal.ZERO : MathUtil.divideOfObject(MathUtil.multiplyOfObject(sumViewImpressions, 100), sumImpressions, 2);
        // 观看点击率
        BigDecimal sumViewClickThroughRate = sumViewImpressions == 0 ? BigDecimal.ZERO : MathUtil.divideOfObject(MathUtil.multiplyOfObject(sumClicks, 100), sumViewImpressions, 2);
        // 品牌搜索次数
        int sumBrandedSearches = rows.stream().filter(Objects::nonNull).map(AdHomePerformancedto::getBrandedSearches).filter(Objects::nonNull).reduce(0, Integer::sum);
        // DPV
        int sumDetailPageViews = rows.stream().filter(Objects::nonNull).map(AdHomePerformancedto::getDetailPageViews).filter(Objects::nonNull).reduce(0, Integer::sum);
        // 累计触达用户
        int sumCumulativeReach = latestReports.stream().filter(Objects::nonNull).map(AdHomePerformancedto::getCumulativeReach).filter(Objects::nonNull).reduce(0, Integer::sum);
        // 平均触达次数
        BigDecimal sumImpressionsFrequencyAverage = latestReports.stream().filter(Objects::nonNull).map(AdHomePerformancedto::getImpressionsFrequencyAverage).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
        // 广告笔单价,广告销售额/广告订单量*100%
        BigDecimal sumAdvertisingUnitPrice = sumAdOrderNum == 0 ? BigDecimal.ZERO : MathUtil.divideOfObject(sumAdSale, sumAdOrderNum, 2);

        //环比数据
        //点击量
        int sumClicksCompare = rowsCompare.stream().filter(item -> item != null && item.getClicks() != null).mapToInt(AdHomePerformancedto::getClicks).sum();
        //广告订单数
        int sumAdOrderNumCompare = rowsCompare.stream().filter(item -> item != null && item.getAdOrderNum() != null).mapToInt(AdHomePerformancedto::getAdOrderNum).sum();
        //曝光量
        int sumImpressionsCompare = rowsCompare.stream().filter(item -> item != null && item.getImpressions() != null).mapToInt(AdHomePerformancedto::getImpressions).sum();
        //广告销售额
        BigDecimal sumAdSaleCompare = rowsCompare.stream().filter(item -> item != null && item.getAdSale() != null).map(e -> e.getAdSale()).reduce(BigDecimal.ZERO, BigDecimal::add);
        //广告花费
        BigDecimal sumAdcostCompare = rowsCompare.stream().filter(item -> item != null && item.getAdCost() != null).map(e -> e.getAdCost()).reduce(BigDecimal.ZERO, BigDecimal::add);

        //ACoS
        BigDecimal sumAcosCompare = sumAdSaleCompare.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : sumAdcostCompare.multiply(new BigDecimal("100")).divide(sumAdSaleCompare, 2, BigDecimal.ROUND_HALF_UP);
        //平均点击费
        BigDecimal sumAdCostPerClickCompare = sumClicksCompare == 0 ? BigDecimal.ZERO : sumAdcostCompare.divide(new BigDecimal(sumClicksCompare), 2, BigDecimal.ROUND_HALF_UP);
        //订单转化率
        BigDecimal sumCvrCompare = sumClicksCompare == 0 ? BigDecimal.ZERO : new BigDecimal(sumAdOrderNumCompare).multiply(new BigDecimal("100")).divide(new BigDecimal(sumClicksCompare), 2, BigDecimal.ROUND_HALF_UP);
        //点击率（CTR）
        BigDecimal sumCtrCompare = sumImpressionsCompare == 0 ? BigDecimal.ZERO : new BigDecimal(sumClicksCompare).multiply(new BigDecimal("100")).divide(new BigDecimal(sumImpressionsCompare), 2, BigDecimal.ROUND_HALF_UP);
        //roas
        BigDecimal roasCompare = sumAdcostCompare.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : sumAdSaleCompare.divide(sumAdcostCompare, 2, BigDecimal.ROUND_HALF_UP);
        //acots
        BigDecimal acotsCompare = shopSalesCompare.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : sumAdcostCompare.multiply(new BigDecimal("100")).divide(shopSalesCompare, 2, BigDecimal.ROUND_HALF_UP);
        //asots
        BigDecimal asotsCompare = shopSalesCompare.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : sumAdSaleCompare.multiply(new BigDecimal("100")).divide(shopSalesCompare, 2, BigDecimal.ROUND_HALF_UP);
        //可见展示次数
        int sumViewImpressionsCompare = rowsCompare.stream()
                .filter(Objects::nonNull)
                .map(e -> CampaignTypeEnum.sb.getCampaignType().equals(e.getType()) ? e.getViewableImpressions() : e.getViewImpressions())
                .filter(Objects::nonNull)
                .reduce(0, Integer::sum);
        // “品牌新买家”观看量
        int sumNewToBrandDetailPageViewsCompare = rowsCompare.stream().filter(Objects::nonNull).map(AdHomePerformancedto::getNewToBrandDetailPageViews).filter(Objects::nonNull).reduce(0, Integer::sum);
        // 加购次数
        int sumAddToCartCompare = rowsCompare.stream().filter(Objects::nonNull).map(AdHomePerformancedto::getAddToCart).filter(Objects::nonNull).reduce(0, Integer::sum);
        // 加购率
        BigDecimal sumAddToCartRateCompare = sumImpressionsCompare == 0 ? BigDecimal.ZERO : MathUtil.divideOfObject(MathUtil.multiplyOfObject(sumAddToCartCompare, 100), sumImpressionsCompare, 2);
        // 单次加购花费
        BigDecimal sumECPAddToCartCompare = sumAddToCartCompare == 0 ? BigDecimal.ZERO : MathUtil.divideOfObject(sumAdcostCompare, sumAddToCartCompare, 2);
        // 视频播至1/4次数
        int sumVideoFirstQuartileViewsCompare = rowsCompare.stream().filter(Objects::nonNull).map(AdHomePerformancedto::getVideoFirstQuartileViews).filter(Objects::nonNull).reduce(0, Integer::sum);
        // 视频播至1/2次数
        int sumVideoMidpointViewsCompare = rowsCompare.stream().filter(Objects::nonNull).map(AdHomePerformancedto::getVideoMidpointViews).filter(Objects::nonNull).reduce(0, Integer::sum);
        // 视频播至3/4次数
        int sumVideoThirdQuartileViewsCompare = rowsCompare.stream().filter(Objects::nonNull).map(AdHomePerformancedto::getVideoThirdQuartileViews).filter(Objects::nonNull).reduce(0, Integer::sum);
        // 视频完整播放次数
        int sumVideoCompleteViewsCompare = rowsCompare.stream().filter(Objects::nonNull).map(AdHomePerformancedto::getVideoCompleteViews).filter(Objects::nonNull).reduce(0, Integer::sum);
        // 视频取消静音
        int sumVideoUnmutesCompare = rowsCompare.stream().filter(Objects::nonNull).map(AdHomePerformancedto::getVideoUnmutes).filter(Objects::nonNull).reduce(0, Integer::sum);
        // 观看率
        BigDecimal sumViewabilityRateCompare = sumImpressionsCompare == 0 ? BigDecimal.ZERO : MathUtil.divideOfObject(MathUtil.multiplyOfObject(sumViewImpressionsCompare, 100), sumImpressionsCompare, 2);
        // 观看点击率
        BigDecimal sumViewClickThroughRateCompare = sumViewImpressionsCompare == 0 ? BigDecimal.ZERO : MathUtil.divideOfObject(MathUtil.multiplyOfObject(sumClicksCompare, 100), sumViewImpressionsCompare, 2);
        // 品牌搜索次数
        int sumBrandedSearchesCompare = rowsCompare.stream().filter(Objects::nonNull).map(AdHomePerformancedto::getBrandedSearches).filter(Objects::nonNull).reduce(0, Integer::sum);
        // DPV
        int sumDetailPageViewsCompare = rowsCompare.stream().filter(Objects::nonNull).map(AdHomePerformancedto::getDetailPageViews).filter(Objects::nonNull).reduce(0, Integer::sum);
        // 广告笔单价,广告销售额/广告订单量*100%
        BigDecimal sumAdvertisingUnitPriceCompare = sumAdOrderNumCompare == 0 ? BigDecimal.ZERO : MathUtil.divideOfObject(sumAdSaleCompare, sumAdOrderNumCompare, 2);
        //vcpm
        BigDecimal sumVcpmCompare = sumViewImpressionsCompare == 0 ? BigDecimal.ZERO : MathUtil.multiplyZero(MathUtil.divideForScale(sumAdcostCompare, new BigDecimal(sumViewImpressionsCompare), 6), BigDecimal.valueOf(1000));
        //每笔订单花费
        BigDecimal sumCpaCompare = sumAdOrderNumCompare == 0 ? BigDecimal.ZERO : sumAdcostCompare.divide(new BigDecimal(sumAdOrderNumCompare), 2, RoundingMode.HALF_UP);
        //广告销量
        int sumSalesNumCompare = rowsCompare.stream().filter(item -> item != null && item.getSalesNum() != null).mapToInt(AdHomePerformancedto::getSalesNum).sum();
        //本广告产品订单量
        int sumAdSaleNumCompare = rowsCompare.stream().filter(item -> item != null && item.getAdSaleNum() != null).mapToInt(AdHomePerformancedto::getAdSaleNum).sum();
        //其他产品广告订单量
        int sumAdOtherOrderNumCompare = sumAdOrderNumCompare - sumAdSaleNumCompare;
        //本广告产品销售额
        BigDecimal sumAdSalesCompare = rowsCompare.stream().filter(item -> item != null && item.getAdSales() != null).map(AdHomePerformancedto::getAdSales).reduce(BigDecimal.ZERO, BigDecimal::add);
        //其他产品广告销售额
        BigDecimal sumAdOtherSalesCompare = sumAdSaleCompare.subtract(sumAdSalesCompare);
        //本广告产品销量
        int sumOrderNumCompare = rowsCompare.stream().filter(item -> item != null && item.getOrderNum() != null).mapToInt(AdHomePerformancedto::getOrderNum).sum();
        //其他产品广告销量
        int sumAdOtherSaleNumCompare = sumSalesNumCompare - sumOrderNumCompare;
        //“品牌新买家”订单量
        int sumOrdersNewToBrand14dCompare = rowsCompare.stream().filter(item -> item != null && item.getOrdersNewToBrand14d() != null).mapToInt(AdHomePerformancedto::getOrdersNewToBrand14d).sum();
        //“品牌新买家”销售额
        BigDecimal sumSalesNewToBrand14dCompare = rowsCompare.stream().filter(item -> item != null && item.getSalesNewToBrand14d() != null).map(AdHomePerformancedto::getSalesNewToBrand14d).reduce(BigDecimal.ZERO, BigDecimal::add);
        //“品牌新买家”销量
        int sumUnitsOrderedNewToBrand14dCompare = rowsCompare.stream().filter(item -> item != null && item.getUnitsOrderedNewToBrand14d() != null).mapToInt(AdHomePerformancedto::getUnitsOrderedNewToBrand14d).sum();
        //“品牌新买家”订单百分比
        BigDecimal sumOrderRateNewToBrand14dCompare = sumAdOrderNumCompare == 0 ? BigDecimal.ZERO : new BigDecimal(sumOrdersNewToBrand14dCompare).multiply(new BigDecimal("100")).divide(new BigDecimal(sumAdOrderNumCompare), 2 , RoundingMode.HALF_UP);
        //“品牌新买家”销售额百分比
        BigDecimal sumSalesRateNewToBrand14dCompare = sumAdSaleCompare.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : sumSalesNewToBrand14dCompare.multiply(new BigDecimal("100")).divide(sumAdSaleCompare, 2, RoundingMode.HALF_UP);
        //“品牌新买家”销量百分比
        BigDecimal sumUnitsOrderedRateNewToBrand14dCompare = sumSalesNumCompare == 0 ? BigDecimal.ZERO : new BigDecimal(sumUnitsOrderedNewToBrand14dCompare).multiply(new BigDecimal("100")).divide(new BigDecimal(sumSalesNumCompare), 2, RoundingMode.HALF_UP);


        AdHomeAggregateDataRpcVo.Builder builder = AdHomeAggregateDataRpcVo.newBuilder()
                .setAcos(sumAcos.stripTrailingZeros().toString())
                .setAcots(acots.stripTrailingZeros().toString())
                .setAsots(asots.stripTrailingZeros().toString())
                .setRoas(roas.stripTrailingZeros().toString())
                .setAdCost(sumAdcost.stripTrailingZeros().toString())
                .setAdCostPerClick(sumAdCostPerClick.stripTrailingZeros().toString())
                .setAdOrderNum(Int32Value.of(sumAdOrderNum))
                .setCvr(sumCVr.stripTrailingZeros().toString())
                .setCtr(sumCtr.stripTrailingZeros().toString())
                .setAdSale(sumAdSale.stripTrailingZeros().toString())
                .setClicks(Int32Value.of(sumClicks))
                .setImpressions(Int32Value.of(sumImpressions))
                .setViewImpressions(Int32Value.of(sumViewImpressions))
                .setCpa(sumCpa.toString())
                .setVcpm(sumVcpm.toString())
                .setAdSaleNum(Int32Value.of(sumAdSaleNum))
                .setAdOtherOrderNum(Int32Value.of(sumAdOtherOrderNum))
                .setAdSales(sumAdSales.toString())
                .setAdOtherSales(sumAdOtherSales.toString())
                .setOrderNum(Int32Value.of(sumSalesNum))
                .setAdSelfSaleNum(Int32Value.of(sumOrderNum))
                .setAdOtherSaleNum(Int32Value.of(sumAdOtherSaleNum))
                .setOrdersNewToBrandFTD(Int32Value.of(sumOrdersNewToBrand14d))
                .setOrderRateNewToBrandFTD(sumOrderRateNewToBrand14d.toString())
                .setSalesNewToBrandFTD(sumSalesNewToBrand14d.toString())
                .setSalesRateNewToBrandFTD(sumSalesRateNewToBrand14d.toString())
                .setUnitsOrderedNewToBrandFTD(Int32Value.of(sumUnitsOrderedNewToBrand14d))
                .setUnitsOrderedRateNewToBrandFTD(sumUnitsOrderedRateNewToBrand14d.toString())
                .setAdCostPercentage(sumAdCostPercentage)
                .setAdSalePercentage(sumAdSalePercentage)
                .setAdOrderNumPercentage(sumAdOrderNumPercentage)
                .setOrderNumPercentage(sumOrderNumPercentage)
                .setNewToBrandDetailPageViews(String.valueOf(sumNewToBrandDetailPageViews))
                .setAddToCart(String.valueOf(sumAddToCart))
                .setAddToCartRate(String.valueOf(sumAddToCartRate))
                .setECPAddToCart(String.valueOf(sumECPAddToCart))
                .setVideoFirstQuartileViews(String.valueOf(sumVideoFirstQuartileViews))
                .setVideoMidpointViews(String.valueOf(sumVideoMidpointViews))
                .setVideoThirdQuartileViews(String.valueOf(sumVideoThirdQuartileViews))
                .setVideoCompleteViews(String.valueOf(sumVideoCompleteViews))
                .setVideoUnmutes(String.valueOf(sumVideoUnmutes))
                .setViewabilityRate(String.valueOf(sumViewabilityRate))
                .setViewClickThroughRate(String.valueOf(sumViewClickThroughRate))
                .setBrandedSearches(String.valueOf(sumBrandedSearches))
                .setDetailPageViews(String.valueOf(sumDetailPageViews))
                .setCumulativeReach(String.valueOf(sumCumulativeReach))
                .setImpressionsFrequencyAverage(String.valueOf(sumImpressionsFrequencyAverage))
                .setAdvertisingUnitPrice(String.valueOf(sumAdvertisingUnitPrice))

                //环比数据
                .setCompareAcos(sumAcosCompare.toPlainString())
                .setCompareRoas(roasCompare.toPlainString())
                .setCompareAdCost(sumAdcostCompare.toPlainString())
                .setCompareAdCostPerClick(sumAdCostPerClickCompare.toPlainString())
                .setCompareAdOrderNum(Int32Value.of(sumAdOrderNumCompare))
                .setCompareCvr(sumCvrCompare.toPlainString())
                .setCompareCtr(sumCtrCompare.toPlainString())
                .setCompareAdSale(sumAdSaleCompare.toPlainString())
                .setCompareClicks(Int32Value.of(sumClicksCompare))
                .setCompareImpressions(Int32Value.of(sumImpressionsCompare))
                .setCompareAcots(acotsCompare.toPlainString())
                .setCompareAsots(asotsCompare.toPlainString())
                .setCompareNewToBrandDetailPageViews(String.valueOf(sumNewToBrandDetailPageViewsCompare))
                .setCompareAddToCart(String.valueOf(sumAddToCartCompare))
                .setCompareAddToCartRate(String.valueOf(sumAddToCartRateCompare))
                .setCompareECPAddToCart(String.valueOf(sumECPAddToCartCompare))
                .setCompareVideoFirstQuartileViews(String.valueOf(sumVideoFirstQuartileViewsCompare))
                .setCompareVideoMidpointViews(String.valueOf(sumVideoMidpointViewsCompare))
                .setCompareVideoThirdQuartileViews(String.valueOf(sumVideoThirdQuartileViewsCompare))
                .setCompareVideoCompleteViews(String.valueOf(sumVideoCompleteViewsCompare))
                .setCompareVideoUnmutes(String.valueOf(sumVideoUnmutesCompare))
                .setCompareViewabilityRate(String.valueOf(sumViewabilityRateCompare))
                .setCompareViewClickThroughRate(String.valueOf(sumViewClickThroughRateCompare))
                .setCompareBrandedSearches(String.valueOf(sumBrandedSearchesCompare))
                .setCompareDetailPageViews(String.valueOf(sumDetailPageViewsCompare))
                .setCompareAdvertisingUnitPrice(String.valueOf(sumAdvertisingUnitPriceCompare))
                .setCompareViewImpressions(Int32Value.of(sumViewImpressionsCompare))
                .setCompareVcpm(sumVcpmCompare.toPlainString())
                .setCompareCpa(sumCpaCompare.toPlainString())
                .setCompareOrderNum(Int32Value.of(sumSalesNumCompare))
                .setCompareAdSaleNum(Int32Value.of(sumAdSaleNumCompare))
                .setCompareAdOtherOrderNum(Int32Value.of(sumAdOtherOrderNumCompare))
                .setCompareAdSales(sumAdSalesCompare.toPlainString())
                .setCompareAdOtherSales(sumAdOtherSalesCompare.toPlainString())
                .setCompareAdSelfSaleNum(Int32Value.of(sumOrderNumCompare))
                .setCompareAdOtherSaleNum(Int32Value.of(sumAdOtherSaleNumCompare))
                .setCompareOrdersNewToBrandFTD(Int32Value.of(sumOrdersNewToBrand14dCompare))
                .setCompareSalesNewToBrandFTD(sumSalesNewToBrand14dCompare.toPlainString())
                .setCompareUnitsOrderedNewToBrandFTD(Int32Value.of(sumUnitsOrderedNewToBrand14dCompare))
                .setCompareOrderRateNewToBrandFTD(sumOrderRateNewToBrand14dCompare.toPlainString())
                .setCompareSalesRateNewToBrandFTD(sumSalesRateNewToBrand14dCompare.toPlainString())
                .setCompareUnitsOrderedRateNewToBrandFTD(sumUnitsOrderedRateNewToBrand14dCompare.toPlainString())

                //环比值
                .setCompareAcosRate(sumAcosCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" : (sumAcos.subtract(sumAcosCompare))
                        .multiply(new BigDecimal(100)).divide(sumAcosCompare, 2, RoundingMode.HALF_UP).toPlainString())

                .setCompareRoasRate(roasCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" : (roas.subtract(roasCompare))
                        .multiply(new BigDecimal(100)).divide(roasCompare, 2, RoundingMode.HALF_UP).toPlainString())

                .setCompareAdCostRate(sumAdcostCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" : (sumAdcost.subtract(sumAdcostCompare))
                        .multiply(new BigDecimal(100)).divide(sumAdcostCompare, 2, RoundingMode.HALF_UP).toPlainString())

                .setCompareAdCostPerClickRate(sumAdCostPerClickCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" : (sumAdCostPerClick.subtract(sumAdCostPerClickCompare))
                        .multiply(new BigDecimal(100)).divide(sumAdCostPerClickCompare, 2, RoundingMode.HALF_UP).toPlainString())

                .setCompareAdOrderNumRate(sumAdOrderNumCompare == 0 ? "-" : new BigDecimal(sumAdOrderNum - sumAdOrderNumCompare)
                        .multiply(new BigDecimal(100)).divide(new BigDecimal(sumAdOrderNumCompare), 2, RoundingMode.HALF_UP).toPlainString())

                .setCompareCvrRate(sumCvrCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" : (sumCVr.subtract(sumCvrCompare))
                        .multiply(new BigDecimal(100)).divide(sumCvrCompare, 2, RoundingMode.HALF_UP).toPlainString())

                .setCompareCtrRate(sumCtrCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" : (sumCtr.subtract(sumCtrCompare))
                        .multiply(new BigDecimal(100)).divide(sumCtrCompare, 2, RoundingMode.HALF_UP).toPlainString())

                .setCompareAdSaleRate(sumAdSaleCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" : (sumAdSale.subtract(sumAdSaleCompare))
                        .multiply(new BigDecimal(100)).divide(sumAdSaleCompare, 2, RoundingMode.HALF_UP).toPlainString())


                .setCompareClicksRate(sumClicksCompare == 0 ? "-" : new BigDecimal(sumClicks - sumClicksCompare)
                        .multiply(new BigDecimal(100)).divide(new BigDecimal(sumClicksCompare), 2, RoundingMode.HALF_UP).toPlainString())

                .setCompareImpressionsRate(sumImpressionsCompare == 0 ? "-" : new BigDecimal(sumImpressions - sumImpressionsCompare)
                        .multiply(new BigDecimal(100)).divide(new BigDecimal(sumImpressionsCompare), 2, RoundingMode.HALF_UP).toPlainString())

                .setCompareAcotsRate(acotsCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" : (acots.subtract(acotsCompare))
                        .multiply(new BigDecimal(100)).divide(acotsCompare, 2, RoundingMode.HALF_UP).toPlainString())

                .setCompareAsotsRate(asotsCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" : (asots.subtract(asotsCompare))
                        .multiply(new BigDecimal(100)).divide(asotsCompare, 2, RoundingMode.HALF_UP).toPlainString())
                .setCompareNewToBrandDetailPageViewsRate(sumNewToBrandDetailPageViewsCompare == 0 ? "-" :
                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(sumNewToBrandDetailPageViews, sumNewToBrandDetailPageViewsCompare, 4), 100)))
                .setCompareAddToCartRates(sumAddToCartCompare == 0 ? "-" :
                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(sumAddToCart, sumAddToCartCompare, 4), 100)))
                .setCompareAddToCartRateRate(sumAddToCartRateCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" :
                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(sumAddToCartRate, sumAddToCartRateCompare, 4), 100)))
                .setCompareECPAddToCartRate(sumECPAddToCartCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" :
                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(sumECPAddToCart, sumECPAddToCartCompare, 4), 100)))
                .setCompareVideoFirstQuartileViewsRate(sumVideoFirstQuartileViewsCompare == 0 ? "-" :
                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(sumVideoFirstQuartileViews, sumVideoFirstQuartileViewsCompare, 4), 100)))
                .setCompareVideoMidpointViewsRate(sumVideoMidpointViewsCompare == 0 ? "-" :
                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(sumVideoMidpointViews, sumVideoMidpointViewsCompare, 4), 100)))
                .setCompareVideoThirdQuartileViewsRate(sumVideoThirdQuartileViewsCompare == 0 ? "-" :
                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(sumVideoThirdQuartileViews, sumVideoThirdQuartileViewsCompare, 4), 100)))
                .setCompareVideoCompleteViewsRate(sumVideoCompleteViewsCompare == 0 ? "-" :
                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(sumVideoCompleteViews, sumVideoCompleteViewsCompare, 4), 100)))
                .setCompareVideoUnmutesRate(sumVideoUnmutesCompare == 0 ? "-" :
                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(sumVideoUnmutes, sumVideoUnmutesCompare, 4), 100)))
                .setCompareViewabilityRateRate(sumViewabilityRateCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" :
                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(sumViewabilityRate, sumViewabilityRateCompare, 4), 100)))
                .setCompareViewClickThroughRateRate(sumViewClickThroughRateCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" :
                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(sumViewClickThroughRate, sumViewClickThroughRateCompare, 4), 100)))
                .setCompareBrandedSearchesRate(sumBrandedSearchesCompare == 0 ? "-" :
                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(sumBrandedSearches, sumBrandedSearchesCompare, 4), 100)))
                .setCompareDetailPageViewsRate(sumDetailPageViewsCompare == 0 ? "-" :
                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(sumDetailPageViews, sumDetailPageViewsCompare, 4), 100)))
                .setCompareAdvertisingUnitPriceRate(sumAdvertisingUnitPriceCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" :
                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(sumAdvertisingUnitPrice, sumAdvertisingUnitPriceCompare, 4), 100)))
                .setCompareViewImpressionsRate(sumViewImpressionsCompare == 0 ? "-" :
                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(sumViewImpressions, sumViewImpressionsCompare, 4), 100)))
                .setCompareVcpmRate(sumVcpmCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" : (sumVcpm.subtract(sumVcpmCompare))
                        .multiply(new BigDecimal(100)).divide(sumVcpmCompare, 2, RoundingMode.HALF_UP).toPlainString())
                .setCompareCpaRate(sumCpaCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" : (sumCpa.subtract(sumCpaCompare))
                        .multiply(new BigDecimal(100)).divide(sumCpaCompare, 2, RoundingMode.HALF_UP).toPlainString())
                .setCompareOrderNumRate(sumSalesNumCompare == 0 ? "-" :
                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(sumSalesNum, sumSalesNumCompare, 4), 100)))
                .setCompareAdSaleNumRate(sumAdSaleNumCompare == 0 ? "-" :
                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(sumAdSaleNum, sumAdSaleNumCompare, 4), 100)))
                .setCompareAdOtherOrderNumRate(sumAdOtherOrderNumCompare == 0 ? "-" :
                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(sumAdOtherOrderNum, sumAdOtherOrderNumCompare, 4), 100)))
                .setCompareAdSalesRate(sumAdSalesCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" : (sumAdSales.subtract(sumAdSalesCompare))
                        .multiply(new BigDecimal(100)).divide(sumAdSalesCompare, 2, RoundingMode.HALF_UP).toPlainString())
                .setCompareAdOtherSalesRate(sumAdOtherSalesCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" : (sumAdOtherSales.subtract(sumAdOtherSalesCompare))
                        .multiply(new BigDecimal(100)).divide(sumAdOtherSalesCompare, 2, RoundingMode.HALF_UP).toPlainString())
                .setCompareAdSelfSaleNumRate(sumOrderNumCompare == 0 ? "-" :
                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(sumOrderNum, sumOrderNumCompare, 4), 100)))
                .setCompareAdOtherSaleNumRate(sumAdOtherSaleNumCompare == 0 ? "-" :
                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(sumAdOtherSaleNum, sumAdOtherSaleNumCompare, 4), 100)))
                .setCompareOrdersNewToBrandFTDRate(sumOrdersNewToBrand14dCompare == 0 ? "-" :
                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(sumOrdersNewToBrand14d, sumOrdersNewToBrand14dCompare, 4), 100)))
                .setCompareSalesNewToBrandFTDRate(sumSalesNewToBrand14dCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" : (sumSalesNewToBrand14d.subtract(sumSalesNewToBrand14dCompare))
                        .multiply(new BigDecimal(100)).divide(sumSalesNewToBrand14dCompare, 2, RoundingMode.HALF_UP).toPlainString())
                .setCompareUnitsOrderedNewToBrandFTDRate(sumUnitsOrderedNewToBrand14dCompare == 0 ? "-" :
                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(sumUnitsOrderedNewToBrand14d, sumUnitsOrderedNewToBrand14dCompare, 4), 100)))
                .setCompareOrderRateNewToBrandFTDRate(sumOrderRateNewToBrand14dCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" : (sumOrderRateNewToBrand14d.subtract(sumOrderRateNewToBrand14dCompare))
                        .multiply(new BigDecimal(100)).divide(sumOrderRateNewToBrand14dCompare, 2, RoundingMode.HALF_UP).toPlainString())
                .setCompareSalesRateNewToBrandFTDRate(sumSalesRateNewToBrand14dCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" : (sumSalesRateNewToBrand14d.subtract(sumSalesRateNewToBrand14dCompare))
                        .multiply(new BigDecimal(100)).divide(sumSalesRateNewToBrand14dCompare, 2, RoundingMode.HALF_UP).toPlainString())
                .setCompareUnitsOrderedRateNewToBrandFTDRate(sumUnitsOrderedRateNewToBrand14dCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" : (sumUnitsOrderedRateNewToBrand14d.subtract(sumUnitsOrderedRateNewToBrand14dCompare))
                        .multiply(new BigDecimal(100)).divide(sumUnitsOrderedRateNewToBrand14dCompare, 2, RoundingMode.HALF_UP).toPlainString());
        if (isVc) {
            builder.setAcots("-");
            builder.setCompareAcotsRate("-");
            builder.setCompareAcots("-");
            builder.setAsots("-");
            builder.setCompareAsotsRate("-");
            builder.setCompareAsots("-");
        }
        return builder.build();
    }


    @Override
    public List<AdProductPageVo> getSpProductVoList(ShopAuth shopAuth, Integer puid, AdProductPageParam param, Page<AdProductPageVo> voPage, boolean isExport) {

        List<AdProductPageVo> pageVoList;

        if (isExport) {
            pageVoList = getSpProductPageVoList(puid, param);
            if (CollectionUtils.isEmpty(pageVoList)) {
                return pageVoList;
            }
            if (StringUtils.isNotBlank(param.getOrderField()) && StringUtils.isNotBlank(param.getOrderType())) {
                boolean isSorted = StringUtils.isNotBlank(param.getOrderField()) && Constants.isADperformanceOrderField(param.getOrderField());
                if (isSorted) {
                    pageVoList = PageUtil.sort(pageVoList, param.getOrderField(), param.getOrderType());
                }
            }
            if (CollectionUtils.isNotEmpty(pageVoList) && pageVoList.size() > EXPORT_MAX_SIZE) {  //限制60000条，需要优化
                pageVoList = pageVoList.subList(0, EXPORT_MAX_SIZE);
            }
            fillinAdInfoForVo(shopAuth, pageVoList, isExport);
            return pageVoList;
        }

        if ((StringUtils.isNotBlank(param.getOrderField()) && StringUtils.isNotBlank(param.getOrderType())) || param.getUseAdvanced()) {  //页面有排序或有高
            List<AdProductPageVo> voList = getSpProductPageVoList(puid, param);
            if (StringUtils.isNotBlank(param.getOrderField()) && StringUtils.isNotBlank(param.getOrderType())) {
                boolean isSorted = StringUtils.isNotBlank(param.getOrderField()) && Constants.isADperformanceOrderField(param.getOrderField());
                if (isSorted) {
                    voList = PageUtil.sort(voList, param.getOrderField(), param.getOrderType());
                }
            }
            //分页
            PageUtil.getPage(voPage, voList);
        } else {
            Page page = new Page(voPage.getPageNo(), voPage.getPageSize());
            page = getSpPageList(puid, param, page);

            if (page.getTotalSize() > Constants.TOTALSIZELIMIT) {  //总数大于十万
                int totalPage = Constants.TOTALSIZELIMIT / voPage.getPageSize();
                voPage.setTotalPage(totalPage);
                voPage.setTotalSize(Constants.TOTALSIZELIMIT);
            } else {
                voPage.setTotalPage(page.getTotalPage());
                voPage.setTotalSize(page.getTotalSize());
            }
            voPage.setRows(page.getRows());
        }

        return null;
    }

    /**
     * 分页获取sp广告产品
     */
    private Page<AdProductPageVo> getSpProductDorisVoList(ShopAuth shopAuth, Integer puid, AdProductPageParam param) {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        Page<AdProductPageVo> page = new Page<>(param.getPageNo(), param.getPageSize());
        if (param.getAdTagId() != null) {
            List<String> relationIds = adMarkupTagDao.getRelationIds(param.getPuid(), param.getShopId(), AdTagTypeEnum.PRODUCT.getType(), param.getAdTagId(), param.getType(), null);
            if (CollectionUtils.isNotEmpty(relationIds)) {
                param.setAdIds(relationIds);
            } else {
                return page;
            }
        }
        if (CollectionUtils.isNotEmpty(param.getAdTagIdList())) {
            List<String> relationIds = adMarkupTagDao.getRelationIds(param.getPuid(), param.getShopId(), AdTagTypeEnum.PRODUCT.getType(), param.getAdTagIdList(), param.getType(), null);
            if (CollectionUtils.isNotEmpty(relationIds)) {
                param.setAdIds(relationIds);
            } else {
                return page;
            }
        }

        if (param.getOnlyCount() != null && param.getOnlyCount()) {
            page.setTotalSize(amazonSpAdProductDorisDao.listAmazonSpAdProductAllCount(puid, param));
            return page;
        }
        stopWatch.stop();
        log.info("getSpProductDorisVoList : param条件拼接 time:{}", stopWatch.getLastTaskTimeMillis());

        stopWatch.start();
        Page<AmazonAdProductDorisAllReport> productDorisAllReportPage = amazonSpAdProductDorisDao.listAmazonSpAdProductPage(puid, param);
        stopWatch.stop();
        log.info("getSpProductDorisVoList : doris查询 time:{}", stopWatch.getLastTaskTimeMillis());

        if (productDorisAllReportPage != null && CollectionUtils.isNotEmpty(productDorisAllReportPage.getRows())) {
            List<AmazonAdProductDorisAllReport> amazonAdProductDorisAllReports = productDorisAllReportPage.getRows();
            ShopSaleDto shopSaleDto = new ShopSaleDto();  //最外层已经查了
            BigDecimal shopSales = param.getShopSales();
            shopSaleDto.setSumRange(shopSales);
            String domain = AmznEndpoint.getByMarketplaceId(shopAuth.getMarketplaceId()).getDomain();
            AdProductPageVo vo;
            List<String> adIds = amazonAdProductDorisAllReports.stream().map(AmazonAdProductDorisAllReport::getAdId).distinct().collect(Collectors.toList());
            stopWatch.start();
            Map<String, AmazonAdProduct> amazonAdProductMap = amazonAdProductDao.getByAdIds(puid, param.getShopId(), adIds).stream().collect(Collectors.toMap(AmazonAdProduct::getAdId, Function.identity()));
            Map<String, AmazonAdProduct> amazonAdProductDorisMap = amazonSpAdProductDorisDao.listByAdIds(puid, param.getShopId(), adIds).stream().collect(Collectors.toMap(AmazonAdProduct::getAdId, Function.identity()));
            stopWatch.stop();
            log.info("getSpProductDorisVoList : getByIds time:{}", stopWatch.getLastTaskTimeMillis());

            stopWatch.start();
            List<AdProductPageVo> voList = Lists.newArrayList();
            for (AmazonAdProductDorisAllReport amazonAdProductDorisAllReport : amazonAdProductDorisAllReports) {
                AmazonAdProduct amazonAdProduct = amazonAdProductMap.get(amazonAdProductDorisAllReport.getAdId());
                AmazonAdProduct amazonAdProductDoris = amazonAdProductDorisMap.get(amazonAdProductDorisAllReport.getAdId());
                vo = new AdProductPageVo();

                if (StringUtils.isNotBlank(param.getServingStatus())) {
                    amazonAdProductDoris.setServingStatus(amazonAdProductDoris.getServingStatus());
                    vo.setServingStatus(amazonAdProductDoris.getServingStatus());
                    vo.setServingStatusDec(amazonAdProductDoris.getServingStatusDec());
                    vo.setServingStatusName(amazonAdProductDoris.getServingStatusName());
                } else {
                    amazonAdProduct.setServingStatus(amazonAdProduct.getServingStatus());
                    vo.setServingStatus(amazonAdProduct.getServingStatus());
                    vo.setServingStatusDec(amazonAdProduct.getServingStatusDec());
                    vo.setServingStatusName(amazonAdProduct.getServingStatusName());
                }

                vo.setDomain(domain);
                vo.setType(Constants.SP);
                vo.setAsin(amazonAdProduct.getAsin());
                convertSpDtoToPageVo(amazonAdProduct, vo);
                if (StringUtils.isNotBlank(param.getStatus())) {
                    vo.setState(amazonAdProductDoris.getState());
                }

                AmazonAdProductReport amazonAdProductReport = buildAmazonAdProductReport(amazonAdProductDorisAllReport);
                cpcCommService.fillReportDataIntoPageVo(vo, amazonAdProductReport, shopSaleDto);
                voList.add(vo);
            }
            AdMetricDto adMetricDto = amazonSpAdProductDorisDao.getSumAdMetric(puid, param);
            filterMetricData(voList, adMetricDto);
            page.setRows(voList);
            page.setTotalPage(productDorisAllReportPage.getTotalPage());
            page.setTotalSize(productDorisAllReportPage.getTotalSize());
            stopWatch.stop();
            log.info("getSpProductDorisVoList : last time:{}", stopWatch.getLastTaskTimeMillis());
        }
        return page;
    }

    private AmazonAdProductReport buildAmazonAdProductReport(AmazonAdProductDorisAllReport amazonAdProductDorisAllReport) {
        AmazonAdProductReport report = new AmazonAdProductReport();
        report.setType("sp");
        report.setCountDate("");
        report.setImpressions(amazonAdProductDorisAllReport.getImpressions());
        report.setClicks(amazonAdProductDorisAllReport.getClicks());
        report.setTotalSales(amazonAdProductDorisAllReport.getTotalSales());   //兼容旧代码
        report.setAdSales(amazonAdProductDorisAllReport.getAdSales());   //兼容旧代码
        report.setSaleNum(amazonAdProductDorisAllReport.getOrderNum());
        report.setCost(amazonAdProductDorisAllReport.getCost());
        //本广告产品订单量
        report.setAdSaleNum(amazonAdProductDorisAllReport.getAdSaleNum());
        //广告销量
        report.setOrderNum(amazonAdProductDorisAllReport.getSaleNum());
        //本广告产品销量
        report.setAdOrderNum(amazonAdProductDorisAllReport.getAdOrderNum());
        return report;
    }


    @Override
    public List<AdProductPageVo> getSdProductVoList(ShopAuth shopAuth, Integer puid, AdProductPageParam param, Page<AdProductPageVo> voPage, boolean isExport) {
        List<AdProductPageVo> pageVoList;

        if (isExport) {
            pageVoList = getSdProductPageVoList(puid, param);
            if (CollectionUtils.isEmpty(pageVoList)) {
                return pageVoList;
            }
            if (StringUtils.isNotBlank(param.getOrderField()) && StringUtils.isNotBlank(param.getOrderType())) {
                boolean isSorted = StringUtils.isNotBlank(param.getOrderField()) && Constants.isADperformanceOrderField(param.getOrderField());
                if (isSorted) {
                    pageVoList = PageUtil.sort(pageVoList, param.getOrderField(), param.getOrderType());
                }
            }
            if (CollectionUtils.isNotEmpty(pageVoList) && pageVoList.size() > EXPORT_MAX_SIZE) {  //限制60000条，需要优化
                pageVoList = pageVoList.subList(0, EXPORT_MAX_SIZE);
            }
            fillinAdInfoForVo(shopAuth, pageVoList, isExport);
            return pageVoList;
        }

        if ((StringUtils.isNotBlank(param.getOrderField()) && StringUtils.isNotBlank(param.getOrderType())) || param.getUseAdvanced()) {  //页面有排序或有高
            List<AdProductPageVo> voList = getSdProductPageVoList(puid, param);
            if (StringUtils.isNotBlank(param.getOrderField()) && StringUtils.isNotBlank(param.getOrderType())) {
                boolean isSorted = StringUtils.isNotBlank(param.getOrderField()) && Constants.isADperformanceOrderField(param.getOrderField());
                if (isSorted) {
                    voList = PageUtil.sort(voList, param.getOrderField(), param.getOrderType());
                }
            }
            //分页
            PageUtil.getPage(voPage, voList);
        } else {
            Page page = new Page(voPage.getPageNo(), voPage.getPageSize());
            page = getSdPageList(puid, param, page);

            if (page.getTotalSize() > Constants.TOTALSIZELIMIT) {  //总数大于十万
                int totalPage = Constants.TOTALSIZELIMIT / voPage.getPageSize();
                voPage.setTotalPage(totalPage);
                voPage.setTotalSize(Constants.TOTALSIZELIMIT);
            } else {
                voPage.setTotalPage(page.getTotalPage());
                voPage.setTotalSize(page.getTotalSize());
            }
            voPage.setRows(page.getRows());
        }

        return null;
    }

    private List<AdProductPageVo> getSpProductPageVoList(Integer puid, AdProductPageParam param) {
        List<AdProductPageVo> voList = Lists.newArrayList();

        //标签筛选
        if (param.getAdTagId() != null) {
            List<String> relationIds = adMarkupTagDao.getRelationIds(param.getPuid(), param.getShopId(), AdTagTypeEnum.PRODUCT.getType(), param.getAdTagId(), param.getType(), null);
            if (CollectionUtils.isNotEmpty(relationIds)) {
                param.setAdIds(relationIds);
            } else {
                return voList;
            }
        }

        if (CollectionUtils.isNotEmpty(param.getAdTagIdList())) {
            List<String> relationIds = adMarkupTagDao.getRelationIds(param.getPuid(), param.getShopId(), AdTagTypeEnum.PRODUCT.getType(), param.getAdTagIdList(), param.getType(), null);
            if (CollectionUtils.isNotEmpty(relationIds)) {
                param.setAdIds(relationIds);
            } else {
                return voList;
            }
        }

        if (StringUtils.isNotBlank(param.getPortfolioId())) {  //广告组合id不为空
            List<String> campaignIds = amazonAdCampaignDao.getCampaignIdsByPortfolioId(puid, param.getShopId(), param.getPortfolioId());
            if (CollectionUtils.isNotEmpty(campaignIds)) {  // 用户选了广告组合又筛选了活动  从广告组合里面的活动列表在做一个过滤
                param.setCampaignIdList(campaignIds);
            } else {
                return voList;
            }
        }

        List<AmazonAdProduct> poList = amazonAdProductDao.getList(puid, param);
        if (CollectionUtils.isNotEmpty(poList)) {
            List<String> adIds = poList.stream().map(AmazonAdProduct::getAdId).collect(Collectors.toList());
            List<AdHomePerformancedto> list = new ArrayList<>();
            if (adIds.size() > 20000) {
                List<List<String>> lists = Lists.partition(adIds, 20000);
                List<AdHomePerformancedto> dtoList;
                for (List<String> subList : lists) {

                    dtoList = amazonAdProductReportDao.listSumReportByAdIds(puid, param.getShopId(), param.getStartDate(), param.getEndDate(), param, subList);

                    list.addAll(dtoList);
                }

                adIds = null;
            } else {

                list = amazonAdProductReportDao.listSumReportByAdIds(puid, param.getShopId(), param.getStartDate(), param.getEndDate(), param, adIds);

            }

            Map<String, AdHomePerformancedto> productReportMap = list.stream().filter(Objects::nonNull).collect(Collectors.toMap(AdHomePerformancedto::getAdId, e -> e));

            // 取店铺销售额
            ShopSaleDto shopSaleDto = new ShopSaleDto();  //最外层已经查了
            BigDecimal shopSales = param.getShopSales();
            shopSaleDto.setSumRange(shopSales);

            String domain = AmznEndpoint.getByMarketplaceId(poList.get(0).getMarketplaceId()).getDomain();
            AdProductPageVo vo;
            for (AmazonAdProduct amazonAdProduct : poList) {
                vo = new AdProductPageVo();
                amazonAdProduct.setServingStatus(amazonAdProduct.getServingStatus());
                vo.setServingStatus(amazonAdProduct.getServingStatus());
                vo.setServingStatusDec(amazonAdProduct.getServingStatusDec());
                vo.setServingStatusName(amazonAdProduct.getServingStatusName());
                vo.setDomain(domain);
                vo.setType(Constants.SP);
                vo.setAsin(amazonAdProduct.getAsin());
                convertSpDtoToPageVo(amazonAdProduct, vo);

                if (productReportMap.size() > 0) {
                    AdHomePerformancedto adHomePerformancedto = productReportMap.get(amazonAdProduct.getAdId());
                    if (adHomePerformancedto != null) {
                        // 填充报告数据
                        AmazonAdProductReport report = new AmazonAdProductReport();
                        report.setType(adHomePerformancedto.getType());
                        report.setCountDate(adHomePerformancedto.getCountDate());
                        report.setImpressions(adHomePerformancedto.getImpressions());
                        report.setClicks(adHomePerformancedto.getClicks());
                        report.setTotalSales(adHomePerformancedto.getAdSale());   //兼容旧代码
                        /**
                         * TODO 广告报告重构
                         * 本广告产品销售额
                         */
                        report.setAdSales(adHomePerformancedto.getAdSales());   //兼容旧代码
                        report.setSaleNum(adHomePerformancedto.getAdOrderNum());
                        report.setCost(adHomePerformancedto.getAdCost());
                        //本广告产品订单量
                        report.setAdSaleNum(adHomePerformancedto.getAdSaleNum());
                        //广告销量
                        report.setOrderNum(adHomePerformancedto.getSalesNum());
                        //本广告产品销量
                        report.setAdOrderNum(adHomePerformancedto.getOrderNum());
                        cpcCommService.fillReportDataIntoPageVo(vo, report, shopSaleDto);
                    }
                }
                voList.add(vo);
            }

            if (param.getUseAdvanced() != null && param.getUseAdvanced()) {  //开启了高级搜索,需要过滤
                cpcCommService.filterProductAdVanceData(voList, param);
            }
            // 获取汇总信息
            AdMetricDto adMetricDto = new AdMetricDto();
            filterSumMetricData(voList, adMetricDto);
            // 填充指标占比
            filterMetricData(voList, adMetricDto);
        }

        return voList;
    }

    private void filterSumMetricData(List<AdProductPageVo> voList, AdMetricDto adMetricDto) {
        if (CollectionUtils.isEmpty(voList)) {
            return;
        }
        adMetricDto.setSumCost(voList.stream().filter(item -> item != null && item.getAdCost() != null).map(item -> new BigDecimal(item.getAdCost())).reduce(BigDecimal.ZERO, BigDecimal::add));
        adMetricDto.setSumAdSale(voList.stream().filter(item -> item != null && item.getAdSale() != null).map(item -> new BigDecimal(item.getAdSale())).reduce(BigDecimal.ZERO, BigDecimal::add));
        adMetricDto.setSumAdOrderNum(BigDecimal.valueOf(voList.stream().filter(item -> item != null && item.getAdOrderNum() != null).mapToInt(AdProductPageVo::getAdOrderNum).sum()));
        adMetricDto.setSumOrderNum(BigDecimal.valueOf(voList.stream().filter(item -> item != null && item.getOrderNum() != null).mapToInt(AdProductPageVo::getOrderNum).sum()));
    }

    // 填充指标占比数据
    private void filterMetricData(List<AdProductPageVo> voList, AdMetricDto adMetricDto) {
        for (AdProductPageVo vo : voList) {
            if (adMetricDto == null) {
                vo.setAdCostPercentage("0");
                vo.setAdSalePercentage("0");
                vo.setAdOrderNumPercentage("0");
                vo.setOrderNumPercentage("0");
                continue;
            }
            computeMetricData(adMetricDto, vo);
        }
    }

    private void computeMetricData(AdMetricDto adMetricDto, AdProductPageVo vo) {
        // 花费占比：单条广告的花费占所查询结果中所有广告花费之和的比例 单个广告花费/查询结果广告花费之和*100%
        if (vo.getAdCost() != null
                && adMetricDto.getSumCost() != null
                && adMetricDto.getSumCost().doubleValue() > 0) {
            vo.setAdCostPercentage(MathUtil.multiply(MathUtil.divide(vo.getAdCost(), adMetricDto.getSumCost().toString()), "100"));
        }

        // 销售额占比：单条广告的销售额占查询结果中所有广告销售额之和的比例 单个广告销售额/查询结果广告销售额之和*100%
        if (vo.getAdSale() != null
                && adMetricDto.getSumAdSale() != null
                && adMetricDto.getSumAdSale().doubleValue() > 0) {
            vo.setAdSalePercentage(MathUtil.multiply(MathUtil.divide(vo.getAdSale(), adMetricDto.getSumAdSale().toString()), "100"));
        }

        // 广告订单数占比：单条广告的订单数占查询结果中所有广告订单数的比例 单个广告订单数/查询结果广告订单数之和*100%
        if (vo.getAdOrderNum() != null
                && adMetricDto.getSumAdOrderNum() != null
                && adMetricDto.getSumAdOrderNum().doubleValue() > 0) {
            vo.setAdOrderNumPercentage(MathUtil.multiply(MathUtil.divide(vo.getAdOrderNum().toString(), adMetricDto.getSumAdOrderNum().toString()), "100"));
        }

        // 广告销量占比：单条广告的销量占比查询结果中所有广告销量之和的比例 单个广告组合广告销量/查询结果广告组合广告销量之和*100%
        if (vo.getOrderNum() != null
                && adMetricDto.getSumOrderNum() != null
                && adMetricDto.getSumOrderNum().doubleValue() > 0) {
            vo.setOrderNumPercentage(MathUtil.multiply(MathUtil.divide(vo.getOrderNum().toString(), adMetricDto.getSumOrderNum().toString()), "100"));
        }
    }

    private Page getSpPageList(Integer puid, AdProductPageParam param, Page page) {
        List<AdProductPageVo> voList = Lists.newArrayList();


        //标签筛选
        if (param.getAdTagId() != null) {
            List<String> relationIds = adMarkupTagDao.getRelationIds(param.getPuid(), param.getShopId(), AdTagTypeEnum.PRODUCT.getType(), param.getAdTagId(), param.getType(), null);
            if (CollectionUtils.isNotEmpty(relationIds)) {
                param.setAdIds(relationIds);
            } else {
                return page;
            }
        }

        if (CollectionUtils.isNotEmpty(param.getAdTagIdList())) {
            List<String> relationIds = adMarkupTagDao.getRelationIds(param.getPuid(), param.getShopId(), AdTagTypeEnum.PRODUCT.getType(), param.getAdTagIdList(), param.getType(), null);
            if (CollectionUtils.isNotEmpty(relationIds)) {
                param.setAdIds(relationIds);
            } else {
                return page;
            }
        }

        if (StringUtils.isNotBlank(param.getPortfolioId())) {  //广告组合id不为空
            List<String> campaignIds = amazonAdCampaignDao.getCampaignIdsByPortfolioId(puid, param.getShopId(), param.getPortfolioId());
            if (CollectionUtils.isNotEmpty(campaignIds)) {  // 用户选了广告组合又筛选了活动  从广告组合里面的活动列表在做一个过滤
                param.setCampaignIdList(campaignIds);
            } else {
                return page;
            }
        }


        page = amazonAdProductDao.getPageList(puid, param, page);

        List<AmazonAdProduct> poList = page.getRows();
        if (CollectionUtils.isNotEmpty(poList)) {
            List<String> adIds = poList.stream().map(AmazonAdProduct::getAdId).collect(Collectors.toList());

            List<AdHomePerformancedto> list = new ArrayList<>();

            list = amazonAdProductReportDao.listSumReportByAdIds(puid, param.getShopId(), param.getStartDate(),
                    param.getEndDate(), param, adIds);

            Map<String, AdHomePerformancedto> productReportMap = list.stream().filter(Objects::nonNull).collect(Collectors.toMap(AdHomePerformancedto::getAdId, e -> e));

            // 取店铺销售额
            ShopSaleDto shopSaleDto = new ShopSaleDto();  //最外层已经查了
            BigDecimal shopSales = param.getShopSales();
            shopSaleDto.setSumRange(shopSales);

            String domain = AmznEndpoint.getByMarketplaceId(poList.get(0).getMarketplaceId()).getDomain();
            AdProductPageVo vo;
            page.setRows(voList);
            for (AmazonAdProduct amazonAdProduct : poList) {
                vo = new AdProductPageVo();
                amazonAdProduct.setServingStatus(amazonAdProduct.getServingStatus());
                vo.setServingStatus(amazonAdProduct.getServingStatus());
                vo.setServingStatusDec(amazonAdProduct.getServingStatusDec());
                vo.setServingStatusName(amazonAdProduct.getServingStatusName());
                vo.setDomain(domain);
                vo.setType(Constants.SP);
                vo.setAsin(amazonAdProduct.getAsin());
                convertSpDtoToPageVo(amazonAdProduct, vo);

                if (productReportMap.size() > 0) {
                    AdHomePerformancedto adHomePerformancedto = productReportMap.get(amazonAdProduct.getAdId());
                    if (adHomePerformancedto != null) {
                        // 填充报告数据
                        AmazonAdProductReport report = new AmazonAdProductReport();
                        report.setType(adHomePerformancedto.getType());
                        report.setCountDate(adHomePerformancedto.getCountDate());
                        report.setImpressions(adHomePerformancedto.getImpressions());
                        report.setClicks(adHomePerformancedto.getClicks());
                        report.setTotalSales(adHomePerformancedto.getAdSale());   //兼容旧代码
                        /**
                         * TODO 广告报告重构
                         * 本广告产品销售额
                         */
                        report.setAdSales(adHomePerformancedto.getAdSales());   //兼容旧代码
                        report.setSaleNum(adHomePerformancedto.getAdOrderNum());
                        report.setCost(adHomePerformancedto.getAdCost());
                        //本广告产品订单量
                        report.setAdSaleNum(adHomePerformancedto.getAdSaleNum());
                        //广告销量
                        report.setOrderNum(adHomePerformancedto.getSalesNum());
                        //本广告产品销量
                        report.setAdOrderNum(adHomePerformancedto.getOrderNum());
                        cpcCommService.fillReportDataIntoPageVo(vo, report, shopSaleDto);
                    }
                }
                voList.add(vo);
            }
        }

        return page;

    }

    private List<AdProductPageVo> getSdProductPageVoList(Integer puid, AdProductPageParam param) {
        List<AdProductPageVo> voList = Lists.newArrayList();

        //标签筛选
        if (param.getAdTagId() != null) {
            List<String> relationIds = adMarkupTagDao.getRelationIds(param.getPuid(), param.getShopId(), AdTagTypeEnum.PRODUCT.getType(), param.getAdTagId(), param.getType(), null);
            if (CollectionUtils.isNotEmpty(relationIds)) {
                param.setAdIds(relationIds);
            } else {
                return voList;
            }
        }

        if (CollectionUtils.isNotEmpty(param.getAdTagIdList())) {
            List<String> relationIds = adMarkupTagDao.getRelationIds(param.getPuid(), param.getShopId(), AdTagTypeEnum.PRODUCT.getType(), param.getAdTagIdList(), param.getType(), null);
            if (CollectionUtils.isNotEmpty(relationIds)) {
                param.setAdIds(relationIds);
            } else {
                return voList;
            }
        }

        if (StringUtils.isNotBlank(param.getPortfolioId())) {  //广告组合id不为空
            List<String> campaignIds = amazonSdAdCampaignDao.getCampaignIdsByPortfolioId(puid, param.getShopId(), param.getPortfolioId());
            if (CollectionUtils.isNotEmpty(campaignIds)) {  // 用户选了广告组合又筛选了活动  从广告组合里面的活动列表在做一个过滤
                param.setCampaignIdList(campaignIds);
            } else {
                return voList;
            }
        }

        List<AmazonSdAdProduct> poList = sdAdProductDao.getList(puid, param);
        if (CollectionUtils.isNotEmpty(poList)) {

            List<String> adIds = poList.stream().map(AmazonSdAdProduct::getAdId).collect(Collectors.toList());
            List<String> campaignIdList = StreamUtil.toListDistinct(poList, AmazonSdAdProduct::getCampaignId);
            List<AmazonAdCampaignAll> campaignList = amazonAdCampaignDao.getByCampaignIds(puid, param.getShopId(), campaignIdList);
            Map<String, AmazonAdCampaignAll> campaignAllMap = StreamUtil.toMap(campaignList, AmazonAdCampaignAll::getCampaignId);
            List<AdHomePerformancedto> list = new ArrayList<>();
            List<AdHomePerformancedto> latestReports = new ArrayList<>();
            if (adIds.size() > 20000) {
                List<List<String>> lists = Lists.partition(adIds, 20000);
                List<AdHomePerformancedto> dtoList = null;
                for (List<String> subList : lists) {

                    dtoList = amazonAdSdProductReportDao.listSumReportByAdIds(puid, param.getShopId(), param.getStartDate(), param.getEndDate(), param, subList);

                    list.addAll(dtoList);
                    latestReports.addAll(amazonAdSdProductReportDao.listLatestReports(puid, param.getShopId(), subList, false));
                }

                adIds = null;
            } else {

                list = amazonAdSdProductReportDao.listSumReportByAdIds(puid, param.getShopId(), param.getStartDate(), param.getEndDate(), param, adIds);
                latestReports = amazonAdSdProductReportDao.listLatestReports(puid, param.getShopId(), adIds, false);

            }

            Map<String, AdHomePerformancedto> productReportMap = list.stream().filter(Objects::nonNull).collect(Collectors.toMap(AdHomePerformancedto::getAdId, e -> e));
            Map<String, AdHomePerformancedto> latestReportMap = latestReports.stream().collect(Collectors.toMap(AdHomePerformancedto::getAdId, Function.identity(), (x, y) -> x));

            // 取店铺销售额
            ShopSaleDto shopSaleDto = new ShopSaleDto();  //最外层已经查了
            BigDecimal shopSales = param.getShopSales();
            shopSaleDto.setSumRange(shopSales);

            String domain = AmznEndpoint.getByMarketplaceId(poList.get(0).getMarketplaceId()).getDomain();
            AdProductPageVo vo;
            for (AmazonSdAdProduct amazonAdProduct : poList) {
                vo = new AdProductPageVo();
                AmazonAdCampaignAll campaignAll = campaignAllMap.get(amazonAdProduct.getCampaignId());
                amazonAdProduct.setServingStatus(amazonAdProduct.getServingStatus());
                vo.setServingStatus(amazonAdProduct.getServingStatus());
                vo.setServingStatusDec(amazonAdProduct.getServingStatusDec());
                vo.setServingStatusName(amazonAdProduct.getServingStatusName());
                vo.setDomain(domain);
                vo.setType(Constants.SD);
                vo.setAsin(amazonAdProduct.getAsin());
                vo.setCostType(campaignAll == null ? null : campaignAll.getCostType());
                convertSdDtoToPageVo(amazonAdProduct, vo);

                if (productReportMap.size() > 0) {
                    AdHomePerformancedto adHomePerformancedto = productReportMap.get(amazonAdProduct.getAdId());
                    if (adHomePerformancedto != null) {
                        // 填充报告数据
                        AmazonAdProductReport report = new AmazonAdProductReport();
                        report.setType(adHomePerformancedto.getType());
                        report.setCountDate(adHomePerformancedto.getCountDate());
                        report.setImpressions(adHomePerformancedto.getImpressions());
                        report.setClicks(adHomePerformancedto.getClicks());
                        report.setTotalSales(adHomePerformancedto.getAdSale());   //兼容旧代码
                        /**
                         * TODO 广告报告重构
                         * 本广告产品销售额
                         */
                        report.setAdSales(adHomePerformancedto.getAdSales());   //兼容旧代码
                        report.setSaleNum(adHomePerformancedto.getAdOrderNum());
                        report.setCost(adHomePerformancedto.getAdCost());
                        //可见展示次数(VCPM专用)
                        report.setViewImpressions(adHomePerformancedto.getViewImpressions());
                        //本广告产品订单量
                        report.setAdSaleNum(adHomePerformancedto.getAdSaleNum());
                        //CPC,VCPM-广告销量
                        report.setOrderNum(adHomePerformancedto.getSalesNum());
                        //CPC,VCPM-“品牌新买家”订单量
                        report.setOrdersNewToBrand14d(adHomePerformancedto.getOrdersNewToBrand14d());
                        //CPC,VCPM-“品牌新买家”销售额
                        report.setSalesNewToBrand14d(adHomePerformancedto.getSalesNewToBrand14d());
                        //CPC,VCPM-“品牌新买家”销量
                        report.setUnitsOrderedNewToBrand14d(adHomePerformancedto.getUnitsOrderedNewToBrand14d());

                        report.setNewToBrandDetailPageViews(adHomePerformancedto.getNewToBrandDetailPageViews());
                        report.setAddToCart(adHomePerformancedto.getAddToCart());
                        report.setAddToCartRate(adHomePerformancedto.getAddToCartRate());
                        report.setECPAddToCart(adHomePerformancedto.getECPAddToCart());
                        report.setVideoFirstQuartileViews(adHomePerformancedto.getVideoFirstQuartileViews());
                        report.setVideoMidpointViews(adHomePerformancedto.getVideoMidpointViews());
                        report.setVideoThirdQuartileViews(adHomePerformancedto.getVideoThirdQuartileViews());
                        report.setVideoCompleteViews(adHomePerformancedto.getVideoCompleteViews());
                        report.setVideoUnmutes(adHomePerformancedto.getVideoUnmutes());
                        report.setViewabilityRate(adHomePerformancedto.getViewabilityRate());
                        report.setViewClickThroughRate(adHomePerformancedto.getViewClickThroughRate());
                        report.setBrandedSearches(adHomePerformancedto.getBrandedSearches());
                        report.setDetailPageViews(adHomePerformancedto.getDetailPageViews());
                        report.setCumulativeReach(adHomePerformancedto.getCumulativeReach());
                        report.setImpressionsFrequencyAverage(adHomePerformancedto.getImpressionsFrequencyAverage());

                        cpcCommService.fillReportDataIntoPageVo(vo, report, shopSaleDto);
                    }
                }
                // 填充最新数据
                if (latestReportMap.get(amazonAdProduct.getAdId()) != null) {
                    cpcCommService.fillLatestReportDataIntoPageVo(vo, latestReportMap.get(amazonAdProduct.getAdId()));
                }
                if(AdOrderByFieldEnum.VCPM.getCode().equals(param.getOrderField())){
                    // vcpm取值逻辑调整 用于排序放至最后
                    int value = OrderTypeEnum.desc.getType().equals(param.getOrderType()) ? Integer.MIN_VALUE : Integer.MAX_VALUE;
                    if(!SBCampaignCostTypeEnum.VCPM.getCode().equals(vo.getCostType())){
                        vo.setVcpm(Integer.toString(value));
                    }else if(StringUtil.isEmpty(vo.getVcpm())){
                        vo.setVcpm("0");
                    }
                }
                voList.add(vo);
            }

            if (param.getUseAdvanced() != null && param.getUseAdvanced()) {  //开启了高级搜索,需要过滤
                cpcCommService.filterProductAdVanceData(voList, param);
            }
            // 获取汇总信息
            AdMetricDto adMetricDto = new AdMetricDto();
            filterSumMetricData(voList, adMetricDto);
            // 填充指标占比
            filterMetricData(voList, adMetricDto);
        }

        return voList;
    }

    private Page getSdPageList(Integer puid, AdProductPageParam param, Page page) {
        List<AdProductPageVo> voList = Lists.newArrayList();

        //标签筛选
        if (param.getAdTagId() != null) {
            List<String> relationIds = adMarkupTagDao.getRelationIds(param.getPuid(), param.getShopId(), AdTagTypeEnum.PRODUCT.getType(), param.getAdTagId(), param.getType(), null);
            if (CollectionUtils.isNotEmpty(relationIds)) {
                param.setAdIds(relationIds);
            } else {
                return page;
            }
        }

        if (CollectionUtils.isNotEmpty(param.getAdTagIdList())) {
            List<String> relationIds = adMarkupTagDao.getRelationIds(param.getPuid(), param.getShopId(), AdTagTypeEnum.PRODUCT.getType(), param.getAdTagIdList(), param.getType(), null);
            if (CollectionUtils.isNotEmpty(relationIds)) {
                param.setAdIds(relationIds);
            } else {
                return page;
            }
        }

        if (StringUtils.isNotBlank(param.getPortfolioId())) {  //广告组合id不为空
            List<String> campaignIds = amazonSdAdCampaignDao.getCampaignIdsByPortfolioId(puid, param.getShopId(), param.getPortfolioId());
            if (CollectionUtils.isNotEmpty(campaignIds)) {  // 用户选了广告组合又筛选了活动  从广告组合里面的活动列表在做一个过滤
                param.setCampaignIdList(campaignIds);
            } else {
                return page;
            }
        }

        page = sdAdProductDao.getPageList(puid, param, page);

        List<AmazonSdAdProduct> poList = page.getRows();
        if (CollectionUtils.isNotEmpty(poList)) {
            List<String> adIds = poList.stream().map(AmazonSdAdProduct::getAdId).collect(Collectors.toList());
            List<AdHomePerformancedto> list = null;
            List<AdHomePerformancedto> latestReports = new ArrayList<>();

            list = amazonAdSdProductReportDao.listSumReportByAdIds(puid, param.getShopId(), param.getStartDate(),
                    param.getEndDate(), param, adIds);
            latestReports = amazonAdSdProductReportDao.listLatestReports(puid, param.getShopId(), adIds, false);

            Map<String, AdHomePerformancedto> productReportMap = list.stream().filter(Objects::nonNull).collect(Collectors.toMap(AdHomePerformancedto::getAdId, e -> e));
            Map<String, AdHomePerformancedto> latestReportMap = latestReports.stream().collect(Collectors.toMap(AdHomePerformancedto::getAdId, Function.identity(), (x, y) -> x));

            // 取店铺销售额
            ShopSaleDto shopSaleDto = new ShopSaleDto();  //最外层已经查了
            BigDecimal shopSales = param.getShopSales();
            shopSaleDto.setSumRange(shopSales);

            String domain = AmznEndpoint.getByMarketplaceId(poList.get(0).getMarketplaceId()).getDomain();
            AdProductPageVo vo;
            page.setRows(voList);
            for (AmazonSdAdProduct amazonAdProduct : poList) {
                vo = new AdProductPageVo();
                amazonAdProduct.setServingStatus(amazonAdProduct.getServingStatus());
                vo.setServingStatus(amazonAdProduct.getServingStatus());
                vo.setServingStatusDec(amazonAdProduct.getServingStatusDec());
                vo.setServingStatusName(amazonAdProduct.getServingStatusName());
                vo.setDomain(domain);
                vo.setType(Constants.SD);
                vo.setAsin(amazonAdProduct.getAsin());
                convertSdDtoToPageVo(amazonAdProduct, vo);

                if (productReportMap.size() > 0) {
                    AdHomePerformancedto adHomePerformancedto = productReportMap.get(amazonAdProduct.getAdId());
                    if (adHomePerformancedto != null) {
                        // 填充报告数据
                        AmazonAdProductReport report = new AmazonAdProductReport();
                        report.setType(adHomePerformancedto.getType());
                        report.setCountDate(adHomePerformancedto.getCountDate());
                        report.setImpressions(adHomePerformancedto.getImpressions());
                        report.setClicks(adHomePerformancedto.getClicks());
                        report.setTotalSales(adHomePerformancedto.getAdSale());   //兼容旧代码
                        /**
                         * TODO 广告报告重构
                         * 本广告产品销售额
                         */
                        report.setAdSales(adHomePerformancedto.getAdSales());   //兼容旧代码
                        report.setSaleNum(adHomePerformancedto.getAdOrderNum());
                        report.setCost(adHomePerformancedto.getAdCost());
                        //可见展示次数(VCPM专用)
                        report.setViewImpressions(adHomePerformancedto.getViewImpressions());
                        //本广告产品订单量
                        report.setAdSaleNum(adHomePerformancedto.getAdSaleNum());
                        //CPC,VCPM-广告销量
                        report.setOrderNum(adHomePerformancedto.getSalesNum());
                        //CPC,VCPM-“品牌新买家”订单量
                        report.setOrdersNewToBrand14d(adHomePerformancedto.getOrdersNewToBrand14d());
                        //CPC,VCPM-“品牌新买家”销售额
                        report.setSalesNewToBrand14d(adHomePerformancedto.getSalesNewToBrand14d());
                        //CPC,VCPM-“品牌新买家”销量
                        report.setUnitsOrderedNewToBrand14d(adHomePerformancedto.getUnitsOrderedNewToBrand14d());

                        report.setNewToBrandDetailPageViews(adHomePerformancedto.getNewToBrandDetailPageViews());
                        report.setAddToCart(adHomePerformancedto.getAddToCart());
                        report.setAddToCartRate(adHomePerformancedto.getAddToCartRate());
                        report.setECPAddToCart(adHomePerformancedto.getECPAddToCart());
                        report.setVideoFirstQuartileViews(adHomePerformancedto.getVideoFirstQuartileViews());
                        report.setVideoMidpointViews(adHomePerformancedto.getVideoMidpointViews());
                        report.setVideoThirdQuartileViews(adHomePerformancedto.getVideoThirdQuartileViews());
                        report.setVideoCompleteViews(adHomePerformancedto.getVideoCompleteViews());
                        report.setVideoUnmutes(adHomePerformancedto.getVideoUnmutes());
                        report.setViewabilityRate(adHomePerformancedto.getViewabilityRate());
                        report.setViewClickThroughRate(adHomePerformancedto.getViewClickThroughRate());
                        report.setBrandedSearches(adHomePerformancedto.getBrandedSearches());
                        report.setDetailPageViews(adHomePerformancedto.getDetailPageViews());
                        report.setCumulativeReach(adHomePerformancedto.getCumulativeReach());
                        report.setImpressionsFrequencyAverage(adHomePerformancedto.getImpressionsFrequencyAverage());

                        cpcCommService.fillReportDataIntoPageVo(vo, report, shopSaleDto);
                    }
                    // 填充最新数据
                    if (latestReportMap.get(amazonAdProduct.getAdId()) != null) {
                        cpcCommService.fillLatestReportDataIntoPageVo(vo, latestReportMap.get(amazonAdProduct.getAdId()));
                    }
                }
                voList.add(vo);
            }
        }

        return page;
    }

    // po->列表页vo
    private void convertDtoToPageVo(AmazonAdProductDto amazonAdProduct, AdProductPageVo vo) {
        vo.setId(amazonAdProduct.getId());
        vo.setShopId(amazonAdProduct.getShopId());
        vo.setCampaignId(amazonAdProduct.getCampaignId());
        vo.setAdGroupId(amazonAdProduct.getAdGroupId());
        vo.setAdId(amazonAdProduct.getAdId());
        vo.setState(amazonAdProduct.getState());
        vo.setAsin(amazonAdProduct.getAsin());
        vo.setSku(amazonAdProduct.getSku());
    }

    // po->列表页vo
    private void convertSpDtoToPageVo(AmazonAdProduct amazonAdProduct, AdProductPageVo vo) {
        vo.setId(amazonAdProduct.getId());
        vo.setShopId(amazonAdProduct.getShopId());
        vo.setCampaignId(amazonAdProduct.getCampaignId());
        vo.setAdGroupId(amazonAdProduct.getAdGroupId());
        vo.setAdId(amazonAdProduct.getAdId());
        vo.setState(amazonAdProduct.getState());
        vo.setAsin(amazonAdProduct.getAsin());
        vo.setSku(amazonAdProduct.getSku());
        vo.setUpdateTime(amazonAdProduct.getUpdateTime());
    }

    // po->列表页vo
    private void convertSdDtoToPageVo(AmazonSdAdProduct amazonAdProduct, AdProductPageVo vo) {
        vo.setId(amazonAdProduct.getId());
        vo.setShopId(amazonAdProduct.getShopId());
        vo.setCampaignId(amazonAdProduct.getCampaignId());
        vo.setAdGroupId(amazonAdProduct.getAdGroupId());
        vo.setAdId(amazonAdProduct.getAdId());
        vo.setState(amazonAdProduct.getState());
        vo.setAsin(amazonAdProduct.getAsin());
        vo.setSku(amazonAdProduct.getSku());
        vo.setUpdateTime(amazonAdProduct.getUpdateTime());
    }

    @Override
    public Result updateBatch(List<AmazonAdProduct> amazonAdProducts, String loginIp) {


        if (CollectionUtils.isEmpty(amazonAdProducts)) {
            return ResultUtil.error("参数错误");
        }
        int puid = amazonAdProducts.get(0).getPuid();
        int uid = amazonAdProducts.get(0).getUpdateId();
        int shopId = amazonAdProducts.get(0).getShopId();
        List<AmazonAdProduct> errorList = Lists.newArrayList();
        List<Long> ids = amazonAdProducts.stream().filter(e -> e.getId() != null).map(AmazonAdProduct::getId).collect(Collectors.toList());
        List<AmazonAdProduct> listByIdList = amazonAdProductDao.getListByLongIdList(puid, ids);
        List<AmazonAdProduct> updateList = Lists.newArrayList();
        Map<Long, AmazonAdProduct> amazonAdProductMap = listByIdList.stream().collect(Collectors.toMap(AmazonAdProduct::getId, e -> e));
        for (AmazonAdProduct amazonAdProductVo : amazonAdProducts) {
            AmazonAdProduct oldAmazonAdProduct = amazonAdProductMap.get(amazonAdProductVo.getId());
            if (oldAmazonAdProduct == null) {
                AmazonAdProduct error = new AmazonAdProduct();
                error.setId(amazonAdProductVo.getId());
                error.setFailReason("对象不存在");
                errorList.add(error);
                continue;
            }
            if (StringUtils.isBlank(amazonAdProductVo.getState())) {
                oldAmazonAdProduct.setFailReason("状态错误");
                errorList.add(oldAmazonAdProduct);
                continue;
            }
            AmazonAdProduct amazonAdProduct = new AmazonAdProduct();
            BeanUtils.copyProperties(oldAmazonAdProduct, amazonAdProduct);
            amazonAdProduct.setState(amazonAdProductVo.getState());
            amazonAdProduct.setUpdateId(amazonAdProductVo.getUpdateId());
            updateList.add(amazonAdProduct);
        }
        if (CollectionUtils.isEmpty(updateList)) {
            BatchResponseVo<AmazonAdProduct, AmazonAdProduct> data = new BatchResponseVo<>();
            data.setCountNum(amazonAdProducts.size());
            data.setSuccessNum(0);
            data.setFailNum(errorList.size());
            data.setErrorList(errorList);
            return ResultUtil.success(data);
        }

        Result<BatchResponseVo<AmazonAdProduct, AmazonAdProduct>> result = cpcAdProductApiService.updateReturnErrorList(updateList);

        if (result.success()) {
            BatchResponseVo<AmazonAdProduct, AmazonAdProduct> data = result.getData();
            if (CollectionUtils.isNotEmpty(errorList)) {
                List<AmazonAdProduct> amazonAdProductError = data.getErrorList();
                amazonAdProductError.addAll(errorList);
                data.setFailNum(data.getErrorList().size());
                data.setCountNum((data.getErrorList() == null ? 0 : data.getErrorList().size()) + (data.getSuccessList() == null ? 0 : data.getSuccessList().size()));
            }
            List<AmazonAdProduct> amazonAdProductSuccess = data.getSuccessList();


            //记操作日志
            if (CollectionUtils.isNotEmpty(amazonAdProductSuccess)) {
                amazonAdProductDao.updateList(puid, amazonAdProductSuccess);
                saveDoris(puid, shopId, amazonAdProductSuccess.stream().map(x -> x.getAdId()).collect(Collectors.toList()));
                //更新成功数据打日志
                log.info("用户批量更新成功,updateId:{},puid :{},shopid:{},更新成功数据：{}", uid, puid, shopId, JSONUtil.objectToJson(amazonAdProductSuccess));

            }
            try {
                if (CollectionUtils.isNotEmpty(data.getErrorList())) {
                    List<AdManageOperationLog> collect = data.getErrorList().stream().map(e -> {
                        AmazonAdProduct amazonAdProduct = amazonAdProductMap.get(e.getId());
                        if (amazonAdProduct != null) {
                            AdManageOperationLog productLog = manageOperationLogService.getProductLog(amazonAdProduct, e);
                            productLog.setIp(loginIp);
                            productLog.setResult(1);
                            productLog.setResultInfo(e.getFailReason());
                            return productLog;
                        }
                        return null;
                    }).filter(Objects::nonNull).collect(Collectors.toList());
                    manageOperationLogService.batchLogsMergeByAdGroup(collect);
                }
                if (CollectionUtils.isNotEmpty(data.getSuccessList())) {
                    List<AdManageOperationLog> collect = data.getSuccessList().stream().map(e -> {
                        AmazonAdProduct amazonAdProduct = amazonAdProductMap.get(e.getId());
                        if (amazonAdProduct != null) {
                            AdManageOperationLog productLog = manageOperationLogService.getProductLog(amazonAdProduct, e);
                            productLog.setIp(loginIp);
                            productLog.setResult(0);
                            return productLog;
                        }
                        return null;
                    }).filter(Objects::nonNull).collect(Collectors.toList());
                    manageOperationLogService.batchLogsMergeByAdGroup(collect);
                }
            } catch (Exception e) {
                log.info("批量操作sp广告日志打印异常", e);
            }
            data.getSuccessList().clear();
        } else {
            try {
                List<AdManageOperationLog> collect = updateList.stream().map(e -> {
                    AdManageOperationLog proLog = manageOperationLogService.getProductLog(amazonAdProductMap.get(e.getId()), e);
                    proLog.setIp(loginIp);
                    proLog.setResult(1);
                    proLog.setResultInfo(result.getMsg());
                    return proLog;
                }).collect(Collectors.toList());
                manageOperationLogService.batchLogsMergeByAdGroup(collect);
            } catch (Exception e) {
                log.info("批量操作sp广告日志打印异常", e);
            }
        }
        return result;
    }

    private List<Object> buildUpLogMessage(Map<Long, AmazonAdProduct> oldList, List<AmazonAdProduct> newList) {
        List<Object> dataList = new ArrayList<>();
        StringBuilder builder = new StringBuilder();
        dataList.add("SP广告批量修改状态");
        newList.forEach(e -> {
            AmazonAdProduct old = oldList.get(e.getId());


            builder.append("adId:").append(e.getAdId());
            if (old != null) {
                builder.append(",旧值:").append(old.getState());
            }
            builder.append(",新值:").append(e.getState());

            dataList.add(builder.toString());
            builder.delete(0, builder.length());
        });
        return dataList;
    }


    /**
     * 写入doris
     *
     * @param succList
     * @param create
     */
    @Override
    public void saveDoris(List<AmazonAdProduct> succList, boolean create, boolean update) {
        try {
            Date date = new Date();
            List<OdsAmazonAdProduct> collect = succList.stream().map(x -> {
                OdsAmazonAdProduct odsAmazonAdProduct = new OdsAmazonAdProduct();
                BeanUtils.copyProperties(x, odsAmazonAdProduct);
                if (create) {
                    odsAmazonAdProduct.setCreateTime(date);
                }
                if (update) {
                    odsAmazonAdProduct.setUpdateTime(date);
                }
                if (StringUtils.isNotBlank(odsAmazonAdProduct.getState())) {
                    odsAmazonAdProduct.setState(odsAmazonAdProduct.getState().toLowerCase());
                }
                return odsAmazonAdProduct;
            }).collect(Collectors.toList());
            dorisService.saveDoris(collect);
        } catch (Exception e) {
            log.error("sp product save doris error", e);
        }
    }


    /**
     * 写入doris
     *
     * @param puid
     * @param shopId
     * @param adIdList
     */
    private void saveDoris(int puid, int shopId, List<String> adIdList) {
        try {
            if (CollectionUtils.isEmpty(adIdList)) {
                return;
            }
            List<AmazonAdProduct> list = amazonAdProductDao.getByAdIds(puid, shopId, adIdList);
            saveDoris(list, false, false);
        } catch (Exception e) {
            log.error("sp product save doris error", e);
        }
    }

    @Override
    public List<OdsAmazonAdProduct> getSpProductGroup(MultiShopGroupByProductParam param) {
        return StringUtils.equalsIgnoreCase(param.getSearchType(), Constants.PRODUCT_TYPE_PARENTASIN) ?
                spProductDorisDao.getGroupIdByParentAsin(param) : spProductDorisDao.getGroupIdByProduct(param);
    }

    @Override
    public List<AdProductPageVo> getSpProductClickData(MultiShopGroupByProductParam param, List<OdsAmazonAdProduct> productGroup) {
        return StringUtils.equalsIgnoreCase(param.getSearchType(), Constants.PRODUCT_TYPE_PARENTASIN) ?
                spProductDorisDao.getProductClickDataByParent(param, productGroup) : spProductDorisDao.getProductClickData(param, productGroup);
    }

    @Override
    public List<OdsAmazonSbAds> getSbProductGroup(MultiShopGroupByProductParam param) {
        List<String> searchValue = param.getSearchValue();
        if (CollectionUtils.isEmpty(searchValue)) {
            return Collections.emptyList();
        }
        List<OdsProduct> productList;
        Function<OdsProduct, String> valueExtractor;
        if (StringUtils.equalsIgnoreCase(param.getSearchType(), Constants.PRODUCT_TYPE_PARENTASIN)) {
            productList = odsProductDao.listByTrueParentAsin(param.getPuid(), param.getMarketplaceId(), param.getShopIds(), searchValue);
            valueExtractor = OdsProduct::getParentAsin;
        } else if (StringUtils.equalsIgnoreCase(param.getSearchType(), Constants.PRODUCT_TYPE_MSKU)) {
            productList = odsProductDao.listBySkuList(param.getPuid(), param.getMarketplaceId(), param.getShopIds(), searchValue);
            valueExtractor = OdsProduct::getSku;
        } else {
            productList = odsProductDao.listByAsinSku(param.getPuid(), param.getMarketplaceId(), param.getShopIds(), searchValue, null);
            valueExtractor = OdsProduct::getAsin;
        }
//        log.info("chTest getSbProductGroup productList:{}", JSONUtil.objectToJson(productList));
        Map<Integer, Set<String>> shopAsinMap = buildShopAsinMap(param, productList, valueExtractor);
//        log.info("chTest getSbProductGroup shopAsinMap:{}", JSONUtil.objectToJson(shopAsinMap));
        if (MapUtils.isEmpty(shopAsinMap)) {
            return Collections.emptyList();
        }

        List<OdsAmazonSbAds> asinsGroup = sbAdsDorisDao.getGroupIdByAsins(param, shopAsinMap);
        if (CollectionUtils.isEmpty(asinsGroup)) {
            return Collections.emptyList();
        }

        asinsGroup.forEach(item -> {
            List<String> list = Arrays.stream(item.getAsins().split(",")).filter(asin -> {
                asin = asin.trim();
                if (StringUtils.isBlank(asin)) {
                    return false;
                }
                if (!shopAsinMap.containsKey(item.getShopId())) {
                    return false;
                }
                Set<String> asinSet = shopAsinMap.get(item.getShopId());
                return asinSet.contains(asin);
            }).distinct().collect(Collectors.toList());
            item.setAsinList(list);
        });
        return asinsGroup.stream().filter(item -> CollectionUtils.isNotEmpty(item.getAsinList())).collect(Collectors.toList());
    }

    private Map<Integer, Set<String>> buildShopAsinMap(MultiShopGroupByProductParam param, List<OdsProduct> productList,
                                                       Function<OdsProduct, String> valueExtractor) {
        if (CollectionUtils.isEmpty(productList)) {
            return Collections.emptyMap();
        }
        if (StringUtils.equalsIgnoreCase(param.getContainType(), MultiShopGroupByProductParam.CONTAIN_ALL)) {
            // 包含所有的场景，需要保证店铺下有所有的推广产品
            Map<Integer, Set<String>> map = StreamUtil.groupingByToSetValue(productList, OdsProduct::getShopId, valueExtractor);
            map.entrySet().removeIf(next -> CollectionUtils.size(next.getValue()) < CollectionUtils.size(param.getSearchValue()));
            if (MapUtils.isEmpty(map)) {
                return Collections.emptyMap();
            }
            productList.removeIf(i -> !map.containsKey(i.getShopId()));
            if (CollectionUtils.isEmpty(productList)) {
                return Collections.emptyMap();
            }
        }
        param.setProductList(productList);
        param.setShopIds(productList.stream().map(OdsProduct::getShopId).distinct().collect(Collectors.toList()));
        return StreamUtil.groupingByToSetValue(productList, OdsProduct::getShopId, OdsProduct::getAsin);
    }
}
