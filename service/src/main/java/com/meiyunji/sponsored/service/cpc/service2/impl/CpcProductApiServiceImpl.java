package com.meiyunji.sponsored.service.cpc.service2.impl;

import com.alibaba.fastjson.JSON;
import com.amazon.advertising.constants.ProductEligibilityOverallStatusEnum;
import com.amazon.advertising.spV3.product.ProductEligibilityResponse;
import com.amazon.advertising.spV3.product.ProductSpV3Client;
import com.amazon.advertising.spV3.product.entity.ProductEligibilityDetail;
import com.meiyunji.sponsored.common.config.DynamicRefreshNacosConfiguration;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.account.service.IShopAuthService;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdProfileDao;
import com.meiyunji.sponsored.service.cpc.dto.CpcProductDto;
import com.meiyunji.sponsored.service.cpc.dto.ProductStatusDto;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdProfile;
import com.meiyunji.sponsored.service.cpc.service2.ICpcProductApiService;
import com.meiyunji.sponsored.service.cpc.util.CpcApiHelper;
import com.meiyunji.sponsored.service.doris.po.OdsProduct;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: liweibin
 * @email: <EMAIL>
 * @date: 2024-07-09  11:17
 */
@Service
@Slf4j
public class CpcProductApiServiceImpl implements ICpcProductApiService {
    @Autowired
    private IScVcShopAuthDao shopAuthDao;
    @Autowired
    private IAmazonAdProfileDao amazonAdProfileDao;
    @Autowired
    private DynamicRefreshNacosConfiguration dynamicRefreshNacosConfiguration;
    @Autowired
    private IShopAuthService shopAuthService;
    @Autowired
    private CpcApiHelper cpcApiHelper;

    @Override
    public List<ProductStatusDto> productEligibility(Integer puid, Integer shopId, String adType, String locale, List<CpcProductDto> productList) {
        List<ProductStatusDto> dtoList = new ArrayList<>();
        if (CollectionUtils.isEmpty(productList)) {
            log.error("product eligibility productList null");
            return dtoList;
        }
        ShopAuth shopAuth = shopAuthDao.getScAndVcByIdAndPuid(shopId, puid);
        if (shopAuth == null) {
            log.error("product eligibility shop null");
            return dtoList;
        }
        AmazonAdProfile profile = amazonAdProfileDao.getProfile(puid, shopId);
        if (profile == null) {
            log.error("product eligibility profile null");
            return dtoList;
        }
        //构建请求对象
        List<ProductEligibilityDetail> productEligibilityDetails = productList.stream().map(e -> {
            ProductEligibilityDetail productEligibilityDetail = new ProductEligibilityDetail();
            productEligibilityDetail.setAsin(e.getAsin());
            productEligibilityDetail.setSku(e.getSku());
            return productEligibilityDetail;
        }).collect(Collectors.toList());
        //获取token
        String accessToken = shopAuthService.getAdToken(shopAuth);
        ProductEligibilityResponse response = ProductSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable())
                .productEligibility(accessToken, profile.getProfileId(), shopAuth.getMarketplaceId(), adType, locale, productEligibilityDetails);
        //解析返回数据
        if (response != null && response.getStatusCode() != null && response.getStatusCode() == 200
                && response.getData() != null && CollectionUtils.isNotEmpty(response.getData().getProductResponseList())) {
            response.getData().getProductResponseList().stream()
                    .filter(e -> ProductEligibilityOverallStatusEnum.INELIGIBLE.getStatus().equals(e.getOverallStatus()) && e.getProductDetails() != null)
                    .forEach(e -> {
                        ProductStatusDto dto = new ProductStatusDto();
                        dto.setAsin(e.getProductDetails().getAsin());
                        dto.setSku(e.getProductDetails().getSku());
                        dtoList.add(dto);
                    });
        }
        return dtoList;
    }

    @Override
    public List<ProductStatusDto> getProductEligibility(ShopAuth shop, AmazonAdProfile profile, String adType, String locale, List<OdsProduct> productList) {
        List<ProductStatusDto> dtoList = new ArrayList<>();
        if (CollectionUtils.isEmpty(productList)) {
            return dtoList;
        }
        //构建请求对象
        List<ProductEligibilityDetail> productEligibilityDetails = productList.stream().map(e -> {
            ProductEligibilityDetail productEligibilityDetail = new ProductEligibilityDetail();
            productEligibilityDetail.setAsin(e.getAsin());
            productEligibilityDetail.setSku(e.getSku());
            return productEligibilityDetail;
        }).collect(Collectors.toList());

        ProductEligibilityResponse response;
        response = cpcApiHelper.call(shop, () -> ProductSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).productEligibility(shopAuthService.getAdToken(shop),
            profile.getProfileId(), shop.getMarketplaceId(), adType, locale, productEligibilityDetails));

        //处理请求结果
        if (response == null) {
            log.error("Calling the Amazon interface:'/eligibility/product/list' response is null， puid:{},shopId:{},adType:{},productList{}", shop.getPuid(), shop.getId(), adType, productList);
            return dtoList;
        }
        log.info("Calling the Amazon interface:'/eligibility/product/list', req: puid:{}, shopId:{}, adType:{}, locale:{}, productEligibilityDetails:{}, resp: {}", shop.getPuid(), shop.getId(), adType, locale, productEligibilityDetails, JSON.toJSONString(response));
        if (response.getStatusCode() != 200) {
            if (response.getError() != null) {
                String errMsg = StringUtils.isNotBlank(response.getError().getMessage()) ? response.getError().getMessage() : StringUtils.isNotBlank(response.getError().getDetails()) ? response.getError().getDetails() : "null";
                log.error("Calling the Amazon interface:'/eligibility/product/list' code:{}，error:{}， puid:{},shopId{},adType:{},productList{}", response.getStatusCode(), errMsg, shop.getPuid(), shop.getId(), adType, productList);
            } else {
                log.error("Calling the Amazon interface:'/eligibility/product/list' code:{}， puid:{},shopId{},adType:{},productList{}", response.getStatusCode(), shop.getPuid(), shop.getId(), adType, productList);
            }
            return dtoList;
        }
        if (response.getError() != null) {
            log.error("Calling the Amazon interface:'/eligibility/product/list' error:{}， puid:{},shopId{},adType:{},productList{}", response.getError().getMessage(), shop.getPuid(), shop.getId(), adType, productList);
            return dtoList;
        }
        if (CollectionUtils.isNotEmpty(response.getData().getProductResponseList())) {
            response.getData().getProductResponseList()
                .forEach(e -> {
                    if (e.getProductDetails() != null) {
                        ProductStatusDto dto = new ProductStatusDto();
                        dto.setAsin(e.getProductDetails().getAsin());
                        dto.setSku(e.getProductDetails().getSku());
                        dto.setStatus(e.getOverallStatus());
                        dtoList.add(dto);
                    }
                });
            return dtoList;
        }
        log.error("Calling the Amazon interface:'/eligibility/product/list' getProductResponseList is empty， puid:{},shopId{},adType:{},productList{}", shop.getPuid(), shop.getId(), adType, productList);
        return dtoList;
    }
}
