package com.meiyunji.sponsored.service.productPerspectiveAnalysis.service;

import com.meiyunji.sponsored.service.productPerspectiveAnalysis.qo.DiagnoseCountParam;

import java.util.List;
import java.util.Map;

/**
 * @author: sun<PERSON><PERSON>
 * @email: sunin<PERSON>@dianxiaomi.com
 * @date: 2023-10-18  20:27
 */
public interface IDiagnoseCountService {

    public Map<Long, Integer> getDiagnoseCount4Campaign(List<DiagnoseCountParam> diagnoseCountParamList);

    Map<Long, Integer> getDiagnoseCount4Keyword(List<DiagnoseCountParam> diagnoseCountParamList);


    Map<Long, Integer> getDiagnoseCount4ManualTargeting(List<DiagnoseCountParam> diagnoseCountParamList);

    Map<Long, Integer> getDiagnoseCount4AutoTargeting(List<DiagnoseCountParam> diagnoseCountParamList);

    Map<Long, Integer> getDiagnoseCount4Placement(List<DiagnoseCountParam> diagnoseCountParamList);

    Map<Long, Integer> getDiagnoseCount4SearchTerms(List<DiagnoseCountParam> diagnoseCountParamList);

    Map<Long, Integer> getAllTypeDiagnoseCount4SearchTerms(List<DiagnoseCountParam> diagnoseCountParamList);

    Map<Long, Integer> getAllTypeDiagnoseCount4Keyword(List<DiagnoseCountParam> diagnoseCountParamList, String adType);

    Map<Long, Integer> getAllTypeDiagnoseCount4ManualTargeting(List<DiagnoseCountParam> diagnoseCountParamList, String adType, boolean isAsinTarget);
}
