package com.meiyunji.sponsored.service.syncTask.amc.reportStrategy;

import com.meiyunji.sellfox.aadas.types.message.notification.AmcReadyNotification;
import com.meiyunji.sponsored.service.amc.enums.AmcMatchTypeEnum;
import com.meiyunji.sponsored.service.amc.enums.AmcTargetingTypeEnum;
import com.meiyunji.sponsored.service.amc.enums.AmcThemeEnum;
import com.meiyunji.sponsored.service.config.CosBucketClient;
import com.meiyunji.sponsored.service.syncTask.amc.dto.AmcTargetingParseDto;
import com.meiyunji.sponsored.service.util.LocalDateTimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: sunlinfeng
 * @email: <EMAIL>
 * @date: 2025-02-14  13:40
 */

@Component
@Slf4j
public abstract class AmcReportProcessor<T> {

    @Autowired
    protected CosBucketClient dataBucketClient;

    protected static final DateTimeFormatter formatter = DateTimeFormatter.ofPattern(LocalDateTimeUtil.YMD_DATE_FORMAT);

    /**
     * 下载和处理amc报告、解析入库
     * @param message
     */
    public void parseAndSaveAmcReport(AmcReadyNotification message) {
        int puid = Integer.parseInt(message.getShopInfoList().get(0).getSellerIdentifier());
        //检查数据，若有必要
        checkData(puid, message);

        //下载并保存数据
        for (String cosPath : message.getCosPaths()) {
            try {
                //下载
                byte[] csvBytes = dataBucketClient.getObjectToBytes(cosPath);

                //转化为实体
                String startTime = message.getStartTime();
                Map<String, AmcReadyNotification.ShopInfo> entityShopMap = message.getShopInfoList().stream().collect(Collectors.toMap(AmcReadyNotification.ShopInfo::getEntityId, shop -> shop, (a, b) -> b));
                List<T> dataList = parseCsvBytesToObj(puid, csvBytes, startTime, entityShopMap);
                if (CollectionUtils.isEmpty(dataList)) {
                    log.error("amc report data parse empty, puid: {}, path: {}", puid, cosPath);
                    continue;
                }

                //处理数据
                processData(dataList);

                //保存数据
                saveData(dataList);

            } catch (IOException e) {
                log.error("amc report download error, puid: {}, path: {}", puid, cosPath);
            }
        }

    }

    /**
     * 将下载的csv一行行转为数据库实体对象集合
     * @param puid
     * @param csvBytes
     * @param countDate
     * @param entityShopMap
     * @return
     */
    public List<T> parseCsvBytesToObj(int puid, byte[] csvBytes, String countDate, Map<String, AmcReadyNotification.ShopInfo> entityShopMap) {
        String csvContent = new String(csvBytes, StandardCharsets.UTF_8);
        List<T> objList = new ArrayList<>();
        // 按行分割 CSV 内容
        String[] lines = csvContent.split("\n");
        for (int i = 1; i < lines.length; i++) {
            // 根据逗号拆分多列 双引号间逗号不分割
            List<String> values = splitByCommaIgnoringQuotes(lines[i]);
            try {
                T t = parseCsvLine2Obj(puid, values.toArray(new String[0]), countDate, entityShopMap);
                if (Objects.nonNull(t)) {
                    objList.add(t);
                }
            } catch (Exception e) {
                log.error("amc report line data error, puid: {}, line data: {}", puid, values);
            }
        }
        return objList;
    }

    public static List<String> splitByCommaIgnoringQuotes(String input) {
        List<String> result = new ArrayList<>();
        StringBuilder current = new StringBuilder();
        boolean inQuotes = false;

        for (int i = 0; i < input.length(); i++) {
            char c = input.charAt(i);
            if (c == '\\') {
                // 如果遇到反斜杠，说明可能是转义字符，直接添加反斜杠和下一个字符
                current.append(c);
                if (i + 1 < input.length()) {
                    current.append(input.charAt(++i));
                }
            } else if (c == '"') {
                // 遇到双引号，切换引号状态
                inQuotes = !inQuotes;
            } else if (c == ',' && !inQuotes) {
                // 如果不在引号内且遇到逗号，将当前部分添加到结果列表并清空
                result.add(current.toString().trim());
                current.setLength(0);
            } else {
                // 其他字符添加到当前部分
                current.append(c);
            }
        }
        // 添加最后一部分
        result.add(current.toString().trim());
        return result;
    }

    /**
     * 把csv中的一行数据进行校验并转为对应的数据库实体对象，如果校验不通过可返回空，或抛出异常，需要子类实现
     * @param puid
     * @param values
     * @param countDate
     * @param entityShopMap
     * @return
     */
    protected abstract T parseCsvLine2Obj(int puid, String[] values, String countDate, Map<String, AmcReadyNotification.ShopInfo> entityShopMap);

    /**
     * 把实体数据写入数据库，需要子类实现
     * @param dataList
     */
    protected abstract void saveData(List<T> dataList);

    /**
     * 转换完数据库实体对象集合后，对数据进行进一步的处理和补充，如有需要则覆盖
     * @param dataList
     * @return
     */
    protected List<T> processData(List<T> dataList) {
        return dataList;
    }

    /**
     * 下载前对消息的就检查，如有需要则覆盖
     * @param puid
     * @param message
     * @return
     */
    protected boolean checkData(int puid, AmcReadyNotification message) {
        return true;
    }

    /**
     * 根据amc源匹配类型和投放表达式解析出投放类型、匹配类型、投放值等，如果无法解析则返回null
     * @param sourceMatchType
     * @param sourceTarget
     * @return
     */
    protected AmcTargetingParseDto getTargetingAndMatchType(String sourceMatchType, String sourceTarget) {

        //投放类型：商品、自动、关键词
        AmcTargetingTypeEnum targetingTypeEnum = AmcTargetingTypeEnum.codeMap.getOrDefault(sourceMatchType, null);
        if (Objects.isNull(targetingTypeEnum)) {
            return null;
        }

        AmcTargetingParseDto parseDto = new AmcTargetingParseDto();
        parseDto.setTargetingType(targetingTypeEnum.getType());

        //自动
        if (AmcTargetingTypeEnum.AUTO.getType().equals(parseDto.getTargetingType())) {
            AmcMatchTypeEnum autoMatchTypeEnum = AmcMatchTypeEnum.autoTypeMap.getOrDefault(sourceTarget, null);
            if (Objects.isNull(autoMatchTypeEnum)) {
                return null;
            }
            parseDto.setMatchType(sourceTarget);
            parseDto.setTargetingText(autoMatchTypeEnum.getType());
            return parseDto;
        }

        //关键词
        if (AmcTargetingTypeEnum.KEYWORD.getType().equals(parseDto.getTargetingType())) {
            //关键词
            String lowerSourceMatchType = sourceMatchType.toLowerCase(Locale.ROOT);
            if (!AmcMatchTypeEnum.keywordTypeSet.contains(lowerSourceMatchType)) {
                return null;
            }
            parseDto.setMatchType(lowerSourceMatchType);
            parseDto.setTargetingText(sourceTarget);
            if(lowerSourceMatchType.equals(AmcMatchTypeEnum.keyword_theme.getType())){
                // sb主题投放
                AmcThemeEnum amcThemeEnum = AmcThemeEnum.getBySbKeywordText(sourceTarget);
                if(amcThemeEnum != null){
                    parseDto.setTargetingText(amcThemeEnum.getCode());
                }
            }
            return parseDto;
        }

        //投放
        if (AmcTargetingTypeEnum.TARGET.getType().equals(parseDto.getTargetingType())) {
            // 包含keyword-group 代表关键词主题投放
            AmcThemeEnum amcThemeEnum = AmcThemeEnum.getBySpKeywordText(sourceTarget);
            if(amcThemeEnum != null){
                parseDto.setMatchType(AmcMatchTypeEnum.keyword_theme.getType());
                parseDto.setTargetingType(AmcTargetingTypeEnum.KEYWORD.getType());
                parseDto.setTargetingText(amcThemeEnum.getCode());
                return parseDto;
            }else{
                // 商品投放
                boolean correctTarget = false;
                for (AmcMatchTypeEnum x : Arrays.asList(AmcMatchTypeEnum.target_category, AmcMatchTypeEnum.target_asin_explanded, AmcMatchTypeEnum.target_asin)) {
                    if (sourceTarget.contains(x.getType())) {
                        correctTarget = true;
                        parseDto.setMatchType(x.getType());
                        break;
                    }
                }
                if (correctTarget) {
                    String targetingText = StringUtils.substringBetween(sourceTarget, "\\\"", "\\\"");
                    parseDto.setTargetingText(targetingText);
                    return parseDto;
                }
            }
        }

        return null;
    }

}
