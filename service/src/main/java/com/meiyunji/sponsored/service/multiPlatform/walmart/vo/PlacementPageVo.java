package com.meiyunji.sponsored.service.multiPlatform.walmart.vo;

import lombok.Data;

/**
 * @author: liwei<PERSON>
 * @email: <EMAIL>
 * @date: 2025-02-25  13:49
 */
@Data
public class PlacementPageVo extends PlacementPageAggregateVo {
    //报告广告位
    private String placement;
    //DYNAMIC动态、FIXED固定
    private String biddingStrategy;
    //店铺id
    private Integer shopId;
    //店铺名称
    private String shopName;
    //广告活动id
    private String campaignId;
    //广告活动名称
    private String campaignName;
    //投放类型
    private String targetingType;
    //竞价倍数
    private String multiplier;
    //是否能编辑竞价倍数
    private boolean multiplierEditable;
}
