package com.meiyunji.sponsored.service.cpc.service2.sd.impl;

import com.amazon.advertising.sb.entity.pageAisn.PageAsinsResult;
import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.exception.ServiceException;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.common.util.JSONUtil;
import com.meiyunji.sponsored.common.util.ResultUtil;
import com.meiyunji.sponsored.common.util.StringUtil;
import com.meiyunji.sponsored.rpc.adCommon.ProductAddResponse;
import com.meiyunji.sponsored.rpc.sd.campaign.SDCreateCampaignNewRequest;
import com.meiyunji.sponsored.rpc.sd.campaign.SdProductVo;
import com.meiyunji.sponsored.rpc.sd.product.CreateSdProductNewRequest;
import com.meiyunji.sponsored.rpc.sd.product.ProductResponse;
import com.meiyunji.sponsored.rpc.vo.ProductNewRpcVo;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.cpc.dao.*;
import com.meiyunji.sponsored.service.cpc.po.*;
import com.meiyunji.sponsored.service.cpc.service2.impl.CpcShopDataService;
import com.meiyunji.sponsored.service.cpc.service2.sb.impl.CpcSbPageAsinsApiService;
import com.meiyunji.sponsored.service.cpc.service2.sd.ICpcSdAdProductService;
import com.meiyunji.sponsored.service.cpc.service2.sp.impl.CpcCommService;
import com.meiyunji.sponsored.service.cpc.util.CpcStatusEnum;
import com.meiyunji.sponsored.service.cpc.vo.*;
import com.meiyunji.sponsored.service.doris.po.OdsAmazonAdProductSd;
import com.meiyunji.sponsored.service.doris.service.IDorisService;
import com.meiyunji.sponsored.service.enums.*;
import com.meiyunji.sponsored.service.dataWarehouse.statsDo.ShopSaleDto;
import com.meiyunji.sponsored.service.log.enums.OperationLogResultEnum;
import com.meiyunji.sponsored.service.log.po.AdManageOperationLog;
import com.meiyunji.sponsored.service.log.service.IAdManageOperationLogService;
import com.meiyunji.sponsored.service.util.AmazonErrorUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> on 2021/7/6
 */
@Service
@Slf4j
public class CpcSdAdProductServiceImpl implements ICpcSdAdProductService {

    @Autowired
    private IAmazonSdAdProductDao amazonSdAdProductDao;
    @Autowired
    private IAmazonAdSdProductReportDao amazonAdSdProductReportDao;
    @Autowired
    private IAmazonSdAdGroupDao amazonSdAdGroupDao;
    @Autowired
    private IScVcShopAuthDao shopAuthDao;
    @Autowired
    private CpcShopDataService cpcShopDataService;
    @Autowired
    private CpcCommService cpcCommService;
    @Autowired
    private IAmazonAdProfileDao amazonAdProfileDao;
    @Autowired
    private CpcSdProductApiService cpcSdProductApiService;
    @Autowired
    private IAdManageOperationLogService manageOperationLogService;
    @Autowired
    private IDorisService dorisService;
    @Autowired
    private IAmazonSdAdCampaignDao amazonSdAdCampaignDao;
    @Autowired
    private CpcSbPageAsinsApiService pageAsinsApiService;

    /**
     * 添加广告产品-SD
     * @param vo
     */
    @Override
    public Result addProduct(AddAdProductVo vo) {
        int puid = vo.getPuid();
        int shopId = vo.getShopId();

        AmazonSdAdGroup amazonSdAdGroup = amazonSdAdGroupDao.getByGroupId(puid, shopId, vo.getGroupId());
        if (amazonSdAdGroup == null) {
            buildProductAddFailInfo(vo, "没有广告组信息");
            return ResultUtil.returnErr("没有广告组信息");
        }

        Map<String, ProductVo> productVoMap = vo.getProducts().stream()
                .filter(e -> StringUtils.isNotBlank(e.getSku())).collect(Collectors.toMap(ProductVo::getSku, e -> e, (p1, p2) -> p1));

        // 过滤掉已经添加的
        amazonSdAdProductDao.listBySku(puid, shopId, vo.getGroupId(), productVoMap.keySet()).forEach((e) -> {
            productVoMap.remove(e.getSku());
            buildProductAddFailInfo(vo, Lists.newArrayList(e.getSku()), "目标广告组 " + amazonSdAdGroup.getName() + " 中已有该MSKU，无法添加");
        });

        if (productVoMap.size() == 0) {
            return ResultUtil.success();
        }

        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(shopId, puid);
        if (shop == null) {
            buildProductAddFailInfo(vo, "没有CPC授权");
            return ResultUtil.returnErr("没有CPC授权");
        }

        AmazonAdProfile profile = amazonAdProfileDao.getProfile(puid, shopId);
        if (profile == null) {
            buildProductAddFailInfo(vo, "没有站点对应的配置信息");
            return ResultUtil.returnErr("没有站点对应的配置信息");
        }

        // vo -> po
        List<ProductVo> productVos = Lists.newArrayList(productVoMap.values());
        List<AmazonSdAdProduct> amazonSdAdProducts = productVos.stream().map(e -> {
            AmazonSdAdProduct amazonSdAdProduct = new AmazonSdAdProduct();
            amazonSdAdProduct.setPuid(puid);
            amazonSdAdProduct.setShopId(shopId);
            amazonSdAdProduct.setMarketplaceId(amazonSdAdGroup.getMarketplaceId());
            amazonSdAdProduct.setProfileId(amazonSdAdGroup.getProfileId());
            amazonSdAdProduct.setCampaignId(amazonSdAdGroup.getCampaignId());
            amazonSdAdProduct.setAdGroupId(amazonSdAdGroup.getAdGroupId());
            amazonSdAdProduct.setState(CpcStatusEnum.enabled.name());
            amazonSdAdProduct.setSku(e.getSku());
            amazonSdAdProduct.setAsin(e.getAsin());
            amazonSdAdProduct.setCreateInAmzup(1);
            amazonSdAdProduct.setCreateId(vo.getUid());
            return amazonSdAdProduct;
        }).collect(Collectors.toList());
        Result result = cpcSdProductApiService.create(shop, profile, amazonSdAdProducts);
        if (!result.success()) {
            buildProductAddFailInfo(vo, result.getMsg());
            return ResultUtil.returnErr(result.getMsg());
        }

        List<AmazonSdAdProduct> succList = amazonSdAdProducts.stream().filter(e -> StringUtils.isNotBlank(e.getAdId())).collect(Collectors.toList());
        if (succList.size() > 0) {
            // 有可能已经添加过了:重复添加同一个asin，接口不会报错，会返回其对应的targetId
            List<String> existInDB = amazonSdAdProductDao.listByAdId(puid, shopId, succList.stream()
                    .map(AmazonSdAdProduct::getAdId).collect(Collectors.toList())).stream().map(AmazonSdAdProduct::getAdId).collect(Collectors.toList());

            // 排除掉已有的
            if (CollectionUtils.isNotEmpty(existInDB)) {
                succList = succList.stream().filter(e -> !existInDB.contains(e.getAdId())).collect(Collectors.toList());
            }

            // 入库
            try {
                amazonSdAdProductDao.batchAdd(puid, succList);
                //写入doris
                saveDoris(succList, true, true);
            } catch (Exception e) {
                log.error("createSdCampaign:", e);
                buildProductAddFailInfo(vo, "系统异常");
                return ResultUtil.returnErr("系统异常");
            }
        }
        /**
         * 写入日志
         * start
         */
        List<AdManageOperationLog> succLog = succList.stream().map(e -> {
            AdManageOperationLog productLog = manageOperationLogService.getSdProductLog(null, e);
            productLog.setIp(vo.getLoginIp());
            productLog.setResult(0);
            return productLog;
        }).collect(Collectors.toList());
        manageOperationLogService.batchLogsMergeByAdGroup(succLog);
        // 没有失败的就代表全部成功了
        List<AmazonSdAdProduct> failList = amazonSdAdProducts.stream().filter(e -> StringUtils.isBlank(e.getAdId())).collect(Collectors.toList());
        if (failList.size() == 0) {
            return ResultUtil.success();
        }
        Map<String, String> errMap = new HashMap<>();
        failList.forEach((e) -> errMap.put(e.getSku(), e.getErrMsg()));
        failList.forEach(e -> buildProductAddFailInfo(vo, Lists.newArrayList(e.getSku()), e.getErrMsg()));
        String finalErrMsg;
        if (shop.getType().equals(ShopTypeEnum.VC.getCode())) {
            finalErrMsg = failList.stream().map(e -> e.getAsin() + ":" + e.getErrMsg()).collect(Collectors.joining(";"));
        } else {
            finalErrMsg = failList.stream().map(e -> e.getSku() + ":" + e.getErrMsg()).collect(Collectors.joining(";"));
        }

        collectFailedLog(failList, finalErrMsg, vo.getLoginIp());
        return ResultUtil.returnErr("以下广告创建失败了: " + JSONUtil.objectToJson(errMap));
    }

    private void buildProductAddFailInfo(AddAdProductVo addAdProductVo, String errorMsg) {
        try {
            if (Objects.isNull(addAdProductVo.getFailInfoList())) {
                return;
            }
            addAdProductVo.getProducts().forEach(e -> {
                ProductAddResponse.AddFailInfo failInfo = ProductAddResponse.AddFailInfo.newBuilder()
                        .setAsin(e.getAsin())
                        .setMsku(e.getSku())
                        .setMsg(errorMsg)
                        .build();
                addAdProductVo.getFailInfoList().add(failInfo);
            });
        } catch (Exception e) {
            log.error("sp添加广告产品，失败信息记录 addAdProductVo {}, {}", addAdProductVo, errorMsg, e);
        }
    }

    private void buildProductAddFailInfo(AddAdProductVo addAdProductVo, List<String> skus, String errorMsg) {
        try {
            if (Objects.isNull(addAdProductVo.getFailInfoList())) {
                return;
            }
            // 需要记录失败信息的场景，只会传一个asin
            String asin = addAdProductVo.getProducts().get(0).getAsin();
            skus.forEach(sku -> {
                ProductAddResponse.AddFailInfo failInfo = ProductAddResponse.AddFailInfo.newBuilder()
                        .setAsin(asin)
                        .setMsku(sku)
                        .setMsg(errorMsg)
                        .build();
                addAdProductVo.getFailInfoList().add(failInfo);
            });
        } catch (Exception e) {
            log.error("sp添加广告产品，失败信息记录 addAdProductVo {}, skus={}, {}", addAdProductVo, skus, errorMsg, e);
        }
    }

    @Override
    public Result<List<AmazonSdAdProduct>> addProductNew(ShopAuth shop, AmazonAdProfile profile,
                                                                 String campaignId, String groupId,
                                                                 CreateSdProductNewRequest request) {
        //校验入参pageType的正确性
        //校验groupId是否存在，如果入参campaignId为null，通过groupId反查获得campaignId
        //根据pageType判断landingPageUrl在广告组中是否唯一
        //根据pageType判断landingPageUrl在广告活动中是否存在其他类型广告产品
        if (Objects.isNull(shop) || Objects.isNull(profile)) {
            log.error("create sd product, shop or profile info is null, request:{}", request);
            throw new ServiceException("对应的店铺信息和配置信息不能为空");
        }
        if (StringUtils.isEmpty(groupId)) {
            log.error("create sd product, group id is null, request:{}", request);
            throw new ServiceException("广告组Id不能为空");
        }
        String pageType = request.getPageType();
        SDAdsLandingPageTypeEnum pageTypeEn = Optional.of(pageType).map(Integer::valueOf)
                .map(SDAdsLandingPageTypeEnum::getSDAdsLandingPageTypeEnumByCode)
                .orElseThrow(() -> new ServiceException("对应的着陆页类型不存在"));

        AmazonSdAdGroup amazonSdAdGroup = amazonSdAdGroupDao.getByGroupId(shop.getPuid(), shop.getId(), groupId);
        if (amazonSdAdGroup == null) {
            log.error("create sd product, group not exist, groupId:{}", groupId);
            throw new ServiceException("广告组不存在");
        }

        if (StringUtils.isEmpty(campaignId)) {
            campaignId = amazonSdAdGroup.getCampaignId();
        }
        //查询对应的广告活动
        AmazonAdCampaignAll campaignInfo = amazonSdAdCampaignDao.getByCampaignId(shop.getPuid(), shop.getId(), campaignId);
        if (campaignInfo == null) {
            log.error("create sd product, campaign not exist, campaignId:{}", campaignId);
            throw new ServiceException("广告活动不存在");
        }
        Map<String, ProductNewRpcVo> productVoMap = request.getProductList().parallelStream()
                .filter(p -> StringUtils.isNotBlank(p.getSku())).collect(Collectors.toMap(ProductNewRpcVo::getSku, e -> e, (old, current) -> current));

        // 过滤掉已经添加的
        List<AmazonSdAdProduct> existSku = amazonSdAdProductDao.listBySku(shop.getPuid(), shop.getId(), groupId, productVoMap.keySet());
        if (CollectionUtils.isNotEmpty(existSku)) {
            existSku.parallelStream().forEach(p -> productVoMap.remove(p.getSku()));
        }
        if (productVoMap.isEmpty()) {
            return ResultUtil.success();
        }

        // vo -> po
        List<ProductNewRpcVo> productVos = Lists.newArrayList(productVoMap.values());
        List<AmazonSdAdProduct> amazonSdAdProducts = productVos.stream().map(p -> {
            AmazonSdAdProduct amazonSdAdProduct = new AmazonSdAdProduct();
            amazonSdAdProduct.setPuid(shop.getPuid());
            amazonSdAdProduct.setShopId(shop.getId());
            amazonSdAdProduct.setMarketplaceId(amazonSdAdGroup.getMarketplaceId());
            amazonSdAdProduct.setProfileId(amazonSdAdGroup.getProfileId());
            amazonSdAdProduct.setCampaignId(campaignInfo.getCampaignId());
            amazonSdAdProduct.setAdGroupId(amazonSdAdGroup.getAdGroupId());
            amazonSdAdProduct.setState(CpcStatusEnum.enabled.name());
            amazonSdAdProduct.setCreateInAmzup(1);
            if (SDAdsLandingPageTypeEnum.STORE == pageTypeEn) {
                amazonSdAdProduct.setLandingPageType(pageTypeEn.getType());
                Optional.of(p.getLandingPageURL()).ifPresent(amazonSdAdProduct::setLandingPageURL);
                Optional.of(p.getAdName()).ifPresent(amazonSdAdProduct::setAdName);//adName只有落地页为旗舰店时生效
            } else {
                amazonSdAdProduct.setSku(p.getSku());
            }
            return amazonSdAdProduct;
        }).collect(Collectors.toList());

        if (SDAdsLandingPageTypeEnum.STORE == pageTypeEn) {
            //如果本次请求的pageType为landingPageType = store 那么在本广告组中只允许存在一个对应的广告产品
            //在其中先获取广告组下是否存在广告产品，如果已经存在则返回，如果不存在，则添加。无需添加分布式锁，这里直接交给亚马逊保证，相当与如果不符合条件，亚马逊会直接抛出异常
            if (CollectionUtils.isNotEmpty(amazonSdAdProductDao.listValidIdByGroupIds(shop.getPuid(), shop.getId(), Collections.singletonList(groupId)))) {
                throw new ServiceException("该广告组已存在广告产品，无法添加旗舰店类型广告产品");
            }
        }

        //还需要查询该广告活动下是否存在其他类型的广告产品
        //首先查询该广告活动下所有广告组，再通过全部广告组id列表，查询广告产品,do{}while(false)避免if嵌套
        do {
            List<AmazonSdAdGroup> existGroupList = amazonSdAdGroupDao.getByCampaignIds(shop.getPuid(), shop.getId(), Collections.singletonList(campaignId));

            if (CollectionUtils.isEmpty(existGroupList)) break;

            List<String> adGroupList = existGroupList.parallelStream().map(AmazonSdAdGroup::getAdGroupId).collect(Collectors.toList());

            if (CollectionUtils.isEmpty(adGroupList)) break;

            List<AmazonSdAdProduct> productList = amazonSdAdProductDao.listValidIdByGroupIds(shop.getPuid(), shop.getId(), adGroupList);

            if (CollectionUtils.isEmpty(productList)) break;

            if (productList.parallelStream().anyMatch(p -> StringUtils.isNotEmpty(p.getSku()))
                    && SDAdsLandingPageTypeEnum.STORE == pageTypeEn)
                throw new ServiceException("该广告活动下只能存在一种类型的广告产品");

            if (productList.parallelStream().anyMatch(p -> StringUtils.isNotEmpty(p.getLandingPageURL()))
                    && SDAdsLandingPageTypeEnum.DETAIL_PAGE == pageTypeEn)
                throw new ServiceException("该广告活动下只能存在一种类型的广告产品");
        } while (false);


        //开始创建SD广告产品
        Result result = cpcSdProductApiService.createNew(shop, profile, amazonSdAdProducts);

        if (!result.success()) {
            throw new ServiceException(result.getMsg());
        }
        List<AmazonSdAdProduct> succList = amazonSdAdProducts.stream().filter(e -> StringUtils.isNotBlank(e.getAdId())).collect(Collectors.toList());
        if (succList.size() > 0) {
            // 有可能已经添加过了:重复添加同一个asin，接口不会报错，会返回其对应的targetId
            List<AmazonSdAdProduct> existInDB = amazonSdAdProductDao.listByAdId(shop.getPuid(), shop.getId(), succList.stream()
                    .map(AmazonSdAdProduct::getAdId).collect(Collectors.toList()));
            if (CollectionUtils.isNotEmpty(existInDB)) {
                List<String> existIds = existInDB.stream().map(AmazonSdAdProduct::getAdId).collect(Collectors.toList());
                // 排除掉已有的
                if (CollectionUtils.isNotEmpty(existInDB)) {
                    succList = succList.stream().filter(e -> !existIds.contains(e.getAdId())).collect(Collectors.toList());
                }
            }

            // 入库
            try {
                amazonSdAdProductDao.batchAdd(shop.getPuid(), succList);
                //写入doris
                saveDoris(succList, true, true);
            } catch (Exception e) {
                log.error("create sd product error:", e);
                throw new ServiceException("系统异常");
            }
        }
        /**
         * 写入日志
         * start
         */
        List<AdManageOperationLog> succLog = succList.stream().map(e -> {
            AdManageOperationLog productLog = manageOperationLogService.getSdProductLog(null, e);
            productLog.setIp(request.getLoginIp());
            productLog.setResult(0);
            return productLog;
        }).collect(Collectors.toList());
        manageOperationLogService.batchLogsMergeByAdGroup(succLog);
        // 没有失败的就代表全部成功了
        List<AmazonSdAdProduct> failList = amazonSdAdProducts.stream().filter(e -> StringUtils.isBlank(e.getAdId())).collect(Collectors.toList());

        if (failList.isEmpty()) {
            return ResultUtil.success(amazonSdAdProducts);
        }
        Map<String, String> errMap = new HashMap<>();
        failList.forEach((e) -> errMap.put(e.getSku(), e.getErrMsg()));
        String finalErrMsg;
        if (shop.getType().equals(ShopTypeEnum.VC.getCode())) {
            finalErrMsg = failList.stream().map(e -> e.getAsin() + ":" + e.getErrMsg()).collect(Collectors.joining(";"));
        } else {
            finalErrMsg = failList.stream().map(e -> e.getSku() + ":" + e.getErrMsg()).collect(Collectors.joining(";"));
        }

        collectFailedLog(failList, finalErrMsg, request.getLoginIp());
        return ResultUtil.error(finalErrMsg);
    }

    @Override
    public NewCreateResultResultVo<SBCommonErrorVo> createProductNew(ShopAuth shop, AmazonAdProfile profile,
                                                                     String campaignId, String groupId,
                                                                     String profileId, SdProductVo request,
                                                                     String loginIp, Integer uid) {
        //校验入参pageType的正确性
        //校验groupId是否存在，如果入参campaignId为null，通过groupId反查获得campaignId
        //根据pageType判断landingPageUrl在广告组中是否唯一
        //根据pageType判断landingPageUrl在广告活动中是否存在其他类型广告产品
        if (Objects.isNull(shop) || Objects.isNull(profile)) {
            log.error("create sd product, shop or profile info is null, request:{}", request);
            throw new ServiceException("对应的店铺信息和配置信息不能为空");
        }
        if (StringUtils.isEmpty(groupId)) {
            log.error("create sd product, group id is null, request:{}", request);
            throw new ServiceException("广告组Id不能为空");
        }
        String pageType = request.getPageType();
        SDAdsLandingPageTypeEnum pageTypeEn = Optional.of(pageType).map(Integer::valueOf)
                .map(SDAdsLandingPageTypeEnum::getSDAdsLandingPageTypeEnumByCode)
                .orElseThrow(() -> {
                    log.error("create sd product, landingPageType not exist, request:{}", request);
                    return new ServiceException("对应的着陆页类型不存在");});

        List<ProductNewRpcVo> productVos = new ArrayList<>();
        Map<String, ProductNewRpcVo> productVoMap = new HashMap<>();
        if (SDAdsLandingPageTypeEnum.DETAIL_PAGE == pageTypeEn) {
            productVoMap = request.getProductList().parallelStream()
                    .filter(p -> StringUtils.isNotBlank(p.getSku())).collect(Collectors.toMap(ProductNewRpcVo::getSku, e -> e, (old, current) -> current));

            // 过滤掉已经添加的
            List<AmazonSdAdProduct> existSku = amazonSdAdProductDao.listBySku(shop.getPuid(), shop.getId(), groupId, productVoMap.keySet());
            if (CollectionUtils.isNotEmpty(existSku)) {
                for (AmazonSdAdProduct p : existSku) {
                    if (Objects.nonNull(productVoMap.get(p.getSku()))) {
                        productVoMap.remove(p.getSku());
                    }
                }
            }
            if (productVoMap.isEmpty()) {
                NewCreateResultResultVo<SBCommonErrorVo> result = NewCreateResultResultVo.<SBCommonErrorVo>builder()
                        .campaignId(campaignId)
                        .adGroupId(groupId)
                        .productIds(existSku.stream().map(AmazonSdAdProduct::getAdId).collect(Collectors.toList()))
                        .build();
                result.setProductList(existSku.stream().map(p -> {
                    NewCreateProductResultVo vo = new NewCreateProductResultVo();
                    Optional.ofNullable(p.getAdId()).ifPresent(vo::setProductId);
                    Optional.ofNullable(p.getSku()).ifPresent(vo::setSku);
                    return vo;
                }).collect(Collectors.toList()));
                return result;
            }
            productVos.addAll(productVoMap.values());
        }
        if (SDAdsLandingPageTypeEnum.STORE == pageTypeEn) {
            productVos.addAll(request.getProductList());
        }

        // vo -> po
        List<AmazonSdAdProduct> amazonSdAdProducts = productVos.stream().map(p -> {
            AmazonSdAdProduct amazonSdAdProduct = new AmazonSdAdProduct();
            amazonSdAdProduct.setPuid(shop.getPuid());
            amazonSdAdProduct.setShopId(shop.getId());
            amazonSdAdProduct.setMarketplaceId(shop.getMarketplaceId());
            Optional.ofNullable(profileId).ifPresent(amazonSdAdProduct::setProfileId);
            amazonSdAdProduct.setCampaignId(campaignId);
            amazonSdAdProduct.setAdGroupId(groupId);
            amazonSdAdProduct.setState(CpcStatusEnum.enabled.name());
            amazonSdAdProduct.setCreateInAmzup(1);
            if (SDAdsLandingPageTypeEnum.STORE == pageTypeEn) {
                amazonSdAdProduct.setLandingPageType(pageTypeEn.getType());
                Optional.of(p.getLandingPageURL()).ifPresent(amazonSdAdProduct::setLandingPageURL);
                Optional.of(p.getAdName()).ifPresent(amazonSdAdProduct::setAdName);//adName只有落地页为旗舰店时生效
            } else {
                amazonSdAdProduct.setSku(p.getSku());
            }
            amazonSdAdProduct.setCreateId(uid);
            Optional.of(p.getAsin()).filter(StringUtils::isNotEmpty).ifPresent(amazonSdAdProduct::setAsin);
            return amazonSdAdProduct;
        }).collect(Collectors.toList());

        if (SDAdsLandingPageTypeEnum.STORE == pageTypeEn) {
            //如果本次请求的pageType为landingPageType = store 那么在本广告组中只允许存在一个对应的广告产品
            //在其中先获取广告组下是否存在广告产品，如果已经存在则返回，如果不存在，则添加。无需添加分布式锁，这里直接交给亚马逊保证，相当与如果不符合条件，亚马逊会直接抛出异常
            if (CollectionUtils.isNotEmpty(amazonSdAdProductDao.listValidIdByGroupIds(shop.getPuid(), shop.getId(), Collections.singletonList(groupId)))) {
                throw new ServiceException("该广告组已存在广告产品，无法添加旗舰店类型广告产品");
            }
        }

        //还需要查询该广告活动下是否存在其他类型的广告产品
        //首先查询该广告活动下所有广告组，再通过全部广告组id列表，查询广告产品,do{}while(false)避免if嵌套
        do {
            List<AmazonSdAdGroup> existGroupList = amazonSdAdGroupDao.getByCampaignIds(shop.getPuid(), shop.getId(), Collections.singletonList(campaignId));

            if (CollectionUtils.isEmpty(existGroupList)) break;

            List<String> adGroupList = existGroupList.parallelStream().map(AmazonSdAdGroup::getAdGroupId).collect(Collectors.toList());

            if (CollectionUtils.isEmpty(adGroupList)) break;

            List<AmazonSdAdProduct> productList = amazonSdAdProductDao.listValidIdByGroupIds(shop.getPuid(), shop.getId(), adGroupList);

            if (CollectionUtils.isEmpty(productList)) break;

            if (productList.parallelStream().anyMatch(p -> StringUtils.isNotEmpty(p.getSku()))
                    && SDAdsLandingPageTypeEnum.STORE == pageTypeEn)
                throw new ServiceException("该广告活动下只能存在一种类型的广告产品");

            if (productList.parallelStream().anyMatch(p -> StringUtils.isNotEmpty(p.getLandingPageURL()))
                    && SDAdsLandingPageTypeEnum.DETAIL_PAGE == pageTypeEn)
                throw new ServiceException("该广告活动下只能存在一种类型的广告产品");
        } while (false);

        //开始创建SD广告产品
        Result result = cpcSdProductApiService.createNew(shop, profile, amazonSdAdProducts);

        if (!result.success()) {
            throw new ServiceException(Optional.ofNullable(result.getData()).map(Object::toString).orElse(SDCreateErrorEnum.PRODUCT_CREATE_ERROR.getMsg()));
        }
        List<AmazonSdAdProduct> succList = amazonSdAdProducts.stream().filter(e -> StringUtils.isNotBlank(e.getAdId())).collect(Collectors.toList());
        if (succList.size() > 0) {
            // 有可能已经添加过了:重复添加同一个asin，接口不会报错，会返回其对应的targetId
            List<AmazonSdAdProduct> existInDB = amazonSdAdProductDao.listByAdId(shop.getPuid(), shop.getId(), succList.stream()
                    .map(AmazonSdAdProduct::getAdId).collect(Collectors.toList()));
            if (CollectionUtils.isNotEmpty(existInDB)) {
                List<String> existIds = existInDB.stream().map(AmazonSdAdProduct::getAdId).collect(Collectors.toList());
                // 排除掉已有的
                if (CollectionUtils.isNotEmpty(existInDB)) {
                    succList = succList.stream().filter(e -> !existIds.contains(e.getAdId())).collect(Collectors.toList());
                }
            }

            // 入库
            try {
                amazonSdAdProductDao.batchAdd(shop.getPuid(), succList);
                //写入doris
                saveDoris(succList, true, true);
            } catch (Exception e) {
                log.error("create sd product error:", e);
                throw new ServiceException("系统异常");
            }
        }
        /**
         * 写入日志
         * start
         */
        List<AdManageOperationLog> succLog = succList.stream().map(e -> {
            AdManageOperationLog productLog = manageOperationLogService.getSdProductLog(null, e);
            Optional.ofNullable(loginIp).ifPresent(productLog::setIp);
            productLog.setResult(0);
            return productLog;
        }).collect(Collectors.toList());
        manageOperationLogService.batchLogsMergeByAdGroup(succLog);

        NewCreateResultResultVo<SBCommonErrorVo> productResultList = NewCreateResultResultVo.<SBCommonErrorVo>builder()
                .campaignId(campaignId)
                .adGroupId(groupId)
                .build();
        productResultList.setProductList(amazonSdAdProducts.stream().map(p -> {
            NewCreateProductResultVo vo = new NewCreateProductResultVo();
            Optional.ofNullable(p.getAdId()).ifPresent(vo::setProductId);
            Optional.ofNullable(p.getSku()).ifPresent(vo::setSku);
            Optional.ofNullable(p.getLandingPageURL()).ifPresent(vo::setLandingPageURL);
            Optional.ofNullable(p.getAdName()).ifPresent(vo::setAdName);
            return vo;
        }).collect(Collectors.toList()));
        // 没有失败的就代表全部成功了
        List<AmazonSdAdProduct> failList = amazonSdAdProducts.stream().filter(e -> StringUtils.isBlank(e.getAdId())).collect(Collectors.toList());

        if (failList.isEmpty()) {
            return productResultList;
        }
        Map<String, String> errMap = new HashMap<>();
        List<SBCommonErrorVo> errorList = new ArrayList<>();
        failList.forEach((e) -> {
            errMap.put(e.getSku(), e.getErrMsg());
            if (StringUtils.isNotBlank(e.getErrMsg())) {
                errorList.add(SBCommonErrorVo.getErrorVo(e.getSku(), e.getErrMsg()));
            }
        }
        );
        String finalErrMsg = failList.stream().map(e -> e.getSku() + ":" + e.getErrMsg()).collect(Collectors.joining(";"));
        collectFailedLog(failList, finalErrMsg, loginIp);
        log.error("create sd product error:{}", finalErrMsg);

        productResultList.setErrInfoList(errorList);
        return productResultList;
    }

    // 失败的日志
    private void collectFailedLog(List<AmazonSdAdProduct> amazonSdAdProducts, String finalErrMsg, String loginIp) {
        List<AdManageOperationLog> failCollect = amazonSdAdProducts.stream().map(e -> {
            AdManageOperationLog productLog = manageOperationLogService.getSdProductLog(null, e);
            productLog.setIp(loginIp);
            productLog.setResult(1);
            productLog.setResultInfo(finalErrMsg);
            return productLog;
        }).collect(Collectors.toList());
        manageOperationLogService.batchLogsMergeByAdGroup(failCollect);
    }

    @Override
    public Result showAdPerformance(int puid, AdPerformanceParam param) {
        if (param.getShopId() == null
                || StringUtils.isBlank(param.getCpcProductId())) {
            return ResultUtil.returnErr("请求参数错误");
        }

        AmazonSdAdProduct amazonSdAdProduct = amazonSdAdProductDao.getByAdId(puid, param.getShopId(), param.getCpcProductId());
        if (amazonSdAdProduct == null) {
            return ResultUtil.returnErr("请求参数错误");
        }

        // 拼装返回的数据VO
        AdPerformanceVo adPerformanceVo = new AdPerformanceVo();
        adPerformanceVo.setShopId(amazonSdAdProduct.getShopId());
        adPerformanceVo.setAdId(amazonSdAdProduct.getAdId());

        // 初始化每天数据，应前端要求保证日期是连续的
        Map<String, CpcCommPageVo> map = new LinkedHashMap<>();
        adPerformanceVo.setMap(map);
        LocalDate startLocalDate = LocalDate.parse(param.getStartDate(), DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD));
        LocalDate endLocalDate = LocalDate.parse(param.getEndDate(), DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD));
        while (startLocalDate.equals(endLocalDate) || startLocalDate.isBefore(endLocalDate)) {
            map.put(startLocalDate.format(DateTimeFormatter.ofPattern(DateUtil.PATTERN)), new CpcCommPageVo());
            startLocalDate = startLocalDate.plusDays(1);
        }

        List<AmazonAdSdProductReport> reports = amazonAdSdProductReportDao.listReports(puid, param.getShopId(), param.getStartDate(),
                    param.getEndDate(), param.getCpcProductId());

        if (CollectionUtils.isNotEmpty(reports)) {
            // 取店铺销售额
            String start = LocalDate.parse(param.getStartDate(), DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD)).format(DateTimeFormatter.ofPattern(DateUtil.PATTERN));
            String end = LocalDate.parse(param.getEndDate(), DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD)).format(DateTimeFormatter.ofPattern(DateUtil.PATTERN));
            ShopSaleDto shopSaleDto = cpcShopDataService.getShopSaleData(param.getShopId(), start, end);

            Map<String, CpcCommPageVo> resultMap = reports.stream().collect(Collectors.toMap(
                    e -> LocalDate.parse(e.getCountDate(), DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD)).format(DateTimeFormatter.ofPattern(DateUtil.PATTERN)),
                    e -> {
                        CpcCommPageVo campaignPageVo = new CpcCommPageVo();

                        // 填充报告数据
                        cpcCommService.fillReportDataIntoPageVo(campaignPageVo, e.getReportBase(), shopSaleDto);
                        return campaignPageVo;

                    }, (p1, p2) -> p1));

            adPerformanceVo.getMap().putAll(resultMap);
        }

        return ResultUtil.returnSucc(adPerformanceVo);
    }

    @Override
    public Result updateState(Integer puid, Integer shopId, Integer uid, String adId, String state) {

        AmazonSdAdProduct amazonSdAdProduct = amazonSdAdProductDao.getByAdId(puid, shopId, adId);
        if (amazonSdAdProduct == null) {
            return ResultUtil.returnErr("没有广告产品信息");
        }
        AmazonSdAdProduct oldAmazonSdAdProduct = new AmazonSdAdProduct();
        BeanUtils.copyProperties(amazonSdAdProduct,oldAmazonSdAdProduct);
        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(shopId, puid);
        if (shop == null) {
            return ResultUtil.returnErr("没有CPC授权");
        }

        AmazonAdProfile profile = amazonAdProfileDao.getProfile(puid, shopId);
        if (profile == null) {
            return ResultUtil.returnErr("没有站点对应的配置信息");
        }

        amazonSdAdProduct.setState(state);

        Result result = cpcSdProductApiService.update(shop, profile, Lists.newArrayList(amazonSdAdProduct));
        /**
         * 记操作日志
         * start
         */
        amazonSdAdProduct.setUpdateId(uid);
        AdManageOperationLog productLog = manageOperationLogService.getSdProductLog(oldAmazonSdAdProduct, amazonSdAdProduct);
        if (result.success()){
            productLog.setResult(0);
            amazonSdAdProductDao.updateById(puid, amazonSdAdProduct);
            saveDoris(Collections.singletonList(amazonSdAdProduct), false, true);

            // 可能部分成功
            if (StringUtils.isNotBlank(result.getMsg())) {
                productLog.setResult(0);
                productLog.setResultInfo(result.getMsg());
                manageOperationLogService.printAdOperationLog(Lists.newArrayList(productLog));
                return ResultUtil.returnErr(result.getMsg());
            }
        } else {
            productLog.setResult(1);
            productLog.setResultInfo(result.getMsg());
            manageOperationLogService.printAdOperationLog(Lists.newArrayList(productLog));
            return result;
        }
        if (!amazonSdAdProduct.getApiSucc()) {
            manageOperationLogService.printAdOperationLog(Lists.newArrayList(productLog));
            return ResultUtil.error(amazonSdAdProduct.getErrMsg());
        }

        // 入库
        try {
            amazonSdAdProductDao.updateByIdAndPuid(puid, amazonSdAdProduct);
            //写入doris
            saveDoris(Collections.singletonList(amazonSdAdProduct), false, true);
            manageOperationLogService.printAdOperationLog(Lists.newArrayList(productLog));
            return ResultUtil.success();
        } catch (Exception e) {
            log.error("createSdProduct:", e);
        }
        manageOperationLogService.printAdOperationLog(Lists.newArrayList(productLog));
        return ResultUtil.returnErr("系统异常");
    }

    @Override
    public Result archive(Integer puid, Integer shopId, Integer uid, String adId, String loginIp) {
        AmazonSdAdProduct amazonSdAdProduct = amazonSdAdProductDao.getByAdId(puid, shopId, adId);
        if (amazonSdAdProduct == null) {
            return ResultUtil.returnErr("没有广告产品信息");
        }
        AmazonSdAdProduct oldAmazonSdAdProduct = new AmazonSdAdProduct();
        BeanUtils.copyProperties(amazonSdAdProduct,oldAmazonSdAdProduct);
        amazonSdAdProduct.setUpdateId(uid);
        amazonSdAdProduct.setState(CpcStatusEnum.archived.name());
        Result result = cpcSdProductApiService.archive(amazonSdAdProduct);
        /**
         * 记操作日志
         * start
         */
        AdManageOperationLog productLog = manageOperationLogService.getSdProductLog(oldAmazonSdAdProduct, amazonSdAdProduct);
        productLog.setIp(loginIp);
        if (result.success()) {
            productLog.setResult(OperationLogResultEnum.SUCCESS.getResultValue());
            amazonSdAdProduct.setUpdateId(uid);
            amazonSdAdProduct.setState(CpcStatusEnum.archived.name());
            amazonSdAdProductDao.updateByIdAndPuid(puid, amazonSdAdProduct);
            //写入doris
            saveDoris(Collections.singletonList(amazonSdAdProduct), false, true);
        } else {
            productLog.setResult(OperationLogResultEnum.FAIL.getResultValue());
            productLog.setResultInfo(result.getMsg());
        }
        //打印操作日志
        manageOperationLogService.printAdOperationLog(Lists.newArrayList(productLog));
        return result;
    }

    private void convertPoToPageVo(AmazonSdAdProduct amazonAdProduct, AdProductPageVo vo) {
        vo.setShopId(amazonAdProduct.getShopId());
        vo.setCampaignId(amazonAdProduct.getCampaignId());
        vo.setAdGroupId(amazonAdProduct.getAdGroupId());
        vo.setAdId(amazonAdProduct.getAdId());
        vo.setState(amazonAdProduct.getState());
        vo.setAsin(amazonAdProduct.getAsin());
        vo.setSku(amazonAdProduct.getSku());
    }

    @Override
    public Result updateBatchState(List<AmazonSdAdProduct> amazonAdProducts, String ip){
        if(CollectionUtils.isEmpty(amazonAdProducts)){
            return ResultUtil.error("参数错误");
        }
        int puid = amazonAdProducts.get(0).getPuid();
        int uid = amazonAdProducts.get(0).getUpdateId();
        int shopId = amazonAdProducts.get(0).getShopId();

        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(shopId, puid);
        if (shop == null) {
            return ResultUtil.returnErr("没有CPC授权");
        }

        AmazonAdProfile profile = amazonAdProfileDao.getProfile(puid, shopId);
        if (profile == null) {
            return ResultUtil.returnErr("没有站点对应的配置信息");
        }
        List<Long> ids = amazonAdProducts.stream().filter(e -> e.getId() != null).map(AmazonSdAdProduct::getId).collect(Collectors.toList());
        List<AmazonSdAdProduct> amazonSdAdProducts = amazonSdAdProductDao.listByIds(puid, shopId, ids);
        if (amazonSdAdProducts == null) {
            return ResultUtil.returnErr("没有广告产品信息");
        }
        List<AmazonSdAdProduct> updateList = Lists.newArrayList();
        List<AmazonSdAdProduct> errorList = new ArrayList<>();
        Map<Long, AmazonSdAdProduct> amazonSdAdProductMap = amazonSdAdProducts.stream().collect(Collectors.toMap(AmazonSdAdProduct::getId, e -> e));
        for (AmazonSdAdProduct amazonAdProduct: amazonAdProducts) {
            AmazonSdAdProduct oldAmazonSdAdProduct = amazonSdAdProductMap.get(amazonAdProduct.getId());
            if (oldAmazonSdAdProduct == null) {
                amazonAdProduct.setFailReason("对象不存在");
                errorList.add(amazonAdProduct);
                continue;
            }
            if (amazonAdProduct.getState() == null) {

                amazonAdProduct.setFailReason("状态错误");
                errorList.add(amazonAdProduct);
                continue;
            }
            AmazonSdAdProduct amazonSdAdProduct = new AmazonSdAdProduct();
            BeanUtils.copyProperties(oldAmazonSdAdProduct, amazonSdAdProduct);
            amazonSdAdProduct.setState(amazonAdProduct.getState());
            amazonSdAdProduct.setUpdateId(uid);
            updateList.add(amazonSdAdProduct);
        }
        if(CollectionUtils.isEmpty(updateList)){
            BatchResponseVo<AmazonSdAdProduct,AmazonSdAdProduct> data = new BatchResponseVo<>();
            data.setCountNum(amazonAdProducts.size());
            data.setSuccessNum(0);
            data.setFailNum(errorList.size());
            data.setErrorList(errorList);
            return ResultUtil.success(data);
        }

        Result<BatchResponseVo<AmazonSdAdProduct, AmazonSdAdProduct>> result = cpcSdProductApiService.updateReturnErrorList(shop, profile, updateList);
        List<AdManageOperationLog> adManageOperationLogs = Lists.newArrayList();
        if (result.success()) {
            BatchResponseVo<AmazonSdAdProduct, AmazonSdAdProduct> data = result.getData();
            List<AmazonSdAdProduct> amazonAdProductError = data.getErrorList();
            if (CollectionUtils.isNotEmpty(errorList)) {
                amazonAdProductError.addAll(errorList);
                data.setFailNum(data.getErrorList().size());
                data.setCountNum((data.getErrorList() == null ? 0 : data.getErrorList().size()) + (data.getSuccessList() == null ? 0 : data.getSuccessList().size()));
            }
            List<AmazonSdAdProduct> amazonAdProductSuccess = data.getSuccessList();

            Map<Long, AmazonSdAdProduct> successMap = amazonAdProductSuccess.stream().collect(Collectors.toMap(AmazonSdAdProduct::getId, e -> e));
            Map<Long, AmazonSdAdProduct> errorMap = amazonAdProductError.stream().collect(Collectors.toMap(AmazonSdAdProduct::getId, e -> e));
            for (AmazonSdAdProduct amazonAdProduct : updateList) {
                AmazonSdAdProduct oldAmazonSdAdProduct = amazonSdAdProductMap.get(amazonAdProduct.getId());
                AdManageOperationLog sdProductLog = manageOperationLogService.getSdProductLog(oldAmazonSdAdProduct, amazonAdProduct);
                sdProductLog.setIp(ip);
                if (successMap.containsKey(amazonAdProduct.getId())) {
                    sdProductLog.setResult(OperationLogResultEnum.SUCCESS.getResultValue());
                } else {
                    sdProductLog.setResult(OperationLogResultEnum.FAIL.getResultValue());
                    AmazonSdAdProduct errorInfo = errorMap.get(amazonAdProduct.getId());
                    sdProductLog.setResultInfo(Objects.nonNull(errorInfo) ? errorInfo.getFailReason() : "");
                }
                adManageOperationLogs.add(sdProductLog);
            }

            //记操作日志
            if (CollectionUtils.isNotEmpty(amazonAdProductSuccess)) {
                amazonSdAdProductDao.updateList(puid, amazonAdProductSuccess);
                //写入doris
                saveDoris(puid, shopId, amazonAdProductSuccess.stream().map(x -> x.getAdId()).collect(Collectors.toList()));
                //更新成功数据打日志
                log.info("用户批量更新成功,updateId:{},puid :{},shopid:{},更新成功数据：{}", uid, puid, shopId, JSONUtil.objectToJson(amazonAdProductSuccess));
                data.getSuccessList().clear();
            }
        } else {
            for (AmazonSdAdProduct amazonAdProduct : updateList) {
                AmazonSdAdProduct oldAmazonSdAdProduct = amazonSdAdProductMap.get(amazonAdProduct.getId());
                AdManageOperationLog sdProductLog = manageOperationLogService.getSdProductLog(oldAmazonSdAdProduct, amazonAdProduct);
                sdProductLog.setIp(ip);
                sdProductLog.setResult(OperationLogResultEnum.FAIL.getResultValue());
                sdProductLog.setResultInfo(result.getMsg());
                adManageOperationLogs.add(sdProductLog);
            }
        }
        manageOperationLogService.batchLogsMergeByAdGroup(adManageOperationLogs);
        return result;
    }

    @Override
    public Result syncProductByAdGroupId(SPadGroupVo vo) {
        int puid = vo.getPuid();
        int shopId = vo.getShopId();
        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(shopId, puid);
        if (shop == null) {
            return ResultUtil.returnErr("没有CPC授权");
        }

        cpcSdProductApiService.syncProductAds(shop, vo.getCampaignId(), vo.getGroupId(), null);
        log.info("已同步campaignId:{},adGroupId:{}的产品广告数据", vo.getCampaignId(), vo.getGroupId());
        return ResultUtil.success();
    }

    private List<Object> buildUpLogMessage( Map<Long, AmazonSdAdProduct> oldList, List<AmazonSdAdProduct> newList) {
        List<Object> dataList = new ArrayList<>();
        dataList.add("SD广告批量修改状态");
        StringBuilder builder = new StringBuilder();
        newList.forEach(e -> {
            AmazonSdAdProduct old = oldList.get(e.getId());


            builder.append("adId:").append(e.getAdId());
            if (old != null) {
                builder.append(",旧值:").append(old.getState());
            }
            builder.append(",新值:").append(e.getState());

            dataList.add(builder.toString());
            builder.delete(0, builder.length());
        });
        return dataList;
    }

    /**
     * 写入doris
     * @param list
     */
    @Override
    public void saveDoris(List<AmazonSdAdProduct> list, boolean create, boolean update) {
        try {
            Date date = new Date();
            List<OdsAmazonAdProductSd> collect = list.stream().map(x -> {
                OdsAmazonAdProductSd odsAmazonAdProductSd = new OdsAmazonAdProductSd();
                BeanUtils.copyProperties(x, odsAmazonAdProductSd);
                if (create) {
                    odsAmazonAdProductSd.setCreateTime(date);
                }
                if (update) {
                    odsAmazonAdProductSd.setUpdateTime(date);
                }
                if (StringUtils.isNotBlank(odsAmazonAdProductSd.getState())) {
                    odsAmazonAdProductSd.setState(odsAmazonAdProductSd.getState().toLowerCase());
                }
                return odsAmazonAdProductSd;
            }).collect(Collectors.toList());
            dorisService.saveDoris(collect);
        } catch (Exception e) {
            log.error("sd product save doris error", e);
        }

    }

    /**
     * 写入doris
     * @param puid
     * @param shopId
     * @param adIdList
     */
    private void saveDoris(int puid, int shopId, List<String> adIdList) {
        try {
            if (CollectionUtils.isEmpty(adIdList)) {
                return;
            }
            List<AmazonSdAdProduct> amazonSdAdProducts = amazonSdAdProductDao.listByAdId(puid, shopId, adIdList);
            saveDoris(amazonSdAdProducts, false, false);
        } catch (Exception e) {
            log.error("sd product save doris error", e);
        }
    }

    @Override
    public List<String> getProductAsinByGroupId(ShopAuth shop, AmazonAdProfile profile,
                                                Integer uid, String adGroupId,
                                                String loginIp) {
        //通过广告组id获取其下对应的所有广告产品asin
        //先校验广告组id是否存在
        //通过广告组id获取其他所有广告产品
        //如果广告产品为url
        //调用亚马逊接口根据url获取其下所有的asin
        //组装数据进行返回
        if (Objects.isNull(shop) || Objects.isNull(profile)) {
            log.error("get sd product asin by group, puid or shopId is null, shop:{}, profile:{}", shop, profile);
            throw new ServiceException("对应的用户信息或店铺信息不能为空");
        }
        if (StringUtils.isEmpty(adGroupId)) {
            log.error("get sd product asin by group, group id is null, shop:{}, profile:{}", shop, profile);
            throw new ServiceException("广告组Id不能为空");
        }
        AmazonSdAdGroup amazonSdAdGroup = amazonSdAdGroupDao.getByGroupId(profile.getPuid(), shop.getId(), adGroupId);
        if (amazonSdAdGroup == null) {
            throw new ServiceException("对应的广告组不存在");
        }
        List<AmazonSdAdProduct> productList = amazonSdAdProductDao.listValidIdByGroupIds(profile.getPuid(), shop.getId(), Collections.singletonList(adGroupId));
        List<String> asinList = new ArrayList<>();

        for (AmazonSdAdProduct pInfo : productList) {
            if (StringUtils.isNotEmpty(pInfo.getLandingPageURL()) && SDAdsLandingPageTypeEnum.STORE.getType().equals(pInfo.getLandingPageType())) {
                //表示广告产品为url类型，需要再调用亚马逊接口进行获取asin
                String landingUrl = pInfo.getLandingPageURL();
                Result<PageAsinsResult> result = pageAsinsApiService.getAsinList(shop, profile, landingUrl);
                if (result.success()) {
                    PageAsinsResult resultData = result.getData();
                    Optional.ofNullable(resultData).map(PageAsinsResult::getAsinList)
                            .filter(CollectionUtils::isNotEmpty).ifPresent(asinList::addAll);
                }
            } else {
                //普通广告产品，直接添加asin
                Optional.ofNullable(pInfo.getAsin()).filter(StringUtils::isNotEmpty).ifPresent(asinList::add);
            }
        }
        return asinList;
    }
}