package com.meiyunji.sponsored.service.multiple.campagin.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 广告活动日志
 *
 * @author: zzh
 * @create: 2024-11-21 10:05
 */
@Data
public class CampaignDataLog {

    @ApiModelProperty("数量")
    private Integer count;

    @ApiModelProperty("操作时间")
    private String siteOperationTime;

    @ApiModelProperty("改动前值")
    private String previousValue;

    @ApiModelProperty("改动后值")
    private String newValue;
}
