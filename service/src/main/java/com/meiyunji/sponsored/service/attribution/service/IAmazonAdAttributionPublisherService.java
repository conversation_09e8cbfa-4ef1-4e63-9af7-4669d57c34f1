package com.meiyunji.sponsored.service.attribution.service;

import com.meiyunji.sponsored.service.attribution.entity.AmazonAdAttributionPublisher;

import java.util.List;

/**
 * @author: wade
 * @date: 2022/3/12 11:54
 * @describe:
 */
public interface IAmazonAdAttributionPublisherService {
    void syncAttributionPublisher();

    List<AmazonAdAttributionPublisher> getPublishers(String name, Boolean macroEnable) ;

    AmazonAdAttributionPublisher getPublisherById(String publiserId);

    AmazonAdAttributionPublisher getPublisherByName(String name);
}
