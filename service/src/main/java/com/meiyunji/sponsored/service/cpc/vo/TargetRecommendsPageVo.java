package com.meiyunji.sponsored.service.cpc.vo;

import com.amazon.advertising.sb.entity.targetingRecommendation.CategoryRecommendationResults;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @author: ys
 * @date: 2024/7/2 11:18
 * @describe:
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TargetRecommendsPageVo {
    private Integer totalSize;
    private Integer totalPage;
    private List<CategoryRecommendationResults> categoryList;
}
