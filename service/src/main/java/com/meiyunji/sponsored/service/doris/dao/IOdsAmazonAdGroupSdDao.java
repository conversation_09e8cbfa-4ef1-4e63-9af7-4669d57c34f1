package com.meiyunji.sponsored.service.doris.dao;

import com.meiyunji.sponsored.common.springjdbc.IDorisBaseDao;
import com.meiyunji.sponsored.service.doris.bo.AdGroupDefaultBidBo;
import com.meiyunji.sponsored.service.doris.po.OdsAmazonAdGroupSd;

import java.util.List;

/**
 * amazon SD广告组表(OdsAmazonAdGroupSd)数据库访问层
 *
 * <AUTHOR>
 * @since 2024-03-26 10:46:18
 */
public interface IOdsAmazonAdGroupSdDao extends IDorisBaseDao<OdsAmazonAdGroupSd> {

    List<AdGroupDefaultBidBo> getBidByGroupIds(Integer puid, Integer shopId, List<String> adGroupIds);

}

