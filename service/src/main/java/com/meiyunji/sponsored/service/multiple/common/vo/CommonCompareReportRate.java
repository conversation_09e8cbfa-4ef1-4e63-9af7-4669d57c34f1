package com.meiyunji.sponsored.service.multiple.common.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 活动报告环比增长率指标数据
 *
 * @author: zzh
 * @create: 2024-11-21 10:05
 */
@Data
public class CommonCompareReportRate extends CommonCompareReport {

    /**
     * 环比曝光量增长率
     */
    private String compareImpressionsRate;

    /**
     * 环比点击量增长率
     */
    private String compareClicksRate;

    /**
     * 环比点击率（CTR）增长率
     */
    private String compareCtrRate;

    /**
     * 环比订单转化率增长率
     */
    private String compareCvrRate;

    /**
     * 环比ACoS增长率
     */
    private String compareAcosRate;

    /**
     * 环比ROAS增长率
     */
    private String compareRoasRate;

    /**
     * 环比ACoTS增长率
     */
    private String compareAcotsRate;

    /**
     * 环比ASoTS增长率
     */
    private String compareAsotsRate;

    /**
     * 环比广告订单数增长率
     */
    private String compareAdOrderNumRate;

    /**
     * 环比广告花费增长率
     */
    private String compareAdCostRate;

    /**
     * 环比平均点击费增长率
     */
    private String compareAdCostPerClickRate;

    /**
     * 环比广告销售额增长率
     */
    private String compareAdSaleRate;

    /**
     * 环比可见广告展示总数增长率
     */
    private String compareViewImpressionsRate;

    /**
     * 环比每笔订单花费增长率
     */
    private String compareCpaRate;

    /**
     * 环比每千次展现费用增长率
     */
    private String compareVcpmRate;

    /**
     * 环比本广告产品订单量增长率
     */
    private String compareAdSaleNumRate;

    /**
     * 环比其他产品广告订单量增长率
     */
    private String compareAdOtherOrderNumRate;

    /**
     * 环比本广告产品销售额增长率
     */
    private String compareAdSalesRate;

    /**
     * 环比其他产品广告销售额增长率
     */
    private String compareAdOtherSalesRate;

    /**
     * 环比广告销量增长率
     */
    private String compareOrderNumRate;

    /**
     * 环比本广告产品销量增长率
     */
    private String compareAdSelfSaleNumRate;

    /**
     * 环比其他产品广告销量增长率
     */
    private String compareAdOtherSaleNumRate;

    /**
     * 环比“品牌新买家”订单量增长率
     */
    private String compareOrdersNewToBrandFTDRate;

    /**
     * 环比“品牌新买家”订单百分比增长率
     */
    private String compareOrderRateNewToBrandFTDRate;

    /**
     * 环比“品牌新买家”销售额增长率
     */
    private String compareSalesNewToBrandFTDRate;

    /**
     * 环比“品牌新买家”销售额占比增长率
     */
    private String compareSalesRateNewToBrandFTDRate;

    /**
     * 环比“品牌新买家”订单转化率增长率
     */
    private String compareOrdersNewToBrandPercentageFTDRate;

    /**
     * 环比“品牌新买家”销量增长率
     */
    private String compareUnitsOrderedNewToBrandFTDRate;

    /**
     * 环比“品牌新买家”销量百分比增长率
     */
    private String compareUnitsOrderedRateNewToBrandFTDRate;

    /**
     * 环比“品牌新买家”观看量增长率
     */
    private String compareNewToBrandDetailPageViewsRate;

    /**
     * 环比广告花费占比增长率
     */
    private String compareAdCostPercentageRate;

    /**
     * 环比广告销售额占比增长率
     */
    private String compareAdSalePercentageRate;

    /**
     * 环比广告订单量占比增长率
     */
    private String compareAdOrderNumPercentageRate;

    /**
     * 环比广告销量占比增长率
     */
    private String compareOrderNumPercentageRate;

    /**
     * 环比加购次数增长率
     */
    private String compareAddToCartRates;

    /**
     * 环比加购率增长率
     */
    private String compareAddToCartRateRate;

    /**
     * 环比单次加购花费增长率
     */
    private String compareECPAddToCartRate;

    /**
     * 环比5秒观看次数增长率
     */
    private String compareVideo5SecondViewsRate;

    /**
     * 环比5秒观看率增长率
     */
    private String compareVideo5SecondViewRateRate;

    /**
     * 环比视频播至1/4次数增长率
     */
    private String compareVideoFirstQuartileViewsRate;

    /**
     * 环比视频播至1/2次数增长率
     */
    private String compareVideoMidpointViewsRate;

    /**
     * 环比视频播至3/4次数增长率
     */
    private String compareVideoThirdQuartileViewsRate;

    /**
     * 环比视频完整播放次数增长率
     */
    private String compareVideoCompleteViewsRate;

    /**
     * 环比视频取消静音增长率
     */
    private String compareVideoUnmutesRate;

    /**
     * 环比可见展示次数增长率
     */
    private String compareViewableImpressionsRate;

    /**
     * 环比观看率增长率
     */
    private String compareViewabilityRateRate;

    /**
     * 环比观看点击率增长率
     */
    private String compareViewClickThroughRateRate;

    /**
     * 环比品牌搜索次数增长率
     */
    private String compareBrandedSearchesRate;

    /**
     * 环比平均触达次数增长率
     */
    private String compareImpressionsFrequencyAverageRate;

    /**
     * 环比广告笔单价增长率
     */
    private String compareAdvertisingUnitPriceRate;

    /**
     * 环比DPV增长率
     */
    private String compareDetailPageViewsRate;
}
