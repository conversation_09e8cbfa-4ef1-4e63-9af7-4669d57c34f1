package com.meiyunji.sponsored.service.autoRuleTask.dao;

import com.meiyunji.sponsored.common.springjdbc.IAdBaseDao;
import com.meiyunji.sponsored.service.autoRuleTask.po.AutoRuleTaskRecordSequence;

import java.util.List;

/**
 * @author: chenzimeng
 * @email: <EMAIL>
 * @date: 2024-01-08  09:36
 */
public interface AutoRuleTaskRecordSequenceDao extends IAdBaseDao<AutoRuleTaskRecordSequence> {

    Long genId();

    List<Long> batchGenId(Integer size);
}
