package com.meiyunji.sponsored.service.reportHour.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.meiyunji.sponsored.common.util.MathUtil;
import com.meiyunji.sponsored.service.excel.excelTools.ExportFormat;
import com.meiyunji.sponsored.service.excel.excelTools.ExportFormatType;
import com.meiyunji.sponsored.service.excel.excelTools.converter.StringConverter;
import lombok.Data;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Optional;

@Data
public class AdCampaignWeekDayVo extends AdCampaignHourBaseVo{
    private Integer weekDay;
    private BigDecimal vcpmCost;
    private Long vcpmImpressions;
    private Long totalImpressions;
    private Long totalClicks;

    /**
     * 统计非vcpm的本广告产品销售额
     */
    private BigDecimal totalAdSelfSale;
    /**
     * 统计非vcpm的广告销售额
     */
    private BigDecimal totalAdSale;

    private BigDecimal acots;

    private BigDecimal asots;


    public Long clicksCompare = 0L;
    public BigDecimal clicksCompareRate = BigDecimal.ZERO;
    public Long impressionsCompare = 0L;
    public BigDecimal impressionsCompareRate = BigDecimal.ZERO;
    public Integer adOrderNumCompare = 0;
    public BigDecimal adOrderNumCompareRate = BigDecimal.ZERO;
    public BigDecimal adCostCompare = BigDecimal.ZERO;
    public BigDecimal adCostCompareRate = BigDecimal.ZERO;
    public BigDecimal adSaleCompare = BigDecimal.ZERO;
    public BigDecimal adSaleCompareRate = BigDecimal.ZERO;
    public Integer adSaleNumCompare = 0;
    public BigDecimal adSaleNumCompareRate = BigDecimal.ZERO;

    /**
     * CPC(对比)
     */
    private BigDecimal adCostPerClickCompare;
    /**
     * CPC(比率)
     */
    private BigDecimal adCostPerClickCompareRate;
    /**
     * 广告点击率(对比)
     */
    private BigDecimal ctrCompare;
    /**
     * 广告点击率(比率)
     */
    private BigDecimal ctrCompareRate;
    /**
     * 广告转化率(对比)
     */
    private BigDecimal cvrCompare;
    /**
     * 广告转化率(比率)
     */
    private BigDecimal cvrCompareRate;
    /**
     * ACoS(对比)
     */
    private BigDecimal acosCompare;
    /**
     * ACoS(比率)
     */
    private BigDecimal acosCompareRate;
    /**
     * ROAS(对比)
     */
    private BigDecimal roasCompare;
    /**
     * ROAS(比率)
     */
    private BigDecimal roasCompareRate;

    public void staticsFromHourVos(List<AdCampaignHourVo> vos) {
        super.staticsFromHourVos(vos);
        //花费
        BigDecimal adCostCompare = vos.stream().map(AdCampaignHourVo::getAdCostCompare)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        this.setAdCostCompare(adCostCompare);
        //曝光
        long impressionsCompare = vos.stream().map(AdCampaignHourVo::getImpressionsCompare)
                .reduce(Long::sum).orElse(0L);
        this.setImpressionsCompare(impressionsCompare);
        //点击量
        long clickCompare = vos.stream().map(AdCampaignHourVo::getClicksCompare)
                .reduce(Long::sum).orElse(0L);
        this.setClicksCompare(clickCompare);
        //订单量
        int orderNumCompare = vos.stream().map(AdCampaignHourVo::getAdOrderNumCompare)
                .reduce(Integer::sum).orElse(0);
        this.setAdOrderNumCompare(orderNumCompare);
        //cpc每次点击花费
        this.setAdCostPerClickCompare(clickCompare == 0 ? BigDecimal.ZERO :
                adCostCompare.divide(BigDecimal.valueOf(clickCompare),
                        2, RoundingMode.HALF_UP));
        //广告点击率
        this.setCtrCompare(impressionsCompare == 0 ? BigDecimal.ZERO :
                BigDecimal.valueOf(clickCompare).multiply(BigDecimal.valueOf(100))
                        .divide(BigDecimal.valueOf(impressionsCompare)
                                , 2, RoundingMode.HALF_UP));
        //广告转化,广告订单/广告点击
        this.setCvrCompare(clickCompare == 0 ? BigDecimal.ZERO :
                BigDecimal.valueOf(orderNumCompare).multiply(BigDecimal.valueOf(100))
                        .divide(BigDecimal.valueOf(clickCompare), 2, RoundingMode.HALF_UP));
        //广告销售额
        BigDecimal adSaleCompare = vos.stream().map(AdCampaignHourVo::getAdSaleCompare)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        this.setAdSaleCompare(adSaleCompare);
        //acos广告花费/广告销售额
        this.setAcosCompare(adSaleCompare.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO :
                adCostCompare.multiply(BigDecimal.valueOf(100)).divide(adSaleCompare, 2, RoundingMode.HALF_UP));
        //roas 广告销售额/广告花费
        this.setRoasCompare(adCostCompare.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO :
                adSaleCompare.divide(adCostCompare, 2, RoundingMode.HALF_UP));
        //广告销量
        this.setAdSaleNumCompare(vos.stream().map(AdCampaignHourVo::getAdSaleNumCompare)
                .reduce(Integer::sum).orElse(0));
    }

    /**
     * 完成click等属性设置后，调用此方法为对比率进行设值
     */
    public void afterPropertiesSet() {
        this.clicksCompareRate = MathUtil.calculateCompareRate(BigDecimal.valueOf(getClicks()), BigDecimal.valueOf(this.clicksCompare));
        this.impressionsCompareRate = MathUtil.calculateCompareRate(BigDecimal.valueOf(getImpressions()), BigDecimal.valueOf(this.impressionsCompare));
        this.adCostCompareRate = MathUtil.calculateCompareRate(getAdCost(), this.adCostCompare);
        this.adOrderNumCompareRate = MathUtil.calculateCompareRate(BigDecimal.valueOf(getAdOrderNum()), BigDecimal.valueOf(adOrderNumCompare));
        this.adSaleCompareRate = MathUtil.calculateCompareRate(getAdSale(), this.adSaleCompare);
        this.adSaleNumCompareRate = MathUtil.calculateCompareRate(BigDecimal.valueOf(getAdSaleNum()), BigDecimal.valueOf(this.adSaleNumCompare));
        this.adCostPerClickCompareRate = MathUtil.calculateCompareRate(getAdCostPerClick(), this.adCostPerClickCompare);
        this.ctrCompareRate = MathUtil.calculateCompareRate(getCtr(), this.ctrCompare);
        this.cvrCompareRate = MathUtil.calculateCompareRate(getCvr(), this.cvrCompare);
        this.acosCompareRate = MathUtil.calculateCompareRate(getAcos(), this.acosCompare);
        this.roasCompareRate = MathUtil.calculateCompareRate(getRoas(), this.roasCompare);
    }
}

