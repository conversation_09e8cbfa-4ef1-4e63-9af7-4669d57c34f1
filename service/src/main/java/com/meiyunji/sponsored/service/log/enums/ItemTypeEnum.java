package com.meiyunji.sponsored.service.log.enums;


/**
 * <AUTHOR>
 * @ date 2022/10/08
 */
public enum ItemTypeEnum {
    /**
     * 对象类型
     */
    CAMPAIGN("CAMPAIGN","调预算"),
    CAMPAIGN_PLACEMENT("CAMPAIGN_PLACEMENT","调广告位"),
    TARGET("TARGET","调竞价"),
    START_STOP("START_STOP","调分时启停"),
    PORTFOLIO("PORTFOLIO","调分时广告组合"),
    ENABLED("enabled","开启"),
    PAUSED("paused","暂停");

    private String itemType;

    private String itemValue;

    ItemTypeEnum(String itemType, String itemValue) {
        this.itemType = itemType;
        this.itemValue = itemValue;
    }

    public  String getItemType() {
        return itemType;
    }

    public void setItemType(String itemType) {
        this.itemType = itemType;
    }

    public String getItemValue() {
        return itemValue;
    }

    public void setItemValue(String itemValue) {
        this.itemValue = itemValue;
    }

    public static String getItemTypeEnumItemValue(String itemType) {
        ItemTypeEnum[] values = values();
        for (ItemTypeEnum value :values) {
            if (itemType.equalsIgnoreCase(value.getItemType())) {
                return value.getItemValue();
            }
        }
        return null;
    }

}
