package com.meiyunji.sponsored.service.multiPlatform.walmart.enums;

import lombok.Getter;

/**
 * @author: ys
 * @date: 2025/3/26 15:03
 * @describe:
 */
@Getter
public enum WalmartOperationLogTargetEnum {
    CAMPAIGN("campaign","广告活动"),
    GROUP("group","广告组"),
    ITEM("item","广告产品"),
    KEYWORD("keyword","关键词投放"),
    SEARCH_IMPRESSION("searchImpression","搜索词"),
    PLACEMENT("placement", "广告位"),
    MULTIPLIER("multiplier", "竞价倍数"),
    ;

    private String targetType;

    private String targetValue;

    WalmartOperationLogTargetEnum(String targetType, String targetValue) {
        this.targetType = targetType;
        this.targetValue = targetValue;
    }

    public static String getOperationLogTargetEnumValue(String moduleType) {
        WalmartOperationLogTargetEnum[] values = values();
        for (WalmartOperationLogTargetEnum value : values) {
            if (moduleType.equalsIgnoreCase(value.getTargetType())) {
                return value.getTargetValue();
            }
        }
        return null;
    }

    public static WalmartOperationLogTargetEnum getOperationLogTargetEnum(String targetType) {
        WalmartOperationLogTargetEnum[] values = values();
        for (WalmartOperationLogTargetEnum value : values) {
            if (targetType.equalsIgnoreCase(value.getTargetType())) {
                return value;
            }
        }
        return null;
    }

}
