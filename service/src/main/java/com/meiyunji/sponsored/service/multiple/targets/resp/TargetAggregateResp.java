package com.meiyunji.sponsored.service.multiple.targets.resp;

import com.meiyunji.sponsored.service.multiple.common.vo.AdHomeChartVo;
import com.meiyunji.sponsored.service.multiple.common.vo.CommonCompareReportRate;
import lombok.Data;

import java.util.List;

/**
 * 投放层级多店铺汇总接口 响应参数
 * @Author: zzh
 * @Date: 2025/4/17 13:24
 */
@Data
public class TargetAggregateResp {

    /**
     * 报告数据
     */
    private CommonCompareReportRate aggregateDataVo;

    /**
     * 多店铺展示币种
     */
    private String currency;

    /**
     * 是否超过限制
     */
    private Boolean overLimit = false;

    /**
     * 日图表数据
     */
    private List<AdHomeChartVo> day;

    /**
     * 周图表数据
     */
    private List<AdHomeChartVo> week;

    /**
     * 月图表数据
     */
    private List<AdHomeChartVo> month;
}
