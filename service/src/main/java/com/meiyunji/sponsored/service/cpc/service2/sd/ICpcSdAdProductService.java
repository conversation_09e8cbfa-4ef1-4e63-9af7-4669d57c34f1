package com.meiyunji.sponsored.service.cpc.service2.sd;

import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.rpc.sd.campaign.SdProductVo;
import com.meiyunji.sponsored.rpc.sd.product.CreateSdProductNewRequest;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdProfile;
import com.meiyunji.sponsored.service.cpc.po.AmazonSdAdProduct;
import com.meiyunji.sponsored.service.cpc.vo.*;

import java.util.List;

/**
 * <AUTHOR> on 2021/7/6
 */
public interface ICpcSdAdProductService {

    Result addProduct(AddAdProductVo addAdProductVo);
    Result<List<AmazonSdAdProduct>> addProductNew(ShopAuth shop, AmazonAdProfile profile,
                                                          String campaignId, String groupId,
                                                          CreateSdProductNewRequest request);

    NewCreateResultResultVo<SBCommonErrorVo> createProductNew(ShopAuth shop, AmazonAdProfile profile,
                                             String campaignId, String groupId,
                                             String profileId, SdProductVo request,
                                             String loginIp, Integer uid);


    Result showAdPerformance(int puid, AdPerformanceParam param);

    Result updateState(Integer puid, Integer shopId, Integer uid, String adId, String state);

    Result archive(Integer puid, Integer shopId, Integer uid, String adId, String loginIp);


    Result updateBatchState(List<AmazonSdAdProduct> amazonAdProducts, String ip);

    //同步广告组产品
    Result syncProductByAdGroupId(SPadGroupVo vo);

    void saveDoris(List<AmazonSdAdProduct> list, boolean create, boolean update);

    List<String> getProductAsinByGroupId(ShopAuth shop, AmazonAdProfile profile, Integer uid, String adGroupId, String loginIp);
}