package com.meiyunji.sponsored.service.excel.excelTools;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.converters.string.StringImageConverter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.event.NotRepeatExecutor;
import com.alibaba.excel.exception.ExcelDataConvertException;
import com.alibaba.excel.metadata.CellData;
import com.alibaba.excel.write.builder.ExcelWriterSheetBuilder;
import com.alibaba.excel.write.handler.WriteHandler;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.alibaba.excel.write.style.column.SimpleColumnWidthStyleStrategy;
import com.alibaba.excel.write.style.row.SimpleRowHeightStyleStrategy;
import com.meiyunji.sponsored.service.excel.excelTools.dto.SheetDataDto;
import com.meiyunji.sponsored.service.excel.excelTools.writeHandler.*;
import com.meiyunji.sponsored.service.util.ClassCacheUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.util.Units;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.multipart.MultipartFile;

import java.io.FileInputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.lang.reflect.Field;
import java.net.URL;
import java.util.*;

/**
 * <AUTHOR>
 * @Date 2022/6/20 20:19
 */
public class EasyExcelUtil {
    private static final Logger logger = LoggerFactory.getLogger(EasyExcelUtil.class);

    /**
     * 通用导出
     *
     * @param out   写入指定的输出流
     * @param rows  数据
     * @param clazz 表头类
     */
    public static void write(OutputStream out, List rows, Class clazz, Integer freezeRow) {
        // 头的策略
        WriteCellStyle headStyle = new WriteCellStyle();
        // 背景色
        headStyle.setFillForegroundColor(IndexedColors.WHITE.getIndex());
        WriteFont headWriteFont = new WriteFont();
        headWriteFont.setFontHeightInPoints((short) 12);
        headStyle.setWriteFont(headWriteFont);
        // 内容的策略(与币种样式冲突，暂不设置)
        WriteCellStyle contentStyle = new WriteCellStyle();
        contentStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        // 这个策略是 头是头的样式 内容是内容的样式 其他的策略可以自己实现
        HorizontalCellStyleStrategy horizontalCellStyleStrategy =
                new HorizontalCellStyleStrategy(headStyle, contentStyle);

        // 写数据
        EasyExcel.write(out, clazz).sheet("sheet")
                .registerWriteHandler(horizontalCellStyleStrategy)                             //自定义样式
                .registerWriteHandler(new FreezeSheetHeadHandler(freezeRow))                   //冻结表头
                .registerWriteHandler(new CurrencyCellHandler())                               //币种单元格样式
                .registerWriteHandler(new SimpleColumnWidthStyleStrategy(16))                  //列宽
                .registerWriteHandler(new SimpleRowHeightStyleStrategy((short) 18, (short) 18))   //行高
                .doWrite(rows);
    }

    /**
     * 通用导出
     *
     * @param out           写入指定的输出流
     * @param rows          数据
     * @param clazz         表头类
     * @param excludeFields 表头类中不导出的列
     */
    public static void write(OutputStream out, List rows, Class clazz, Integer freezeRow, List<String> excludeFields) {
        // 头的策略
        WriteCellStyle headStyle = new WriteCellStyle();
        // 背景色
        headStyle.setFillForegroundColor(IndexedColors.WHITE.getIndex());
        WriteFont headWriteFont = new WriteFont();
        headWriteFont.setFontHeightInPoints((short) 12);
        headStyle.setWriteFont(headWriteFont);
        // 内容的策略(与币种样式冲突，暂不设置)
        WriteCellStyle contentStyle = new WriteCellStyle();
        contentStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        // 这个策略是 头是头的样式 内容是内容的样式 其他的策略可以自己实现
        HorizontalCellStyleStrategy horizontalCellStyleStrategy =
                new HorizontalCellStyleStrategy(headStyle, contentStyle);

        ExcelWriterSheetBuilder builder = null;
        // 写数据
        if (CollectionUtils.isNotEmpty(excludeFields)) {
            builder = EasyExcel.write(out, clazz).excludeColumnFiledNames(excludeFields).sheet("sheet");
        } else {
            builder = EasyExcel.write(out, clazz).sheet("sheet");
        }

        builder.registerWriteHandler(horizontalCellStyleStrategy)                             //自定义样式
                .registerWriteHandler(new FreezeSheetHeadHandler(freezeRow))                   //冻结表头
                .registerWriteHandler(new CurrencyCellHandler())                               //币种单元格样式
                .registerWriteHandler(new SimpleColumnWidthStyleStrategy(16))                  //列宽
                .registerWriteHandler(new SimpleRowHeightStyleStrategy((short) 18, (short) 18))   //行高
                .doWrite(rows);
    }

    /**
     * 通用导出
     *
     * @param out                    写入指定的输出流
     * @param rows                   数据
     * @param clazz                  表头类
     * @param headerFieldList        指定表头类中需要导出列
     * @param orderByHeaderFieldList 列顺序是否根据表头字段集合的顺序排序
     */
    public static void writeByInclude(OutputStream out, List rows, Class clazz, List<String> headerFieldList, boolean orderByHeaderFieldList) {
        // 头的策略
        WriteCellStyle headStyle = new WriteCellStyle();
        // 背景色
        headStyle.setFillForegroundColor(IndexedColors.WHITE.getIndex());
        WriteFont headWriteFont = new WriteFont();
        headWriteFont.setFontHeightInPoints((short) 12);
        headStyle.setWriteFont(headWriteFont);
        // 内容的策略(与币种样式冲突，暂不设置)
        WriteCellStyle contentStyle = new WriteCellStyle();
        // 这个策略是 头是头的样式 内容是内容的样式 其他的策略可以自己实现
        HorizontalCellStyleStrategy horizontalCellStyleStrategy = new HorizontalCellStyleStrategy(headStyle, contentStyle);

        ExcelWriterSheetBuilder builder = null;

        // 写数据
        if (CollectionUtils.isNotEmpty(headerFieldList)) {
            builder = EasyExcel.write(out, clazz)
                    .includeColumnFiledNames(headerFieldList)
                    .sheet("sheet");
        } else {
            builder = EasyExcel.write(out, clazz).sheet("sheet");
        }
        builder.registerWriteHandler(horizontalCellStyleStrategy);
        WriteHandlerBuild currencyAndRateHandlerBuild = new WriteHandlerBuild().rate().currencyNew(clazz, true);
        for (WriteHandler writeHandler : currencyAndRateHandlerBuild.handlers()) {
            builder.registerWriteHandler(writeHandler);
        }

        //按照指定字段顺序排序
        if (orderByHeaderFieldList) {
            builder.head(getHeadByFields(headerFieldList, clazz));
            builder.doWrite(getDataByFields(headerFieldList, rows, clazz));
        } else {
            builder.doWrite(rows);
        }

    }

    /**
     * 根据字段名称和class上的注解属性的列名，组装数据
     *
     * @param headerFieldList
     * @param rows
     * @param clazz
     * @return
     */
    private static List<List<Object>> getDataByFields(List<String> headerFieldList, List rows, Class clazz) {
        List<List<Object>> list = new ArrayList<>(rows.size());
        if (CollectionUtils.isEmpty(rows)) {
            return list;
        }
        Map<String, Field> fieldMap = ClassCacheUtil.getClassFieldMap(clazz);
        rows.forEach(x -> {
            List<Object> row = new ArrayList<>(headerFieldList.size());
            headerFieldList.forEach(y -> {
                if (fieldMap.containsKey(y)) {
                    Field field = fieldMap.get(y);
                    field.setAccessible(true);
                    try {
                        row.add(field.get(x));
                    } catch (IllegalAccessException e) {
                        logger.error("get field value error", e);
                    }
                }
            });
            list.add(row);
        });

        return list;
    }

    /**
     * 根据字段名称和class上的注解属性的列名，如果获取不到则显示为字段名
     *
     * @param headerFieldList
     * @param clazz
     * @return
     */
    private static List<List<String>> getHeadByFields(List<String> headerFieldList, Class clazz) {
        Map<String, String> map = ClassCacheUtil.getExcelHeaderName(clazz);
        List<List<String>> list = new ArrayList<List<String>>();
        headerFieldList.forEach(x -> list.add(Collections.singletonList(map.getOrDefault(x, x))));

        return list;
    }


    /**
     * 通用导出
     *
     * @param out   写入指定的输出流
     * @param rows  数据
     * @param clazz 表头类
     */
    public static void write(OutputStream out, List rows, Class clazz, List<WriteHandler> handlers, List<String> voExcludeFields, ExcelExtraParam excelExtraParam) {
        // 头的策略
        WriteCellStyle headStyle = new WriteCellStyle();
        // 背景色
        headStyle.setFillForegroundColor(IndexedColors.WHITE.getIndex());
        WriteFont headWriteFont = new WriteFont();
        headWriteFont.setFontHeightInPoints((short) 12);
        headStyle.setWriteFont(headWriteFont);
        // 内容的策略(与币种样式冲突，暂不设置)
        WriteCellStyle contentStyle = new WriteCellStyle();
        contentStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        Boolean wrapped = Optional.ofNullable(excelExtraParam).map(ExcelExtraParam::getWrapped).orElse(null);
        contentStyle.setWrapped(wrapped);
        // 这个策略是 头是头的样式 内容是内容的样式 其他的策略可以自己实现
        HorizontalCellStyleStrategy horizontalCellStyleStrategy =
                new HorizontalCellStyleStrategy(headStyle, contentStyle);

        // 写数据
        ExcelWriterSheetBuilder builder = EasyExcel.write(out, clazz).sheet("sheet").registerWriteHandler(horizontalCellStyleStrategy);
        if (CollectionUtils.isNotEmpty(voExcludeFields)) {
            builder = builder.excludeColumnFiledNames(voExcludeFields);
        }
        if (handlers != null && !handlers.isEmpty()) {
            for (WriteHandler handler : handlers) {
                builder.registerWriteHandler(handler);
            }
        }
        // 图片处理
        imageWriteHandler(clazz, builder, voExcludeFields, rows);
        builder.doWrite(rows);
    }

    /**
     * 通用导出
     *
     * @param out             写入指定的输出流
     * @param rows            数据
     * @param clazz           表头类
     * @param voExcludeFileds 表头类不导出的字段
     */
    public static void write(OutputStream out, List rows, Class clazz, List<WriteHandler> handlers, List<String> voExcludeFileds) {
        // 头的策略
        WriteCellStyle headStyle = new WriteCellStyle();
        // 背景色
        headStyle.setFillForegroundColor(IndexedColors.WHITE.getIndex());
        WriteFont headWriteFont = new WriteFont();
        headWriteFont.setFontHeightInPoints((short) 12);
        headStyle.setWriteFont(headWriteFont);
        // 内容的策略(与币种样式冲突，暂不设置)
        WriteCellStyle contentStyle = new WriteCellStyle();
        // 这个策略是 头是头的样式 内容是内容的样式 其他的策略可以自己实现
        HorizontalCellStyleStrategy horizontalCellStyleStrategy =
            new HorizontalCellStyleStrategy(headStyle, contentStyle);

        ExcelWriterSheetBuilder builder = null;
        // 写数据
        if (null != voExcludeFileds && !voExcludeFileds.isEmpty()) {
            builder = EasyExcel.write(out, clazz).excludeColumnFiledNames(voExcludeFileds).sheet("sheet").registerWriteHandler(horizontalCellStyleStrategy);
            if (handlers != null && !handlers.isEmpty()) {
                for (WriteHandler handler : handlers) {
                    builder.registerWriteHandler(handler);
                }
            }
        } else {
            builder = EasyExcel.write(out, clazz).sheet("sheet").registerWriteHandler(horizontalCellStyleStrategy);
            if (handlers != null && !handlers.isEmpty()) {
                for (WriteHandler handler : handlers) {
                    builder.registerWriteHandler(handler);
                }
            }
        }
        try {
            builder.doWrite(rows);
        } catch (Exception exception) {
            if (exception.getCause() instanceof ExcelDataConvertException) {
                ExcelDataConvertException excelDataConvertException = (ExcelDataConvertException)exception.getCause();
                String cellMsg = "";
                CellData cellData = excelDataConvertException.getCellData();
                //这里有一个celldatatype的枚举值,用来判断CellData的数据类型
                CellDataTypeEnum type = cellData.getType();
                if (type.equals(CellDataTypeEnum.NUMBER)) {
                    cellMsg = cellData.getNumberValue().toString();
                } else if (type.equals(CellDataTypeEnum.STRING)) {
                    cellMsg = cellData.getStringValue();
                } else if (type.equals(CellDataTypeEnum.BOOLEAN)) {
                    cellMsg = cellData.getBooleanValue().toString();
                }
                String errorMsg = String.format("excel表格:第%s行,第%s列,数据值为:%s,该数据值不符合要求,请检验后重新导入!<span style=\"color:red\">请检查其他的记录是否有同类型的错误!</span>", excelDataConvertException.getRowIndex() + 1, excelDataConvertException.getColumnIndex(), cellMsg);
                logger.error(errorMsg);
            }
            logger.error("builder doWrite is error, errorMsg: {}", exception.getMessage());
        }
    }

    /**
     * 不创建对象的写
     *
     * @param out
     * @param headers
     * @param rows
     * @param handlers
     * @param voExcludeFileds
     */
    public static void noModlewrite(OutputStream out, List<String> headers, List<List<Object>> rows, List<WriteHandler> handlers) {
        // 头的策略
        WriteCellStyle headStyle = new WriteCellStyle();
        // 背景色
        headStyle.setFillForegroundColor(IndexedColors.WHITE.getIndex());
        WriteFont headWriteFont = new WriteFont();
        headWriteFont.setFontHeightInPoints((short)12);
        headStyle.setWriteFont(headWriteFont);
        // 内容的策略(与币种样式冲突，暂不设置)
        WriteCellStyle contentStyle = new WriteCellStyle();
        // 这个策略是 头是头的样式 内容是内容的样式 其他的策略可以自己实现
        HorizontalCellStyleStrategy horizontalCellStyleStrategy =
            new HorizontalCellStyleStrategy(headStyle, contentStyle);

        ExcelWriterSheetBuilder builder = EasyExcel.write(out).sheet("sheet").registerWriteHandler(horizontalCellStyleStrategy);
        //registerWriteHandler
        if (CollectionUtils.isNotEmpty(handlers)) {
            for (WriteHandler handler : handlers) {
                builder.registerWriteHandler(handler);
            }
        }
        List<List<String>> head = head(headers);
        builder.head(head);
        try {
            builder.doWrite(rows);
        } catch (Exception exception) {
            if (exception.getCause() instanceof ExcelDataConvertException) {
                ExcelDataConvertException excelDataConvertException = (ExcelDataConvertException)exception.getCause();
                String cellMsg = "";
                CellData cellData = excelDataConvertException.getCellData();
                //这里有一个celldatatype的枚举值,用来判断CellData的数据类型
                CellDataTypeEnum type = cellData.getType();
                if (type.equals(CellDataTypeEnum.NUMBER)) {
                    cellMsg = cellData.getNumberValue().toString();
                } else if (type.equals(CellDataTypeEnum.STRING)) {
                    cellMsg = cellData.getStringValue();
                } else if (type.equals(CellDataTypeEnum.BOOLEAN)) {
                    cellMsg = cellData.getBooleanValue().toString();
                }
                String errorMsg = String.format("excel表格:第%s行,第%s列,数据值为:%s,该数据值不符合要求,请检验后重新导入!<span style=\"color:red\">请检查其他的记录是否有同类型的错误!</span>", excelDataConvertException.getRowIndex() + 1, excelDataConvertException.getColumnIndex(), cellMsg);
                logger.error(errorMsg);
            }
            logger.error("builder doWrite is error, errorMsg: {}", exception.getMessage());
        }
    }

    private static List<List<String>> head(List<String> headers) {
        List<List<String>> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(headers)) {
            return result;
        }
        for (String header : headers) {
            List<String> head = new ArrayList<>();
            head.add(header);
            result.add(head);
        }
        return result;
    }

    /**
     * 自定义复杂表头导出
     *
     * @param rows 数据
     * @param head 表头
     * @param out  输出流
     */
    public static void write(List<List<Object>> rows, List<List<String>> head, OutputStream out, Integer freezeRow) {
        ExcelWriter writer = EasyExcelFactory.write(out).build();
        // 头的策略
        WriteCellStyle headStyle = new WriteCellStyle();
        // 背景色
        headStyle.setFillForegroundColor(IndexedColors.WHITE.getIndex());
        WriteFont headWriteFont = new WriteFont();
        headWriteFont.setFontHeightInPoints((short) 12);
        headStyle.setWriteFont(headWriteFont);
        // 内容的策略(与币种样式冲突，暂不设置)
        WriteCellStyle contentStyle = new WriteCellStyle();
        contentStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        // 这个策略是 头是头的样式 内容是内容的样式 其他的策略可以自己实现
        HorizontalCellStyleStrategy horizontalCellStyleStrategy =
                new HorizontalCellStyleStrategy(headStyle, contentStyle);

        WriteSheet sheet = EasyExcel.writerSheet("sheet").head(head)
                .registerWriteHandler(horizontalCellStyleStrategy)                             //自定义样式
                .registerWriteHandler(new FreezeSheetHeadHandler(freezeRow))                   //冻结表头
                .registerWriteHandler(new RateCellHandler())                                   //冻结表头
                .registerWriteHandler(new CurrencyCellHandler())                               //币种单元格样式
                .registerWriteHandler(new SimpleColumnWidthStyleStrategy(15))                  //列宽
                .registerWriteHandler(new SimpleRowHeightStyleStrategy((short) 24, (short) 24))   //行高
                .build();

        // 写数据
        writer.write(rows, sheet);
        writer.finish();
    }

    /**
     * 自定义复杂表头导出
     *
     * @param rows 数据
     * @param head 表头
     * @param out  输出流
     */
    public static void write(List<List<Object>> rows, List<List<String>> head, OutputStream out, Integer freezeRow, List<Integer> needHiddenColumn) {
        ExcelWriter writer = EasyExcelFactory.write(out).build();
        // 头的策略
        WriteCellStyle headStyle = new WriteCellStyle();
        // 背景色
        headStyle.setFillForegroundColor(IndexedColors.WHITE.getIndex());
        WriteFont headWriteFont = new WriteFont();
        headWriteFont.setFontHeightInPoints((short) 12);
        headStyle.setWriteFont(headWriteFont);
        // 内容的策略(与币种样式冲突，暂不设置)
        WriteCellStyle contentStyle = new WriteCellStyle();
        contentStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        // 这个策略是 头是头的样式 内容是内容的样式 其他的策略可以自己实现
        HorizontalCellStyleStrategy horizontalCellStyleStrategy =
                new HorizontalCellStyleStrategy(headStyle, contentStyle);

        WriteSheet sheet = EasyExcel.writerSheet("sheet").head(head)
                .registerWriteHandler(horizontalCellStyleStrategy)                             //自定义样式
                .registerWriteHandler(new FreezeSheetHeadHandler(freezeRow))                   //冻结表头
                .registerWriteHandler(new RateCellHandler())                                   //冻结表头
                .registerWriteHandler(new CurrencyCellHandler())                               //币种单元格样式
                .registerWriteHandler(new ExcelColumnWidthStyleStrategy(15, needHiddenColumn))
                .registerWriteHandler(new SimpleRowHeightStyleStrategy((short) 24, (short) 24))   //行高
                .build();

        // 写数据
        writer.write(rows, sheet);
        writer.finish();
    }

    /**
     * 自定义复杂表头导出
     *
     * @param rows 数据
     * @param head 表头
     * @param out  输出流
     */
    public static void write(List<List<Object>> rows, List<List<String>> head, OutputStream out, List<WriteHandler> handlers) {
        // 头的策略
        WriteCellStyle headStyle = new WriteCellStyle();
        // 背景色
        headStyle.setFillForegroundColor(IndexedColors.WHITE.getIndex());
        WriteFont headWriteFont = new WriteFont();
        headWriteFont.setFontHeightInPoints((short) 12);
        headStyle.setWriteFont(headWriteFont);
        // 内容的策略(与币种样式冲突，暂不设置)
        WriteCellStyle contentStyle = new WriteCellStyle();
        contentStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        // 这个策略是 头是头的样式 内容是内容的样式 其他的策略可以自己实现
        HorizontalCellStyleStrategy horizontalCellStyleStrategy =
                new HorizontalCellStyleStrategy(headStyle, contentStyle);

        // 写数据
        ExcelWriterSheetBuilder builder = EasyExcel.write(out).sheet("sheet").head(head).registerWriteHandler(horizontalCellStyleStrategy);
        if (handlers != null && !handlers.isEmpty()) {
            for (WriteHandler handler : handlers) {
                builder.registerWriteHandler(handler);
            }
        }
        builder.doWrite(rows);
    }

    /**
     * 按照class模板设置的行列属性导出
     *
     * @param out      写入指定的输出流
     * @param list     数据
     * @param clazz    表头类
     * @param handlers 特殊字段处理方案
     */
    public static void writeByClass(OutputStream out, List list, Class clazz, List<WriteHandler> handlers) {
        // 头的策略
        WriteCellStyle headStyle = new WriteCellStyle();

        // 背景色
        headStyle.setFillForegroundColor(IndexedColors.WHITE.getIndex());

        WriteFont headWriteFont = new WriteFont();
        headWriteFont.setFontHeightInPoints((short) 12);
        headStyle.setWriteFont(headWriteFont);

        // 内容的策略(与币种样式冲突，暂不设置)
        WriteCellStyle contentStyle = new WriteCellStyle();
        contentStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        // 这个策略是 头是头的样式 内容是内容的样式 其他的策略可以自己实现
        HorizontalCellStyleStrategy horizontalCellStyleStrategy =
                new HorizontalCellStyleStrategy(headStyle, contentStyle);

        // 写数据
        ExcelWriterSheetBuilder sheetBuilder = EasyExcel.write(out, clazz).sheet("sheet")
                .registerWriteHandler(horizontalCellStyleStrategy);
        if (CollectionUtils.isNotEmpty(handlers)) {
            for (WriteHandler handler : handlers) {
                sheetBuilder.registerWriteHandler(handler);
            }
        }
        sheetBuilder.doWrite(list);
    }

    /**
     * 按照class模板设置的行列属性导出
     *
     * @param out       写入指定的输出流
     * @param list      数据
     * @param clazz     表头类
     * @param freezeRow 冻结前几行
     */
    public static void writeByClass(OutputStream out, List list, Class clazz, Integer freezeRow, Map<Integer, List<String>> selectMap, int deep, Integer first) {
        // 头的策略
        WriteCellStyle headStyle = new WriteCellStyle();

        // 背景色
        headStyle.setFillForegroundColor(IndexedColors.WHITE.getIndex());

        WriteFont headWriteFont = new WriteFont();
        headWriteFont.setFontHeightInPoints((short) 12);
        headStyle.setWriteFont(headWriteFont);

        // 内容的策略(与币种样式冲突，暂不设置)
        WriteCellStyle contentStyle = new WriteCellStyle();
        contentStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        // 这个策略是 头是头的样式 内容是内容的样式 其他的策略可以自己实现
        HorizontalCellStyleStrategy horizontalCellStyleStrategy =
                new HorizontalCellStyleStrategy(headStyle, contentStyle);

        // 写数据
        EasyExcel.write(out, clazz).sheet("sheet")
                .registerWriteHandler(horizontalCellStyleStrategy)               //自定义样式
                .registerWriteHandler(new FreezeSheetHeadHandler(freezeRow))     //冻结表头
                .registerWriteHandler(new CurrencyCellHandler())                 //币种单元格样式
                .registerWriteHandler(new SelectSheetWriteHandler(selectMap, deep, first))
                .doWrite(list);
    }

//    /**
//     * 多个excel文件导出为zip，单个导出为excel
//     * @param response
//     * @param bytes
//     * @param fileName
//     */
//    public static void writeBytes(HttpServletResponse response, List<byte[]> bytes, String fileName) {
//        try {
//            if (bytes.size() > 1) {
//                OutputStream out = getZipResponseOutPutStream(response, fileName);
//                ZipOutputStream zipOut = new ZipOutputStream(out);
//                for(int j=0,len=bytes.size();j<len;j++){
//                    zipOut.putNextEntry(new ZipEntry(fileName+"_"+j+".xlsx"));
//                    zipOut.write(bytes.get(j));
//                    zipOut.closeEntry();
//                }
//                zipOut.flush();
//                zipOut.close();
//            }else {
//                OutputStream out = getResponseOutPutStream(response, fileName);
//                out.write(bytes.get(0));
//                out.flush();
//                out.close();
//            }
//        } catch (Exception e) {
//            logger.error("导出报错：",e);
//        }
//    }

    /**
     * 模板导出回填下拉框
     *
     * @param selectMap k: 下拉框所在列下标 v：下拉选项
     * @param deep      下拉框纵深
     * @param out       输出流
     */
    public static void writeTemplateWithSelect(Map<Integer, List<String>> selectMap, Integer deep, Integer first, FileInputStream is, OutputStream out) {
        ExcelWriter writer = EasyExcelFactory.write(out).withTemplate(is).build();

        WriteSheet sheet = EasyExcel.writerSheet()
                .registerWriteHandler(new SelectSheetWriteHandler(selectMap, deep, first))
                .build();
        // 写数据
        writer.write(null, sheet);
        writer.finish();
    }

    /**
     * 通用的easyExcel 导入
     *
     * @param fileData
     * @param listener
     * @param clazz
     * @param headRowNumber
     * @throws IOException
     */
    public static void commonRead(MultipartFile fileData, AnalysisEventListener listener, Class clazz, Integer headRowNumber) throws IOException {
        EasyExcel.read(fileData.getInputStream())
                .head(clazz)
                .registerReadListener(listener)
                .headRowNumber(headRowNumber)
                .sheet()
                .doRead();
    }

    /**
     * 自定义复杂表头，多sheet导出
     * 销量统计专用
     */
    public static void writeMultiSheet(SheetDataDto sheetData, ExcelWriter writer, List<WriteHandler> handlers) {

        // 头的策略
        WriteCellStyle headStyle = new WriteCellStyle();
        // 背景色
        headStyle.setFillForegroundColor(IndexedColors.WHITE.getIndex());
        WriteFont headWriteFont = new WriteFont();
        headWriteFont.setFontHeightInPoints((short) 12);
        headStyle.setWriteFont(headWriteFont);
        // 内容的策略(与币种样式冲突，暂不设置)
        WriteCellStyle contentStyle = new WriteCellStyle();
        contentStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        // 这个策略是 头是头的样式 内容是内容的样式 其他的策略可以自己实现
        HorizontalCellStyleStrategy horizontalCellStyleStrategy =
                new HorizontalCellStyleStrategy(headStyle, contentStyle);
        ExcelWriterSheetBuilder builder = new ExcelWriterSheetBuilder(writer).registerWriteHandler(horizontalCellStyleStrategy);
        if (handlers != null && !handlers.isEmpty()) {
            for (WriteHandler handler : handlers) {
                builder.registerWriteHandler(handler);
            }
        }
        builder.sheetNo(sheetData.getSheetNo()).sheetName(sheetData.getSheetName()).head(sheetData.getHead());
        WriteSheet sheet = builder.build();
        writer.write(sheetData.getRows(), sheet);

    }

    public static void writeMultipleSheets(OutputStream out, List<List> rowList, List<Class> clazzList,
                                           List<WriteHandler> handlers, List<String> voExcludeFields,
                                           ExcelExtraParam excelExtraParam) {
        // 头的策略
        WriteCellStyle headStyle = new WriteCellStyle();
        // 背景色
        headStyle.setFillForegroundColor(IndexedColors.WHITE.getIndex());
        WriteFont headWriteFont = new WriteFont();
        headWriteFont.setFontHeightInPoints((short) 12);
        headStyle.setWriteFont(headWriteFont);
        // 内容的策略(与币种样式冲突，暂不设置)
        WriteCellStyle contentStyle = new WriteCellStyle();
        contentStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        Boolean wrapped = Optional.ofNullable(excelExtraParam).map(ExcelExtraParam::getWrapped).orElse(null);
        contentStyle.setWrapped(wrapped);
        // 这个策略是 头是头的样式 内容是内容的样式 其他的策略可以自己实现
        HorizontalCellStyleStrategy horizontalCellStyleStrategy =
                new HorizontalCellStyleStrategy(headStyle, contentStyle);

        ExcelWriter writer = EasyExcel.write(out).build();
        for (int i = 0; i < rowList.size(); i++) {
            // 创建sheet并注册样式
            ExcelWriterSheetBuilder builder = new ExcelWriterSheetBuilder(writer).registerWriteHandler(horizontalCellStyleStrategy);
            if (CollectionUtils.isNotEmpty(voExcludeFields)) {
                builder = builder.excludeColumnFiledNames(voExcludeFields);
            }
            if (handlers != null && !handlers.isEmpty()) {
                for (WriteHandler handler : handlers) {
                    builder.registerWriteHandler(handler);
                }
            }
            builder.sheetNo(i).sheetName("sheet" + (i + 1)).head(clazzList.get(i));
            WriteSheet sheet = builder.build();
            writer.write(rowList.get(i), sheet);
        }
        writer.finish();
    }



    /**
     * 图片导出写入处理handler
     * @param clazz          导出dto class
     * @param builder        sheet builder
     */
    private static void imageWriteHandler(Class clazz, ExcelWriterSheetBuilder builder, List rows){
        imageWriteHandler(null, clazz, builder, null, rows);
    }

    /**
     * 图片导出写入处理handler
     * @param clazz          导出dto class
     * @param builder        sheet builder
     * @param excludeFields  导出排除列属性字段名
     */
    private static void imageWriteHandler(Class clazz, ExcelWriterSheetBuilder builder, List<String> excludeFields, List rows){
        imageWriteHandler(null, clazz, builder, excludeFields, rows);
    }

    /**
     * 图片导出写入处理handler
     * @param containImage   是否包含图片，外部传入为准
     * @param clazz          导出dto class
     * @param builder        sheet builder
     * @param excludeFields  导出排除列属性字段名
     */
    private static void imageWriteHandler(Boolean containImage, Class clazz, ExcelWriterSheetBuilder builder, List<String> excludeFields, List rows){
        imageWriteHandler(containImage, clazz, null, builder, excludeFields, null, rows);
    }

    /**
     * 图片导出写入处理handler
     * @param clazz          导出dto class
     * @param builder        sheet builder
     * @param includeFields  导出列属性字段名
     */
    private static void imageWriteHandlerWithIncludeFields(Class clazz, ExcelWriterSheetBuilder builder, List<String> includeFields, List rows){
        imageWriteHandler(null, clazz, null, builder, null,includeFields, rows);
    }

    /**
     * 图片导出写入处理handler
     * @param containImage     是否包含图片，外部传入为准
     * @param clazz            导出dto class
     * @param builder          sheet builder
     * @param excludeFields    导出排除列属性字段名
     * @param bindingFieldMap  绑定属性集合，例如用来解决，【图片】和【原始链接】字段，需要在页面上只展示【图片】，字段，但是导出需要一起导出【图片】【原始链接】，或者都不导出，可以为空，就会去class类上个根据@ExcelImageUrl的bindingPathField来确定
     */
    private static void imageWriteHandler(Boolean containImage, Class clazz, Map<String, String> bindingFieldMap
            , ExcelWriterSheetBuilder builder, List<String> excludeFields, List<String> includeFields, List rows){
        // 递归所有父类获取所有属性,获取绑定属性，即如果排除图片字段，也排除图片原始链接字段
        if(bindingFieldMap == null){
            bindingFieldMap = ExcelImageUtil.getBindingFieldMap(clazz);
        }
        // 因为后面excludeFields可能有赋值操作，因此此处转换为本的可操作List
        List<String> excludeFieldsLocal = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(excludeFields)){
            excludeFieldsLocal.addAll(excludeFields);
        }
        for (Map.Entry<String, String> entry: bindingFieldMap.entrySet()) {
            String dataByteField = entry.getKey();
            String bindingPathField = entry.getValue();
            if(excludeFieldsLocal != null && excludeFieldsLocal.contains(dataByteField)){
                excludeFieldsLocal.add(bindingPathField);
            }
        }
        //用于排除图片原始链接字段
//        builder.excludeColumnFiledNames(excludeFieldsLocal);

        // 获取是否包含图片字段，如果包含需要使用图片样式
        if(containImage == null){
            containImage = checkContainImageField(clazz ,excludeFieldsLocal, includeFields);
        }
        if(!containImage){
            return;
        }
        // 注册图片的处理handler
        builder.registerWriteHandler(new ImageExportHandler());

        // 去掉默认的样式
        List<WriteHandler> customWriteHandlerList = builder.build().getCustomWriteHandlerList();
        if(!CollectionUtils.isEmpty(customWriteHandlerList)) {
            for (Iterator<WriteHandler> iterator = customWriteHandlerList.iterator(); iterator.hasNext(); ) {
                WriteHandler writeHandler =  iterator.next();
                if(writeHandler instanceof NotRepeatExecutor){
                    NotRepeatExecutor executor = (NotRepeatExecutor)writeHandler;
                    for (int i = 0; i < imageExcludeStrategys.size(); i++) {
                        if(imageExcludeStrategys.get(i).equals(executor.uniqueValue())){
                            iterator.remove();
                        }
                    }
                }
            }
        }

        // 异步图片下载
        ExcelImageUtil.asyncSetImageData(rows, clazz, excludeFieldsLocal);

    }
    /**
     * <AUTHOR>
     * 导出类是否包含图片字段
     * @param clazz
     * @param excludeFields 排除导出字段
     * @return
     */
    public static boolean checkContainImageField(Class clazz, List<String> excludeFields) {
        return checkContainImageField(clazz, excludeFields, null);
    }

    /**
     * <AUTHOR>
     * 导出类是否包含图片字段
     * @param clazz
     * @param excludeFields 排除导出字段
     * @return
     */
    public static boolean checkContainImageField(Class clazz, List<String> excludeFields, List<String> includeFields) {
        if (clazz == null) {
            return false;
        }

        // 递归所有父类获取所有属性
        List<Field> fieldList = new ArrayList<>();
        getFields(clazz, fieldList);

        // 遍历所有属性字段，判断是否包含图片字段
        for (int i = 0; i < fieldList.size(); i++) {
            Field field = fieldList.get(i);
            if(checkImageField(field, excludeFields, includeFields)){
                return true;
            }
        }

        return false;
    }

    /**
     * 递归所有父类获取所有属性字段
     * @param clazz
     * @param fieldList
     */
    public static void getFields(Class clazz, List<Field> fieldList) {
        if(clazz == null){
            return;
        }
        // 如果父类是Object就跳过
        if (clazz.toString().equals("class java.lang.Object")) {
            return;
        }
        // 递归获取所有属性
        Field[] fields = clazz.getDeclaredFields();
        if(fields != null && fields.length > 0){
            for (int i = 0; i < fields.length; i++) {
                fieldList.add(fields[i]);
            }
        }
        getFields(clazz.getSuperclass(), fieldList);
    }


    /**
     * 判断field是否是图片字段
     * @param f
     * @param excludeFields
     * @return
     */
    private static boolean checkImageField(Field f, List<String> excludeFields, List<String> includeFields) {
        ExcelIgnore excelIgnore = f.getAnnotation(ExcelIgnore.class);
        if(excelIgnore != null){
            return false;
        }
        ExcelProperty excelProperty= f.getAnnotation(ExcelProperty.class);
        if(excelProperty == null){
            return false;
        }
        // 如果包含列有值，说明需要从包含列中取字段
        if(CollectionUtils.isNotEmpty(includeFields)){
            if(!includeFields.contains(f.getName())){
                return false;
            }
        }
        if(f.getGenericType().equals(String.class) && excelProperty.converter().equals(StringImageConverter.class) && (excludeFields == null || !excludeFields.contains(f.getName()))){
            return true;
        }
        if(f.getGenericType().equals(URL.class)&& (excludeFields == null ||  !excludeFields.contains(f.getName()))){
            return true;
        }
        if(f.getGenericType().equals(byte[].class)&& (excludeFields == null || !excludeFields.contains(f.getName()))){
            return true;
        }
        return false;
    }

    /**
     * 如果图片导出，排除默认样式的策略名
     */
    private static List<String> imageExcludeStrategys = Arrays.asList("RowHighStyleStrategy", "ColumnWidthStyleStrategy");

    private static final char[] DIGITS = {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f'};
    private static final int IMAGE_TYPE_MARK_LENGTH = 28;

    private static final Map<String, Integer> FILE_TYPE_MAP;

    /**
     * Default image type
     */
    public static int defaultImageType = Workbook.PICTURE_TYPE_PNG;

    static {
        FILE_TYPE_MAP = new HashMap<>();
        FILE_TYPE_MAP.put("ffd8ff", Workbook.PICTURE_TYPE_JPEG);
        FILE_TYPE_MAP.put("89504e47", Workbook.PICTURE_TYPE_PNG);
    }

    public static int getImageTypeFormat(byte[] image) {
        Integer imageType = getImageType(image);
        if (imageType != null) {
            return imageType;
        }
        return defaultImageType;
    }

    public static Integer getImageType(byte[] image) {
        if (image == null || image.length <= IMAGE_TYPE_MARK_LENGTH) {
            return null;
        }
        byte[] typeMarkByte = new byte[IMAGE_TYPE_MARK_LENGTH];
        System.arraycopy(image, 0, typeMarkByte, 0, IMAGE_TYPE_MARK_LENGTH);
        return FILE_TYPE_MAP.get(encodeHexStr(typeMarkByte));
    }

    private static String encodeHexStr(byte[] data) {
        final int len = data.length;
        final char[] out = new char[len << 1];
        // two characters from the hex value.
        for (int i = 0, j = 0; i < len; i++) {
            out[j++] = DIGITS[(0xF0 & data[i]) >>> 4];
            out[j++] = DIGITS[0x0F & data[i]];
        }
        return new String(out);
    }

    public static int getCoordinate(Integer coordinate) {
        if (coordinate == null) {
            return 0;
        }
        return Units.toEMU(coordinate);
    }


}
