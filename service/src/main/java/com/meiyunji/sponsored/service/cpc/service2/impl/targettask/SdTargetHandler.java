package com.meiyunji.sponsored.service.cpc.service2.impl.targettask;

import com.amazon.advertising.mode.targeting.Expression;
import com.amazon.advertising.mode.targeting.ExpressionEnum;
import com.amazon.advertising.mode.targeting.TargetingClauseResult;
import com.amazon.advertising.sd.constant.TacticEnum;
import com.amazon.advertising.sd.entity.targeting.CreateTargetingClausesResponse;
import com.amazon.advertising.sd.entity.targeting.ProductTargetingClient;
import com.amazon.advertising.sd.mode.ExpressionNested;
import com.amazon.advertising.sd.mode.TargetingClause;
import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.config.DynamicRefreshNacosConfiguration;
import com.meiyunji.sponsored.common.util.JSONUtil;
import com.meiyunji.sponsored.common.util.ResultUtil;
import com.meiyunji.sponsored.common.util.ThreadPoolUtil;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.account.service.IShopAuthService;
import com.meiyunji.sponsored.service.cpc.constants.AdTargetObjectTypeEnum;
import com.meiyunji.sponsored.service.cpc.constants.AdTargetTaskConstant;
import com.meiyunji.sponsored.service.cpc.constants.AdTargetTaskStatusEnum;
import com.meiyunji.sponsored.service.cpc.constants.TargetingTypeEnum;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdProfileDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonSdAdGroupDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonSdAdTargetingDao;
import com.meiyunji.sponsored.service.cpc.dto.AdTargetDetailDto;
import com.meiyunji.sponsored.service.cpc.dto.CommonAmazonAdTargeting;
import com.meiyunji.sponsored.service.cpc.po.*;
import com.meiyunji.sponsored.service.cpc.service.ICommonAmazonAdTargetingService;
import com.meiyunji.sponsored.service.cpc.service2.sd.ICpcSdTargetingService;
import com.meiyunji.sponsored.service.cpc.service2.sd.impl.CpcSdTargetingApiService;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.util.CpcApiHelper;
import com.meiyunji.sponsored.service.cpc.util.CpcStatusEnum;
import com.meiyunji.sponsored.service.log.enums.OperationLogResultEnum;
import com.meiyunji.sponsored.service.log.po.AdManageOperationLog;
import com.meiyunji.sponsored.service.log.service.IAdManageOperationLogService;
import com.meiyunji.sponsored.service.util.AmazonErrorUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: nongwenhua
 * @email: <EMAIL>
 * @create: 2024-05-08 19:23
 */
@Service(AdTargetTaskConstant.SD_TARGET_HANDLER)
@Slf4j
public class SdTargetHandler implements TargetTaskHandler {

    @Autowired
    private ICommonAmazonAdTargetingService commonAmazonAdTargetingService;
    @Autowired
    private IAmazonSdAdTargetingDao amazonSdAdTargetingDao;
    @Autowired
    private TargetTaskComponent targetTaskComponent;
    @Autowired
    private IAmazonAdProfileDao amazonAdProfileDao;
    @Autowired
    private IScVcShopAuthDao shopAuthDao;
    @Autowired
    private CpcApiHelper cpcApiHelper;
    @Autowired
    private DynamicRefreshNacosConfiguration dynamicRefreshNacosConfiguration;
    @Autowired
    private IShopAuthService shopAuthService;
    @Autowired
    private IAmazonSdAdGroupDao amazonSdAdGroupDao;
    @Autowired
    private CpcSdTargetingApiService cpcSdTargetingApiService;
    @Autowired
    private ICpcSdTargetingService cpcSdTargetingService;
    @Autowired
    private IAdManageOperationLogService adOperationLogService;

    @Override
    public void handle(AdTargetTask adTargetTask, List<AdTargetTaskDetail> adTargetTaskDetails) {
        //处理业务返回结果
        createTargeting(adTargetTask, adTargetTaskDetails);
    }

    private void createTargeting(AdTargetTask adTargetTask, List<AdTargetTaskDetail> adTargetTaskDetails) {
        List<AdTargetTaskDetail> needUpdateDetailList = new ArrayList<>();
        if (CollectionUtils.isEmpty(adTargetTaskDetails)) {
            targetTaskComponent.updateTaskStatus(adTargetTask, needUpdateDetailList);
            return;
        }

        // 排除已存在的关键词
        Set<String> adGroupIdSet = new HashSet<>();
        Map<Long, AdTargetTaskDetail> adTargetTaskDetailMap = new HashMap<>();
        for (AdTargetTaskDetail targetTaskDetail : adTargetTaskDetails) {
            adGroupIdSet.add(targetTaskDetail.getAdGroupId());
            adTargetTaskDetailMap.put(targetTaskDetail.getId(), targetTaskDetail);
        }

        if (CollectionUtils.isEmpty(adGroupIdSet)) {
            targetTaskComponent.updateTaskStatus(adTargetTask, needUpdateDetailList);
            return;
        }

        List<AmazonSdAdGroup> amazonSbAdGroups = amazonSdAdGroupDao.listByGroupId(adTargetTask.getPuid(), adTargetTask.getShopId(), new ArrayList<>(adGroupIdSet));
        Map<String, AmazonSdAdGroup> amazonAdGroupMap = amazonSbAdGroups.stream().collect(Collectors.toMap(AmazonSdAdGroup::getAdGroupId, Function.identity(), (newVal, oldVal) -> newVal));

        int originalTaskDetailSize = adTargetTaskDetails.size();
        List<AmazonSdAdTargeting> amazonAdTargetings = convertAddTargetingVoToPO(adTargetTask.getUid(), amazonAdGroupMap, adTargetTaskDetails, needUpdateDetailList, adTargetTask.getTargetingType());
        if (CollectionUtils.isEmpty(amazonAdTargetings)) {
            targetTaskComponent.updateTaskStatus(adTargetTask, needUpdateDetailList);
            return;
        }

        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(adTargetTask.getShopId(), adTargetTask.getPuid());
        if (shop == null) {
            adTargetTaskDetails.forEach(each -> {
                each.setFailureReason("没有CPC授权");
                each.setStatus(AdTargetTaskStatusEnum.FAILURE.getCode());
                needUpdateDetailList.add(each);
            });
            targetTaskComponent.updateTaskStatus(adTargetTask, needUpdateDetailList);
            return;
        }

        AmazonAdProfile profile = amazonAdProfileDao.getProfile(adTargetTask.getPuid(), adTargetTask.getShopId());
        if (profile == null) {
            adTargetTaskDetails.forEach(each -> {
                each.setFailureReason("没有站点对应的配置信息");
                each.setStatus(AdTargetTaskStatusEnum.FAILURE.getCode());
                needUpdateDetailList.add(each);
            });
            targetTaskComponent.updateTaskStatus(adTargetTask, needUpdateDetailList);
            return;
        }

        int failureNum = 0;
        List<List<AmazonSdAdTargeting>> amazonAdTargetingPartition = Lists.partition(amazonAdTargetings, AdTargetTaskConstant.MAX_SD_TARGET_SIZE);
        for (List<AmazonSdAdTargeting> amazonAdTargetingList : amazonAdTargetingPartition) {
            Result result = create(amazonAdTargetingList, adTargetTaskDetailMap, shop, profile);
            logSdTargetCreate(amazonAdTargetingList, adTargetTask.getLoginIp(), result);
            for (AmazonSdAdTargeting amazonSdAdTargeting : amazonAdTargetingList) {
                AdTargetTaskDetail adTargetTaskDetail = adTargetTaskDetailMap.get(amazonSdAdTargeting.getTargetTaskDetailId());
                needUpdateDetailList.add(adTargetTaskDetail);
                if (AdTargetTaskStatusEnum.FAILURE.getCode() == adTargetTaskDetail.getStatus()) {
                    failureNum++;
                }
            }
            if (result.success()) {
                List<AmazonSdAdTargeting> succList = amazonAdTargetingList.stream().filter(e -> StringUtils.isNotBlank(e.getTargetId())).collect(Collectors.toList());
                if (succList.size() > 0) {
                    // 有可能已经添加过了: 重复添加同一个投放，接口不会报错，会返回其对应的targetId
                    List<String> existInDB = amazonSdAdTargetingDao.listByTargetId(adTargetTask.getPuid(), adTargetTask.getShopId(), succList.stream()
                            .map(AmazonSdAdTargeting::getTargetId).collect(Collectors.toList())).stream().map(AmazonSdAdTargeting::getTargetId).collect(Collectors.toList());
                    // 排除掉已有的
                    if (CollectionUtils.isNotEmpty(existInDB)) {
                        succList = succList.stream().filter(e -> !existInDB.contains(e.getTargetId())).collect(Collectors.toList());
                    }
                    // 入库
                    try {
                        amazonSdAdTargetingDao.batchAdd(adTargetTask.getPuid(), succList);
                    } catch (Exception e) {
                        log.error("createSdTargeting:", e);
                    }

                    Map<String, List<String>> adCampaignId2TargetIds = new HashMap<>();
                    for (AmazonSdAdTargeting amazonSdAdTargeting : succList) {
                        String adGroupId = amazonSdAdTargeting.getAdGroupId();
                        if (StringUtils.isBlank(adGroupId)) {
                            continue;
                        }
                        AmazonSdAdGroup amazonSdAdGroup = amazonAdGroupMap.get(adGroupId);
                        if (amazonSdAdGroup == null) {
                            continue;
                        }
                        String campaignId = amazonSdAdGroup.getCampaignId();
                        List<String> targetIds = adCampaignId2TargetIds.get(campaignId);
                        if (CollectionUtils.isEmpty(targetIds)) {
                            targetIds = new ArrayList<>();
                        }
                        targetIds.add(amazonSdAdTargeting.getTargetId());
                        adCampaignId2TargetIds.put(campaignId, targetIds);
                    }
                    List<String> adCampaignIds = new ArrayList<>(adCampaignId2TargetIds.keySet());
                    adCampaignIds.forEach(each -> {
                        ThreadPoolExecutor pool = ThreadPoolUtil.getSdTargetSyncPool();
                        pool.execute(() -> {
                            cpcSdTargetingApiService.syncTargetings(shop, each);
                            cpcSdTargetingService.saveDoris(adTargetTask.getPuid(), adTargetTask.getShopId(), adCampaignId2TargetIds.get(each));
                        });
                    });
                }
            }
            targetTaskComponent.updateTaskDetailStatus(adTargetTask, needUpdateDetailList);
            needUpdateDetailList.clear();
        }

        failureNum += originalTaskDetailSize - amazonAdTargetings.size();
        int adTargetTaskStatus = targetTaskComponent.getAdTargetTaskStatus(originalTaskDetailSize, failureNum);
        targetTaskComponent.updateTaskStatus(adTargetTask, adTargetTaskStatus);
    }

    private void logSdTargetCreate(List<AmazonSdAdTargeting> targetList, String ip, Result result) {
        try {
            if (CollectionUtils.isEmpty(targetList)) {
                return;
            }
            List<AdManageOperationLog> operationLogs = Lists.newArrayList();
            for (AmazonSdAdTargeting targeting : targetList) {
                AdManageOperationLog targetLog = adOperationLogService.getSdTargetsLog(null, targeting);
                targetLog.setIp(ip);
                if (StringUtils.isNotBlank(targeting.getTargetId()) && !StringUtils.equalsIgnoreCase(targeting.getTargetId(), "null")) {
                    targetLog.setResult(OperationLogResultEnum.SUCCESS.getResultValue());
                } else {
                    targetLog.setResult(OperationLogResultEnum.FAIL.getResultValue());
                    if (StringUtils.isNotBlank(targeting.getErrMsg())) {
                        targetLog.setResultInfo(targeting.getErrMsg());
                    } else {
                        targetLog.setResultInfo(result.getMsg());
                    }
                }
                operationLogs.add(targetLog);
            }
            adOperationLogService.batchLogsMergeByAdGroup(operationLogs);
        } catch (Exception e) {
            log.error("SD商品投放创建日志异常", e);
        }
    }

    private Result create(List<AmazonSdAdTargeting> amazonSdAdTargetings, Map<Long, AdTargetTaskDetail> adTargetTaskDetailMap, ShopAuth shop, AmazonAdProfile profile) {
        List<TargetingClause> targetingClauses = amazonSdAdTargetings.stream().map(e -> {
            TargetingClause targetingClause = new TargetingClause();
            if (StringUtils.isNotBlank(e.getAdGroupId())) {
                targetingClause.setAdGroupId(Long.valueOf(e.getAdGroupId()));
            }
            targetingClause.setExpressionType(e.getExpressionType());
            targetingClause.setState(e.getState());
            if (e.getBid() != null) {
                targetingClause.setBid(e.getBid().doubleValue());
            }
            if (StringUtils.isNotBlank(e.getExpression())) {
                targetingClause.setExpressions(JSONUtil.jsonToArray(e.getExpression(), ExpressionNested.class));
            }
            return targetingClause;
        }).collect(Collectors.toList());

        CreateTargetingClausesResponse response = cpcApiHelper.call(shop, () -> ProductTargetingClient.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable())
                .create(shopAuthService.getAdToken(shop), profile.getProfileId(),
                        shop.getMarketplaceId(), targetingClauses));

        if (response == null) {
            for (AmazonSdAdTargeting amazonAdTargeting : amazonSdAdTargetings) {
                AdTargetTaskDetail adTargetTaskDetail = adTargetTaskDetailMap.get(amazonAdTargeting.getTargetTaskDetailId());
                adTargetTaskDetail.setFailureReason("网络延迟，请稍后重试");
                adTargetTaskDetail.setStatus(AdTargetTaskStatusEnum.FAILURE.getCode());
            }
            return ResultUtil.returnErr("网络延迟，请稍后重试");
        }

        //处理返回结果中的错误信息
        String errMsg = "网络延迟，请稍后重试";
        String formatErrMsg = errMsg;
        if (CollectionUtils.isNotEmpty(response.getResultList())) {
            List<TargetingClauseResult> resultList = response.getResultList();
            int index = 0;
            for (TargetingClauseResult productAdResult : resultList) {
                if ("SUCCESS".equals(productAdResult.getCode())) {
                    AmazonSdAdTargeting amazonAdTargeting = amazonSdAdTargetings.get(index);
                    amazonAdTargeting.setTargetId(String.valueOf(productAdResult.getTargetId()));

                    AdTargetTaskDetail adTargetTaskDetail = adTargetTaskDetailMap.get(amazonAdTargeting.getTargetTaskDetailId());
                    adTargetTaskDetail.setStatus(AdTargetTaskStatusEnum.SUCCESS.getCode());
                } else {
                    AmazonSdAdTargeting amazonAdTargeting = amazonSdAdTargetings.get(index);
                    amazonAdTargeting.setTargetId(String.valueOf(productAdResult.getTargetId()));
                    String returnError = StringUtils.defaultIfBlank(productAdResult.getDetails(), productAdResult.getDescription());
                    String error = AmazonErrorUtils.getError(returnError);

                    amazonSdAdTargetings.get(index).setErrMsg(error);

                    AdTargetTaskDetail adTargetTaskDetail = adTargetTaskDetailMap.get(amazonAdTargeting.getTargetTaskDetailId());
                    adTargetTaskDetail.setFailureReason(targetTaskComponent.getError(error, returnError));
                    adTargetTaskDetail.setFailureReasonDetail(returnError);
                    adTargetTaskDetail.setStatus(AdTargetTaskStatusEnum.FAILURE.getCode());
                }
                index++;
            }

            return ResultUtil.success();
        } else if (response.getError() != null) {
            String returnErrMsg = errMsg;
            if ("403".equals(response.getError().getCode())) {
                returnErrMsg = "店铺没有SD广告权限，请到Amazon后台开通SD广告管理";
            } else if (StringUtils.isNotBlank(response.getError().getDetails())) {
                returnErrMsg = response.getError().getDetails();
            }
            errMsg = AmazonErrorUtils.getError(returnErrMsg);
            formatErrMsg = targetTaskComponent.getError(errMsg, returnErrMsg);
        }
        for (AmazonSdAdTargeting amazonAdTargeting : amazonSdAdTargetings) {
            AdTargetTaskDetail adTargetTaskDetail = adTargetTaskDetailMap.get(amazonAdTargeting.getTargetTaskDetailId());
            adTargetTaskDetail.setFailureReason(formatErrMsg);
            adTargetTaskDetail.setFailureReasonDetail(errMsg);
            adTargetTaskDetail.setStatus(AdTargetTaskStatusEnum.FAILURE.getCode());
        }
        return ResultUtil.error(errMsg);
    }

    private List<AmazonSdAdTargeting> convertAddTargetingVoToPO(Integer uid, Map<String, AmazonSdAdGroup> amazonAdGroupMap, List<AdTargetTaskDetail> adTargetTaskDetails, List<AdTargetTaskDetail> needUpdateDetailList, String targetingType) {
        List<AmazonSdAdTargeting> amazonAdTargetList = new ArrayList<>(adTargetTaskDetails.size());
        AmazonSdAdTargeting target;
        ExpressionNested expressionNested;

        Expression expression;
        Iterator<AdTargetTaskDetail> it = adTargetTaskDetails.iterator();
        AdTargetTaskDetail adTargetTaskDetail;
        List<String> targetIds = adTargetTaskDetails.stream().map(AdTargetTaskDetail::getTargetId).distinct().collect(Collectors.toList());
        AdTargetTaskDetail first = adTargetTaskDetails.get(0);
        List<Integer> sourceShopIds = adTargetTaskDetails.stream().map(AdTargetTaskDetail::getSourceShopId).distinct().collect(Collectors.toList());
        Map<String, AdTargetDetailDto> targetDetailMap = buildTargetDetailMap(first.getPuid(), targetIds, sourceShopIds, targetingType);
        while (it.hasNext()) {
            adTargetTaskDetail = it.next();
            AmazonSdAdGroup amazonSdAdGroup = amazonAdGroupMap.get(adTargetTaskDetail.getAdGroupId());
            if (amazonSdAdGroup == null) {
                adTargetTaskDetail.setStatus(AdTargetTaskStatusEnum.FAILURE.getCode());
                adTargetTaskDetail.setFailureReason("广告组不存在");
                needUpdateDetailList.add(adTargetTaskDetail);
                it.remove();
            } else {
                String type = AdTargetObjectTypeEnum.getTargetTypeByCode(adTargetTaskDetail.getTargetObjectType());
                target = new AmazonSdAdTargeting();
                amazonAdTargetList.add(target);

                target.setPuid(amazonSdAdGroup.getPuid());
                target.setShopId(amazonSdAdGroup.getShopId());
                target.setMarketplaceId(amazonSdAdGroup.getMarketplaceId());
                target.setProfileId(amazonSdAdGroup.getProfileId());
                target.setCampaignId(amazonSdAdGroup.getCampaignId());
                target.setAdGroupId(amazonSdAdGroup.getAdGroupId());
                target.setExpressionType(Constants.MANUAL);
                target.setState(CpcStatusEnum.enabled.name());
                target.setType(type);
                target.setBid(adTargetTaskDetail.getBid());
                target.setCreateId(uid);
                target.setCreateInAmzup(1);
                target.setTargetTaskDetailId(adTargetTaskDetail.getId());

                if (adTargetTaskDetail.getSuggested() != null) {
                    target.setSuggested(adTargetTaskDetail.getSuggested());
                }
                if (adTargetTaskDetail.getRangeStart() != null) {
                    target.setRangeStart(adTargetTaskDetail.getRangeStart());
                }
                if (adTargetTaskDetail.getRangeEnd() != null) {
                    target.setRangeEnd(adTargetTaskDetail.getRangeEnd());
                }

                List<Expression> expressions = new ArrayList<>();
                if (SdTargetTypeEnum.asin.name().equals(type)) {
                    expression = new Expression();
                    expression.setType(ExpressionEnum.asinSameAs.value());
                    expressions.add(expression);
                    if (StringUtils.isEmpty(adTargetTaskDetail.getTargetId())) {
                        expression.setValue(adTargetTaskDetail.getTargetObject());
                        target.setTargetText(adTargetTaskDetail.getTargetObject());
                        target.setImgUrl(adTargetTaskDetail.getImgUrl());
                        target.setTitle(adTargetTaskDetail.getTargetObjectDesc());
                    } else {
                        AdTargetDetailDto adTargetDetail = targetDetailMap.get(adTargetTaskDetail.getTargetId());
                        expression.setValue(adTargetDetail.getTargetObject());
                        target.setTargetText(adTargetDetail.getTargetObject());
                        target.setImgUrl(adTargetDetail.getImgUrl());
                        target.setTitle(adTargetDetail.getTargetObjectDesc());
                    }
                } else {
                    // 类目投放任务肯定包含targetId
                    AdTargetDetailDto adTargetDetail = targetDetailMap.get(adTargetTaskDetail.getTargetId());
                    expression = new Expression();
                    expression.setType(ExpressionEnum.asinCategorySameAs.value());
                    expression.setValue(adTargetDetail.getCategoryId());
                    expressions.add(expression);
                    target.setTargetText(adTargetDetail.getTargetObject());
                    target.setCategoryName(adTargetDetail.getCategoryName());
                    target.setBrandName(adTargetDetail.getBrandName());
                    target.setMinPrice(adTargetDetail.getMinPrice());
                    target.setMaxPrice(adTargetDetail.getMaxPrice());
                    if (StringUtils.isNotBlank(adTargetDetail.getMinReviewRating())) {
                        target.setMinReviewRating(Integer.valueOf(adTargetDetail.getMinReviewRating()));
                    }
                    if (StringUtils.isNotBlank(adTargetDetail.getMaxReviewRating())) {
                        target.setMaxReviewRating(Integer.valueOf(adTargetDetail.getMaxReviewRating()));
                    }
//                    target.setPrimeShippingEligible(adTargetDetail.asinIsPrimeShippingEligible());

                    if (StringUtils.isNotBlank(adTargetDetail.getBrandId())) {
                        expression = new Expression();
                        expression.setType(ExpressionEnum.asinBrandSameAs.value());
                        expression.setValue(adTargetDetail.getBrandId());
                        expressions.add(expression);
                    }
                    if (StringUtils.isNotBlank(adTargetDetail.getMinPrice()) || StringUtils.isNotBlank(adTargetDetail.getMaxPrice())) {
                        expression = new Expression();
                        if (StringUtils.isBlank(adTargetDetail.getMinPrice())) {
                            expression.setType(ExpressionEnum.asinPriceLessThan.value());
                            expression.setValue(adTargetDetail.getMaxPrice());
                            expressions.add(expression);
                        } else if (StringUtils.isBlank(adTargetDetail.getMaxPrice())) {
                            expression.setType(ExpressionEnum.asinPriceGreaterThan.value());
                            expression.setValue(adTargetDetail.getMinPrice());
                            expressions.add(expression);
                        } else {
                            expression.setType(ExpressionEnum.asinPriceBetween.value());
                            expression.setValue(adTargetDetail.getMinPrice() + "-" + adTargetDetail.getMaxPrice());
                            expressions.add(expression);
                        }
                    }
                    if (adTargetDetail.getMinReviewRating() != null || adTargetDetail.getMaxReviewRating() != null) {
                        expression = new Expression();
                        if (adTargetDetail.getMinReviewRating() == null) {
                            expression.setType(ExpressionEnum.asinReviewRatingLessThan.value());
                            expression.setValue(String.valueOf(adTargetDetail.getMaxReviewRating()));
                            expressions.add(expression);
                        } else if (adTargetDetail.getMaxReviewRating() == null) {
                            expression.setType(ExpressionEnum.asinReviewRatingGreaterThan.value());
                            expression.setValue(String.valueOf(adTargetDetail.getMinReviewRating()));
                            expressions.add(expression);
                        } else {
                            expression.setType(ExpressionEnum.asinReviewRatingBetween.value());
                            expression.setValue(adTargetDetail.getMinReviewRating() + "-" + adTargetDetail.getMaxReviewRating());
                            expressions.add(expression);
                        }
                    }
                    if (adTargetDetail.getAsinIsPrimeShippingEligible() != null) {
                        expression = new Expression();
                        expression.setType(ExpressionEnum.asinIsPrimeShippingEligible.value());
                        expression.setValue(adTargetDetail.getAsinIsPrimeShippingEligible());
                        expressions.add(expression);
                    }
                }
                List<ExpressionNested> expressionNesteds = new ArrayList<>();
                for (Expression e : expressions) {
                    expressionNested = new ExpressionNested();
                    expressionNested.setType(e.getType());
                    expressionNested.setValue(e.getValue());
                    expressionNesteds.add(expressionNested);
                }
                target.setExpression(JSONUtil.objectToJson(expressionNesteds));

            }
        }

        return amazonAdTargetList;
    }

    @Override
    public Map<String, AdTargetDetailDto> buildTargetDetailMap(Integer puid, List<String> targetIds, List<Integer> sourceShopIds, String targetingType) {
        List<CommonAmazonAdTargeting> list = commonAmazonAdTargetingService.listByTargetIds(targetingType, puid, sourceShopIds, targetIds);
        return list.stream().map(each -> {
            AdTargetDetailDto adTargetDetail = new AdTargetDetailDto();
            adTargetDetail.setTargetId(each.getTargetId());
            String type = each.getType();
            if (TargetTypeEnum.asin.name().equals(type)) {
                adTargetDetail.setTargetObject(each.getTargetingValue());
                adTargetDetail.setTargetObjectDesc(each.getTitle());
                adTargetDetail.setImgUrl(each.getImgUrl());
            } else if (TargetTypeEnum.category.name().equals(type)) {
                adTargetDetail.setTargetObject(each.getTargetingValue());
                boolean isSp = TargetingTypeEnum.SP_TARGETING_CODE_LIST.contains(targetingType);
                targetTaskComponent.fillCategoryDetail(adTargetDetail, each, isSp);
            }
            return adTargetDetail;
        }).collect(Collectors.toMap(AdTargetDetailDto::getTargetId, Function.identity(), (newVal, oldVal) -> newVal));
    }
}
