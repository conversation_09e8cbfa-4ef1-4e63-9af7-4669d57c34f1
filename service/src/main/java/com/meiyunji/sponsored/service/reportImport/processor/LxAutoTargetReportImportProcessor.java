package com.meiyunji.sponsored.service.reportImport.processor;

import com.amazonaws.util.json.Jackson;
import com.meiyunji.sellfox.aadas.types.enumeration.Marketplace;
import com.meiyunji.sponsored.common.exception.BizServiceException;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.cpc.dao.*;
import com.meiyunji.sponsored.service.cpc.po.*;
import com.meiyunji.sponsored.service.cpc.service.IAmazonAdKeywordDaoRoutingService;
import com.meiyunji.sponsored.service.cpc.service.IAmazonAdTargetDaoRoutingService;
import com.meiyunji.sponsored.service.enums.AutoTargetTypeEnum;
import com.meiyunji.sponsored.service.reportImport.enums.ReportImportErrType;
import com.meiyunji.sponsored.service.reportImport.message.AdReportImportMessage;
import com.meiyunji.sponsored.service.reportImport.model.BaseLxReport;
import com.meiyunji.sponsored.service.reportImport.model.LxAutoTargetReport;
import com.meiyunji.sponsored.service.reportImport.vo.DuplicationCampaignVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * lx自动投放报告导入处理器
 *
 * <AUTHOR>
 * @date 2023/05/26
 */
@Service
@Slf4j
public class LxAutoTargetReportImportProcessor extends AbstractLxReportImportProcessor<LxAutoTargetReport> {
    private final ICpcTargetingReportDao cpcTargetingReportDao;
    @Resource
    private IAmazonAdTargetDaoRoutingService amazonAdTargetDaoRoutingService;

    protected LxAutoTargetReportImportProcessor(
            IAmazonAdCampaignAllDao amazonAdCampaignAllDao, IAmazonAdGroupDao amazonAdGroupDao,
            IAmazonSdAdGroupDao amazonSdAdGroupDao, IAmazonSbAdGroupDao amazonSbAdGroupDao,
            ICpcTargetingReportDao cpcTargetingReportDao) {
        super(amazonAdCampaignAllDao, amazonAdGroupDao, amazonSdAdGroupDao, amazonSbAdGroupDao);
        this.cpcTargetingReportDao = cpcTargetingReportDao;
    }

    /**
     * 导入报告
     *
     * @param importMessage           导入消息
     * @param reports                 报告
     * @param shopAuthMap    店铺数据
     */
    @Override
    public void importReport(AdReportImportMessage importMessage, List<LxAutoTargetReport> reports, Map<String, ShopAuth> shopAuthMap) {
        Integer puid = importMessage.getPuid();
        Long taskId = importMessage.getScheduleId();

        List<Integer> shopIds = shopAuthMap.values().stream().map(ShopAuth::getId).collect(Collectors.toList());
        //按活动名称查询所有广告活动
        List<AmazonAdCampaignAll> allCampaigns = listByTypeAndCampaignNames(puid, reports, shopIds);

        //记录重复活动
        Map<String, List<AmazonAdCampaignAll>> mapList = allCampaigns.stream()
                .collect(Collectors.groupingBy(k -> getCampaignKeyFormat(
                        k.getShopId(), k.getType(), k.getName())));
        for (Map.Entry<String, List<AmazonAdCampaignAll>> entry : mapList.entrySet()) {
            if (entry.getValue().size() > 1) {
                Map<Integer, ShopAuth> shopIdMap = shopAuthMap.values().stream().collect(Collectors.toMap(ShopAuth::getId, Function.identity(), (a, b) -> a));
                Map<String, String> map = new HashMap<>(3);
                map.put("campaignName", entry.getValue().get(0).getName());
                map.put("country", Marketplace.fromId(shopIdMap.get(entry.getValue().get(0).getShopId()).getMarketplaceId()).getCountryCode());
                map.put("shopName", shopIdMap.get(entry.getValue().get(0).getShopId()).getName());
                throw new BizServiceException(ReportImportErrType.DUPLICATION_CAMPAIGN.name(), Jackson.toJsonString(map));
            }
        }

        Map<String, AmazonAdCampaignAll> campaignNameMap = getCampaignNameMap(allCampaigns);

        Map<String, List<LxAutoTargetReport>> lxAutoTargetReportMap = reports.stream().collect(
                Collectors.groupingBy(LxAutoTargetReport::getAdType));

        //填充campaignId,用于更精确查询ad-group
        lxAutoTargetReportMap.forEach((k, v) -> {
            fillCampaignIdForReport(puid, campaignNameMap, v);
        });

        Map<String, AmazonAdGroup> spGroupMap = getSpAdGroupMap(puid, reports);
        //填充groupId,用于更精确查询
        lxAutoTargetReportMap.forEach((k, v) -> {
            for (LxAutoTargetReport report : v) {
                String mapKey = getGroupKeyFormat( report.getShopId(), report.getCampaignId(), report.getAdGroupName());
                if (!spGroupMap.containsKey(mapKey)) {
                    throw new BizServiceException("[行号 "+ (report.getRowNumber() + 1) +"] " + "广告组名称不存在 (广告组名称: " + report.getAdGroupName()+")");
                }
                AmazonAdGroup adGroup = spGroupMap.get(mapKey);
                report.setGroupId(adGroup.getAdGroupId());

                //填充投放类型
                String targetType = "NONE";
                if ("紧密匹配".equalsIgnoreCase(report.getTarget().trim())) {
                    targetType = AutoTargetTypeEnum.queryHighRelMatches.name();
                } else  if ("宽泛匹配".equalsIgnoreCase(report.getTarget().trim())) {
                    targetType = AutoTargetTypeEnum.queryBroadRelMatches.name();
                } else  if ("同类商品".equalsIgnoreCase(report.getTarget().trim())) {
                    targetType = AutoTargetTypeEnum.asinSubstituteRelated.name();
                } else  if ("关联商品".equalsIgnoreCase(report.getTarget().trim())) {
                    targetType = AutoTargetTypeEnum.asinAccessoryRelated.name();
                }
                report.setTargetType(targetType);
            }
        });

        //查询自动投放广告管理数据
        List<String> adGroupIds = reports.stream().map(BaseLxReport::getGroupId).collect(Collectors.toList());
        List<AmazonAdTargeting> adTargetList = amazonAdTargetDaoRoutingService.getAutoTargetsByGroupIds(puid, adGroupIds);
        Map<String, AmazonAdTargeting> targetingMap = adTargetList.stream().collect(Collectors.toMap(k ->
                String.valueOf(k.getShopId()).concat("#").concat(k.getCampaignId()).concat("#").concat(k.getAdGroupId())
                .concat("#").concat(k.getTargetingValue()), Function.identity(), (a, b) -> a));


        List<CpcTargetingReport> spReports = new ArrayList<>();
        for (LxAutoTargetReport report : reports) {
            String mapKey =  String.valueOf(report.getShopId()).concat("#")
                    .concat(report.getCampaignId()).concat("#").concat(report.getGroupId())
                    .concat("#").concat(report.getTargetType());
            if (!targetingMap.containsKey(mapKey)) {
                throw new BizServiceException("[行号 "+ (report.getRowNumber() + 1) +"] " + "投放不存在 (广告组名称: " + report.getAdGroupName() + "; 投放: "+ report.getTarget()+")");
            }
            AmazonAdTargeting amazonAdTargeting = targetingMap.get(mapKey);

            CpcTargetingReport po = new CpcTargetingReport();
            po.setCountDate(report.getSfCountDate());
            po.setPuid(report.getPuid());
            po.setShopId(report.getShopId());
            po.setMarketplaceId(report.getMarketplaceId());
            po.setCampaignId(report.getCampaignId());
            po.setAdGroupId(report.getGroupId());
            po.setTargetId(amazonAdTargeting.getTargetId());
            po.setAdGroupName(report.getAdGroupName());
            po.setCampaignName(report.getCampaignName());

            po.setImpressions(isDxmNumeric(report.getImpressions()) ? Integer.valueOf(report.getImpressions()) : 0);
            po.setClicks(isDxmNumeric(report.getClicks()) ? Integer.valueOf(report.getClicks()) : 0);
            po.setCost(isDxmNumeric(report.getAdCost()) ? new BigDecimal(report.getAdCost()) : BigDecimal.ZERO);
            po.setTotalSales(isDxmNumeric(report.getAdSales()) ? new BigDecimal(report.getAdSales()) : BigDecimal.ZERO);
            po.setAdSales(isDxmNumeric(report.getAdSelfSales()) ? new BigDecimal(report.getAdSelfSales()) : BigDecimal.ZERO);
            po.setOrderNum(0);
            po.setAdOrderNum(0);
            //lx自动投放无销量字段
            po.setSaleNum(isDxmNumeric(report.getAdOrder()) ? Integer.valueOf(report.getAdOrder()) : 0);
            po.setAdSaleNum(isDxmNumeric(report.getAdSelfOrder()) ? Integer.valueOf(report.getAdSelfOrder()) : 0);
            spReports.add(po);
        }

        if (CollectionUtils.isNotEmpty(spReports)) {
            cpcTargetingReportDao.insertList(puid, spReports);
        }



    }

    private void fillCampaignIdForReport(Integer puid, Map<String, AmazonAdCampaignAll> campaignNameMap, List<LxAutoTargetReport> v) {
        for (LxAutoTargetReport report : v) {
            String mapKey = getCampaignKeyFormat(
                    report.getShopId(), report.getAdType().toLowerCase(), report.getCampaignName());
            if (!campaignNameMap.containsKey(mapKey)) {
                throw new BizServiceException("[行号 "+ (report.getRowNumber() + 1) +"] " + "广告活动不存在 (活动名称: " + report.getCampaignName()+")");
            }
            AmazonAdCampaignAll campaignAll = campaignNameMap.get(mapKey);
            report.setCampaignId(campaignAll.getCampaignId());
        }
    }
}
