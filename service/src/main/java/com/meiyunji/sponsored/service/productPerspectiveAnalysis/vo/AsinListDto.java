package com.meiyunji.sponsored.service.productPerspectiveAnalysis.vo;

import lombok.Data;

import java.util.List;

/**
 * @author: sunlinfeng
 * @email: sunin<PERSON>@dianxiaomi.com
 * @date: 2023-09-01  10:16
 */

@Data
public class AsinListDto {
    //ids
    private String ids;
    //asin编码
    private String asin;
    //父asin
    private String parentAsin;
    //msku
    private String msku;
    //店铺id
    private Integer shopId;
    //站点id
    private String marketplaceId;
    // 父Id
    private Long parentId;
    //图片
    private String mainImage;

    // 子asin
    private List<String> childAsin;
    private List<String> childSku;

    private String shopIdStr;
}
