package com.meiyunji.sponsored.service.cpc.vo;

import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.enums.QueryKeywordPageThreadEnum;
import lombok.Data;

@Data
public class PageRequestThreadDto {
    private QueryKeywordPageThreadEnum type;
    private Page page;
    private CpcQueryWordDto dto;
    private int puid;

    public static PageRequestThreadDto getStaticPageRequestThreadDto(QueryKeywordPageThreadEnum type, CpcQueryWordDto dto,Page page, int puid){
        PageRequestThreadDto requestThreadDto = new PageRequestThreadDto();
        requestThreadDto.setDto(dto);
        requestThreadDto.setPage(page);
        requestThreadDto.setPuid(puid);
        requestThreadDto.setType(type);
        return requestThreadDto;
    }
}
