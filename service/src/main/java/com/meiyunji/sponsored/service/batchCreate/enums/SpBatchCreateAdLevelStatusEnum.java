package com.meiyunji.sponsored.service.batchCreate.enums;

/**
 * @author: sun<PERSON><PERSON>
 * @email: <EMAIL>
 * @date: 2023-11-17  11:17
 */

import lombok.Getter;

import java.util.Arrays;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * sp批量创建广告层级任务状态
 * 任务状态,0提交中,1成功,2失败可重试,3失败不可重试,4手动终止
 */
@Getter
public enum SpBatchCreateAdLevelStatusEnum {

    DOING((byte) 0, "提交中", false),
    SUCCESS((byte) 1, "成功", false),
    FAILURE_AND_RETRY((byte) 2, "失败可重试", true),
    FAILURE((byte) 3, "失败不可重试", true),
    STOP((byte) 4, "手动终止", false);

    private Byte code;

    private String desc;

    private boolean showErrMsg;

    SpBatchCreateAdLevelStatusEnum(Byte code, String desc, boolean showErrMsg) {
        this.code = code;
        this.desc = desc;
        this.showErrMsg = showErrMsg;
    }

    public static Set<Byte> statusSet = Arrays.stream(SpBatchCreateAdLevelStatusEnum.values()).map(SpBatchCreateAdLevelStatusEnum::getCode).collect(Collectors.toSet());

    public static SpBatchCreateAdLevelStatusEnum getSpBatchCreateAdLevelStatusEnumByCode(int code) {
        for(SpBatchCreateAdLevelStatusEnum en : SpBatchCreateAdLevelStatusEnum.values()) {
            if(en.code == code) {
                return en;
            }
        }
        return null;
    }


    public static SpBatchCreateTaskStatusEnum convertLevelStatus2TaskStatus(byte code) {

        if (!statusSet.contains(code)) {
            return null;
        }

        if (SpBatchCreateAdLevelStatusEnum.DOING.getCode() == code || SpBatchCreateAdLevelStatusEnum.FAILURE_AND_RETRY.getCode() == code) {
            return SpBatchCreateTaskStatusEnum.DOING;
        }

        if (SpBatchCreateAdLevelStatusEnum.FAILURE.getCode() == code) {
            return SpBatchCreateTaskStatusEnum.FINISH_FAILURE;
        }

        if (SpBatchCreateAdLevelStatusEnum.SUCCESS.getCode() == code || SpBatchCreateAdLevelStatusEnum.STOP.getCode() == code) {
            return SpBatchCreateTaskStatusEnum.FINISH_SUCCESS;
        }

        return null;
    }
}
