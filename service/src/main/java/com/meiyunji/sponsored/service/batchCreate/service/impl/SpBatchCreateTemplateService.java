package com.meiyunji.sponsored.service.batchCreate.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.meiyunji.sponsored.common.exception.ServiceException;
import com.meiyunji.sponsored.rpc.batchCreate.spBatchCreate.SpBatchCreateQueryTemplateRequest;
import com.meiyunji.sponsored.rpc.batchCreate.spBatchCreate.SpBatchCreateQueryTemplateVo;
import com.meiyunji.sponsored.rpc.batchCreate.spBatchCreate.SpBatchCreateSaveTemplateRequest;
import com.meiyunji.sponsored.service.batchCreate.dao.IAmazonAdBatchNameTemplateDao;
import com.meiyunji.sponsored.service.batchCreate.dto.template.MaxTemplateNoDto;
import com.meiyunji.sponsored.service.batchCreate.enums.TemplateOperationTypeEnum;
import com.meiyunji.sponsored.service.batchCreate.enums.TemplateTypeEnum;
import com.meiyunji.sponsored.service.batchCreate.po.AmazonAdBatchNameTemplate;
import com.meiyunji.sponsored.service.batchCreate.service.ISpBatchCreateTemplateService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * @author: sunlinfeng
 * @email: <EMAIL>
 * @date: 2023-11-16  09:37
 */

@Service
@Slf4j
public class SpBatchCreateTemplateService implements ISpBatchCreateTemplateService {

    @Autowired
    private IAmazonAdBatchNameTemplateDao amazonAdBatchNameTemplateDao;

    private static final Integer MAX_TEMPLATE_NAME_LENGTH = 30;

    private static final int DEFAULT_TEMPLATE_VAL = 1;

    @Override
    public AmazonAdBatchNameTemplate saveTemplate(SpBatchCreateSaveTemplateRequest request) {
        String errMsg = "";
        if (StringUtils.isNotEmpty(request.getTemplateName()) && request.getTemplateName().length() > MAX_TEMPLATE_NAME_LENGTH) {
            throw new ServiceException("模板名称长度不能超过30个字符");
        }
        if (TemplateOperationTypeEnum.EDIT_TYPE.getCode() == request.getType()) {
            //更新命名模板
            //先把存在的数据查询出来
            AmazonAdBatchNameTemplate template = amazonAdBatchNameTemplateDao.getById(request.getPuid(), request.getTemplateId());
            if (Objects.isNull(template)) {
                log.error("对应模板不存在，{}", JSONObject.toJSONString(request));
                throw new ServiceException("对应模板不存在");
            }
            if (StringUtils.isNotEmpty(request.getTemplateName()) && !request.getTemplateName().equals(template.getTemplateName())) {
                //修改了模板名称，需要校验名称是否已存在
                if (Objects.nonNull(amazonAdBatchNameTemplateDao.getByUidAndNameAndTempType(request.getPuid(),
                        request.getUid(), request.getTemplateName().trim(), request.getTemplateType()))) {
                    throw new ServiceException("模板名称已存在");
                }
            }
            Optional.of(request.getTemplateName()).filter(StringUtils::isNotEmpty).ifPresent(template::setTemplateName);
            Optional.of(request.getTemplateContent()).filter(StringUtils::isNotEmpty).ifPresent(template::setTemplateContent);
            int result = amazonAdBatchNameTemplateDao.updateByPuidAndTemplateId(request.getPuid(), template);
            if (result == 1) {
                return template;
            } else {
                throw new ServiceException("更新失败");
            }
        } else {
            if (StringUtils.isEmpty(request.getTemplateName())) {
                throw new ServiceException("模板名称不能为空");
            }
            AmazonAdBatchNameTemplate template = new AmazonAdBatchNameTemplate();
            template.setPuid(request.getPuid());
            template.setUid(request.getUid());
            template.setTemplateType((byte) request.getTemplateType());
            template.setTemplateContent(request.getTemplateContent());
            template.setIsDelete((byte) 1);
            template.setCreateTime(new Date());


            try {
                JSON.parseObject(template.getTemplateContent());
            } catch (Exception e) {
                errMsg = "模板内容不正确";
                log.error(errMsg, e);
                throw new ServiceException(errMsg);
            }

            if (StringUtils.isNotEmpty(request.getTemplateName())) {
                //修改了模板名称，需要校验名称是否已存在
                if (Objects.nonNull(amazonAdBatchNameTemplateDao.getByUidAndNameAndTempType(request.getPuid(),
                        request.getUid(), request.getTemplateName().trim(), request.getTemplateType()))) {
                    throw new ServiceException("模板名称已存在");
                }
            }

                //查询当前最大编号
                MaxTemplateNoDto maxTemplateNoDto = amazonAdBatchNameTemplateDao.getCountByPuidAndUid(template.getPuid(), template.getUid(), template.getTemplateType());

                if (maxTemplateNoDto.getTemplateCount() >= 100 && TemplateTypeEnum.CAMPAIGN.getCode() == request.getTemplateType()) {
                    errMsg = "广告活动命名模板数量已超过上限，请删除后再新增模板";
                    throw new ServiceException(errMsg);
                }

            if (maxTemplateNoDto.getTemplateCount() >= 100 && TemplateTypeEnum.GROUP.getCode() == request.getTemplateType()) {
                errMsg = "广告组命名模板数量已超过上限，请删除后再新增模板";
                throw new ServiceException(errMsg);
            }

                template.setTemplateName(request.getTemplateName().trim());
                template.setTemplateNo(1);//no 为旧逻辑自动为模板添加命名使用的字段，现逻辑添加了用户自定义命名后，no字段废弃，所有新逻辑将该字段设置默认值

                if ("default".equals(request.getDefaultType())) {
                    template.setDefaultTemp(DEFAULT_TEMPLATE_VAL);
                    //如果设置默认模板，需要校验一下默认模板是否已经存在
                    if (amazonAdBatchNameTemplateDao.getDefaultByUidAndTempType(request.getPuid(), request.getUid(), request.getTemplateType())> 1) {
                        throw new ServiceException("用户已经存在默认模板，无法重复创建默认模板");
                    }
                }
            try {
                //保存
                Long result =amazonAdBatchNameTemplateDao.save(request.getPuid(), template);
                if (result == 1L) {
                    return amazonAdBatchNameTemplateDao.getByUidAndNameAndTempType(request.getPuid(),
                            request.getUid(), request.getTemplateName().trim(), request.getTemplateType());
                }
            } catch (Exception e) {
                errMsg = "保存模板失败";
                log.error(errMsg, e);
            }
            throw new ServiceException(errMsg);
        }
    }

    @Override
    public List<SpBatchCreateQueryTemplateVo> quertTemplate(SpBatchCreateQueryTemplateRequest request) {

        List<SpBatchCreateQueryTemplateVo> list = Collections.emptyList();

        try {
            List<AmazonAdBatchNameTemplate> poList = amazonAdBatchNameTemplateDao.queryList(request.getPuid(), request.getUid(), (byte) request.getTemplateType());
            if (CollectionUtils.isEmpty(poList)) {
                return list;
            }

            list = new ArrayList<>(poList.size());
            for (AmazonAdBatchNameTemplate template : poList) {
                SpBatchCreateQueryTemplateVo.Builder builder = SpBatchCreateQueryTemplateVo.newBuilder();
                builder.setId(template.getId());
                builder.setTemplateType(template.getTemplateType());
                builder.setTemplateName(template.getTemplateName());
                builder.setTemplateContent(template.getTemplateContent());
                if (Objects.nonNull(template.getDefaultTemp()) && template.getDefaultTemp() == DEFAULT_TEMPLATE_VAL) {
                    builder.setDefaultType("default");
                    //如果是默认模板，放在list首位
                    list.add(0, builder.build());
                    continue;
                } else {
                    builder.setDefaultType("custom");
                }
                list.add(builder.build());
            }
        } catch (Exception e) {
            log.error("查询模板失败", e);
        }

        return list;
    }

    @Override
    public void deleteTemplate(Integer puid, Integer uid, List<Long> id) {
       if (CollectionUtils.isEmpty(id)) {
           return;
       }
       if (Objects.isNull(puid) || puid == 0|| Objects.isNull(uid) || uid == 0) {
           throw new ServiceException("puid或uid为空");
       }
       //批量删除
        int result = amazonAdBatchNameTemplateDao.deleteByIdList(puid, id);
       if (result <= 0) {
           throw new ServiceException("删除失败");
       }
    }
}
