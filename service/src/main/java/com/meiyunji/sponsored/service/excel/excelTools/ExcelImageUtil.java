package com.meiyunji.sponsored.service.excel.excelTools;

import com.alibaba.excel.util.IoUtils;
import com.meiyunji.sponsored.common.util.PropertiesUtil;
import com.meiyunji.sponsored.service.util.ImageUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Field;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLConnection;
import java.util.*;
import java.util.concurrent.*;

public class ExcelImageUtil {

    private static final Logger logger = LoggerFactory.getLogger(EasyExcelUtil.class);

    /**
     * 异步设置图片数据
     *
     * @param rows
     * @param clazz
     */
    public static void asyncSetImageData(List rows, Class clazz,List<String> excludeFieldsLocal) {
        if (clazz == null) {
            return;
        }
        // 获取需要异步下载的图片集合
        Set<String> imageUrls = getImageUrls(rows, clazz, excludeFieldsLocal);

        // 异步获取图片缓存map
        Map<String, byte[]> imageDataCacheMap = asyncDownloadImage(imageUrls);

        // 设置图片数据列
        setImageDataCell(rows, clazz, imageDataCacheMap,excludeFieldsLocal);

    }

    /**
     * 异步下载图片
     *
     * @param imageUrls 图片url：String，图片的网络地址
     * @return
     */
    public static Map<String, byte[]> asyncDownloadImage(Set<String> imageUrls) {
        // url-图片数组缓存
        Map<String, byte[]> imageMap = new ConcurrentHashMap<>();
        if (CollectionUtils.isEmpty(imageUrls)) {
            return imageMap;
        }
        logger.info("start asyncDownloadImage,imageUrls:{}", imageUrls.size());
        // 线程池核心线程数，默认3个
        int corePoolSize = 3;
        // 创建线程池
        ExecutorService pool = new ThreadPoolExecutor(corePoolSize, corePoolSize, 0, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>());
        try {
            List<Future> futures = new ArrayList<>();
            for (String imageUrl : imageUrls) {
                Future<?> future = pool.submit(new AsyncDownloadImageTask(imageUrl, imageMap));
                futures.add(future);
            }
            for (Future future : futures) {
                try {
                    future.get();
                } catch (Exception e) {
                    logger.error("asyncDownloadImage future.get error", e);
                }
            }
        } finally {
            pool.shutdown();
        }
        return imageMap;
    }

    /**
     * 根据缓存map设置图片数据
     *
     * @param rows              数据行
     * @param clazz             导出类
     * @param imageDataCacheMap 图片缓存map
     */
    public static void setImageDataCell(List rows, Class clazz, Map<String, byte[]> imageDataCacheMap,List<String> excludeFieldsLocal) {
        if (CollectionUtils.isEmpty(rows) || CollectionUtils.isEmpty(imageDataCacheMap)) {
            return;
        }
        logger.info("start setImageDataCell,clazz:{},rows:{},imageDataCacheMap:{}", clazz.getName(), rows.size(), imageDataCacheMap.size());
        // 收集自定义图片字段，找到对应的真实设置图片数据的字段
        Map<Field, Field> imageUrlDataMapping = getImageUrlDataMapping(clazz,excludeFieldsLocal);
        for (Object object : rows) {
            try {
                for (Map.Entry<Field, Field> entry : imageUrlDataMapping.entrySet()) {
                    Field imageUrlField = entry.getKey();
                    Field dataByteField = entry.getValue();
                    imageUrlField.setAccessible(true);
                    String url = (String) imageUrlField.get(object);
                    if (StringUtils.hasLength(url)) {
                        byte[] imageByte = imageDataCacheMap.get(url);
                        dataByteField.setAccessible(true);
                        dataByteField.set(object, imageByte);
                        dataByteField.setAccessible(false);
                    }
                    imageUrlField.setAccessible(false);
                }
            } catch (Exception e) {
                logger.error("setImageDataCell error, clazz:" + clazz.getName(), e);
            }
        }
    }

    /**
     * 手机class中可以被设置图片数据的字段映射
     * key:@ExcelImageUrl field ; value: ExcelImageUrl.dataByteField()
     *
     * @param clazz
     * @return
     */
    public static Map<Field, Field> getImageUrlDataMapping(Class clazz, List<String> excludeFieldsLocal) {
        logger.info("getImageUrlDataMapping,class:{}", clazz);
        Map<Field, Field> imageDataMapping = new HashMap<>();
        // 递归所有父类获取所有属性
        List<Field> fieldList = new ArrayList<>();
        EasyExcelUtil.getFields(clazz, fieldList);
        // 收集自定义图片字段，找到对应的真实设置图片数据的字段
        for (Field field : fieldList) {
            ExcelImageUrl excelImageUrl = field.getAnnotation(ExcelImageUrl.class);
            if (excelImageUrl != null) {
                try {
                    String dataByteSource = excelImageUrl.dataByteSourceField();
                    Field dataByteSourceField = clazz.getDeclaredField(dataByteSource);
                    if (!excludeFieldsLocal.contains(field.getName())) {
                        imageDataMapping.put(dataByteSourceField, field);
                    }
                } catch (Exception e) {
                    logger.error("getImageUrlDataMapping error,class:" + clazz.getName(), e);
                }
            }
        }
        return imageDataMapping;
    }

    /**
     * 获取所有需要下载的图片路径,带有@ExcelImageUrl注解
     *
     * @param rows
     * @param clazz
     * @return
     */
    public static Set<String> getImageUrls(List rows, Class clazz,List<String> excludeFieldsLocal) {
        Set<String> imageUrls = new HashSet<>();
        if (CollectionUtils.isEmpty(rows)) {
            return imageUrls;
        }
        logger.info("start getImageUrls,class{},rows:{}", clazz.getName(), rows.size());
        // 收集图片-图片数据对应关系
        Map<Field, Field> imageUrlDataMapping = getImageUrlDataMapping(clazz, excludeFieldsLocal);
        for (Object object : rows) {
            try {
                for (Map.Entry<Field, Field> entry : imageUrlDataMapping.entrySet()) {
                    Field imageUrlField = entry.getKey();
                    imageUrlField.setAccessible(true);
                    String url = (String) imageUrlField.get(object);
                    imageUrlField.setAccessible(false);
                    if (StringUtils.hasLength(url)) {
                        imageUrls.add(url);
                    }
                }
            } catch (Exception e) {
                logger.error("getImageUrls error ,class:" + clazz.getName(), e);
            }
        }
        return imageUrls;
    }

    /**
     * 获取跟图片字段绑定的字段
     * 解决弹窗不显示绑定字段，但是导出要导出绑定字段
     * 【图片原始链接】是【图片字段】的绑定字段
     *
     * @param clazz
     * @return
     */
    public static Map<String, String> getBindingFieldMap(Class clazz) {
        Map<String, String> bindingFieldMap = new HashMap<>();
        if (clazz == null) {
            return bindingFieldMap;
        }
        // 获取所有属性
        List<Field> fieldList = new ArrayList<>();
        EasyExcelUtil.getFields(clazz, fieldList);
        for (Field field : fieldList) {
            ExcelImageUrl excelImageUrl = field.getAnnotation(ExcelImageUrl.class);
            if (excelImageUrl != null) {
                String bindingPathField = excelImageUrl.bindingPathField();
                if (StringUtils.hasLength(bindingPathField)) {
                    bindingFieldMap.put(field.getName(), bindingPathField);
                }
            }
        }
        return bindingFieldMap;
    }

    /**
     * 获取图片字节数组
     *
     * @param url
     * @return
     * @throws IOException
     */
    public static byte[] getImageDataByte(URL url, int redirect) throws IOException {
        // 最大重试3次
        for (int i = 0, retry = 3; i < retry; i++) {
            logger.info("start getImageDataByte retry:" + i + ",url:" + url);
            InputStream inputStream = null;
            try {
                // 开启连接
                URLConnection uc = url.openConnection();
                uc.setRequestProperty("sec-ch-ua", "\"Not_A Brand\";v=\"99\", \"Google Chrome\";v=\"109\", \"Chromium\";v=\"109\"");
                uc.setRequestProperty("sec-ch-ua-mobile", "?0");
                uc.setRequestProperty("sec-ch-ua-platform", "\"Windows\"");
                uc.setRequestProperty("Sec-Fetch-Dest", "document");
                uc.setRequestProperty("Sec-Fetch-Mode", "navigate");
                uc.setRequestProperty("Sec-Fetch-Site", "none");
                uc.setRequestProperty("Sec-Fetch-User", "?1");
                uc.setRequestProperty("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/109.0.0.0 Safari/537.36");
                // 连接超时时间
                uc.setConnectTimeout(4000);
                // 读取超时时间
                uc.setReadTimeout(4000);
                // 获取响应状态
                int statusCode = ((HttpURLConnection) uc).getResponseCode();
                switch (statusCode) {
                    case 200:
                        inputStream = url.openStream();
                        break;
                    case 404:
                        inputStream = getNoImageStream();
                        break;
                    case 302:
                        HttpURLConnection urlConnection = (HttpURLConnection)url.openConnection();
                        String location = urlConnection.getHeaderField("Location");
                        urlConnection.disconnect();
                        if(redirect > 0 && !StringUtils.isEmpty(location) && !location.equals(url.toString())){
                            return getImageDataByteNoRedirect(new URL(ImageUtils.getCompressImageUrlByCOS(location)));
                        }
                        break;
                    default:
                        inputStream = getNoImageStream();
                        break;
                }
                if(inputStream == null){
                    inputStream = getNoImageStream();
                }
                return getCellData(inputStream);
            } catch (Exception e) {
                // 重试休眠
                try {
                    int sleepMs = 1000 * i * 2 + 1000;
                    Thread.sleep(sleepMs);
                    logger.error("getImageDataByte error, retry:" + i + ",url:" + url + ", sleep:" + sleepMs, e);
                } catch (InterruptedException ex) {
                    logger.error("getImageDataByte sleep error", ex);
                }
            } finally {
                // 每次关闭流
                if (inputStream != null) {
                    inputStream.close();
                }
            }
        }
        // 如果超过了最大重试次数，并且没有返回对应的图片流，就获取默认图片流返回
        return getCellData(getNoImageStream());
    }

    /**
     * 无重定向获取图片流
     * @param url
     * @return
     * @throws IOException
     */
    public static byte[] getImageDataByteNoRedirect(URL url) throws IOException {
        return getImageDataByte(url, 0);
    }

    /**
     * 获取图片字节数组
     *
     * @param imageUrl
     * @return
     * @throws IOException
     */
    public static byte[] getImageDataByte(String imageUrl) throws IOException {
        return getImageDataByte(new URL(ImageUtils.getCompressImageUrlByCOS(imageUrl)), 1);
    }

    /**
     * 无图图片流
     *
     * @return
     * @throws IOException
     */
    public static InputStream getNoImageStream() throws IOException {
        URL url = new URL("https://www.sellfox.com/static/images/back/kong.png");
        InputStream inputStream = url.openStream();
        return inputStream;
    }

    /**
     * 创建cellData
     *
     * @param inputStream
     * @return
     * @throws IOException
     */
    public static byte[] getCellData(InputStream inputStream) throws IOException {
        byte[] bytes = IoUtils.toByteArray(inputStream);
        return bytes;
    }

    /**
     * 异步下载图片任务
     */
    public static class AsyncDownloadImageTask implements Runnable {
        private Map<String, byte[]> imageMap;
        private String imageUrl;

        public AsyncDownloadImageTask(String imageUrl, Map<String, byte[]> imageMap) {
            this.imageUrl = imageUrl;
            this.imageMap = imageMap;
        }

        @Override
        public void run() {
            try {
                if (imageMap.get(imageUrl) == null) {
                    byte[] imageByte = getImageDataByte(imageUrl);
                    imageMap.put(imageUrl, imageByte);
                }
            } catch (Exception e) {
                logger.error("DownloadExportPicTask get image byte error", e);
            }
        }
    }

}
