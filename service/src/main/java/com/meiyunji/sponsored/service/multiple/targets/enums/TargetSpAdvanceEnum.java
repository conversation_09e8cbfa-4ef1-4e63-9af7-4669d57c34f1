package com.meiyunji.sponsored.service.multiple.targets.enums;

import cn.hutool.core.collection.CollectionUtil;
import lombok.Getter;

import java.util.HashSet;
import java.util.Set;

/**
 * 投放sp列表页-高级筛选字段枚举
 *
 * @author: zzh
 * @create: 2024-10-18 10:05
 */
@Getter
public enum TargetSpAdvanceEnum {
    BIDDING_MIN("biddingMin", " and IFNULL(t.bid, g.default_bid) >= ? ", new HashSet<>()),
    BIDDING_MAX("biddingMax", " and IFNULL(t.bid, g.default_bid) <= ? ", new HashSet<>()),
    SEARCH_FREQUENCY_RANK_MIN("searchFrequencyRankMin", " and search_frequency_rank >= ? ", new HashSet<>()),
    SEARCH_FREQUENCY_RANK_MAX("searchFrequencyRankMax", " and search_frequency_rank <= ?", new HashSet<>()),
    WEEK_RATIO_MIN("weekRatioMin", " and round(week_ratio*100,2) >= ? ", new HashSet<>()),
    WEEK_RATIO_MAX("weekRatioMax", " and round(week_ratio*100,2) <= ? ", new HashSet<>()),
    IMPRESSIONS_MIN("impressionsMin", " and impressionsDoris >= ? ", CollectionUtil.newHashSet("impressionsDoris")),
    IMPRESSIONS_MAX("impressionsMax", " and impressionsDoris <= ? ", CollectionUtil.newHashSet("impressionsDoris")),
    CLICKS_MIN("clicksMin", " and clicksDoris >= ? ", CollectionUtil.newHashSet("clicksDoris")),
    CLICKS_MAX("clicksMax", " and clicksDoris <= ? ", CollectionUtil.newHashSet("clicksDoris")),
    CLICK_RATE_MIN("clickRateMin", " and ROUND(ifnull(clicksDoris/impressionsDoris,0),4) >= ? ", CollectionUtil.newHashSet("clicksDoris", "impressionsDoris")),
    CLICK_RATE_MAX("clickRateMax", " and ROUND(ifnull(clicksDoris/impressionsDoris,0),4) <= ? ", CollectionUtil.newHashSet("clicksDoris", "impressionsDoris")),
    COST_MIN("costMin", " and costDoris >= ? ", CollectionUtil.newHashSet("costDoris")),
    COST_MAX("costMax", " and costDoris <= ? ", CollectionUtil.newHashSet("costDoris")),
    CPA_MIN("cpaMin", " and ROUND(ifnull(costDoris/orderNumDoris, 0), 4) >= ? ", CollectionUtil.newHashSet("costDoris", "orderNumDoris")),
    CPA_MAX("cpaMax", " and ROUND(ifnull(costDoris/orderNumDoris, 0), 4) <= ? ", CollectionUtil.newHashSet("costDoris", "orderNumDoris")),
    CPC_MIN("cpcMin", " and ROUND(ifnull(costDoris/clicksDoris,0),2) >= ? ", CollectionUtil.newHashSet("costDoris", "clicksDoris")),
    CPC_MAX("cpcMax", " and ROUND(ifnull(costDoris/clicksDoris,0),2) <= ? ", CollectionUtil.newHashSet("costDoris", "clicksDoris")),
    ORDER_NUM_MIN("orderNumMin", " and orderNumDoris >= ? ", CollectionUtil.newHashSet("orderNumDoris")),
    ORDER_NUM_MAX("orderNumMax", " and orderNumDoris <= ? ", CollectionUtil.newHashSet("orderNumDoris")),
    AD_SALE_NUM_MIN("adSaleNumMin", " and ifnull(adOrderNumDoris, 0) >= ? ", CollectionUtil.newHashSet("adOrderNumDoris")),
    AD_SALE_NUM_MAX("adSaleNumMax", " and ifnull(adOrderNumDoris, 0) <= ? ", CollectionUtil.newHashSet("adOrderNumDoris")),
    AD_OTHER_ORDER_NUM_MIN("adOtherOrderNumMin", " and ifnull(orderNumDoris - adOrderNumDoris, 0) >= ? ", CollectionUtil.newHashSet("orderNumDoris", "adOrderNumDoris")),
    AD_OTHER_ORDER_NUM_MAX("adOtherOrderNumMax", " and ifnull(orderNumDoris - adOrderNumDoris, 0) <= ? ", CollectionUtil.newHashSet("orderNumDoris", "adOrderNumDoris")),
    SALES_MIN("salesMin", " and totalSalesDoris >= ? ", CollectionUtil.newHashSet("totalSalesDoris")),
    SALES_MAX("salesMax", " and totalSalesDoris <= ? ", CollectionUtil.newHashSet("totalSalesDoris")),
    AD_SALES_MIN("adSalesMin", " and ifnull(adSalesDoris, 0) >= ? ", CollectionUtil.newHashSet("adSalesDoris")),
    AD_SALES_MAX("adSalesMax", " and ifnull(adSalesDoris, 0) <= ? ", CollectionUtil.newHashSet("adSalesDoris")),
    AD_OTHER_SALES_MIN("adOtherSalesMin", " and ifnull(totalSalesDoris - adSalesDoris, 0) >= ?", CollectionUtil.newHashSet("totalSalesDoris", "adSalesDoris")),
    AD_OTHER_SALES_MAX("adOtherSalesMax", " and ifnull(totalSalesDoris - adSalesDoris, 0) <= ?", CollectionUtil.newHashSet("totalSalesDoris", "adSalesDoris")),
    ACOS_MIN("acosMin", " and ROUND(ifnull(costDoris/totalSalesDoris,0),4) >= ? ", CollectionUtil.newHashSet("costDoris", "totalSalesDoris")),
    ACOS_MAX("acosMax", " and ROUND(ifnull(costDoris/totalSalesDoris,0),4) <= ? ", CollectionUtil.newHashSet("costDoris", "totalSalesDoris")),
    ROAS_MIN("roasMin", " and ROUND(ifnull(totalSalesDoris/costDoris,0),4) >= ? ", CollectionUtil.newHashSet("totalSalesDoris", "costDoris")),
    ROAS_MAX("roasMax", " and ROUND(ifnull(totalSalesDoris/costDoris,0),4) <= ? ", CollectionUtil.newHashSet("totalSalesDoris", "costDoris")),
    SALES_CONVERSION_RATE_MIN("salesConversionRateMin", " and ROUND(ifnull(orderNumDoris/clicksDoris,0),4) >= ? ", CollectionUtil.newHashSet("orderNumDoris", "clicksDoris")),
    SALES_CONVERSION_RATE_MAX("salesConversionRateMax", " and ROUND(ifnull(orderNumDoris/clicksDoris,0),4) <= ? ", CollectionUtil.newHashSet("orderNumDoris", "clicksDoris")),
    ACOTS_MIN("acotsMin", " and ROUND(ifnull(costDoris/shopSalesDoris,0),4) >= ? ", CollectionUtil.newHashSet("costDoris")),
    ACOTS_MAX("acotsMax", " and ROUND(ifnull(costDoris/shopSalesDoris,0),4) <= ? ", CollectionUtil.newHashSet("costDoris")),
    ASOTS_MIN("asotsMin", " and ROUND(ifnull(totalSalesDoris/shopSalesDoris,0),4) >= ? ", CollectionUtil.newHashSet("totalSalesDoris")),
    ASOTS_MAX("asotsMax", " and ROUND(ifnull(totalSalesDoris/shopSalesDoris,0),4) <= ? ", CollectionUtil.newHashSet("totalSalesDoris")),
    AD_SALES_TOTAL_MIN("adSalesTotalMin", " and saleNumDoris >= ? ", CollectionUtil.newHashSet("saleNumDoris")),
    AD_SALES_TOTAL_MAX("adSalesTotalMax", " and saleNumDoris <= ? ", CollectionUtil.newHashSet("saleNumDoris")),
    AD_SELF_SALE_NUM_MIN("adSelfSaleNumMin", " and ifnull(adSaleNumDoris, 0) >= ? ", CollectionUtil.newHashSet("adSaleNumDoris")),
    AD_SELF_SALE_NUM_MAX("adSelfSaleNumMax", " and ifnull(adSaleNumDoris, 0) <= ? ", CollectionUtil.newHashSet("adSaleNumDoris")),
    AD_OTHER_SALE_NUM_MIN("adOtherSaleNumMin", " and ifnull(saleNumDoris - adSaleNumDoris, 0) >= ? ", CollectionUtil.newHashSet("saleNumDoris","adSaleNumDoris")),
    AD_OTHER_SALE_NUM_Max("adOtherSaleNumMax", " and ifnull(saleNumDoris - adSaleNumDoris, 0) <= ? ", CollectionUtil.newHashSet("saleNumDoris","adSaleNumDoris")),
    ADVERTISING_UNIT_PRICE_MIN("advertisingUnitPriceMin", " and ROUND(ifnull(totalSalesDoris/orderNumDoris, 0), 2) >= ? ", CollectionUtil.newHashSet("totalSalesDoris", "orderNumDoris")),
    ADVERTISING_UNIT_PRICE_MAX("advertisingUnitPriceMax", " and ROUND(ifnull(totalSalesDoris/orderNumDoris, 0), 2) <= ? ", CollectionUtil.newHashSet("totalSalesDoris", "orderNumDoris")),
    TOP_IMPRESSION_SHARE_MIN("topImpressionShareMin", " and maxTopIsDoris >= ? ", CollectionUtil.newHashSet("maxTopIsDoris")),
    TOP_IMPRESSION_SHARE_MAX("topImpressionShareMax", " and minTopIsDoris <= ? ", CollectionUtil.newHashSet("minTopIsDoris")),
    ;

    // 高级筛选字段
    private final String code;
    // having字段
    private final String havingBy;
    // 涉及字段
    private final Set<String> columnList;

    TargetSpAdvanceEnum(String code, String havingBy, Set<String> columnList) {
        this.code = code;
        this.havingBy = havingBy;
        this.columnList = columnList;
    }

    /**
     * 根据code获取统计字段集合
     */
    public static Set<String> getSetByCode(String code) {
        for (TargetSpAdvanceEnum advanceEnum : TargetSpAdvanceEnum.values()) {
            if (advanceEnum.getCode().equals(code)) {
                return advanceEnum.getColumnList();
            }
        }
        return new HashSet<>();
    }

    /**
     * 根据code获取高级筛选
     */
    public static String getHavingByByCode(String code , String targetType) {
        if(!"keyword".equals(targetType) && ("searchFrequencyRank".equals(code) || "weekRatio".equals(code))) {
            return null;
        }
        for (TargetSpAdvanceEnum havingByEnum : TargetSpAdvanceEnum.values()) {
            if (havingByEnum.getCode().equals(code)) {
                return havingByEnum.getHavingBy();
            }
        }
        return null;
    }
}
