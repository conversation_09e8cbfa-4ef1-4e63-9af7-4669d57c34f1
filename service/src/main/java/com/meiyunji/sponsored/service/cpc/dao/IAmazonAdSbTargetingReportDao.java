package com.meiyunji.sponsored.service.cpc.dao;

import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.springjdbc.IBaseShardingDao;
import com.meiyunji.sponsored.service.cpc.bo.AdOrderBo;
import com.meiyunji.sponsored.service.cpc.dto.AdReportData;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdSbTargetingReport;
import com.meiyunji.sponsored.service.cpc.vo.*;
import com.meiyunji.sponsored.service.cpc.vo.chartVo.AdHomePerformancedto;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.dto.TargetFirstPlaceIsDto;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Created by DXM_0081 on 2021/5/14.
 */
public interface IAmazonAdSbTargetingReportDao extends IBaseShardingDao<AmazonAdSbTargetingReport> {

    /**
     * 批量插入、更新
     * @param puid
     * @param list
     */
    void insertOrUpdateList(Integer puid, List<AmazonAdSbTargetingReport> list);

    Page getPageList(Integer puid, SearchVo search, Page page);

    Page detailPageList(Integer puid, Integer shopId, String marketplaceId, ReportParam param , Page page);

    List<AmazonAdSbTargetingReport> getChartList(Integer puid, Integer shopId, String marketplaceId, String startStr, String endStr, String targetId);

    AmazonAdSbTargetingReport getSumReportByTargetId(Integer puid, Integer shopId, String marketplaceId, String startStr, String endStr, String targetId);

    List<Map<String, Object>> getAdCampaignMap(int puid, Integer shopId, String marketplaceId);

    List<AmazonAdSbTargetingReport> listSumReports(Integer puid, Integer shopId, String startDate, String endDate, List<String> targetIds);

    List<AmazonAdSbTargetingReport> listReports(Integer puid, Integer shopId, String startDate, String endDate, String targetId);

    List<AdHomePerformancedto> listSumReportByTargetIds(Integer puid, Integer shopId, String startStr, String endStr, TargetingPageParam param, List<String> targetIds);

    List<AdHomePerformancedto> getSbReportByDate(Integer puid, Integer shopId, String startStr, String endStr, TargetingPageParam param);

    List<AdHomePerformancedto> getSbReportByTargetIdList(Integer puid, Integer shopId, String startStr, String endStr, List<String> targetIdList, boolean isGroupBy);

    AdMetricDto getSumAdMetric(Integer puid, Integer shopId, String startStr, String endStr, TargetingPageParam param);



    /**
     *
     * @param puid
     * @param shopId
     * @param date  更新数据前的时间
     * @return
     */
    List<String> getTargetListByUpdateTime(Integer puid, Integer shopId, Date date);

    List<AmazonAdSbTargetingReport> getReportByTargetId(Integer puid, Integer shopId, String start, String end, String marketplaceId, String targetId);

    List<AmazonAdSbTargetingReport> getReportByTargetIdsGroupByCountDate(int puid, Integer shopId, String startDate, String endDate, String marketplaceId, List<String> targetIds);

    List<AmazonAdSbTargetingReport> getFirstPlaceIsByTargetId(int puid, int shopId, String marketId, String startDate, String endDate, String targetId);

    int deleteByPuidAndShopId(Integer puid, Integer shopId, Integer limit);

    List<SbTargetPageVo> getReportDataByTargetIdList(Integer puid, TargetingPageParam param, List<String> targetIdList);

    List<AdOrderBo> getTargetIdAndIndexList(Integer puid, TargetingPageParam param);

    List<String> getTargetIdListByParam(Integer puid, TargetingPageParam param);

    AdMetricDto getTargetPageSumMetricDataByTargetIdList(Integer puid, TargetingPageParam param, List<String> targetIdList);

    List<String> getTargetIdsByTargetIds(int puid, int shopId, String start, List<String> targetIds);

    List<AdReportData> getAllReportByTargetIdsAndDate(Integer puid, Integer shopId, String startStr, String endStr, List<String> targetIdList, List<String> adGroupIds);

    List<TargetFirstPlaceIsDto> getFirstPlaceIsDtoByKeywordId(int puid, List<Integer> shopIdList, String startDate, String endDate, List<String> targetIdList);
}
