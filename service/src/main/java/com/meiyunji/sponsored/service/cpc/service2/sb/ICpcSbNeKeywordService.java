package com.meiyunji.sponsored.service.cpc.service2.sb;

import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.rpc.sb.neKeyword.NeKeywordResponse;
import com.meiyunji.sponsored.service.cpc.po.AmazonSbAdNeKeyword;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdProfile;
import com.meiyunji.sponsored.service.cpc.vo.*;
import com.meiyunji.sponsored.service.negative.request.NegativeArchiveRequest;

import java.util.List;

/**
 * Created by lm on 2021/8/5.
 */
public interface ICpcSbNeKeywordService {


    /**
     * 否定关键词-列表页
     * @param param :
     * @return :
     */
    Result<Page<NeKeywordsPageVo>> pageList(SbNeKeywordsPageParam param);

    /**
     * 添加否定关键词
     *
     * @param addNeKeywordsVo@return
     */
    Result<List<NeKeywordResponse.Data>> createNeKeywords(AddSbNeKeywordsVo addNeKeywordsVo);
    NewCreateResultResultVo<SBCommonErrorVo> createNeKeywords(AddSbNeKeywordsVo addNeKeywordsVo, ShopAuth shop, AmazonAdProfile profile);

    Result createCampaignIdsNeKeywords(AddSbCampaignIdsNeKeywordsVo addNeKeywordsVo);

    /**
     * 否定关键词-归档
     *
     * @param puid
     * @param shopId
     * @param uid
     * @param id ：
     * @return ：
     */
    Result archive(Integer puid,Integer shopId,Integer uid, Long id, String ip);

    /**
     * 否定关键词-批量归档
     * @param puid
     * @param shopId
     * @param uid
     * @param idList
     * @return
     */
    Result batchArchive(Integer puid, Integer shopId, Integer uid, List<Long> idList, String ip);

    void saveDoris(List<AmazonSbAdNeKeyword> keywordList);

    void saveDoris(Integer puid, Integer shopId, List<String> keywordIdList);

    List<AmazonSbAdNeKeyword> listByKeywordText(Integer puid, Integer shopId, List<NegativeArchiveRequest.NegativeInfo> infoList);
}
