package com.meiyunji.sponsored.service.strategy.dao.impl;

import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.springjdbc.BaseShardingDaoImpl;
import com.meiyunji.sponsored.common.springjdbc.ConditionBuilder;
import com.meiyunji.sponsored.common.util.SqlStringUtil;
import com.meiyunji.sponsored.service.strategy.dao.AdvertiseStrategyTopBudgetScheduleDao;
import com.meiyunji.sponsored.service.strategy.po.AdvertiseStrategyTopBudgetSchedule;
import com.meiyunji.sponsored.service.util.LocalDateTimeUtil;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Repository
public class AdvertiseStrategyTopBudgetScheduleDaoImpl extends BaseShardingDaoImpl<AdvertiseStrategyTopBudgetSchedule> implements AdvertiseStrategyTopBudgetScheduleDao {
    @Override
    public AdvertiseStrategyTopBudgetSchedule selectByPrimaryKey(int puid, Long id) {
        return getByPuidAndId(puid, id);
    }

    @Override
    public int batchInsert(int puid, List<AdvertiseStrategyTopBudgetSchedule> list) {
        StringBuilder sql = new StringBuilder("insert into t_advertise_strategy_top_budget_schedule " +
                "    (id, puid, shop_id,profile_id ,marketplace_id, `type`, task_id, `day`, " +
                "      `start`, `end`, new_value, origin_value,`reduction_value`,create_at, last_update_at) " +
                "    values ");
        List<Object> argsList = Lists.newArrayList();
        for (AdvertiseStrategyTopBudgetSchedule schedule : list) {
            sql.append("(?, ?, ? , ?, ?, ?, ?,?,?,?,?,?,?, now(), now()),");
            argsList.add(schedule.getId());
            argsList.add(puid);
            argsList.add(schedule.getShopId());
            argsList.add(schedule.getProfileId());
            argsList.add(schedule.getMarketplaceId());
            argsList.add(schedule.getType());
            argsList.add(schedule.getTaskId());
            argsList.add(schedule.getDay());
            argsList.add(schedule.getStart());
            argsList.add(schedule.getEnd());
            argsList.add(schedule.getNewValue());
            argsList.add(schedule.getOriginValue());
            argsList.add(schedule.getReductionValue());
        }
        sql.deleteCharAt(sql.length() - 1);
        return getJdbcTemplate(puid).update(sql.toString(), argsList.toArray());
    }

    @Override
    public void updateReductionValue(int puid, Long taskId,String reductionValue){
        String sql = "update t_advertise_strategy_top_budget_schedule set reduction_value = ? , last_update_at = now() where puid = ? and task_id = ?";
        List<Object> argsList = Lists.newArrayList();
        argsList.add(reductionValue);
        argsList.add(puid);
        argsList.add(taskId);

        getJdbcTemplate(puid).update(sql,argsList.toArray());
    }

    @Override
    public int deleteStrategySchedule(int puid, int shopId, Long taskId) {
        String sql = "delete from t_advertise_strategy_top_budget_schedule where puid = ? and task_id = ?";
        List<Object> argsList = Lists.newArrayList();
        argsList.add(puid);
        argsList.add(taskId);
        return getJdbcTemplate(puid).update(sql,argsList.toArray());
    }

    @Override
    public int updateStrategySchedule(int puid, List<AdvertiseStrategyTopBudgetSchedule> list) {
        StringBuilder sql = new StringBuilder("update t_advertise_strategy_top_budget_schedule " +
                " set type = ?,day = ?,start = ?,end = ?,origin_value = ?," +
                " new_value = ? where puid = ? and task_id = ?");
        List<Object> argsList = Lists.newArrayList();
        for (AdvertiseStrategyTopBudgetSchedule schedule : list) {
            argsList.add(schedule.getType());
            argsList.add(schedule.getDay());
            argsList.add(schedule.getStart());
            argsList.add(schedule.getEnd());
            argsList.add(schedule.getOriginValue());
            argsList.add(schedule.getNewValue());
            argsList.add(puid);
            argsList.add(schedule.getTaskId());
        }
        sql.deleteCharAt(sql.length() - 1);
        return getJdbcTemplate(puid).update(sql.toString(), argsList.toArray());
    }

    @Override
    public List<AdvertiseStrategyTopBudgetSchedule> listByTaskId(int puid, int shopId, Long taskId) {
        return listByCondition(puid, new ConditionBuilder.Builder()
                .equalTo("puid",puid)
                .equalTo("shop_id", shopId)
                .equalTo("task_id",taskId).build());
    }

    @Override
    public List<AdvertiseStrategyTopBudgetSchedule> listByTaskIds(int puid, List<Long> taskIds) {
        return listByCondition(puid, new ConditionBuilder.Builder()
                .equalTo("puid",puid)
                .in("task_id", taskIds.toArray()).build());
    }

    @Override
    public Page<AdvertiseStrategyTopBudgetSchedule> pageByTaskId(int puid, int shopId, String marketplaceId , String type, Long taskId, int pageNo, int pageSize) {
        StringBuilder sql = new StringBuilder(" SELECT * FROM t_advertise_strategy_top_budget_schedule ");
        StringBuilder countSql = new StringBuilder(" SELECT count(id) FROM t_advertise_strategy_top_budget_schedule");
        List<Object> args = Lists.newArrayList();
        StringBuilder whereSql = new StringBuilder(" where puid = ? and task_id = ?");
        args.add(puid);
        args.add(taskId);
        //查询当前时间转站点时间
        LocalDateTime localDateTime = LocalDateTimeUtil.convertChinaToSiteTime(LocalDateTime.now(),marketplaceId);
        Integer week = localDateTime.getDayOfWeek().getValue();
        Integer hours = localDateTime.getHour() * 60;
        if (type.equals("WEEKLY")) {
            whereSql.append(" and ? <= day ");
            whereSql.append(" and IF ( day= ? , `end` > ? , 1=1) ");
            args.add(week);
            args.add(week);
            args.add(hours);
        } else {
            whereSql.append(" and `end` > ?");
            args.add(hours);
        }
        sql.append(whereSql);
        countSql.append(whereSql);
        return this.getPageResult(puid, pageNo, pageSize, countSql.toString(), args.toArray(),
                sql.toString(), args.toArray(), AdvertiseStrategyTopBudgetSchedule.class);
    }

    @Override
    public Integer queryExecutePreview(int puid, List<Long> taskIds, String type,String marketplaceId) {
        StringBuilder countSql = new StringBuilder(" SELECT count(e.id) FROM t_advertise_strategy_top_budget_schedule e" +
                " left join t_advertise_strategy_top_budget_template s on (e.puid = s.puid and e.shop_id = s.shop_id and e.marketplace_id = s.marketplace_id " +
                " and e.task_id = s.task_id) ");
        List<Object> args = Lists.newArrayList();
        StringBuilder whereSql = new StringBuilder(" where e.puid = ? and s.status = 'ENABLED' ");
        args.add(puid);
        whereSql.append(SqlStringUtil.dealInList("e.task_id", taskIds, args));
        //查询当前时间转站点时间
        LocalDateTime localDateTime = LocalDateTimeUtil.convertChinaToSiteTime(LocalDateTime.now(),marketplaceId);
        Integer week = localDateTime.getDayOfWeek().getValue();
        Integer hours = localDateTime.getHour() * 60;
        if (type.equals("WEEKLY")) {
            whereSql.append(" and ? <= day ");
            whereSql.append(" and IF ( day= ? , `end` > ? , 1=1) ");
            args.add(week);
            args.add(week);
            args.add(hours);
        } else {
            whereSql.append(" and `end` > ?");
            args.add(hours);
        }
        countSql.append(whereSql);
        return getJdbcTemplate(puid).queryForObject(countSql.toString(),Integer.class,args.toArray());
    }

    @Override
    public int deleteByPuidAndShopId(Integer puid, Integer shopId, Integer limit) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sql = new StringBuilder("delete from ").append(this.getJdbcHelper().getTable()).append(" where puid = ? ");
        argsList.add(puid);
        sql.append("and shop_id = ?");
        argsList.add(shopId);
        if (limit != null) {
            sql.append(" limit ? ");
            argsList.add(limit);
        }
        return getJdbcTemplate(puid).update(sql.toString(), argsList.toArray());
    }
}
