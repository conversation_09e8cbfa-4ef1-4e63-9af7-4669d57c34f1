package com.meiyunji.sponsored.service.newDashboard.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.amazon.advertising.sd.constant.TacticEnum;
import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.common.util.ParamCopyUtil;
import com.meiyunji.sponsored.rpc.newDashboard.DashboardAdTargetingMatrixData;
import com.meiyunji.sponsored.rpc.newDashboard.DashboardAdTargetingMatrixRequest;
import com.meiyunji.sponsored.rpc.newDashboard.DashboardAdTargetingMatrixVo;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.batchCreate.dto.targeting.TargetingExpressionDTO;
import com.meiyunji.sponsored.service.batchCreate.enums.KeywordAndTargetingExcludeNeEnum;
import com.meiyunji.sponsored.service.cpc.bo.AmazonAdCampaignAllBo;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdCampaignAllDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdGroupDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonSbAdGroupDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonSdAdGroupDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdGroup;
import com.meiyunji.sponsored.service.cpc.po.AmazonSbAdGroup;
import com.meiyunji.sponsored.service.cpc.po.AmazonSdAdGroup;
import com.meiyunji.sponsored.service.cpc.po.TargetTypeEnum;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.doris.dao.*;
import com.meiyunji.sponsored.service.doris.po.*;
import com.meiyunji.sponsored.service.enums.SpKeywordGroupValueEnum;
import com.meiyunji.sponsored.service.log.enums.MatchTypeEnum;
import com.meiyunji.sponsored.service.newDashboard.dto.DashboardAdQueryWordMatrixTopDto;
import com.meiyunji.sponsored.service.newDashboard.dto.DashboardAdTargetingMatrixDto;
import com.meiyunji.sponsored.service.newDashboard.dto.DashboardAdTargetingMatrixTopDto;
import com.meiyunji.sponsored.service.newDashboard.enums.DashboardDataFieldEnum;
import com.meiyunji.sponsored.service.newDashboard.enums.DashboardOrderByEnum;
import com.meiyunji.sponsored.service.newDashboard.enums.DashboardThemeKeywordTextEnum;
import com.meiyunji.sponsored.service.newDashboard.enums.ThemeKeywordTextEnum;
import com.meiyunji.sponsored.service.newDashboard.service.IDashboardAdTargetingMatrixService;
import com.meiyunji.sponsored.service.newDashboard.util.CalculateAdDataUtil;
import com.meiyunji.sponsored.service.newDashboard.util.CalculateUtil;
import com.meiyunji.sponsored.service.newDashboard.util.TargetingInfoUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: ys
 * @date: 2024/4/23 14:30
 * @describe:
 */
@Service
@Slf4j
public class DashboardAdTargetingMatrixServiceImpl implements IDashboardAdTargetingMatrixService {

    @Autowired
    private IOdsAmazonAdKeywordReportDao odsAmazonAdKeywordReportDao;

    @Autowired
    private IOdsCpcTargetingReportDao odsCpcTargetingReportDao;

    @Autowired
    private IOdsAmazonAdSbKeywordReportDao odsAmazonAdSbKeywordReportDao;

    @Autowired
    private IOdsAmazonAdSbTargetingReportDao odsAmazonAdSbTargetingReportDao;

    @Autowired
    private IOdsAmazonAdSdTargetingReportDao odsAmazonAdSdTargetingReportDao;

    @Autowired
    private IOdsAmazonAdKeywordDao odsAmazonAdKeywordDao;

    @Autowired
    private IOdsAmazonAdTargetingDao odsAmazonAdTargetingDao;

    @Autowired
    private IOdsAmazonAdKeywordSbDao odsAmazonAdKeywordSbDao;

    @Autowired
    private IOdsAmazonAdTargetingSbDao odsAmazonAdTargetingSbDao;

    @Autowired
    private IOdsAmazonAdTargetingSdDao odsAmazonAdTargetingSdDao;

    @Autowired
    private IScVcShopAuthDao shopAuthDao;

    @Autowired
    private IAmazonAdCampaignAllDao amazonAdCampaignAllDao;

    @Autowired
    private IAmazonAdGroupDao amazonAdGroupDao;

    @Autowired
    private IAmazonSbAdGroupDao amazonSbAdGroupDao;

    @Autowired
    private IAmazonSdAdGroupDao amazonSdAdGroupDao;

    @Override
    public DashboardAdTargetingMatrixVo getKeywordAndTargetingMatrix(DashboardAdTargetingMatrixRequest req) {
        //包含关键词投放，和商品投放，其中又分为自动投放和手动投放
        //数据源为:sp的关键词投放，商品投放 t_amazon_ad_keyword_report, t_cpc_query_targeting_report
        //sb:关键词投放，商品投放 t_amazon_ad_sb_keyword_report, t_amazon_ad_sb_targeting_report
        //sd：商品投放 t_amazon_ad_sd_targeting_report
        List<DashboardAdTargetingMatrixDto> matrixResult = getMatrixList(req);
        //先填充计算属性
        matrixResult.forEach(CalculateAdDataUtil::calAdCalDataReflex);
        List<DashboardAdTargetingMatrixData> data = matrixResult.stream().map(m -> {
            DashboardAdTargetingMatrixData.Builder builder = DashboardAdTargetingMatrixData.newBuilder();
            BeanUtils.copyProperties(m, builder, ParamCopyUtil.checkPropertiesNullOrEmptySuper(m));
            Optional.ofNullable(m.getShopId()).ifPresent(builder::setShopId);
            Optional.ofNullable(m.getCost()).map(CalculateUtil::formatDecimal).ifPresent(builder::setCost);
            Optional.ofNullable(m.getTotalSales()).map(CalculateUtil::formatDecimal).ifPresent(builder::setTotalSales);
            Optional.ofNullable(m.getImpressions()).map(String::valueOf).ifPresent(builder::setImpressions);
            Optional.ofNullable(m.getClicks()).map(String::valueOf).ifPresent(builder::setClicks);
            Optional.ofNullable(m.getOrderNum()).map(String::valueOf).ifPresent(builder::setOrderNum);
            Optional.ofNullable(m.getSaleNum()).map(String::valueOf).ifPresent(builder::setSaleNum);
            Optional.ofNullable(m.getAcos()).map(CalculateUtil::formatPercent).ifPresent(builder::setAcos);
            Optional.ofNullable(m.getRoas()).map(String::valueOf).ifPresent(builder::setRoas);
            Optional.ofNullable(m.getClickRate()).map(CalculateUtil::formatPercent).ifPresent(builder::setClickRate);
            Optional.ofNullable(m.getConversionRate()).map(CalculateUtil::formatPercent).ifPresent(builder::setConversionRate);
            Optional.ofNullable(m.getCpc()).map(CalculateUtil::formatDecimal).ifPresent(builder::setCpc);
            Optional.ofNullable(m.getCpa()).map(CalculateUtil::formatDecimal).ifPresent(builder::setCpa);
            return builder.build();
        }).collect(Collectors.toList());
        DashboardAdTargetingMatrixVo.Builder result = DashboardAdTargetingMatrixVo.newBuilder();
        result.addAllRows(data);
        return result.build();
    }

    private List<DashboardAdTargetingMatrixDto> getMatrixList(DashboardAdTargetingMatrixRequest req) {
        List<DashboardAdTargetingMatrixDto> dtoList = Lists.newArrayList();
        int puid = req.getPuid();
        DashboardDataFieldEnum dataField = DashboardDataFieldEnum.fieldMap.get(req.getDataField());
        int limit = req.getLimit();
        DashboardOrderByEnum orderBy = DashboardOrderByEnum.orderByMap.get(Optional.of(req.getOrderBy()).map(String::toLowerCase).orElse("desc"));
        List<DashboardAdTargetingMatrixTopDto> matrixList = new ArrayList<>();
        StopWatch sw = new StopWatch("target matrix query start");
        sw.start("query sp sb sd report from ods");
        Optional.ofNullable(getSpMatrixInfoList(req)).filter(CollectionUtils::isNotEmpty).ifPresent(matrixList::addAll);
        Optional.ofNullable(getSbMatrixInfoList(req)).filter(CollectionUtils::isNotEmpty).ifPresent(matrixList::addAll);
        Optional.ofNullable(getSdMatrixInfoList(req)).filter(CollectionUtils::isNotEmpty).ifPresent(matrixList::addAll);
        sw.stop();

        //需要对结果集进行排序，取前n，相当于内存排序
        //还需要对结果进行排序，取前limit行数据
        sw.start("sort sp,sb,sd result");
        matrixList = keywordAndTargetingComparator(matrixList, dataField, limit, orderBy);
        if (CollectionUtils.isEmpty(matrixList)) {
            return dtoList;
        }
        sw.stop();
        Map<String, DashboardAdTargetingMatrixTopDto> spKeywordMap = new HashMap<>();
        Map<String, DashboardAdTargetingMatrixTopDto> sbKeywordMap = new HashMap<>();
        Map<String, DashboardAdTargetingMatrixTopDto> spTargetingMap = new HashMap<>();
        Map<String, DashboardAdTargetingMatrixTopDto> sbTargetingMap = new HashMap<>();
        Map<String, DashboardAdTargetingMatrixTopDto> sdTargetingMap = new HashMap<>();
        for (DashboardAdTargetingMatrixTopDto dto : matrixList) {
            if (StringUtils.isNotBlank(dto.getKeywordId())) {
                if (Constants.SP.equals(dto.getType())) {
                    spKeywordMap.put(dto.getKeywordId(), dto);
                }
                if (Constants.SB.equals(dto.getType())) {
                    sbKeywordMap.put(dto.getKeywordId(), dto);
                }
            }
            if (StringUtils.isNotBlank(dto.getTargetingId())) {
                if (Constants.SP.equals(dto.getType())) {
                    spTargetingMap.put(dto.getTargetingId(), dto);
                }
                if (Constants.SB.equals(dto.getType())) {
                    sbTargetingMap.put(dto.getTargetingId(), dto);
                }
                if (Constants.SD.equals(dto.getType())) {
                    sdTargetingMap.put(dto.getTargetingId(), dto);
                }
            }
        }

        //拼装数据进行返回
        //查询sp数据
        sw.start("query sp targeting,sb targeting, sd targeting ");
        //拼装数据进行返回
        //查询sp数据
        Map<String, OdsAmazonAdKeyword> spKeywordInfoMap = new HashMap<>();
        Map<String, OdsAmazonAdTargeting> spTargetingInfoMap = new HashMap<>();
        Set<Integer> allShopIdSet = new HashSet<>();
        if (CollectionUtils.isNotEmpty(spKeywordMap.keySet())) {
            List<OdsAmazonAdKeyword> keywordInfoList = odsAmazonAdKeywordDao.getByKeywordIdsAndShopIds(puid, req.getShopIdList(),
                    new ArrayList<>(spKeywordMap.keySet()));
            spKeywordInfoMap = keywordInfoList.parallelStream().collect(Collectors.toMap(OdsAmazonAdKeyword::getKeywordId, v1 -> v1, (old, current) -> current));
            Optional.of(keywordInfoList.parallelStream()
                            .map(OdsAmazonAdKeyword::getShopId).collect(Collectors.toList()))
                    .filter(CollectionUtils::isNotEmpty).ifPresent(allShopIdSet::addAll);
        }
        if (CollectionUtils.isNotEmpty(spTargetingMap.keySet())) {
            List<OdsAmazonAdTargeting> targetingInfoList = odsAmazonAdTargetingDao.getByTargetingIds(puid, req.getShopIdList(),
                    new ArrayList<>(spTargetingMap.keySet()));
            spTargetingInfoMap = targetingInfoList.parallelStream().collect(Collectors.toMap(OdsAmazonAdTargeting::getTargetId, v1 -> v1, (old, current) -> current));
            Optional.of(targetingInfoList.parallelStream()
                            .map(OdsAmazonAdTargeting::getShopId).collect(Collectors.toList()))
                    .filter(CollectionUtils::isNotEmpty).ifPresent(allShopIdSet::addAll);
        }

        //查询sb数据
        Map<String, OdsAmazonAdKeywordSb> sbKeywordInfoMap = new HashMap<>();
        Map<String, OdsAmazonAdTargetingSb> sbTargetingInfoMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(sbKeywordMap.keySet())) {
            List<OdsAmazonAdKeywordSb> sbKeywordInfoList = odsAmazonAdKeywordSbDao.getByKeywordIds(puid, req.getShopIdList(),
                    new ArrayList<>(sbKeywordMap.keySet()));
            sbKeywordInfoMap = sbKeywordInfoList.parallelStream().collect(Collectors.toMap(OdsAmazonAdKeywordSb::getKeywordId, v1 -> v1, (old, current) -> current));
            Optional.of(sbKeywordInfoList.parallelStream()
                            .map(OdsAmazonAdKeywordSb::getShopId).collect(Collectors.toList()))
                    .filter(CollectionUtils::isNotEmpty).ifPresent(allShopIdSet::addAll);
        }
        if (CollectionUtils.isNotEmpty(sbTargetingMap.keySet())) {
            List<OdsAmazonAdTargetingSb> sbTargetingInfoList = odsAmazonAdTargetingSbDao.getByTargetingIds(puid, req.getShopIdList(),
                    new ArrayList<>(sbTargetingMap.keySet()));
            sbTargetingInfoMap = sbTargetingInfoList.parallelStream().collect(Collectors.toMap(OdsAmazonAdTargetingSb::getTargetId, v1 -> v1, (old, current) -> current));
            Optional.of(sbTargetingInfoList.parallelStream()
                            .map(OdsAmazonAdTargetingSb::getShopId).collect(Collectors.toList()))
                    .filter(CollectionUtils::isNotEmpty).ifPresent(allShopIdSet::addAll);
        }

        //查询sd数据
        Map<String, OdsAmazonAdTargetingSd> sdTargetingInfoMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(sdTargetingMap.keySet())) {
            List<OdsAmazonAdTargetingSd> sdTargetingInfoList = odsAmazonAdTargetingSdDao.getByTargetingIds(puid, req.getShopIdList(),
                    new ArrayList<>(sdTargetingMap.keySet()));
            sdTargetingInfoMap = sdTargetingInfoList.parallelStream().collect(Collectors.toMap(OdsAmazonAdTargetingSd::getTargetId, v1 -> v1, (old, current) -> current));
            Optional.of(sdTargetingInfoList.parallelStream()
                            .map(OdsAmazonAdTargetingSd::getShopId).collect(Collectors.toList()))
                    .filter(CollectionUtils::isNotEmpty).ifPresent(allShopIdSet::addAll);
        }
        sw.stop();

        for (DashboardAdTargetingMatrixTopDto dto : matrixList) {
            if (StringUtils.isNotBlank(dto.getKeywordId())) {
                if (Constants.SP.equals(dto.getType()) && Objects.nonNull(spKeywordInfoMap.get(dto.getKeywordId()))) {
                    fillKeywordInfo(dto, spKeywordInfoMap.get(dto.getKeywordId()));
                }
                if (Constants.SB.equals(dto.getType()) && Objects.nonNull(sbKeywordInfoMap.get(dto.getKeywordId()))) {
                    fillSbKeywordInfo(dto, sbKeywordInfoMap.get(dto.getKeywordId()));
                }

            }
            if (StringUtils.isNotBlank(dto.getTargetingId())) {
                if (Constants.SP.equals(dto.getType()) && Objects.nonNull(spTargetingInfoMap.get(dto.getTargetingId()))) {
                    fillTargetingInfo(dto, spTargetingInfoMap.get(dto.getTargetingId()));
                }
                if (Constants.SB.equals(dto.getType()) && Objects.nonNull(sbTargetingInfoMap.get(dto.getTargetingId()))) {
                    fillSbTargetingInfo(dto, sbTargetingInfoMap.get(dto.getTargetingId()));
                }
                if (Constants.SD.equals(dto.getType()) && Objects.nonNull(sdTargetingInfoMap.get(dto.getTargetingId()))) {
                    fillSdTargetingInfo(dto, sdTargetingInfoMap.get(dto.getTargetingId()));
                }
            }
        }

        //还需要按给类型进行反查广告活动名称和广告组名称
        Set<String> allSpAdGroupIdSet = matrixList.parallelStream().filter(m -> Constants.SP.equals(m.getType())).map(DashboardAdTargetingMatrixTopDto::getAdGroupId).collect(Collectors.toSet());
        Set<String> allSbAdGroupIdSet = matrixList.parallelStream().filter(m -> Constants.SB.equals(m.getType())).map(DashboardAdTargetingMatrixTopDto::getAdGroupId).collect(Collectors.toSet());
        Set<String> allSdAdGroupIdSet = matrixList.parallelStream().filter(m -> Constants.SD.equals(m.getType())).map(DashboardAdTargetingMatrixTopDto::getAdGroupId).collect(Collectors.toSet());
        sw.start("query campaign and group and shop info");
        List<AmazonAdCampaignAllBo> campaignNameList = new ArrayList<>();
        List<AmazonAdGroup> spGroupList = new ArrayList<>();
        List<AmazonSbAdGroup> sbGroupList = new ArrayList<>();
        List<AmazonSdAdGroup> sdGroupList = new ArrayList<>();
        List<ShopAuth> shopAuthList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(allSpAdGroupIdSet)) {
            spGroupList = amazonAdGroupDao.listByGroupIdsAndShopIdList(puid, new ArrayList<>(allShopIdSet), new ArrayList<>(allSpAdGroupIdSet));
        }
        if (CollectionUtils.isNotEmpty(allSbAdGroupIdSet)) {
            sbGroupList = amazonSbAdGroupDao.getGroupByShopIdsAndGroupIds(puid, new ArrayList<>(allShopIdSet), new ArrayList<>(allSbAdGroupIdSet));
        }
        if (CollectionUtils.isNotEmpty(allSdAdGroupIdSet)) {
            sdGroupList = amazonSdAdGroupDao.getGroupByShopIdsAndGroupIds(puid, new ArrayList<>(allShopIdSet), new ArrayList<>(allSdAdGroupIdSet));
        }
        if (CollectionUtils.isNotEmpty(allShopIdSet)) {
            shopAuthList = shopAuthDao.listAllByIds(puid, new ArrayList<>(allShopIdSet));
        }
        Map<String, AmazonAdGroup> allspGroupMap = spGroupList.parallelStream().collect(Collectors.toMap(AmazonAdGroup::getAdGroupId, v1 -> v1, (oldVal, newVal) -> newVal));
        Map<String, AmazonSbAdGroup> allsbGroupMap = sbGroupList.parallelStream().collect(Collectors.toMap(AmazonSbAdGroup::getAdGroupId, v1 -> v1, (oldVal, newVal) -> newVal));
        Map<String, AmazonSdAdGroup> allsdGroupMap = sdGroupList.parallelStream().collect(Collectors.toMap(AmazonSdAdGroup::getAdGroupId, v1 -> v1, (oldVal, newVal) -> newVal));

        // SD投放表无法查到广告活动id,需要通过SD广告组进行反填
        List<String> sdCampaignIds = sdGroupList.stream().map(AmazonSdAdGroup::getCampaignId).distinct().collect(Collectors.toList());
        Set<String> allCampaignIdSet = matrixList.parallelStream().map(DashboardAdTargetingMatrixTopDto::getCampaignId).collect(Collectors.toSet());
        allCampaignIdSet.addAll(sdCampaignIds);
        if (CollectionUtils.isNotEmpty(allCampaignIdSet)) {
            campaignNameList = amazonAdCampaignAllDao.listByShopIdListAndCampaignIdList(puid, allShopIdSet, allCampaignIdSet);

        }
        Map<String, AmazonAdCampaignAllBo> allCampaignInfoMap = campaignNameList.parallelStream().collect(Collectors.toMap(AmazonAdCampaignAllBo::getCampaignId, v1 -> v1, (oldVal, newVal) -> newVal));

        Map<Integer, ShopAuth> shopInfoMap = shopAuthList.parallelStream().collect(Collectors.toMap(ShopAuth::getId, v1 -> v1, (oldVal, newVal) -> newVal));
        sw.stop();
        //组装数据
        for (DashboardAdTargetingMatrixTopDto dto : matrixList) {
            dtoList.add(convertDto(dto, spTargetingInfoMap,
                    sbTargetingInfoMap, sdTargetingInfoMap,
                    shopInfoMap, allCampaignInfoMap,
                    allspGroupMap, allsbGroupMap,
                    allsdGroupMap));
        }
        log.info(sw.prettyPrint());
        return dtoList;
    }

    private DashboardAdTargetingMatrixDto convertDto(DashboardAdTargetingMatrixTopDto dto, Map<String, OdsAmazonAdTargeting> spTargetingInfoMap,
                                                     Map<String, OdsAmazonAdTargetingSb> sbTargetingInfoMap, Map<String, OdsAmazonAdTargetingSd> sdTargetingInfoMap,
                                                     Map<Integer, ShopAuth> shopInfoMap, Map<String, AmazonAdCampaignAllBo> allCampaignNameMap,
                                                     Map<String, AmazonAdGroup> allSpGroupInfoMap, Map<String, AmazonSbAdGroup> allsbGroupInfoMap,
                                                     Map<String, AmazonSdAdGroup> allsdGroupInfoMap) {
        DashboardAdTargetingMatrixDto result = new DashboardAdTargetingMatrixDto();
        BeanUtils.copyProperties(dto, result, ParamCopyUtil.checkPropertiesNullOrEmptySuper(dto));
        Optional.ofNullable(dto.getCost()).ifPresent(result::setCost);
        Optional.ofNullable(dto.getTotalSales()).ifPresent(result::setTotalSales);
        Optional.ofNullable(dto.getImpressions()).ifPresent(result::setImpressions);
        Optional.ofNullable(dto.getClicks()).ifPresent(result::setClicks);
        Optional.ofNullable(dto.getOrderNum()).ifPresent(result::setOrderNum);
        Optional.ofNullable(dto.getSaleNum()).ifPresent(result::setSaleNum);
        Optional.ofNullable(dto.getShopId()).ifPresent(result::setShopId);

        if (StringUtils.isNotBlank(dto.getKeywordId())) {
            String matchType = Optional.ofNullable(dto.getMatchType()).map(String::toUpperCase).orElse("");
            KeywordAndTargetingExcludeNeEnum en = KeywordAndTargetingExcludeNeEnum.getKeywordAndTargetingExcludeNeEnumByCode(matchType);
            result.setTargetId(dto.getKeywordId());
            result.setMatchType(Optional.ofNullable(en).map(KeywordAndTargetingExcludeNeEnum::getMsg).orElse(""));
            result.setTargetText(dto.getKeywordText());
            result.setExpressionType("manual");
        }
        if (StringUtils.isNotBlank(dto.getTargetingId())) {
            String expression = "";
            String type = "";
            if (Constants.SP.equals(dto.getType())) {
                OdsAmazonAdTargeting spTargetingInfo = spTargetingInfoMap.get(dto.getTargetingId());
                if (Objects.nonNull(spTargetingInfo)) {
                    expression = spTargetingInfo.getExpression();
                    if ("asin".equalsIgnoreCase(spTargetingInfo.getType())) {
                        type = "";//设置为空，不采用type进行判断匹配类型，从expression中解析
                    } else {
                        type = spTargetingInfo.getType();
                    }
                    result.setTargetText(spTargetingInfo.getTargetingValue());
                    result.setExpressionType(spTargetingInfo.getExpressionType());
                    TargetingInfoUtil.getExpressionInfo(spTargetingInfo.getType(), spTargetingInfo.getResolvedExpression(), result);
                }
            }
            if (Constants.SB.equals(dto.getType())) {
                OdsAmazonAdTargetingSb sbTargetingInfo = sbTargetingInfoMap.get(dto.getTargetingId());
                if (Objects.nonNull(sbTargetingInfo)) {
                    expression = sbTargetingInfo.getExpression();
                    type = sbTargetingInfo.getType();
                    result.setTargetText(sbTargetingInfo.getTargetText());
                }
                result.setExpressionType("manual");
            }
            if (Constants.SD.equals(dto.getType())) {
                OdsAmazonAdTargetingSd sdTargetingInfo = sdTargetingInfoMap.get(dto.getTargetingId());
                AmazonSdAdGroup sdGroupInfo = allsdGroupInfoMap.get(dto.getAdGroupId());
                if (Objects.nonNull(sdGroupInfo)) {
                    if (TacticEnum.T00020.name().equals(sdGroupInfo.getTactic())) {//商品投放
                        if ("similarProduct".equalsIgnoreCase(sdTargetingInfo.getType())) {
                            type = "category";
                        } else {
                            type = sdTargetingInfo.getType();
                        }
                    }
                    if (TacticEnum.T00030.name().equals(sdGroupInfo.getTactic())) {//受众投放
                        type = sdTargetingInfo.getTargetType();
                    }
                    expression = sdTargetingInfo.getTargetType();
                    result.setTargetText(sdTargetingInfo.getTargetText());
                }
                result.setExpressionType("manual");
            }
            result.setTargetId(dto.getTargetingId());
            if (Constants.SD.equals(dto.getType())) {
                result.setMatchType(expression);
            } else {
                result.setMatchType(keywordAndTargetingTypeHandler(type, expression));
            }

        }
        if (Constants.SP.equals(dto.getType())) {
            Optional.ofNullable(allSpGroupInfoMap.get(dto.getAdGroupId())).map(AmazonAdGroup::getName).ifPresent(result::setAdGroupName);
            Optional.ofNullable(allCampaignNameMap.get(dto.getCampaignId())).map(AmazonAdCampaignAllBo::getName).ifPresent(result::setCampaignName);
            if (MatchTypeEnum.theme.getMatchType().equalsIgnoreCase(dto.getMatchType())) {
                SpKeywordGroupValueEnum themeKeywordTextEnum = SpKeywordGroupValueEnum.getKeywordGroupValueEnumByText(dto.getKeywordText());
                if (themeKeywordTextEnum != null) {
                    result.setTargetText(themeKeywordTextEnum.getTextCn());
                }
            }
        }
        if (Constants.SB.equals(dto.getType())) {
            Optional.ofNullable(allsbGroupInfoMap.get(dto.getAdGroupId())).map(AmazonSbAdGroup::getName).ifPresent(result::setAdGroupName);
            Optional.ofNullable(allCampaignNameMap.get(dto.getCampaignId())).map(AmazonAdCampaignAllBo::getName).ifPresent(result::setCampaignName);
            if (MatchTypeEnum.theme.getMatchType().equalsIgnoreCase(dto.getMatchType())) {
                ThemeKeywordTextEnum themeKeywordTextEnum = ThemeKeywordTextEnum.getEnumByCode(dto.getKeywordText());
                if (themeKeywordTextEnum != null) {
                    result.setTargetText(themeKeywordTextEnum.getTextCn());
                }
            }
        }
        if (Constants.SD.equals(dto.getType())) {
            Optional.ofNullable(allsdGroupInfoMap.get(dto.getAdGroupId())).map(AmazonSdAdGroup::getName).ifPresent(result::setAdGroupName);
            // 补充sd的活动id和活动名称
            AmazonAdCampaignAllBo amazonAdCampaignAllBo = Optional.ofNullable(allsdGroupInfoMap.get(dto.getAdGroupId())).map(data -> allCampaignNameMap.get(data.getCampaignId())).orElse(null);
            if (amazonAdCampaignAllBo != null) {
                result.setCampaignId(amazonAdCampaignAllBo.getCampaignId());
                result.setCampaignName(amazonAdCampaignAllBo.getName());
            }
        }
        result.setType(StringUtils.upperCase(dto.getType()));
        Optional.ofNullable(shopInfoMap.get(dto.getShopId())).map(ShopAuth::getName).ifPresent(result::setShopName);
        return result;
    }

    private void fillKeywordInfo(DashboardAdTargetingMatrixTopDto dto, OdsAmazonAdKeyword info) {
        Optional.ofNullable(info.getKeywordText()).ifPresent(dto::setKeywordText);
        Optional.ofNullable(info.getShopId()).ifPresent(dto::setShopId);
        Optional.ofNullable(info.getMarketplaceId()).ifPresent(dto::setMarketplaceId);
        Optional.ofNullable(info.getCampaignId()).ifPresent(dto::setCampaignId);
        Optional.ofNullable(info.getAdGroupId()).ifPresent(dto::setAdGroupId);
        Optional.ofNullable(info.getMatchType()).ifPresent(dto::setMatchType);
    }

    private void fillTargetingInfo(DashboardAdTargetingMatrixTopDto dto, OdsAmazonAdTargeting info) {
        Optional.ofNullable(info.getShopId()).ifPresent(dto::setShopId);
        Optional.ofNullable(info.getMarketplaceId()).ifPresent(dto::setMarketplaceId);
        Optional.ofNullable(info.getCampaignId()).ifPresent(dto::setCampaignId);
        Optional.ofNullable(info.getAdGroupId()).ifPresent(dto::setAdGroupId);
    }

    private void fillSbKeywordInfo(DashboardAdTargetingMatrixTopDto dto, OdsAmazonAdKeywordSb info) {
        String keywordText;
        if (KeywordAndTargetingExcludeNeEnum.THEME.getCode().equalsIgnoreCase(info.getMatchType())) {
            keywordText = Optional.ofNullable(info.getKeywordText())
                    .map(DashboardThemeKeywordTextEnum.enumMap::get)
                    .map(DashboardThemeKeywordTextEnum::getTextCn)
                    .orElse(info.getKeywordText());
        } else {
            keywordText = info.getKeywordText();
        }
        Optional.ofNullable(keywordText).ifPresent(dto::setKeywordText);
        Optional.ofNullable(info.getShopId()).ifPresent(dto::setShopId);
        Optional.ofNullable(info.getMarketplaceId()).ifPresent(dto::setMarketplaceId);
        Optional.ofNullable(info.getCampaignId()).ifPresent(dto::setCampaignId);
        Optional.ofNullable(info.getAdGroupId()).ifPresent(dto::setAdGroupId);
        Optional.ofNullable(info.getMatchType()).ifPresent(dto::setMatchType);
    }

    private void fillSbTargetingInfo(DashboardAdTargetingMatrixTopDto dto, OdsAmazonAdTargetingSb info) {
        Optional.ofNullable(info.getShopId()).ifPresent(dto::setShopId);
        Optional.ofNullable(info.getMarketplaceId()).ifPresent(dto::setMarketplaceId);
        Optional.ofNullable(info.getCampaignId()).ifPresent(dto::setCampaignId);
        Optional.ofNullable(info.getAdGroupId()).ifPresent(dto::setAdGroupId);
    }

    private void fillSdTargetingInfo(DashboardAdTargetingMatrixTopDto dto, OdsAmazonAdTargetingSd info) {
        Optional.ofNullable(info.getShopId()).ifPresent(dto::setShopId);
        Optional.ofNullable(info.getMarketplaceId()).ifPresent(dto::setMarketplaceId);
        Optional.ofNullable(info.getAdGroupId()).ifPresent(dto::setAdGroupId);
    }

    private List<DashboardAdTargetingMatrixTopDto> keywordAndTargetingComparator(List<DashboardAdTargetingMatrixTopDto> matrixList, DashboardDataFieldEnum dataField,
                                                                                 int limit, DashboardOrderByEnum orderBy) {
        Class<DashboardAdTargetingMatrixTopDto> cla = DashboardAdTargetingMatrixTopDto.class;
        Field[] fields = getAllField(cla);
        Map<String, Field> fieldMap = Arrays.stream(fields).peek(f -> f.setAccessible(true))
                .filter(e-> !e.isSynthetic()).collect(Collectors.toMap(Field::getName, v1 -> v1));
        StringBuilder compareKey = new StringBuilder(dataField.getCode());
        matrixList = matrixList.stream().sorted((o1, o2) -> {
            try {
                int result = 0;
                Field compareField = fieldMap.get(compareKey.toString());
                if (Objects.nonNull(compareField)) {
                    Object val1 = compareField.get(o1);
                    Object val2 = compareField.get(o2);
                    if (Objects.isNull(val1) && Objects.isNull(val2)) {
                        return 0;
                    }
                    if (Objects.nonNull(val1) && Objects.isNull(val2)) {
                        return 1;
                    }
                    if (Objects.isNull(val1)) {
                        return -1;
                    }
                    if (String.class.isAssignableFrom(compareField.getType()) && StringUtils.isNotEmpty(val1.toString())
                            && StringUtils.isNotEmpty(val2.toString())) {
                        String rateValue1 = val1.toString();
                        String rateValue2 = val2.toString();
                        result = new BigDecimal(rateValue1).compareTo(new BigDecimal(rateValue2));
                    }
                    if (Integer.class.isAssignableFrom(compareField.getType())) {
                        Integer compareVal1 = (Integer) val1;
                        Integer compareVal2 = (Integer) val2;
                        result = compareVal1.compareTo(compareVal2);
                    }
                    if (Long.class.isAssignableFrom(compareField.getType())) {
                        Long compareVal1 = (Long) val1;
                        Long compareVal2 = (Long) val2;
                        result = compareVal1.compareTo(compareVal2);
                    }
                    if (BigDecimal.class.isAssignableFrom(compareField.getType())) {
                        BigDecimal compareVal1 = (BigDecimal) val1;
                        BigDecimal compareVal2 = (BigDecimal) val2;
                        result = compareVal1.compareTo(compareVal2);
                    }
                }
//                if (result == 0) {
//                    if (Objects.nonNull(o1.getKeywordId()) && Objects.nonNull(o2.getKeywordId()) )
//                        return o1.getKeywordId().compareTo(o2.getKeywordId());
//                    if (Objects.nonNull(o1.getTargetingId()) && Objects.nonNull(o2.getTargetingId()) )
//                        return o1.getTargetingId().compareTo(o2.getTargetingId());
//                }else{
                return result;
//                }
            } catch (IllegalAccessException e) {
                log.error("compare keyword and targeting list data error", e);
            }
            return 0;
        }).collect(Collectors.toList());
        int subLimit = Math.min(limit, matrixList.size());

        //按请求字段进行升降序
        if (DashboardOrderByEnum.DESC == orderBy) {
            Collections.reverse(matrixList);
        }
        matrixList = matrixList.subList(0, subLimit);
        return matrixList;
    }

    private String keywordAndTargetingTypeHandler(String manualType, String matchType) {
        if (Objects.nonNull(TargetTypeEnum.getTargetTypeEnumMap().get(manualType))) {
            return manualType;
        } else {
            if (Objects.nonNull(KeywordAndTargetingExcludeNeEnum.getKeywordAndTargetingExcludeNeEnumByCode(matchType))) {
                return KeywordAndTargetingExcludeNeEnum.getKeywordAndTargetingExcludeNeEnumByCode(matchType).getMsg();
            }
            try {
                List<TargetingExpressionDTO> dtoList = JSONObject.parseArray(matchType, TargetingExpressionDTO.class);
                return Optional.ofNullable(dtoList)
                        .filter(CollectionUtils::isNotEmpty)
                        .map(l -> l.get(0))
                        .map(TargetingExpressionDTO::getType)
                        .map(KeywordAndTargetingExcludeNeEnum::getKeywordAndTargetingExcludeNeEnumByCode)
                        .map(KeywordAndTargetingExcludeNeEnum::getMsg).orElse("");
            } catch (Exception e) {
                log.error("format error ", e);
            }
        }
        return "";
    }

    /**
     * 获取sp的keyword和targeting投放的矩阵排行数据
     *
     * @param req
     * @return
     */
    private List<DashboardAdTargetingMatrixTopDto> getSpMatrixInfoList(DashboardAdTargetingMatrixRequest req) {
        List<String> portfolioIds = req.getPortfolioIdsList().stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
        List<String> campaignIds = req.getCampaignIdsList().stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
        List<String> siteToday = null;
        if (Boolean.TRUE.equals(req.getSiteToday())) {
            siteToday = CalculateAdDataUtil.getSiteToday(req.getMarketplaceIdList());
        }
        int puid = req.getPuid();
        List<Integer> shopList = req.getShopIdList();
        List<String> marketplaceIdList = req.getMarketplaceIdList();
        String currency = req.getCurrency();
        String startDate = Optional.of(req.getStartDate()).map(DateUtil::toFormatDate).orElse("");
        String endDate = Optional.of(req.getEndDate()).map(DateUtil::toFormatDate).orElse("");
        DashboardDataFieldEnum dataField = DashboardDataFieldEnum.fieldMap.get(req.getDataField());
        Integer limit = req.getLimit();
        DashboardOrderByEnum orderBy = DashboardOrderByEnum.orderByMap.get(req.getOrderBy());
        //分别查询keyword和targeting的前n排名数据
        List<DashboardAdTargetingMatrixTopDto> keywordTopList = odsAmazonAdKeywordReportDao.getKeywordTopList(puid, marketplaceIdList, shopList, currency, startDate, endDate,
                dataField, null, limit, orderBy, siteToday, req.getSiteToday(), portfolioIds, campaignIds, req.getNoZero());
        //第一步从表取出来的只是排序字段的值，以及前n的排序投放关键词
        //第二步需要根据这些keywordId获取其对应的其他汇总值
        List<String> keywordIdList = keywordTopList.parallelStream().map(DashboardAdTargetingMatrixTopDto::getKeywordId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(keywordIdList)) {
            List<DashboardAdTargetingMatrixTopDto> keywordInfoList = odsAmazonAdKeywordReportDao.getKeywordTopInfoList(puid, marketplaceIdList, shopList, currency, startDate, endDate,
                    dataField, keywordIdList, orderBy, siteToday, req.getSiteToday(), null, null, null);
            fillAllDate4List(keywordTopList, keywordInfoList);
        }
        List<DashboardAdTargetingMatrixTopDto> targetingList = odsCpcTargetingReportDao.getTargetingTopList(puid, marketplaceIdList, shopList, currency, startDate, endDate,
                dataField, null, limit, orderBy, siteToday, req.getSiteToday(), portfolioIds, campaignIds, req.getNoZero());
        List<String> targetingIdList = targetingList.parallelStream().map(DashboardAdTargetingMatrixTopDto::getTargetingId).collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(targetingIdList)) {
            List<DashboardAdTargetingMatrixTopDto> targetingInfoList = odsCpcTargetingReportDao.getTargetingInfoList(puid, marketplaceIdList, shopList, currency, startDate, endDate,
                    dataField, targetingIdList, orderBy, siteToday, req.getSiteToday(), null, null, null);
            fillAllDate4List(targetingList, targetingInfoList);
        }
        return Optional.ofNullable(keywordTopList).map(l -> {
            l.addAll(targetingList);
            return l;
        }).orElse(targetingList);
    }

    private void fillAllDate4List(List<DashboardAdTargetingMatrixTopDto> firstList, List<DashboardAdTargetingMatrixTopDto> secondList) {
        if (CollectionUtils.isEmpty(secondList) || CollectionUtils.isEmpty(secondList)) {
            return;
        }
        Map<String, DashboardAdTargetingMatrixTopDto> secondKeywordMap = secondList.stream().filter(s -> StringUtils.isNotEmpty(s.getKeywordId()))
                .collect(Collectors.toMap(DashboardAdTargetingMatrixTopDto::getKeywordId, v1 -> v1, (old, current) -> current));
        Map<String, DashboardAdTargetingMatrixTopDto> secondTargetingMap = secondList.stream().filter(s -> StringUtils.isNotEmpty(s.getTargetingId()))
                .collect(Collectors.toMap(DashboardAdTargetingMatrixTopDto::getTargetingId, v1 -> v1, (old, current) -> current));
        for (DashboardAdTargetingMatrixTopDto dto : firstList) {
            if (Objects.nonNull(dto) && Objects.nonNull(secondKeywordMap.get(dto.getKeywordId()))) {
                //set values
                BeanUtils.copyProperties(secondKeywordMap.get(dto.getKeywordId()), dto,
                        ParamCopyUtil.checkPropertiesNullOrEmptySuper(secondKeywordMap.get(dto.getKeywordId())));
            }
            if (Objects.nonNull(dto) && Objects.nonNull(secondTargetingMap.get(dto.getTargetingId()))) {
                //set values
                BeanUtils.copyProperties(secondTargetingMap.get(dto.getTargetingId()), dto,
                        ParamCopyUtil.checkPropertiesNullOrEmptySuper(secondTargetingMap.get(dto.getTargetingId())));
            }
        }
    }

    private List<DashboardAdTargetingMatrixTopDto> getSbMatrixInfoList(DashboardAdTargetingMatrixRequest req) {
        List<String> portfolioIds = req.getPortfolioIdsList().stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
        List<String> campaignIds = req.getCampaignIdsList().stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
        List<String> siteToday = null;
        if (Boolean.TRUE.equals(req.getSiteToday())) {
            siteToday = CalculateAdDataUtil.getSiteToday(req.getMarketplaceIdList());
        }
        int puid = req.getPuid();
        List<Integer> shopList = req.getShopIdList();
        List<String> marketplaceIdList = req.getMarketplaceIdList();
        String currency = req.getCurrency();
        String startDate = Optional.of(req.getStartDate()).map(DateUtil::toFormatDate).orElse("");
        String endDate = Optional.of(req.getEndDate()).map(DateUtil::toFormatDate).orElse("");
        DashboardDataFieldEnum dataField = DashboardDataFieldEnum.fieldMap.get(req.getDataField());
        Integer limit = req.getLimit();
        DashboardOrderByEnum orderBy = DashboardOrderByEnum.orderByMap.get(req.getOrderBy());
        //分别查询keyword和targeting的前n排名数据
        List<DashboardAdTargetingMatrixTopDto> keywordTopList = odsAmazonAdSbKeywordReportDao.getKeywordTopList(puid, marketplaceIdList, shopList, currency, startDate, endDate,
                dataField, null, limit, orderBy, siteToday, req.getSiteToday(), portfolioIds, campaignIds, req.getNoZero());
        if (CollectionUtils.isNotEmpty(keywordTopList)) {
            List<String> sbKeywordIdList = keywordTopList.stream().map(DashboardAdTargetingMatrixTopDto::getKeywordId).collect(Collectors.toList());
            List<DashboardAdTargetingMatrixTopDto> sbKeywordInfoList = odsAmazonAdSbKeywordReportDao.getKeywordTopInfoList(puid, marketplaceIdList, shopList, currency, startDate, endDate,
                    dataField, sbKeywordIdList, orderBy, siteToday, req.getSiteToday(), null, null, null);
            fillAllDate4List(keywordTopList, sbKeywordInfoList);
        }
        List<DashboardAdTargetingMatrixTopDto> targetingList = odsAmazonAdSbTargetingReportDao.getTargetingTopList(puid, marketplaceIdList, shopList, currency, startDate, endDate,
                dataField, null, limit, orderBy, siteToday, req.getSiteToday(), portfolioIds, campaignIds, req.getNoZero());
        if (CollectionUtils.isNotEmpty(targetingList)) {
            List<String> sbTargetingIdList = targetingList.parallelStream().map(DashboardAdTargetingMatrixTopDto::getTargetingId).collect(Collectors.toList());
            List<DashboardAdTargetingMatrixTopDto> sbTargetingInfoList = odsAmazonAdSbTargetingReportDao.getTargetingTopInfoList(puid, marketplaceIdList, shopList, currency, startDate, endDate,
                    dataField, sbTargetingIdList, orderBy, siteToday, req.getSiteToday(), null, null, null);
            fillAllDate4List(targetingList, sbTargetingInfoList);
        }
        return Optional.ofNullable(keywordTopList).map(l -> {
            l.addAll(targetingList);
            return l;
        }).orElse(targetingList);
    }

    private List<DashboardAdTargetingMatrixTopDto> getSdMatrixInfoList(DashboardAdTargetingMatrixRequest req) {
        List<String> portfolioIds = req.getPortfolioIdsList().stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
        List<String> campaignIds = req.getCampaignIdsList().stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
        List<String> siteToday = null;
        if (Boolean.TRUE.equals(req.getSiteToday())) {
            siteToday = CalculateAdDataUtil.getSiteToday(req.getMarketplaceIdList());
        }
        int puid = req.getPuid();
        List<Integer> shopList = req.getShopIdList();
        List<String> marketplaceIdList = req.getMarketplaceIdList();
        String currency = req.getCurrency();
        String startDate = Optional.of(req.getStartDate()).map(DateUtil::toFormatDate).orElse("");
        String endDate = Optional.of(req.getEndDate()).map(DateUtil::toFormatDate).orElse("");
        DashboardDataFieldEnum dataField = DashboardDataFieldEnum.fieldMap.get(req.getDataField());
        Integer limit = req.getLimit();
        DashboardOrderByEnum orderBy = DashboardOrderByEnum.orderByMap.get(req.getOrderBy());
        //sd只有商品投放
        List<DashboardAdTargetingMatrixTopDto> targetingList = odsAmazonAdSdTargetingReportDao.getTargetingTopList(puid, marketplaceIdList, shopList, currency, startDate, endDate,
                dataField, null, limit, orderBy, siteToday, req.getSiteToday(), portfolioIds, campaignIds, req.getNoZero());
        if (CollectionUtils.isNotEmpty(targetingList)) {
            List<String> sdTargetingIdList = targetingList.parallelStream().map(DashboardAdTargetingMatrixTopDto::getTargetingId).collect(Collectors.toList());
            List<DashboardAdTargetingMatrixTopDto> sdTargetingInfoList = odsAmazonAdSdTargetingReportDao.getTargetingTopInfoList(puid, marketplaceIdList, shopList, currency, startDate, endDate,
                    dataField, sdTargetingIdList, orderBy, siteToday, req.getSiteToday(), null, null, null);
            fillAllDate4List(targetingList, sdTargetingInfoList);
        }
        return targetingList;
    }

    private <T> Field[] getAllField(Class<T> cla) {
        Class clazz = cla;
        List<Field> fieldList = new ArrayList<>();
        while (clazz != null) {
            fieldList.addAll(new ArrayList<>(Arrays.asList(clazz.getDeclaredFields())));
            clazz = clazz.getSuperclass();
        }
        Field[] fields = new Field[fieldList.size()];
        fieldList.toArray(fields);
        return fields;
    }
}
