package com.meiyunji.sponsored.service.reportImport2.processor;

import com.alibaba.fastjson.JSONReader;
import com.google.common.collect.Lists;
import com.meiyunji.sellfox.aadas.types.enumeration.Marketplace;
import com.meiyunji.sponsored.common.springjdbc.DbColumn;
import com.meiyunji.sponsored.common.util.GZipUtils;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.config.CosBucketClient;
import com.meiyunji.sponsored.service.cpc.dao.*;
import com.meiyunji.sponsored.service.cpc.po.*;
import com.meiyunji.sponsored.service.cpc.service.IAmazonAdTargetDaoRoutingService;
import com.meiyunji.sponsored.service.enums.CampaignTypeEnum;
import com.meiyunji.sponsored.service.reportImport2.modle.LxAmazonAdGroupReport;
import com.meiyunji.sponsored.service.reportImport2.modle.LxAmazonAdProductTargetReport;
import com.meiyunji.sponsored.service.reportImport2.vo.AmazonAdReportImportMessage;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.io.InputStreamReader;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * lx活动报告导入处理器
 *
 * <AUTHOR>
 * @date 2023/05/26
 */
@Service
@Slf4j
public class LxAmazonAdProductTargetReportImportProcessor extends AbstractAmazonAdLxReportImportProcessor<LxAmazonAdGroupReport> {



    private final IAmazonSdAdTargetingDao amazonSdAdTargetingDao;
    private final IAmazonSbAdTargetingDao amazonSbAdTargetingDao;
    private final IAmazonAdSbTargetingReportDao amazonAdSbTargetingReportDao;
    private final IAmazonAdSdTargetingReportDao amazonAdSdTargetingReportDao;
    private final ICpcTargetingReportDao cpcTargetingReportDao;
    private final IAmazonAdTargetDaoRoutingService amazonAdTargetDaoRoutingService;


    protected LxAmazonAdProductTargetReportImportProcessor(IAmazonAdCampaignAllDao amazonAdCampaignAllDao, IAmazonAdGroupDao amazonAdGroupDao,
                                                           IAmazonSdAdGroupDao amazonSdAdGroupDao, IAmazonSbAdGroupDao amazonSbAdGroupDao,
                                                           IScVcShopAuthDao shopAuthDao, CosBucketClient tempBucketClient,
                                                           IAmazonSdAdTargetingDao amazonSdAdTargetingDao,
                                                           IAmazonSbAdTargetingDao amazonSbAdTargetingDao,
                                                           IAmazonAdSbTargetingReportDao amazonAdSbTargetingReportDao,
                                                           IAmazonAdSdTargetingReportDao amazonAdSdTargetingReportDao,
                                                           ICpcTargetingReportDao cpcTargetingReportDao,
                                                           IAmazonAdTargetDaoRoutingService amazonAdTargetDaoRoutingService) {
        super(amazonAdCampaignAllDao, amazonAdGroupDao, amazonSdAdGroupDao, amazonSbAdGroupDao, shopAuthDao, tempBucketClient);
        this.amazonSdAdTargetingDao = amazonSdAdTargetingDao;
        this.amazonSbAdTargetingDao = amazonSbAdTargetingDao;
        this.amazonAdSbTargetingReportDao = amazonAdSbTargetingReportDao;
        this.amazonAdSdTargetingReportDao = amazonAdSdTargetingReportDao;
        this.cpcTargetingReportDao = cpcTargetingReportDao;
        this.amazonAdTargetDaoRoutingService = amazonAdTargetDaoRoutingService;
    }


    @Override
    public void importReport(AmazonAdReportImportMessage importMessage) {

        //读取数据
        try (InputStreamReader inputStreamReader = new InputStreamReader(new ByteArrayInputStream(GZipUtils.release(tempBucketClient.getObjectToBytes(importMessage.getFileId().replaceFirst("/", "")))))) {
            ShopAuth shopAuth = shopAuthDao.getScAndVcByIdAndPuid(importMessage.getShopId(), importMessage.getPuid());
            JSONReader jsonReader = new JSONReader(inputStreamReader);
            jsonReader.startArray();
            List<LxAmazonAdProductTargetReport> reports = Lists.newArrayListWithExpectedSize(500);
            while (jsonReader.hasNext()) {
                jsonReader.startObject();
                LxAmazonAdProductTargetReport report = new LxAmazonAdProductTargetReport();
                report.readFromJsonReader(jsonReader);
                jsonReader.endObject();
                if (report.getImpressions() != 0) {
                    reports.add(report);
                    if (reports.size() >= 500) {
                        dealReport(importMessage, reports, shopAuth);
                        reports = Lists.newArrayListWithExpectedSize(500);
                    }
                }

            }
            jsonReader.endArray();
            jsonReader.close();
            if (CollectionUtils.isNotEmpty(reports)) {
                dealReport(importMessage, reports, shopAuth);
            }
        } catch (Exception e) {
            log.error("import campaign report error puid:{},shopId：{}，scheduleId :{} error:", importMessage.getPuid(), importMessage.getShopId(), importMessage.getScheduleId(), e);
        }

    }

    private void dealReport(AmazonAdReportImportMessage importMessage, List<LxAmazonAdProductTargetReport> reports, ShopAuth shopAuth) {
        Integer puid = importMessage.getPuid();
        Integer shopId = importMessage.getShopId();

        String adType = importMessage.getAdType().toLowerCase();
        List<String> campaignIds = reports.stream().map(LxAmazonAdProductTargetReport::getCampaignId).collect(Collectors.toList());
        List<AmazonAdCampaignAll> byCampaignIds = amazonAdCampaignAllDao.getByCampaignIds(puid, shopId, shopAuth.getMarketplaceId(), campaignIds, adType);
        List<String> adTargetIds = reports.stream().map(LxAmazonAdProductTargetReport::getTargetId).collect(Collectors.toList());

        Map<String, AmazonAdTargeting> spTarget = new HashMap<>();
        Map<String, AmazonSbAdTargeting> sbTarget = new HashMap<>();
        Map<String, AmazonSdAdTargeting> sdTarget = new HashMap<>();

        if (CampaignTypeEnum.sp.name().equalsIgnoreCase(importMessage.getAdType())) {
            spTarget = amazonAdTargetDaoRoutingService.getByAdTargetIds(puid, shopId, adTargetIds).stream().collect(Collectors.toMap(AmazonAdTargeting::getTargetId, Function.identity(), (e1, e2) -> e2));

        } else if (CampaignTypeEnum.sb.name().equalsIgnoreCase(importMessage.getAdType())) {
            sbTarget = amazonSbAdTargetingDao.listByTargetId(puid, shopId, adTargetIds).stream().collect(Collectors.toMap(AmazonSbAdTargeting::getTargetId, Function.identity(), (e1, e2) -> e2));
        } else if (CampaignTypeEnum.sd.name().equalsIgnoreCase(importMessage.getAdType())) {
            sdTarget = amazonSdAdTargetingDao.listByTargetId(puid, shopId, adTargetIds).stream().collect(Collectors.toMap(AmazonSdAdTargeting::getTargetId, Function.identity(), (e1, e2) -> e2));
        }
        Map<String, AmazonAdCampaignAll> amazonAdCampaignAllMap = byCampaignIds.stream().collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, Function.identity(), (e1, e2) -> e2));


        List<CpcTargetingReport> spReports = new ArrayList<>();
        List<AmazonAdSbTargetingReport> sbReports = new ArrayList<>();
        List<AmazonAdSdTargetingReport> sdReports = new ArrayList<>();
        Map<String, AmazonAdTargeting> finalSpTarget = spTarget;
        Map<String, AmazonSbAdTargeting> finalSbTarget = sbTarget;
        Map<String, AmazonSdAdTargeting> finalSdTarget = sdTarget;
        reports.forEach(e -> {
            AmazonAdCampaignAll amazonAdCampaignAll = amazonAdCampaignAllMap.get(e.getCampaignId());
            if (amazonAdCampaignAll == null) {
                log.error("pxq-report-import puid : {} shop_id : {} campaignId : {} 不存在", puid, shopId, e.getCampaignId());
                return;
            }


            if (CampaignTypeEnum.sp.name().equalsIgnoreCase(importMessage.getAdType())) {
                AmazonAdTargeting amazonAdTargeting = finalSpTarget.get(e.getTargetId());
                if (amazonAdTargeting == null) {
                    log.error("pxq-report-import puid : {} shop_id : {} campaignId : {}, groupId:{}, targetId: {} 不存在", puid, shopId, e.getCampaignId(), e.getAdGroupId(), e.getTargetId());
                    return;
                }
                CpcTargetingReport spReport = buildSpAdProductTargetReport(importMessage.getCountDate(), e, amazonAdTargeting, amazonAdCampaignAll, shopAuth);
                spReports.add(spReport);
            } else if (CampaignTypeEnum.sb.name().equalsIgnoreCase(importMessage.getAdType())) {
                AmazonSbAdTargeting amazonSbAdTargeting = finalSbTarget.get(e.getTargetId());
                if (amazonSbAdTargeting == null) {
                    log.error("pxq-report-import puid : {} shop_id : {} campaignId : {}, targetId:{} 不存在", puid, shopId, e.getCampaignId(), e.getTargetId());
                    return;
                }
                AmazonAdSbTargetingReport sbReport = buildSbAdProductTargetReport(importMessage.getCountDate(), e, amazonSbAdTargeting, amazonAdCampaignAll, shopAuth);
                sbReports.add(sbReport);
            } else if (CampaignTypeEnum.sd.name().equalsIgnoreCase(importMessage.getAdType())) {
                AmazonSdAdTargeting adTargeting = finalSdTarget.get(e.getTargetId());
                if (adTargeting == null) {
                    log.error("pxq-report-import puid : {} shop_id : {} campaignId : {}, targetId:{} 不存在", puid, shopId, e.getCampaignId(), e.getTargetId());
                    return;
                }
                AmazonAdSdTargetingReport sdReport = buildSdProductTargetReport(importMessage.getCountDate(), e, adTargeting, amazonAdCampaignAll, shopAuth);
                sdReports.add(sdReport);
            }
        });


        //持久数据到数据库
        if (CollectionUtils.isNotEmpty(spReports)) {
            cpcTargetingReportDao.insertList(puid, spReports);
        }

        if (CollectionUtils.isNotEmpty(sbReports)) {
            amazonAdSbTargetingReportDao.insertOrUpdateList(puid, sbReports);
        }

        if (CollectionUtils.isNotEmpty(sdReports)) {
            amazonAdSdTargetingReportDao.insertOrUpdateList(puid, sdReports);
        }
    }


    /**
     * 构建sd组报告
     *
     * @param report              报告
     * @param amazonSdAdTargeting 亚马逊sd广告组
     * @param campaignAll         活动所有
     * @return {@link AmazonAdSdTargetingReport}
     */
    private AmazonAdSdTargetingReport buildSdProductTargetReport(String countDate, LxAmazonAdProductTargetReport report, AmazonSdAdTargeting amazonSdAdTargeting, AmazonAdCampaignAll campaignAll, ShopAuth shopAuth) {
        AmazonAdSdTargetingReport sdTargetingReport = new AmazonAdSdTargetingReport();
        sdTargetingReport.setPuid(shopAuth.getPuid());
        sdTargetingReport.setShopId(shopAuth.getId());
        sdTargetingReport.setMarketplaceId(shopAuth.getMarketplaceId());
        sdTargetingReport.setCountDate(countDate);
        sdTargetingReport.setTacticType(campaignAll.getTactic());
        sdTargetingReport.setCurrency(Marketplace.fromId(shopAuth.getMarketplaceId()).getCurrencyCode().name());
        sdTargetingReport.setCampaignName(report.getCampaignName());
        sdTargetingReport.setCampaignId(report.getCampaignId());
        sdTargetingReport.setAdGroupName(report.getAdGroupName());
        sdTargetingReport.setAdGroupId(report.getAdGroupId());
        sdTargetingReport.setTargetId(report.getTargetId());
        sdTargetingReport.setSales14d(report.getSales() != null ? report.getSales() : BigDecimal.ZERO);
        sdTargetingReport.setSales14dSameSKU(report.getDirectSales() != null ? report.getDirectSales() : BigDecimal.ZERO);
        sdTargetingReport.setUnitsOrdered14d(report.getAdUnits() != null ? report.getAdUnits() : 0);
        sdTargetingReport.setConversions14d(report.getOrders() != null ? report.getOrders() : 0);
        sdTargetingReport.setConversions14dSameSKU(report.getDirectOrders() != null ? report.getDirectOrders() : 0);
        sdTargetingReport.setClicks(report.getClicks() != null ? report.getClicks() : 0);
        sdTargetingReport.setCost(report.getSpends() != null ? report.getSpends() : BigDecimal.ZERO);
        sdTargetingReport.setImpressions(report.getImpressions() != null ? report.getImpressions() : 0);
        sdTargetingReport.setViewImpressions(report.getViewImpressions() != null ? report.getViewImpressions() : 0);
        sdTargetingReport.setCostType(campaignAll.getCostType());
        return sdTargetingReport;
    }

    /**
     * 构建sb广告组报告
     *
     * @param report              报告
     * @param amazonSbAdTargeting 亚马逊sb广告组
     * @return {@link AmazonAdSbGroupReport}
     */
    private AmazonAdSbTargetingReport buildSbAdProductTargetReport(String countDate, LxAmazonAdProductTargetReport report, AmazonSbAdTargeting amazonSbAdTargeting, AmazonAdCampaignAll campaignAll, ShopAuth shopAuth) {
        AmazonAdSbTargetingReport sbTargetReport = new AmazonAdSbTargetingReport();
        sbTargetReport.setPuid(shopAuth.getPuid());
        sbTargetReport.setShopId(shopAuth.getId());
        sbTargetReport.setMarketplaceId(shopAuth.getMarketplaceId());
        sbTargetReport.setCountDate(countDate);
        sbTargetReport.setCurrency(Marketplace.fromId(shopAuth.getMarketplaceId()).getCurrencyCode().name());
        sbTargetReport.setAdFormat(Optional.ofNullable(campaignAll.getAdFormat()).orElse("manual"));
        sbTargetReport.setCampaignName(campaignAll.getName());
        sbTargetReport.setCampaignId(report.getCampaignId());
        sbTargetReport.setAdGroupName(report.getAdGroupName());
        sbTargetReport.setAdGroupId(amazonSbAdTargeting.getAdGroupId());

        sbTargetReport.setTargetId(report.getTargetId());

        sbTargetReport.setTargetingExpression(amazonSbAdTargeting.getType() + "=\"" + amazonSbAdTargeting.getTargetText() + "\"");
        sbTargetReport.setTargetingText(amazonSbAdTargeting.getType() + "=\"" + amazonSbAdTargeting.getTargetText() + "\"");

        sbTargetReport.setSales14d(report.getSales() != null ? report.getSales() : BigDecimal.ZERO);
        sbTargetReport.setSales14dSameSKU(report.getDirectSales() != null ? report.getDirectSales() : BigDecimal.ZERO);
        sbTargetReport.setConversions14d(report.getOrders() != null ? report.getOrders() : 0);
        sbTargetReport.setConversions14dSameSKU(report.getDirectOrders() != null ? report.getDirectOrders() : 0);

        sbTargetReport.setClicks(report.getClicks() != null ? report.getClicks() : 0);
        sbTargetReport.setCost(report.getSpends() != null ? report.getSpends() : BigDecimal.ZERO);
        sbTargetReport.setImpressions(report.getImpressions() != null ? report.getImpressions() : 0);

        sbTargetReport.setUnitsSold14d(report.getAdUnits() != null ? report.getAdUnits() : 0);


        //未定义
//        sbGroupReport.setDpv14d(isDxmNumeric(report.getDpv()) ? Integer.valueOf(report.getDpv()) : 0);
//
//        try {
//            sbGroupReport.setVideo5SecondViewRate(new BigDecimal(report.getVideo5SecondViewRate().replaceAll("%", "")).doubleValue());
//        } catch (Exception exception) {
//            log.error("buildSbAdGroupReport execute setVideo5SecondViewRate errorMsg: {}", exception.getMessage());
//        }
//        sbGroupReport.setVideo5SecondViews(isDxmNumeric(report.getVideo5SecondViews()) ? Integer.valueOf(report.getVideo5SecondViews()) : 0);
//        sbGroupReport.setVideoFirstQuartileViews(isDxmNumeric(report.getVideoFirstQuartileViews()) ? Integer.valueOf(report.getVideoFirstQuartileViews()) : 0);
//        sbGroupReport.setVideoMidpointViews(isDxmNumeric(report.getVideoMidpointViews()) ? Integer.valueOf(report.getVideoMidpointViews()) : 0);
//        sbGroupReport.setVideoThirdQuartileViews(isDxmNumeric(report.getVideoThirdQuartileViews()) ? Integer.valueOf(report.getVideoThirdQuartileViews()) : 0);
//        sbGroupReport.setVideoUnmutes(isDxmNumeric(report.getVideoUnmutes()) ? Integer.valueOf(report.getVideoUnmutes()) : 0);
//        sbGroupReport.setViewableImpressions(isDxmNumeric(report.getViewImpressions()) ? Integer.valueOf(report.getViewImpressions()) : null);
//        sbGroupReport.setVideoCompleteViews(isDxmNumeric(report.getVideoCompleteViews()) ? Integer.valueOf(report.getVideoCompleteViews()) : 0);
//
//
//        try {
//            sbGroupReport.setVctr(new BigDecimal(report.getVctr().replaceAll("%", "")).doubleValue());
//        } catch (Exception exception) {
//            log.error("buildSbAdGroupReport execute setVctr errorMsg: {}", exception.getMessage());
//        }
//
//        try {
//            sbGroupReport.setVtr(new BigDecimal(report.getVtr().replaceAll("%", "")).doubleValue());
//        } catch (Exception exception) {
//            log.error("buildSbAdGroupReport execute setVtr errorMsg: {}", exception.getMessage());
//        }

        return sbTargetReport;
    }

    /**
     * 构建sp广告组报告
     *
     * @param report 报告
     * @return {@link AmazonAdGroupReport}
     */
    private CpcTargetingReport buildSpAdProductTargetReport(String countDate, LxAmazonAdProductTargetReport report, AmazonAdTargeting amazonAdTargeting, AmazonAdCampaignAll campaignAll, ShopAuth shopAuth) {
        CpcTargetingReport cpcTargetingReport = new CpcTargetingReport();
        cpcTargetingReport.setPuid(shopAuth.getPuid());
        cpcTargetingReport.setShopId(shopAuth.getId());
        cpcTargetingReport.setMarketplaceId(shopAuth.getMarketplaceId());
        cpcTargetingReport.setCountDate(countDate);
        cpcTargetingReport.setCampaignId(report.getCampaignId());
        cpcTargetingReport.setAdGroupId(report.getAdGroupId());
        cpcTargetingReport.setAdGroupName(report.getAdGroupName());
        cpcTargetingReport.setCampaignName(campaignAll.getName());
        cpcTargetingReport.setTargetId(report.getTargetId());
        cpcTargetingReport.setTotalSales(report.getSales() != null ? report.getSales() : BigDecimal.ZERO);
        cpcTargetingReport.setAdSales(report.getDirectSales() != null ? report.getDirectSales() : BigDecimal.ZERO);
        String targetingType = null;
        if ("auto".equalsIgnoreCase(campaignAll.getTargetingType())) {
            targetingType = "TARGETING_EXPRESSION_PREDEFINED";
        }
        if ("manual".equalsIgnoreCase(campaignAll.getTargetingType())) {
            targetingType = "TARGETING_EXPRESSION";
        }
        cpcTargetingReport.setTargetingType(targetingType);
        cpcTargetingReport.setOrderNum(report.getAdUnits() != null ? report.getAdUnits() : 0);
        cpcTargetingReport.setAdOrderNum(report.getDirectUnits() != null ? report.getDirectUnits() : 0);
        cpcTargetingReport.setSaleNum(report.getOrders() != null ? report.getOrders() : 0);
        cpcTargetingReport.setAdSaleNum(report.getDirectOrders() != null ? report.getDirectOrders() : 0);

        cpcTargetingReport.setClicks(report.getClicks() != null ? report.getClicks() : 0);
        cpcTargetingReport.setCost(report.getSpends() != null ? report.getSpends() : BigDecimal.ZERO);
        cpcTargetingReport.setImpressions(report.getImpressions() != null ? report.getImpressions() : 0);

        return cpcTargetingReport;
    }


}
