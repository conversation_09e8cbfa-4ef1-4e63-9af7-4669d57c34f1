package com.meiyunji.sponsored.service.autoRule.po;

import com.meiyunji.sponsored.common.springjdbc.DbColumn;
import com.meiyunji.sponsored.common.springjdbc.DbTable;
import lombok.Data;

import java.io.Serializable;

@DbTable(value = "t_ad_auto_rule_status_sequence")
@Data
public class AdvertiseAutoRuleStatusSequence implements Serializable {
    @DbColumn(value = "id", key = true, autoIncrement = true)
    private Long id;

    @DbColumn(value = "stub")
    private String stub;
}
