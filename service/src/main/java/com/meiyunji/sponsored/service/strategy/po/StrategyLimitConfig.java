package com.meiyunji.sponsored.service.strategy.po;

import com.meiyunji.sponsored.common.springjdbc.DbColumn;
import com.meiyunji.sponsored.common.springjdbc.DbTable;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 报告数据层级差异监控表
 *
 * @Author: hejh
 * @Date: 2024/5/30 10:57
 */
@Data
@DbTable(value = "t_strategy_limit_config")
public class StrategyLimitConfig implements Serializable {
    /**
     * 主键id
     */
    @DbColumn(value = "id", autoIncrement = true, key = true)
    private Long id;
    /**
     * puid
     */
    @DbColumn(value = "puid")
    private Integer puid;
    /**
     * 对于分时策略:单个puid下广告组模板数量限制.对于自动化:单个puid下除去强排名模板的数量限制
     */
    @DbColumn(value = "group_template_num_limit")
    private Integer groupTemplateNumLimit;
    /**
     * 对于分时策略:单个模板下广告组受控对象数量限制.
     */
    @DbColumn(value = "group_controlled_object_num_limit")
    private Integer groupControlledObjectNumLimit;
    /**
     * 策略类型：1-分时策略. 2-自动化
     */
    @DbColumn(value = "strategy_type")
    private Integer strategyType;
    /**
     * 是否过期：1-已过期、0-未过期
     */
    @DbColumn(value = "expired")
    private Integer expired;
    /**
     * 过期时间
     */
    @DbColumn(value = "expired_time")
    private LocalDateTime expiredTime;
    /**
     * 创建时间
     */
    @DbColumn(value = "create_time")
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    @DbColumn(value = "update_time")
    private LocalDateTime updateTime;
}
