package com.meiyunji.sponsored.service.cpc.dao;

import com.meiyunji.sellfox.aadas.types.enumeration.CampaignStrategy;
import com.meiyunji.sellfox.aadas.types.schedule.CampaignAdjustment;
import com.meiyunji.sellfox.aadras.types.schedule.AdvertiseRuleTaskExecuteRecordV2Message;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.springjdbc.IBaseShardingDao;
import com.meiyunji.sponsored.service.autoRule.vo.AdCampaignAutoRuleParam;
import com.meiyunji.sponsored.service.autoRule.vo.AllUpdateAutoRuleParam;
import com.meiyunji.sponsored.service.autoRule.vo.AutoRuleCampaignVo;
import com.meiyunji.sponsored.service.cpc.bo.AllCampaignOrderBo;
import com.meiyunji.sponsored.service.cpc.bo.AmazonAdCampaignAllBo;
import com.meiyunji.sponsored.service.cpc.bo.AmazonAdCampaignTargetingTypeBO;
import com.meiyunji.sponsored.service.cpc.bo.FeedTargetCampaignDto;
import com.meiyunji.sponsored.service.cpc.dto.CampaignTypeDto;
import com.meiyunji.sponsored.service.autoRule.vo.CampaignIdWithAdTypeVo;
import com.meiyunji.sponsored.service.cpc.bo.*;
import com.meiyunji.sponsored.service.cpc.vo.MultiShopCampaignListParam;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdCampaignAll;
import com.meiyunji.sponsored.service.cpc.vo.*;
import com.meiyunji.sponsored.service.cpc.vo.pricing.ScheduleTaskFinishedVo;
import com.meiyunji.sponsored.service.export.dto.DownloadCenterCampaignBaseDataBO;
import com.meiyunji.sponsored.service.gps.vo.GpsCampaign;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.qo.CampaignViewParam;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.qo.DiagnoseCountParam;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.qo.PlacementViewParam;
import com.meiyunji.sponsored.service.strategy.vo.AdCampaignStrategyParam;
import com.meiyunji.sponsored.service.strategy.vo.AdSpaceStrategyParam;
import com.meiyunji.sponsored.service.strategy.vo.ControlledObjectParam;
import com.meiyunji.sponsored.service.strategyTask.vo.ProcessTaskParam;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;


public interface IAmazonAdCampaignAllDao extends IBaseShardingDao<AmazonAdCampaignAll> {


    void insertOnDuplicateKeyUpdateSp(Integer puid, List<AmazonAdCampaignAll> amazonAdCampaignList, boolean syncHistory);

    void batchAddSd(int puid, List<AmazonAdCampaignAll> list, boolean syncHistory);

    void batchUpdateSd(int puid, List<AmazonAdCampaignAll> list, boolean syncHistory);

    void batchAddSb(int puid, List<AmazonAdCampaignAll> list, boolean syncHistory);

    void batchUpdateSb(int puid, List<AmazonAdCampaignAll> list, boolean syncHistory);

    void batchAddSbV3(int puid, List<AmazonAdCampaignAll> list, boolean syncHistory);

    void batchUpdateSbV3(int puid, List<AmazonAdCampaignAll> list, boolean syncHistory);

    List<AmazonAdCampaignAll> listByCampaignId(int puid, Integer shopId, List<String> campaignIds, String type);

    List<AmazonAdCampaignAll> listByCampaignIdNoShopId(int puid, List<String> campaignIds, String type);

    List<AmazonAdCampaignAll> listByCampaignIdNoType(int puid, int shopId, List<String> campaignIds);

    List<AmazonAdCampaignAll> listByCampaignIdNoType(int puid,int shopId,List<String> campaignIds, List<String> portfolioIds);

    List<AmazonAdCampaignAll> autoRuleCampaign(int puid, int shopId, String campaignName, List<String> portfolioIds, List<String> adTypeList, List<String> campaignIds, String state, List<String> servingStatus);

    List<String> getSpCampaignIdsByTargetType(Integer puid, Integer shopId, String targetingType);

    List<String> getCampaignIdsByPortfolioId(Integer puid, Integer shopId, String portfolioId, String type, String state, String servingStatus);

    List<String> getCampaignIdsByPortfolioIdAndShopIds(Integer puid, List<Integer> shopId, String portfolioId, String type, String state, String servingStatus);

    List<CampaignTypeDto> getCampaignIdsTypeByPortfolioId(Integer puid, List<Integer> shopId, String portfolioId, String campaignId, String type, String state, String servingStatus);

    /**
     * 重构版本：返回 SQL 字符串而非查询结果
     * 获取根据 portfolio_id 筛选的 campaign_id 和 type 的 SQL 查询语句
     *
     * @param puid 用户ID
     * @param shopId 店铺ID列表
     * @param portfolioId 广告组合ID
     * @param campaignId 广告活动ID
     * @param type 广告类型
     * @param state 状态
     * @param servingStatus 服务状态
     * @param argsList 参数列表（用于收集SQL参数）
     * @return SQL查询字符串
     */
    String getCampaignIdsTypeByPortfolioIdSql(Integer puid, List<Integer> shopId, String portfolioId, String campaignId, String type, String state, String servingStatus, List<Object> argsList);

    List<String> getCampaignIdsAndShopIdsByPortfolioId(Integer puid, List<Integer> shopId, String portfolioId, String name);

    List<String> getCampaignIdsByPortfolioId(Integer puid, Integer shopId, List<String> portfolioIds, List<String> types);

    List<String> getCampaignIdsAllByPortfolioId(Integer puid, Integer shopId, List<String> portfolioIds, List<String> types);

    List<String> getCampaignIdByShopIdsPortfolioIds(Integer puid, List<Integer> shopIds, List<String> portfolioIds, List<String> types);

    List<String> getCampaignIdByPortfolioIdCampaignId(Integer puid, String marketplaceId, List<Integer> shopIds, List<String> portfolioIds, List<String> campaignIds);

    List<String> getCampaignIdListByPortfolioId(Integer puid, Integer shopId, List<String> portfolioIds, String type);

    List<String> getCampaignIdListByPortfolioIdNoType(Integer puid, Integer shopId, List<String> portfolioIds);

    /**
     * 通过广告组合Ids获取广告活动Ids
     *
     * @param puid
     * @param shopId
     * @param portfolioIds
     * @return
     */
    List<String> getCampaignIdsByPortfolioIdList(Integer puid, Integer shopId, List<String> portfolioIds);

    List<String> getCampaignIdsByPortfolioIdList(Integer puid, List<Integer> shopId, List<String> portfolioIds);

    List<String> getCampaignIdsByPortfolioId(Integer puid, Integer shopId, String portfolioId, String type, String campaignIds);

    List<String> getCampaignIdsByPortfolioId(Integer puid, String shopId, String portfolioId, String type, String campaignIds);

    List<AmazonAdCampaignAll> getCampaignsByPortfolioIdList(Integer puid, Integer shopId, List<String> portfolioIds);

    List<AmazonAdCampaignAll> getCampaignsByPortfolioIdList(AllUpdateAutoRuleParam param);

    List<AmazonAdCampaignAll> getCampaignsByPortfolioIdList(Integer puid, Integer shopId, List<String> portfolioIds, String campaignName);

    List<AmazonAdCampaignAll> getList(Integer puid, CampaignPageParam param);

    List<AmazonAdCampaignAll> getCampaignViewList(Integer puid, CampaignViewParam param);

    List<AmazonAdCampaignAll> getAllCampaignViewList(Integer puid, CampaignViewParam param);

    Page<AmazonAdCampaignAll> getList(Integer puid, Integer shopId, List<String> types, List<String> portfiolioIds, Integer pageSize, Integer pageNo, String campaignName, String targetingType, String campaignId, List<String> statusList);

    List<AmazonAdCampaignAll> listCampaignIds(int puid, int shopId, String state, String servingStatus, List<String> campaignIdList);

    Page getPageList(Integer puid, CampaignPageParam param, Page page);

    List<String> getCampaignIdsByCampaign(Integer puid, CampaignPageParam param);


    AmazonAdCampaignAll getCampaignByCampaignId(int puid, Integer shopId, String campaignId, String type);

    List<AmazonAdCampaignAll> getByCampaignIds(Integer puid, Integer shopId, String marketplaceId, List<String> campaignId, String type);

    List<AmazonAdCampaignAll> listByCampaignIds(Integer puid, Integer shopId,  List<String> campaignId);

    List<AmazonAdCampaignAll> listByShopIdAndCampaignIds(Integer puid, List<Integer> shopId,  List<String> campaignId);

    List<AmazonAdCampaignAll> getByCampaignIdsAndShopIdList(Integer puid, List<Integer> shopIdList, String marketplaceId, List<String> campaignId, String type);

    List<AmazonAdCampaignAll> getByCampaignIdsAndShopIdList(Integer puid, List<Integer> shopIdList, String marketplaceId, List<String> campaignId, List<String> typeList);

    List<String> getPortfolioListByCampaignIds(Integer puid, Integer shopId, List<String> campaignId, String type);

    List<String> getPortfolioListByCampaignIds(Integer puid, Integer shopId, List<String> campaignId);

    AmazonAdCampaignAll getByCampaignId(int puid, Integer shopId, String marketPlaceId, String campaignId, String type);

    AmazonAdCampaignAll getByCampaignName(int puid, Integer shopId, String campaignName, String type);

    AmazonAdCampaignAll getByCampaignIdAndStrategy(int puid, Integer shopId, String marketPlaceId, String campaignId, String type, String strategy, String state, String servingStatus);

    List<AmazonAdCampaignAll> getByCampaignIdAndStrategy(int puid, Integer shopId, String marketPlaceId, List<String> campaignIds, String type, String strategy, String state, String servingStatus);

    List<String> getByCampaignIdsAndStrategy(int puid, Integer shopId, String marketPlaceId, List<String> campaignIds, String type, String strategy, String state, String servingStatus);

    List<String> getCampaignIdByStrategy(int puid, Integer shopId, String marketPlaceId, String campaignId, String type, String strategy, String state, String servingStatus);

    List<String> getCampaignIdByStrategy(int puid, Integer shopId, String marketPlaceId, List<String> campaignIds, String type, String strategy, String state, String servingStatus);

    /**
     * 获取SB广告活动/广告组下的Asin集合
     *
     * @param puid
     * @param shopId
     * @param campaignId
     * @return
     */
    AmazonAdCampaignAll getSbAsinByCampaignId(int puid, Integer shopId, String campaignId);
    AmazonAdCampaignAll getSdAsinByCampaignId(int puid, Integer shopId, String campaignId);

    /**
     * 获取SB广告活动/广告组下的Asin集合
     *
     * @param puid
     * @param shopId
     * @param campaignId
     * @return
     */
    List<AmazonAdCampaignAll> getAllStateSbAsinByCampaignId(int puid, Integer shopId, String campaignId);

    boolean exist(Integer puid, Integer shopId, String name, String type);

    void batchUpdateAmazonAdCampaign(Integer puid, List<AmazonAdCampaignAll> list, String type);

    List<AmazonAdCampaignAll> getCampaignsByShop(Integer puid, Integer shopId, String type);

    List<AmazonAdCampaignAll> getCampaignsByNoShop(Integer puid, Integer shopId, String type);

    List<AmazonAdCampaignAll> getListByPortfolioId(Integer puid, Integer shopId, String portfolioId, String type);

    List<AmazonAdCampaignAll> getListByPortfolioId(Integer puid, Integer shopId, String portfolioId);

    void batchUpdatePortfolio(Integer puid, Integer shopId, String portfolioId, Integer uid, List<String> list);

    List<AmazonAdCampaignAll> getAllCampaignsByShop(Integer puid, Integer shopId, String strategyType, List<String> campaignIdList, String type, String state, String servingStatus);

    String getMarketplaceIdByShopId(Integer puid, Integer shopId, String type);

    String getNameByCpId(int puid, Integer shopId, String campaignId, String type);

    String getTypeByCpId(int puid, Integer shopId, String marketplaceId, String campaignId);

    List<AdCampaignOptionVo> getCampaignsByType(Integer puid, Integer shopId, String type, String campaignType, String groupType, String name, String campaignIds, String portfolioId);

    List<AdCampaignOptionVo> getCampaignsByType(Integer puid, String shopId, String type, String campaignType, String groupType, String name, String campaignIds, String portfolioId);

    Long getId(Integer puid, Integer shopId, String campaignId, String type);

    String getProfileId(int puid, Integer shopId, String marketPlaceId, String campaignId, String type);

    void updateState(int puid, Integer shopId, String campaignId, String state, int updateId, String type);

    void updateDailyBudget(int puid, Integer shopId, String campaignId, Double dailyBudget, int updateId, String type);

    int updateOutOfBudgetInfo(int puid, Integer shopId, String campaignId, Double dailyBudget, Long timestamp, int updateId, String type);

    void updateDailyBudgetAndServingStatus(int puid, Integer shopId, String campaignId, Double dailyBudget, int updateId, String type);

    String getNameByCampaignId(Integer puid, Integer shopId, String marketplaceId, String campaignId, String type);


    String getCampaignTypeByCampaignId(Integer puid, Integer shopId, String marketplaceId, String campaignId, String type);

    Page pageListForTask(int puid, CpcTaskSearchDto dto, Page page, String type);

    List<Map<String, Object>> getCampaignNames(int puid, Integer shopId, String marketplaceId, String searchValue, Integer pageNo, Integer pageSize, String type);

    Integer getCampaignNamesTotalSize(int puid, Integer shopId, String searchValue, String type);

    List<AmazonAdCampaignAll> getNamesAndState(int puid, Integer shopId, String type);

    List<String> getCampaignIdsByState(Integer puid, Integer shopId, String state, String type);

    void updateBudgetPricing(Integer puid, Integer shopId, String campaignId, Integer isPricing, Integer pricingState, int updateId, String type);

    void updateStatePricing(Integer puid, Integer shopId, String campaignId, Integer isPricing, Integer pricingState, int updateId, String type);

    void updateDataUpdateTime(Integer puid, Integer shopId, String campaignId, LocalDate localDate, String type);

    void updateSpacePricing(Integer puid, Integer shopId, String campaignId, Integer isPricing, Integer pricingState, int updateId, String type);

    List<Map<String, Object>> getCampaignNameList(int puid, Integer shopId, String type);

    List<Map<String, Object>> getCampaignNameByShopIdsAndCampaignIdsList(int puid, List<Integer> shopIds, List<String> campaignIds, String type);

    List<AmazonAdCampaignAll> getCampaignNameByShopIdsList(int puid, List<Integer> shopIds, String type);

    List<String> getArchivedItems(Integer puid, Integer shopId, String type);

    List<String> getUpdateAfterReportSyncTimeItems(Integer puid, Integer shopId, LocalDateTime syncAt, String type);

    List<Map<String, Object>> getSpCampaignNames(int puid, GetNamesDto dto);

    List<AmazonAdCampaignAll> listNoCampaignTargetType(Integer puid, Integer shopId, String campaignId, String type);

    String getSbCreativeByCampaignId(Integer puid, Integer shopId, String campaignId);

    List<AmazonAdCampaignAll> getListCampaign(int puid);

    /**
     * 分时调价添加活动查询(新版分时调价专属)
     *
     * @param param
     * @return
     */
    Page<AmazonAdCampaignAll> queryAmazonAdCampaignAll(AdCampaignStrategyParam param);

    /**
     * 分时调价广告位添加活动查询(新版分时调价专属)
     *
     * @param param
     * @return
     */
    Page<AmazonAdCampaignAll> queryAmazonAdSpaceAll(AdSpaceStrategyParam param);

    /**
     * 分时调价添加活动查询(新版分时调价专属)
     *
     * @param param
     * @return
     */
    Page<AmazonAdCampaignAll> queryAmazonAdCampaignStateAll(AdCampaignStrategyParam param);


    /**
     * 自动化日志查询广告活动对象
     * @param campaignId
     * @return
     */
    AmazonAdCampaignAll getByCampaignId(int puid, Integer shopId, String campaignId);

    String getSbFormatByCampaignId(Integer puid, Integer shopId, String campaignId);


    List<String> getCampaignIds(Integer puid, Integer shopId, String type);

    List<String> getCampaignIds(Integer puid, Integer shopId, String type, List<String> campaignIds);

    void batchUpdateSbAdFormat(int puid, List<AmazonAdCampaignAll> list);

    List<AmazonAdCampaignAll> getListSbFormatIsNUll(Integer puid, Integer shopId, List<String> campaignId);

    /**
     * TODO 关联关键词库查询
     *
     * @param puid
     * @param shopIds
     * @param campaignIds
     * @param type
     * @return
     */
    List<AmazonAdCampaignAll> getListByShopIdsAndCampaignIds(int puid, List<Integer> shopIds, List<String> campaignIds, String type);

    List<AmazonAdCampaignAll> getNameByShopIdsAndCampaignIds(int puid, List<Integer> shopIds,
                                                             List<String> campaignIds, String type,
                                                             String campaignName);

    List<AmazonAdCampaignAll> getNameAndStateByShopIdsAndCampaignIds(int puid, List<Integer> shopIds,
                                                             List<String> campaignIds, String type,
                                                             String campaignName);

    List<AmazonAdCampaignAll> getNameByShopIdsAndPortfolioIdsAndCampaignIds(Integer puid, List<Integer> shopIdList, List<String> portfolioIdList, List<String> campaignIds, String type, String campaignName);

    /**
     * 自动化规则受控活动查询
     *
     * @param puid
     * @param shopId
     * @param portfolioIds
     * @param campaignName
     * @return
     */
    List<AmazonAdCampaignAll> getAutoRuleCampaigns(int puid, int shopId, List<String> portfolioIds, String campaignName);

    /**
     * 自动化规则活动分页查询
     * @param param
     * @param itemIds
     * @param similarRuleItemIdList
     * @return
     */
    Page<AmazonAdCampaignAll> pageAutoRuleCampaigns(AdCampaignAutoRuleParam param, List<String> itemIds, List<String> similarRuleItemIdList);

    /**
     * 获取每日预算汇总字段
     *
     * @param puid
     * @param param
     */
    BigDecimal getSumDailyBudget(Integer puid, CampaignPageParam param);

    int setEndDateNull(AmazonAdCampaignAll campaignAll);

    /**
     * 策略同步修改
     *
     * @param puid
     * @param shopId
     * @param itemType
     * @param message
     * @param type
     * @param campaignId
     * @return
     */
    void strategyUpdate(int puid, int shopId, String itemType, ScheduleTaskFinishedVo message, String type, String campaignId);

    /**
     * 策略同步修改
     *
     * @param puid
     * @param shopId
     * @param campaignId
     * @return
     */
    void strategyUpdatePlacement(int puid, int shopId, List<CampaignAdjustment> adjustmentList, String campaignId);

    /**
     * 策略同步修改
     *
     * @param puid
     * @param shopId
     * @param campaignId
     * @return
     */
    void strategyUpdateStrategy(int puid, int shopId, CampaignStrategy strategy, String campaignId);

    String getCampaignIdByName(Integer puid, Integer shopId, String name);

    List<AmazonAdCampaignAll> listByTypeAndCampaignNames(int puid, String adType, List<String> campaignNames, List<Integer> shopIds);

    List<String> getCampaignIdsByPortfolioIdOrStatusOrServingStatus(Integer puid, Integer shopId, String portfolioId, String state, String servingStatus, String type);

    List<String> getCampaignIdsByPortfolioIdOrStatusOrServingStatus(Integer puid, Integer shopId, String portfolioId, String campaignIdList, String state, String servingStatus, String type);

    List<String> getNameByCampaignIds(Integer puid, Integer shopId, List<String> campaignIds);

    List<String> queryCampaignIds4StartStop(ControlledObjectParam param);

    List<String> queryCampaignIds(ControlledObjectParam param);

    List<AmazonAdCampaignTargetingTypeBO> getTargetingTypeByCampaignIdList(Integer puid, Integer shopId, List<String> campaignIdList);

    /**
     * 策略同步修改
     *
     * @param message
     * @return
     */
    void autoRuleUpdate(AdvertiseRuleTaskExecuteRecordV2Message message);


    /**
     * 分页获取广告位视图的广告活动
     */
    Page<AmazonAdCampaignAll> getPlacementViewPage(Integer puid, PlacementViewParam param);

    /**
     * 获取广告位视图的广告活动
     */
    List<AmazonAdCampaignAll> getPlacementViewList(Integer puid, PlacementViewParam param);

    List<String> getDiagnoseCountCampaignId(DiagnoseCountParam diagnoseCountParam);

    void insertList4BatchCreate(Integer puid, List<AmazonAdCampaignAll> successCampaignAllList);

    List<AmazonAdCampaignAll> listByCampaignIds(Integer puid, List<String> campaignIds);

    List<String> getCampaignIdsByStates(Integer puid, Integer shopId, List<String> states, String type);

    int deleteByPuidAndShopId(Integer puid, Integer shopId, Integer limit);

    Map<String, AmazonAdCampaignAll> getSdCampaignByCampaignIds(Integer puid, Integer shopId, List<String> campaignIds);

    List<String> getSdTacticAndCostTypeByCampaignIds(Integer puid, Integer shopId, String campaignId);

    Page<CampaignInfoPageVo> getAllCampaignPage(Integer puid, CampaignPageParam param);

    List<String> getCampaignIdListByParamAndIds(Integer puid, CampaignPageParam param, List<String> campaignIds);

    List<CampaignInfoPageVo> getCampaignPageVoListByCampaignIdList(Integer puid, CampaignPageParam param, List<String> campaignIdList);

    List<AllCampaignOrderBo> getCampaignIdAndOrderFieldList(Integer puid, CampaignPageParam param, List<String> campaignIdList, String orderField);

    List<String> getVcpmCampaignIdsByCampaignIds(Integer puid, Integer shopId, List<String> campaignId);

    List<FeedTargetCampaignDto> getFeedTargetListByCampaignIds(Integer puid, List<String> campaignIdList);

    List<AmazonAdCampaignAll> listByGetCampaignIds(Integer puid, List<String> campaignIds);

    List<String> getCampaignIds(ProcessTaskParam param);

    List<String> getAutoRuleCampaignIds(com.meiyunji.sponsored.service.autoRuleTask.vo.ProcessTaskParam param);

    List<GpsCampaign> getGpsCampaignByName(Integer puid, List<String> nameList);

    AmazonAdCampaignAll getByLimitTime(int puid, int shopId, int seconds);

    //根据广告活动id获取数据
    List<AmazonAdCampaignAllBo> listBoByCampaignIds(Integer puid, List<Integer> shopIdList, List<String> campaignIdList);
    List<AmazonAdCampaignAllBo> listByShopIdListAndCampaignIdList(Integer puid, Collection<Integer> shopIdList, Collection<String> campaignIdList);

    List<AmazonSdAdCampaignCostTypeBo> listSdCostTypeBoByCampaignIds(Integer puid, List<Integer> shopIdList, List<String> campaignIdList);

    List<String> getCampaignIdsByPortfolioIdOrStatusOrServingStatus(Integer puid, List<Integer> shopIds, String portfolioId, String state, String servingStatus, List<String> campaignIds, String type);

    int getValidShopCountByCondition(Integer puid, Integer shopId);

    Page<AmazonAdCampaignAll> getMultiShopCampaignList(Integer puid, MultiShopCampaignListParam param);

    List<MultiShopCampaignIdVo> getMultiShopCampaignId(Integer puid, List<Integer> shopIdList, List<String> portfolioIdList, List<String> campaignIdList, List<String> adTypeList);

    List<String> getCampaignIdByShopAndPortfolio(Integer puid, List<Integer> shopIdsList, List<String> portfolioIdsList);

    List<AmazonAdCampaignAll> getByShopCampaignPair(Integer puid, List<MultiShopCampaignListParam> paramList);

    List<String> queryArchivedByCampaignIdList(Integer puid, Integer shopId, List<String> campaignIdList);

    List<String> getCampaignIdListByPortfolioIdAndType(Integer puid, Integer shopId, List<String> portfolioIds, List<String> adTypeList);

    List<CampaignIdWithAdTypeVo> getCampaignIdListByPortfolioIdWithType(Integer puid, Integer shopId, List<String> portfolioIds, List<String> adTypeList);

    List<AmazonAdCampaignAll> getByCampaignIdsAndShopIdListAndMarketplaceIdList(Integer puid, List<Integer> shopIds, List<String> marketplaceIds, List<String> ids, String type);

    List<CampaignTypeDto> getCampaignIdsAndTypeByPortfolioId(Integer puid, List<Integer> shopIds, String portfolioId, String searchVal);

    List<String> getCampaignIdListByPortfolioIdAndStrategyType(Integer puid, Integer shopId, List<String> campaignIdList, com.meiyunji.sponsored.service.autoRuleTask.vo.ProcessTaskParam param);

    List<DownloadCenterCampaignBaseDataBO> queryBaseData4DownloadByCampaignIdList(Integer puid, Integer shopId, List<String> campaignIdList);

    List<AutoRuleCampaignVo> getCampaignDataByCampaignId(int puid, int shopId, ArrayList<String> campaignIds);
}
