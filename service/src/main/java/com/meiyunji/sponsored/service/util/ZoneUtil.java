package com.meiyunji.sponsored.service.util;

import org.apache.commons.lang3.StringUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Set;
import java.util.TimeZone;

/**
 * @project: amzup
 * @description:
 * @author: HLX
 * @create: 2020-06-11 19:20
 **/
public class ZoneUtil {

    public static LocalDate getTargetZoneTime(String targetMarketplaceId, LocalDateTime time, ZoneId srcZoneId) {
        //获取目标站点时区
        ZoneId targetZoneId = ZoneUtil.getZoneIdByAmzSite(targetMarketplaceId);
        return LocalDateTimeUtil.getZoneDate(time, srcZoneId, targetZoneId);
    }


    public static Set<String> getAllZoneIds() {
        return ZoneId.getAvailableZoneIds();
    }

    public static ZoneId getZoneIdByAmzSite(String site) {
        ZoneId zoneId = ZoneId.of("Asia/Shanghai");
        if (StringUtils.isBlank(site)) return zoneId;
        switch (site) {
            case "US":
            case "ATVPDKIKX0DER":
            case "CA":
            case "A2EUQ1WTGCTBG2":
            case "MX":
            case "A1AM78C64UM0Y8":
                zoneId = ZoneId.of("US/Pacific"); //美国，加拿大，墨西哥（西八区，太平洋时间）
                break;
            case "FR":
            case "A13V1IB3VIYZZH":
            case "IT":
            case "APJ6JRA9NG5V4":
            case "ES":
            case "A1RKKUPIHCS9HS":
            case "DE":
            case "A1PA6795UKMFR9":
                zoneId = ZoneId.of("Europe/Paris"); //法国，意大利，西班牙，德国（东一区，巴黎时间）
                break;
            case "UK":
            case "A1F83G8C2ARO7P":
                zoneId = ZoneId.of("Europe/London"); //英国（零时区，伦敦时间）
                break;
            case "JP":
            case "A1VC38T7YXB528":
                zoneId = ZoneId.of("Asia/Tokyo"); //日本（东九区，东京时间）
                break;
            case "AU":
            case "A39IBJ37TRP1C6":
                zoneId = ZoneId.of("Australia/Canberra"); //澳大利亚（东10区，堪培拉时间）
                break;
            case "IN":
            case "A21TJRUUN4KGV":
                zoneId = ZoneId.of("Asia/Kolkata"); //（东5区，新德里时间）
                break;
            case "BR":
            case "A2Q3Y263D00KWC":
                zoneId = ZoneId.of("Brazil/East");// (巴西 西三区 巴西时间)
                break;
            case "NL":
            case "A1805IZSGTT6HS":
                zoneId = ZoneId.of("Europe/Amsterdam"); //(荷兰 阿姆斯特丹时间)
                break;
            case "SA":
            case "A17E79C6D8DWNP":
                zoneId = ZoneId.of("Asia/Riyadh"); //(沙特阿拉伯  利雅德时间)
                break;
            case "TR":
            case "A33AVAJ2PDY3EV":
                zoneId = ZoneId.of("Turkey"); // ( 土耳其 东三区  土耳其时间)
                break;
            case "AE":
            case "A2VIGQ35RCS4UG":
                zoneId = ZoneId.of("Asia/Dubai"); //( 阿联酋 迪拜时间)
                break;
            case "SG":
            case "A19VAU5U5O7RUS":
                zoneId = ZoneId.of("Asia/Singapore"); // (新加坡 新加坡时间)
                break;
            case "SE":
            case "A2NODRKZP88ZB9":
                zoneId = ZoneId.of("Europe/Stockholm");           // 瑞典 （ 斯德哥尔摩时间 ）
                break;
            case "PL":
            case "A1C3SOZRARQ6R3":
                zoneId = ZoneId.of("Europe/Warsaw");           // 波兰 （ 华沙时间 ）
                break;
            case "BE":
            case "AMEN7PMS3EDWL":
                zoneId = ZoneId.of("Europe/Brussels");           //比利时
                break;
        }

        return zoneId;
    }

    public static String getZoneName(String site) {
        String name = "北京时间";
        if (StringUtils.isBlank(site)) return name;
        switch (site) {
            case "US":
            case "ATVPDKIKX0DER":
            case "CA":
            case "A2EUQ1WTGCTBG2":
            case "MX":
            case "A1AM78C64UM0Y8":
                name = "太平洋时间"; //美国，加拿大，墨西哥（西八区，太平洋时间）
                break;
            case "FR":
            case "A13V1IB3VIYZZH":
            case "IT":
            case "APJ6JRA9NG5V4":
            case "ES":
            case "A1RKKUPIHCS9HS":
            case "DE":
            case "A1PA6795UKMFR9":
                name = "巴黎时间"; //法国，意大利，西班牙，德国（东一区，巴黎时间）
                break;
            case "UK":
            case "A1F83G8C2ARO7P":
                name = "伦敦时间"; //英国（零时区，伦敦时间）
                break;
            case "JP":
            case "A1VC38T7YXB528":
                name = "东京时间"; //日本（东九区，东京时间）
                break;
            case "AU":
            case "A39IBJ37TRP1C6":
                name = "堪培拉时间"; //澳大利亚（东10区，堪培拉时间）
                break;
            case "IN":
            case "A21TJRUUN4KGV":
                name = "新德里时间"; //（东5区，新德里时间）
                break;
            case "BR":
            case "A2Q3Y263D00KWC":
                name = "巴西时间";// (巴西 西三区 巴西时间)
                break;
            case "NL":
            case "A1805IZSGTT6HS":
                name = "荷兰时间"; //(荷兰 阿姆斯特丹时间)
                break;
            case "SA":
            case "A17E79C6D8DWNP":
                name = "利雅德时间"; //(沙特阿拉伯  利雅德时间)
                break;
            case "TR":
            case "A33AVAJ2PDY3EV":
                name = "土耳其时间"; // ( 土耳其 东三区  土耳其时间)
                break;
            case "AE":
            case "A2VIGQ35RCS4UG":
                name = "迪拜时间"; //( 阿联酋 迪拜时间)
                break;
            case "SG":
            case "A19VAU5U5O7RUS":
                name = "新加坡时间"; // (新加坡 新加坡时间)
                break;
            case "SE":
            case "A2NODRKZP88ZB9":
                name = "瑞典时间"; //瑞典
                break;
            case "PL":
            case "A1C3SOZRARQ6R3":
                name = "华沙时间";           // 波兰 （ 华沙时间 ）
                break;
            case "BE":
            case "AMEN7PMS3EDWL":
                name = "比利时时间";           //比利时
                break;
        }
        return name;
    }

}
