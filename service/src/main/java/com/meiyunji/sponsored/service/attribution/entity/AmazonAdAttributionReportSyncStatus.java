package com.meiyunji.sponsored.service.attribution.entity;

import com.meiyunji.sponsored.common.springjdbc.DbColumn;
import com.meiyunji.sponsored.common.springjdbc.DbTable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
    * 站外流量归因报告同步状态
    */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@DbTable(value = "t_amazon_ad_attribution_report_sync_status")
public class AmazonAdAttributionReportSyncStatus implements Serializable {
    @DbColumn(value = "id", key = true, autoIncrement = true)
    private Long id;

    /**
    * 商户uid
    */
    @DbColumn("puid")
    private Integer puid;

    /**
    * 店铺ID
    */
    @DbColumn("shop_id")
    private Integer shopId;

    /**
    * 站点
    */
    @DbColumn("marketplace_id")
    private String marketplaceId;
    /**
    * profile_id
    */
    @DbColumn("profile_id")
    private String profileId;
    /**
    * attribution advertiserId
    */
    @DbColumn("advertiser_id")
    private String advertiserId;

    /**
    * 上次同步时间
    */
    @DbColumn("last_sync_time")
    private LocalDateTime lastSyncTime;

    /**
    * 下次同步时间
    */
    @DbColumn("next_sync_time")
    private LocalDateTime nextSyncTime;

    /**
    * 创建时间
    */
    @DbColumn("create_time")
    private LocalDateTime createTime;

    /**
    * 更新时间
    */
    @DbColumn("update_time")
    private LocalDateTime updateTime;
}