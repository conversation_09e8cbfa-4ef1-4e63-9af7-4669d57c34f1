package com.meiyunji.sponsored.service.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.google.protobuf.Int32Value;
import com.meiyunji.sponsored.service.excel.excelTools.ExportFormat;
import com.meiyunji.sponsored.service.excel.excelTools.ExportFormatType;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.Optional;

@Data
public class CpcSpaceVo {

    @ExcelProperty(value = "广告位")
    private String name;

    @ExcelProperty(value = "广告活动的竞价策略")
    private String strategy;

    @ExcelProperty(value = "广告组合")
    private String portfolioName;

    @ExcelProperty(value = "所属广告活动")
    private String advertisingActivities;

    @ExcelProperty(value = "推广类型")
    private String type;

    @ExcelProperty(value = "投放类型")
    private String campaignTargetingType;

    @ExcelProperty(value = "默认调整")
    private String percentage;

    @ExcelProperty(value = "曝光量")
    private Integer impressions;

    @ExcelProperty(value = "点击量")
    private Integer clicks;

    @ExcelProperty(value = "点击率")
    private String ctr;

    @ExcelProperty(value = "订单转化率")
    private String cvr;

    @ExcelProperty(value = "ACoS")
    private String acos;

    @ExcelProperty(value = "ACoTS")
    private String acots;

    @ExcelProperty(value = "RoAS")
    private String roas;

    @ExcelProperty(value = "ASoTS")
    private String asots;

    @ExcelProperty("广告笔单价")
    @ExportFormat(value = ExportFormatType.CURRENCY)
    private String advertisingUnitPrice;

    @ExcelProperty(value = "广告订单数")
    private Integer adOrderNum;

    @ExcelProperty(value = "广告花费")
    @ExportFormat(value = ExportFormatType.CURRENCY)
    private String adCost;

    @ExcelProperty(value = "广告花费占比")
    private String adCostPercentage;

    @ExcelProperty(value = "平均点击费用")
    @ExportFormat(value = ExportFormatType.CURRENCY)
    private String adCostPerClick;

    @ExcelProperty(value = "广告销售额")
    @ExportFormat(value = ExportFormatType.CURRENCY)
    private String adSale;

    @ExcelProperty(value = "广告销售额占比")
    private String adSalePercentage;

    @ExcelProperty(value = "广告订单量占比")
    private String adOrderNumPercentage;

    @ExcelProperty(value = "本广告产品订单量")
    private Integer adSaleNum;

    @ExcelProperty(value = "其他产品广告订单量")
    private Integer adOtherOrderNum;

    @ExcelProperty(value = "本广告产品销售额")
    @ExportFormat(value = ExportFormatType.CURRENCY)
    private String adSales;

    @ExcelProperty(value = "其他产品广告销售额")
    @ExportFormat(value = ExportFormatType.CURRENCY)
    private String adOtherSales;

    @ExcelProperty(value = "广告销量")
    private Integer orderNum;

    @ExcelProperty(value = "广告销量占比")
    private String orderNumPercentage;

    @ExcelProperty(value = "本广告产品销量")
    private Integer adSelfSaleNum;

    @ExcelProperty(value = "其他产品广告销量")
    private Integer adOtherSaleNum;

    @ExcelProperty(value = "企业购广告位竞价调整")
    private String amazonBusinessPercentage;
}

