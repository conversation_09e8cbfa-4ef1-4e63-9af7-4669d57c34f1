package com.meiyunji.sponsored.service.enums;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

public enum SpCategoryBidV3ConvertV5Enum {

     /**
     * asinCategorySameAs，value为categoryId，直接使用
     * asinBrandSameAs，value为brandId，直接使用
     * asinIsPrimeShippingEligible，value为true或false，直接使用
     * asinPriceLessThan，value转换成0-max
     * asinPriceBetween，value转换成min-max，直接使用
     * asinPriceGreaterThan，value转换成min-99999
     * asinReviewRatingLessThan，value转换成0-max
     * asinReviewRatingBetween，value转换成min-max，直接使用
     * asinReviewRatingGreaterThan，value转换成min-5*/

    /**
     * category="${categoryId}" brand="${brand}" rating=${minReviewRating}-${maxReviewRating} price=${minPrice}-${maxPrice} prime-shipping-eligible="${primeShippingEligible}"
     */

    //类目
    asinCategorySameAs("asinCategorySameAs", "category=\"%s\""),
    //品牌
    asinBrandSameAs("asinBrandSameAs", "brand=\"%s\""),
    //配送
    asinIsPrimeShippingEligible("asinIsPrimeShippingEligible", "prime-shipping-eligible=\"%s\""),
    //价格
    asinPriceLessThan("asinPriceLessThan", "price=0-%s"),
    asinPriceBetween("asinPriceBetween", "price=%s"),
    //没有上限则给999999999
    asinPriceGreaterThan("asinPriceGreaterThan", "price=%s-999999999"),
    //星级
    asinReviewRatingLessThan("asinReviewRatingLessThan", "rating=0-%s"),
    asinReviewRatingBetween("asinReviewRatingBetween", "rating=%s"),
    asinReviewRatingGreaterThan("asinReviewRatingGreaterThan", "rating=%s-5");

    private String v3Expression;
    private String v5Expression;

    public static Map<String, SpCategoryBidV3ConvertV5Enum> typeMap = Arrays.stream(SpCategoryBidV3ConvertV5Enum.values())
            .collect((Collectors.groupingBy(SpCategoryBidV3ConvertV5Enum::getV3Expression,
                    Collectors.collectingAndThen(Collectors.toList(), value -> value.get(0)))));

    SpCategoryBidV3ConvertV5Enum(String v3Expression, String v5Expression) {
        this.v3Expression = v3Expression;
        this.v5Expression = v5Expression;
    }

    public String getV3Expression() {
        return v3Expression;
    }

    public String getV5Expression() {
        return v5Expression;
    }
}
