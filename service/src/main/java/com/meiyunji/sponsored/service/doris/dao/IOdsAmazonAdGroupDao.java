package com.meiyunji.sponsored.service.doris.dao;

import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.springjdbc.IDorisBaseDao;
import com.meiyunji.sponsored.service.cpc.vo.AsinLibsParam;
import com.meiyunji.sponsored.service.cpc.vo.KeywordLibsPageParam;
import com.meiyunji.sponsored.service.doris.bo.AdGroupDefaultBidBo;
import com.meiyunji.sponsored.service.doris.po.OdsAmazonAdGroup;

import java.util.List;

/**
 * amazon广告组表(OdsAmazonAdGroup)数据库访问层
 *
 * <AUTHOR>
 * @since 2024-03-26 10:46:17
 */
public interface IOdsAmazonAdGroupDao extends IDorisBaseDao<OdsAmazonAdGroup> {
    /**
     * 根据广告组ids获取默认竞价
     */
    List<AdGroupDefaultBidBo> defaultBidBoListByGroupIds(Integer puid, Integer shopId, List<String> adGroupIds);

    Page<OdsAmazonAdGroup> listPageByByCondition(Integer puid, List<Integer> shopIds, KeywordLibsPageParam param);

    Page<OdsAmazonAdGroup> listPageByByCondition(Integer puid, List<Integer> shopIds, AsinLibsParam param);

}

