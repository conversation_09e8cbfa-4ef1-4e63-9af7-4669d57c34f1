package com.meiyunji.sponsored.service.autoRule.vo;

import com.meiyunji.sponsored.service.cpc.constants.BrandMessageConstants;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;

/**
 * @author: chenzimeng
 * @email: <EMAIL>
 * @date: 2023-07-05  19:17
 */
@Data
public class AdKeywordTargetAutoRuleVo implements Serializable {
    private Long id;
    private Integer puid;
    private Integer shopId;
    private String marketplaceId;
    private String campaignId;
    private String campaignName;
    private String adType;
    private Double biddingValue;
    private String adGroupId;
    private String adGroupName;
    private String shopName;
    private String marketplaceName;
    //投放值，asin，词，类目
    private String keywordText;
    private String currency;
    //投放id，商品，自动，关键词，受众
    private String keywordId;
    //关键词的匹配类型，精准广泛词组
    private String matchType;
    private String matchName;
    private String state;
    private String itemId;
    private String itemName;
    //投放类型，对应前端传值的投放类型，AutoRuleTargetTypeEnum，4种
    private String targetType;
    private String servingStatus;
    private String servingStatusName;
    private String servingStatusDec;
    //投放类型，数据库的投放类型，用于受众投放解析title
    private String detailTargetType;

    //表达式，用于解析以下的类目和受众字段等
    private String resolvedExpression;

    //asin,category,audience,auto,keyword,用于前端解析判断投放的各种情况
    private String type;

    //asin相关
    //asin
    private String asin;
    //标题（受众投放类型）
    private String title;
    //图片url
    private String imgUrl;

    //类目投放相关
    //类目
    private String category;
    //品牌名称
    private String brandName;
    //价格
    private String commodityPriceRange;
    //星级
    private String rating;
    //配送
    private String distribution;

    //受众投放-回溯期
    private String lookback;

}
