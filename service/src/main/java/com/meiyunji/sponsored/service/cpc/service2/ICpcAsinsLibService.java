package com.meiyunji.sponsored.service.cpc.service2;

import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.rpc.asins.PageListAsinsRequest;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdAsinLibMarkupAsin;
import com.meiyunji.sponsored.service.cpc.qo.KeywordLibMarkupAsinQo;
import com.meiyunji.sponsored.service.cpc.qo.KeywordLibUnMarkAsinQo;
import com.meiyunji.sponsored.service.cpc.vo.*;

import java.util.List;

public interface ICpcAsinsLibService {
    Result<AddAsinLibVo> createAsins(Integer puid, Integer uid, List<AsinLibsVo> voList, List<Integer> uidList);

    List<String> getAsinListByPuid(Integer puid);

    Integer getAsinByPuidAndAsinCnt(Integer puid, List<String> asinList);
    /**
     * 获取asin投放详情数据
     * @param param
     * @return
     */
    Result<Page<AsinLibsDetailVo>> getAsinDetail(AsinLibsDetailParam param);

    /**
     * 获取asin否定投放详情数据
     * @param param
     * @return
     */
    Result<Page<AsinLibsDetailVo>> getAsinNeTargetDetail(AsinLibsDetailParam param);

    /**
     * 获取投放详情的汇总数据
     * @param param
     * @return
     */
    Result<KeywordLibsTotalVo> getAsinDetailAggregateData(AsinLibsDetailParam param);

    Result<AsinLibsPageListVo> pageList(PageListAsinsRequest request);

    Result<List<AsinLibsVo>> exportList(PageListAsinsRequest request);

    /**
    * 删除收藏ASIN
    * @param puid
    * @param uid
    * @param asins
    * @return
    */
    Result deleteAsin(int puid, int uid, List<String> asins);

    /**
     * 标记ASIN标签
     * @param puid
     * @param uid
     * @param shopIdList
     * @param qo
     * @return
     */
    Result<List<ErrorMsgVo>> markupAsin(Integer puid, Integer uid, List<Integer> shopIdList, KeywordLibMarkupAsinQo qo);

    /**
     * 广告活动列表
     * @param param
     * @return
     */
    Page<KeywordLibsCampaignListVO> getAsinsDetailCampaignListPage(AsinLibsParam param);

    /**
     * 广告组合列表
     * @param param
     * @return
     */
    Page<KeywordLibsGroupListVO> getAsinsDetailGroupListPage(AsinLibsParam param);

    Result updateRemark(AsinLibsVo vo);

    Page<AmazonAdAsinLibMarkupAsin> getAsinPageList(Integer puid, List<Integer> uidList, List<Integer> shopIdList, AsinLibAsinTagPageListVo req);

    Result unMarkAsin(int puid, List<Integer> uidList, List<Integer> shopIdList, KeywordLibUnMarkAsinQo qo);
}
