package com.meiyunji.sponsored.service.cpc.dao;

import com.meiyunji.sponsored.common.springjdbc.IAdBaseDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdPostReportSyncRecord;

import java.util.Date;
import java.util.List;

public interface IAmazonAdPostReportSyncRecordDao extends IAdBaseDao<AmazonAdPostReportSyncRecord> {
    void upsertPostReportSyncRecords(List<AmazonAdPostReportSyncRecord> postReportSyncRecords);

    List<AmazonAdPostReportSyncRecord> listByUniqueKeyAndExecuteTime(Integer puid, Integer shopId, String postProfileId, Date date, Boolean isInitFinish);

    List<AmazonAdPostReportSyncRecord> listByPuidAndShopId(Integer puid, Integer shopId);

    void updateLastSyncDate(Long id, String metricDate);

    void updateNextExecuteTimeAndInitFinish(Long id, Date nextExecuteTime);
}
