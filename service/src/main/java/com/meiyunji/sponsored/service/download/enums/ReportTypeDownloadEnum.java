package com.meiyunji.sponsored.service.download.enums;

public enum ReportTypeDownloadEnum {
    AD_CAMPAIGN_REPORT("adCampaignReport","广告活动报告"),
    AD_GROUP_REPORT("adGroupReport","广告组报告"),
    AD_PRODUCT_REPORT("adProductReport","广告产品报告"),
    AD_SPACE_REPORT("adSpaceReport","广告位报告"),
    AD_TARGERING_REPORT("adTargeringReport","投放报告"),
    AD_SD_TARGERING_REPORT("sdTargetListReport","投放报告"),
    AD_SEARCH_TERM_REPORT("adSearchTermReport","搜索词报告"),
    AD_PURCHASED_ITEM_REPORT("adPurchasedItemReport","已购买商品报告"),
    AD_CAMPAIGN_MATCHED_TARGET_REPORT("adCampaignMatchedTargetReport","匹配的目标报告"),
    AD_GROSS_AND_INVALID_TRAFFIC_ALL_REPORT("adGrossAndInvalidTrafficAllReport", "总流量与无效流量报告"),
    AMAZON_BUSINESS_REPORT("amazonBusinessReport", "企业购广告位报告"),
    ;
    private String code;
    private String name;

    ReportTypeDownloadEnum (String code,String name){
        this.code = code;
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public String getCode() {
        return code;
    }

    public static String getByValue(String code) {
        for (ReportTypeDownloadEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status.getName();
            }
        }
        return "";
    }
}
