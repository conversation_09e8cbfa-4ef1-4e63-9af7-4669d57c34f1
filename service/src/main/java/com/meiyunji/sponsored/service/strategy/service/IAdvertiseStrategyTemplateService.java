package com.meiyunji.sponsored.service.strategy.service;

import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.service.strategy.po.AdvertiseStrategyTemplate;
import com.meiyunji.sponsored.service.strategy.vo.AdvertiseStrategyTemplateRequest;
import com.meiyunji.sponsored.service.vo.DisabledTemplateDto;

import java.util.List;

public interface IAdvertiseStrategyTemplateService {

    /**
     * 分页查询
     *
     * @param puid
     * @param strategyTemplateVo
     * @return
     */
    Result<Page<AdvertiseStrategyTemplate>> pageList(int puid, AdvertiseStrategyTemplateRequest strategyTemplateVo);

    /**
     * 新增
     *
     * @param puid
     * @param template
     * @param loginIp
     * @return
     */
    Result<Long> insertTemplate(Integer puid, AdvertiseStrategyTemplate template, String loginIp, String traceId);

    /**
     * 修改
     *
     * @param puid
     * @param template
     * @param loginIp
     * @return
     */
    Result<Long> updateTemplate(Integer puid, AdvertiseStrategyTemplate template, String loginIp, String traceId);

    /**
     * 删除模板
     *
     * @param puid
     * @param id
     * @param uid
     * @param loginIp
     * @return
     */
    Result<String> deleteTemplate(Integer puid, Long id, Integer uid, String loginIp, String traceId);

    /**
     * 编辑模板
     *
     * @param puid
     * @param id
     * @param marketplaceId
     * @return
     */
    Result<AdvertiseStrategyTemplate> getTemplateById(Integer puid, Long id, String marketplaceId);

    /**
     * 复制模板
     *
     * @param puid
     * @param id
     * @return
     */
    Result<AdvertiseStrategyTemplate> copyTemplate(Integer puid, Long id);

    /**
     * 模板列表查询
     *
     * @param puid
     * @param templateName
     * @return
     */
    Result<List<AdvertiseStrategyTemplate>> getList(Integer puid, String templateName,String itemType);

    /**
     * 转移模板列表查询
     *
     * @param puid
     * @param templateName
     * @param shopId
     * @return
     */
    Result<List<AdvertiseStrategyTemplate>> transferTemplate(Integer puid, String templateName, Integer shopId);

    /**
     * 模板导出
     *
     * @param puid
     * @param param
     * @return
     */
    Page<AdvertiseStrategyTemplate> excelExport(int puid, AdvertiseStrategyTemplateRequest strategyTemplateVo);


    void updateTemplateStatus(int puid, List<AdvertiseStrategyTemplate> templateList, String status, Long updateUid, Boolean isAuto);

    void disableAllTemplateStatus(Integer puid, Integer shopId, Boolean isAuto);

    void retryDisableTemplate(Integer puid, List<AdvertiseStrategyTemplate> templateList);
}