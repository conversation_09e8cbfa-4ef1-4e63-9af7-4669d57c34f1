package com.meiyunji.sponsored.service.strategy.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Objects;

@Data
public class MarketplaceVo implements Serializable {

    private String marketplaceId;

    private String marketplaceName;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        MarketplaceVo that = (MarketplaceVo) o;
        return Objects.equals(marketplaceId, that.marketplaceId) && Objects.equals(marketplaceName, that.marketplaceName);
    }

    @Override
    public int hashCode() {
        return Objects.hash(marketplaceId, marketplaceName);
    }
}
