package com.meiyunji.sponsored.service.cpc.service2.sp.impl;

import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.common.util.PageUtil;
import com.meiyunji.sponsored.common.util.ResultUtil;
import com.meiyunji.sponsored.service.cpc.dao.ICpcQueryKeywordReportDao;
import com.meiyunji.sponsored.service.cpc.dao.ICpcQueryTargetingReportDao;
import com.meiyunji.sponsored.service.cpc.po.CpcQueryKeywordReport;
import com.meiyunji.sponsored.service.cpc.po.CpcQueryTargetingReport;
import com.meiyunji.sponsored.service.cpc.service2.impl.CpcShopDataService;
import com.meiyunji.sponsored.service.cpc.service2.sp.ICpcAdQueryWordService;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.vo.*;
import com.meiyunji.sponsored.service.dataWarehouse.statsDo.ShopSaleDto;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Created by xp on 2021/4/22.
 */
@Service
public class CpcAdQueryWordServiceImpl implements ICpcAdQueryWordService {

    @Autowired
    private ICpcQueryKeywordReportDao cpcQueryKeywordReportDao;
    @Autowired
    private ICpcQueryTargetingReportDao cpcQueryTargetingReportDao;
    @Autowired
    private CpcCommService cpcCommService;
    @Autowired
    private CpcShopDataService cpcShopDataService;

    @Override
    public Result<Page<QueryByKeywordPageVo>> pageListByKeyword(Integer puid, QueryWordPageParam param) {
        List<CpcQueryKeywordReport> rows = cpcQueryKeywordReportDao.listByPageParam(puid, param);

        Page<QueryByKeywordPageVo> voPage = new Page<>();
        voPage.setPageNo(param.getPageNo());
        voPage.setPageSize(param.getPageSize());

        if (CollectionUtils.isNotEmpty(rows)) {
            // 取店铺销售额
            String start = LocalDate.parse(param.getStartDate(), DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD))
                    .format(DateTimeFormatter.ofPattern(DateUtil.PATTERN));

            String end = LocalDate.parse(param.getEndDate(), DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD))
                    .format(DateTimeFormatter.ofPattern(DateUtil.PATTERN));
            ShopSaleDto shopSaleDto = cpcShopDataService.getShopSaleData(param.getShopId(), start, end);

            List<QueryByKeywordPageVo> voList = new ArrayList<>(rows.size());
            voPage.setRows(voList);
            QueryByKeywordPageVo vo;
            for (CpcQueryKeywordReport report : rows) {
                vo = new QueryByKeywordPageVo();
                voList.add(vo);

                convertPoToPageVo(report, vo);

                // 填充报告数据
                fillReportData(vo, report, shopSaleDto);
            }

            // 是否需要按广告表现指标排序, 需要的要取满足过滤条件的所有活动，排序后在用程序分页；
            boolean isSorted = StringUtils.isNotBlank(param.getOrderField()) && Constants.isADperformanceOrderField(param.getOrderField());
            if (isSorted) {
                voList = PageUtil.sort(voList, param.getOrderField(), param.getOrderType());
            }
            PageUtil.getPage(voPage, voList);
        }

        return ResultUtil.returnSucc(voPage);
    }

    @Override
    public Result showKeywordPerformance(int puid, AdPerformanceParam param) {
        if (param.getShopId() == null
                || StringUtils.isBlank(param.getKeywordId())
                || StringUtils.isBlank(param.getQuery())) {
            return ResultUtil.returnErr("请求参数错误");
        }

        // 拼装返回的数据VO
        AdPerformanceVo adPerformanceVo = new AdPerformanceVo();
        adPerformanceVo.setShopId(param.getShopId());
        adPerformanceVo.setKeywordId(param.getKeywordId());
        adPerformanceVo.setQuery(param.getQuery());

        // 初始化每天数据，应前端要求保证日期是连续的
        Map<String, CpcCommPageVo> map = new LinkedHashMap<>();
        adPerformanceVo.setMap(map);
        LocalDate startLocalDate = LocalDate.parse(param.getStartDate(), DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD));
        LocalDate endLocalDate = LocalDate.parse(param.getEndDate(), DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD));
        while (startLocalDate.equals(endLocalDate) || startLocalDate.isBefore(endLocalDate)) {
            map.put(startLocalDate.format(DateTimeFormatter.ofPattern(DateUtil.PATTERN)), new CpcCommPageVo());
            startLocalDate = startLocalDate.plusDays(1);
        }

        List<CpcQueryKeywordReport> reports = cpcQueryKeywordReportDao.listReports(puid, param.getShopId(), param.getStartDate(),
                    param.getEndDate(), param.getKeywordId(), param.getQuery());

        if (CollectionUtils.isNotEmpty(reports)) {
            // 取店铺销售额
            String start = LocalDate.parse(param.getStartDate(), DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD))
                    .format(DateTimeFormatter.ofPattern(DateUtil.PATTERN));

            String end = LocalDate.parse(param.getEndDate(), DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD))
                    .format(DateTimeFormatter.ofPattern(DateUtil.PATTERN));
            ShopSaleDto shopSaleDto = cpcShopDataService.getShopSaleData(param.getShopId(), start, end);

            Map<String, CpcCommPageVo> resultMap = reports.stream().collect(Collectors.toMap(
                    e -> LocalDate.parse(e.getCountDate(), DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD)).format(DateTimeFormatter.ofPattern(DateUtil.PATTERN)),
                    e -> {
                        CpcCommPageVo campaignPageVo = new CpcCommPageVo();

                        // 填充报告数据
                        cpcCommService.fillReportDataIntoPageVo(campaignPageVo, e, shopSaleDto);
                        return campaignPageVo;

                    }, (p1, p2) -> p1));

            adPerformanceVo.getMap().putAll(resultMap);
        }

        return ResultUtil.returnSucc(adPerformanceVo);
    }

    @Override
    public Result<Page<QueryByTargetPageVo>> pageListByTarget(Integer puid, QueryWordPageParam param) {
        List<CpcQueryTargetingReport> reportList = cpcQueryTargetingReportDao.listByPageParam(puid, param);
        Page<QueryByTargetPageVo> voPage = new Page<>();
        voPage.setPageNo(param.getPageNo());
        voPage.setPageSize(param.getPageSize());

        if (CollectionUtils.isNotEmpty(reportList)) {
            // 取店铺销售额
            String start = LocalDate.parse(param.getStartDate(), DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD))
                    .format(DateTimeFormatter.ofPattern(DateUtil.PATTERN));

            String end = LocalDate.parse(param.getEndDate(), DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD))
                    .format(DateTimeFormatter.ofPattern(DateUtil.PATTERN));
            ShopSaleDto shopSaleDto = cpcShopDataService.getShopSaleData(param.getShopId(), start, end);

            List<QueryByTargetPageVo> voList = new ArrayList<>(reportList.size());
            voPage.setRows(voList);
            QueryByTargetPageVo vo;
            for (CpcQueryTargetingReport report : reportList) {
                vo = new QueryByTargetPageVo();
                voList.add(vo);
                convertPoToPageVo(report, vo);

                // 填充报告数据
                fillReportData(vo, report, shopSaleDto);
            }

            // 是否需要按广告表现指标排序, 需要的要取满足过滤条件的所有活动，排序后在用程序分页；
            boolean isSorted = StringUtils.isNotBlank(param.getOrderField()) && Constants.isADperformanceOrderField(param.getOrderField());
            if (isSorted) {
                voList = PageUtil.sort(voList, param.getOrderField(), param.getOrderType());
            }
            PageUtil.getPage(voPage, voList);
        }

        return ResultUtil.returnSucc(voPage);
    }

    @Override
    public Result<AdPerformanceVo> showTargetPerformance(int puid, AdPerformanceParam param) {
        if (param.getShopId() == null
                || StringUtils.isBlank(param.getTargetId())
                || StringUtils.isBlank(param.getQuery())) {
            return ResultUtil.returnErr("请求参数错误");
        }

        // 拼装返回的数据VO
        AdPerformanceVo adPerformanceVo = new AdPerformanceVo();
        adPerformanceVo.setShopId(param.getShopId());
        adPerformanceVo.setTargetId(param.getTargetId());
        adPerformanceVo.setQuery(param.getQuery());

        // 初始化每天数据，应前端要求保证日期是连续的
        Map<String, CpcCommPageVo> map = new LinkedHashMap<>();
        adPerformanceVo.setMap(map);
        LocalDate startLocalDate = LocalDate.parse(param.getStartDate(), DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD));
        LocalDate endLocalDate = LocalDate.parse(param.getEndDate(), DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD));
        while (startLocalDate.equals(endLocalDate) || startLocalDate.isBefore(endLocalDate)) {
            map.put(startLocalDate.format(DateTimeFormatter.ofPattern(DateUtil.PATTERN)), new CpcCommPageVo());
            startLocalDate = startLocalDate.plusDays(1);
        }

        List<CpcQueryTargetingReport> reports = cpcQueryTargetingReportDao.listReports(puid, param.getShopId(), param.getStartDate(),
                    param.getEndDate(), param.getTargetId(), param.getQuery());
        if (CollectionUtils.isNotEmpty(reports)) {
            // 取店铺销售额
            String start = LocalDate.parse(param.getStartDate(), DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD))
                    .format(DateTimeFormatter.ofPattern(DateUtil.PATTERN));

            String end = LocalDate.parse(param.getEndDate(), DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD))
                    .format(DateTimeFormatter.ofPattern(DateUtil.PATTERN));
            ShopSaleDto shopSaleDto = cpcShopDataService.getShopSaleData(param.getShopId(), start, end);

            Map<String, CpcCommPageVo> resultMap = reports.stream().collect(Collectors.toMap(
                    e -> LocalDate.parse(e.getCountDate(), DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD)).format(DateTimeFormatter.ofPattern(DateUtil.PATTERN)),
                    e -> {
                        CpcCommPageVo campaignPageVo = new CpcCommPageVo();

                        // 填充报告数据
                        cpcCommService.fillReportDataIntoPageVo(campaignPageVo, e, shopSaleDto);
                        return campaignPageVo;

                    }, (p1, p2) -> p1));

            adPerformanceVo.getMap().putAll(resultMap);
        }

        return ResultUtil.returnSucc(adPerformanceVo);
    }

    private void convertPoToPageVo(CpcQueryKeywordReport report, QueryByKeywordPageVo vo) {
        vo.setId(report.getId());
        vo.setShopId(report.getShopId());
        vo.setQuery(report.getQuery());
        vo.setKeyword(report.getKeywordText());
        vo.setKeywordId(report.getKeywordId());
        vo.setMatchType(report.getMatchType());
    }

    private void fillReportData(QueryByKeywordPageVo vo, CpcQueryKeywordReport report, ShopSaleDto shopSaleDto) {
        if (report != null) {
            ReportMetadata reportMetadata = new ReportMetadata();
            reportMetadata.setImpressions(report.getImpressions());
            reportMetadata.setClicks(report.getClicks());
            reportMetadata.setOrderNum(report.getOrderNum());
            reportMetadata.setAdOrderNum(report.getOrderNum());
            reportMetadata.setSaleNum(report.getSaleNum());
            reportMetadata.setAdSaleNum(report.getAdSaleNum());
            reportMetadata.setCost(report.getCost());
            reportMetadata.setCostUsd(report.getCostUsd());
            reportMetadata.setCostRmb(report.getCostRmb());
            reportMetadata.setSales(report.getTotalSales());
            reportMetadata.setSalesUsd(report.getTotalSalesUsd());
            reportMetadata.setSalesRmb(report.getTotalSalesRmb());
            reportMetadata.setAdSales(report.getAdSales());
            reportMetadata.setAdSalesUsd(report.getAdSalesUsd());
            reportMetadata.setAdSalesRmb(report.getAdSalesRmb());

            if (shopSaleDto != null) {
                reportMetadata.setShopSales(shopSaleDto.getSumRange());
            }

            cpcCommService.completeCpcCommPageVo(vo, reportMetadata);
        }
    }

    private void convertPoToPageVo(CpcQueryTargetingReport report, QueryByTargetPageVo vo) {
        vo.setId(report.getId());
        vo.setShopId(report.getShopId());
        vo.setQuery(report.getQuery());
        vo.setTargeting(report.getTargetingExpression());
        vo.setTargetId(report.getTargetId());
        vo.setIsAsin(report.getIsAsin());
        vo.setImgUrl(report.getMainImage());
        vo.setTitle(report.getTitle());
    }

    private void fillReportData(QueryByTargetPageVo vo, CpcQueryTargetingReport report, ShopSaleDto shopSaleDto) {
        if (report != null) {
            ReportMetadata reportMetadata = new ReportMetadata();
            reportMetadata.setImpressions(report.getImpressions());
            reportMetadata.setClicks(report.getClicks());
            reportMetadata.setOrderNum(report.getOrderNum());
            reportMetadata.setAdOrderNum(report.getOrderNum());
            reportMetadata.setSaleNum(report.getSaleNum());
            reportMetadata.setAdSaleNum(report.getAdSaleNum());
            reportMetadata.setCost(report.getCost());
            reportMetadata.setCostUsd(report.getCostUsd());
            reportMetadata.setCostRmb(report.getCostRmb());
            reportMetadata.setSales(report.getTotalSales());
            reportMetadata.setSalesUsd(report.getTotalSalesUsd());
            reportMetadata.setSalesRmb(report.getTotalSalesRmb());
            reportMetadata.setAdSales(report.getAdSales());
            reportMetadata.setAdSalesUsd(report.getAdSalesUsd());
            reportMetadata.setAdSalesRmb(report.getAdSalesRmb());

            if (shopSaleDto != null) {
                reportMetadata.setShopSales(shopSaleDto.getSumRange());
            }

            cpcCommService.completeCpcCommPageVo(vo, reportMetadata);
        }
    }
}
