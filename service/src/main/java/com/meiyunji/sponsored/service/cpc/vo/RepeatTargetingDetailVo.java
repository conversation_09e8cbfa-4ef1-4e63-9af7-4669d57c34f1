package com.meiyunji.sponsored.service.cpc.vo;

import com.meiyunji.sponsored.service.cpc.po.AdTag;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RepeatTargetingDetailVo {

    private Long id;

    private Integer puid;

    private Integer uid;

    private String keywordText;

    private String keywordId;

    private Integer shopId;

    private String shopName;

    private String matchType;

    private String type;

    private BigDecimal targetValue = BigDecimal.ZERO;

    /**
     * 标签
     */
    private List<AdTag> adTags;

    /**
     * 有效状态
     */
    private String state;

    /**
     * 服务状态
     */
    private String status;

    /**
     * 广告组合Id
     */
    private String portfolioId;

    /**
     * 广告组合名称
     */
    private String portfolioName;

    /**
     * 是否隐藏
     */
    private Integer isHidden;

    /**
     * 广告活动Id
     */
    private String campaignId;

    /**
     * 广告活动名称
     */
    private String campaignName;

    /**
     * 每日预算
     */
    private BigDecimal campaignBid;

    private Boolean adCampaignIsArchived;

    /**
     * 广告组Id
     */
    private String adGroupId;

    /**
     * 广告组名称
     */
    private String adGroupName;

    private Boolean adGroupIsArchived;

    /**
     * 竞价
     */
    private String bid;

    /**
     * 建议竞价
     */
    private String suggestBid;

    private String rangeStart;

    private String rangeEnd;

    private String groupType;

    /**
     * 广告产品
     */
    private List<RepeatTargetingProductVo> products;

    /**
     * 搜索词排名
     */
    private Integer searchFrequencyRank;

    /**
     * 排名周变化率
     */
    private BigDecimal weekRatio;

    /**
     * 广告曝光量
     */
    private Integer impressions = 0;

    /**
     * 广告点击量
     */
    private Integer clicks = 0;

    /**
     * 广告点击率 点击量/曝光量 * 100%
     */
    private BigDecimal clickRate = BigDecimal.ZERO;

    /**
     * 广告订单量
     */
    private Integer saleNum = 0;

    /**
     * 广告转化率 广告订单量/点击量 * 100%
     */
    private BigDecimal salesConversionRate = BigDecimal.ZERO;

    /**
     * 广告销售额
     */
    private BigDecimal totalSales = BigDecimal.ZERO;

    /**
     * 广告花费
     */
    private BigDecimal cost = BigDecimal.ZERO;

    /**
     * CPC 广告花费/广告点击量(非百分比数据)
     */
    private BigDecimal cpc = BigDecimal.ZERO;

    /**
     * CPA 广告花费/广告订单量
     */
    private BigDecimal cpa = BigDecimal.ZERO;

    /**
     * ACOS 广告花费/销售额*100%
     */
    private BigDecimal acos = BigDecimal.ZERO;

    /**
     * ROAS 销售额/广告费(非百分比数据)
     */
    private BigDecimal roas = BigDecimal.ZERO;

    /**
     * 广告曝光量环比值
     */
    private Integer compareImpressions = 0;

    /**
     * 广告点击量环比值
     */
    private Integer compareClicks = 0;

    /**
     * 广告点击率环比值 点击量/曝光量 * 100%
     */
    private BigDecimal compareClickRate = BigDecimal.ZERO;

    /**
     * 广告订单量环比值
     */
    private Integer compareSaleNum = 0;

    /**
     * 广告转化率环比值 广告订单量/点击量 * 100%
     */
    private BigDecimal compareSalesConversionRate = BigDecimal.ZERO;

    /**
     * 广告销售额环比值
     */
    private BigDecimal compareTotalSales = BigDecimal.ZERO;

    /**
     * 广告花费环比值
     */
    private BigDecimal compareCost = BigDecimal.ZERO;

    /**
     * CPC环比值 广告花费/广告点击量(非百分比数据)
     */
    private BigDecimal compareCpc = BigDecimal.ZERO;

    /**
     * CPA环比值 广告花费/广告订单量
     */
    private BigDecimal compareCpa = BigDecimal.ZERO;

    /**
     * ACOS环比值 广告花费/销售额*100%
     */
    private BigDecimal compareAcos = BigDecimal.ZERO;

    /**
     * ROAS环比值 销售额/广告费(非百分比数据)
     */
    private BigDecimal compareRoas = BigDecimal.ZERO;

    /**
     * 用于指标值排序
     */
    private BigDecimal orderField = BigDecimal.ZERO;

    /**
     * 广告曝光量环比值
     */
    private String diffImpressions;

    /**
     * 广告点击量环比值
     */
    private String diffClicks;

    /**
     * 广告点击率环比值 点击量/曝光量 * 100%
     */
    private String diffClickRate;

    /**
     * 广告订单量环比值
     */
    private String diffSaleNum;

    /**
     * 广告转化率环比值 广告订单量/点击量 * 100%
     */
    private String diffSalesConversionRate;

    /**
     * 广告销售额环比值
     */
    private String diffTotalSales;

    /**
     * 广告花费环比值
     */
    private String diffCost;

    /**
     * CPC环比值 广告花费/广告点击量(非百分比数据)
     */
    private String diffCpc;

    /**
     * CPA环比值 广告花费/广告订单量
     */
    private String diffCpa;

    /**
     * ACOS环比值 广告花费/销售额*100%
     */
    private String diffAcos;

    /**
     * ROAS环比值 销售额/广告费(非百分比数据)
     */
    private String diffRoas;

    private String costType;

    private String adFormat;
}
