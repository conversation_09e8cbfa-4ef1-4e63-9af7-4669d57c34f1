package com.meiyunji.sponsored.service.post.request;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class GetPostDetailRequest implements Serializable {
    private static final long serialVersionUID = 123456L;

    @NotNull(message = "id不能为空")
    private Integer id;

    private Integer puid;

    private Integer uid;

    @NotNull(message = "shopId不能为空")
    private Integer shopId;

    @NotNull(message = "postProfileId不能为空")
    private String postProfileId;

    @NotNull(message = "postId不能为空")
    private String postId;

}
