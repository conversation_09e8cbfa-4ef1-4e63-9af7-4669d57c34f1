package com.meiyunji.sponsored.service.cpc.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel
public class AdProductReportVo {

    @ApiModelProperty("曝光量:广告被展示的次数")
    private Integer impressions;

    @ApiModelProperty("点击量:广告被点击的次数")
    private Integer clicks;

    @ApiModelProperty("点击率（CTR）:广告展示时被买家点击的比率。该值由点击量除以曝光量计算得出")
    private BigDecimal ctr;

    @ApiModelProperty("订单转化率:此值由总广告订单数除以点击量计算得出")
    private BigDecimal cvr;

    @ApiModelProperty(" ACoS是在指定时间范围内，广告花费与广告销售额的百分比，此值由广告花费除以广告销售额计算得出")
    private BigDecimal acos;

    @ApiModelProperty(" ROAS=广告销售额/广告费")
    private BigDecimal roas;

    @ApiModelProperty("ACoTS是在指定时间范围内，广告总花费与总销售额的百分比，此值由广告总花费除以总销售额计算得出。")
    private BigDecimal acots;

    @ApiModelProperty("ASoTS是在指定时间范围内，广告销售额与原价销售额的百分比，此值由广告销售额除以原价销售额计算得出。")
    private BigDecimal asots;

    @ApiModelProperty("广告订单数:是指通过点击广告售出产品的订单数量")
    private Integer adOrderNum;

    @ApiModelProperty("广告花费:广告活动的广告总花费")
    private BigDecimal adCost;

    @ApiModelProperty("平均点击费：广告的每次点击费用")
    private BigDecimal adCostPerClick;

    @ApiModelProperty("广告销售额:广告销售额是指通过广告售出产品的销售额")
    private BigDecimal adSale;
}
