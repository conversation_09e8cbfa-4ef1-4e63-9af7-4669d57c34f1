package com.meiyunji.sponsored.service.post.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class GetAsinsResponse implements Serializable {
    private static final long serialVersionUID = 123456L;

    @JsonProperty("code")
    private Integer code;

    @JsonProperty("msg")
    private String msg;

    @JsonProperty("data")
    private List<GetSuggestAsinResponse.SuggestAsinVo> data;
}
