package com.meiyunji.sponsored.service.enums;

import com.meiyunji.sponsored.service.vo.AdvertisingCampaignVo;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @see AdvertisingCampaignVo
 * 注意注意！！！ poParamKey对应着主项目的AdvertisingCampaignExportFieldEnum，修改了这个类，主项目的也要对应修改；
 */

public enum AdvertisingKeywordExportFieldEnum {
    STATE("enable", "有效状态", false),
    CAMPAIGN_STATE("campaignState", "广告活动状态", false),
    GROUP_NAME("groupName", "所属广告组", false),
    SUGGEST_BID("suggestBid", "建议竞价", true),
    SUGGEST_BID_RANGE("suggestBidRange", "建议竞价范围", true),
    WEEK_RATIO("weekRatio", "排名周变化率", false),
    IMPRESSIONS("impressions", "广告曝光量", false),
    CPA("cpa", "CPA", true),
    CVR("cvr", "广告转化率", false),
    ACOTS("acots", "ACoTS", false),
    AD_ORDER_NUM("adOrderNum", "广告订单量", false),
    OTHER_AD_ORDER_NUM("otherAdOrderNum", "其他产品广告订单量", false),
    AD_SELF_SALE("adSelfSale", "本广告产品销售额", true),
    AD_SALE_NUM_PERCENTAGE("adSaleNumPercentage", "广告销量占比", false),
    KEYWORD_TEXT("keywordText", "关键词", false),
    KEYWORD_TEXT_CN("keywordTextCn", "关键词翻译", false),
    TYPE("type", "推广类型", false),
    MATCH_TYPE("matchType", "匹配类型", false),
    TARGETING_TYPE("targetingType", "投放类型", false),
    CAMPAIGN_NAME("campaignName", "所属广告活动", false),
    BID("bid", "竞价", true),
    AD_COST("adCost", "广告花费", true),
    TOP_IMPRESSION_SHARE("topImpressionShare", "搜索结果首页首位IS", false),
    AD_COST_PER_CLICK("adCostPerClick", "CPC", true),
    ACOS("acos", "ACoS", false),
    ASOTS("asots", "ASoTS", false),
    AD_ORDER_NUM_PERCENTAGE("adOrderNumPercentage", "广告订单量占比", false),
    AD_SALE("adSale", "广告销售额", true),
    AD_OTHER_SALES("adOtherSales", "其他产品广告销售额", true),
    AD_SELF_SALE_NUM("adSelfSaleNum", "本广告产品销量", false),
    AD_TAGS("adTags", "标签", false),
    PORTFOLIO_NAME("portfolioName", "广告组合", false),
    SEARCH_FREQUENCY_RANK("searchFrequencyRank", "ABA搜索词排名", false),
    AD_COST_PERCENTAGE("adCostPercentage", "广告花费占比", false),
    CLICKS("clicks", "广告点击量", false),
    CTR("ctr", "广告点击率", false),
    ROAS("roas", "ROAS", false),
    ADVERTISING_UNIT_PRICE("advertisingUnitPrice", "广告笔单价", true),
    SELF_AD_ORDER_NUM("selfAdOrderNum", "本广告产品订单量", false),
    AD_SALE_PERCENTAGE("adSalePercentage", "广告销售额占比", false),
    AD_SALE_NUM("adSaleNum", "广告销量", false),
    AD_OTHER_SALE_NUM("adOtherSaleNum", "其他产品广告销量", false),
    VIEW_IMPRESSIONS("viewImpressions", "可见展示次数", false),
    ORDERS_NEW_TO_BRAND_PERCENTAGE_FTD("ordersNewToBrandPercentageFTD", "“品牌新买家”订单转化率", false),
    ORDERS_NEW_TO_BRAND_FTD("ordersNewToBrandFTD", "“品牌新买家”订单量", false),
    ORDER_RAT_ENEW_TO_BRAND_FTD("orderRateNewToBrandFTD", "“品牌新买家”订单百分比", false),
    SALES_NEW_TO_BRAND_FTD("salesNewToBrandFTD", "“品牌新买家”销售额", true),
    SALES_RATE_NEW_TO_BRAND_FTD("salesRateNewToBrandFTD", "“品牌新买家”销售额百分比", false),
    VIDEO_5_SECOND_VIEWS("video5SecondViews", "5秒观看次数", false),
    VIDEO_MIDPOINT_VIEWS("videoMidpointViews", "视频播至1/2次数", false),
    VIDEO_UNMUTES("videoUnmutes", "视频取消静音", false),
    BRANDED_SEARCHES("brandedSearches", "品牌搜索次数", false),
    VIDEO_5_SECOND_VIEW_RATE("video5SecondViewRate", "5秒观看率", false),
    VIDEO_THIRD_QUARTILE_VIEWS("videoThirdQuartileViews", "视频播至3/4次数", false),
    VIEWABILITY_RATE("viewabilityRate", "观看率", false),
    VIDEO_FIRST_QUARTILE_VIEWS("videoFirstQuartileViews", "视频播至1/4次数", false),
    VIDEO_COMPLETE_VIEWS("videoCompleteViews", "视频完整播放次数", false),
    VIEW_CLICK_THROUGH_RATE("viewClickThroughRate", "观看点击率", false),
    AD_STRATEGY_TAG("adStrategyTag", "广告策略类型标签", false),
    ;

    // Static variable to hold the list of poParamKey values
    private static final List<String> PO_PARAM_KEY_LIST = Collections.unmodifiableList(
        Arrays.stream(values())
            .map(AdvertisingKeywordExportFieldEnum::getPoParamKey)
            .collect(Collectors.toList())
    );
    /**
     * 后端po属性名
     */
    private final String poParamKey;
    private final String tableColName;
    private final boolean currencyStyle;

    AdvertisingKeywordExportFieldEnum(String poParamKey, String tableColName, boolean currencyStyle) {
        this.poParamKey = poParamKey;
        this.tableColName = tableColName;
        this.currencyStyle = currencyStyle;
    }

    // Method to get the list of poParamKey values
    public static List<String> getPoParamKeyList() {
        return PO_PARAM_KEY_LIST;
    }

    // Static method to get enum by poParamKey, returns null if not found
    public static AdvertisingKeywordExportFieldEnum fromPoParamKey(String key) {
        for (AdvertisingKeywordExportFieldEnum value : values()) {
            if (value.poParamKey.equals(key)) {
                return value;
            }
        }
        return null; // Return null if no match is found
    }

    public String getPoParamKey() {
        return poParamKey;
    }

    public String getTableColName() {
        return tableColName;
    }

    public boolean getCurrencyStyle() {
        return currencyStyle;
    }

}
