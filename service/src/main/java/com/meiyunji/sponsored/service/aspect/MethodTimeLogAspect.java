package com.meiyunji.sponsored.service.aspect;

import com.meiyunji.sponsored.service.annotation.MethodTimeLog;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;

/**
 * @author: sunlinfeng
 * @email: <EMAIL>
 * @date: 2023-11-22  11:04
 */

@Slf4j
@Component
@Aspect
public class MethodTimeLogAspect {

    //定义注解切点
    @Pointcut("@annotation(com.meiyunji.sponsored.service.annotation.MethodTimeLog)")
    public void methodTimeLogPoint() {
    }

    //环绕增强
    @Around("methodTimeLogPoint()")
    public Object doAround(ProceedingJoinPoint joinPoint) throws Throwable {
        MethodSignature pointSignature = (MethodSignature) joinPoint.getSignature();
        Method method = pointSignature.getMethod();
        MethodTimeLog annotation = method.getAnnotation(MethodTimeLog.class);
        String methodName = StringUtils.isBlank(annotation.value()) ? method.getDeclaringClass().getName() + "#" + method.getName() : annotation.value();
        Object[] args = joinPoint.getArgs();
        String s = "";
        StringBuilder sb = new StringBuilder();
        if (annotation.showParam()) {
            String[] parameterNames = pointSignature.getParameterNames();
            for (int i = 0; i < parameterNames.length; i++) {
                if (i > 0) {
                    sb.append(", ");
                }
                sb.append(parameterNames[i]);
                sb.append(": ");
                sb.append(args[i]);
            }
            if (parameterNames.length > 0) {
                s = String.format(", 方法入参[%s]", sb.toString());
            }
        }

        if (StringUtils.isNotBlank(annotation.extra())) {
            s += ", " + annotation.extra();
        }

        log.info("[{}]方法开始执行{}", methodName, s);
        long start = System.currentTimeMillis();
        //默认执行方法
        Object result = joinPoint.proceed(args);

        if (annotation.showParam()) {
            log.info("[{}]方法执行结束, 方法出参: {}, {}耗时: {}ms ",
                    methodName,
                    result,
                    StringUtils.isNotBlank(annotation.extra() + ", ") ? annotation.extra() : "",
                    System.currentTimeMillis() - start);
        } else {
            log.info("[{}]方法执行结束, {}耗时: {}ms ",
                    methodName,
                    StringUtils.isNotBlank(annotation.extra() + ", ") ? annotation.extra() : "",
                    System.currentTimeMillis() - start);
        }

        return result;
    }

}
