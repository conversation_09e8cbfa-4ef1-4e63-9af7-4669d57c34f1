package com.meiyunji.sponsored.service.enums;

/**
 * 广告标签类型
 */
public enum AdMarkupTargetTypeEnum {


    KEYWORD("keyword","搜索词投放"),

    TARGET("target","商品投放");


    private String type;

    private String name;


    AdMarkupTargetTypeEnum(String type, String name) {
        this.type = type;
        this.name = name;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}

