package com.meiyunji.sponsored.service.strategy.po;

import com.meiyunji.sponsored.common.springjdbc.DbColumn;
import com.meiyunji.sponsored.common.springjdbc.DbTable;
import com.meiyunji.sponsored.common.util.JSONUtil;
import com.meiyunji.sponsored.service.strategy.vo.OriginValueVo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
    * 广告策略调度表
    */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@DbTable(value = "t_advertise_strategy_schedule")
public class AdvertiseStrategySchedule implements Serializable {

    @DbColumn(value = "id")
    private Long id;

    /**
     * puid
     */
    @DbColumn(value = "puid")
    private Integer puid;

    /**
     * 店铺ID
     */
    @DbColumn(value = "shop_id")
    private Integer shopId;

    /**
     * 站点ID
     */
    @DbColumn(value = "marketplace_id")
    private String marketplaceId;

    /**
    * 广告类型:SP,SB，SD
    */
    @DbColumn(value = "ad_type")
    private String adType;

    /**
    * 对象类型：CAMPAIGN->活动 CAMPAIGN_PLACEMENT->广告位，TARGET-> 投放
    */
    @DbColumn(value = "item_type")
    private String itemType;

    /**
    * 周期类型: DAILY->每日，WEEKLY->每周
    */
    @DbColumn(value = "type")
    private String type;

    /**
    * 策略状态ID
    */
    @DbColumn(value = "task_id")
    private Long taskId;

    /**
     * 具体的时间
     */
    @DbColumn(value = "count_date")
    private String countDate;

    /**
    * 1-7表示一周
    */
    @DbColumn(value = "day")
    private Integer day;

    /**
    * 开始时间 小时
    */
    @DbColumn(value = "start")
    private Integer start;

    /**
    * 结束时间  小时
    */
    @DbColumn(value = "end")
    private Integer end;

    /**
    * 新值
    */
    @DbColumn(value = "new_value")
    private String newValue;

    /**
    * 旧值
    */
    @DbColumn(value = "origin_value")
    private String originValue;

    /**
     * 还原值
     */
    @DbColumn(value = "return_value")
    private String returnValue;

    /**
    * 创建时间
    */
    @DbColumn(value = "create_at")
    private LocalDateTime createAt;

    /**
    * 更新时间
    */
    @DbColumn(value = "last_update_at")
    private LocalDateTime lastUpdateAt;

    @DbColumn(value = "campaign_id")
    private String campaignId;

    @DbColumn(value = "ad_group_id")
    private String adGroupId;

    /**
     * 操作对象ID
     */
    @DbColumn(value = "item_id")
    private String itemId;

    /**
     * 投放类型(autoTarget:自动投放,keywordTarget:关键词投放,productTarget:商品投放,audienceTarget:受众投放)
     */
    @DbColumn(value = "target_type")
    private String targetType;

    /**
     * 投放名称
     */
    @DbColumn(value = "target_name")
    private String targetName;

    /**
     * 预算模板子类型
     */
    @DbColumn(value = "children_item_type")
    private String childrenItemType;

    /**
     * 启停对象类型
     */
    @DbColumn(value = "start_stop_item_type")
    private String startStopItemType;

    /**
     * 受控对象有效状态
     */
    @DbColumn(value = "effective_status")
    private String effectiveStatus;

    /**
     * 受控对象关联的id
     */
    @DbColumn(value = "strategy_ad_group_id")
    private Long strategyAdGroupId;

    /**
     * 添加方式
     */
    @DbColumn(value = "add_way_type")
    private String addWayType;

    // 任务推送旧值
    public BigDecimal getOriginBudget() {
        if (originValue == null) {
            return BigDecimal.valueOf(0.00);
        }
        OriginValueVo originValueVo = JSONUtil.jsonToObject(originValue,OriginValueVo.class);
        return originValueVo.getBudgetValue();
    }

    public String getOriginStrategy() {
        if (originValue == null) {
            return null;
        }
        OriginValueVo originValueVo = JSONUtil.jsonToObject(originValue,OriginValueVo.class);
        return originValueVo.getStrategy();
    }

    public BigDecimal getOriginAdPlaceTopValue() {
        if (originValue == null) {
            return null;
        }
        OriginValueVo originValueVo = JSONUtil.jsonToObject(originValue,OriginValueVo.class);
        return originValueVo.getAdPlaceTopValue();

    }

    public BigDecimal getOriginAdPlaceProductValue() {
        if (originValue == null) {
            return null;
        }
        OriginValueVo originValueVo = JSONUtil.jsonToObject(originValue,OriginValueVo.class);
        return originValueVo.getAdPlaceProductValue();
    }

    public BigDecimal getOriginAdOtherValue() {
        if (originValue == null) {
            return null;
        }
        OriginValueVo originValueVo = JSONUtil.jsonToObject(originValue,OriginValueVo.class);
        return originValueVo.getAdOtherValue();
    }

    public BigDecimal getOriginBiddingValue() {
        if (originValue == null) {
            return null;
        }
        OriginValueVo originValueVo = JSONUtil.jsonToObject(originValue,OriginValueVo.class);
        return originValueVo.getBiddingValue();
    }

    public String getOriginStateValue() {
        if (originValue == null) {
            return null;
        }
        OriginValueVo originValueVo = JSONUtil.jsonToObject(originValue,OriginValueVo.class);
        return originValueVo.getState();
    }
    public BigDecimal getReturnAmountValue() {
        if (returnValue == null) {
            return BigDecimal.valueOf(0.00);
        }
        OriginValueVo originValueVo = JSONUtil.jsonToObject(returnValue,OriginValueVo.class);
        return originValueVo.getAmount();
    }
    public String getReturnPolicyValue() {
        if (returnValue == null) {
            return null;
        }
        OriginValueVo originValueVo = JSONUtil.jsonToObject(returnValue,OriginValueVo.class);
        return originValueVo.getPolicy();
    }

    // 任务推送新值
    public BigDecimal getNewBudgetValue() {
        if (newValue == null) {
            return null;
        }
        OriginValueVo newValueVo = JSONUtil.jsonToObject(newValue,OriginValueVo.class);
        return newValueVo.getBudgetValue();
    }

    public String getNewStrategy() {
        if (newValue == null) {
            return null;
        }
        OriginValueVo newValueVo = JSONUtil.jsonToObject(newValue,OriginValueVo.class);
        return newValueVo.getStrategy();
    }

    public BigDecimal getNewAdPlaceTopValue() {
        if (newValue == null) {
            return null;
        }
        OriginValueVo newValueVo = JSONUtil.jsonToObject(newValue,OriginValueVo.class);
        return newValueVo.getAdPlaceTopValue();

    }

    public BigDecimal getNewAdPlaceProductValue() {
        if (newValue == null) {
            return null;
        }
        OriginValueVo newValueVo = JSONUtil.jsonToObject(newValue,OriginValueVo.class);
        return newValueVo.getAdPlaceProductValue();
    }

    public BigDecimal getNewAdOtherValue() {
        if (newValue == null) {
            return null;
        }
        OriginValueVo newValueVo = JSONUtil.jsonToObject(newValue,OriginValueVo.class);
        return newValueVo.getAdOtherValue();
    }

    public BigDecimal getNewBiddingValue() {
        if (newValue == null) {
            return null;
        }
        OriginValueVo newValueVo = JSONUtil.jsonToObject(newValue,OriginValueVo.class);
        return newValueVo.getBiddingValue();
    }

    public String getNewStateValue() {
        if (newValue == null) {
            return null;
        }
        OriginValueVo newValueVo = JSONUtil.jsonToObject(newValue,OriginValueVo.class);
        return newValueVo.getState();
    }

    public BigDecimal getNewAmountValue() {
        if (newValue == null) {
            return BigDecimal.valueOf(0.00);
        }
        OriginValueVo newValueVo = JSONUtil.jsonToObject(newValue,OriginValueVo.class);
        return newValueVo.getAmount();
    }

    public String getNewPolicyValue() {
        if (newValue == null) {
            return null;
        }
        OriginValueVo newValueVo = JSONUtil.jsonToObject(newValue,OriginValueVo.class);
        return newValueVo.getPolicy();
    }
}