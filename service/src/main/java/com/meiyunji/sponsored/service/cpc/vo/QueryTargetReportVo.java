package com.meiyunji.sponsored.service.cpc.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel
public class QueryTargetReportVo extends CpcCommPageVo {

    private Integer shopId;
    private String marketplaceId;
    private String query;
    private String queryCn;
    private String campaignId;
    private String campaignName;
    private String campaignTargetingType;
    private String adGroupId;
    private String adGroupName;
    private String type;
    @ApiModelProperty("投放的类型  keyword、target")
    private String targetType;  //投放的类型  keyword、target
    private String targetId;

    private String currency;

    private String matchType;

    //投放类型为ASIN的商品信息
    private String imgUrl;
    private String targetTitle;
    //投放类型为类目投放
    private String category;
    private String brandName;
    private String commodityPriceRange;
    private String categoryRating;
    private String distribution;
    //回溯期:lookBack
    private String lookBack;
}
