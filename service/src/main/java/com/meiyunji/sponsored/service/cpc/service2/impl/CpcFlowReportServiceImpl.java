package com.meiyunji.sponsored.service.cpc.service2.impl;

import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.service.cpc.dao.*;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdGrossAndInvalidTrafficAllReport;
import com.meiyunji.sponsored.service.cpc.service2.*;
import com.meiyunji.sponsored.service.cpc.vo.*;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * @author: hejh
 * @date: 2024/5/17 20:13
 * @describe: CpcCommonServiceImpl
 */
@Service
@Slf4j
public class CpcFlowReportServiceImpl implements ICpcFlowReportService {

    @Autowired
    private IAmazonAdGrossAndInvalidTrafficAllReportDao reportDao;

    @Override
    public Page pageList(int puid, SearchVo searchVo, Page page) {
        page = reportDao.pageManageListExport(puid, searchVo, page);

        List<AmazonAdGrossAndInvalidTrafficAllReport> poList = page.getRows();

        if (CollectionUtils.isNotEmpty(poList)) {
            List<ReportVo> list = Lists.newArrayListWithExpectedSize(poList.size());

            poList.forEach(e -> {
                ReportVo vo = new ReportVo();
                BeanUtils.copyProperties(e, vo);
                vo.setClicks(e.getClicks().intValue());
                vo.setImpressions(e.getImpressions().intValue());
                list.add(vo);
            });
            page.setRows(list);
        }
        return page;
    }

}
