package com.meiyunji.sponsored.service.config;

import com.amazonaws.util.Md5Utils;
import com.meiyunji.sponsored.common.util.MD5Util;
import com.meiyunji.sponsored.service.log.enums.OperationLogFromEnum;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.Set;

/**
 * 动态刷新配置
 *
 * <AUTHOR>
 * @date 2023/07/28
 */
@RefreshScope
@Component
@Slf4j
public class DynamicRefreshConfiguration {
    /**
     * 监控数据开关配置
     */
    @Value("${switch.monitor-switch:false}")
    private boolean monitorSwitch;

    /**
     * 监控数据开关配置
     */
    @Value("${switch.strategy-compensation:false}")
    private boolean strategyCompensation;

    @Value("${downloadCenter.thread.pageSize:100000}")
    private int threadPageSize;

    @Value("${downloadCenter.thread.limitPage:-1}")
    private int limitPage;


    @Value("${syncShopLimit:2000}")
    private int syncShopLimit;

    public int getSyncShopLimit() {
        return syncShopLimit;
    }

    /**
     * 监控数据开关配置
     */
    @Value("${switch.operation-log-send-switch:false}")
    private boolean operationLogSendSwitch;

    @Value("#{'${grayList.operation_log:}'.empty ? null : '${grayList.operation_log:}'.split(',')}")
    private Set<Integer> grayListOperationLog;


    @Value("#{'${grayList.ad_token:}'.empty ? null : '${grayList.ad_token:}'.split(',')}")
    private Set<Integer> grayListAdToken;

    @Value("${adManagePage.partition:100000}")
    private Integer adManagePagePartition;

    @Value("${supportAbaRank.order.grayList:NONE}")
    private String supportAbaRankOrderWhiteList;

    @Value("${supportAbaRank.export.grayList:NONE}")
    private String supportAbaRankExportWhiteList;

    @Value("${supportAbaRank.grayPercentage:0}")
    private Integer supportAbaRankPercentage;

    @Value("#{'${adManagePage.whitePuids:}'.empty ? null : '${adManagePage.whitePuids:}'.split(',')}")
    private Set<String> admanagePageWhitePuidSet;

    @Value("${adManagePage.target.limit:300000}")
    private Integer admanagePageTargetLimit;
    @Value("#{'${adManagePage.target.whitePuids:}'.empty ? null : '${adManagePage.target.whitePuids:}'.split(',')}")
    private Set<String> admanagePageTargetWhitePuidSet;

    @Value("${campaignPage.partition:100000}")
    private Integer campaignPagePartition;

    @Value("${groupPage.partition:100000}")
    private Integer groupPagePartition;

    @Value("#{'${campaignPage.whitePuids:}'.empty ? null : '${campaignPage.whitePuids:}'.split(',')}")
    private Set<String> campaignPageWhitePuidSet;

    @Value("${dorisPage.partition:9000}")
    private Integer dorisPagePartition;

    @Value("#{'${dorisPage.campaign.whitePuids:0}'.empty ? null : '${dorisPage.campaign.whitePuids:0}'.split(',')}")
    private Set<String> dorisPageCampaign;

    @Value("${dorisPage.all.percentage:-1}")
    private Integer dorisPageAllPercentage;

    public Integer getDorisPageAllPercentage() {
        return dorisPageAllPercentage;
    }

    public void setDorisPageAllPercentage(Integer dorisPageAllPercentage) {
        this.dorisPageAllPercentage = dorisPageAllPercentage;
    }

    @Value("#{'${dorisPage.db.white:-1}'.empty ? null : '${dorisPage.db.white:-1}'.split(',')}")
    private Set<String> dorisPageAllDb;

    public Set<String> getDorisPageAllDb() {
        return dorisPageAllDb;
    }

    public void setDorisPageAllDb(Set<String> dorisPageAllDb) {
        this.dorisPageAllDb = dorisPageAllDb;
    }

    @Value("#{'${dorisPage.placement.whitePuids:0}'.empty ? null : '${dorisPage.placement.whitePuids:0}'.split(',')}")
    private Set<String> dorisPagePlacement;

    @Value("#{'${dorisPage.adProduct.whitePuids:0}'.empty ? null : '${dorisPage.adProduct.whitePuids:0}'.split(',')}")
    private Set<String> dorisPageAdProduct;

    @Value("#{'${dorisPage.adManageBudget.whitePuids:all}'.empty ? null : '${dorisPage.adManageBudget.whitePuids:all}'.split(',')}")
    private Set<String> dorisAdManageBudgetLog;

    @Value("#{'${dorisPage.group.whitePuids:0}'.empty ? null : '${dorisPage.group.whitePuids:0}'.split(',')}")
    private Set<String> dorisPageGroup;

    @Value("#{'${dorisPage.log.whitePuids:100,3}'.empty ? null : '${dorisPage.log.whitePuids:100,3}'.split(',')}")
    private Set<String> dorisPageLog;

    @Value("${dorisPage.log.type:}")
    private String dorisLogType;

    @Value("${dorisPage.log.writeES:false}")
    private boolean dorisLogWriteES;

    @Value("${dorisPage.log.date:20241025}")
    private Integer dorisLogDate;

    @Value("#{'${dorisPage.target.whitePuids:}'.empty ? null : '${dorisPage.target.whitePuids:}'.split(',')}")
    private Set<String> dorisPageTarget;

    @Value("#{'${dorisPage.keyword.whitePuids:}'.empty ? null : '${dorisPage.keyword.whitePuids:}'.split(',')}")
    private Set<String> dorisPageKeyword;

    @Value("#{'${dorisPage.targetSb.whitePuids:}'.empty ? null : '${dorisPage.targetSb.whitePuids:}'.split(',')}")
    private Set<String> dorisPageTargetSb;

    @Value("#{'${dorisPage.targetSd.whitePuids:}'.empty ? null : '${dorisPage.targetSd.whitePuids:}'.split(',')}")
    private Set<String> dorisPageTargetSd;

    @Value("${adTagSystem.campaignPage.tagCampaignLimit:3000}")
    private Integer adTagSystemCampaignPageTagCampaignLimit;

    @Value("#{'${groupPage.whitePuids:}'.empty ? null : '${groupPage.whitePuids:}'.split(',')}")
    private Set<String> groupPageWhitePuidSet;

    @Value("#{'${dorisPage.portfolio.whitePuids:0}'.empty ? null : '${dorisPage.portfolio.whitePuids:0}'.split(',')}")
    private Set<String> dorisPagePortfolio;

    @Value("#{'${dorisPageExportTask.whitePuids:3}'.empty ? null : '${dorisPageExportTask.whitePuids:3}'.split(',')}")
    private Set<String> dorisPageExportTask;

    @Getter
    @Value("#{'${dorisPage.placementReport.whitePuids:}'.empty ? null : '${dorisPage.placementReport.whitePuids:}'.split(',')}")
    private Set<String> dorisPagePlacementReport;

    /**
     * 删除无效店铺数据配置
     */
    @Value("${InvalidShop.delete.limit:1000}")
    private Integer invalidShopDeleteLimit;

    @Value("${InvalidShop.delete.waitTime:10000}")
    private Integer invalidShopDeleteWaitTime;

    @Value("${InvalidShop.delete.enable:false}")
    private Boolean invalidShopDeleteEnable;

    @Value("${InvalidShop.delete.filter.enable:true}")
    private Boolean invalidShopDeleteFilterEnable;

    /**
     * 删除迁移店铺数据配置
     */
    @Value("${MigrateShop.delete.limit:1000}")
    private Integer migrateShopDeleteLimit;

    @Value("${MigrateShop.delete.waitTime:10000}")
    private Integer migrateShopDeleteWaitTime;

    @Value("${MigrateShop.delete.enable:false}")
    private Boolean migrateShopDeleteEnable;

    //店铺授权触发基础数据初始化同步相关的配置
    /**
     * 店铺授权初始化同步数据开关
     */
    @Value("${shop.syncAd.init.enable:true}")
    private boolean syncAdInitEnable;

    /**
     * 店铺授权初始化任务重试次数
     */
    @Value("${shop.syncAd.init.executeLimit:10}")
    private int syncAdInitExecuteLimit;

    /**
     * 店铺授权初始化店铺级任务下一次重试时间间隔(秒)
     */
    @Value("${shop.syncAd.init.shopLevelTaskGapSeconds:300}")
    private int syncAdInitShopLevelTaskGapSeconds;

    /**
     * 店铺授权初始化组级任务下一次重试时间间隔(秒)
     */
    @Value("${shop.syncAd.init.groupLevelTaskGapSeconds:600}")
    private int syncAdInitGroupLevelTaskGapSeconds;

    /**
     * 多少时间内已同步过的店铺不再进行同步(秒), 7 * 24 * 3600 = 604800
     */
    @Value("${shop.syncAd.init.limitSeconds: 604800}")
    private int TimeLimitForShopSyncSeconds;

    /**
     * 数据同步成功率：单个shopId下同步成功任务数量 / 总任务数 * 100%
     */
    @Value("${shop.syncAd.init.successThreshold: 0.8}")
    private double SuccessThreshold;

    /**
     * 多少时间内的任务记录不删除 10 * 24 * 3600 = 864000
     */
    @Value("${shop.syncAd.init.deleteTimeLimitSecond: 864000}")
    private int deleteTimeLimitSecond;

    /**
     * 一次删除的最大数量
     */
    @Value("${shop.syncAd.init.onceDelMaxCount: 10000}")
    private int onceDelMaxCount;

    /**
     * 店铺授权触发初始化时，广告类型下的组级任务达到该上限则交给定时任务分片来处理
     */
    @Value("${shop.syncAd.init.waitScheduleHandleCount:10000}")
    private int waitScheduleHandleCount;

    /**
     * 店铺授权触发初始化测试puid
     */
    @Value("#{'${shop.syncAd.init.grayPuid:}'.empty ? null : '${shop.syncAd.init.grayPuid:}'.split(',')}")
    private Set<Integer> syncAdInitGrayPuidSet;

    /**
     * 店店铺授权触发初始化灰度百分比
     */
    @Value("${shop.syncAd.init.grayPercent:0}")
    private int syncAdInitGrayPercent;

    /**
     * 监控数据开关配置
     */
    @Value("${switch.management-stream-split-switch:false}")
    private Boolean managementStreamSplitSwitch;


    /**
     * asin 信息是否查询用户自己asin
     */
    @Value("${switch.asin-info-check:true}")
    private Boolean asinInfoCheckSwitch;

    @Value("${manage-log.search.redundancy-days:90}")
    private int manageLogSearchRedundancyDays;

    @Value("${manage-log.search.optimize:false}")
    private Boolean manageLogSearchOptimize;

    @Value("${manage-log.search.totalSizeLimit:10000}")
    private int manageLogSearchTotalSizeLimit;

    @Value("${manage-log.search.operationContentWay:1}")
    private int manageLogSearchOperationContentWay;

    @Value("${limit.stream.retry:3}")
    private Integer streamRetryLimit;

    /**
     * 广告产品表同步父asin分片
     */
    @Value("${parentAsin.sync.partition:100}")
    private Integer parentAsinSyncPartition;

    @Value("${target-split.write-switch:false}")
    private Boolean splitTargetWriteSwitch;

    @Value("${target-split.sharding-query-switch:false}")
    private Boolean splitTargetShardingQuerySwitch;

    /**
     * 店铺授权触发初始化测试puid
     */
    @Value("#{'${target-split.write-puids:}'.empty ? null : '${target-split.write-puids:}'.split(',')}")
    private Set<Integer> targetSplitWritePuids;

    @Value("#{'${target-split.sharding-query-puids:}'.empty ? null : '${target-split.sharding-query-puids:}'.split(',')}")
    private Set<Integer> targetSplitShardingQueryPuids;

    @Value("${threadpool.monitor.warningSize:3000}")
    private Integer threadpoolMonitorWarningSize;

    /**
     * 报告监控随机生成shopId的个数
     */
    @Value("${reportMonitor.randomShopIdCount: 10}")
    private Integer randomListSum;
    /**
     * 报告监控单个抽样店铺最少拥有广告活动数量
     */
    @Value("${reportMonitor.minCampaignCount: 100}")
    private Integer minCampaignCount;

    /**
     * 报告监控单次处理报告记录的数量
     */
    @Value("${reportMonitor.toBeDealRecordCount: 100}")
    private Integer toBeDealRecordCount;

    /**
     * 通过落地页查询asinlist并放进缓存，缓存时间
     */
    @Value("${asinList.cacheTime: 3600}")
    private Integer asinListByUrlCacheTimeSeconds;

    public Integer getAsinListByUrlCacheTimeSeconds() {
        return asinListByUrlCacheTimeSeconds;
    }

    /**
     * 查询否定投放是否包含报告数据
     */
    @Value("${neTarget.containsReport:true}")
    private Boolean containsReport;

    public Boolean getContainsReport() {
        return containsReport;
    }


    @Value("#{'${dorisPage.queryWordRoot.whitePuids:}'.empty ? null : '${dorisPage.queryWordRoot.whitePuids:}'.split(',')}")
    private Set<String> dorisPageQueryWordRootWhitePuidSet;

    @Value("${dorisPage.queryWordRoot.partition:9000}")
    private Integer dorisPageQueryWordRootPartition;

    @Value("#{'${wordFrequency.whitePuids:}'.empty ? null : '${wordFrequency.whitePuids:}'.split(',')}")
    private Set<String> wordFrequencyWhitePuidSet;

    @Getter
    @Value("${copy.campaign.group.limit:100}")
    private Integer copyCampaignGroupLimit;

    @Getter
    @Value("#{T(com.meiyunji.sponsored.service.config.IndexStrategyConfig).splitToMap('${copy.campaign.group.puid.limit:}')}")
    private Map<Integer, Integer> copyCampaignGroupPuidLimit;

    @Getter
    @Value("${copy.campaign.product.limit:30000}")
    private Integer copyCampaignProductLimit;

    @Getter
    @Value("#{T(com.meiyunji.sponsored.service.config.IndexStrategyConfig).splitToMap('${copy.campaign.product.puid.limit:}')}")
    private Map<Integer, Integer> copyCampaignProductPuidLimit;

    @Value("#{'${wordFrequency.keywordWordRoot.blackPuids:}'.empty ? '101021,22525,117451,16554,86583,95191,59324,53702,73418,11819,98805'.split(',') : '${wordFrequency.keywordWordRoot.blackPuids:}'.split(',')}")
    private Set<String> keywordWordRootBlackPuids;

    @Value("#{'${wordFrequency.singleWord.whilePuids:}'.empty ? null : '${wordFrequency.singleWord.whilePuids:}'.split(',')}")
    private Set<String> singleWordWhilePuids;

    /**
     * 词根计算每批限制
     */
    @Value("${wordFrequency.wordRootCalculateLimit: 200000}")
    private Integer wordRootCalculateLimit;

    /**
     * 词根计算天数限制
     */
    @Value("${wordFrequency.wordRootCalculateDateBeforeLimit: -60}")
    private Integer wordRootCalculateDateBeforeLimit;

    /**
     * 列表页接口是否发送同步消息
     */
    @Value("${switch.sync-manage:false}")
    private Boolean syncManage;

    @Value("#{'${adManageTag.init.whitePuids:}'.empty ? null : '${adManageTag.init.whitePuids:}'.split(',')}")
    private Set<String> adManageTagInitWhitePuidSet;

    @Value("${adManageTag.init.all:false}")
    private boolean adManageTagInitAllBool;

    @Value("#{'${keywordLib.asinLib.add.limit.permit:0}'.empty ? null : '${keywordLib.asinLib.add.limit.permit:0}'.split(',')}")
    private Set<String> keywordLibAndAsinLibAddLimitPermit;


    @Value("${dashboard.effectData.sellerId.filter:true}")
    private Boolean dashboardEffectDataSellerIdFilter;

    @Value("#{'${cosPublic.transferMean.stringList:}'.empty ? null : '${cosPublic.transferMean.stringList:}'.split('0')}")
    private Set<String> cosPublicTransferMeanStringList;

    public Set<String> getKeywordLibAndAsinLibPermitPuidSet() {
        return keywordLibAndAsinLibAddLimitPermit;
    }


    public Boolean isSyncManage() {
        return syncManage;
    }

    public Set<String> getDorisPageLog() {
        return dorisPageLog;
    }

    /**
     * 列表页接口是否发送同步消息
     */
    @Value("${switch.sync-manage-proxy:false}")
    private Boolean syncManageProxy;

    public Boolean getSyncManageProxy() {
        return syncManageProxy;
    }

    /**
     * 列表页接口是否发送同步消息
     */
    @Value("${switch.sync-stream-manage-proxy:false}")
    private Boolean syncStreamManageProxy;

    public Boolean getSyncStreamManageProxy() {
        return syncStreamManageProxy;
    }

    /**
     * 店铺授权过期失败次数
     */
    @Value("${checkShopAuthMax: 5}")
    private Integer checkShopAuthMax;

    public Integer getShopAuthErrorCountMax() {
        if (checkShopAuthMax == null) {
            return 5;
        } else if (checkShopAuthMax < 3) {
            return 5;
        }
        return checkShopAuthMax;
    }

    public Boolean isSplitTargetQuery(Integer puid) {
        return true;
    }

    /**
     * 投放任务可以使用线程池处理的投放数量上限
     */
    @Value("${target-task.use-multi-thread.size.limit:50}")
    private Integer useMultiThreadHandleTargetTaskSizeLimit;

    @Value("#{'${sbvData.whitePuids:}'.empty ? null : '${sbvData.whitePuids:}'.split(',')}")
    private Set<String> sbvDataWhitePuidSet;

    public boolean checkSbvData (int puid) {
        if (CollectionUtils.isEmpty(sbvDataWhitePuidSet)) {
            return false;
        }
        if (sbvDataWhitePuidSet.contains("all")) {
            return true;
        }
        if (sbvDataWhitePuidSet.contains(String.valueOf(puid))) {
            return true;
        }
        return false;
    }

    @Value("${grayScale.operationNotes.grayList:NONE}")
    private String operationNotesGrayList;

    @Value("${grayScale.operationNotes.grayPercentage:0}")
    private Integer operationNotesGrayPercentage;

    public String getOperationNotesGrayList() {
        return operationNotesGrayList;
    }

    public Integer getOperationNotesGrayPercentage() {
        return operationNotesGrayPercentage;
    }

    public Integer getStreamRetryLimit() {
        return streamRetryLimit;
    }

    public Boolean isManagementStreamSplitSwitch() {
        return managementStreamSplitSwitch;
    }

    public boolean isMonitorSwitch() {
        return monitorSwitch;
    }

    public int getReportExportThreadPageSize() {
        return threadPageSize;
    }

    public int getReportlimitPage() {
        return limitPage;
    }

    public Integer getAdManagePagePartition() {
        return adManagePagePartition;
    }

    public Set<String> getAdmanagePageWhitePuidSet() {
        return admanagePageWhitePuidSet;
    }

    public Integer getAdmanagePageTargetLimit() {
        return admanagePageTargetLimit;
    }

    public Set<String> getAdmanagePageTargetWhitePuidSet() {
        return admanagePageTargetWhitePuidSet;
    }

    public Integer getCampaignPagePartition() {
        return campaignPagePartition;
    }

    @Value("${dorisPage.home.percent:0}")
    private Integer dorisHomePercent;

    public Integer getDorisHomePercent() {
        return dorisHomePercent;
    }

    public void setDorisHomePercent(Integer dorisHomePercent) {
        this.dorisHomePercent = dorisHomePercent;
    }

    @Value("${dorisPage.report.percent:0}")
    private Integer dorisReportPercent;

    public Integer getGroupLimit(Integer puid) {
        if (copyCampaignGroupPuidLimit.containsKey(puid)) {
            return copyCampaignGroupPuidLimit.get(puid);
        }
        return copyCampaignGroupLimit;
    }

    public Integer getProductLimit(Integer puid) {
        if (copyCampaignProductPuidLimit.containsKey(puid)) {
            return copyCampaignProductPuidLimit.get(puid);
        }
        return copyCampaignProductLimit;
    }

    public Boolean verifyGroupAndAdReport(Integer puid) {
        if (dorisReportPercent > 0) {
            return verifyPercentage(puid, dorisReportPercent);
        }
        return false;
    }

    public Boolean verifyDorisHome(Integer puid) {
        if (dorisHomePercent > 0) {
            return verifyPercentage(puid, dorisHomePercent);
        }
        return false;
    }

    public boolean verifyDorisPage(Integer puid, Set<String> set) {
        if (CollectionUtils.isNotEmpty(set) && (set.contains("all") || set.contains(puid.toString()))) {
            return true;
        }

        if (CollectionUtils.isNotEmpty(dorisPageAllDb)) {
            String dbIndex = getDb(puid);
            if (dorisPageAllDb.contains(dbIndex)) {
                log.info("doris select");
                return true;
            }
        }

        if (dorisPageAllPercentage > 0) {
            // 百分比灰度
            return verifyPercentage(puid, dorisPageAllPercentage);
        }
        return false;
    }

    public boolean verifyDorisPageByPuid(Integer puid, Set<String> set) {
        if (CollectionUtils.isNotEmpty(set) && (set.contains("all") || set.contains(puid.toString()))) {
            return true;
        }
        return false;
    }

    /**
     * 校验日志和日期
     */
    public boolean verifyDorisLogType(String type, String date) {
        if (StringUtils.isBlank(dorisLogType)) {
            return false;
        }

        if ("all".equalsIgnoreCase(dorisLogType)) {
            return true;
        }

        if (dorisLogType.contains(type)) {
            if (OperationLogFromEnum.AMAZON.getOperationType().equals(type)) {
                return Integer.parseInt(date.replace("-", "").trim()) >= dorisLogDate;
            }
            return true;
        }
        return false;
    }

    /**
     * 是否验证邮编
     */
    @Value("${switch.keyword_card_check_postcode:false}")
    private Boolean keywordCardCheckPostcode;

    public Boolean getKeywordCardCheckPostcode() {
        return keywordCardCheckPostcode;
    }

    @Value("${asin.fba.inventory.querySize:2000}")
    private int asinFbaInventoryQuerySize;

    public int getAsinFbaInventoryQuerySize() {
        return asinFbaInventoryQuerySize;
    }

    private String getDb(Integer puid) {
        byte[] digest = MD5Util.getMD5Digest("" + puid);
        int byte1 = digest[digest.length - 2] & 0xff;
        int byte2 = digest[digest.length - 1] & 0xff;
        int index = (byte1 << 8) + byte2;
        index = index % 8;
        return String.valueOf(index);
    }

    public boolean verifyPercentage(Integer puid, Integer percentage){
        if (percentage >= 100) {
            return true;
        }
        byte aByte = Md5Utils.computeMD5Hash(puid.toString().getBytes(StandardCharsets.UTF_8))[0];
        BigDecimal divide = BigDecimal.valueOf(aByte + 128).divide(BigDecimal.valueOf(255), 2, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100));
        return divide.intValue() < percentage;
    }

    public Set<String> getDorisPageCampaign() {
        return dorisPageCampaign;
    }

    public Set<String> getDorisPagePlacement() {
        return dorisPagePlacement;
    }

    public Set<String> getDorisPageAdProduct() {
        return dorisPageAdProduct;
    }

    public Integer getDorisPagePartition() {
        return dorisPagePartition;
    }

    public Integer getGroupPagePartition() {
        return groupPagePartition;
    }

    public Set<String> getGroupPageWhitePuidSet() {
        return groupPageWhitePuidSet;
    }

    public Set<String> getDorisPagePortfolio() {
        return dorisPagePortfolio;
    }

    public Set<String> getDorisPageExportTask() {
        return dorisPageExportTask;
    }

    public Set<String> getCampaignPageWhitePuidSet() {
        return campaignPageWhitePuidSet;
    }

    public Set<String> getDorisPageGroup() {
        return dorisPageGroup;
    }

    public boolean isOperationLogSendSwitch() {
        return operationLogSendSwitch;
    }

    public Integer getInvalidShopDeleteLimit() {
        return invalidShopDeleteLimit;
    }

    public Integer getInvalidShopDeleteWaitTime() {
        return invalidShopDeleteWaitTime;
    }

    public Boolean getInvalidShopDeleteEnable() {
        return invalidShopDeleteEnable;
    }

    public Boolean getInvalidShopDeleteFilterEnable() {
        return invalidShopDeleteFilterEnable;
    }

    public Integer getMigrateShopDeleteLimit() {
        return migrateShopDeleteLimit;
    }

    public Integer getMigrateShopDeleteWaitTime() {
        return migrateShopDeleteWaitTime;
    }

    public Boolean getMigrateShopDeleteEnable() {
        return migrateShopDeleteEnable;
    }

    public Set<String> getDorisAdManageBudgetLog() {
        return dorisAdManageBudgetLog;
    }

    public boolean verifyOperationLogGray(Integer puid) {
        if (puid == null || CollectionUtils.isEmpty(grayListOperationLog)) {
            return false;
        }
        return grayListOperationLog.contains(puid);
    }


    public boolean verifyAdTokenGray(Integer puid) {
        if (puid == null || CollectionUtils.isEmpty(grayListAdToken)) {
            return false;
        }
        //如果配置一个-10 则代表全部返回true
        if (grayListAdToken.size() == 1 && grayListAdToken.contains(-10)) {
            return true;
        }
        return grayListAdToken.contains(puid);
    }

    public boolean isSyncAdInitEnable() {
        return syncAdInitEnable;
    }

    public int getSyncAdInitExecuteLimit() {
        return syncAdInitExecuteLimit;
    }

    public int getSyncAdInitShopLevelTaskGapSeconds() {
        return syncAdInitShopLevelTaskGapSeconds;
    }

    public int getSyncAdInitGroupLevelTaskGapSeconds() {
        return syncAdInitGroupLevelTaskGapSeconds;
    }

    public int getWaitScheduleHandleCount() {
        return waitScheduleHandleCount;
    }

    public Set<Integer> getSyncAdInitGrayPuidSet() {
        return syncAdInitGrayPuidSet;
    }

    public int getSyncAdInitGrayPercent() {
        return syncAdInitGrayPercent;
    }

    public int getManageLogSearchRedundancyDays() {
        return manageLogSearchRedundancyDays;
    }

    public Boolean getManageLogSearchOptimize() {
        return manageLogSearchOptimize;
    }

    public int getManageLogSearchTotalSizeLimit() {
        return manageLogSearchTotalSizeLimit;
    }

    public int getManageLogSearchOperationContentWay() {
        return manageLogSearchOperationContentWay;
    }

    public Integer getParentAsinSyncPartition() {
        return parentAsinSyncPartition;
    }

    public int getTimeLimitForShopSyncSeconds() {
        return TimeLimitForShopSyncSeconds;
    }

    public double getSuccessThreshold() {
        return SuccessThreshold;
    }

    public int getDeleteTimeLimitSecond() {
        return deleteTimeLimitSecond;
    }

    public int getOnceDelMaxCount() {
        return onceDelMaxCount;
    }

    public Set<String> getDorisPageTarget() {
        return dorisPageTarget;
    }

    public Set<String> getDorisPageKeyword() {
        return dorisPageKeyword;
    }

    public Set<String> getDorisPageTargetSb() {
        return dorisPageTargetSb;
    }

    public Set<String> getDorisPageTargetSd() {
        return dorisPageTargetSd;
    }

    public Integer getThreadpoolMonitorWarningSize() {
        return threadpoolMonitorWarningSize;
    }

    public Integer getRandomListSum() {
        return randomListSum;
    }

    public Integer getMinCampaignCount() {
        return minCampaignCount;
    }

    public Integer getToBeDealRecordCount() {
        return toBeDealRecordCount;
    }

    public Set<String> getDorisPageQueryWordRootWhitePuidSet() {
        return dorisPageQueryWordRootWhitePuidSet;
    }

    public Integer getDorisPageQueryWordRootPartition() {
        return dorisPageQueryWordRootPartition;
    }

    public Integer getUseMultiThreadHandleTargetTaskSizeLimit() {
        return useMultiThreadHandleTargetTaskSizeLimit;
    }

    public Set<String> getWordFrequencyWhitePuidSet() {
        return wordFrequencyWhitePuidSet;
    }

    public String getSupportAbaRankOrderWhiteList() {
        return supportAbaRankOrderWhiteList;
    }

    public String getSupportAbaRankExportWhiteList() {
        return supportAbaRankExportWhiteList;
    }

    public Integer getSupportAbaRankPercentage() {
        return supportAbaRankPercentage;
    }

    public Integer getWordRootCalculateLimit() {
        return wordRootCalculateLimit;
    }

    public Integer getWordRootCalculateDateBeforeLimit() {
        return wordRootCalculateDateBeforeLimit;
    }

    public Set<String> getKeywordWordRootBlackPuids() {
        return keywordWordRootBlackPuids;
    }

    public Set<String> getSingleWordWhilePuids() {
        return singleWordWhilePuids;
    }

    public Set<String> getAdManageTagInitWhitePuidSet() {
        return adManageTagInitWhitePuidSet;
    }

    public boolean getAdManageTagInitAllBool() {
        return adManageTagInitAllBool;
    }

    public Integer getAdTagSystemCampaignPageTagCampaignLimit() {
        return adTagSystemCampaignPageTagCampaignLimit;
    }

    public boolean isStrategyCompensation() {
        return strategyCompensation;
    }

    public boolean isDorisLogWriteES() {
        return dorisLogWriteES;
    }

    public Boolean getDashboardEffectDataSellerIdFilter() {
        return dashboardEffectDataSellerIdFilter;
    }

    public Set<String> getCosPublicTransferMeanStringList() {
        return cosPublicTransferMeanStringList;
    }

    public Boolean getAsinInfoCheckSwitch() {
        return asinInfoCheckSwitch;
    }
}
