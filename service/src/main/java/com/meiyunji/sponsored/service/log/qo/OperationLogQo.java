package com.meiyunji.sponsored.service.log.qo;

import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.util.StringUtil;
import com.meiyunji.sponsored.service.log.enums.OperationLogFromEnum;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

@Data
public class OperationLogQo {

    private Integer pageNo = 1;
    private Integer pageSize = 20;

    private Integer puid;
    private Integer uid;
    private String uuid;
    private Integer shopId;
    private String module;
    private String userName;
    private List<String> userIds;
    private String operationContent;
    private String start;
    private String end;
    /**
     * {@link OperationLogFromEnum} sellfox,amazon,auto,other;
     */
    private String from;
    /**
     * sp,sb,sd
     */
    private String adType;
    private String marketplaceId;
    private String campaignId;
    private String campaignIds;
    private List<String> campaignIdList = Lists.newArrayList();
    private String groupId;
    private String groupIds;
    private List<String> groupIdList = Lists.newArrayList();
    /**
     * 操作对象
     */
    private String operationObject;
    private List<String> operationObjectList;

    private String operationType;

    private String isShow;
    
    private Integer isSuccess;

    /**
     * 投放类型
     */
    private String targetType;

    /**
     * 调整范围
     */
    private String adjustmentRange;

    /**
     * 模板Id
     */
    private Long templateId;

    private String targetId;

    private String changeType;

    private String siteStart;
    private String siteEnd;

    /**
     * 投放名称
     */
    private String targetName;

    private String portfolioId;
    private String portfolioIds;
    private List<String> portfolioIdList = Lists.newArrayList();
    private List<String> portfolioCampaignIds = Lists.newArrayList();

    private String productSearchValue ;
    private String productSearchType ;
    private String productSearchQueryType ;

    // 批量搜索
    public List<String> getListSearchValue(){
        if(StringUtils.isNotBlank(this.targetName)){
            return StringUtil.stringToList(this.targetName.trim(),"%±%");
        }
        return new ArrayList<>();
    }

}
