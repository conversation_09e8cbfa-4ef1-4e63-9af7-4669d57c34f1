package com.meiyunji.sponsored.service.cpc.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: ys
 * @date: 2024/6/5 14:13
 * @describe:
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SBCampaignInfoVo {
    private String campaignId;
    private String campaignName;
    private String budget;
    private String budgetType;
    private String brandEntityId;
    private String portfolioId;
    private Boolean bidOptimization;
    private String placementPercentage;
    private String startDateStr;
    private String endDateStr;
    private String campaignState;
}
