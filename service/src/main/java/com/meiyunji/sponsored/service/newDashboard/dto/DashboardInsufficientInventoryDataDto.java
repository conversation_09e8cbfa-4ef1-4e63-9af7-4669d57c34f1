package com.meiyunji.sponsored.service.newDashboard.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.meiyunji.sponsored.service.sellfoxApi.vo.FbaInTransitApiVO;
import com.meiyunji.sponsored.service.sellfoxApi.vo.FbaStockApiVO;
import com.meiyunji.sponsored.service.sellfoxApi.vo.OutOfStockDateAfterArrivalVO;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 全景仪库存不足响应
 * @author: zzh
 * @date: 2025-03-14  10:41
 */
@Data
public class DashboardInsufficientInventoryDataDto {

    /**
     * 用户id
     */
    private Integer puid;

    /**
     * 店铺id
     */
    private Integer shopId;

    @ExcelProperty("店铺名称")
    private String shopName;

    /**
     * 站点id
     */
    private String marketplaceId;

    @ExcelProperty("站点名称")
    private String marketplaceName;

    @ExcelProperty("asin")
    private String asin;

    @ExcelProperty("msku")
    private String mskus;

    /**
     * msku
     */
    private List<String> msku;

    /**
     * asin图片
     */
    private String mainImage;

    @ExcelProperty("广告活动数量")
    private Integer campaignNum;

    @ExcelProperty("在库在途库存可售天数（FBA）")
    private Integer inStockInTransitSellableDays;

    @ExcelProperty("在库库存可售天数（FBA)")
    private Integer inStockSellableDays;

    @ExcelProperty("预估断货前可售天数（FBA)")
    private Integer sellableDays;

    @ExcelProperty("到货后断货日期")
    private String outOfStockAfterArrival;

    @ExcelProperty("到货后断货日期至今天数")
    private Long betweenDay;

    /**
     * 到货后断货日期详情
     */
    private List<OutOfStockDateAfterArrivalVO> outOfStockDateAfterArrivalDetail;

    @ExcelProperty("fba库存")
    private Integer fbaStock;

    /**
     * fba库存详情
     */
    private List<FbaStockApiVO> fbaStockDetail;

    @ExcelProperty("fba在途库存")
    private Integer fbaInTransit;

    /**
     * fba在途库存详情
     */
    private List<FbaInTransitApiVO> fbaInTransitDetail;

    @ExcelProperty("日均销量")
    private BigDecimal dailySale;

    @ExcelProperty("昨日销量")
    private Integer saleYesterday;

    @ExcelProperty("3天销量")
    private Integer saleDay3;

    @ExcelProperty("7天销量")
    private Integer saleDay7;

    @ExcelProperty("14天销量")
    private Integer saleDay14;

    @ExcelProperty("30天销量")
    private Integer saleDay30;

    @ExcelProperty("60天销量")
    private Integer saleDay60;

    @ExcelProperty("90天销量")
    private Integer saleDay90;
}
