package com.meiyunji.sponsored.service.sync.strgtegy;

import com.meiyunji.sponsored.common.support.RedisService;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.config.DynamicRefreshConfiguration;
import com.meiyunji.sponsored.service.cpc.service2.sd.impl.CpcSdGroupApiService;
import com.meiyunji.sponsored.service.stream.enums.StreamConstants;
import com.meiyunji.sponsored.service.stream.service.IAmazonManagementStreamTaskRetryService;
import lombok.SneakyThrows;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @description: some desc
 * @author: pxq
 * @email: <EMAIL>
 * @date: 2024/1/17 15:29
 */
@Service
public class ManageSyncSdGroupProcess extends AbstractSyncServerStatusProcessStrategy {


    @Resource
    private CpcSdGroupApiService cpcSdGroupApiService;

    @Resource
    private DynamicRefreshConfiguration dynamicRefreshConfiguration;

    public ManageSyncSdGroupProcess(IAmazonManagementStreamTaskRetryService amazonManagementStreamTaskRetryService, DynamicRefreshConfiguration dynamicRefreshConfiguration, RedisService redisService) {
        super(amazonManagementStreamTaskRetryService, dynamicRefreshConfiguration, redisService);

    }


    @Override
    public int getMaxCount() {
        return StreamConstants.SP_MAX_GROUP_IDS_COUNT;
    }

    @SneakyThrows
    @Override
    public void doExecute(ShopAuth shopAuth, String ids) {
        Boolean syncManageProxy = dynamicRefreshConfiguration.getSyncManageProxy();
        cpcSdGroupApiService.syncAdGroups(shopAuth, null, ids, null, true, null, Boolean.TRUE.equals(syncManageProxy));
    }
}
