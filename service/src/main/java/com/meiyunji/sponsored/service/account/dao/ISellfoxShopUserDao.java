package com.meiyunji.sponsored.service.account.dao;


import com.meiyunji.sponsored.common.springjdbc.IBaseDao;
import com.meiyunji.sponsored.service.account.po.SellfoxShopUser;

import java.util.List;

/**
 * 店铺用户关联表
 *
 * chenyunshi 2020/12/25
 */
public interface ISellfoxShopUserDao extends IBaseDao<SellfoxShopUser> {

    Integer createNew(Integer puid, Integer shopId, Integer userId);

    // 获取用户拥有的店铺id
    List<Integer> getShopIdListByUser(Integer puid, Integer userId);

    // 获取用户拥有的店铺id
    List<Integer> getUserIdListByShop(Integer puid, Integer shopId);

}
