package com.meiyunji.sponsored.service.cpc.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 否定投放报告数据dto
 * @Author: hejh
 * @Date: 2024/11/13 21:11
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NeTargetReportDataDto {
    /**
     * targetId or keyword_id
     */
    private String targetId;

    private BigDecimal cost;
    private BigDecimal totalSales;
    private Integer impressions;
    private Integer clicks;
    private Integer adOrderNum;

    //用于计算分析数据日周月
    private Date countDay;
    private Integer countMonth;
    private String countDate;

    //用于导出前后30天任务日期
    private Date creationDate;
    private Date creationBeforeDate;
    private Date creationAfterDate;
}
