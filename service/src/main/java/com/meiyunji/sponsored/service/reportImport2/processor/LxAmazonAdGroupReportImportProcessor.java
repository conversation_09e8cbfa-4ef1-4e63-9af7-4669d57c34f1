package com.meiyunji.sponsored.service.reportImport2.processor;

import com.alibaba.fastjson.JSONReader;
import com.google.common.collect.Lists;
import com.meiyunji.sellfox.aadas.types.enumeration.Marketplace;
import com.meiyunji.sponsored.common.util.GZipUtils;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.config.CosBucketClient;
import com.meiyunji.sponsored.service.cpc.dao.*;
import com.meiyunji.sponsored.service.cpc.po.*;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.enums.CampaignTypeEnum;
import com.meiyunji.sponsored.service.reportImport.constanst.LxReportConstant;
import com.meiyunji.sponsored.service.reportImport.model.LxAdGroupReport;
import com.meiyunji.sponsored.service.reportImport.model.LxCampaignReport;
import com.meiyunji.sponsored.service.reportImport2.modle.LxAmazonAdCampaignReport;
import com.meiyunji.sponsored.service.reportImport2.modle.LxAmazonAdGroupReport;
import com.meiyunji.sponsored.service.reportImport2.vo.AmazonAdReportImportMessage;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.io.InputStreamReader;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * lx活动报告导入处理器
 *
 * <AUTHOR>
 * @date 2023/05/26
 */
@Service
@Slf4j
public class LxAmazonAdGroupReportImportProcessor extends AbstractAmazonAdLxReportImportProcessor<LxAmazonAdGroupReport> {


    private final IAmazonAdGroupReportDao amazonAdGroupReportDao;
    private final IAmazonAdSbGroupReportDao amazonAdSbGroupReportDao;
    private final IAmazonAdSdGroupReportDao amazonAdSdGroupReportDao;


    protected LxAmazonAdGroupReportImportProcessor(IAmazonAdCampaignAllDao amazonAdCampaignAllDao, IAmazonAdGroupDao amazonAdGroupDao,
                                                   IAmazonSdAdGroupDao amazonSdAdGroupDao, IAmazonSbAdGroupDao amazonSbAdGroupDao,
                                                   IScVcShopAuthDao shopAuthDao, CosBucketClient tempBucketClient,
                                                   IAmazonAdGroupReportDao amazonAdGroupReportDao,
                                                   IAmazonAdSbGroupReportDao amazonAdSbGroupReportDao,
                                                   IAmazonAdSdGroupReportDao amazonAdSdGroupReportDao) {
        super(amazonAdCampaignAllDao, amazonAdGroupDao, amazonSdAdGroupDao, amazonSbAdGroupDao, shopAuthDao, tempBucketClient);
        this.amazonAdGroupReportDao = amazonAdGroupReportDao;
        this.amazonAdSbGroupReportDao = amazonAdSbGroupReportDao;
        this.amazonAdSdGroupReportDao = amazonAdSdGroupReportDao;
    }


    @Override
    public void importReport(AmazonAdReportImportMessage importMessage) {

        //读取数据
        try (InputStreamReader inputStreamReader = new InputStreamReader(new ByteArrayInputStream(GZipUtils.release(tempBucketClient.getObjectToBytes(importMessage.getFileId().replaceFirst("/", "")))))) {
            ShopAuth shopAuth = shopAuthDao.getScAndVcByIdAndPuid(importMessage.getShopId(), importMessage.getPuid());
            JSONReader jsonReader = new JSONReader(inputStreamReader);
            jsonReader.startArray();
            List<LxAmazonAdGroupReport> reports = Lists.newArrayListWithExpectedSize(500);
            while (jsonReader.hasNext()) {
                jsonReader.startObject();
                LxAmazonAdGroupReport report = new LxAmazonAdGroupReport();
                report.readFromJsonReader(jsonReader);
                jsonReader.endObject();
                if (report.getImpressions() != 0) {
                    reports.add(report);
                    if (reports.size() >= 500) {
                        dealReport(importMessage, reports, shopAuth);
                        reports = Lists.newArrayListWithExpectedSize(500);
                    }
                }

            }
            jsonReader.endArray();
            jsonReader.close();
            if (CollectionUtils.isNotEmpty(reports)) {
                dealReport(importMessage, reports, shopAuth);
            }
        } catch (Exception e) {
            log.error("import campaign report error puid:{},shopId：{}，scheduleId :{} error:", importMessage.getPuid(), importMessage.getShopId(), importMessage.getScheduleId(), e);
        }

    }

    private void dealReport(AmazonAdReportImportMessage importMessage, List<LxAmazonAdGroupReport> reports, ShopAuth shopAuth) {
        Integer puid = importMessage.getPuid();
        Integer shopId = importMessage.getShopId();

        String adType = importMessage.getAdType().toLowerCase();
        List<String> campaignIds = reports.stream().map(LxAmazonAdGroupReport::getCampaignId).collect(Collectors.toList());
        List<AmazonAdCampaignAll> byCampaignIds = amazonAdCampaignAllDao.getByCampaignIds(puid, shopId, shopAuth.getMarketplaceId(), campaignIds, adType);
        List<String> adGroupIds = reports.stream().map(LxAmazonAdGroupReport::getAdGroupId).collect(Collectors.toList());

        Map<String, AmazonAdGroup> spAdGroups = new HashMap<>();
        Map<String, AmazonSbAdGroup> sbAdGroups = new HashMap<>();
        Map<String, AmazonSdAdGroup> sdAdGroups = new HashMap<>();

        if (CampaignTypeEnum.sp.name().equalsIgnoreCase(importMessage.getAdType())) {
            spAdGroups = amazonAdGroupDao.getAdGroupByIds(puid, shopId, adGroupIds).stream().collect(Collectors.toMap(AmazonAdGroup::getAdGroupId, Function.identity(), (e1, e2) -> e2));

        } else if (CampaignTypeEnum.sb.name().equalsIgnoreCase(importMessage.getAdType())) {
            sbAdGroups = amazonSbAdGroupDao.getAdGroupByIds(puid, shopId, adGroupIds).stream().collect(Collectors.toMap(AmazonSbAdGroup::getAdGroupId, Function.identity(), (e1, e2) -> e2));
        } else if (CampaignTypeEnum.sd.name().equalsIgnoreCase(importMessage.getAdType())) {
            sdAdGroups = amazonSdAdGroupDao.getByGroupIds(puid, shopId, adGroupIds).stream().collect(Collectors.toMap(AmazonSdAdGroup::getAdGroupId, Function.identity(), (e1, e2) -> e2));
        }
        Map<String, AmazonAdCampaignAll> amazonAdCampaignAllMap = byCampaignIds.stream().collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, Function.identity(), (e1, e2) -> e2));


        Map<String, AmazonAdGroup> finalSpAdGroups = spAdGroups;
        Map<String, AmazonSbAdGroup> finalSbAdGroups = sbAdGroups;
        Map<String, AmazonSdAdGroup> finalSdAdGroups = sdAdGroups;

        List<AmazonAdGroupReport> spReports = new ArrayList<>();
        List<AmazonAdSbGroupReport> sbReports = new ArrayList<>();
        List<AmazonAdSdGroupReport> sdReports = new ArrayList<>();
        reports.forEach(e -> {
            AmazonAdCampaignAll amazonAdCampaignAll = amazonAdCampaignAllMap.get(e.getCampaignId());
            if (amazonAdCampaignAll == null) {
                log.error("pxq-report-import puid : {} shop_id : {} campaignId : {} 不存在", puid, shopId, e.getCampaignId());
                return;
            }


            if (CampaignTypeEnum.sp.name().equalsIgnoreCase(importMessage.getAdType())) {
                AmazonAdGroup amazonAdGroup = finalSpAdGroups.get(e.getAdGroupId());
                if (amazonAdGroup == null) {
                    log.error("pxq-report-import puid : {} shop_id : {} campaignId : {}, groupId:{} 不存在", puid, shopId, e.getCampaignId(), e.getAdGroupId());
                    return;
                }
                AmazonAdGroupReport spReport = buildSpAdGroupReport(importMessage.getCountDate(), e, amazonAdGroup, amazonAdCampaignAll, shopAuth);
                spReports.add(spReport);
            } else if (CampaignTypeEnum.sb.name().equalsIgnoreCase(importMessage.getAdType())) {
                AmazonSbAdGroup amazonSbAdGroup = finalSbAdGroups.get(e.getAdGroupId());
                if (amazonSbAdGroup == null) {
                    log.error("pxq-report-import puid : {} shop_id : {} campaignId : {}, groupId:{} 不存在", puid, shopId, e.getCampaignId(), e.getAdGroupId());
                    return;
                }
                AmazonAdSbGroupReport sbReport = buildSbAdGroupReport(importMessage.getCountDate(), e, amazonSbAdGroup, amazonAdCampaignAll, shopAuth);
                sbReports.add(sbReport);
            } else if (CampaignTypeEnum.sd.name().equalsIgnoreCase(importMessage.getAdType())) {
                AmazonSdAdGroup amazonSdAdGroup = finalSdAdGroups.get(e.getAdGroupId());
                if (amazonSdAdGroup == null) {
                    log.error("pxq-report-import puid : {} shop_id : {} campaignId : {}, groupId:{} 不存在", puid, shopId, e.getCampaignId(), e.getAdGroupId());
                    return;
                }
                AmazonAdSdGroupReport sdReport = buildSdGroupReport(importMessage.getCountDate(), e, amazonSdAdGroup, amazonAdCampaignAll, shopAuth);
                sdReports.add(sdReport);
            }
        });


        //持久数据到数据库
        if (CollectionUtils.isNotEmpty(spReports)) {
            amazonAdGroupReportDao.insertList(puid, spReports);
        }

        if (CollectionUtils.isNotEmpty(sbReports)) {
            amazonAdSbGroupReportDao.insertOrUpdateList(puid, sbReports);
        }

        if (CollectionUtils.isNotEmpty(sdReports)) {
            amazonAdSdGroupReportDao.insertOrUpdateList(puid, sdReports);
        }
    }


    /**
     * 构建sd组报告
     *
     * @param report          报告
     * @param amazonSdAdGroup 亚马逊sd广告组
     * @param campaignAll     活动所有
     * @return {@link AmazonAdSdGroupReport}
     */
    private AmazonAdSdGroupReport buildSdGroupReport(String countDate, LxAmazonAdGroupReport report, AmazonSdAdGroup amazonSdAdGroup, AmazonAdCampaignAll campaignAll, ShopAuth shopAuth) {
        AmazonAdSdGroupReport sdGroupReport = new AmazonAdSdGroupReport();
        sdGroupReport.setPuid(shopAuth.getPuid());
        sdGroupReport.setShopId(shopAuth.getId());
        sdGroupReport.setMarketplaceId(shopAuth.getMarketplaceId());
        sdGroupReport.setCountDate(countDate);
        sdGroupReport.setTacticType(amazonSdAdGroup.getTactic());
        sdGroupReport.setCurrency(Marketplace.fromId(shopAuth.getMarketplaceId()).getCurrencyCode().name());
        sdGroupReport.setCampaignName(report.getCampaignName());
        sdGroupReport.setCampaignId(report.getCampaignId());
        sdGroupReport.setAdGroupName(amazonSdAdGroup.getName());
        sdGroupReport.setAdGroupId(amazonSdAdGroup.getAdGroupId());
        sdGroupReport.setSales14d(report.getSales() != null ? report.getSales() : BigDecimal.ZERO);
        sdGroupReport.setSales14dSameSKU(report.getDirectSales() != null ? report.getDirectSales() : BigDecimal.ZERO);
        sdGroupReport.setUnitsOrdered14d(report.getAdUnits() != null ? report.getAdUnits() : 0);
        sdGroupReport.setConversions14d(report.getOrders() != null ? report.getOrders() : 0);
        sdGroupReport.setConversions14dSameSKU(report.getDirectOrders() != null ? report.getDirectOrders() : 0);
        sdGroupReport.setClicks(report.getClicks() != null ? report.getClicks() : 0);
        sdGroupReport.setCost(report.getSpends() != null ? report.getSpends() : BigDecimal.ZERO);
        sdGroupReport.setImpressions(report.getImpressions() != null ? report.getImpressions() : 0);
        sdGroupReport.setViewImpressions(report.getViewImpressions() != null ? report.getViewImpressions() : 0);
        sdGroupReport.setCostType(campaignAll.getCostType());
        return sdGroupReport;
    }

    /**
     * 构建sb广告组报告
     *
     * @param report          报告
     * @param amazonSbAdGroup 亚马逊sb广告组
     * @return {@link AmazonAdSbGroupReport}
     */
    private AmazonAdSbGroupReport buildSbAdGroupReport(String countDate, LxAmazonAdGroupReport report, AmazonSbAdGroup amazonSbAdGroup, AmazonAdCampaignAll campaignAll, ShopAuth shopAuth) {
        AmazonAdSbGroupReport sbGroupReport = new AmazonAdSbGroupReport();
        sbGroupReport.setPuid(shopAuth.getPuid());
        sbGroupReport.setShopId(shopAuth.getId());
        sbGroupReport.setMarketplaceId(shopAuth.getMarketplaceId());
        sbGroupReport.setCountDate(countDate);
        sbGroupReport.setCurrency(Marketplace.fromId(shopAuth.getMarketplaceId()).getCurrencyCode().name());
        sbGroupReport.setAdFormat(Optional.ofNullable(amazonSbAdGroup.getAdFormat()).orElse("manual"));
        sbGroupReport.setCampaignName(campaignAll.getName());
        sbGroupReport.setCampaignId(report.getCampaignId());
        sbGroupReport.setAdGroupName(amazonSbAdGroup.getName());
        sbGroupReport.setAdGroupId(amazonSbAdGroup.getAdGroupId());


        sbGroupReport.setSales14d(report.getSales() != null ? report.getSales() : BigDecimal.ZERO);
        sbGroupReport.setSales14dSameSKU(report.getDirectSales() != null ? report.getDirectSales() : BigDecimal.ZERO);
        sbGroupReport.setConversions14d(report.getOrders() != null ? report.getOrders() : 0);
        sbGroupReport.setConversions14dSameSKU(report.getDirectOrders() != null ? report.getDirectOrders() : 0);

        sbGroupReport.setClicks(report.getClicks() != null ? report.getClicks() : 0);
        sbGroupReport.setCost(report.getSpends() != null ? report.getSpends() : BigDecimal.ZERO);
        sbGroupReport.setImpressions(report.getImpressions() != null ? report.getImpressions() : 0);

        sbGroupReport.setUnitsSold14d(report.getAdUnits() != null ? report.getAdUnits() : 0);

        //未定义
//        sbGroupReport.setDpv14d(isDxmNumeric(report.getDpv()) ? Integer.valueOf(report.getDpv()) : 0);
//
//        try {
//            sbGroupReport.setVideo5SecondViewRate(new BigDecimal(report.getVideo5SecondViewRate().replaceAll("%", "")).doubleValue());
//        } catch (Exception exception) {
//            log.error("buildSbAdGroupReport execute setVideo5SecondViewRate errorMsg: {}", exception.getMessage());
//        }
//        sbGroupReport.setVideo5SecondViews(isDxmNumeric(report.getVideo5SecondViews()) ? Integer.valueOf(report.getVideo5SecondViews()) : 0);
//        sbGroupReport.setVideoFirstQuartileViews(isDxmNumeric(report.getVideoFirstQuartileViews()) ? Integer.valueOf(report.getVideoFirstQuartileViews()) : 0);
//        sbGroupReport.setVideoMidpointViews(isDxmNumeric(report.getVideoMidpointViews()) ? Integer.valueOf(report.getVideoMidpointViews()) : 0);
//        sbGroupReport.setVideoThirdQuartileViews(isDxmNumeric(report.getVideoThirdQuartileViews()) ? Integer.valueOf(report.getVideoThirdQuartileViews()) : 0);
//        sbGroupReport.setVideoUnmutes(isDxmNumeric(report.getVideoUnmutes()) ? Integer.valueOf(report.getVideoUnmutes()) : 0);
//        sbGroupReport.setViewableImpressions(isDxmNumeric(report.getViewImpressions()) ? Integer.valueOf(report.getViewImpressions()) : null);
//        sbGroupReport.setVideoCompleteViews(isDxmNumeric(report.getVideoCompleteViews()) ? Integer.valueOf(report.getVideoCompleteViews()) : 0);
//
//
//        try {
//            sbGroupReport.setVctr(new BigDecimal(report.getVctr().replaceAll("%", "")).doubleValue());
//        } catch (Exception exception) {
//            log.error("buildSbAdGroupReport execute setVctr errorMsg: {}", exception.getMessage());
//        }
//
//        try {
//            sbGroupReport.setVtr(new BigDecimal(report.getVtr().replaceAll("%", "")).doubleValue());
//        } catch (Exception exception) {
//            log.error("buildSbAdGroupReport execute setVtr errorMsg: {}", exception.getMessage());
//        }

        return sbGroupReport;
    }

    /**
     * 构建sp广告组报告
     *
     * @param report 报告
     * @return {@link AmazonAdGroupReport}
     */
    private AmazonAdGroupReport buildSpAdGroupReport(String countDate, LxAmazonAdGroupReport report, AmazonAdGroup amazonSpAdGroup, AmazonAdCampaignAll campaignAll, ShopAuth shopAuth) {
        AmazonAdGroupReport amazonAdGroupReport = new AmazonAdGroupReport();
        amazonAdGroupReport.setPuid(shopAuth.getPuid());
        amazonAdGroupReport.setShopId(shopAuth.getId());
        amazonAdGroupReport.setMarketplaceId(shopAuth.getMarketplaceId());
        amazonAdGroupReport.setCountDate(countDate);
        amazonAdGroupReport.setCampaignId(report.getCampaignId());
        amazonAdGroupReport.setAdGroupId(report.getAdGroupId());
        amazonAdGroupReport.setAdGroupName(amazonSpAdGroup.getName());
        amazonAdGroupReport.setCampaignName(campaignAll.getName());

        amazonAdGroupReport.setTotalSales(report.getSales() != null ? report.getSales() : BigDecimal.ZERO);
        amazonAdGroupReport.setAdSales(report.getDirectSales() != null ? report.getDirectSales() : BigDecimal.ZERO);


        amazonAdGroupReport.setOrderNum(report.getAdUnits() != null ? report.getAdUnits() : 0);
        amazonAdGroupReport.setAdOrderNum(report.getDirectUnits() != null ? report.getDirectUnits() : 0);
        amazonAdGroupReport.setSaleNum(report.getOrders() != null ? report.getOrders() : 0);
        amazonAdGroupReport.setAdSaleNum(report.getDirectOrders() != null ? report.getDirectOrders() : 0);

        amazonAdGroupReport.setClicks(report.getClicks() != null ? report.getClicks() : 0);
        amazonAdGroupReport.setCost(report.getSpends() != null ? report.getSpends() : BigDecimal.ZERO);
        amazonAdGroupReport.setImpressions(report.getImpressions() != null ? report.getImpressions() : 0);

        return amazonAdGroupReport;
    }


}
