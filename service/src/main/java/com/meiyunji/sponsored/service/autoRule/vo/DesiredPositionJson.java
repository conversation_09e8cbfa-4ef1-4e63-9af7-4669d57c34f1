package com.meiyunji.sponsored.service.autoRule.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 广告数据条件
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class DesiredPositionJson implements Serializable {

    @JsonProperty("page")
    private Integer page;

    @JsonProperty("from")
    private Integer from;

    @JsonProperty("to")
    private Integer to;
}
