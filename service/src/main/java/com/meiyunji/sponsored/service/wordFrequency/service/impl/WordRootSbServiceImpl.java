package com.meiyunji.sponsored.service.wordFrequency.service.impl;


import com.google.common.collect.Lists;
import com.meiyunji.amazon.mws.base.AmznEndpoint;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.base.ProcessMsg;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.config.DynamicRefreshNacosConfiguration;
import com.meiyunji.sponsored.common.exception.BizServiceException;
import com.meiyunji.sponsored.common.springjdbc.PartitionSqlUtil;
import com.meiyunji.sponsored.common.support.StringRedisService;
import com.meiyunji.sponsored.common.util.*;
import com.meiyunji.sponsored.service.account.dao.IVcShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.account.po.VcShopAuth;
import com.meiyunji.sponsored.service.account.service.IShopAuthService;
import com.meiyunji.sponsored.service.common.qo.WordTranslateQo;
import com.meiyunji.sponsored.service.common.service.IWordTranslateService;
import com.meiyunji.sponsored.service.config.DynamicRefreshConfiguration;
import com.meiyunji.sponsored.service.cpc.dao.*;
import com.meiyunji.sponsored.service.cpc.po.*;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.doris.dao.IOdsCpcSbQueryKeywordReportDao;
import com.meiyunji.sponsored.service.doris.dao.IOdsAmazonWordRootQueryDao;
import com.meiyunji.sponsored.service.doris.dao.IOdsAmazonAdSbKeywordReportDao;
import com.meiyunji.sponsored.service.doris.dao.IOdsAmazonAdWordRootKeywordSbDao;
import com.meiyunji.sponsored.service.excel.excelTools.WriteHandlerBuild;
import com.meiyunji.sponsored.service.excel.excelTools.service.IExcelService;
import com.meiyunji.sponsored.service.wordFrequency.bo.WordBo;
import com.meiyunji.sponsored.service.wordFrequency.bo.WordRootSbPageBo;
import com.meiyunji.sponsored.service.wordFrequency.bo.WordRootTopBo;
import com.meiyunji.sponsored.service.wordFrequency.bo.WordRootTranslatorBo;
import com.meiyunji.sponsored.service.wordFrequency.dao.IAmazonAdWordRootKeywordSbDao;
import com.meiyunji.sponsored.service.wordFrequency.dao.IWordRootKeywordSbDao;
import com.meiyunji.sponsored.service.wordFrequency.dao.IWordRootQueryDao;
import com.meiyunji.sponsored.service.wordFrequency.dto.WordFrequencyAdMetricDto;
import com.meiyunji.sponsored.service.wordFrequency.dto.WordRootTopDto;
import com.meiyunji.sponsored.service.wordFrequency.enums.WordRoot;
import com.meiyunji.sponsored.service.wordFrequency.po.AmazonAdWordRootKeywordSb;
import com.meiyunji.sponsored.service.wordFrequency.po.AmazonAdWordRootKeywordSp;
import com.meiyunji.sponsored.service.wordFrequency.po.WordRootKeywordSb;
import com.meiyunji.sponsored.service.wordFrequency.po.WordRootQuery;
import com.meiyunji.sponsored.service.wordFrequency.qo.GetWordRootAggregateDataQo;
import com.meiyunji.sponsored.service.wordFrequency.qo.GetWordRootDataQo;
import com.meiyunji.sponsored.service.wordFrequency.qo.KeywordTopQo;
import com.meiyunji.sponsored.service.wordFrequency.qo.QueryWordTopQo;
import com.meiyunji.sponsored.service.wordFrequency.service.IWordRootSbService;
import com.meiyunji.sponsored.service.wordFrequency.service.helper.WordRootServiceHelper;
import com.meiyunji.sponsored.service.wordFrequency.vo.GetWordRootAggregateDataVo;
import com.meiyunji.sponsored.service.wordFrequency.vo.GetWordRootDataReportVo;
import com.meiyunji.sponsored.service.wordFrequency.vo.GetWordRootDataVo;
import com.meiyunji.sponsored.service.wordFrequency.vo.WordRootTopVo;
import com.meiyunji.sponsored.service.wordFrequency.service.helper.WordRootCalculateServiceHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: zhulukun
 * @email: <EMAIL>
 * @date: 2023-11-01  22:57
 */
@Slf4j
@Service
public class WordRootSbServiceImpl implements IWordRootSbService {

    @Resource
    private ICpcSbQueryKeywordReportDao iCpcSbQueryKeywordReportDao;

    @Resource
    private IWordRootKeywordSbDao iWordRootKeywordSbDao;

    @Resource
    private IWordRootQueryDao iWordRootQueryDao;

    @Autowired
    private IAmazonSbAdKeywordDao amazonSbAdKeywordDao;

    @Autowired
    private PartitionSqlUtil partitionSqlUtil;

    @Autowired
    private DynamicRefreshNacosConfiguration dynamicRefreshNacosConfiguration;

    @Autowired
    private IAmazonSbAdNeKeywordDao amazonSbAdNeKeywordDao;

    @Autowired
    private StringRedisService stringRedisService;

    @Autowired
    @Lazy
    private IExcelService excelService;

    @Autowired
    private IAmazonAdProfileDao amazonAdProfileDao;

    @Autowired
    private IShopAuthService shopAuthService;

    @Autowired
    private IAmazonAdWordRootKeywordSbDao amazonAdWordRootKeywordSbDao;

    @Autowired
    private IOdsAmazonAdSbKeywordReportDao odsAmazonAdSbKeywordReportDao;

    @Autowired
    private IOdsAmazonAdWordRootKeywordSbDao odsAmazonAdWordRootKeywordSbDao;

    @Resource
    private IAmazonSbAdKeywordDao iAmazonSbAdKeywordDao;

    @Resource
    private IAmazonAdWordRootKeywordSbDao iAmazonAdWordRootKeywordSbDao;
    
    @Autowired
    private IOdsCpcSbQueryKeywordReportDao odsCpcSbQueryKeywordReportDao;

    @Autowired
    private IOdsAmazonWordRootQueryDao odsAmazonWordRootQueryDao;

    @Autowired
    private IAmazonAdSbKeywordReportDao amazonAdSbKeywordReportDao;

    @Autowired
    private DynamicRefreshConfiguration dynamicRefreshConfiguration;
    @Autowired
    private IWordTranslateService wordTranslateService;

    @Resource
    private IVcShopAuthDao vcShopAuthDao;

    private final long FREQUENCY = 1;

    @Override
    public boolean wordFrequencyQueryHandler(ShopAuth shopAuth, Integer page) {
        List<CpcSbQueryKeywordReport> cpcSbQueryKeywordReportList = iCpcSbQueryKeywordReportDao.listSbKeywordReport(shopAuth.getPuid(), shopAuth.getId(), page);
        if (cpcSbQueryKeywordReportList.size() == 0) {
            return false;
        }
        List<String> queryList = cpcSbQueryKeywordReportList.stream().map(item -> item.getQuery()).collect(Collectors.toList());
        if (page != null) {
            // 计算全量
            insertOrUpdateWordRoot(queryList, cpcSbQueryKeywordReportList, shopAuth);
        } else {
            // 计算增量
            List<CpcSbQueryKeywordReport> cpcSbQueryKeywordTimeRangeReportList = iCpcSbQueryKeywordReportDao.listSbKeywordReportByTimeRange(shopAuth.getPuid(), shopAuth.getId(), 1);
            if (cpcSbQueryKeywordTimeRangeReportList.size() == 0) {
                return false;
            }
            insertOrUpdateWordRoot(queryList, cpcSbQueryKeywordTimeRangeReportList, shopAuth);
        }
        return true;
    }

    @Override
    public boolean wordFrequencyQueryNewHandler(ShopAuth shopAuth, Integer page) {
        List<WordBo> wordBoList = iCpcSbQueryKeywordReportDao.listWordBo(shopAuth.getPuid(), shopAuth.getId(), page);
        if (wordBoList.size() == 0) {
            return false;
        }
        if (page != null) {
            // 计算全量
            this.insertOrUpdateWordRootQueryData(null, wordBoList, shopAuth);
        } else {
            // 计算增量
            List<WordBo> wordBoListByTimeRange = iCpcSbQueryKeywordReportDao.listWordBoTimeRange(shopAuth.getPuid(), shopAuth.getId(), 1);
            if (wordBoListByTimeRange.size() == 0) {
                return false;
            }
            List<String> keywordTextList = wordBoList.stream().map(WordBo::getWord).distinct().collect(Collectors.toList());
            this.insertOrUpdateWordRootQueryData(keywordTextList, wordBoListByTimeRange, shopAuth);
        }
        return true;
    }

    @Override
    public boolean wordFrequencyKeywordHandler(ShopAuth shopAuth, Integer page) {
        List<AmazonSbAdKeyword> amazonAdKeywordList = iAmazonSbAdKeywordDao.listWordRootSbKeyword(shopAuth.getPuid(), shopAuth.getId(), page);
        if (amazonAdKeywordList.size() == 0) {
            return false;
        }
        List<String> keywordTextList = amazonAdKeywordList.stream().map(item -> item.getKeywordText()).collect(Collectors.toList());
        if (page != null) {
            // 计算全量
            insertOrUpdateWordRootKeyword(keywordTextList, amazonAdKeywordList, shopAuth);
        } else {
            // 计算增量
            List<AmazonSbAdKeyword> amazonAdKeywordListByTimeRange = iAmazonSbAdKeywordDao.listWordRootSbKeywordTimeRange(shopAuth.getPuid(), shopAuth.getId(), 1);
            if (amazonAdKeywordListByTimeRange.size() == 0) {
                return false;
            }
            insertOrUpdateWordRootKeyword(keywordTextList, amazonAdKeywordListByTimeRange, shopAuth);
        }
        return true;
    }

    private void insertOrUpdateWordRootKeyword(List<String> keywordTextList, List<AmazonSbAdKeyword> amazonAdKeywordList, ShopAuth shopAuth) {
        List<AmazonAdWordRootKeywordSb> wordRootKeywordSbList = new ArrayList<>();
        for (AmazonSbAdKeyword amazonAdKeyword : amazonAdKeywordList) {
            List<String> wordRootList = WordRootCalculateServiceHelper.getTop5WordRootList(amazonAdKeyword.getKeywordText(), keywordTextList, shopAuth.getMarketplaceId());
            for (String wordRoot : wordRootList) {
                //写新表
                AmazonAdWordRootKeywordSb amazonAdWordRootKeywordSb = WordRootCalculateServiceHelper.buildWordRootKeywordSb(amazonAdKeyword);
                amazonAdWordRootKeywordSb.setWordRoot(wordRoot);
                amazonAdWordRootKeywordSb.setWordFrequencyType(Arrays.asList(amazonAdWordRootKeywordSb.getWordRoot().split(" ")).size() > 1 ? WordRoot.WordFrequencyType.PHRASE.getType() : WordRoot.WordFrequencyType.SINGLE.getType());
                wordRootKeywordSbList.add(amazonAdWordRootKeywordSb);
            }
        }
        if (wordRootKeywordSbList.size() > 0) {
            try {
                iAmazonAdWordRootKeywordSbDao.batchInsertOrUpdateSbKeyword(shopAuth.getPuid(), wordRootKeywordSbList);
            } catch (Exception e) {
                log.error("", e);
            }
        }
    }

    @Override
    public boolean wordFrequencyKeywordNewHandler(ShopAuth shopAuth, Integer page) {
        List<WordBo> wordBoList = amazonAdSbKeywordReportDao.listWordBo(shopAuth.getPuid(), shopAuth.getId(), page);
        if (wordBoList.size() == 0) {
            return false;
        }
        if (page != null) {
            // 计算全量
            this.insertOrUpdateWordRootData(null, wordBoList, shopAuth);
        } else {
            // 计算增量
            List<WordBo> wordBoListByTimeRange = amazonAdSbKeywordReportDao.listWordBoTimeRange(shopAuth.getPuid(), shopAuth.getId(), 1);
            if (wordBoListByTimeRange.size() == 0) {
                return false;
            }
            List<String> keywordTextList = wordBoList.stream().map(WordBo::getWord).distinct().collect(Collectors.toList());
            this.insertOrUpdateWordRootData(keywordTextList, wordBoListByTimeRange, shopAuth);
        }
        return true;
    }

    private void insertOrUpdateWordRootQueryData(List<String> wordList, List<WordBo> wordBoList, ShopAuth shopAuth) {
        //词根计算
        List<WordBo> wordRootList = WordRootCalculateServiceHelper.generatingWordRoot(wordList, wordBoList, shopAuth);
        //是否存储所有单个词
        boolean saveSingleWord = dynamicRefreshConfiguration.verifyDorisPageByPuid(shopAuth.getPuid(), dynamicRefreshConfiguration.getSingleWordWhilePuids());
        //词根插入
        if (wordRootList.size() > 0) {
            List<List<WordBo>> listPartition = Lists.partition(wordRootList, Constants.WORD_ROOT_INSERT_PARTITION);
            int i = 0;
            for (List<WordBo> list : listPartition) {
                //填充关键词其他数据
                List<Long> ids = list.stream().map(e -> Long.parseLong(e.getWordId())).distinct().collect(Collectors.toList());
                Map<Long, CpcSbQueryKeywordReport> queryMap = iCpcSbQueryKeywordReportDao.wordListByIds(shopAuth.getPuid(), shopAuth.getId(), ids)
                        .stream().collect(Collectors.toMap(CpcSbQueryKeywordReport::getId, Function.identity()));
                List<WordRootQuery> insertList = new ArrayList<>();
                list.stream().filter(e -> queryMap.containsKey(Long.parseLong(e.getWordId()))).forEach(e -> {
                    CpcSbQueryKeywordReport cpcSbQueryKeywordReport = queryMap.get(Long.valueOf(e.getWordId()));
                    WordRootQuery wordRootQuery = WordRootCalculateServiceHelper.buildWordRootQueryReportSb(cpcSbQueryKeywordReport);
                    wordRootQuery.setWordRoot(e.getWord());
                    wordRootQuery.setWordFrequencyType(Arrays.asList(wordRootQuery.getWordRoot().split(" ")).size() > 1 ? WordRoot.WordFrequencyType.PHRASE.getType() : WordRoot.WordFrequencyType.SINGLE.getType());
                    insertList.add(wordRootQuery);
                    //切割成单个词根
                    if (saveSingleWord && WordRoot.WordFrequencyType.PHRASE.getType().equals(wordRootQuery.getWordFrequencyType())) {
                        Arrays.stream(wordRootQuery.getWordRoot().split(" ")).filter(StringUtils::isNotBlank).map(String::trim).distinct().forEach(w -> {
                            WordRootQuery singleWordRoot = WordRootCalculateServiceHelper.buildWordRootQueryReportSb(cpcSbQueryKeywordReport);
                            singleWordRoot.setWordRoot(w);
                            singleWordRoot.setWordFrequencyType(WordRoot.WordFrequencyType.SINGLE.getType());
                            insertList.add(singleWordRoot);
                        });
                    }
                });
                partitionSqlUtil.save(shopAuth.getPuid(), insertList, 0, dynamicRefreshNacosConfiguration.getShardRule2(), iWordRootQueryDao::batchInsertOrUpdateSbQuery);
            }
        }
    }

    /**
     * 计算并插入词根
     */
    private void insertOrUpdateWordRootData(List<String> wordList, List<WordBo> wordBoList, ShopAuth shopAuth) {
        //词根计算
        List<WordBo> wordRootList = WordRootCalculateServiceHelper.generatingWordRoot(wordList, wordBoList, shopAuth);
        //是否存储所有单个词
        boolean saveSingleWord = dynamicRefreshConfiguration.verifyDorisPageByPuid(shopAuth.getPuid(), dynamicRefreshConfiguration.getSingleWordWhilePuids());
        //词根插入
        if (wordRootList.size() > 0) {
            List<List<WordBo>> listPartition = Lists.partition(wordRootList, Constants.WORD_ROOT_INSERT_PARTITION);
            int i = 0;
            for (List<WordBo> list : listPartition) {
                //填充关键词其他数据
                List<String> keywordIds = list.stream().map(WordBo::getWordId).distinct().collect(Collectors.toList());
                Map<String, AmazonSbAdKeyword> keywordMap = amazonSbAdKeywordDao.wordListByKeywordIds(shopAuth.getPuid(), shopAuth.getId(), keywordIds)
                        .stream().collect(Collectors.toMap(AmazonSbAdKeyword::getKeywordId, Function.identity()));
                List<AmazonAdWordRootKeywordSb> insertList = new ArrayList<>();
                list.stream().filter(e -> keywordMap.containsKey(e.getWordId())).forEach(e -> {
                    AmazonSbAdKeyword amazonSbAdKeyword = keywordMap.get(e.getWordId());
                    AmazonAdWordRootKeywordSb amazonAdWordRootKeywordSb = WordRootCalculateServiceHelper.buildWordRootKeywordSb(amazonSbAdKeyword);
                    amazonAdWordRootKeywordSb.setWordRoot(e.getWord());
                    amazonAdWordRootKeywordSb.setWordFrequencyType(Arrays.asList(amazonAdWordRootKeywordSb.getWordRoot().split(" ")).size() > 1 ? WordRoot.WordFrequencyType.PHRASE.getType() : WordRoot.WordFrequencyType.SINGLE.getType());
                    insertList.add(amazonAdWordRootKeywordSb);
                    //切割成单个词根
                    if (saveSingleWord && WordRoot.WordFrequencyType.PHRASE.getType().equals(amazonAdWordRootKeywordSb.getWordFrequencyType())) {
                        Arrays.stream(amazonAdWordRootKeywordSb.getWordRoot().split(" ")).filter(StringUtils::isNotBlank).map(String::trim).distinct().forEach(w -> {
                            AmazonAdWordRootKeywordSb singleWordRoot = WordRootCalculateServiceHelper.buildWordRootKeywordSb(amazonSbAdKeyword);
                            singleWordRoot.setWordRoot(w);
                            singleWordRoot.setWordFrequencyType(WordRoot.WordFrequencyType.SINGLE.getType());
                            insertList.add(singleWordRoot);
                        });
                    }
                });
                partitionSqlUtil.save(shopAuth.getPuid(), insertList, 0, dynamicRefreshNacosConfiguration.getShardRule2(), iAmazonAdWordRootKeywordSbDao::batchInsertOrUpdateSbKeyword);
            }
        }
    }
    private void insertOrUpdateWordRoot(List<String> queryList, List<CpcSbQueryKeywordReport> cpcSbQueryKeywordReportList, ShopAuth shopAuth) {
        List<WordRootQuery> wordRootQueryList = new ArrayList<>();
        for (CpcSbQueryKeywordReport cpcSbQueryKeywordReport : cpcSbQueryKeywordReportList) {
            List<String> wordRootList = WordRootCalculateServiceHelper.getTop5WordRootList(cpcSbQueryKeywordReport.getQuery(), queryList, shopAuth.getMarketplaceId());
            for (String wordRoot : wordRootList) {
                WordRootQuery wordRootQuery = WordRootCalculateServiceHelper.buildWordRootQueryReportSb(cpcSbQueryKeywordReport);
                wordRootQuery.setWordRoot(wordRoot);
                wordRootQuery.setWordFrequencyType(Arrays.asList(wordRootQuery.getWordRoot().split(" ")).size() > 1 ? WordRoot.WordFrequencyType.PHRASE.getType() : WordRoot.WordFrequencyType.SINGLE.getType());
                wordRootQueryList.add(wordRootQuery);
            }
        }
        if (wordRootQueryList.size() > 0) {
            try {
                iWordRootQueryDao.batchInsertOrUpdateSbQuery(shopAuth.getPuid(), wordRootQueryList);
            } catch (Exception e) {
                log.error("", e);
            }
        }
    }

    @Override
    public Result<List<WordRootTopVo>> getQueryWordTopList(QueryWordTopQo qo) {
        //获取列表页所有query
        if (CollectionUtils.isNotEmpty(qo.getQueryWordTagTypeList())) {
            List<SearchQueryTagParam> queryTagParams = iCpcSbQueryKeywordReportDao.listAdGroupIdByQueryWordDto(qo);
            List<String> matchTypeList = Lists.newArrayList();
            for (String matchType : qo.getQueryWordTagTypeList()) {
                if ("isExact".equalsIgnoreCase(matchType)) {
                    matchTypeList.add(Constants.EXACT);
                }
                if ("isBroad".equalsIgnoreCase(matchType)) {
                    matchTypeList.add(Constants.BROAD);
                }
                if ("isPhrase".equalsIgnoreCase(matchType)) {
                    matchTypeList.add(Constants.PHRASE);
                }
                if ("isNegativeExact".equalsIgnoreCase(matchType)) {
                    matchTypeList.add(Constants.NEGATIVEEXACT);
                }
                if ("isNegativePhrase".equalsIgnoreCase(matchType)) {
                    matchTypeList.add(Constants.NEGATIVEPHRASE);
                }
            }
            if (CollectionUtils.isNotEmpty(queryTagParams)) {
                if (CollectionUtils.isNotEmpty(matchTypeList)) {
                    List<SearchQueryTagParam> searchQueryTagParamArrayList = Lists.newArrayList();
                    List<SearchQueryTagParam> searchQueryTagParamList = amazonSbAdKeywordDao.getSearchQueryTag(qo.getPuid(), qo.getShopId(), matchTypeList, queryTagParams);
                    List<SearchQueryTagParam> searchQueryTagParams = amazonSbAdNeKeywordDao.getSearchQueryTag(qo.getPuid(), qo.getShopId(), matchTypeList, queryTagParams);
                    if (CollectionUtils.isNotEmpty(searchQueryTagParamList)) {
                        searchQueryTagParamArrayList.addAll(searchQueryTagParamList);
                    }
                    if (CollectionUtils.isNotEmpty(searchQueryTagParams)) {
                        searchQueryTagParamArrayList.addAll(searchQueryTagParams);
                    }
                    if (CollectionUtils.isNotEmpty(searchQueryTagParamArrayList)) {
                        qo.setSearchQueryTagParamList(searchQueryTagParamArrayList);
                    } else {
                        return ResultUtil.success();
                    }
                } else {
                    return ResultUtil.success();
                }
            } else {
                return ResultUtil.success();
            }
        }
        List<String> queryIdList = iCpcSbQueryKeywordReportDao.listQueryIdByQueryWordDto(qo);
        if (CollectionUtils.isEmpty(queryIdList)) {
            return ResultUtil.success();
        }

        //根据query获取词根top
        List<WordRootTopVo> voList;
        if (queryIdList.size() > Constants.QUERY_WORD_ROOT_SIZE) {
            Vector<WordRootTopBo> boList = new Vector<>();
            List<List<String>> queryPartitionList = Lists.partition(queryIdList, Constants.QUERY_WORD_ROOT_SIZE);
            CountDownLatch countDownLatch = new CountDownLatch(queryPartitionList.size());
            ThreadPoolExecutor threadPoolExecutor = ThreadPoolUtil.getQueryWordRootPool();
            for (List<String> queryIds : queryPartitionList) {
                threadPoolExecutor.execute(() -> {
                    WordRootTopDto dto = new WordRootTopDto();
                    dto.setPuid(qo.getPuid());
                    dto.setShopId(qo.getShopId());
                    dto.setIdList(queryIds);
                    if (StringUtils.isNotBlank(qo.getWordFrequencyType())) {
                        dto.setWordFrequencyType(Integer.valueOf(qo.getWordFrequencyType()));
                    }
                    try {
                        boList.addAll(iWordRootKeywordSbDao.listByQueryList(dto));
                    } catch (Exception e) {
                        log.error(String.format("多线程获取SB词根数据异常: %s, puid: %d", e.getMessage(), qo.getPuid()), e);
                    } finally {
                        countDownLatch.countDown();
                    }
                });
            }
            try {
                countDownLatch.await();
            } catch (InterruptedException e) {
                throw new BizServiceException("获取SB词根数据异常，请联系管理员");
            }
            if (CollectionUtils.isEmpty(boList)) {
                return ResultUtil.success();
            }
            //按词根合并，并排序取topN条
            voList = boList.stream().collect(Collectors.groupingBy(WordRootTopBo::getWordRoot, Collectors.summingInt(WordRootTopBo::getCount)))
                    .entrySet().stream().sorted(Comparator.comparing(Map.Entry<String, Integer>::getValue).reversed())
                    .limit(qo.getTop()).map(e -> new WordRootTopVo(e.getKey(), e.getValue())).collect(Collectors.toList());
        } else {
            WordRootTopDto dto = new WordRootTopDto();
            dto.setPuid(qo.getPuid());
            dto.setShopId(qo.getShopId());
            dto.setTop(qo.getTop());
            dto.setIdList(queryIdList);
            if (StringUtils.isNotBlank(qo.getWordFrequencyType())) {
                dto.setWordFrequencyType(Integer.valueOf(qo.getWordFrequencyType()));
            }
            List<WordRootTopBo> boList = iWordRootKeywordSbDao.listByQueryList(dto);
            if (CollectionUtils.isEmpty(boList)) {
                return ResultUtil.success();
            }
            voList = boList.stream().map(e -> new WordRootTopVo(e.getWordRoot(), e.getCount())).collect(Collectors.toList());
        }

        return ResultUtil.success(voList);
    }

    @Override
    public Result<List<WordRootTopVo>> getDorisKeywordTopList(KeywordTopQo qo) {
        return ResultUtil.success(odsAmazonAdSbKeywordReportDao.getWordRootTopList(qo.getPuid(), qo));
    }

    @Override
    public Result<Page<GetWordRootDataVo>> getData(GetWordRootDataQo qo) {
        Page<GetWordRootDataVo> voPage = new Page<>(qo.getPageNo(), qo.getPageSize());
        //获取词根列表页数据
        Page<WordRootSbPageBo> page = iWordRootKeywordSbDao.pageList(qo);
        if (CollectionUtils.isEmpty(page.getRows())) {
            return ResultUtil.success(voPage);
        }
        VcShopAuth byId = vcShopAuthDao.getById(qo.getShopId());
        boolean isVc = byId != null;

        //获取占比信息
        WordFrequencyAdMetricDto adMetricDto = iWordRootKeywordSbDao.getPageListAdMetricDto(qo);
        List<GetWordRootDataVo> voList = new ArrayList<>();
        GetWordRootDataVo vo;
        for (WordRootSbPageBo bo : page.getRows()) {
            vo = new GetWordRootDataVo();
            vo.setWordRoot(bo.getWordRoot());
            vo.setWordRootCN(bo.getWordRootCn());
            //填充指标数据
            this.fillDataVo(qo.getShopSales(), vo, bo, isVc);
            WordRootServiceHelper.computeMetricData(adMetricDto, vo);
            voList.add(vo);
        }
        voPage.setPageNo(page.getPageNo());
        voPage.setPageSize(page.getPageSize());
        voPage.setTotalPage(page.getTotalPage());
        voPage.setTotalSize(page.getTotalSize());
        voPage.setRows(voList);

        return ResultUtil.success(voPage);
    }

    @Override
    public Result<GetWordRootAggregateDataVo> getAggregateData(GetWordRootAggregateDataQo qo) {
        GetWordRootAggregateDataVo aggregateDataVo = new GetWordRootAggregateDataVo();
        //获取词根列表页数据
        WordRootSbPageBo bo = iWordRootKeywordSbDao.getPageListAggregateData(qo);
        VcShopAuth byId = vcShopAuthDao.getById(qo.getShopId());
        boolean isVc = byId != null;

        //填充指标数据
        this.fillDataVo(qo.getShopSales(), aggregateDataVo, bo, isVc);
        //填充占比数据
        WordRootServiceHelper.fillMetricData(aggregateDataVo);
        return ResultUtil.success(aggregateDataVo);
    }

    @Override
    public List<GetWordRootDataVo> getChartData(GetWordRootDataQo qo) {
        //获取词根列表页数据
        Page<WordRootSbPageBo> page = iWordRootKeywordSbDao.pageList(qo);
        List<GetWordRootDataVo> voList = new ArrayList<>();
        GetWordRootDataVo vo;
        VcShopAuth byId = vcShopAuthDao.getById(qo.getShopId());
        boolean isVc = byId != null;

        for (WordRootSbPageBo bo : page.getRows()) {
            vo = new GetWordRootDataVo();
            vo.setWordRoot(bo.getWordRoot());
            //填充指标数据
            this.fillDataVo(qo.getShopSales(), vo, bo, isVc);
            voList.add(vo);
        }

        return voList;
    }

    @Override
    public void getExportData(String uuid, GetWordRootDataQo qo) {
        qo.setPageNo(1);
        qo.setPageSize(100);
        //获取词根列表页数据
        Page<GetWordRootDataVo> page = this.getData(qo).getData();
        if (CollectionUtils.isEmpty(page.getRows())) {
            stringRedisService.set(uuid, new ProcessMsg(-1, 0, "导出数据为空"));
            return;
        }

        int width = 0;
        String fileName = "词频分析列表页导出";
        int count = 0;
        String downloadUrl;
        List<GetWordRootDataReportVo> list = new ArrayList<>();
        List<String> urls = Lists.newLinkedList();
        int maxSize = Constants.FILE_MAX_SIZE;
        String currency = AmznEndpoint.getByMarketplaceId(qo.getMarketplaceId()).getCurrencyCode().value();
        List<String> voExcludeFields = Lists.newArrayList("adSaleNum", "adOtherOrderNum", "adSales", "adOtherSales", "orderNum", "orderNumPercentage", "adSelfSaleNum", "adOtherSaleNum");

        try {
            while (true) {
                list.addAll(WordRootServiceHelper.convertVoToReportVo(page.getRows(), currency));
                if (list.size() < maxSize) {
                    if (++width % 20 == 0) {
                        stringRedisService.set(uuid, new ProcessMsg(0, width, page.getTotalSize(), "导出中"));
                    }
                } else {
                    downloadUrl = excelService.easyExcelHandlerExport(qo.getPuid(), list, fileName + "(" + count++ + ")", GetWordRootDataReportVo.class, new WriteHandlerBuild(), voExcludeFields);
                    log.info("词频分析列表页导出downloadUrl:{}", downloadUrl);
                    list.clear();
                    urls.add(downloadUrl);
                }

                if (page.getPageNo() == page.getTotalPage()) {
                    if (list.size() > 0) {
                        downloadUrl = excelService.easyExcelHandlerExport(qo.getPuid(), list, fileName + "(" + count + ")", GetWordRootDataReportVo.class, new WriteHandlerBuild(), voExcludeFields);
                        log.info("词频分析列表页导出downloadUrl:{}", downloadUrl);
                        list.clear();
                        urls.add(downloadUrl);
                    }
                    stringRedisService.set(uuid, new ProcessMsg(1, width, page.getTotalSize(), "导出完成", urls));
                    break;
                }

                qo.setPageNo(page.getPageNo() + 1);
                page = this.getData(qo).getData();
            }
        } catch (Exception e) {
            log.error("词频分析列表页导出异常：", e);
            stringRedisService.set(uuid, new ProcessMsg(-1, 0, 0, "导出过程中出现异常"));
        }
    }

    @Override
    public Result<List<WordRootTopVo>> getDorisTopList(QueryWordTopQo qo) {
        return ResultUtil.success(odsCpcSbQueryKeywordReportDao.getWordRootToplist(qo));
    }

    @Override
    public Result<Page<GetWordRootDataVo>> getDorisData(GetWordRootDataQo qo, boolean isExport) {
        Page<GetWordRootDataVo> voPage = new Page<>(qo.getPageNo(), qo.getPageSize());
        //获取词根列表页数据
        Page<WordRootSbPageBo> page = odsAmazonWordRootQueryDao.sbPageList(qo.getPuid(), qo);
        List<WordRootSbPageBo> rows = page.getRows();
        if (CollectionUtils.isEmpty(page.getRows())) {
            return ResultUtil.success(voPage);
        }
        VcShopAuth byId = vcShopAuthDao.getById(qo.getShopId());
        boolean isVc = byId != null;

        //获取其他字段值
        page.setRows(odsAmazonWordRootQueryDao.sbPageListByWordRoots(qo.getPuid(), qo, rows.stream().map(WordRootSbPageBo::getWordRoot).collect(Collectors.toList())));
        //获取占比信息
        WordFrequencyAdMetricDto adMetricDto = odsAmazonWordRootQueryDao.getSbPageListAdMetricDto(qo.getPuid(), qo);
        //获取翻译词
        List<WordTranslateQo> wordTranslateQos = page.getRows().stream().map(e -> new WordTranslateQo(qo.getMarketplaceId(), e.getWordRoot())).collect(Collectors.toList());
        Map<String, String> wordTranslateMap = wordTranslateService.getWordTranslateMap(qo.getPuid(), wordTranslateQos, isExport);
        List<GetWordRootDataVo> voList = new ArrayList<>();
        GetWordRootDataVo vo;
        for (WordRootSbPageBo bo : page.getRows()) {
            vo = new GetWordRootDataVo();
            vo.setWordRoot(bo.getWordRoot());
            vo.setWordRootCN(wordTranslateMap.get(wordTranslateService.getWordTranslateKey(qo.getMarketplaceId(), vo.getWordRoot())));
            //填充指标数据
            this.fillDataVo(qo.getShopSales(), vo, bo, isVc);
            WordRootServiceHelper.computeMetricData(adMetricDto, vo);
            voList.add(vo);
        }
        voPage.setPageNo(page.getPageNo());
        voPage.setPageSize(page.getPageSize());
        voPage.setTotalPage(page.getTotalPage());
        voPage.setTotalSize(page.getTotalSize());
        voPage.setRows(voList);

        return ResultUtil.success(voPage);
    }

    @Override
    public Result<GetWordRootAggregateDataVo> getDorisAggregateData(GetWordRootAggregateDataQo qo) {
        GetWordRootAggregateDataVo aggregateDataVo = new GetWordRootAggregateDataVo();
        //获取词根列表页数据
        WordRootSbPageBo bo = odsAmazonWordRootQueryDao.getSbPageListAggregateData(qo.getPuid(), qo);
        VcShopAuth byId = vcShopAuthDao.getById(qo.getShopId());
        boolean isVc = byId != null;

        //填充指标数据
        this.fillDataVo(qo.getShopSales(), aggregateDataVo, bo, isVc);
        //填充占比数据
        WordRootServiceHelper.fillMetricData(aggregateDataVo);
        return ResultUtil.success(aggregateDataVo);
    }

    @Override
    public List<GetWordRootDataVo> getDorisChartData(GetWordRootDataQo qo) {
        //获取词根列表页数据
        Page<WordRootSbPageBo> page = odsAmazonWordRootQueryDao.sbPageList(qo.getPuid(), qo);
        if (CollectionUtils.isNotEmpty(page.getRows())) {
            page.setRows(odsAmazonWordRootQueryDao.sbPageListByWordRoots(qo.getPuid(), qo, page.getRows().stream().map(WordRootSbPageBo::getWordRoot).collect(Collectors.toList())));
        }
        VcShopAuth byId = vcShopAuthDao.getById(qo.getShopId());
        boolean isVc = byId != null;

        List<GetWordRootDataVo> voList = new ArrayList<>();
        GetWordRootDataVo vo;
        for (WordRootSbPageBo bo : page.getRows()) {
            vo = new GetWordRootDataVo();
            vo.setWordRoot(bo.getWordRoot());
            //填充指标数据
            this.fillDataVo(qo.getShopSales(), vo, bo, isVc);
            voList.add(vo);
        }

        return voList;
    }

    @Override
    public void getExportDorisData(String uuid, GetWordRootDataQo qo) {
        qo.setPageNo(1);
        qo.setPageSize(100);
        //获取词根列表页数据
        Page<GetWordRootDataVo> page = this.getDorisData(qo, true).getData();
        if (CollectionUtils.isEmpty(page.getRows())) {
            stringRedisService.set(uuid, new ProcessMsg(-1, 0, "导出数据为空"));
            return;
        }

        int width = 0;
        String fileName = "词频分析列表页导出";
        int count = 0;
        String downloadUrl;
        List<GetWordRootDataReportVo> list = new ArrayList<>();
        List<String> urls = Lists.newLinkedList();
        int maxSize = Constants.FILE_MAX_SIZE;
        String currency = AmznEndpoint.getByMarketplaceId(qo.getMarketplaceId()).getCurrencyCode().value();
        List<String> voExcludeFields = Lists.newArrayList("adSaleNum", "adOtherOrderNum", "adSales", "adOtherSales", "orderNum", "orderNumPercentage", "adSelfSaleNum", "adOtherSaleNum");

        try {
            while (true) {
                list.addAll(WordRootServiceHelper.convertVoToReportVo(page.getRows(), currency));
                if (list.size() < maxSize) {
                    if (++width % 20 == 0) {
                        stringRedisService.set(uuid, new ProcessMsg(0, width, page.getTotalSize(), "导出中"));
                    }
                } else {
                    downloadUrl = excelService.easyExcelHandlerExport(qo.getPuid(), list, fileName + "(" + count++ + ")", GetWordRootDataReportVo.class, new WriteHandlerBuild(), voExcludeFields);
                    log.info("词频分析列表页导出downloadUrl:{}", downloadUrl);
                    list.clear();
                    urls.add(downloadUrl);
                }

                if (page.getPageNo() == page.getTotalPage()) {
                    if (list.size() > 0) {
                        downloadUrl = excelService.easyExcelHandlerExport(qo.getPuid(), list, fileName + "(" + count + ")", GetWordRootDataReportVo.class, new WriteHandlerBuild(), voExcludeFields);
                        log.info("词频分析列表页导出downloadUrl:{}", downloadUrl);
                        list.clear();
                        urls.add(downloadUrl);
                    }
                    stringRedisService.set(uuid, new ProcessMsg(1, width, page.getTotalSize(), "导出完成", urls));
                    break;
                }

                qo.setPageNo(page.getPageNo() + 1);
                page = this.getDorisData(qo, true).getData();
            }
        } catch (Exception e) {
            log.error("词频分析列表页导出异常：", e);
            stringRedisService.set(uuid, new ProcessMsg(-1, 0, 0, "导出过程中出现异常"));
        }
    }

    @Override
    public Result<Page<GetWordRootDataVo>> getDorisKeywordData(GetWordRootDataQo qo, boolean isExport) {
        //获取列表页词根
        Page<GetWordRootDataVo> page = odsAmazonAdWordRootKeywordSbDao.pageList(qo.getPuid(), qo);
        List<GetWordRootDataVo> rows = page.getRows();
        if (CollectionUtils.isEmpty(rows)) {
            return ResultUtil.success(page);
        }
        VcShopAuth byId = vcShopAuthDao.getById(qo.getShopId());
        boolean isVc = byId != null;

        //获取其他字段值
        page.setRows(odsAmazonAdWordRootKeywordSbDao.pageListByWordRoots(qo.getPuid(), qo, rows.stream().map(GetWordRootDataVo::getWordRoot).collect(Collectors.toList())));

        //获取占比信息
        WordFrequencyAdMetricDto adMetricDto = odsAmazonAdWordRootKeywordSbDao.getPageListAdMetricDto(qo.getPuid(), qo);
        //获取翻译词
        List<WordTranslateQo> wordTranslateQos = page.getRows().stream().map(e -> new WordTranslateQo(qo.getMarketplaceId(), e.getWordRoot())).collect(Collectors.toList());
        Map<String, String> wordTranslateMap = wordTranslateService.getWordTranslateMap(qo.getPuid(), wordTranslateQos, isExport);
        for (GetWordRootDataVo vo : page.getRows()) {
            //填充指标数据
            this.fillDataVo(qo.getShopSales(), vo, isVc);
            WordRootServiceHelper.computeMetricData(adMetricDto, vo);
            vo.setWordRootCN(wordTranslateMap.get(wordTranslateService.getWordTranslateKey(qo.getMarketplaceId(), vo.getWordRoot())));
        }
        return ResultUtil.success(page);
    }

    @Override
    public Result<GetWordRootAggregateDataVo> getDorisKeywordAggregateData(GetWordRootAggregateDataQo qo) {
        //获取词根列表页数据
        GetWordRootAggregateDataVo aggregateDataVo = odsAmazonAdWordRootKeywordSbDao.getPageListAggregateData(qo.getPuid(), qo);
        VcShopAuth byId = vcShopAuthDao.getById(qo.getShopId());
        boolean isVc = byId != null;

        //填充指标数据
        this.fillDataVo(qo.getShopSales(), aggregateDataVo, isVc);
        //填充占比数据
        WordRootServiceHelper.fillMetricData(aggregateDataVo);
        return ResultUtil.success(aggregateDataVo);
    }

    @Override
    public List<GetWordRootDataVo> getDorisKeywordChartData(GetWordRootDataQo qo) {
        Page<GetWordRootDataVo> page = odsAmazonAdWordRootKeywordSbDao.pageList(qo.getPuid(), qo);
        if (CollectionUtils.isNotEmpty(page.getRows())) {
            page.setRows(odsAmazonAdWordRootKeywordSbDao.pageListByWordRoots(qo.getPuid(), qo, page.getRows().stream().map(GetWordRootDataVo::getWordRoot).collect(Collectors.toList())));
        }
        VcShopAuth byId = vcShopAuthDao.getById(qo.getShopId());
        boolean isVc = byId != null;

        for (GetWordRootDataVo vo : page.getRows()) {
            this.fillDataVo(qo.getShopSales(), vo, isVc);
        }

        return page.getRows();
    }

    @Override
    public void getExportDorisKeywordData(String uuid, GetWordRootDataQo qo) {
        qo.setPageNo(1);
        qo.setPageSize(Constants.WORD_ROOT_EXPORT_SIZE);
        //获取词根列表页数据
        Page<GetWordRootDataVo> page = this.getDorisKeywordData(qo, true).getData();
        if (CollectionUtils.isEmpty(page.getRows())) {
            stringRedisService.set(uuid, new ProcessMsg(-1, 0, "导出数据为空"));
            return;
        }

        int width = 0;
        String fileName = "词频分析列表页导出";
        int count = 0;
        String downloadUrl;
        List<GetWordRootDataReportVo> list = new ArrayList<>();
        List<String> urls = Lists.newLinkedList();
        int maxSize = Constants.FILE_MAX_SIZE;
        String currency = AmznEndpoint.getByMarketplaceId(qo.getMarketplaceId()).getCurrencyCode().value();

        try {
            while (true) {
                list.addAll(WordRootServiceHelper.convertVoToReportVo(page.getRows(), currency));
                if (list.size() < maxSize) {
                    if (++width % 20 == 0) {
                        stringRedisService.set(uuid, new ProcessMsg(0, width, page.getTotalSize(), "导出中"));
                    }
                } else {
                    downloadUrl = excelService.easyExcelHandlerExport(qo.getPuid(), list, fileName + "(" + count++ + ")", GetWordRootDataReportVo.class, new WriteHandlerBuild());
                    log.info("关键词词频分析列表页导出downloadUrl:{}", downloadUrl);
                    list.clear();
                    urls.add(downloadUrl);
                }

                if (page.getPageNo() == page.getTotalPage()) {
                    if (list.size() > 0) {
                        downloadUrl = excelService.easyExcelHandlerExport(qo.getPuid(), list, fileName + "(" + count + ")", GetWordRootDataReportVo.class, new WriteHandlerBuild());
                        log.info("关键词词频分析列表页导出downloadUrl:{}", downloadUrl);
                        list.clear();
                        urls.add(downloadUrl);
                    }
                    stringRedisService.set(uuid, new ProcessMsg(1, width, page.getTotalSize(), "导出完成", urls));
                    break;
                }

                qo.setPageNo(page.getPageNo() + 1);
                page = this.getDorisKeywordData(qo, true).getData();
            }
        } catch (Exception e) {
            log.error("关键词词频分析列表页导出异常：", e);
            stringRedisService.set(uuid, new ProcessMsg(-1, 0, 0, "导出过程中出现异常"));
        }
    }

    @Override
    public void queryWordRootTranslators(Integer puid, ShopAuth shopAuth, String startTime, String endTime) {
        AmazonAdProfile profile = amazonAdProfileDao.getProfile(puid, shopAuth.getId());
        int i = 1;
        while (true) {
            //获取需要翻译的词根
            List<WordRootTranslatorBo> wordRootTranslatorBos = iWordRootKeywordSbDao.listTranslatorBoByShopId(puid, shopAuth.getId(), startTime, endTime, Constants.WORD_ROOT_TRANSLATOR_SIZE);
            if (CollectionUtils.isEmpty(wordRootTranslatorBos)) {
                break;
            }
            Set<String> wordRoots = wordRootTranslatorBos.stream().filter(e -> StringUtils.isNotBlank(e.getWordRoot()))
                    .map(e -> StringUtils.trim(e.getWordRoot())).collect(Collectors.toSet());

            if (CollectionUtils.isNotEmpty(wordRoots)) {
                log.info("{}@{} execute word root translators task,sb keyword word root size: {} ", puid, shopAuth.getId(), wordRoots.size());
                String accessToken = shopAuthService.getAdToken(shopAuth);
                //调用亚马逊api接口翻译词根
                boolean isSucc = WordRootServiceHelper.getWordRootTranslatorApi(puid, shopAuth, profile.getProfileId(), accessToken, wordRoots, wordRootTranslatorBos);
                if (isSucc) {
                    //批量更新词根
                    List<WordRootTranslatorBo> updateList = wordRootTranslatorBos.stream().filter(e -> StringUtils.isNotBlank(e.getWordRootCn())).collect(Collectors.toList());
                    try {
                        iWordRootKeywordSbDao.batchUpdateWordRootCn(puid, updateList);
                    } catch (Exception e) {
                        String maxWordRoot = "";
                        String maxWordRootCn = "";
                        for (WordRootTranslatorBo bo : updateList) {
                            if (bo.getWordRootCn().length() > maxWordRootCn.length()) {
                                maxWordRootCn = bo.getWordRootCn();
                                maxWordRoot = bo.getWordRoot();
                            }
                        }
                        log.error(String.format("execute word root translators task error, puid: %d, shopId: %d, max length WordRoot: %s, max length WordRootCn: %s",
                                puid, shopAuth.getId(), maxWordRoot, maxWordRootCn), e);
                        break;
                    }
                } else {
                    if (i++ > 3) {
                        break;
                    }
                }
            } else {
                break;
            }
        }
    }

    @Override
    public void keywordWordRootTranslators(Integer puid, ShopAuth shopAuth, String startTime, String endTime) {
        AmazonAdProfile profile = amazonAdProfileDao.getProfile(puid, shopAuth.getId());
        int i = 1;
        while (true) {
            //获取需要翻译的词根
            List<WordRootTranslatorBo> wordRootTranslatorBos = amazonAdWordRootKeywordSbDao.listTranslatorBoByShopId(puid, shopAuth.getId(), startTime, endTime, Constants.WORD_ROOT_TRANSLATOR_SIZE);
            if (CollectionUtils.isEmpty(wordRootTranslatorBos)) {
                break;
            }
            Set<String> wordRoots = wordRootTranslatorBos.stream().filter(e -> StringUtils.isNotBlank(e.getWordRoot()))
                    .map(e -> StringUtils.trim(e.getWordRoot())).collect(Collectors.toSet());

            if (CollectionUtils.isNotEmpty(wordRoots)) {
                log.info("{}@{} execute keyword sb word root translators task, word root size: {} ", puid, shopAuth.getId(), wordRoots.size());
                String accessToken = shopAuthService.getAdToken(shopAuth);
                //调用亚马逊api接口翻译词根
                boolean isSucc = WordRootServiceHelper.getWordRootTranslatorApi(puid, shopAuth, profile.getProfileId(), accessToken, wordRoots, wordRootTranslatorBos);
                if (isSucc) {
                    //批量更新词根
                    List<WordRootTranslatorBo> updateList = wordRootTranslatorBos.stream().filter(e -> StringUtils.isNotBlank(e.getWordRootCn())).collect(Collectors.toList());
                    try {
                        amazonAdWordRootKeywordSbDao.batchUpdateWordRootCn(puid, updateList);
                    } catch (Exception e) {
                        String maxWordRoot = "";
                        String maxWordRootCn = "";
                        for (WordRootTranslatorBo bo : updateList) {
                            if (bo.getWordRootCn().length() > maxWordRootCn.length()) {
                                maxWordRootCn = bo.getWordRootCn();
                                maxWordRoot = bo.getWordRoot();
                            }
                        }
                        log.error(String.format("execute keyword sb word root translators task error, puid: %d, shopId: %d, max length WordRoot: %s, max length WordRootCn: %s",
                                puid, shopAuth.getId(), maxWordRoot, maxWordRootCn), e);
                        break;
                    }
                } else {
                    if (i++ > 3) {
                        break;
                    }
                }
            } else {
                break;
            }
        }
    }

    /**
     * 填充指标数据
     */
    private void fillDataVo(BigDecimal shopSales, GetWordRootAggregateDataVo vo, WordRootSbPageBo bo, boolean isVc) {
        bo.setCost((bo.getCost() != null) ? bo.getCost().setScale(2, RoundingMode.HALF_UP) : BigDecimal.ZERO);
        bo.setImpressions(bo.getImpressions() != null ? bo.getImpressions() : 0L);
        bo.setClicks(bo.getClicks() != null ? bo.getClicks() : 0);
        bo.setConversions14d(bo.getConversions14d() != null ? bo.getConversions14d() : 0);
        bo.setSales14d((bo.getSales14d() != null) ? bo.getSales14d().setScale(2, RoundingMode.HALF_UP) : BigDecimal.ZERO);
        vo.setFrequency(bo.getFrequency() != null ? bo.getFrequency() : 0);
        vo.setAdCost(bo.getCost().toString());
        vo.setImpressions(bo.getImpressions());
        vo.setClicks(bo.getClicks());
        vo.setAdOrderNum(bo.getConversions14d());
        vo.setAdSale(bo.getSales14d().toString());
        vo.setCtr(vo.getImpressions() == 0 ? "0.00" : DoubleUtil.divide(Double.valueOf(vo.getClicks()) * 100, Double.valueOf(vo.getImpressions()), 2).toString());
        vo.setCvr(vo.getClicks() == 0 ? "0.00" : DoubleUtil.divide(Double.valueOf(vo.getAdOrderNum()) * 100, Double.valueOf(vo.getClicks()), 2).toString());
        vo.setCpa(BigDecimal.valueOf(vo.getAdOrderNum()).compareTo(BigDecimal.ZERO) == 0 ? "0" : MathUtil.divide(bo.getCost(), BigDecimal.valueOf(vo.getAdOrderNum())).setScale(2, RoundingMode.HALF_UP).toString());
        vo.setAdCostPerClick(BigDecimal.valueOf(vo.getClicks()).compareTo(BigDecimal.ZERO) == 0 ? "0" : MathUtil.divide(bo.getCost(), BigDecimal.valueOf(vo.getClicks())).setScale(2, RoundingMode.HALF_UP).toString());
        vo.setAcos(MathUtil.multiply(bo.getSales14d().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : MathUtil.divide(bo.getCost(), bo.getSales14d()), BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP).toString());
        vo.setRoas(bo.getCost().compareTo(BigDecimal.ZERO) == 0 ? "0" : bo.getSales14d().divide(bo.getCost(), 2, RoundingMode.HALF_UP).toString());
        vo.setAcots(shopSales.compareTo(BigDecimal.ZERO) == 0 ? "0" : bo.getCost().multiply(BigDecimal.valueOf(100)).divide(shopSales, 2, RoundingMode.HALF_UP).toString());
        vo.setAsots(shopSales.compareTo(BigDecimal.ZERO) == 0 ? "0" : bo.getSales14d().multiply(BigDecimal.valueOf(100)).divide(shopSales, 2, RoundingMode.HALF_UP).toString());
        //sb没有的字段设置为0
        vo.setAdSaleNum(0);
        vo.setAdSelfSaleNum(0);
        vo.setAdSales("0");
        vo.setOrderNum(0);
        vo.setAdOtherOrderNum(MathUtil.subtractInteger(vo.getAdOrderNum(), vo.getAdSaleNum()));
        vo.setAdOtherSales(MathUtil.subtract(vo.getAdSale(), vo.getAdSales()));
        vo.setAdOtherSaleNum(MathUtil.subtractInteger(vo.getOrderNum(), vo.getAdSelfSaleNum()));
        if (isVc) {
            vo.setAcots("-");
            vo.setAsots("-");
        }
    }

    /**
     * 填充指标数据
     * @param shopSales
     * @param vo
     */
    private void fillDataVo(BigDecimal shopSales, GetWordRootAggregateDataVo vo, boolean isVc) {
        vo.setImpressions(vo.getImpressions() != null ? vo.getImpressions() : 0L);
        vo.setClicks(vo.getClicks() != null ? vo.getClicks() : 0);
        vo.setAdCost(vo.getAdCost() != null ? new BigDecimal(vo.getAdCost()).setScale(2, RoundingMode.HALF_UP).toString() : "0");
        vo.setAdOrderNum(vo.getAdOrderNum() != null ? vo.getAdOrderNum() : 0);
        vo.setAdSale(vo.getAdSale() != null ? new BigDecimal(vo.getAdSale()).setScale(2, RoundingMode.HALF_UP).toString() : "0");
        vo.setCpa((BigDecimal.valueOf(vo.getAdOrderNum()).compareTo(BigDecimal.ZERO) == 0) ? "0" : MathUtil.divide(new BigDecimal(vo.getAdCost()), new BigDecimal(vo.getAdOrderNum())).setScale(2, RoundingMode.HALF_UP).toString());
        vo.setAdCostPerClick((BigDecimal.valueOf(vo.getClicks()).compareTo(BigDecimal.ZERO) == 0) ? "0" : MathUtil.divide(new BigDecimal(vo.getAdCost()), BigDecimal.valueOf(vo.getClicks())).setScale(2, RoundingMode.HALF_UP).toString());
        vo.setCtr((vo.getImpressions() == 0) ? "0.00" : DoubleUtil.divide(Double.valueOf(vo.getClicks()) * 100, Double.valueOf(vo.getImpressions()), 2).toString());
        vo.setCvr((vo.getClicks() == 0) ? "0.00" : DoubleUtil.divide(Double.valueOf(vo.getAdOrderNum()) * 100, Double.valueOf(vo.getClicks()), 2).toString());
        BigDecimal adSale = new BigDecimal(vo.getAdSale());
        vo.setAcos((adSale.compareTo(BigDecimal.ZERO) == 0) ? "0" : MathUtil.multiply(MathUtil.divide(new BigDecimal(vo.getAdCost()), adSale), BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP).toString());
        vo.setRoas(new BigDecimal(vo.getAdCost()).compareTo(BigDecimal.ZERO) == 0 ? "0" : adSale.divide(new BigDecimal(vo.getAdCost()), 2, RoundingMode.HALF_UP).toString());
        vo.setAcots((shopSales.compareTo(BigDecimal.ZERO) == 0) ? "0" : new BigDecimal(vo.getAdCost()).multiply(BigDecimal.valueOf(100)).divide(shopSales, 2, RoundingMode.HALF_UP).toString());
        vo.setAsots((shopSales.compareTo(BigDecimal.ZERO) == 0) ? "0" : adSale.multiply(BigDecimal.valueOf(100)).divide(shopSales, 2, RoundingMode.HALF_UP).toString());
        //sb没有的字段设置为0
        vo.setAdSaleNum(0);
        vo.setAdSelfSaleNum(0);
        vo.setAdSales("0");
        vo.setOrderNum(0);
        vo.setAdOtherOrderNum(MathUtil.subtractInteger(vo.getAdOrderNum(), vo.getAdSaleNum()));
        vo.setAdOtherSales(MathUtil.subtract(vo.getAdSale(), vo.getAdSales()));
        vo.setAdOtherSaleNum(MathUtil.subtractInteger(vo.getOrderNum(), vo.getAdSelfSaleNum()));
        if (isVc) {
            vo.setAcots("-");
            vo.setAsots("-");

        }
    }

}

