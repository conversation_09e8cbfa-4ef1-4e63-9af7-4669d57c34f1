package com.meiyunji.sponsored.service.cpc.dto;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import lombok.Data;

import java.time.LocalDate;
import java.util.List;

/**
 * 小时级 查询对象
 */
@Data
public class FeedHourlySelectDTO {

    private String sellerId;
    private String marketplaceId;
    private LocalDate start;
    private LocalDate end;
    private List<String> campaignIds;
    private List<Integer> weekdayList;
    private List<String> adGroupIds;
    private List<String> adIds;
    private String type;
    private Integer puid;
    private Integer shopId;
    private Integer isCompare;
    private LocalDate startCompare;
    private LocalDate endCompare;
    private List<String> excludeCampaignIds;
    private List<String> keywordIds;
    private List<String> placements;
    private List<ShopAuth> shopAuths;
    private List<String> sellerIdList;
    private List<String> marketplaceIdList;
    private List<String> concatWsList;
}
