package com.meiyunji.sponsored.service.multiPlatform.walmart.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @author: ys
 * @date: 2025/2/28 13:36
 * @describe:
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class WalmartGroupGetAllReq {
    private Integer puid;
    private List<Integer> shopIdList;
    private List<String> campaignIdList;
    private String targetType;
    private String searchValue;
    private Integer pageNo;
    private Integer pageSize;
}
