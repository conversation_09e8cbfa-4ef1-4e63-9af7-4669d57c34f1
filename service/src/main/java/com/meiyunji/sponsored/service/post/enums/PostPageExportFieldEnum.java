package com.meiyunji.sponsored.service.post.enums;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: heqiwen
 * @Date: 2025/04/08 15:20
 * @Description: 帖子导出字段枚举
 */
public enum PostPageExportFieldEnum {
    STATUS("status", "状态"),
    SHOP_NAME("shopName", "店铺"),
    BRAND_NAME("brandName", "品牌"),
    CAPTION("caption", "帖子标题"),
    ASIN("asinList", "ASIN"),
    LIVE_DATE("liveDate", "发布时间"),
    IMPRESSIONS("impressions", "可见展示次数"),
    CLICKS("clicks", "点击量"),
    CTR("ctr", "点击率"),
    CLICKS_TO_BRAND_STORE("clicksToBrandStore", "品牌旗舰店点击量"),
    CLICKS_TO_DETAIL_PAGE("clicksToDetailPage", "商品详情页点击量"),
    CLICKS_TO_FOLLOW("clicksToFollow", "关注量"),
    REACH("reach", "用户触达数"),
    POST_CREATED_DATE("postCreatedDate", "创建时间"),
    CREATOR("creator", "创建人"),
    REMARK("remark", "备注");

    private static final List<String> PO_PARAM_KEY_LIST = Collections.unmodifiableList(
            Arrays.stream(values())
                    .map(PostPageExportFieldEnum::getPoParamKey)
                    .collect(Collectors.toList())
    );

    /**
     * 后端po属性名
     */
    private final String poParamKey;
    private final String tableColName;

    PostPageExportFieldEnum(String poParamKey, String tableColName) {
        this.poParamKey = poParamKey;
        this.tableColName = tableColName;
    }

    public static List<String> getPoParamKeyList() {
        return PO_PARAM_KEY_LIST;
    }

    public static PostPageExportFieldEnum fromPoParamKey(String key) {
        for (PostPageExportFieldEnum value : values()) {
            if (value.poParamKey.equals(key)) {
                return value;
            }
        }
        return null; // Return null if no match is found
    }

    public String getPoParamKey() {
        return poParamKey;
    }

    public String getTableColName() {
        return tableColName;
    }
}
