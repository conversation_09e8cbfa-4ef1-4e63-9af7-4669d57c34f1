package com.meiyunji.sponsored.service.batchCreate.adStructure;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Table;
import com.meiyunji.sponsored.rpc.batchCreate.spBatchCreate.*;
import com.meiyunji.sponsored.service.batchCreate.dto.adstructure.AdStructureCampaignTypeDto;
import com.meiyunji.sponsored.service.batchCreate.dto.adstructure.AdStructureGroupTypeDto;
import com.meiyunji.sponsored.service.batchCreate.enums.AdStructureBeanId;
import com.meiyunji.sponsored.service.batchCreate.enums.AdStructureLevelEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Objects;

/**
 * @author: sunlinfeng
 * @email: <EMAIL>
 * @date: 2023-11-13  19:49
 */

/**
 * 精细化2广告结构：主力产品大词高预算
 */

@Component(AdStructureBeanId.COMPLEX_2)
public class Complex2AdStructure extends AdStructure {

    /**
     * 精细化2广告结构json数据：主力产品大词高预算
     * 也可以直接使用AdStructureCampaignTypeDto进行封装
     * 1个自动广告活动，活动下1个广告组(自动)
     * 1个手动广告活动，活动下1个关键词投放广告组且为精确匹配
     * 1个手动广告活动，活动下1个关键词投放广告组且为广泛匹配
     * 1个手动广告活动，活动下1个关键词投放广告组且为词组匹配
     * 1个手动广告活动，活动下1个商品投放广告组且为精确匹配
     * 1个手动广告活动，活动下1个商品投放广告组且为拓展匹配
     */
    private String complex2Json = "[{\"targetingType\":\"auto\",\"groupTypeList\":[{\"type\":\"auto\",\"matchType\":\"auto\"}]},{\"targetingType\":\"manual\",\"groupTypeList\":[{\"type\":\"keyword\",\"matchType\":\"exact\"}]},{\"targetingType\":\"manual\",\"groupTypeList\":[{\"type\":\"keyword\",\"matchType\":\"broad\"}]},{\"targetingType\":\"manual\",\"groupTypeList\":[{\"type\":\"keyword\",\"matchType\":\"phrase\"}]},{\"targetingType\":\"manual\",\"groupTypeList\":[{\"type\":\"targeting\",\"matchType\":\"asinSameAs\"}]},{\"targetingType\":\"manual\",\"groupTypeList\":[{\"type\":\"targeting\",\"matchType\":\"asinExpandedFrom\"}]}]";

    @PostConstruct
    @Override
    public void init() {
        STRUCTURE_JSON = complex2Json;
        super.init();
    }

    @Override
    public List<SpBatchCreatePreviewVo> generatePreview(List<SpBatchCreatePreviewBatchDataRequest> batchDataRequestList) {
        return super.generatePreview(batchDataRequestList);
    }

    @Override
    public boolean check(SpBatchCreateSubmitRequest request) {
        //return super.check(request);
        //2024.11.27：不需要check，由用户随便怎么造
        return true;
    }
    
}
