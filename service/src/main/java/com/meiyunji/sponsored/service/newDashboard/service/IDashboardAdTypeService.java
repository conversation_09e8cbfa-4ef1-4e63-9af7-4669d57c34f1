package com.meiyunji.sponsored.service.newDashboard.service;

import com.meiyunji.sponsored.rpc.newDashboard.DashboardAdTypeResponseVo;
import com.meiyunji.sponsored.service.newDashboard.vo.DashboardAdTypeReqVo;

import java.util.List;

/**
 * @author: sun<PERSON><PERSON>
 * @email: sunin<PERSON>@dianxiaomi.com
 * @date: 2024-03-25  16:46
 */
public interface IDashboardAdTypeService {
    List<DashboardAdTypeResponseVo> queryAdTypeCharts(DashboardAdTypeReqVo reqVo);

    List<String> exportAdTypeCharts(DashboardAdTypeReqVo reqVo);
}
