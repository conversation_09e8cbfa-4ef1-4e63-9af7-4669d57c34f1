package com.meiyunji.sponsored.service.newDashboard.service;

import com.meiyunji.sponsored.rpc.newDashboard.DashboardAdQueryWordMatrixRequest;
import com.meiyunji.sponsored.rpc.newDashboard.DashboardAdQueryWordMatrixSingleVo;
import com.meiyunji.sponsored.rpc.newDashboard.DashboardAdQueryWordMatrixVo;

/**
 * @author: ys
 * @date: 2024/4/26 16:39
 * @describe:
 */
public interface IDashboardAdQueryWordMatrixService {
    DashboardAdQueryWordMatrixVo getQueryWordMatrix(DashboardAdQueryWordMatrixRequest req);
    DashboardAdQueryWordMatrixSingleVo getQueryWordMatrixSingle(DashboardAdQueryWordMatrixRequest req);
}
