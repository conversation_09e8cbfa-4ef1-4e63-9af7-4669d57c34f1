package com.meiyunji.sponsored.service.batchCreate.adStructure;

import com.alibaba.fastjson.JSON;
import com.meiyunji.sponsored.rpc.batchCreate.spBatchCreate.SpBatchCreatePreviewBaseInfoRequest;
import com.meiyunji.sponsored.rpc.batchCreate.spBatchCreate.SpBatchCreatePreviewBatchDataRequest;
import com.meiyunji.sponsored.rpc.batchCreate.spBatchCreate.SpBatchCreatePreviewVo;
import com.meiyunji.sponsored.rpc.batchCreate.spBatchCreate.SpBatchCreateSubmitRequest;
import com.meiyunji.sponsored.service.batchCreate.dto.adstructure.AdStructureCampaignTypeDto;
import com.meiyunji.sponsored.service.batchCreate.enums.AdStructureBeanId;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.List;

/**
 * @author: sunlin<PERSON>
 * @email: <EMAIL>
 * @date: 2023-11-13  19:49
 */

/**
 * 自定义广告结构
 */

@Component(AdStructureBeanId.CUSTOM)
public class CustomAdStructure extends AdStructure {

    /**
     * 自定义广告结构json数据，初始化结构和简约版2一样：自定义结构
     * 也可以直接使用AdStructureCampaignTypeDto进行封装
     * 1个自动广告活动，活动下1个广告组(自动)
     * 1个手动广告活动，活动下1个关键词投放广告组
     * 1个手动广告活动，活动下1个商品投放广告组
     */
    private String customJson = "[{\"targetingType\":\"auto\",\"groupTypeList\":[{\"type\":\"auto\",\"matchType\":\"auto\"}]},{\"targetingType\":\"manual\",\"groupTypeList\":[{\"type\":\"keyword\",\"matchType\":\"\"}]},{\"targetingType\":\"manual\",\"groupTypeList\":[{\"type\":\"targeting\",\"matchType\":\"\"}]}]";

    @PostConstruct
    @Override
    public void init() {
        STRUCTURE_JSON = customJson;
        structureCampaignTypeDtoList = JSON.parseArray(customJson, AdStructureCampaignTypeDto.class);
    }

    @Override
    public List<SpBatchCreatePreviewVo> generatePreview(List<SpBatchCreatePreviewBatchDataRequest> batchDataRequestList) {
        return super.generatePreview(batchDataRequestList);
    }

    @Override
    public boolean check(SpBatchCreateSubmitRequest request) {
        //自定义结构随便造，没有限制
        return true;
    }
    
}
