package com.meiyunji.sponsored.service.newDashboard.service.globalView;

import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.util.StreamUtil;
import com.meiyunji.sponsored.service.account.service.IUserService;
import com.meiyunji.sponsored.service.doris.dao.globalView.IGlobalViewDao;
import com.meiyunji.sponsored.service.newDashboard.dto.globalView.GlobalViewSalesmanDto;
import com.meiyunji.sponsored.service.newDashboard.dto.globalView.base.GlobalViewBaseDataDto;
import com.meiyunji.sponsored.service.newDashboard.enums.globalView.GlobalViewConstant;
import com.meiyunji.sponsored.service.newDashboard.enums.globalView.GlobalViewProcessorEnum;
import com.meiyunji.sponsored.service.newDashboard.vo.globalView.GlobalViewBaseReqVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 全局概览-业务员列表
 *
 * @author: zzh
 * @create: 2024-10-18 10:05
 */
@Slf4j
@Service
public class GlobalViewSalesmanProcessor extends AbstractGlobalViewProcessor<GlobalViewBaseReqVo, GlobalViewSalesmanDto> {

    @Resource
    private IGlobalViewDao globalViewDao;

    @Resource
    private IUserService userService;

    @Override
    public Page<GlobalViewSalesmanDto> getPageData(GlobalViewBaseReqVo req) {
        // 分页获取原始指标数据
        Page<GlobalViewBaseDataDto> page = globalViewDao.getPageData(req, GlobalViewProcessorEnum.SALESMAN);
        if (CollectionUtils.isEmpty(page.getRows())) {
            return new Page<>(req.getPageNo(), req.getPageSize());
        }
        // key值 业务员id集合
        List<Integer> devIdList = StreamUtil.toList(page.getRows(), it -> Integer.parseInt(it.getKey()));
        // 获取业务员信息
        Map<String, String> nameIdMap = userService.getUserNameIdMap(devIdList);
        // 构建列表返回参数
        List<GlobalViewSalesmanDto> salesmanList = buildSalesman(page, nameIdMap);
        return new Page<>(page.getPageNo(), page.getPageSize(), page.getTotalPage(), page.getTotalSize(), salesmanList);
    }

    @Override
    public GlobalViewBaseDataDto getSumData(GlobalViewBaseReqVo req, List<GlobalViewSalesmanDto> dataList) {
        // 业务员无需统计店铺销售额数据
        return globalViewDao.getSumData(req, GlobalViewProcessorEnum.SALESMAN);
    }

    @Override
    public List<GlobalViewSalesmanDto> getMomData(GlobalViewBaseReqVo req, List<GlobalViewSalesmanDto> dataList) {
        return getMomYoyData(req, req.getMomStartDate(), req.getMomEndDate(), dataList);
    }

    @Override
    public List<GlobalViewSalesmanDto> getYoyData(GlobalViewBaseReqVo req, List<GlobalViewSalesmanDto> dataList) {
        return getMomYoyData(req, req.getYoyStartDate(), req.getYoyEndDate(), dataList);
    }

    @Override
    public List<String> getExportHeader() {
        List<String> headerList = new ArrayList<>(GlobalViewConstant.baseHeaderList);
        headerList.add(0, "salesman");
        // 业务员无需统计店铺销售额数据
        List<String> shopList = new ArrayList<>(GlobalViewConstant.shopHeaderList);
        headerList = headerList.stream().filter(x -> !shopList.contains(x)).collect(Collectors.toList());
        return headerList;
    }

    @Override
    public List<GlobalViewSalesmanDto> getExportData(List<GlobalViewSalesmanDto> dataList) {
        return dataList;
    }

    /**
     * 构建列表返回参数
     */
    private static List<GlobalViewSalesmanDto> buildSalesman(Page<GlobalViewBaseDataDto> page, Map<String, String> nameIdMap) {
        List<GlobalViewSalesmanDto> salesmanList = new ArrayList<>();
        for (GlobalViewBaseDataDto row : page.getRows()) {
            GlobalViewSalesmanDto date = new GlobalViewSalesmanDto();
            BeanUtils.copyProperties(row, date);
            // 业务员信息
            date.setDevId(Integer.valueOf(row.getKey()));
            date.setSalesman(nameIdMap.get(row.getKey()));
            salesmanList.add(date);
        }
        return salesmanList;
    }

    /**
     * 根据时间获取同环比原始指标数据
     */
    private List<GlobalViewSalesmanDto> getMomYoyData(GlobalViewBaseReqVo req, String startDate, String endDate, List<GlobalViewSalesmanDto> dataList) {
        List<GlobalViewSalesmanDto> list = new ArrayList<>();
        List<Integer> devIdList = StreamUtil.toList(dataList, GlobalViewSalesmanDto::getDevId);
        req.setDevIdList(devIdList);
        List<GlobalViewBaseDataDto> listData = globalViewDao.getListData(req, GlobalViewProcessorEnum.SALESMAN, startDate, endDate);
        Map<String, GlobalViewBaseDataDto> dataMap = StreamUtil.toMap(listData, GlobalViewBaseDataDto::getKey);
        for (GlobalViewSalesmanDto dto : dataList) {
            GlobalViewSalesmanDto salesmanDto = new GlobalViewSalesmanDto();
            GlobalViewBaseDataDto data = dataMap.get(dto.getKey());
            if (data != null) {
                BeanUtils.copyProperties(data, salesmanDto);
            }
            salesmanDto.setKey(dto.getKey());
            list.add(salesmanDto);
        }
        return list;
    }
}
