package com.meiyunji.sponsored.service.cpc.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

@Data
public class AllSearchTermExcelVO {

    @ExcelProperty(value = "用户搜索词")
    private String query;
    @ExcelProperty(value = "用户搜索词翻译")
    private String queryCn;
    @ExcelProperty(value = "出现次数")
    private Integer occurrenceNum;
    @ExcelProperty(value = "广告订单量")
    private Integer orderNum;
    @ExcelProperty(value = "广告转换率")
    private String salesConversionRate;
    @ExcelProperty(value = "ABA搜索词排名")
    private String searchFrequencyRank;
    @ExcelProperty(value = "广告花费")
    private String cost;
    @ExcelProperty(value = "广告曝光量")
    private Integer impressions;
    @ExcelProperty(value = "广告点击量")
    private Integer clicks;
    @ExcelProperty(value = "CPC")
    private String cpc;
    @ExcelProperty(value = "CPA")
    private String cpa;
    @ExcelProperty(value = "ACoS")
    private String acos;
    @ExcelProperty(value = "ROAS")
    private String roas;
    @ExcelProperty(value = "广告销售额")
    private String sales;
    @ExcelProperty(value = "本广告产品销售额")
    private String adSales;
    @ExcelProperty(value = "其它广告产品销售额")
    private String adOtherSales;

}
