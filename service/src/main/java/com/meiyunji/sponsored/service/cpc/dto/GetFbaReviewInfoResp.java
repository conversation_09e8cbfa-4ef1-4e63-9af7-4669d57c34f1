package com.meiyunji.sponsored.service.cpc.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 查询fba可售数据resp
 *
 * @Author: hejh
 * @Date: 2024/9/2 16:17
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class GetFbaReviewInfoResp implements Serializable {

    /**
     * 响应代码，默认情况下 0 表示成功。
     */
    @JsonProperty("code")
    private Integer code;

    /**
     * 响应消息，用于描述错误信息或其他提示信息。
     */
    @JsonProperty("msg")
    private String msg;

    /**
     * 产品数据列表，包含每个产品的详细信息。
     */
    @JsonProperty("data")
    private List<FbaInfo> data;

    /**
     * Product 类表示单个产品的详细信息。
     */
    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class FbaInfo {

        /**
         * MSKU，产品的唯一标识符。
         */
        @JsonProperty("msku")
        private String msku;

        /**
         * 店铺 ID，表示产品所属的店铺。
         */
        @JsonProperty("shopId")
        private Integer shopId;

        /**
         * 产品标题。
         */
        @JsonProperty("title")
        private String title;

        /**
         * 币种，表示价格的货币单位。
         */
        @JsonProperty("currency")
        private String currency;

        /**
         * 标准价，产品的标准定价。
         */
        @JsonProperty("standardPrice")
        private Double standardPrice;

        /**
         * 优惠价，产品的折扣价。
         */
        @JsonProperty("listingPricing")
        private Double listingPricing;

        /**
         * 价格，通常为优惠价，如果没有优惠则为标准价。
         */
        @JsonProperty("price")
        private Double price;

        /**
         * FBA 可售库存数量。
         */
        @JsonProperty("available")
        private Integer available;

        /**
         * 待调仓库存数量。
         */
        @JsonProperty("reservedTransfer")
        private Integer reservedTransfer;

        /**
         * 调仓中库存数量。
         */
        @JsonProperty("reservedProcessing")
        private Integer reservedProcessing;

        /**
         * 待发货订单的库存数量。
         */
        @JsonProperty("reservedCustomerorders")
        private Integer reservedCustomerorders;

        /**
         * 计划入库的库存数量。
         */
        @JsonProperty("inboundWorking")
        private Integer inboundWorking;

        /**
         * 在途库存数量，即从供应商到达仓库的途中。
         */
        @JsonProperty("inboundShipped")
        private Integer inboundShipped;

        /**
         * 入库中库存数量，即正在接收和入库的库存。
         */
        @JsonProperty("inboundReceiving")
        private Integer inboundReceiving;

        /**
         * 不可售库存数量，可能由于损坏或其他原因无法销售。
         */
        @JsonProperty("unfulfillable")
        private Integer unfulfillable;

        /**
         * 调查中的库存数量，可能在检查质量或其他调查过程中。
         */
        @JsonProperty("research")
        private Integer research;

        /**
         * 总库存数量，计算公式：总库存 = 可售 + 待调仓 + 调仓中 + 待发货 + 在途 + 入库中。
         */
        @JsonProperty("totalInventory")
        private Integer totalInventory;

        /**
         * 产品评分，通常是用户对产品的评价分数。
         */
        @JsonProperty("rating")
        private String rating;

        /**
         * 评论数，表示该产品的用户评价数量。
         */
        @JsonProperty("ratingCount")
        private Integer ratingCount;
    }
}
