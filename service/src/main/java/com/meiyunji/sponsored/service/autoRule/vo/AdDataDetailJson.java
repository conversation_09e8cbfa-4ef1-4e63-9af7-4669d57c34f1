package com.meiyunji.sponsored.service.autoRule.vo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class AdDataDetailJson implements Serializable {
    @JsonProperty("ruleIndexList")
    private List<RuleIndexJson> ruleIndexList;
    @JsonProperty("originalValue")
    private String originalValue;
    @JsonProperty("executeValue")
    private String executeValue;
    @JsonProperty("operatorType")
    private String operatorType;
    @JsonProperty("adjustType")
    private String adjustType;
    @JsonProperty("adjustValue")
    private String adjustValue;
}
