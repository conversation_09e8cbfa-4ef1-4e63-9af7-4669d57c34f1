package com.meiyunji.sponsored.service.autoRule.enums;

/**
 * @author: sun<PERSON><PERSON>
 * @email: <EMAIL>
 * @date: 2024-06-12  13:55
 */
public enum AutoRuleTypeEnum {

    customRule("customRule", "自定义规则"),
    keywordCard("keywordCard", "抢排名"),
    keywordAcosRaisePrice("keywordAcosRaisePrice", "投放ACoS低自动提价"),
    campaignOverBudgetRaiseBudget("campaignOverBudgetRaiseBudget", "广告活动超预算自动增加预算"),
    searchQueryAutoUpdateBidding("searchQueryAutoUpdateBidding", "搜索词自动添加到否定投放"),
    keywordAcosLowerPrice("keywordAcosLowerPrice", "投放ACoS高自动降价"),
    campaignAcosRaiseBudget("campaignAcosRaiseBudget", "广告活动ACoS低自动增加预算");

    private String value;
    private String desc;

    AutoRuleTypeEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public String getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

}
