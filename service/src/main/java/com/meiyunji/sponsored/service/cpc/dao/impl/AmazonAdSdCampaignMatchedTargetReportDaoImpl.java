package com.meiyunji.sponsored.service.cpc.dao.impl;

import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.config.DynamicRefreshNacosConfiguration;
import com.meiyunji.sponsored.common.springjdbc.BaseShardingDaoImpl;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.common.util.SqlStringUtil;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdSdCampaignMatchedTargetReportDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdSdCampaignMatchedTargetReport;
import com.meiyunji.sponsored.service.cpc.service.impl.ReportService;
import com.meiyunji.sponsored.service.cpc.vo.SearchVo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Created by lm on 2021/5/14.
 *
 */
@Repository
public class AmazonAdSdCampaignMatchedTargetReportDaoImpl extends BaseShardingDaoImpl<AmazonAdSdCampaignMatchedTargetReport> implements IAmazonAdSdCampaignMatchedTargetReportDao {

    @Autowired
    private DynamicRefreshNacosConfiguration nacosConfiguration;

    @Override
    public void insertOrUpdateList(Integer puid, List<AmazonAdSdCampaignMatchedTargetReport> list) {
        //插入原表
        insertOrUpdateListOriginAndHotTable(puid, list, getJdbcHelper().getTable());
        if (nacosConfiguration.isHotTableWritePhase2Enable()) {
            //插入热表
            insertOrUpdateListOriginAndHotTable(puid, list, getHotTableName());
        }
    }

    private void insertOrUpdateListOriginAndHotTable(Integer puid, List<AmazonAdSdCampaignMatchedTargetReport> list, String tableName) {
        StringBuilder sql = new StringBuilder("INSERT INTO ");
        sql.append(tableName);
        sql.append(" (`puid`,`shop_id`,`marketplace_id`,`count_date`,`tactic_type`,`campaign_name`,`campaign_id`,")
                .append("`impressions`,`clicks`,`cost`,`currency`,`conversions1d`,`conversions7d`,`conversions14d`,`conversions30d`,`conversions1d_same_sku`,`conversions7d_same_sku`,")
                .append("`conversions14d_same_sku`,`conversions30d_same_sku`,`units_ordered1d`,`units_ordered7d`,`units_ordered14d`,`units_ordered30d`,`sales1d`,`sales7d`,`sales14d`,`sales30d`,")
                .append("`sales1d_same_sku`,`sales7d_same_sku`,`sales14d_same_sku`,`sales30d_same_sku`,`orders_new_to_brand14d`,`sales_new_to_brand14d`,`units_ordered_new_to_brand14d`,`detail_page_view14d`,`view_impressions`,`cost_type`,`attributed_branded_searches14d`,`view_attributed_branded_searches14d`,`matched_target`,`create_time`,`update_time`) values ");
        List<Object> argsList = Lists.newArrayList();
        for (AmazonAdSdCampaignMatchedTargetReport report : list) {
            sql.append(" (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,now(),now()),");
            argsList.add(puid);
            argsList.add(report.getShopId());
            argsList.add(report.getMarketplaceId());
            argsList.add(report.getCountDate());
            argsList.add(report.getTacticType());
            argsList.add(report.getCampaignName());
            argsList.add(report.getCampaignId());
            argsList.add(report.getImpressions());
            argsList.add(report.getClicks());
            argsList.add(report.getCost());
            argsList.add(report.getCurrency());
            argsList.add(report.getConversions1d());
            argsList.add(report.getConversions7d());
            argsList.add(report.getConversions14d());
            argsList.add(report.getConversions30d());
            argsList.add(report.getConversions1dSameSKU());
            argsList.add(report.getConversions7dSameSKU());
            argsList.add(report.getConversions14dSameSKU());
            argsList.add(report.getConversions30dSameSKU());
            argsList.add(report.getUnitsOrdered1d());
            argsList.add(report.getUnitsOrdered7d());
            argsList.add(report.getUnitsOrdered14d());
            argsList.add(report.getUnitsOrdered30d());
            argsList.add(report.getSales1d());
            argsList.add(report.getSales7d());
            argsList.add(report.getSales14d());
            argsList.add(report.getSales30d());
            argsList.add(report.getSales1dSameSKU());
            argsList.add(report.getSales7dSameSKU());
            argsList.add(report.getSales14dSameSKU());
            argsList.add(report.getSales30dSameSKU());
            argsList.add(report.getOrdersNewToBrand14d());
            argsList.add(report.getSalesNewToBrand14d());
            argsList.add(report.getUnitsOrderedNewToBrand14d());
            argsList.add(report.getDetailPageView14d());
            argsList.add(report.getViewImpressions());
            argsList.add(report.getCostType());
            argsList.add(report.getAttributedBrandedSearches14d());
            argsList.add(report.getViewAttributedBrandedSearches14d());
            argsList.add(report.getMatchedTarget());
        }
        sql.deleteCharAt(sql.length()-1);
        sql.append(" on duplicate key update `matched_target`=values(matched_target),  `view_attributed_branded_searches14d`=values(view_attributed_branded_searches14d),  `attributed_branded_searches14d`=values(attributed_branded_searches14d), `campaign_name`=values(campaign_name),`impressions`=values(impressions),`clicks`=values(clicks),`cost`=values(cost),`currency`=values(currency),");
        sql.append("`conversions1d`=values(conversions1d),`conversions7d`=values(conversions7d),`conversions14d`=values(conversions14d),`conversions30d`=values(conversions30d),`conversions1d_same_sku`=values(conversions1d_same_sku),`conversions7d_same_sku`=values(conversions7d_same_sku),");
        sql.append("`conversions14d_same_sku`=values(conversions14d_same_sku),`conversions30d_same_sku`=values(conversions30d_same_sku),`units_ordered1d`=values(units_ordered1d),`units_ordered7d`=values(units_ordered7d),`units_ordered14d`=values(units_ordered14d),`units_ordered30d`=values(units_ordered30d),");
        sql.append("`sales1d`=values(sales1d),`sales7d`=values(sales7d),`sales14d`=values(sales14d),`sales30d`=values(sales30d),`sales1d_same_sku`=values(sales1d_same_sku),`sales7d_same_sku`=values(sales7d_same_sku),");
        sql.append("`sales14d_same_sku`=values(sales14d_same_sku),`sales30d_same_sku`=values(sales30d_same_sku),`orders_new_to_brand14d`=values(orders_new_to_brand14d),`sales_new_to_brand14d`=values(sales_new_to_brand14d),`units_ordered_new_to_brand14d`=values(units_ordered_new_to_brand14d),`detail_page_view14d`=values(detail_page_view14d),`view_impressions`=values(view_impressions),`cost_type`=values(cost_type)");
        getJdbcTemplate(puid).update(sql.toString(),argsList.toArray());
    }

    @Override
    public Page getPageList(Integer puid, SearchVo search, Page page) {
        String tableName = getTableNameByStartDate(search.getStart());
        StringBuilder selectSql = new StringBuilder("SELECT matched_target,cost_type,shop_id,count_date,marketplace_id,campaign_id,campaign_name,sum(`cost`) cost,sum(sales14d) sales14d,")
                .append("sum(sales14d_same_sku) sales14d_same_sku,sum(`impressions`) impressions,sum(`clicks`) clicks,sum(conversions14d) conversions14d, sum(`conversions14d_same_sku`) conversions14d_same_sku,")
                .append("sum(`units_ordered14d`) units_ordered14d, ")
                .append("sum(view_impressions) view_impressions ,sum(`detail_page_view14d`) detail_page_view14d,")
                .append("sum(`orders_new_to_brand14d`) orders_new_to_brand14d,sum(view_attributed_branded_searches14d) view_attributed_branded_searches14d,sum(attributed_branded_searches14d) attributed_branded_searches14d,")
                .append("sum(`sales_new_to_brand14d`) sales_new_to_brand14d ,sum(`units_ordered_new_to_brand14d`) units_ordered_new_to_brand14d")
                .append(" FROM ").append(tableName).append(" ");
        StringBuilder countSql = new StringBuilder("select count(*) from ( select campaign_id FROM ").append(tableName).append(" ");
        List<Object> argsList = Lists.newArrayList();
        StringBuilder whereSql = new StringBuilder(" where puid=? ");
        argsList.add(puid);
        if (search.getShopId() != null) {
            whereSql.append("and shop_id=? ");
            argsList.add(search.getShopId());
        } else if (CollectionUtils.isNotEmpty(search.getShopIds())) {
            whereSql.append("and shop_id in ('").append(StringUtils.join(search.getShopIds(),"','")).append("') ");
        }
        if (CollectionUtils.isNotEmpty(search.getCampaignIds())) {
            whereSql.append("and campaign_id in ('").append(StringUtils.join(search.getCampaignIds(),"','")).append("') ");
        }
        if (StringUtils.isNotBlank(search.getAdFormat())) {
            whereSql.append("and campaign_type = ? ");
            argsList.add(search.getAdFormat());
        }
        whereSql.append("and count_date>=? and count_date<=?  ");
        argsList.add(DateUtil.dateToStrWithFormat(search.getStart(),"yyyyMMdd"));
        argsList.add(DateUtil.dateToStrWithFormat(search.getEnd(),"yyyyMMdd"));
        if(StringUtils.isNotBlank(search.getSearchValue())){
            search.setSearchValue(SqlStringUtil.dealLikeSql(search.getSearchValue()));
            whereSql.append(" and campaign_name like ?");
            argsList.add(search.getSearchValue()+"%");
        }
        whereSql.append(" group by campaign_id ");
        if ("daily".equals(search.getTabType())) {
            whereSql.append(", count_date");
        }
        selectSql.append(whereSql);
        countSql.append(whereSql).append(") t");
        if(StringUtils.isNotBlank(search.getOrderField()) && StringUtils.isNotBlank(search.getOrderValue())){
            String orderField = ReportService.getOrderField(search.getOrderField(),true);
            if(StringUtils.isNotBlank(orderField)){
                selectSql.append(" order by ").append(orderField);
                if("desc".equals(search.getOrderValue())){
                    selectSql.append("desc");
                }
                selectSql.append(" ,campaign_id ");   //增加一个排序字段
                if("desc".equals(search.getOrderValue())){
                    selectSql.append("desc");
                }
            }
        }
        Object[] args = argsList.toArray();
        return this.getPageResult(puid, page.getPageNo(), page.getPageSize(), countSql.toString(), args, selectSql.toString(), args,AmazonAdSdCampaignMatchedTargetReport.class);
    }


}
