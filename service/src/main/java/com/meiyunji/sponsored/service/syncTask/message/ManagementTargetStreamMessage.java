package com.meiyunji.sponsored.service.syncTask.message;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.pulsar.shade.com.fasterxml.jackson.annotation.JsonProperty;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ManagementTargetStreamMessage extends TargetStreamBaseMessage {

    /**
     * 	An identifier used to identify the dataset (in this case targets)
     */
    @JsonProperty("dataset_id")
    private String datasetId;

    /**
     * Unique identifier of the advertiser (standard in Stream datasets)
     */
    @JsonProperty("advertiser_id")
    private String advertiserId;

    /**
     * 	The marketplace of the advertiser
     */
    @JsonProperty("marketplace_id")
    private String marketplaceId;

    /**
     * Unique identifier of advertiser. Also referred to as profileId in the API
     */
    @JsonProperty("accountId")
    private String accountId;

    @JsonProperty("campaginId")
    private String campaginId;

    @JsonProperty("targetId")
    private String targetId;

    /**
     * Product/program type of campaign. One of: SPONSORED_PRODUCTS, SPONSORED_BRANDS, SPONSORED_DISPLAY
     */
    @JsonProperty("adProduct")
    private String adProduct;

    /**
     * The type of targeting e.g. KEYWORD, AUDIENCE, EXPRESSION, AUTO (EXPRESSION_PREDEFINED), WEBSITE, APP, ASIN_EXPAND
     */
    @JsonProperty("targetType")
    private String targetType;


    /**
     * Negative or positive targeting on the expression
     */
    @JsonProperty("negative")
    private boolean negative;

    /**
     * The bid used for auction. A null bid is valid, and means the target is inheriting from the ad group default bid
     */
    @JsonProperty("bid")
    private double bid;

    @JsonProperty("version")
    private Integer version;

    /**
     * ISO4217 currency code
     */
    @JsonProperty("currencyCode")
    private String currencyCode;

    /**
     * The advertiser defined state falling into one of these values:
     * Sponsored Products: ENABLED, PAUSED, ARCHIVED, USER_DELETED, ENABLING, OTHER
     * Sponsored Brands/Sponsored Display: ENABLED, PAUSED, ARCHIVED
     */
    @JsonProperty("state")
    private String state;

    @JsonProperty("keywordTarget")
    private KeywordTarget keywordTarget;

    @JsonProperty("productTarget")
    private TargetingClause productTarget;

    @JsonProperty("productAudienceTarget")
    private TargetingClause productAudienceTarget;

    @JsonProperty("productCategoryTarget")
    private TargetingClause productCategoryTarget;

    @JsonProperty("productCategoryAudienceTarget")
    private TargetingClause productCategoryAudienceTarget;

    @JsonProperty("audienceTarget")
    private TargetingClause audienceTarget;

    @JsonProperty("autoTarget")
    private TargetingClause autoTarget;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public class KeywordTarget {

        /**
         * Matching type depending on target type.
         * For keywords: EXACT, PHRASE, BROAD (positive only)
         */
        @JsonProperty("matchType")
        private String matchType;

        /**
         * Keyword value to be used for targeting
         */
        @JsonProperty("keyword")
        private String keyword;

        /**
         * 	Keyword value language
         */
        @JsonProperty("nativeLanguageKeyword")
        private String nativeLanguageKeyword;

        /**
         * Locale of language for keyword
         */
        @JsonProperty("nativeLanguageLocale")
        private String nativeLanguageLocale;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public class TargetingClause {

        /**
         * Matching type depending on target type.
         * For keywords: EXACT, PHRASE, BROAD (positive only)
         * For expressions: ASIN, CATEGORY, BRAND (negative only), DYNAMIC_SEGMENTS (Sponsored Display only).
         * For audiences: VIEWS_REMARKETING, PURCHASE_REMARKETING, AMAZON_AUDIENCES
         */
        @JsonProperty("matchType")
        private String matchType;

        /**
         * Targeting expression string
         */
        @JsonProperty("targetingClause")
        private String targetingClause;

    }

}
