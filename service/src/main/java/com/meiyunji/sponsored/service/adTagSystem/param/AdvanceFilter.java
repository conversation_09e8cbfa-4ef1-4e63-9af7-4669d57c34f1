package com.meiyunji.sponsored.service.adTagSystem.param;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @author: liweibin
 * @email: <EMAIL>
 * @date: 2024-10-25  16:51
 */
@Data
public class AdvanceFilter {
    private Integer campaignCountMax;  //广告活动数量
    private Integer campaignCountMin;
    private BigDecimal adCostMin; //花费
    private BigDecimal adCostMax;
    private Integer impressionsMin;  //曝光量
    private Integer impressionsMax;
    private Integer clicksMin;  //点击量
    private Integer clicksMax;
    private BigDecimal cpaMin; //cpa
    private BigDecimal cpaMax;
    private BigDecimal adCostPerClickMin;  //cpc
    private BigDecimal adCostPerClickMax;
    private BigDecimal ctrMin;  //广告点击率
    private BigDecimal ctrMax;
    private BigDecimal cvrMin;  //订单转化率
    private BigDecimal cvrMax;
    private BigDecimal acosMin;  //acos
    private BigDecimal acosMax;
    private BigDecimal roasMin;  //roas
    private BigDecimal roasMax;
    private BigDecimal acotsMin;
    private BigDecimal acotsMax;
    private BigDecimal asotsMin;
    private BigDecimal asotsMax;
    private BigDecimal advertisingUnitPriceMin;  //广告笔单价
    private BigDecimal advertisingUnitPriceMax;
    private Integer adOrderNumMin;  //广告订单量
    private Integer adOrderNumMax;
    private BigDecimal adSaleMin;  //广告销售额
    private BigDecimal adSaleMax;
    private Integer adSaleNumMin; //广告销量
    private Integer adSaleNumMax;
    private Integer selfAdOrderNumMin; //本广告产品订单量
    private Integer selfAdOrderNumMax;
    private Integer otherAdOrderNumMin; //其他产品广告订单量
    private Integer otherAdOrderNumMax;
    private BigDecimal adSelfSaleMin; //本广告产品销售额最小值
    private BigDecimal adSelfSaleMax;
    private BigDecimal adOtherSalesMin; //其他产品广告销售额最小值
    private BigDecimal adOtherSalesMax;
    private Integer adSelfSaleNumMin; //本产品广告销量
    private Integer adSelfSaleNumMax;
    private Integer adOtherSaleNumMin; //其他产品广告销量
    private Integer adOtherSaleNumMax;
    private Integer ordersNewToBrandFTDMin; //“品牌新买家”订单量
    private Integer ordersNewToBrandFTDMax;
    private BigDecimal orderRateNewToBrandFTDMin; //“品牌新买家”订单百分比
    private BigDecimal orderRateNewToBrandFTDMax;
    private BigDecimal salesNewToBrandFTDMin; //“品牌新买家”销售额
    private BigDecimal salesNewToBrandFTDMax;
    private BigDecimal salesRateNewToBrandFTDMin; //“品牌新买家”销售额百分比
    private BigDecimal salesRateNewToBrandFTDMax;
    private Integer unitsOrderedNewToBrandFTDMin; //“品牌新买家”销量
    private Integer unitsOrderedNewToBrandFTDMax;
    private BigDecimal unitsOrderedRateNewToBrandFTDMin; //“品牌新买家”销量百分比
    private BigDecimal unitsOrderedRateNewToBrandFTDMax;
}
