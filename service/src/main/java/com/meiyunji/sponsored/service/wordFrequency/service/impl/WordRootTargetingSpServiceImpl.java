package com.meiyunji.sponsored.service.wordFrequency.service.impl;

import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.config.DynamicRefreshNacosConfiguration;
import com.meiyunji.sponsored.common.springjdbc.PartitionSqlUtil;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.account.service.IShopAuthService;
import com.meiyunji.sponsored.service.config.DynamicRefreshConfiguration;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdProfileDao;
import com.meiyunji.sponsored.service.cpc.dao.ICpcQueryTargetingReportDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdProfile;
import com.meiyunji.sponsored.service.cpc.po.CpcQueryKeywordReport;
import com.meiyunji.sponsored.service.cpc.po.CpcQueryTargetingReport;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.taskGrpcApi.AadasApiFactory;
import com.meiyunji.sponsored.service.wordFrequency.bo.WordBo;
import com.meiyunji.sponsored.service.wordFrequency.bo.WordRootTranslatorBo;
import com.meiyunji.sponsored.service.wordFrequency.dao.IWordRootQueryDao;
import com.meiyunji.sponsored.service.wordFrequency.dao.IWordRootTargetingSpDao;
import com.meiyunji.sponsored.service.wordFrequency.enums.WordRoot;
import com.meiyunji.sponsored.service.wordFrequency.po.WordRootKeywordSp;
import com.meiyunji.sponsored.service.wordFrequency.po.WordRootQuery;
import com.meiyunji.sponsored.service.wordFrequency.po.WordRootTargetingSp;
import com.meiyunji.sponsored.service.wordFrequency.service.IWordRootTargetingSpService;
import com.meiyunji.sponsored.service.wordFrequency.service.helper.WordRootServiceHelper;
import com.meiyunji.sponsored.service.wordFrequency.service.helper.WordRootCalculateServiceHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: zhulukun
 * @email: <EMAIL>
 * @date: 2023-11-01  22:57
 */
@Slf4j
@Service
public class WordRootTargetingSpServiceImpl implements IWordRootTargetingSpService {

    @Resource
    private ICpcQueryTargetingReportDao iCpcQueryTargetingReportDao;

    @Resource
    private IWordRootQueryDao iWordRootQueryDao;
    @Resource
    private IWordRootTargetingSpDao iWordRootTargetingSpDao;

    @Autowired
    private IAmazonAdProfileDao amazonAdProfileDao;

    @Autowired
    private IShopAuthService shopAuthService;

    @Autowired
    private DynamicRefreshNacosConfiguration dynamicRefreshNacosConfiguration;

    @Autowired
    private PartitionSqlUtil partitionSqlUtil;

    @Autowired
    private DynamicRefreshConfiguration dynamicRefreshConfiguration;

    @Override
    public boolean wordFrequencyHandler(ShopAuth shopAuth, Integer page) {
        List<CpcQueryTargetingReport> cpcQueryTargetingReportList = iCpcQueryTargetingReportDao.listSpTargetingReport(shopAuth.getPuid(), shopAuth.getId(), page);
        if (cpcQueryTargetingReportList.size() == 0) {
            return false;
        }
        List<String> queryList = cpcQueryTargetingReportList.stream().map(item -> item.getQuery()).collect(Collectors.toList());
        if (page != null) {
            // 计算全量
            insertOrUpdateWordRoot(queryList, cpcQueryTargetingReportList, shopAuth);
        } else {
            List<CpcQueryTargetingReport> cpcQueryTargetingTimeRangeReportList = iCpcQueryTargetingReportDao.listSpTargetingReportByTimeRange(shopAuth.getPuid(), shopAuth.getId(), 1);
            if (cpcQueryTargetingTimeRangeReportList.size() == 0) {
                return false;
            }
            insertOrUpdateWordRoot(queryList, cpcQueryTargetingTimeRangeReportList, shopAuth);
        }
        return true;
    }

    @Override
    public boolean wordFrequencyQueryHandler(ShopAuth shopAuth, Integer page) {
        List<WordBo> wordBoList = iCpcQueryTargetingReportDao.listWordBo(shopAuth.getPuid(), shopAuth.getId(), page);
        if (wordBoList.size() == 0) {
            return false;
        }
        if (page != null) {
            // 计算全量
            this.insertOrUpdateWordRootData(null, wordBoList, shopAuth);
        } else {
            // 计算增量
            List<WordBo> wordBoListByTimeRange = iCpcQueryTargetingReportDao.listWordBoTimeRange(shopAuth.getPuid(), shopAuth.getId(), 1);
            if (wordBoListByTimeRange.size() == 0) {
                return false;
            }
            List<String> keywordTextList = wordBoList.stream().map(WordBo::getWord).distinct().collect(Collectors.toList());
            this.insertOrUpdateWordRootData(keywordTextList, wordBoListByTimeRange, shopAuth);
        }
        return true;
    }

    private void insertOrUpdateWordRootData(List<String> wordList, List<WordBo> wordBoList, ShopAuth shopAuth) {
        //词根计算
        List<WordBo> wordRootList = WordRootCalculateServiceHelper.generatingWordRoot(wordList, wordBoList, shopAuth);
        //是否存储所有单个词
        boolean saveSingleWord = dynamicRefreshConfiguration.verifyDorisPageByPuid(shopAuth.getPuid(), dynamicRefreshConfiguration.getSingleWordWhilePuids());
        //词根插入
        if (wordRootList.size() > 0) {
            List<List<WordBo>> listPartition = Lists.partition(wordRootList, Constants.WORD_ROOT_INSERT_PARTITION);
            int i = 0;
            for (List<WordBo> list : listPartition) {
                //填充关键词其他数据
                List<Long> ids = list.stream().map(e -> Long.parseLong(e.getWordId())).distinct().collect(Collectors.toList());
                Map<Long, CpcQueryTargetingReport> queryMap = iCpcQueryTargetingReportDao.wordListByIds(shopAuth.getPuid(), shopAuth.getId(), ids)
                        .stream().collect(Collectors.toMap(CpcQueryTargetingReport::getId, Function.identity()));
                List<WordRootQuery> insertList = new ArrayList<>();
                list.stream().filter(e -> queryMap.containsKey(Long.parseLong(e.getWordId()))).forEach(e -> {
                    CpcQueryTargetingReport cpcQueryTargetingReport = queryMap.get(Long.valueOf(e.getWordId()));
                    WordRootQuery wordRootQuery = WordRootCalculateServiceHelper.buildWordRootTargetingReportSp(cpcQueryTargetingReport);
                    wordRootQuery.setWordRoot(e.getWord());
                    wordRootQuery.setWordFrequencyType(Arrays.asList(wordRootQuery.getWordRoot().split(" ")).size() > 1 ? WordRoot.WordFrequencyType.PHRASE.getType() : WordRoot.WordFrequencyType.SINGLE.getType());
                    insertList.add(wordRootQuery);
                    //切割成单个词根
                    if (saveSingleWord && WordRoot.WordFrequencyType.PHRASE.getType().equals(wordRootQuery.getWordFrequencyType())) {
                        Arrays.stream(wordRootQuery.getWordRoot().split(" ")).filter(StringUtils::isNotBlank).map(String::trim).distinct().forEach(w -> {
                            WordRootQuery singleWordRoot = WordRootCalculateServiceHelper.buildWordRootTargetingReportSp(cpcQueryTargetingReport);
                            singleWordRoot.setWordRoot(w);
                            singleWordRoot.setWordFrequencyType(WordRoot.WordFrequencyType.SINGLE.getType());
                            insertList.add(singleWordRoot);
                        });
                    }
                });
                partitionSqlUtil.save(shopAuth.getPuid(), insertList, 0, dynamicRefreshNacosConfiguration.getShardRule2(), iWordRootQueryDao::batchInsertOrUpdateSpTargeting);
            }
        }
    }

    private void insertOrUpdateWordRoot(List<String> queryList, List<CpcQueryTargetingReport> cpcQueryTargetingReportList, ShopAuth shopAuth) {
        List<WordRootQuery> wordRootQueryList = new ArrayList<>();
        for (CpcQueryTargetingReport cpcQueryTargetingReport : cpcQueryTargetingReportList) {
            List<String> wordRootList = WordRootCalculateServiceHelper.getTop5WordRootList(cpcQueryTargetingReport.getQuery(), queryList, shopAuth.getMarketplaceId());
            for (String wordRoot : wordRootList) {
                WordRootQuery wordRootQuery = WordRootCalculateServiceHelper.buildWordRootTargetingReportSp(cpcQueryTargetingReport);
                wordRootQuery.setWordRoot(wordRoot);
                wordRootQuery.setWordFrequencyType(Arrays.asList(wordRootQuery.getWordRoot().split(" ")).size() > 1 ? WordRoot.WordFrequencyType.PHRASE.getType() : WordRoot.WordFrequencyType.SINGLE.getType());
                wordRootQueryList.add(wordRootQuery);
            }
        }
        if (wordRootQueryList.size() > 0) {
            try {
                iWordRootQueryDao.batchInsertOrUpdateSpTargeting(shopAuth.getPuid(), wordRootQueryList);
            } catch (Exception e) {
                log.error("", e);
            }
        }
    }

    @Override
    public void wordRootTranslators(Integer puid, ShopAuth shopAuth, String startTime, String endTime) {
        AmazonAdProfile profile = amazonAdProfileDao.getProfile(puid, shopAuth.getId());
        int i = 1;
        while (true) {
            //获取需要翻译的词根
            List<WordRootTranslatorBo> wordRootTranslatorBos = iWordRootTargetingSpDao.listTranslatorBoByShopId(puid, shopAuth.getId(), startTime, endTime, Constants.WORD_ROOT_TRANSLATOR_SIZE);
            if (CollectionUtils.isEmpty(wordRootTranslatorBos)) {
                break;
            }
            Set<String> wordRoots = wordRootTranslatorBos.stream().filter(e -> StringUtils.isNotBlank(e.getWordRoot()))
                    .map(e -> StringUtils.trim(e.getWordRoot())).collect(Collectors.toSet());

            if (CollectionUtils.isNotEmpty(wordRoots)) {
                log.info("{}@{} execute word root translators task,sp targeting word root size: {} ", puid, shopAuth.getId(), wordRoots.size());
                String accessToken = shopAuthService.getAdToken(shopAuth);
                //调用亚马逊api接口翻译词根
                boolean isSucc = WordRootServiceHelper.getWordRootTranslatorApi(puid, shopAuth, profile.getProfileId(), accessToken, wordRoots, wordRootTranslatorBos);
                if (isSucc) {
                    //批量更新词根
                    List<WordRootTranslatorBo> updateList = wordRootTranslatorBos.stream().filter(e -> StringUtils.isNotBlank(e.getWordRootCn())).collect(Collectors.toList());
                    try {
                        iWordRootTargetingSpDao.batchUpdateWordRootCn(puid, updateList);
                    } catch (Exception e) {
                        String maxWordRoot = "";
                        String maxWordRootCn = "";
                        for (WordRootTranslatorBo bo : updateList) {
                            if (bo.getWordRootCn().length() > maxWordRootCn.length()) {
                                maxWordRootCn = bo.getWordRootCn();
                                maxWordRoot = bo.getWordRoot();
                            }
                        }
                        log.error(String.format("execute word root translators task error, puid: %d, shopId: %d, max length WordRoot: %s, max length WordRootCn: %s",
                                puid, shopAuth.getId(), maxWordRoot, maxWordRootCn), e);
                        break;
                    }
                } else {
                    if (i++ > 3) {
                        break;
                    }
                }
            } else {
                break;
            }
        }
    }
}

