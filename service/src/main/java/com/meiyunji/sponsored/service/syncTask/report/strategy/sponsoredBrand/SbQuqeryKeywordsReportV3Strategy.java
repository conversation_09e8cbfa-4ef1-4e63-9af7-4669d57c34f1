package com.meiyunji.sponsored.service.syncTask.report.strategy.sponsoredBrand;

import com.alibaba.fastjson.JSONReader;
import com.amazon.advertising.mode.MarketTimezoneAndCurrencyEnum;
import com.amazon.advertising.sb.mode.report.SbReportKeyword;
import com.google.common.collect.Lists;
import com.meiyunji.sellfox.aadas.types.enumeration.AmazonReportType;
import com.meiyunji.sellfox.aadas.types.enumeration.AmazonReportV3Type;
import com.meiyunji.sellfox.aadas.types.message.notification.ReportReadyNotification;
import com.meiyunji.sellfox.aadras.api.enumeration.AdvertiseRuleTaskTypePb;
import com.meiyunji.sellfox.aadras.types.message.task.GroupSearchQueryScheduleMessage;
import com.meiyunji.sellfox.aadras.types.message.task.QueryKeywordScheduleMessage;
import com.meiyunji.sponsored.common.enums.AmazonAd;
import com.meiyunji.sponsored.common.support.RedisConstant;
import com.meiyunji.sponsored.common.support.RedisService;
import com.meiyunji.sponsored.common.util.JSONUtil;
import com.meiyunji.sponsored.common.util.MD5Util;
import com.meiyunji.sponsored.service.autoRule.dao.IAdvertiseAutoRuleStatusDao;
import com.meiyunji.sponsored.service.autoRule.po.AdvertiseAutoRuleStatus;
import com.meiyunji.sponsored.service.config.CosBucketClient;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdCampaignAllDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdProfileDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonSbAdsDao;
import com.meiyunji.sponsored.service.cpc.dao.ICpcSbQueryKeywordReportDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdProfile;
import com.meiyunji.sponsored.service.cpc.po.CpcSbQueryKeywordReport;
import com.meiyunji.sponsored.service.enums.CampaignTypeEnum;
import com.meiyunji.sponsored.service.localization.dao.IAmazonKeywordLocalizationScheduleDao;
import com.meiyunji.sponsored.service.localization.po.AmazonKeywordLocalizationSchedule;
import com.meiyunji.sponsored.service.syncTask.entity.SponsoredProductSearchTerm;
import com.meiyunji.sponsored.service.syncTask.entity.sb.SbReportV3Keyword;
import com.meiyunji.sponsored.service.syncTask.report.strategy.AbstractReportProcessStrategy;
import jdk.nashorn.internal.objects.NativeUint16Array;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.pulsar.client.api.Producer;
import org.apache.pulsar.client.api.PulsarClientException;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.io.ByteArrayInputStream;
import java.io.InputStreamReader;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.zip.GZIPInputStream;

/**
 * @author: wade
 * @date: 2021/12/28 16:32
 * @describe: sb 关键词报告处理类
 */
@Component
@Slf4j
@ConditionalOnProperty(name = "aadas.scheduler.executors.reports-consumer.enabled", havingValue = "true")
public class SbQuqeryKeywordsReportV3Strategy extends AbstractReportProcessStrategy {

    private final ICpcSbQueryKeywordReportDao cpcSbQueryKeywordReportDao;
    private final IAmazonAdCampaignAllDao amazonAdCampaignAllDao;
    private final RedisService redisService;
    private final Producer<ReportReadyNotification> reportReadyProducer;
    private final IAmazonKeywordLocalizationScheduleDao amazonKeywordLocalizationScheduleDao;
    private final IAmazonAdProfileDao amazonAdProfileDao;
    private final IAdvertiseAutoRuleStatusDao advertiseAutoRuleStatusDao;
    private final Producer<byte[]> queryWordReportsChangeProducer;
    private final Producer<byte[]> queryKeywordReportsChangeProducer;

    public SbQuqeryKeywordsReportV3Strategy(
            CosBucketClient dataBucketClient,
            ICpcSbQueryKeywordReportDao cpcSbQueryKeywordReportDao,
            IAmazonAdCampaignAllDao amazonAdCampaignAllDao,IAmazonSbAdsDao amazonSbAdsDao,
            RedisService redisService, Producer<ReportReadyNotification> reportReadyProducer,
            IAmazonKeywordLocalizationScheduleDao amazonKeywordLocalizationScheduleDao,
            IAmazonAdProfileDao amazonAdProfileDao, IAdvertiseAutoRuleStatusDao advertiseAutoRuleStatusDao,
            Producer<byte[]> queryKeywordReportsChangeProducer,
            Producer<byte[]> queryWordReportsChangeProducer) {
        super(dataBucketClient);
        this.cpcSbQueryKeywordReportDao = cpcSbQueryKeywordReportDao;
        this.amazonAdCampaignAllDao = amazonAdCampaignAllDao;
        this.redisService = redisService;
        this.reportReadyProducer = reportReadyProducer;
        this.amazonKeywordLocalizationScheduleDao = amazonKeywordLocalizationScheduleDao;
        this.amazonAdProfileDao = amazonAdProfileDao;
        this.advertiseAutoRuleStatusDao = advertiseAutoRuleStatusDao;
        this.queryWordReportsChangeProducer = queryWordReportsChangeProducer;
        this.queryKeywordReportsChangeProducer = queryKeywordReportsChangeProducer;
    }


    @Override
    public Boolean checkValid(ReportReadyNotification notification) {
        return notification.getVersion() == 3 && AmazonReportV3Type.sb_searchTerm == notification.getV3Type();
    }


    @Override
    public void processReport(ReportReadyNotification notification) throws Exception {
        try (InputStreamReader inputStreamReader = new InputStreamReader(new GZIPInputStream(
                new ByteArrayInputStream(dataBucketClient.getObjectToBytes(notification.getPath()))))) {
            JSONReader jsonReader = new JSONReader(inputStreamReader);
            jsonReader.startArray();
            List<SbReportV3Keyword> reports = Lists.newArrayListWithExpectedSize(500);
            Set<String> groupList = new HashSet<>();
            Set<String> queryIdList = new HashSet<>();
            while (jsonReader.hasNext()) {
                jsonReader.startObject();
                SbReportV3Keyword report = new SbReportV3Keyword();
                report.readFromJsonReader(jsonReader);
                jsonReader.endObject();
                if(StringUtils.isNotBlank(report.getSearchTerm())) {
                    reports.add(report);
                    groupList.add(report.getAdGroupId().toString());
                    queryIdList.add(MD5Util.getMD5(report.getKeywordId() + report.getSearchTerm()));
                }
                if (reports.size() >= 500) {
                    dealReport(notification, reports);
                    reports = Lists.newArrayListWithExpectedSize(500);
                }
            }
            jsonReader.endArray();
            jsonReader.close();
            if (CollectionUtils.isNotEmpty(reports)) {
                dealReport(notification, reports);
            }
            if (!groupList.isEmpty()) {
                //判断是否有受控对象推送消息至数据
                try {
                    List<AdvertiseAutoRuleStatus> listByItemIdsAndEnabled = advertiseAutoRuleStatusDao.getListByItemIdsAndEnabled(notification.getSellerIdentifier(), notification.getMarketplaceIdentifier(), "SB", AdvertiseRuleTaskTypePb.AdvertiseRuleTaskType.GROUP_SEARCH_QUERY.name(), Lists.newArrayList(groupList));
                    if(CollectionUtils.isNotEmpty(listByItemIdsAndEnabled)){
                        List<String> collect = listByItemIdsAndEnabled.stream().map(AdvertiseAutoRuleStatus::getItemId).collect(Collectors.toList());
                        for (AdvertiseAutoRuleStatus item: listByItemIdsAndEnabled){
                            conversionMessage(notification.getSellerId(), item);
                            queryWordReportsChangeProducer.send(JSONUtil.objectToJson(conversionMessage(notification.getSellerId(), item)).getBytes(StandardCharsets.UTF_8));
                        }
                    }
                } catch (Exception e) {
                    log.error("推送自动化规则广告组搜索词数据错误",e);
                }
            }

            if (!queryIdList.isEmpty()) {
                //判断是否有受控对象推送消息至数据
                try {
                    List<AdvertiseAutoRuleStatus> listByItemIdsAndEnabled = advertiseAutoRuleStatusDao.ListByItemIdsByChildItemType(notification.getSellerIdentifier(), notification.getMarketplaceIdentifier(), "SB", AmazonAd.ChildrenItemType.CHILDREN_SEARCH_QUERY.getName(), Lists.newArrayList(queryIdList));
                    if (CollectionUtils.isNotEmpty(listByItemIdsAndEnabled)) {
                        for (AdvertiseAutoRuleStatus item: listByItemIdsAndEnabled) {
                            queryKeywordReportsChangeProducer.send(JSONUtil.objectToJson(buildQueryKeywordScheduleMessage(notification.getSellerId(), item)).getBytes(StandardCharsets.UTF_8));
                        }
                    }
                } catch (Exception e) {
                    log.error("推送自动化规则搜索词数据错误",e);
                }
            }
        } catch (Exception e) {
            log.info("报告处理发生错误{}@{} reportType={} countDate={}", notification.getSellerIdentifier()
                    , notification.getMarketplaceIdentifier(), notification.getV3Type(), notification.getV3StartDate(), e);
            throw e;
        }
    }


    private void dealReport(ReportReadyNotification notification, List<SbReportV3Keyword> reports) throws Exception {
        List<SbReportV3Keyword> validReports = reports.stream().filter(item -> BigDecimal.valueOf(item.getImpressions()).compareTo(BigDecimal.ZERO) != 0).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(validReports)) {
            return;
        }
        List<CpcSbQueryKeywordReport> poList = getPoBySbReportKeywords(notification, validReports);
        List<List<CpcSbQueryKeywordReport>> partition = Lists.partition(poList, 200);
        for (List<CpcSbQueryKeywordReport> queryKeywordReports : partition) {
            cpcSbQueryKeywordReportDao.insertList(notification.getSellerIdentifier(), queryKeywordReports);
        }
        AmazonKeywordLocalizationSchedule schedule =
                amazonKeywordLocalizationScheduleDao.getByShopId(notification.getSellerIdentifier(),
                        notification.getMarketplaceIdentifier());
        if (schedule != null) {
            schedule.setSbNextSyncAt(LocalDateTime.now());
            amazonKeywordLocalizationScheduleDao.updateSbNextSyncAtById(schedule);
        } else {
            AmazonAdProfile profile = amazonAdProfileDao.getProfile(notification.getSellerIdentifier(),
                    notification.getMarketplaceIdentifier());
            if (profile == null) {
                return;
            }
            AmazonKeywordLocalizationSchedule localizationSchedule = new AmazonKeywordLocalizationSchedule();
            localizationSchedule.setPuid(notification.getSellerIdentifier());
            localizationSchedule.setShopId(notification.getMarketplaceIdentifier());
            localizationSchedule.setMarketplaceId(profile.getMarketplaceId());
            localizationSchedule.setProfileId(profile.getProfileId());
            amazonKeywordLocalizationScheduleDao.save(localizationSchedule);
        }
    }


    private List<CpcSbQueryKeywordReport> getPoBySbReportKeywords(ReportReadyNotification notification, List<SbReportV3Keyword> reports) {
        //获取货币单位
        String currencyCode = MarketTimezoneAndCurrencyEnum.getByMarketplaceId(notification.getMarketplace().getId())
                .getCurrencyCode();
        List<CpcSbQueryKeywordReport> list = Lists.newArrayListWithExpectedSize(reports.size());
        CpcSbQueryKeywordReport sbQueryKeywordReport;
        boolean needRetry = false;
        for (SbReportV3Keyword keyword : reports) {
            //查询sb_format
            String adFormat = amazonAdCampaignAllDao.getSbFormatByCampaignId(notification.getSellerIdentifier(),
                    notification.getMarketplaceIdentifier(), keyword.getCampaignId().toString());
            if (StringUtils.isBlank(adFormat)) {
                needRetry = true;
                log.info("广告活动管理数据未成功匹配成功,30分钟后再解析报告. puid: {} shopId : {} campaignId: {}",
                        notification.getSellerIdentifier(), notification.getMarketplaceIdentifier(), keyword.getCampaignId());
                continue;
            }
            sbQueryKeywordReport = new CpcSbQueryKeywordReport();
            sbQueryKeywordReport.setPuid(notification.getSellerIdentifier());
            sbQueryKeywordReport.setShopId(notification.getMarketplaceIdentifier());
            sbQueryKeywordReport.setMarketplaceId(notification.getMarketplace().getId());
            sbQueryKeywordReport.setCountDate(keyword.getDate().format(DateTimeFormatter.ofPattern("yyyyMMdd")));
            sbQueryKeywordReport.setCampaignName(keyword.getCampaignName());
            sbQueryKeywordReport.setCampaignId(keyword.getCampaignId().toString());
            sbQueryKeywordReport.setAdGroupName(keyword.getAdGroupName());
            sbQueryKeywordReport.setAdGroupId(keyword.getAdGroupId().toString());
            sbQueryKeywordReport.setKeywordText(keyword.getKeywordText());
            sbQueryKeywordReport.setKeywordId(keyword.getKeywordId().toString());
            sbQueryKeywordReport.setMatchType(keyword.getMatchType());
            sbQueryKeywordReport.setQuery(keyword.getSearchTerm());
            sbQueryKeywordReport.setImpressions(keyword.getImpressions());
            sbQueryKeywordReport.setClicks(keyword.getClicks());
            sbQueryKeywordReport.setCost(keyword.getCost() != null ? keyword.getCost() : null);
            sbQueryKeywordReport.setConversions14d(keyword.getPurchasesClicks());
            sbQueryKeywordReport.setSales14d(keyword.getSalesClicks() == null ? null : keyword.getSalesClicks());
            sbQueryKeywordReport.setCampaignBudget(keyword.getCampaignBudgetAmount() != null ? keyword.getCampaignBudgetAmount().doubleValue() : null);
            sbQueryKeywordReport.setCampaignBudgetType(keyword.getCampaignBudgetType());
            sbQueryKeywordReport.setCampaignStatus(keyword.getCampaignStatus());
            sbQueryKeywordReport.setKeywordBid(keyword.getKeywordBid() != null ? keyword.getKeywordBid().doubleValue() : null);
            sbQueryKeywordReport.setKeywordStatus(keyword.getAdKeywordStatus());
            sbQueryKeywordReport.setAdFormat(adFormat);
            sbQueryKeywordReport.setCurrency(currencyCode);
            sbQueryKeywordReport.setMatchType(keyword.getMatchType());
//            sbQueryKeywordReport.setSearchTermImpressionRank(keyword.getSearchTermImpressionRank() != null ? keyword.getSearchTermImpressionRank() : null);
//            sbQueryKeywordReport.setSearchTermImpressionShare(keyword.getSearchTermImpressionShare() != null ? keyword.getSearchTermImpressionShare() : null);
//            sbQueryKeywordReport.setOrdersNewToBrand14d(keyword.getNewToBrandPurchasesClicks());
//            sbQueryKeywordReport.setOrdersNewToBrandPercentage14d(keyword.getAttributedOrdersNewToBrandPercentage14d());
//            sbQueryKeywordReport.setOrderRateNewToBrand14d(keyword.getAttributedOrderRateNewToBrand14d());
//            sbQueryKeywordReport.setSalesNewToBrand14d(keyword.getAttributedSalesNewToBrand14d() != null ? BigDecimal.valueOf(keyword.getAttributedSalesNewToBrand14d()) : null);
//            sbQueryKeywordReport.setSalesNewToBrandPercentage14d(keyword.getAttributedSalesNewToBrandPercentage14d());
//            sbQueryKeywordReport.setUnitsOrderedNewToBrand14d(keyword.getAttributedUnitsOrderedNewToBrand14d());
//            sbQueryKeywordReport.setUnitsOrderedNewToBrandPercentage14d(keyword.getAttributedUnitsOrderedNewToBrandPercentage14d());
            sbQueryKeywordReport.setVctr(keyword.getViewClickThroughRate() != null ? keyword.getViewClickThroughRate().doubleValue() : null);
            sbQueryKeywordReport.setVideo5SecondViewRate(keyword.getVideo5SecondViewRate() != null ? keyword.getVideo5SecondViewRate().doubleValue() : null);
            sbQueryKeywordReport.setVideo5SecondViews(keyword.getVideo5SecondViews());
            sbQueryKeywordReport.setVideoFirstQuartileViews(keyword.getVideoFirstQuartileViews());
            sbQueryKeywordReport.setVideoMidpointViews(keyword.getVideoMidpointViews());
            sbQueryKeywordReport.setVideoThirdQuartileViews(keyword.getVideoThirdQuartileViews());
            sbQueryKeywordReport.setVideoUnmutes(keyword.getVideoUnmutes());
            sbQueryKeywordReport.setViewableImpressions(keyword.getViewableImpressions());
            sbQueryKeywordReport.setVideoCompleteViews(keyword.getVideoCompleteViews());
            sbQueryKeywordReport.setVtr(keyword.getViewabilityRate() != null ? keyword.getViewabilityRate().doubleValue() : null);
            sbQueryKeywordReport.setQueryId(MD5Util.getMD5(sbQueryKeywordReport.getKeywordId() + sbQueryKeywordReport.getQuery()));
            sbQueryKeywordReport.setVideo5SecondViewRate(keyword.getVideo5SecondViewRate() == null ? null : keyword.getVideo5SecondViewRate().doubleValue());
            sbQueryKeywordReport.setVideo5SecondViews(keyword.getVideo5SecondViews());
            sbQueryKeywordReport.setVideoCompleteViews(keyword.getVideoCompleteViews());
            sbQueryKeywordReport.setVideoFirstQuartileViews(keyword.getVideoFirstQuartileViews());
            sbQueryKeywordReport.setVideoMidpointViews(keyword.getVideoMidpointViews());
            sbQueryKeywordReport.setVideoThirdQuartileViews(keyword.getVideoThirdQuartileViews());
            sbQueryKeywordReport.setVideoUnmutes(keyword.getVideoUnmutes());
            sbQueryKeywordReport.setViewableImpressions(keyword.getViewableImpressions());

            list.add(sbQueryKeywordReport);
        }

        String cacheKey = String.format(RedisConstant.SELLFOX_AD_SB_DAILY_REPORT, notification.getSellerIdentifier(),
                notification.getMarketplaceIdentifier(), notification.getV3Type().name(), notification.getV3StartDate().format(DateTimeFormatter.ofPattern("yyyyMMdd")));
        if (needRetry) {
            Long value = redisService.incr(cacheKey, 1L);
            if (value <= 1) {
                redisService.expire(cacheKey, 3600);
                try {
                    reportReadyProducer.newMessage().value(notification)
                            .deliverAfter(30, TimeUnit.MINUTES).send();
                } catch (PulsarClientException e) {
                    log.error("Pulsar send message with an error.", e);
                }
            }
        }
        return list;
    }

    private GroupSearchQueryScheduleMessage conversionMessage(String sellerId, AdvertiseAutoRuleStatus autoRuleStatus) {
        GroupSearchQueryScheduleMessage message = new GroupSearchQueryScheduleMessage();
        message.setMarketplaceId(autoRuleStatus.getMarketplaceId());
        message.setPuid(autoRuleStatus.getPuid());
        message.setGroupId(autoRuleStatus.getItemId());
        message.setType(CampaignTypeEnum.sb.name());
        message.setSellerId(sellerId);
        message.setItemType(AdvertiseRuleTaskTypePb.AdvertiseRuleTaskType.GROUP_SEARCH_QUERY.name());
        return message;
    }

    private QueryKeywordScheduleMessage buildQueryKeywordScheduleMessage(String sellerId, AdvertiseAutoRuleStatus autoRuleStatus) {
        QueryKeywordScheduleMessage queryKeywordScheduleMessage = new QueryKeywordScheduleMessage();
        queryKeywordScheduleMessage.setMarketplaceId(autoRuleStatus.getMarketplaceId());
        queryKeywordScheduleMessage.setPuid(autoRuleStatus.getPuid());
        queryKeywordScheduleMessage.setQueryId(autoRuleStatus.getItemId());
        queryKeywordScheduleMessage.setType(CampaignTypeEnum.sb.name());
        queryKeywordScheduleMessage.setSellerId(sellerId);
        queryKeywordScheduleMessage.setItemType(AdvertiseRuleTaskTypePb.AdvertiseRuleTaskType.QUERY_KEYWORD.name());
        return queryKeywordScheduleMessage;
    }
}
