package com.meiyunji.sponsored.service.strategy.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.meiyunji.sponsored.common.springjdbc.DbColumn;
import com.meiyunji.sponsored.common.springjdbc.DbTable;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * @author: chenzimeng
 * @email: <EMAIL>
 * @date: 2024-01-30  10:33
 */
@Data
@DbTable("t_advertise_strategy_ad_group")
public class AdvertiseStrategyAdGroup implements Serializable {

    @DbColumn(value = "id")
    private Long id;

    /**
     * puid
     */
    @DbColumn(value = "puid")
    private Integer puid;

    /**
     * 店铺ID
     */
    @DbColumn(value = "shop_id")
    private Integer shopId;

    /**
     * 站点ID
     */
    @DbColumn(value = "marketplace_id")
    private String marketplaceId;

    /**
     * 广告类型:SP,SB，SD
     */
    @DbColumn(value = "ad_type")
    private String adType;

    /**
     * 任务Id
     */
    @DbColumn(value = "task_id")
    private Long taskId;

    /**
     * 对象类型：CAMPAIGN->活动 CAMPAIGN_PLACEMENT->广告位，TARGET-> 投放
     */
    @DbColumn(value = "item_type")
    private String itemType;

    /**
     * 周期类型: DAILY->每日，WEEKLY->每周
     */
    @DbColumn(value = "type")
    private String type;

    /**
     * 有效状态
     */
    @DbColumn(value = "effective_status")
    private Integer effectiveStatus;

    /**
     * 默认竞价
     */
    @DbColumn(value = "default_bid")
    private BigDecimal defaultBid;

    /**
     * 模板id
     */
    @DbColumn(value = "template_id")
    private Long templateId;

    /**
     * 规则
     */
    @DbColumn(value = "rule")
    private String rule;

    /**
     * 版本号
     */
    @DbColumn(value = "version")
    private Integer version;


    /**
     * 规则状态
     */
    @DbColumn(value = "status")
    private String status;

    /**
     * 广告活动id
     */
    @DbColumn(value = "campaign_id")
    private String campaignId;

    /**
     * 广告组id
     */
    @DbColumn(value = "ad_group_id")
    private String adGroupId;

    /**
     * 创建时间
     */
    @DbColumn(value = "create_at")
    private LocalDateTime createAt;

    /**
     * 更新时间
     */
    @DbColumn(value = "last_update_at")
    private LocalDateTime lastUpdateAt;

}
