package com.meiyunji.sponsored.service.config;

import com.meiyunji.sponsored.service.kafka.AdStrategyTemplateEnableKafkaProducer;
import com.meiyunji.sponsored.service.properties.KafkaProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.common.serialization.StringSerializer;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.core.DefaultKafkaProducerFactory;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.core.ProducerFactory;

import java.util.HashMap;
import java.util.Map;

@Configuration
@EnableConfigurationProperties({KafkaProperties.class})
@ConditionalOnProperty(name = "spring.kafka.ad-strategy-template-enabled.enabled", havingValue = "true")
@Slf4j
public class AdStrategyTemplateEnableKafkaProducerConfiguration {

    @Value("${spring.kafka.ad-strategy-template-enabled.bootstrap-servers}")
    private String bootstrapServers;

    private ProducerFactory<String,  String> producerFactory() {
        return new DefaultKafkaProducerFactory<>(producerConfigs());
    }

    @Bean
    public KafkaTemplate<String,  String> adStrategyTemplateEnableKafkaTemplate() {
        return new KafkaTemplate<>(producerFactory());
    }

    private Map<String, Object> producerConfigs() {
        Map<String, Object> props = new HashMap<>();
        props.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, bootstrapServers);
        props.put(ProducerConfig.RETRIES_CONFIG, 10);
        props.put(ProducerConfig.ACKS_CONFIG, "1");
        props.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
        props.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
        props.put(ProducerConfig.COMPRESSION_TYPE_CONFIG, "gzip");
        return props;
    }




    @Bean(name = "adStrategyTemplateEnableKafkaProducer")
    @ConditionalOnProperty(name = "kafka.producers.ad-strategy-template-enabled.enabled", havingValue = "true")
    public AdStrategyTemplateEnableKafkaProducer adStrategyTemplateEnableKafkaProducer(
            KafkaProperties kafkaProperties, KafkaTemplate<String, String> adStrategyTemplateEnableKafkaTemplate) {
        KafkaProperties.ProducerProperties producerProperty = kafkaProperties.getProducers().get("ad-strategy-template-enabled");
        return new AdStrategyTemplateEnableKafkaProducer(producerProperty.getTopic(), adStrategyTemplateEnableKafkaTemplate);
    }
}
