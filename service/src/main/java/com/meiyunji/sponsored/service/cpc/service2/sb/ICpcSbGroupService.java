package com.meiyunji.sponsored.service.cpc.service2.sb;

import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdProfile;
import com.meiyunji.sponsored.service.cpc.po.AmazonSbAdGroup;
import com.meiyunji.sponsored.service.cpc.vo.*;

import java.util.List;

/**
 * Created by xp on 2021/3/29.
 * 广告活动业务
 */
public interface ICpcSbGroupService {



    Result showGroupPerformance(int puid, AdPerformanceParam param);

    Result<String> createGroup(SbAdGroupVo vo);
    NewCreateResultResultVo createGroup(SbAdGroupVo vo, ShopAuth shop, AmazonAdProfile profile);

    Result<String> updateGroup(List<SbAdGroupVo> vo);

    Result<String> deleteGroup(SbAdGroupVo vo, List<String> adGroupIds);

    void saveDoris(List<AmazonSbAdGroup> addList, List<AmazonSbAdGroup> updateList);

    void saveDoris(List<AmazonSbAdGroup> groupList);

    void saveDoris(Integer puid, Integer shopId, List<String> adGroupIdList);
}
