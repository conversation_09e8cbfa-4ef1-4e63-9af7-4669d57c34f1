package com.meiyunji.sponsored.service.wordFrequency.dao;

import com.meiyunji.sponsored.common.springjdbc.IBaseShardingSphereDao;
import com.meiyunji.sponsored.service.wordFrequency.bo.WordRootTranslatorBo;
import com.meiyunji.sponsored.service.wordFrequency.po.WordRootQuery;

import java.util.List;

/**
 * @author: zhulukun
 * @email: <EMAIL>
 * @date: 2023-11-01  22:54
 */
public interface IWordRootQueryDao extends IBaseShardingSphereDao<WordRootQuery> {
    void batchInsertOrUpdateSpQuery(Integer puid, List<WordRootQuery> wordRootQueryList);

    void batchInsertOrUpdateSpTargeting(Integer puid, List<WordRootQuery> wordRootQueryList);

    void batchInsertOrUpdateSbQuery(Integer puid, List<WordRootQuery> wordRootQueryList);

    /**
     * 查询未翻译的列表
     */
    List<WordRootTranslatorBo> listTranslatorBoByShopId(Integer puid, Integer shopId, String start, String end, int limit);

    /**
     * 填充翻译
     */
    void batchUpdateWordRootCn(Integer puid, List<WordRootTranslatorBo> updateList);

    int deleteByPuidAndShopId(Integer puid, Integer shopId, Integer limit);

}
