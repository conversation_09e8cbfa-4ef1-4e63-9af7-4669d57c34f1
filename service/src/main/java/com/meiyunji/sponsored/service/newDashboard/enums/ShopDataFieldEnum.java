package com.meiyunji.sponsored.service.newDashboard.enums;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 店铺数据字段枚举
 */
public enum ShopDataFieldEnum {

    COST("cost", "广告花费"),
    ORIG_COST("origCost", "广告花费原币种值"),
    TOTAL_SALES("totalSales", "广告销售额"),
    ORIG_TOTAL_SALES("origTotalSales", "广告销售额原币种值"),
    ACOS("acos", "ACoS"),
    ROAS("roas", "ROAS"),
    IMPRESSIONS("impressions", "广告曝光量"),
    CLICKS("clicks", "广告点击量"),
    CLICK_RATE("clickRate", "广告转化率"),
    CONVERSION_RATE("conversionRate", "广告转化率"),
    ORDER_NUM("orderNum", "广告订单量"),
    SALE_NUM("saleNum", "广告销量"),
    SHOP_SALES("shopSales", "店铺销售额"),
    ORIG_SHOP_SALES("origShopSales", "店铺销售额"),
    SHOP_SALE_NUM("shopSaleNum", "店铺销量"),
    ACOTS("acots", "ACoTS"),
    ASOTS("asots", "ASots"),
    CPC("cpc", "cpc"),
    SPC("spc", "spc"),
    CPA("cpa", "cpa"),
    ADVERTISING_UNIT_PRICE("advertisingUnitPrice", "广告笔单价"),

    ORIG_CPC("origCpc", "cpc原币种值"),
    ORIG_SPC("origSpc", "spc原币种值"),
    ORIG_CPA("origCpa", "cpa原币种值"),
    ORIG_ADVERTISING_UNIT_PRICE("origAdvertisingUnitPrice", "广告笔单价原币种值"),

    ;



    ;

    private String code;

    private String desc;



    public static Map<String, ShopDataFieldEnum> fieldMap = Arrays.stream(ShopDataFieldEnum.values())
            .collect((Collectors.groupingBy(ShopDataFieldEnum::getCode,
                    Collectors.collectingAndThen(Collectors.toList(), value -> value.get(0)))));

    ShopDataFieldEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
