package com.meiyunji.sponsored.service.cpc.service2.sp.impl;

import com.amazon.advertising.sd.mode.Campaign;
import com.amazon.advertising.spV3.campaign.entity.CampaignExtendEntityV3;
import com.amazon.advertising.sb.mode.campaigm.CampaignV4;
import com.amazonservices.mws.client.MwsXmlReader;
import com.amazonservices.mws.products.model.AttributeSetList;
import com.amazonservices.mws.products.model.GetMatchingProductResult;
import com.google.common.collect.Lists;
import com.meiyunji.amazon.mws.model.AmznAttributeSetList;
import com.meiyunji.amazon.mws.model.AmznItemAttributes;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.common.util.StringUtil;
import com.meiyunji.sponsored.common.util.ThreadPoolUtil;
import com.meiyunji.sponsored.service.account.dao.ISellfoxShopUserDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.cpc.dao.*;
import com.meiyunji.sponsored.service.cpc.dao.impl.IAmazonAdKeywordsLibDetailDao;
import com.meiyunji.sponsored.service.cpc.dto.AmazonServingStatusDto;
import com.meiyunji.sponsored.service.cpc.po.*;
import com.meiyunji.sponsored.service.cpc.service.IAmazonAdKeywordDaoRoutingService;
import com.meiyunji.sponsored.service.cpc.service.IAmazonAdTargetDaoRoutingService;
import com.meiyunji.sponsored.service.cpc.service.impl.AmazonAdProfileApiService;
import com.meiyunji.sponsored.service.cpc.service2.impl.AmazonAdPortfolioApiService;
import com.meiyunji.sponsored.service.cpc.service2.sb.impl.*;
import com.meiyunji.sponsored.service.cpc.service2.sd.impl.*;
import com.meiyunji.sponsored.service.cpc.service2.sp.ICpcAdSyncService;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.vo.*;
import com.meiyunji.sponsored.service.enums.AdModularEnum;
import com.meiyunji.sponsored.service.enums.SbAdGroupTypeEnum;
import com.meiyunji.sponsored.service.feign.param.CountByPuidAndKeywordTextParam;
import com.meiyunji.sponsored.service.feign.service.KeywordRankMonitorFeignService;
import com.meiyunji.sponsored.service.function.ThFunction;
import com.meiyunji.sponsored.service.product.po.AsinImage;
import com.meiyunji.sponsored.service.product.po.Product;
import com.meiyunji.sponsored.service.product.service.ISyncAsinImageService;
import com.meiyunji.sponsored.service.product.util.SpecialCharUnicodeUtil;
import com.meiyunji.sponsored.service.sellfoxApi.IAmazonAsinIamgeApi;
import com.meiyunji.sponsored.service.stream.service.IAmazonManagementStreamTaskRetryService;
import com.meiyunji.sponsored.service.system.service.IAmazonRequestLimitService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 广告同步服务组件实现类
 *
 * <AUTHOR> on 2021/6/8
 */
@Service
@Slf4j
public class CpcAdSyncServiceImpl implements ICpcAdSyncService {

    @Autowired
    private CpcCampaignApiService cpcCampaignApiService;
    @Autowired
    private CpcAdGroupApiService cpcAdGroupApiService;
    @Autowired
    private CpcKeywordsApiService cpcKeywordsApiService;
    @Autowired
    private CpcNeKeywordsApiService cpcNeKeywordsApiService;
    @Autowired
    private CpcTargetingApiService cpcTargetingApiService;
    @Autowired
    private CpcAdProductApiService cpcAdProductApiService;
    @Autowired
    private CpcSdCampaignApiService cpcSdCampaignApiService;
    @Autowired
    private CpcSdGroupApiService cpcSdGroupApiService;
    @Autowired
    private CpcSdTargetingApiService cpcSdTargetingApiService;
    @Autowired
    private CpcSdProductApiService cpcSdProductApiService;
    @Autowired
    private CpcSdNeTargetingApiService cpcSdNeTargetingApiService;
    @Autowired
    private IAmazonRequestLimitService requestLimitService;
    @Autowired
    private IAmazonAdCampaignDao amazonAdCampaignDao;
    @Autowired
    private IAmazonAdGroupDao amazonAdGroupDao;
    @Autowired
    private IAmazonAdAsinReportDao amazonAdAsinReportDao;
    @Autowired
    private IAmazonAdAsinReportKeywordDao amazonAdAsinReportKeywordDao;
    @Autowired
    private ICpcQueryTargetingReportDao cpcQueryTargetingReportDao;
    @Autowired
    private IAmazonSdAdTargetingDao amazonSdAdTargetingDao;
    @Autowired
    private IAmazonSdAdNeTargetingDao amazonSdAdNeTargetingDao;
    @Autowired
    private CpcSbCampaignApiService cpcSbCampaignApiService;
    @Autowired
    private CpcSbGroupApiService cpcSbGroupApiService;
    @Autowired
    private CpcSbKeywordApiService cpcSbKeywordApiService;
    @Autowired
    private CpcSbNeKeywordApiService cpcSbNeKeywordApiService;
    @Autowired
    private CpcSbTargetApiService cpcSbTargetApiService;
    @Autowired
    private CpcSbNeTargetApiService cpcSbNeTargetApiService;
    @Autowired
    private IAmazonSbAdCampaignDao amazonSbAdCampaignDao;
    @Autowired
    private IAmazonSbAdKeywordDao amazonSbAdKeywordDao;
    @Autowired
    private CpcCampaignNeTargetingApiService campaignNeTargetingApiService;
    @Autowired
    private AmazonAdProfileApiService profileApiService;
    @Autowired
    private AmazonAdPortfolioApiService portfolioApiService;
    @Autowired
    private IAmazonAdProfileDao amazonAdProfileDao;
    @Autowired
    private CpcSdCreativeApiService cpcSdCreativeApiService;
    @Autowired
    private IAmazonAsinIamgeApi iAmazonIamgeApi;
    @Autowired
    private IAmazonSbAdTargetingDao sbAdTargetingDao;
    @Autowired
    private ISyncAsinImageService syncAsinImageService;
    @Autowired
    private CpcSbAdsApiService cpcSbAdsApiService;
    @Autowired
    private IAmazonSbAdGroupDao amazonSbAdGroupDao;
    @Autowired
    private IAmazonSbAdsDao amazonSbAdsDao;
    @Autowired
    private IAmazonAdKeywordReportDao amazonAdKeywordReportDao;
    @Autowired
    private IAmazonAdSbKeywordReportDao amazonAdSbKeywordReportDao;
    @Autowired
    private IAmazonAdKeywordsLibDetailDao amazonAdKeywordsLibDetailDao;
    @Autowired
    private IAmazonAdKeywordsLibDao amazonAdKeywordsLibDao;
    @Autowired
    private IAmazonAdCampaignNeKeywordsDao amazonAdCampaignNeKeywordsDao;
    @Autowired
    private IAmazonSbAdNeKeywordDao amazonSbAdNeKeywordDao;
    @Autowired
    ISellfoxShopUserDao sellfoxShopUserDao;
    @Autowired
    IKeywordDao keywordDao;
    @Autowired
    IAdKeywordLibTagDao adKeywordLibTagDao;
    @Autowired
    private IAmazonManagementStreamTaskRetryService amazonManagementStreamTaskRetryService;
    @Autowired
    private IAmazonSbAdNeTargetingDao amazonSbAdNeTargetingDao;
    @Autowired
    private KeywordRankMonitorFeignService keywordRankMonitorFeignService;
    @Autowired
    private IAmazonAdNeKeywordDao amazonAdNekeywordDao;
    @Autowired
    private IAmazonAdNeTargetingDao amazonAdNeTargetingDao;
    @Autowired
    private IAmazonAdKeywordDaoRoutingService amazonAdKeywordDaoRoutingService;
    @Autowired
    private IAmazonAdTargetDaoRoutingService amazonAdTargetDaoRoutingService;
    @Resource
    private AmazonAdSbThemeKeywordApiService amazonAdSbThemeKeywordApiService;


    @Override
    public void syncSpByShop(ShopAuth shop, String campaignId,String type) {
        if (shop == null) {
            return;
        }
        if(StringUtils.isBlank(type)){
            try {
                cpcCampaignApiService.syncCampaigns(shop, campaignId, false);
            } catch (Exception e) {
                log.error("同步广告活动异常shopId:" + shop.getId(), e);
            }

            try {
                cpcAdGroupApiService.syncAdGroups(shop, campaignId, null);
            } catch (Exception e) {
                log.error("同步广告组异常shopId:" + shop.getId(), e);
            }

            try {
                cpcKeywordsApiService.syncKeywords(shop, campaignId, null, null);
            } catch (Exception e) {
                log.error("同步关键词异常shopId:" + shop.getId(), e);
            }

            try {
                cpcNeKeywordsApiService.syncCampaignNeKeywords(shop, campaignId);
            } catch (Exception e) {
                log.error("同步广告活动否定关键词异常shopId:" + shop.getId(), e);
            }

            try {
                cpcNeKeywordsApiService.syncNeKeywords(shop, campaignId, null, null);
            } catch (Exception e) {
                log.error("同步否定关键词异常shopId:" + shop.getId(), e);
            }

            try {
                cpcTargetingApiService.syncTargetings(shop, campaignId, null, null);
            } catch (Exception e) {
                log.error("同步定位异常shopId:" + shop.getId(), e);
            }

            try {
                cpcTargetingApiService.syncNeTargetings(shop, campaignId, null, null);
            } catch (Exception e) {
                log.error("同步否定定位异常shopId:" + shop.getId(), e);
            }

            try {
                cpcAdProductApiService.syncAds(shop, campaignId, null, null);
            } catch (Exception e) {
                log.error("同步广告产品异常shopId:" + shop.getId(), e);
            }

            try {
                campaignNeTargetingApiService.syncCampaignNeTargetingByShop(shop,campaignId);
            } catch (Exception e) {
                log.error("同步广告活动否定商品投放异常 shopId:" + shop.getId(), e);
            }

            if(StringUtils.isBlank(campaignId)){

                try {
                    profileApiService.getProfile(shop);
                } catch (Exception e) {
                    log.error("同步顶级预算异常 shopId:" + shop.getId(), e);
                }


                //先获取到配置信息
                AmazonAdProfile amazonAdProfile = amazonAdProfileDao.getProfile(shop.getPuid(), shop.getId());
                if (amazonAdProfile == null) {
                    log.error("syncCampaigns--配置信息为空");
                    return;
                }

                try {
                    portfolioApiService.syncPortfolio(shop, amazonAdProfile, null);
                } catch (Exception e) {
                    e.printStackTrace();
                    log.error("同步广告组合异常 shopId:" + shop.getId(), e);
                }
            }

            /**
             * 通过关联查询广告数据确定广告组的类型，这个类型前后端交互时要用，暂时还不能去掉
             * 要去掉需要和设计讨论下，改下交互
             */
            confirmAdGroupType(shop, campaignId);
        } else if("1".equals(type)){

            try {
                cpcAdGroupApiService.syncAdGroups(shop, campaignId, null);
            } catch (Exception e) {
                log.error("同步广告组异常shopId:" + shop.getId(), e);
            }

            try {
                cpcKeywordsApiService.syncKeywords(shop, campaignId, null, null);
            } catch (Exception e) {
                log.error("同步关键词异常shopId:" + shop.getId(), e);
            }
            try {
                cpcTargetingApiService.syncTargetings(shop, campaignId, null, null);
            } catch (Exception e) {
                log.error("同步定位异常shopId:" + shop.getId(), e);
            }

            /**
             * 通过关联查询广告数据确定广告组的类型，这个类型前后端交互时要用，暂时还不能去掉
             * 要去掉需要和设计讨论下，改下交互
             */
            confirmAdGroupType(shop, campaignId);
        } else if("2".equals(type)){
            try {
                cpcNeKeywordsApiService.syncCampaignNeKeywords(shop, campaignId);
            } catch (Exception e) {
                log.error("同步广告活动否定关键词异常shopId:" + shop.getId(), e);
            }

            try {
                cpcNeKeywordsApiService.syncNeKeywords(shop, campaignId, null, null);
            } catch (Exception e) {
                log.error("同步否定关键词异常shopId:" + shop.getId(), e);
            }
            try {
                cpcTargetingApiService.syncNeTargetings(shop, campaignId, null, null);
            } catch (Exception e) {
                log.error("同步否定定位异常shopId:" + shop.getId(), e);
            }
            try {
                campaignNeTargetingApiService.syncCampaignNeTargetingByShop(shop,campaignId);
            } catch (Exception e) {
                log.error("同步广告活动否定商品投放异常 shopId:" + shop.getId(), e);
            }

        } else if("3".equals(type)){

            try {
                profileApiService.getProfile(shop);
            } catch (Exception e) {
                log.error("同步顶级预算异常 shopId:" + shop.getId(), e);
            }


            //先获取到配置信息
            AmazonAdProfile amazonAdProfile = amazonAdProfileDao.getProfile(shop.getPuid(), shop.getId());
            if (amazonAdProfile == null) {
                log.error("syncCampaigns--配置信息为空");
                return;
            }

            try {
                portfolioApiService.syncPortfolio(shop, amazonAdProfile, null);
            } catch (Exception e) {
                e.printStackTrace();
                log.error("同步广告组合异常 shopId:" + shop.getId(), e);
            }
        } else if("4".equals(type)){
            try {
                cpcCampaignApiService.syncCampaigns(shop, campaignId, false);
            } catch (Exception e) {
                log.error("同步广告活动异常shopId:" + shop.getId(), e);
            }
            try {
                cpcAdProductApiService.syncAds(shop, campaignId, null, null);
            } catch (Exception e) {
                log.error("同步广告产品异常shopId:" + shop.getId(), e);
            }
        }
    }

    @Override
    public void syncSdByShop(ShopAuth shop, String campaignId, String type) {
        if (shop == null) {
            return;
        }
        if (StringUtils.isBlank(type)) {
            try {
                cpcSdCampaignApiService.syncCampaigns(shop, campaignId, false);
            } catch (Exception e) {
                log.error("同步sd广告活动异常shopId:" + shop.getId(), e);
            }

        try {
            cpcSdGroupApiService.syncAdGroups(shop, campaignId, null);
        } catch (Exception e) {
            log.error("同步sd广告组异常shopId:" + shop.getId(), e);
        }

            try {
                cpcSdTargetingApiService.syncTargetings(shop, campaignId);
            } catch (Exception e) {
                log.error("同步sd投放异常shopId:" + shop.getId(), e);
            }

        try {
            cpcSdProductApiService.syncProductAds(shop, campaignId, null, null);
        } catch (Exception e) {
            log.error("同步sd广告产品异常shopId:" + shop.getId(), e);
        }

            try {
                cpcSdNeTargetingApiService.syncNeTargetings(shop, campaignId);
            } catch (Exception e) {
                log.error("同步sd否定投放异常shopId:" + shop.getId(), e);
            }
        } else if ("1".equals(type)) {
            try {
                cpcSdCampaignApiService.syncCampaigns(shop, campaignId, false);
            } catch (Exception e) {
                log.error("同步sd广告活动异常shopId:" + shop.getId(), e);
            }
        } else if ("2".equals(type)) {
            try {
                cpcSdGroupApiService.syncAdGroups(shop, campaignId, null);
            } catch (Exception e) {
                log.error("同步sd广告组异常shopId:" + shop.getId(), e);
            }
        } else if ("3".equals(type)) {
            try {
                cpcSdTargetingApiService.syncTargetings(shop, campaignId);
            } catch (Exception e) {
                log.error("同步sd投放异常shopId:" + shop.getId(), e);
            }
        } else if ("4".equals(type)) {
            try {
                cpcSdProductApiService.syncProductAds(shop, campaignId, null, null);
            } catch (Exception e) {
                log.error("同步sd广告产品异常shopId:" + shop.getId(), e);
            }
        } else if ("5".equals(type)) {
            try {
                cpcSdNeTargetingApiService.syncNeTargetings(shop, campaignId);
            } catch (Exception e) {
                log.error("同步sd否定投放异常shopId:" + shop.getId(), e);
            }
        }
    }

    @Override
    public void syncSbByShop(ShopAuth shop, String campaignId, String type) {
        if (shop == null) {
            return;
        }
        if(StringUtils.isBlank(type)){

            try {
                cpcSbAdsApiService.syncAds(shop, campaignId, null);
            } catch (Exception e) {
                log.error("同步sb否定投放异常shopId:" + shop.getId(), e);
            }

            try {
                cpcSbCampaignApiService.syncCampaignsV4(shop, campaignId, false);
            } catch (Exception e) {
                log.error("同步sb广告活动V4异常shopId:" + shop.getId(), e);
            }

            try {
                cpcSbGroupApiService.syncAdGroups(shop, campaignId, null);
            } catch (Exception e) {
                log.error("同步sb广告组异常shopId:" + shop.getId(), e);
            }


            try {
                cpcSbKeywordApiService.syncKeywords(shop, campaignId);
            } catch (Exception e) {
                log.error("同步sb关键词异常shopId:" + shop.getId(), e);
            }

            try {
                cpcSbNeKeywordApiService.syncNeKeywords(shop, campaignId, null);
            } catch (Exception e) {
                log.error("同步sb否定关键词异常shopId:" + shop.getId(), e);
            }

            try {
                cpcSbTargetApiService.syncTargets(shop, campaignId);
            } catch (Exception e) {
                log.error("同步sb投放异常shopId:" + shop.getId(), e);
            }

            try {
                cpcSbNeTargetApiService.syncNeTargets(shop, campaignId);
            } catch (Exception e) {
                log.error("同步sb否定投放异常shopId:" + shop.getId(), e);
            }

            try {
                amazonAdSbThemeKeywordApiService.syncThemes(shop, campaignId);
            } catch (Exception e) {
                log.error("同步sb主题匹配异常shopId:" + shop.getId(), e);
            }

            //通过关联查询广告数据确定sb广告组投放的类型
            confirmAdGroupTargetType(shop, campaignId);
            confirmAdGroupAdFormat(shop,campaignId);

        } else if("1".equals(type)) {
            try {
                cpcSbAdsApiService.syncAds(shop, campaignId, null);
            } catch (Exception e) {
                log.error("同步sb否定投放异常shopId:" + shop.getId(), e);
            }
            try {
                cpcSbCampaignApiService.syncCampaignsV4(shop, campaignId, false);

            } catch (Exception e) {
                log.error("同步sb否定投放异常shopId:" + shop.getId(), e);
            }

        } else if("2".equals(type)){

            try {
                cpcSbNeKeywordApiService.syncNeKeywords(shop, campaignId, null);
            } catch (Exception e) {
                log.error("同步sb否定关键词异常shopId:" + shop.getId(), e);
            }


            try {
                cpcSbNeTargetApiService.syncNeTargets(shop, campaignId);
            } catch (Exception e) {
                log.error("同步sb否定投放异常shopId:" + shop.getId(), e);
            }

        } else if("3".equals(type)){
            try {
                cpcSbAdsApiService.syncAds(shop, campaignId, null);
            } catch (Exception e) {
                log.error("同步sb否定投放异常shopId:" + shop.getId(), e);
            }

            try {
                cpcSbGroupApiService.syncAdGroups(shop, campaignId, null);
            } catch (Exception e) {
                log.error("同步sb广告组异常shopId:" + shop.getId(), e);
            }

            try {
                cpcSbKeywordApiService.syncKeywords(shop, campaignId);
            } catch (Exception e) {
                log.error("同步sb关键词异常shopId:" + shop.getId(), e);
            }


            try {
                cpcSbTargetApiService.syncTargets(shop, campaignId);
            } catch (Exception e) {
                log.error("同步sb投放异常shopId:" + shop.getId(), e);
            }

            try {
                amazonAdSbThemeKeywordApiService.syncThemes(shop, campaignId);
            } catch (Exception e) {
                log.error("同步sb主题匹配异常shopId:" + shop.getId(), e);
            }

            //通过关联查询广告数据确定sb广告组投放的类型
            confirmAdGroupTargetType(shop, campaignId);
            confirmAdGroupAdFormat(shop,campaignId);

        } else if ("4".equals(type)) {
            try {
                cpcSbGroupApiService.syncAdGroups(shop, campaignId, null);
            } catch (Exception e) {
                log.error("同步sb广告组异常shopId:" + shop.getId(), e);
            }

            try {
                cpcSbKeywordApiService.syncKeywords(shop, campaignId);
            } catch (Exception e) {
                log.error("同步sb关键词异常shopId:" + shop.getId(), e);
            }


            try {
                cpcSbTargetApiService.syncTargets(shop, campaignId);
            } catch (Exception e) {
                log.error("同步sb投放异常shopId:" + shop.getId(), e);
            }
            
            try {
                amazonAdSbThemeKeywordApiService.syncThemes(shop, campaignId);
            } catch (Exception e) {
                log.error("同步sb主题匹配异常shopId:" + shop.getId(), e);
            }

            //通过关联查询广告数据确定sb广告组投放的类型
            confirmAdGroupTargetType(shop, campaignId);
            confirmAdGroupAdFormat(shop, campaignId);
        }
    }

    @Override
    public List<AmazonServingStatusDto> syncSbByShopAndId(ShopAuth shop, String ids, String type) {

        if (AdModularEnum.campaign.name().equalsIgnoreCase(type)) {
            try {
                List<String> idList = StringUtil.splitStr(ids);
                List<List<String>> lists = Lists.partition(idList, 100);
                for (List<String> id : lists) {
                    cpcSbCampaignApiService.syncCampaignsV4(shop, String.join(",", id), false);
                }
            } catch (Exception e) {
                log.error("同步sb广告活动V4异常shopId:" + shop.getId(), e);
            }
            return cpcCampaignApiService.listByIds(shop.getPuid(), shop.getId(), ids);
        }

        if (AdModularEnum.group.name().equalsIgnoreCase(type)) {
            try {
                List<String> idList = StringUtil.splitStr(ids);
                List<List<String>> lists = Lists.partition(idList, 100);
                for (List<String> id : lists) {
                    cpcSbGroupApiService.syncAdGroups(shop, null, String.join(",", id));
                }
//                cpcSbGroupApiService.syncAdGroups(shop, null, ids);
            } catch (Exception e) {
                log.error("同步sb广告组异常shopId:" + shop.getId(), e);
            }
            return cpcSbGroupApiService.listByIds(shop.getPuid(), shop.getId(), ids);
        }

        if (AdModularEnum.adProduct.name().equalsIgnoreCase(type)) {
            try {
                cpcSbAdsApiService.syncAds(shop, null, ids);
            } catch (Exception e) {
                log.error("同步sb广告商品异常shopId:" + shop.getId(), e);
            }
            return cpcSbAdsApiService.listByIds(shop.getPuid(), shop.getId(), ids);
        }

        if (AdModularEnum.keywordTarget.name().equalsIgnoreCase(type)) {
            try {
                cpcSbKeywordApiService.syncKeywordsIds(shop, ids);
            } catch (Exception e) {
                log.error("同步sb关键词异常shopId:" + shop.getId(), e);
            }
            return new ArrayList<>();
        }

        if (AdModularEnum.neKeyword.name().equalsIgnoreCase(type)) {
            try {
                cpcSbNeKeywordApiService.syncNeKeywords(shop, null, StringUtil.splitStr(ids));
            } catch (Exception e) {
                log.error("同步sb否定关键词异常shopId:" + shop.getId(), e);
            }
            return new ArrayList<>();
        }

        if (AdModularEnum.queryTarget.name().equalsIgnoreCase(type)) {
            try {
                cpcSbTargetApiService.syncTargets(shop, ids);
            } catch (Exception e) {
                log.error("同步sb投放异常shopId:" + shop.getId(), e);
            }
            return new ArrayList<>();
        }

        if (AdModularEnum.neTarget.name().equalsIgnoreCase(type)) {
            try {
                cpcSbNeTargetApiService.syncNeTargets(shop, ids);
            } catch (Exception e) {
                log.error("同步sb否定投放异常shopId:" + shop.getId(), e);
            }
            return new ArrayList<>();
        }
        return new ArrayList<>();
    }

    @Override
    public List<AmazonServingStatusDto> syncSdByShopAndId(ShopAuth shop, String ids, String type) {
        if (AdModularEnum.campaign.name().equalsIgnoreCase(type)) {
            try {
                cpcSdCampaignApiService.syncCampaigns(shop, ids, false);
            } catch (Exception e) {
                log.error("同步sd广告活动异常shopId:" + shop.getId(), e);
            }
            return cpcCampaignApiService.listByIds(shop.getPuid(), shop.getId(), ids);
        }

        if (AdModularEnum.group.name().equalsIgnoreCase(type)) {
            try {
                cpcSdGroupApiService.syncAdGroups(shop, null, ids);
            } catch (Exception e) {
                log.error("同步sd广告组异常shopId:" + shop.getId(), e);
            }
            return cpcSdGroupApiService.listByIds(shop.getPuid(), shop.getId(), ids);
        }

        if (AdModularEnum.queryTarget.name().equalsIgnoreCase(type)) {
            try {
                List<String> idList = StringUtil.splitStr(ids);
                List<List<String>> lists = Lists.partition(idList, 100);
                for (List<String> id : lists) {
                    cpcSdTargetingApiService.syncByTargetId(shop, String.join(",", id));
                }
            } catch (Exception e) {
                log.error("同步sd投放异常shopId:" + shop.getId(), e);
            }
            return cpcSdTargetingApiService.listByIds(shop.getPuid(), shop.getId(), ids);
        }

        if (AdModularEnum.adProduct.name().equalsIgnoreCase(type)) {
            try {
                cpcSdProductApiService.syncProductAds(shop, null, null, ids);
            } catch (Exception e) {
                log.error("同步sd广告产品异常shopId:" + shop.getId(), e);
            }
            return cpcSdProductApiService.listByIds(shop.getPuid(), shop.getId(), ids);
        }

        if (AdModularEnum.neTarget.name().equalsIgnoreCase(type)) {
            try {
                cpcSdNeTargetingApiService.syncNeByTargetId(shop, ids);
            } catch (Exception e) {
                log.error("同步sd否定投放异常shopId:" + shop.getId(), e);
            }
            return cpcSdNeTargetingApiService.listByIds(shop.getPuid(), shop.getId(), ids);
        }
        return new ArrayList<>();
    }

    @Override
    public List<AmazonServingStatusDto> syncSpByShopAndId(ShopAuth shop, String ids, String type) {
        if (AdModularEnum.campaign.name().equalsIgnoreCase(type)) {
            try {
                cpcCampaignApiService.syncCampaigns(shop, ids, false);
            } catch (Exception e) {
                log.error("同步广告活动异常shopId:" + shop.getId(), e);
            }
            return cpcCampaignApiService.listByIds(shop.getPuid(), shop.getId(), ids);
        }

        if (AdModularEnum.group.name().equalsIgnoreCase(type)) {
            try {
                cpcAdGroupApiService.syncAdGroups(shop, null, ids);
            } catch (Exception e) {
                log.error("同步广告组异常shopId:" + shop.getId(), e);
            }
            return cpcAdGroupApiService.listByIds(shop.getPuid(), shop.getId(), ids);
        }

        if (AdModularEnum.keywordTarget.name().equalsIgnoreCase(type)) {
            try {
                cpcKeywordsApiService.syncKeywords(shop, null, null, ids);
            } catch (Exception e) {
                log.error("同步关键词异常shopId:" + shop.getId(), e);
            }
            return cpcKeywordsApiService.listByIds(shop.getPuid(), shop.getId(), ids);
        }

        if (AdModularEnum.neKeyword.name().equalsIgnoreCase(type)) {
            try {
                cpcNeKeywordsApiService.syncNeKeywords(shop, null, null, ids);
            } catch (Exception e) {
                log.error("同步否定关键词异常shopId:" + shop.getId(), e);
            }
            return cpcNeKeywordsApiService.listByIds(shop.getPuid(), shop.getId(), ids);
        }

        if (AdModularEnum.queryTarget.name().equalsIgnoreCase(type)) {
            try {
                cpcTargetingApiService.syncTargetings(shop, null, null, ids);
            } catch (Exception e) {
                log.error("同步定位异常shopId:" + shop.getId(), e);
            }
            return cpcTargetingApiService.listByIds(shop.getPuid(), shop.getId(), ids);
        }

        if (AdModularEnum.neTarget.name().equalsIgnoreCase(type)) {
            try {
                cpcTargetingApiService.syncNeTargetings(shop, null, null, ids);
            } catch (Exception e) {
                log.error("同步否定定位异常shopId:" + shop.getId(), e);
            }
            return cpcTargetingApiService.listByIds(shop.getPuid(), shop.getId(), ids);
        }

        if (AdModularEnum.adProduct.name().equalsIgnoreCase(type)) {
            try {
                cpcAdProductApiService.syncAds(shop, null, null, ids);
            } catch (Exception e) {
                log.error("同步广告产品异常shopId:" + shop.getId(), e);
            }
            return cpcAdProductApiService.listByIds(shop.getPuid(), shop.getId(), ids);
        }

        if (AdModularEnum.portfolio.name().equalsIgnoreCase(type)) {
            AmazonAdProfile amazonAdProfile = amazonAdProfileDao.getProfile(shop.getPuid(), shop.getId());
            if (amazonAdProfile == null) {
                log.error("syncCampaigns--配置信息为空");
                return new ArrayList<>();
            }

            try {
                portfolioApiService.syncPortfolio(shop, amazonAdProfile, ids);
            } catch (Exception e) {
                log.error("同步广告组合异常 shopId:" + shop.getId(), e);
            }
            return portfolioApiService.listByIds(shop.getPuid(), shop.getId(), ids);
        }
        return new ArrayList<>();
    }

    @Override
    public void asyncSbByShop(ShopAuth shop, String campaignId,String type){
        ThreadPoolUtil.getCpcAdShopAsyncPool().execute(() -> {
            try {
                syncSbByShop(shop,  campaignId, type);
            } catch (Exception e) {
                log.error("puid={} shopId={} asyncSbByShop = {}", shop.getPuid(), shop.getId(), e);
            }
        });
    }

    @Override
    public void asyncSpByShop(ShopAuth shop, String campaignId, String type) {
        ThreadPoolUtil.getCpcAdShopAsyncPool().execute(() -> {
            try {
                syncSpByShop(shop,  campaignId, type);
            } catch (Exception e) {
                log.error("puid={} shopId={} asyncSpByShop = {}", shop.getPuid(), shop.getId(), e);
            }
        });
    }

    @Override
    public void asyncSdByShop(ShopAuth shop, String campaignId, String type) {
        ThreadPoolUtil.getCpcAdShopAsyncPool().execute(() -> {
            try {
                syncSdByShop(shop,  campaignId, type);
            } catch (Exception e) {
                log.error("puid={} shopId={} asyncSdByShop = {}", shop.getPuid(), shop.getId(), e);
            }
        });
    }



    @Override
    public void syncTargetAsinInfo(ShopAuth shop) {
        if (shop == null) {
            return;
        }

        long offset = 0;
        int limit = 200;
        while (true) {
            // 没有图片的asin
            List<AmazonAdTargeting> amazonAdTargetingList = amazonAdTargetDaoRoutingService.listNoAsinInfo(shop.getPuid(), shop.getId(), offset, limit);
            if (CollectionUtils.isEmpty(amazonAdTargetingList)) {
                break;
            }
            offset = amazonAdTargetingList.get(amazonAdTargetingList.size() - 1).getId();

            Map<String, List<AmazonAdTargeting>> asinMap = amazonAdTargetingList
                    .stream().filter(e -> StringUtils.isNotBlank(e.getTargetingValue()))
                    .peek(e -> e.setTargetingValue(e.getTargetingValue().toUpperCase()))
                    .collect(Collectors.groupingBy(AmazonAdTargeting::getTargetingValue));

            List<String> asins = Lists.newArrayList(asinMap.keySet());
            List<List<String>> partition = Lists.partition(asins, 100);
            List<GetMatchingProductResult> results;
            List<AmazonAdTargeting> needUpdateList = new ArrayList<>();
            for (List<String> subList : partition) {
                try {
                    List<AsinImage> listByAsins = syncAsinImageService.getListByAsins(shop.getPuid(), shop.getMarketplaceId(), subList);
                    for (AsinImage asinImage : listByAsins){
                        if(StringUtils.isBlank(asinImage.getImage()) || StringUtils.isBlank(asinImage.getTitle())){
                            continue;
                        }
                        List<AmazonAdTargeting> amazonAdTargetings = asinMap.get(asinImage.getAsin().toUpperCase());
                        if (CollectionUtils.isNotEmpty(amazonAdTargetings)) {
                            amazonAdTargetings.forEach(e -> {
                                e.setImgUrl(asinImage.getImage());
                                e.setTitle(asinImage.getTitle());
                            });
                            needUpdateList.addAll(amazonAdTargetings);
                        }
                    }
                } catch (Exception e1) {
                    log.error("syncTargetAsinInfo1", e1);
                }
            }
            if (needUpdateList.size() > 0) {
                amazonAdTargetDaoRoutingService.batchSetAsinInfo(shop.getPuid(), needUpdateList);
            }
        }

        log.info("同步asin定位图片、标题结束，shopId:{}", shop.getId());
    }

    @Override
    public void syncAsinReportAsinImage(ShopAuth shop, String startDate, String campaignId) {
        if (shop == null) {
            return;
        }

        long offset = 0;
        int limit = 200;
        List<AmazonAdAsinReport> amazonAdAsinReports;
        while (true) {
            amazonAdAsinReports = amazonAdAsinReportDao.listNoAsinImage(shop.getPuid(), shop.getId(), startDate, campaignId, offset, limit);
            if (CollectionUtils.isEmpty(amazonAdAsinReports)) {
                break;
            }

            offset = amazonAdAsinReports.get(amazonAdAsinReports.size() - 1).getId();

            // 没有图片的asin
            Map<String, List<AmazonAdAsinReport>> asinMap = amazonAdAsinReports.stream()
                    .filter(e -> StringUtils.isNotBlank(e.getAsin()))
                    .peek(e -> e.setAsin(e.getAsin().toUpperCase()))
                    .collect(Collectors.groupingBy(AmazonAdAsinReport::getAsin));

            if (asinMap.size() == 0) {
                log.error("syncAsinReportImage-没有需要同步asin信息的cpc定位, shopId:{}", shop.getId());
                continue;
            }


            List<String> asins = Lists.newArrayList(asinMap.keySet());
            List<List<String>> partition = Lists.partition(asins, 100);
            List<GetMatchingProductResult> results;
            List<AmazonAdAsinReport> needUpdateList = new ArrayList<>();
            for (List<String> as : partition) {
                try {
                    List<AsinImage> listByAsins = syncAsinImageService.getListByAsins(shop.getPuid(),shop.getMarketplaceId(),as);
                    for (AsinImage asinImage : listByAsins) {
                        if (StringUtils.isBlank(asinImage.getImage())) {
                            continue;
                        }
                        List<AmazonAdAsinReport> amazonAdTargetings = asinMap.get(asinImage.getAsin().toUpperCase());
                        if (CollectionUtils.isNotEmpty(amazonAdTargetings)) {
                            amazonAdTargetings.forEach(e -> {
                                e.setAsinImage(asinImage.getImage());
                            });
                            needUpdateList.addAll(amazonAdTargetings);
                        }
                    }
                } catch (Exception e1) {
                    log.error("syncTargetAsinInfo1", e1);
                }
            }

            if (needUpdateList.size() > 0) {
                amazonAdAsinReportDao.batchSetAsinImage(shop.getPuid(), needUpdateList);
            }
        }


        log.info("syncAsinReportImage-同步asin图片结束，shopId:{}", shop.getId());
    }

    @Override
    public void syncAsinReportOtherAsinImage(ShopAuth shop, String startDate, String campaignId) {
        if (shop == null) {
            return;
        }

        long offset = 0;
        int limit = 200;
        List<AmazonAdAsinReport> amazonAdAsinReports;
        while (true) {
            amazonAdAsinReports = amazonAdAsinReportDao.listNoOtherAsinImage(shop.getPuid(), shop.getId(), startDate, campaignId, offset, limit);
            if (CollectionUtils.isEmpty(amazonAdAsinReports)) {
                break;
            }

            offset = amazonAdAsinReports.get(amazonAdAsinReports.size() - 1).getId();

            // 没有图片的asin
            Map<String, List<AmazonAdAsinReport>> asinMap = amazonAdAsinReports.stream()
                    .filter(e -> StringUtils.isNotBlank(e.getOtherAsin()))
                    .peek(e -> e.setOtherAsin(e.getOtherAsin().toUpperCase()))
                    .collect(Collectors.groupingBy(AmazonAdAsinReport::getOtherAsin));

            if (asinMap.size() == 0) {
                log.error("syncAsinReportOtherAsinImage-没有需要同步asin信息的cpc定位, shopId:{}", shop.getId());
                continue;
            }


            List<String> asins = Lists.newArrayList(asinMap.keySet());
            List<List<String>> partition = Lists.partition(asins, 100);
            List<GetMatchingProductResult> results;
            List<AmazonAdAsinReport> needUpdateList = new ArrayList<>();
            for (List<String> subList : partition) {
                try {
                    List<AsinImage> listByAsins = syncAsinImageService.getListByAsins(shop.getPuid(),shop.getMarketplaceId(),subList);
                    for (AsinImage asinImage : listByAsins){
                        if(StringUtils.isBlank(asinImage.getImage())){
                            continue;
                        }
                        List<AmazonAdAsinReport> amazonAdTargetings = asinMap.get(asinImage.getAsin().toUpperCase());
                        if (CollectionUtils.isNotEmpty(amazonAdTargetings)) {
                            amazonAdTargetings.forEach(e -> {
                                e.setOtherAsinImage(asinImage.getImage());
                            });
                            needUpdateList.addAll(amazonAdTargetings);
                        }
                    }

                } catch (Exception e){
                    log.error("同步图片错误");
                }

                if (needUpdateList.size() > 0) {
                    amazonAdAsinReportDao.batchSetOtherAsinImage(shop.getPuid(), needUpdateList);
                }
            }


        }

        log.info("syncAsinReportOtherAsinImage-同步asin图片结束，shopId:{}", shop.getId());
    }

    @Override
    public void syncAsinReportKeywordAsinImage(ShopAuth shop, String startDate, String campaignId) {
        if (shop == null) {
            return;
        }

        long offset = 0;
        int limit = 200;
        List<AmazonAdAsinReportKeyword> amazonAdAsinReports;
        while (true) {
            amazonAdAsinReports = amazonAdAsinReportKeywordDao.listNoAsinImage(shop.getPuid(), shop.getId(), startDate, campaignId, offset, limit);
            if (CollectionUtils.isEmpty(amazonAdAsinReports)) {
                break;
            }

            offset = amazonAdAsinReports.get(amazonAdAsinReports.size() - 1).getId();

            // 没有图片的asin
            Map<String, List<AmazonAdAsinReportKeyword>> asinMap = amazonAdAsinReports.stream()
                    .filter(e -> StringUtils.isNotBlank(e.getAsin()))
                    .peek(e -> e.setAsin(e.getAsin().toUpperCase()))
                    .collect(Collectors.groupingBy(AmazonAdAsinReportKeyword::getAsin));

            if (asinMap.size() == 0) {
                log.error("syncAsinReportKeywordAsinImage-没有需要同步asin信息的cpc定位, shopId:{}", shop.getId());
                continue;
            }

            List<String> asins = Lists.newArrayList(asinMap.keySet());
            List<List<String>> partition = Lists.partition(asins, 100);
            List<GetMatchingProductResult> results;
            List<AmazonAdAsinReportKeyword> needUpdateList = new ArrayList<>();
            for (List<String> subList : partition) {
                try {
                    List<AsinImage> listByAsins = syncAsinImageService.getListByAsins(shop.getPuid(),shop.getMarketplaceId(),subList);
                    for (AsinImage asinImage : listByAsins){
                        if(StringUtils.isBlank(asinImage.getImage())){
                            continue;
                        }
                        List<AmazonAdAsinReportKeyword> amazonAdTargetings = asinMap.get(asinImage.getAsin().toUpperCase());
                        if (CollectionUtils.isNotEmpty(amazonAdTargetings)) {
                            amazonAdTargetings.forEach(e -> {
                                e.setAsinImage(asinImage.getImage());
                            });
                            needUpdateList.addAll(amazonAdTargetings);
                        }
                    }

                } catch (Exception e){
                    log.error("同步图片错误");
                }


            }

            if (needUpdateList.size() > 0) {
                amazonAdAsinReportKeywordDao.batchSetAsinImage(shop.getPuid(), needUpdateList);
            }
        }

        log.info("syncAsinReportKeywordAsinImage-同步asin图片结束，shopId:{}", shop.getId());
    }

    @Override
    public void syncAsinReportKeywordOtherAsinImage(ShopAuth shop, String startDate, String campaignId) {
        if (shop == null) {
            return;
        }

        long offset = 0;
        int limit = 200;
        List<AmazonAdAsinReportKeyword> amazonAdAsinReports;
        while (true) {
            amazonAdAsinReports = amazonAdAsinReportKeywordDao.listNoOtherAsinImage(shop.getPuid(), shop.getId(), startDate, campaignId, offset, limit);
            if (CollectionUtils.isEmpty(amazonAdAsinReports)) {
                break;
            }

            offset = amazonAdAsinReports.get(amazonAdAsinReports.size() - 1).getId();

            // 没有图片的asin
            Map<String, List<AmazonAdAsinReportKeyword>> asinMap = amazonAdAsinReports.stream()
                    .filter(e -> StringUtils.isNotBlank(e.getOtherAsin()))
                    .peek(e -> e.setOtherAsin(e.getOtherAsin().toUpperCase()))
                    .collect(Collectors.groupingBy(AmazonAdAsinReportKeyword::getOtherAsin));

            if (asinMap.size() == 0) {
                log.error("syncAsinReportKeywordOtherAsinImage-没有需要同步asin信息的cpc定位, shopId:{}", shop.getId());
                continue;
            }

            List<String> asins = Lists.newArrayList(asinMap.keySet());
            List<List<String>> partition = Lists.partition(asins, 100);
            List<GetMatchingProductResult> results;
            List<AmazonAdAsinReportKeyword> needUpdateList = new ArrayList<>();
            for (List<String> subList : partition) {
                try {
                    List<AsinImage> listByAsins = syncAsinImageService.getListByAsins(shop.getPuid(),shop.getMarketplaceId(),subList);
                    for (AsinImage asinImage : listByAsins){
                        if(StringUtils.isBlank(asinImage.getImage())){
                            continue;
                        }
                        List<AmazonAdAsinReportKeyword> amazonAdTargetings = asinMap.get(asinImage.getAsin().toUpperCase());
                        if (CollectionUtils.isNotEmpty(amazonAdTargetings)) {
                            amazonAdTargetings.forEach(e -> {
                                e.setOtherAsinImage(asinImage.getImage());
                            });
                            needUpdateList.addAll(amazonAdTargetings);
                        }
                    }

                } catch (Exception e){
                    log.error("同步图片错误");
                }

            }

            if (needUpdateList.size() > 0) {
                amazonAdAsinReportKeywordDao.batchSetOtherAsinImage(shop.getPuid(), needUpdateList);
            }
        }

        log.info("syncAsinReportKeywordOtherAsinImage-同步asin图片结束，shopId:{}", shop.getId());
    }

    @Override
    public void syncQueryTargetingReportAsinImage(ShopAuth shop, String startDate, String endDate, String campaignId, long blockingTime) {
        if (shop == null) {
            return;
        }

        long offset = 0;
        int limit = 200;
        List<CpcQueryTargetingReport> targetingReports;
        while (true) {

            targetingReports = cpcQueryTargetingReportDao.listNoAsinInfo(shop.getPuid(), shop.getId(), startDate, endDate, campaignId, offset, limit);

            if (CollectionUtils.isEmpty(targetingReports)) {
                break;
            }

            offset = targetingReports.get(targetingReports.size() - 1).getId();

            // 没有图片的asin
            Map<String, List<CpcQueryTargetingReport>> asinMap = targetingReports.stream()
                    .filter(e -> StringUtils.isNotBlank(e.getQuery()))
                    .peek(e -> e.setQuery(e.getQuery().toUpperCase()))
                    .collect(Collectors.groupingBy(CpcQueryTargetingReport::getQuery));

            if (asinMap.size() == 0) {
                log.error("syncQueryTargetingReportAsinImage-没有需要同步asin信息, shopId:{}", shop.getId());
                continue;
            }


            List<String> asins = Lists.newArrayList(asinMap.keySet());
            List<List<String>> partition = Lists.partition(asins, 100);
            List<GetMatchingProductResult> results;
            List<CpcQueryTargetingReport> needUpdateList = new ArrayList<>();
            for (List<String> subList : partition) {

                try {
                    List<AsinImage> listByAsins = syncAsinImageService.getListByAsins(shop.getPuid(),shop.getMarketplaceId(),subList);
                    for (AsinImage asinImage : listByAsins) {
                        if (StringUtils.isBlank(asinImage.getImage()) || StringUtils.isBlank(asinImage.getTitle())) {
                            continue;
                        }
                        List<CpcQueryTargetingReport> amazonAdTargetings = asinMap.get(asinImage.getAsin().toUpperCase());
                        if (CollectionUtils.isNotEmpty(amazonAdTargetings)) {
                            amazonAdTargetings.forEach(e -> {
                                e.setMainImage(asinImage.getImage());
                                e.setTitle(asinImage.getTitle());
                            });
                            needUpdateList.addAll(amazonAdTargetings);
                        }
                    }

                } catch (Exception e) {
                    log.error("同步图片错误");
                }

            }

            if (needUpdateList.size() > 0) {
                cpcQueryTargetingReportDao.batchSetAsinInfo(shop.getPuid(), needUpdateList);
            }
        }

        log.info("syncQueryTargetingReportAsinImage-同步asin图片结束，shopId:{}", shop.getId());
    }

    @Override
    public void syncSdTargetingAsinInfo(ShopAuth shop, long blockingTime) {
        if (shop == null) {
            return;
        }

        long offset = 0;
        int limit = 200;
        List<AmazonSdAdTargeting> amazonSdAdTargetings;
        while (true) {
            amazonSdAdTargetings = amazonSdAdTargetingDao.listNoAsinImage(shop.getPuid(), shop.getId(), offset, limit);
            if (CollectionUtils.isEmpty(amazonSdAdTargetings)) {
                break;
            }

            offset = amazonSdAdTargetings.get(amazonSdAdTargetings.size() - 1).getId();

            // 没有图片的asin
            Map<String, List<AmazonSdAdTargeting>> asinMap = amazonSdAdTargetings.stream()
                    .filter(e -> StringUtils.isNotBlank(e.getTargetText()))
                    .peek(e -> e.setTargetText(e.getTargetText().toUpperCase()))
                    .collect(Collectors.groupingBy(AmazonSdAdTargeting::getTargetText));

            if (asinMap.size() == 0) {
                log.error("syncAsinReportImage-没有需要同步asin信息的cpc定位, shopId:{}", shop.getId());
                continue;
            }

            List<String> asins = Lists.newArrayList(asinMap.keySet());
            List<List<String>> partition = Lists.partition(asins, 100);
            List<AmazonSdAdTargeting> needUpdateList = new ArrayList<>();
            for (List<String> subList : partition) {
                try {
                    List<AsinImage> listByAsins = syncAsinImageService.getListByAsins(shop.getPuid(),shop.getMarketplaceId(),subList);
                    for (AsinImage asinImage : listByAsins){
                        if(StringUtils.isBlank(asinImage.getImage()) || StringUtils.isBlank(asinImage.getTitle())){
                            continue;
                        }
                        List<AmazonSdAdTargeting> amazonAdTargetings = asinMap.get(asinImage.getAsin().toUpperCase());
                        if (CollectionUtils.isNotEmpty(amazonAdTargetings)) {
                            amazonAdTargetings.forEach(e -> {
                                e.setImgUrl(asinImage.getImage());
                                e.setTitle(asinImage.getTitle());
                            });
                            needUpdateList.addAll(amazonAdTargetings);
                        }
                    }

                } catch (Exception e){
                    log.error("同步图片错误");
                }
            }
            if (needUpdateList.size() > 0) {
                amazonSdAdTargetingDao.batchSetAsinImage(shop.getPuid(), needUpdateList);
            }
        }

        log.info("syncAsinReportImage-同步asin图片结束，shopId:{}", shop.getId());
    }

    @Override
    public void syncSbTargetingAsinInfo(ShopAuth shop, long blockingTime) {
        if (shop == null) {
            return;
        }

        long offset = 0;
        int limit = 200;
        List<AmazonSbAdTargeting> amazonSbAdTargetings;
        while (true) {
            amazonSbAdTargetings = sbAdTargetingDao.listNoAsinImage(shop.getPuid(), shop.getId(), offset, limit);
            if (CollectionUtils.isEmpty(amazonSbAdTargetings)) {
                break;
            }

            offset = amazonSbAdTargetings.get(amazonSbAdTargetings.size() - 1).getId();

            // 没有图片的asin
            Map<String, List<AmazonSbAdTargeting>> asinMap = amazonSbAdTargetings.stream()
                    .filter(e -> StringUtils.isNotBlank(e.getTargetText()))
                    .peek(e -> e.setTargetText(e.getTargetText().toUpperCase()))
                    .collect(Collectors.groupingBy(AmazonSbAdTargeting::getTargetText));

            if (asinMap.size() == 0) {
                log.error("syncAsinReportImage-没有需要同步asin信息的cpc定位, shopId:{}", shop.getId());
                continue;
            }
            List<String> asins = Lists.newArrayList(asinMap.keySet());
            List<List<String>> partition = Lists.partition(asins, 100);
            List<AmazonSbAdTargeting> needUpdateList = new ArrayList<>();
            for (List<String> subList : partition) {
                List<AsinImage> listByAsins = syncAsinImageService.getListByAsins(shop.getPuid(),shop.getMarketplaceId(),subList);
                for (AsinImage asinImage : listByAsins){
                    if(StringUtils.isBlank(asinImage.getImage()) || StringUtils.isBlank(asinImage.getTitle())){
                        continue;
                    }
                    List<AmazonSbAdTargeting> amazonAdTargetings = asinMap.get(asinImage.getAsin().toUpperCase());
                    if (CollectionUtils.isNotEmpty(amazonAdTargetings)) {
                        amazonAdTargetings.forEach(e -> {
                            e.setImgUrl(asinImage.getImage());
                            e.setTitle(asinImage.getTitle());
                        });
                        needUpdateList.addAll(amazonAdTargetings);
                    }
                }
            }
            if (needUpdateList.size() > 0) {
                sbAdTargetingDao.batchSetAsinImage(shop.getPuid(), needUpdateList);
            }
        }

        log.info("syncAsinReportImage-同步asin图片结束，shopId:{}", shop.getId());
    }

    @Override
    public void syncSdNeTargetingAsinInfo(ShopAuth shop, long blockingTime) {
        if (shop == null) {
            return;
        }

        long offset = 0;
        int limit = 200;
        List<AmazonSdAdNeTargeting> amazonSdAdTargetings;
        while (true) {
            amazonSdAdTargetings = amazonSdAdNeTargetingDao.listNoAsinImage(shop.getPuid(),
                    shop.getId(), offset, limit);
            if (CollectionUtils.isEmpty(amazonSdAdTargetings)) {
                break;
            }

            offset = amazonSdAdTargetings.get(amazonSdAdTargetings.size() - 1).getId();

            // 没有图片的asin
            Map<String, List<AmazonSdAdNeTargeting>> asinMap = amazonSdAdTargetings.stream()
                    .filter(e -> StringUtils.isNotBlank(e.getTargetText()))
                    .peek(e -> e.setTargetText(e.getTargetText().toUpperCase()))
                    .collect(Collectors.groupingBy(AmazonSdAdNeTargeting::getTargetText));

            if (asinMap.size() == 0) {
                log.error("syncAsinReportImage-没有需要同步asin信息的cpc定位, shopId:{}", shop.getId());
                continue;
            }
            List<String> asins = Lists.newArrayList(asinMap.keySet());
            List<List<String>> partition = Lists.partition(asins, 100);
            List<AmazonSdAdNeTargeting> needUpdateList = new ArrayList<>();
            for (List<String> subList : partition) {
                try {

                    List<AsinImage> listByAsins = syncAsinImageService.getListByAsins(shop.getPuid(),shop.getMarketplaceId(),subList);
                    for (AsinImage asinImage : listByAsins){
                        if(StringUtils.isBlank(asinImage.getImage()) || StringUtils.isBlank(asinImage.getTitle())){
                            continue;
                        }
                        List<AmazonSdAdNeTargeting> amazonAdTargetings = asinMap.get(asinImage.getAsin().toUpperCase());
                        if (CollectionUtils.isNotEmpty(amazonAdTargetings)) {
                            amazonAdTargetings.forEach(e -> {
                                e.setImgUrl(asinImage.getTitle());
                                e.setTitle(asinImage.getTitle());
                            });
                            needUpdateList.addAll(amazonAdTargetings);
                        }
                    }
                } catch (Exception e1) {
                    log.error("syncTargetAsinInfo1", e1);
                }

            }

            if (needUpdateList.size() > 0) {
                amazonSdAdNeTargetingDao.batchSetAsinImage(shop.getPuid(), needUpdateList);
            }
        }

        log.info("syncAsinReportImage-同步asin图片结束，shopId:{}", shop.getId());
    }

    private Product convertToProduct(com.amazonservices.mws.products.model.Product p) {
        Product amProduct = new Product();
        if (p.getIdentifiers() != null && p.getIdentifiers().getMarketplaceASIN() != null
                && p.getIdentifiers().getMarketplaceASIN().getASIN() != null) {
            amProduct.setAsin(p.getIdentifiers().getMarketplaceASIN().getASIN());
        }

        AttributeSetList attributeSetList = p.getAttributeSets();
        MwsXmlReader reader2 = new MwsXmlReader(SpecialCharUnicodeUtil.dealXML(attributeSetList.toXML()));
        AmznAttributeSetList attributeSet = reader2.readValue(AmznAttributeSetList.class);
        if (attributeSet != null && attributeSet.getItemAttributes() != null && attributeSet.getItemAttributes().size() > 0) {
            AmznItemAttributes attribute = attributeSet.getItemAttributes().get(0);
            if(attribute.getSmallImage() != null) {
                log.info("asin: "+amProduct.getAsin()+" 图片："+attribute.getSmallImage().getUrl()+"启用采集");
                if (amProduct.getChildAsins() == null && attribute.getSmallImage().getUrl().contains("no-img")){
                } else {
                    String mainImage = attribute.getSmallImage().getUrl();
                    amProduct.setMainImage(mainImage);
                }
            }

            if (StringUtils.isNotBlank(attribute.getTitle())) {
                amProduct.setTitle(attribute.getTitle());
            }
        }
        return amProduct;
    }

    @Override
    public void confirmAdGroupType(ShopAuth shop, String campaignId) {
        confirmAdGroupType(shop, campaignId, null);
    }

    /**
     * 通过关联查询广告数据确定广告组的类型，这个类型前后端交互时要用，暂时还不能去掉
     * 要去掉需要和设计讨论下，改下交互
     * sp整个店铺或根据广告活动id处理adGgroupType
     * @param shop：
     * @param campaignId
     */
    @Override
    public void confirmAdGroupType(ShopAuth shop, String campaignId, String groupId) {
        List<AmazonAdGroup> amazonAdGroups = amazonAdGroupDao.listNoAdGroupType(shop.getPuid(), shop.getId(), campaignId, groupId);
        confirmAdGroupTypeBatch(shop, amazonAdGroups);
    }




    /**
     * sp根据广告组id处理adGgroupType
     * @param shop
     * @param groupIdList
     */
    @Override
    public List<AmazonAdGroup> confirmAdGroupTypeByGroupIdList(ShopAuth shop, List<String> groupIdList) {
        List<AmazonAdGroup> amazonAdGroups = amazonAdGroupDao.listNoAdGroupType(shop.getPuid(), shop.getId(), null, new HashSet<>(groupIdList));
        return confirmAdGroupTypeBatch(shop, amazonAdGroups);
    }



    /**
     * sp根据广告组id处理adGgroupType，批处理，返回处理失败的广告组
     * @param shop
     * @param amazonAdGroups
     */
    private List<AmazonAdGroup> confirmAdGroupTypeBatch(ShopAuth shop, List<AmazonAdGroup> amazonAdGroups) {

        List<AmazonAdGroup> failList = new ArrayList<>();

        if (CollectionUtils.isEmpty(amazonAdGroups)) {
            return failList;
        }

        //分片批处理优化
        List<List<AmazonAdGroup>> partitionList = Lists.partition(amazonAdGroups, 500);
        partitionList.forEach(partition -> {
            //Lists.partition是假分片，有增删的情况下需要新拷贝一个list
            List<AmazonAdGroup> list = new ArrayList<>(partition);
            //最终需要修改的group
            List<AmazonAdGroup> needUpdateList = new ArrayList<>(list.size());
            //活动id集合和组id集合
            Set<String> campaignIdSet = new HashSet<>();
            Set<String> groupIdSet = new HashSet<>();
            list.forEach(x -> {
                campaignIdSet.add(x.getCampaignId());
                groupIdSet.add(x.getAdGroupId());
            });


            //通过活动判断组类型
            List<AmazonAdCampaignAll> campaignList = amazonAdCampaignDao.getByCampaignIds(shop.getPuid(), shop.getId(), shop.getMarketplaceId(), new ArrayList<>(campaignIdSet));
            //活动集合转map
            Map<String, AmazonAdCampaignAll> campaignMap = campaignList.stream().collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, Function.identity(), (oldV, newV) -> newV));
            Iterator<AmazonAdGroup> autoIterator = list.iterator();
            while (autoIterator.hasNext()) {
                AmazonAdGroup amazonAdGroup = autoIterator.next();
                AmazonAdCampaignAll amazonAdCampaign = campaignMap.get(amazonAdGroup.getCampaignId());
                if (amazonAdCampaign != null) {
                    if (Constants.AUTO.equalsIgnoreCase(amazonAdCampaign.getTargetingType())) {
                        amazonAdGroup.setAdGroupType(Constants.GROUP_TYPE_AUTO);
                        needUpdateList.add(amazonAdGroup);
                        autoIterator.remove();
                        groupIdSet.remove(amazonAdGroup.getAdGroupId());
                    }
                }
            }


            //处理手动-关键词
            computeGroupType4Sp(shop.getPuid(), shop.getId(), list, groupIdSet, needUpdateList, Constants.GROUP_TYPE_KEYWORD,
                    (puid, shopId, curGroupIdSet) -> amazonAdKeywordDaoRoutingService.countByGroupIdSet(puid, shopId, curGroupIdSet));

            //处理手动-投放
            computeGroupType4Sp(shop.getPuid(), shop.getId(), list, groupIdSet, needUpdateList, Constants.GROUP_TYPE_TARGETING,
                    (puid, shopId, curGroupIdSet) -> amazonAdTargetDaoRoutingService.countByGroupIdSet(puid, shopId, curGroupIdSet));

            //再根据投放检查一下自动投放
            computeGroupType4Sp(shop.getPuid(), shop.getId(), list, groupIdSet, needUpdateList, Constants.GROUP_TYPE_AUTO,
                    (puid, shopId, curGroupIdSet) -> amazonAdTargetDaoRoutingService.countByGroupIdSet4Auto(puid, shopId, curGroupIdSet));

            //批量更新
            if (CollectionUtils.isNotEmpty(needUpdateList)) {
                amazonAdGroupDao.batchUpdateAdGrpupType(shop.getPuid(), needUpdateList);
            }

            if (CollectionUtils.isNotEmpty(list)) {
                failList.addAll(list);
            }

        });

        return failList;
    }


    /**
     * sp通用计算adGroupType
     * @param puid              puid
     * @param shopId            shopid
     * @param list              待处理的adGroupType为空的广告组
     * @param groupIdSet        待处理的adGroupType为空的广告组id集合
     * @param needUpdateList    收集需要更新的广告组
     * @param adGroupType       正确的AdGroupType的值
     * @param function          函数式接口
     */
    private void computeGroupType4Sp(Integer puid,
                    Integer shopId,
                    List<AmazonAdGroup> list,
                    Set<String> groupIdSet,
                    List<AmazonAdGroup> needUpdateList,
                    String adGroupType,
                    ThFunction<Integer, Integer, Set<String>, List<AdGroupTargetCountDto>> function){
        //处理手动-投放
        if (CollectionUtils.isNotEmpty(list)) {
            List<AdGroupTargetCountDto> adGroupTargetCountDtoList = function.apply(puid, shopId, groupIdSet);
            Map<String, AdGroupTargetCountDto> countDtoMap = adGroupTargetCountDtoList.stream().collect(Collectors.toMap(AdGroupTargetCountDto::getAdGroupId, Function.identity(), (oldV, newV) -> newV));

            if (!countDtoMap.isEmpty()) {
                Iterator<AmazonAdGroup> targetIterator = list.iterator();
                while (targetIterator.hasNext()) {
                    AmazonAdGroup amazonAdGroup = targetIterator.next();
                    AdGroupTargetCountDto countDao = countDtoMap.get(amazonAdGroup.getAdGroupId());
                    if (countDao != null && countDao.getTargetCount() > 0) {
                        amazonAdGroup.setAdGroupType(adGroupType);
                        needUpdateList.add(amazonAdGroup);
                        targetIterator.remove();
                        groupIdSet.remove(amazonAdGroup.getAdGroupId());
                    }
                }
            }
        }
    }


    /**
     * sp根据投放数据处理adGgroupType
     * @param shop
     * @param spAdGroupTypeDtoList
     */
    @Override
    public void confirmSpAdGroupTypeByTargetData(ShopAuth shop, List<SpAdGroupTypeDto> spAdGroupTypeDtoList) {
        //通过投放数据确定sp广告组投放的类型
        List<List<SpAdGroupTypeDto>> partitionList = Lists.partition(spAdGroupTypeDtoList, 500);
        partitionList.forEach(partition -> {

            Set<String> campaignIdSet = new HashSet<>();
            Set<String> groupIdSet = new HashSet<>();
            Map<String, String> map = new HashMap<>();

            partition.forEach(x -> {
                campaignIdSet.add(x.getCampaignId());
                groupIdSet.add(x.getGroupId());
                map.put(x.getGroupId(), x.getAdGroupType());
            });

            List<AmazonAdGroup> amazonAdGroups = amazonAdGroupDao.listNoAdGroupType(shop.getPuid(), shop.getId(), campaignIdSet, groupIdSet);
            List<AmazonAdGroup> needUpdate = new ArrayList<>();

            if (CollectionUtils.isNotEmpty(amazonAdGroups)) {
                for (AmazonAdGroup adGroup : amazonAdGroups) {
                    String spAdGroupType = map.get(adGroup.getAdGroupId());
                    if(StringUtils.isNotBlank(spAdGroupType)) {
                        adGroup.setAdGroupType(spAdGroupType);
                        needUpdate.add(adGroup);
                    }
                }
                if (CollectionUtils.isNotEmpty(needUpdate)) {
                    amazonAdGroupDao.batchUpdateAdGrpupType(shop.getPuid(), needUpdate);
                }
            }
        });
    }


    /**
     * 通过关联查询广告数据确定sb广告组的adGroupType，adFormat，landingPage
     */
    @Override
    public List<AmazonSbAdGroup> confirmAdGroupTargetTypeAndAdFormatAndLandingPage(ShopAuth shop, String campaignId, List<String> groupIdList) {
        List<AmazonSbAdGroup> groupList = amazonSbAdGroupDao.getListByNullField(shop.getPuid(), shop.getId(), campaignId, groupIdList, Arrays.asList("ad_group_type","ad_format", "landing_page"));
        return confirmAdGroupTargetTypeAndAdFormatAndLandingPageBatch(shop, groupList);
    }

    /**
     * 批量通过关联查询广告数据确定sb广告组的adGroupType，adFormat，landingPage，批处理
     * @param shop
     * @param groupList
     */
    private List<AmazonSbAdGroup> confirmAdGroupTargetTypeAndAdFormatAndLandingPageBatch(ShopAuth shop, List<AmazonSbAdGroup> groupList) {

        List<AmazonSbAdGroup> failList = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(groupList)) {
            //分片批处理
            List<List<AmazonSbAdGroup>> partitionList = Lists.partition(groupList, 500);
            partitionList.forEach(list -> {

                //转map
                Map<String, AmazonSbAdGroup> groupMap = new HashMap<>();

                //需要修改的
                Set<String> needUpdateGroupIdSet = new HashSet<>();

                //需要处理adGroupType的
                List<String> blankAdGroupTypeGroupIdList = new ArrayList<>();

                //需要处理adFormat或landingPage的
                List<String> blankAdFormatGroupIdList = new ArrayList<>();

                list.forEach(x -> {
                    groupMap.put(x.getAdGroupId(), x);
                    if (StringUtils.isBlank(x.getAdGroupType())) {
                        blankAdGroupTypeGroupIdList.add(x.getAdGroupId());
                    }
                    if (StringUtils.isAnyBlank(x.getAdFormat(), x.getLandingPage())) {
                        blankAdFormatGroupIdList.add(x.getAdGroupId());
                    }
                });


                //处理adGroupType
                //关键词
                computeGroupType4Sb(shop.getPuid(), shop.getId(), blankAdGroupTypeGroupIdList, groupMap, needUpdateGroupIdSet, SbAdGroupTypeEnum.keyword,
                        (puid, shopId, groupIdList) -> amazonSbAdKeywordDao.countByGroupIdList(puid, shopId, groupIdList));

                //投放
                computeGroupType4Sb(shop.getPuid(), shop.getId(), blankAdGroupTypeGroupIdList, groupMap, needUpdateGroupIdSet, SbAdGroupTypeEnum.product,
                        (puid, shopId, groupIdList) -> sbAdTargetingDao.countByGroupIdList(puid, shopId, groupIdList));

                //否定关键词
                computeGroupType4Sb(shop.getPuid(), shop.getId(), blankAdGroupTypeGroupIdList, groupMap, needUpdateGroupIdSet, SbAdGroupTypeEnum.keyword,
                        (puid, shopId, groupIdList) -> amazonSbAdNeKeywordDao.countByGroupIdList(puid, shopId, groupIdList));

                //否定投放
                computeGroupType4Sb(shop.getPuid(), shop.getId(), blankAdGroupTypeGroupIdList, groupMap, needUpdateGroupIdSet, SbAdGroupTypeEnum.product,
                        (puid, shopId, groupIdList) -> amazonSbAdNeTargetingDao.countByGroupIdList(puid, shopId, groupIdList));


                //处理adFormat和landingPage
                if (CollectionUtils.isNotEmpty(blankAdFormatGroupIdList)) {
                    List<AmazonSbAds> sbAdsList  = amazonSbAdsDao.getAdGroupByIds(shop.getPuid(), shop.getId(), blankAdFormatGroupIdList);
                    if(CollectionUtils.isEmpty(sbAdsList)){
                        return;
                    }
                    Map<String, AmazonSbAds> amazonSbAdsMap = sbAdsList.stream()
                            .filter(e -> e != null && StringUtils.isNotBlank(e.getAdGroupId()) && StringUtils.isNotBlank(e.getAdFormat()))
                            .collect(Collectors.groupingBy(AmazonSbAds::getAdGroupId,
                                    Collectors.collectingAndThen(Collectors.toList(), value -> value.get(0))));


                    for (String blankAdFormatGroupId : blankAdFormatGroupIdList) {
                        AmazonSbAds amazonSbAds = amazonSbAdsMap.get(blankAdFormatGroupId);
                        AmazonSbAdGroup group = groupMap.get(blankAdFormatGroupId);
                        if (amazonSbAds != null && group != null) {
                            if(StringUtils.isNotBlank(amazonSbAds.getAdFormat())){
                                group.setAdFormat(amazonSbAds.getAdFormat());
                            }
                            if (StringUtils.isNotBlank(amazonSbAds.getLandingPage())) {
                                group.setLandingPage(amazonSbAds.getLandingPage());
                            }
                            needUpdateGroupIdSet.add(blankAdFormatGroupId);
                        }
                    }
                }

                //更新和重试的
                List<AmazonSbAdGroup> updateList = new ArrayList<>();
                List<AmazonSbAdGroup> retryList = new ArrayList<>();
                groupMap.forEach((k, v) -> {
                    if (needUpdateGroupIdSet.contains(k)) {
                        updateList.add(v);
                    }
                    if (StringUtils.isAnyBlank(v.getAdGroupType(), v.getAdFormat(), v.getLandingPage())) {
                        retryList.add(v);
                    }
                });

                //批量更新
                if (CollectionUtils.isNotEmpty(updateList)) {
                    amazonSbAdGroupDao.batchUpdateAdGroupTypeAndAdFormat(shop.getPuid(), updateList);
                }

                if(CollectionUtils.isNotEmpty(retryList)) {
                    failList.addAll(retryList);
                }
            });
        }

        return failList;
    }

    /**
     * 通过查询sb投放确定sb广告组的adGroupType
     * @param shop
     * @param campaignId
     */
    @Override
    public void confirmAdGroupTargetType(ShopAuth shop, String campaignId) {
        confirmAdGroupTargetType(shop, campaignId, null);
    }

    /**
     * 通过关联查询广告投放数据（投放和否投）确定sb广告活动投放的类型
     */
    public void confirmAdGroupTargetType(ShopAuth shop, String campaignId, String groupId) {
        confirmAdGroupTargetType(shop, campaignId, groupId, true, true);
    }

    /**
     * 通过关联查询广告投放数据（投放和否投）确定sb广告活动投放的类型
     */
    @Override
    public void confirmAdGroupTargetType(ShopAuth shop, String campaignId, String groupId, boolean useTargeting, boolean useNetargeting) {

        if (!useTargeting && !useNetargeting) {
            return;
        }

        List<AmazonSbAdGroup> groupList = amazonSbAdGroupDao.listNoAdGroupType(shop.getPuid(), shop.getId(), campaignId, groupId);
        if (CollectionUtils.isNotEmpty(groupList)) {
            //优化为批处理
            List<List<AmazonSbAdGroup>> partitionList = Lists.partition(groupList, 500);
            partitionList.forEach(list -> {
                //转map
                Map<String, AmazonSbAdGroup> groupMap = new HashMap<>();
                //需要检查的groupId
                List<String> blankAdGroupTypeList = new ArrayList<>(list.size());
                list.forEach(x -> {
                    groupMap.put(x.getAdGroupId(), x);
                    blankAdGroupTypeList.add(x.getAdGroupId());
                });

                //存储最终需要实际修改的groupId
                Set<String> needUpdateGroupIdSet = new HashSet<>();

                if (useTargeting) {
                    //关键词
                    computeGroupType4Sb(shop.getPuid(), shop.getId(), blankAdGroupTypeList, groupMap, needUpdateGroupIdSet, SbAdGroupTypeEnum.keyword,
                            (puid, shopId, groupIdList) -> amazonSbAdKeywordDao.countByGroupIdList(puid, shopId, groupIdList));

                    //投放
                    computeGroupType4Sb(shop.getPuid(), shop.getId(), blankAdGroupTypeList, groupMap, needUpdateGroupIdSet, SbAdGroupTypeEnum.product,
                            (puid, shopId, groupIdList) -> sbAdTargetingDao.countByGroupIdList(puid, shopId, groupIdList));
                }

                if (useNetargeting) {
                    //否定关键词
                    computeGroupType4Sb(shop.getPuid(), shop.getId(), blankAdGroupTypeList, groupMap, needUpdateGroupIdSet, SbAdGroupTypeEnum.keyword,
                            (puid, shopId, groupIdList) -> amazonSbAdNeKeywordDao.countByGroupIdList(puid, shopId, groupIdList));

                    //否定投放
                    computeGroupType4Sb(shop.getPuid(), shop.getId(), blankAdGroupTypeList, groupMap, needUpdateGroupIdSet, SbAdGroupTypeEnum.product,
                            (puid, shopId, groupIdList) -> amazonSbAdNeTargetingDao.countByGroupIdList(puid, shopId, groupIdList));
                }


                //取出更新的AmazonSbAdGroup
                List<AmazonSbAdGroup> updateList = new ArrayList<>(needUpdateGroupIdSet.size());
                groupMap.forEach((k, v) -> {
                    if (needUpdateGroupIdSet.contains(k)) {
                        updateList.add(v);
                    }
                });

                //批量更新
                if (CollectionUtils.isNotEmpty(updateList)) {
                    amazonSbAdGroupDao.batchUpdateAdGroupType(shop.getPuid(), updateList);
                }
            });
        }
    }

    /**
     * sb通用计算adGroupType
     * @param puid
     * @param shopId
     * @param blankAdGroupTypeList
     * @param groupMap
     * @param needUpdateGroupIdSet
     * @param adGroupType
     * @param function
     */
    private void computeGroupType4Sb(Integer puid,
                     Integer shopId,
                     List<String> blankAdGroupTypeList,
                     Map<String, AmazonSbAdGroup> groupMap,
                     Set<String> needUpdateGroupIdSet,
                     SbAdGroupTypeEnum adGroupType,
                     ThFunction<Integer, Integer, List<String>, List<AdGroupTargetCountDto>> function) {

        //执行对应的函数查询投放数据
        Map<String, AdGroupTargetCountDto> countDtoMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(blankAdGroupTypeList)) {
            List<AdGroupTargetCountDto> countDtoList = function.apply(puid, shopId, blankAdGroupTypeList);
            countDtoMap = countDtoList.stream().collect(Collectors.toMap(AdGroupTargetCountDto::getAdGroupId, Function.identity(), (oldV, newV) -> newV));
        }

        if (!countDtoMap.isEmpty()) {
            //遍历
            Iterator<String> iterator = blankAdGroupTypeList.iterator();
            while (iterator.hasNext()) {
                String groupId = iterator.next();
                AdGroupTargetCountDto countDao = countDtoMap.get(groupId);
                //如果投放有数据
                if (countDao != null && countDao.getTargetCount() > 0) {
                    AmazonSbAdGroup group = groupMap.get(countDao.getAdGroupId());
                    if (group == null) {
                        continue;
                    }
                    //设置对应的广告组类型
                    group.setAdGroupType(adGroupType.getAdGroupType());
                    //需要更新的集合
                    needUpdateGroupIdSet.add(groupId);
                    //移除已处理
                    iterator.remove();
                }
            }
        }
    }

    /**
     * 通过关联查询广告数据确定sb广组投放类型
     * 以及添加广告创意着陆页数据
     */
    private void confirmAdGroupAdFormat(ShopAuth shop, String campaignId) {
        List<AmazonSbAdGroup> groupList = amazonSbAdGroupDao.getListByNullField(shop.getPuid(), shop.getId(), campaignId, Arrays.asList("ad_format", "landing_page"));
        List<AmazonSbAdGroup> needUpdate = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(groupList)) {
            List<AmazonSbAds> sbAdsList  = amazonSbAdsDao.getAdGroupByIds(shop.getPuid(), shop.getId(), groupList.stream().map(AmazonSbAdGroup::getAdGroupId).collect(Collectors.toList()));
            if(CollectionUtils.isEmpty(sbAdsList)){
                return;
            }
            Map<String, AmazonSbAds> amazonSbAdsMap = sbAdsList.stream()
                    .filter(e -> e != null && StringUtils.isNotBlank(e.getAdGroupId()) && StringUtils.isNotBlank(e.getAdFormat()))
                    .collect(Collectors.groupingBy(AmazonSbAds::getAdGroupId,
                            Collectors.collectingAndThen(Collectors.toList(), value -> value.get(0))));
            for (AmazonSbAdGroup adGroup : groupList) {
                AmazonSbAds amazonSbAds = amazonSbAdsMap.get(adGroup.getAdGroupId());
                if (amazonSbAds != null) {
                    if(StringUtils.isNotBlank(amazonSbAds.getAdFormat())){
                        adGroup.setAdFormat(amazonSbAds.getAdFormat());
                    }
                    if (StringUtils.isNotBlank(amazonSbAds.getLandingPage())) {
                        adGroup.setLandingPage(amazonSbAds.getLandingPage());
                    }
                    needUpdate.add(adGroup);
                }
            }
        }
        if (needUpdate.size() > 0) {
            amazonSbAdGroupDao.batchUpdateAdFormat(shop.getPuid(), needUpdate);
        }
    }

    @Override
    public void confirmSbAdGroupTargetTypeByTargetData(ShopAuth shopAuth, List<SbAdGroupTypeDto> sbAdGroupTypeDtoList) {
        //通过投放数据确定sb广告组投放的类型
        List<List<SbAdGroupTypeDto>> partitionList = Lists.partition(sbAdGroupTypeDtoList, 500);
        partitionList.forEach(partition -> {

            Set<String> campaignIdSet = new HashSet<>();
            Set<String> groupIdSet = new HashSet<>();
            Map<String, SbAdGroupTypeEnum> map = new HashMap<>();

            partition.forEach(x -> {
                campaignIdSet.add(x.getCampaignId());
                groupIdSet.add(x.getGroupId());
                map.put(x.getGroupId(), x.getSbAdGroupTypeEnum());
            });

            List<AmazonSbAdGroup> groupList = amazonSbAdGroupDao.listNoAdGroupType(shopAuth.getPuid(), shopAuth.getId(), campaignIdSet, groupIdSet);
            List<AmazonSbAdGroup> needUpdate = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(groupList)) {
                for (AmazonSbAdGroup adGroup : groupList) {
                    SbAdGroupTypeEnum sbAdGroupTypeEnum = map.get(adGroup.getAdGroupId());
                    if(sbAdGroupTypeEnum != null) {
                        adGroup.setAdGroupType(sbAdGroupTypeEnum.getAdGroupType());
                        needUpdate.add(adGroup);
                    }
                }
                if (CollectionUtils.isNotEmpty(needUpdate)) {
                    amazonSbAdGroupDao.batchUpdateAdGrpupType(shopAuth.getPuid(), needUpdate);
                }
            }
        });
    }


    @Override
    public void syncSpCampaignState(ShopAuth shop, String campaignId) {
        ThreadPoolUtil.getCpcAdShopAsyncPool().execute(() -> {
            try {
                cpcCampaignApiService.syncCampaigns(shop, campaignId, false);
            } catch (Exception e) {
                log.error("同步广告活动异常shopId:" + shop.getId(), e);
            }
        });
    }

    @Override
    public void syncSpKeyword(ShopAuth shop, String keywordId) {
        try {
            cpcKeywordsApiService.syncKeywords(shop, null, null, keywordId);
        } catch (Exception e) {
            log.error("同步关键词异常shopId:" + shop.getId(), e);
        }
    }

    @Override
    public void syncSpTarget(ShopAuth shop, String targetId) {
        try {
            cpcTargetingApiService.syncTargetings(shop, null, null, targetId);
        } catch (Exception e) {
            log.error("同步关键词异常shopId:" + shop.getId(), e);
        }
    }

    @Override
    public List<CampaignExtendEntityV3> detectAdTypeSyncSpCampaign(ShopAuth shop) {
        return cpcCampaignApiService.detectAdTypeSyncSpCampaign(shop);
    }

    @Override
    public List<CampaignV4> detectAdTypeSyncSbCampaign(ShopAuth shop) {
        return cpcSbCampaignApiService.detectAdTypeSyncSbCampaign(shop);
    }

    @Override
    public List<com.amazon.advertising.sd.mode.Campaign> detectAdTypeSyncSdCampaign(ShopAuth shop) {
        return cpcSdCampaignApiService.detectAdTypeSyncSdCampaign(shop);
    }

    /**
     * 修复广告数据确定sb广告活动投放的类型
     */
    @Override
    public void repairSbCampaingTargetType(ShopAuth shop, List<String> campaignId) {
        List<AmazonAdCampaignAll> campaignList = amazonSbAdCampaignDao.listByCampaignId(shop.getPuid(), shop.getId(), campaignId);
        if (CollectionUtils.isNotEmpty(campaignList)) {
            Lists.partition(campaignList,100).forEach(e->{
                List<AmazonAdCampaignAll> l = new ArrayList<>();
                for (AmazonAdCampaignAll campaign : e) {
                    boolean exist = amazonSbAdKeywordDao.exist(shop.getPuid(), shop.getId(), campaign.getCampaignId());
                    if (exist) {
                        campaign.setTargetType("keyword");
                        l.add(campaign);
                    }
                }
                if (!l.isEmpty()) {
                    amazonSbAdCampaignDao.batchUpdateCampaignTargetType(shop.getPuid(), l);
                }
            });

        }
    }

    @Override
    public void syncSdCreativeByShop(ShopAuth shop, String adGroupIds, String creativeId) {
        cpcSdCreativeApiService.syncSdCreative(shop, adGroupIds, creativeId);
    }


    @Override
    public void syncByShopProfileAndPortfolio(ShopAuth shop) {
        if (shop == null) {
            return;
        }

        try {
            profileApiService.getProfile(shop);
        } catch (Exception e) {
            log.error("同步顶级预算异常 shopId:" + shop.getId(), e);
        }


        //先获取到配置信息
        AmazonAdProfile amazonAdProfile = amazonAdProfileDao.getProfile(shop.getPuid(), shop.getId());
        if (amazonAdProfile == null) {
            log.error("syncCampaigns--配置信息为空");
            return;
        }

        try {
            portfolioApiService.syncPortfolio(shop, amazonAdProfile, null);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("同步广告组合异常 shopId:" + shop.getId(), e);
        }
    }

    @Override
    public void aggregationKeywordsData(Integer puid) {
        //从关键词投放表里同步关键词库详情数据
        String startStr = LocalDate.now().minusDays(7).format(DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD));
        String endStr = LocalDate.now().minusDays(1).format(DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD));
        List<KeywordLibsDetailVo> detailVos = Lists.newArrayListWithExpectedSize(2);
        List<String> keywordTextList = amazonAdKeywordsLibDao.getKeywordTextList(puid);
        if (CollectionUtils.isEmpty(keywordTextList)) {
            return ;
        }
        //通过关键词库里的shopId,keyowordText获取对应的sp,sb关键词投放数据
        try {
            List<KeywordLibsDetailVo> spKeywordList = amazonAdKeywordDaoRoutingService.aggregateKeyword(puid, keywordTextList);
            List<KeywordLibsDetailVo> sbKeywordList = amazonSbAdKeywordDao.aggregateSbKeyword(puid, keywordTextList);
            List<KeywordLibsDetailVo> spCampaignNeKeywordList = amazonAdCampaignNeKeywordsDao.aggregateNeKeyword(puid, keywordTextList);
            List<KeywordLibsDetailVo> sbNekeywordList = amazonSbAdNeKeywordDao.aggregateSbNeKeyword(puid, keywordTextList);
            detailVos.addAll(spKeywordList);
            detailVos.addAll(sbKeywordList);
            detailVos.addAll(spCampaignNeKeywordList);
            detailVos.addAll(sbNekeywordList);

            List<String> keywordIdList = detailVos.stream().filter(item -> Constants.SP.equalsIgnoreCase(item.getType())).map(KeywordLibsDetailVo::getKeywordId).filter(Objects::nonNull).collect(Collectors.toList());
            List<String> sbKeywordIdList = detailVos.stream().filter(item -> Constants.SB.equalsIgnoreCase(item.getType())).map(KeywordLibsDetailVo::getKeywordId).filter(Objects::nonNull).collect(Collectors.toList());
            //SP报告数据
            Map<String, AmazonAdKeywordReport> keywordReportMap = amazonAdKeywordReportDao.listSumReportsByPuid(puid, keywordIdList, startStr, endStr)
                    .stream()
                    .filter(Objects::nonNull)
                    .collect(Collectors.toMap(AmazonAdKeywordReport::getKeywordId, e -> e, (e1, e2) -> e1));
            //SB报告数据
            Map<String, AmazonAdSbKeywordReport> sbKeywordReportMap = amazonAdSbKeywordReportDao.listSumReportsByPuid(puid, sbKeywordIdList, startStr, endStr)
                    .stream()
                    .filter(Objects::nonNull)
                    .collect(Collectors.toMap(AmazonAdSbKeywordReport::getKeywordId, e -> e, (e1, e2) -> e1));

            //detailDtos封装详情信息
            List<AmazonAdKeywordsLibDetail> details = new ArrayList<>(detailVos.size());
            List<AmazonAdKeywordsLibDetail> detailsList;
            List<List<KeywordLibsDetailVo>> detailPartionVo = Lists.partition(detailVos,10000);
            for (List<KeywordLibsDetailVo> detailVosList : detailPartionVo){
                detailsList = detailVosList.parallelStream().filter(Objects::nonNull).map(e -> {
                    AmazonAdKeywordsLibDetail detail = new AmazonAdKeywordsLibDetail();
                    if (Constants.SP.equals(e.getType())) {
                        AmazonAdKeywordReport spKeywordReport = keywordReportMap.get(e.getKeywordId());
                        if (spKeywordReport != null) {
                            e.setSingleQuantity7d(spKeywordReport.getSaleNum());
                            e.setAverageAcos7d(spKeywordReport.getAcos());
                        }
                    }
                    if (Constants.SB.equals(e.getType())) {
                        AmazonAdSbKeywordReport sbKeywordReport = sbKeywordReportMap.get(e.getKeywordId());
                        if (sbKeywordReport != null) {
                            e.setSingleQuantity7d(sbKeywordReport.getConversions14d());
                            e.setAverageAcos7d(sbKeywordReport.getReportBase().getAcos());
                        }
                    }
                    e.setKeywordText(e.getKeywordText().trim());
                    BeanUtils.copyProperties(e, detail);
                    //注意：sql带keywordText条件查询不区分大小写，当需要根据keywordText分组汇总时，把数据统一处理成小写
                    e.setKeywordText(e.getKeywordText().toLowerCase());
                    return detail;
                }).collect(Collectors.toList());
                details.addAll(detailsList);
            }

            if (CollectionUtils.isNotEmpty(details)) {
                amazonAdKeywordsLibDetailDao.insertOnDuplicateKeyUpdate(puid, details);
                long t1 = Instant.now().toEpochMilli();
                //从detailVos里同步关键词库列表数据
                syncKeywordLibList(puid, detailVos, keywordTextList);
                log.info("同步关键词库列表数据，共耗时：{}", Instant.now().toEpochMilli() - t1);
            }
        } catch (Exception e) {
            log.error("关键词库详情数据同步异常:{}", e);
        }
    }

    @Override
    public List<CampaignExtendEntityV3> checkAdTypeSyncSpCampaign(ShopAuth shop) throws IOException, ClassNotFoundException {
        return cpcCampaignApiService.checkAdTypeSyncSpCampaign(shop);
    }

    @Override
    public List<CampaignV4> checkAdTypeSyncSbCampaign(ShopAuth shop) {
        return cpcSbCampaignApiService.checkAdTypeSyncSbCampaign(shop);
    }

    @Override
    public List<Campaign> checkAdTypeSyncSdCampaign(ShopAuth shop) {
        return cpcSdCampaignApiService.checkAdTypeSyncSdCampaign(shop);
    }

    private void syncKeywordLibList(Integer puid, List<KeywordLibsDetailVo> details, List<String> keywordTexts) {
        try {
            CountByPuidAndKeywordTextParam param = new CountByPuidAndKeywordTextParam();
            param.setPuid(puid);
            param.setUid(puid);
            param.setKeywordList(keywordTexts);
            List<MonitorKeywordVo> monitorKeywords  = null;
            try {
                monitorKeywords = keywordRankMonitorFeignService.getCountByPuidAndKeywordText(param);
            } catch (Exception e) {
                log.error("广告定时任务调用工具服务异常:", e);
                return;
            }
            if (CollectionUtils.isEmpty(monitorKeywords)) {
                return;
            }
            Map<String, MonitorKeywordVo> monKeywordNumMap = monitorKeywords.stream().collect(Collectors.toMap(MonitorKeywordVo::getKeywordText, Function.identity()));
            List<AmazonAdKeywordsLib> keywordsLibs = amazonAdKeywordsLibDao.listByPuid(puid);
            keywordsLibs.stream().peek(item -> {
                //获取uid下的shopId
                List<Integer> shopIds = sellfoxShopUserDao.getShopIdListByUser(puid, item.getUid());
                Map<String, Long> targetNumMap = details.stream()
                        .filter(vo -> shopIds.contains(vo.getShopId()) && Constants.BIDDABLE.equals(vo.getTargetType()) &&
                                Lists.newArrayList(Constants.ENABLED,Constants.PAUSED).contains(vo.getState()))
                        .collect(Collectors.groupingBy(KeywordLibsDetailVo::getKeywordText, Collectors.counting()));
                Map<String, Long> negateTargetNumMap = details.stream()
                        .filter(vo -> shopIds.contains(vo.getShopId()) && Constants.NEGATIVE.equals(vo.getTargetType()) &&
                                Lists.newArrayList(Constants.ENABLED,Constants.PAUSED).contains(vo.getState()))
                        .collect(Collectors.groupingBy(KeywordLibsDetailVo::getKeywordText, Collectors.counting()));
                Map<String, Integer> singleQuantity7dMap = details.stream()
                        .filter(vo -> shopIds.contains(vo.getShopId()) && Lists.newArrayList(Constants.ENABLED,Constants.PAUSED).contains(vo.getState()))
                        .collect(Collectors.groupingBy(KeywordLibsDetailVo::getKeywordText,
                                Collectors.summingInt(vo -> vo.getSingleQuantity7d() != null ? vo.getSingleQuantity7d() : 0)));
                Map<String, Double> averageAcos7dMap = details.stream()
                        .filter(vo -> shopIds.contains(vo.getShopId()) && Lists.newArrayList(Constants.ENABLED,Constants.PAUSED).contains(vo.getState())
                                && vo.getAverageAcos7d() != null && vo.getAverageAcos7d().compareTo(BigDecimal.ZERO) > 0)
                        .collect(Collectors.groupingBy(KeywordLibsDetailVo::getKeywordText,
                                Collectors.averagingDouble(vo -> vo.getAverageAcos7d().doubleValue())));
                //投放预览数
                if (targetNumMap.get(item.getKeywordText().toLowerCase()) != null) {
                    int targetNum = targetNumMap.get(item.getKeywordText().toLowerCase()).intValue();
                    item.setTargetNum(targetNum);
                } else {
                    item.setTargetNum(0);
                }
                if (negateTargetNumMap.get(item.getKeywordText().toLowerCase()) != null) {
                    int negateTargetNum = negateTargetNumMap.get(item.getKeywordText().toLowerCase()).intValue();
                    item.setNegateTargetNum(negateTargetNum);
                } else {
                    item.setNegateTargetNum(0);
                }
                if (singleQuantity7dMap.get(item.getKeywordText().toLowerCase()) != null) {
                    int singleQuantity7d = singleQuantity7dMap.get(item.getKeywordText().toLowerCase());
                    item.setSingleQuantity7d(singleQuantity7d);
                } else {
                    item.setSingleQuantity7d(0);
                }
                if (averageAcos7dMap.get(item.getKeywordText().toLowerCase()) != null) {
                    double averageAcos7d = averageAcos7dMap.get(item.getKeywordText().toLowerCase());
                    item.setAverageAcos7d(BigDecimal.valueOf(averageAcos7d));
                } else {
                    item.setAverageAcos7d(BigDecimal.ZERO);
                }
                if (monKeywordNumMap.get(item.getKeywordText().toLowerCase()) != null) {
                    MonitorKeywordVo monKeywordNum = monKeywordNumMap.get(item.getKeywordText().toLowerCase());
                    int monCount = monKeywordNum.getCount();
                    item.setKeywordMonitor(monCount);
                } else {
                    item.setKeywordMonitor(0);
                }
            }).collect(Collectors.toList());
            amazonAdKeywordsLibDao.insertOnDuplicateKeyUpdate(puid, keywordsLibs);
        }catch (Exception e) {
            log.error("关键词库列表数据同步异常:{}", e);
        }
    }
}