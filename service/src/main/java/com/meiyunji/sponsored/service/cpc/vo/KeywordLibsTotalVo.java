package com.meiyunji.sponsored.service.cpc.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

//汇总数据
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class KeywordLibsTotalVo {
    /**
     * 投放条数
     */
    Integer putNum = 0;
    /**
     * 广告曝光量
     */
    private Long impressions = 0L;

    /**
     * 广告点击量
     */
    private Long clicks = 0L;

    /**
     * 广告点击率 点击量/曝光量 * 100%
     */
    private BigDecimal clickRate = BigDecimal.ZERO;

    /**
     * 广告订单量
     */
    private Integer saleNum = 0;

    /**
     * 广告转化率 广告订单量/点击量 * 100%
     */
    private BigDecimal salesConversionRate = BigDecimal.ZERO;

    /**
     * 广告销售额
     */
    private BigDecimal totalSales = BigDecimal.ZERO;

    /**
     * 广告花费
     */
    private BigDecimal cost = BigDecimal.ZERO;

    /**
     * CPC 广告花费/广告点击量(非百分比数据)
     */
    private BigDecimal cpc = BigDecimal.ZERO;

    /**
     * CPA 广告花费/广告订单量
     */
    private BigDecimal cpa = BigDecimal.ZERO;

    /**
     * ACOS 广告花费/销售额*100%
     */
    private BigDecimal acos = BigDecimal.ZERO;

    /**
     * ROAS 销售额/广告费(非百分比数据)
     */
    private BigDecimal roas = BigDecimal.ZERO;
}
