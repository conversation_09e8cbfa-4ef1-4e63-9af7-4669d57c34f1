package com.meiyunji.sponsored.service.account.dao;

import com.meiyunji.sponsored.common.springjdbc.ISlaveBaseDao;
import com.meiyunji.sponsored.service.account.po.ScAndVcShopAuth;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.dto.ShopByPuidDto;

import java.util.List;

/**
 * ShopAuth
 *
 * <AUTHOR>
 */
public interface ISlaveScVcShopAuthDao extends ISlaveBaseDao<ScAndVcShopAuth> {


    ShopAuth getVcAndScByIdAndPuid(Object id, Integer puid);

    List<ShopAuth> listAllByIds(int puid, List<Integer> shopIds);

    List<String> getSellerIdByShopIds(List<Integer> shopIdList);

    List<ShopAuth> getAllValidAdShopByLimit(Integer puid, Integer shopId, int start, int limit);

    List<ShopAuth> getAllValidAdShopByLimitOrderByIdDesc(Integer puid, Integer shopId, int start, int limit);

    List<ShopByPuidDto> getAllValidAdShopByPuid(Integer puid);

    List<Integer> getAllValidAdShopPuidByLimit(int start, int limit);

    List<ShopAuth> getShopAuthByMarketplaceId(String marketplaceId);

    List<ShopAuth> getBySellerIdsAndMarketplaceIds(List<String> sellerIds, List<String> marketplaceIds);

    List<ShopAuth> getAdAuthShopByShopIdList(List<Integer> puids);

    ShopAuth getByShopAuth(String sellerId, String marketplaceId);

    ShopAuth getValidAdShopById(Integer puid, Integer shopId);

    List<ShopAuth> getScAndVcShopList(int puid, List<Integer> shopIds, String adStatus);

    List<ShopAuth> getScAndVcBySellerIdsAndMarketplaceIds(List<String> sellerIds, List<String> marketplaceIds);

    ShopAuth getScAndVcById(int id);

    List<ShopAuth> getScAndVcByIds(List<Integer> ids);

    List<Integer> getAllAdAuthShopPuid();
}