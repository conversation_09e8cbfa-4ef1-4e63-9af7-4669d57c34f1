package com.meiyunji.sponsored.service.doris.dao.impl;

import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.springjdbc.DorisBaseDaoImpl;
import com.meiyunji.sponsored.common.springjdbc.ObjectMapper;
import com.meiyunji.sponsored.common.util.SqlStringUtil;
import com.meiyunji.sponsored.common.util.StringUtil;
import com.meiyunji.sponsored.service.cpc.vo.RepeatTargetingProductVo;
import com.meiyunji.sponsored.service.cpc.vo.ReportVo;
import com.meiyunji.sponsored.service.doris.dao.IOdsProductDao;
import com.meiyunji.sponsored.service.doris.po.OdsProduct;
import com.meiyunji.sponsored.service.newDashboard.dto.CategoryNameDto;
import com.meiyunji.sponsored.service.post.request.GetSuggestAsinRequest;
import com.meiyunji.sponsored.service.post.response.AsinPageResponse;
import com.meiyunji.sponsored.service.post.vo.AsinProductVo;
import com.meiyunji.sponsored.service.util.LocalDateTimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 在线产品表-主项目分库(OdsProduct)数据库访问层
 *
 * <AUTHOR>
 * @since 2024-03-26 10:46:22
 */
@Repository
@Slf4j
public class OdsProductDaoImpl extends DorisBaseDaoImpl<OdsProduct> implements IOdsProductDao {


    @Override
    public List<String> listByParentAsin(Integer puid, Integer shopId, List<String> parentAsin) {
        List<Object> args = new ArrayList<>();
        StringBuilder select = new StringBuilder("select DISTINCT asin from ods_t_product where puid = ? and shop_id = ? ");
        args.add(puid);
        args.add(shopId);
        if (CollectionUtils.isNotEmpty(parentAsin)) {
            select.append(" and (");
            select.append(SqlStringUtil.dealInListNotAnd("asin", parentAsin, args));
            select.append(" or ");
            select.append(SqlStringUtil.dealInListNotAnd("parent_asin", parentAsin, args));
            select.append(")");
        }
        Object[] arg = args.toArray();
        return getJdbcTemplate().queryForList(select.toString(), String.class, arg);
    }

    @Override
    public List<String> listByParentAsin(Integer puid, String marketplaceId, List<Integer> shopIds, List<String> parentAsins) {
        List<Object> args = new ArrayList<>();
        StringBuilder sql = new StringBuilder("select DISTINCT asin from ods_t_product where puid=? and marketplace_id=? ");
        args.add(puid);
        args.add(marketplaceId);
        sql.append(SqlStringUtil.dealInList("shop_id", shopIds, args));
        if (CollectionUtils.isNotEmpty(parentAsins)) {
            sql.append(" and (");
            sql.append(" ( ").append(SqlStringUtil.dealInListNotAnd("asin", parentAsins, args)).append(" and (parent_asin is null or parent_asin = '')) ");
            sql.append(" or ");
            sql.append(" ( ").append(SqlStringUtil.dealInListNotAnd("parent_asin", parentAsins, args)).append(" ) ");
            sql.append(")");
        }
        Object[] arg = args.toArray();
        return getJdbcTemplate().queryForList(sql.toString(), String.class, arg);
    }

    @Override
    public List<String> listByParentAsin(Integer puid, List<String> marketplaceId, List<Integer> shopIds, List<String> parentAsins) {
        List<Object> args = new ArrayList<>();
        StringBuilder sql = new StringBuilder("select DISTINCT asin from ods_t_product where puid=? ");
        args.add(puid);
        sql.append(SqlStringUtil.dealInList("shop_id", shopIds, args));
        sql.append(SqlStringUtil.dealInList("marketplace_id", marketplaceId, args));
        if (CollectionUtils.isNotEmpty(parentAsins)) {
            sql.append(" and (");
            sql.append(" ( ").append(SqlStringUtil.dealInListNotAnd("asin", parentAsins, args)).append(" and (parent_asin is null or parent_asin = '')) ");
            sql.append(" or ");
            sql.append(" ( ").append(SqlStringUtil.dealInListNotAnd("parent_asin", parentAsins, args)).append(" ) ");
            sql.append(")");
        }
        Object[] arg = args.toArray();
        return getJdbcTemplate().queryForList(sql.toString(), String.class, arg);
    }

    @Override
    public List<String> listByParentAsin(Integer puid, List<Integer> shopIds, List<String> parentAsins) {
        List<Object> args = new ArrayList<>();
        StringBuilder sql = new StringBuilder("select DISTINCT asin from ods_t_product where puid=? ");
        args.add(puid);
        sql.append(SqlStringUtil.dealInList("shop_id", shopIds, args));
        sql.append(" and (");
        sql.append(" ( ").append(SqlStringUtil.dealInListNotAnd("asin", parentAsins, args)).append(" and (parent_asin is null or parent_asin = '')) ");
        sql.append(" or ");
        sql.append(" ( ").append(SqlStringUtil.dealInListNotAnd("parent_asin", parentAsins, args)).append(" ) ");
        sql.append(")");
        Object[] arg = args.toArray();
        return getJdbcTemplate().queryForList(sql.toString(), String.class, arg);
    }

    @Override
    public List<OdsProduct> listByParentAsins(Integer puid, String marketplaceId, List<Integer> shopIds, List<String> parentAsins) {
        if (CollectionUtils.isEmpty(parentAsins)) {
            return Lists.newArrayList();
        }
        List<Object> args = new ArrayList<>();
        StringBuilder sql = new StringBuilder("select shop_id, asin, sku, if(parent_asin is null or parent_asin='', asin, parent_asin) parent_asin")
                .append(" from ods_t_product where asin is not null and asin !='' and puid=? and marketplace_id=? ");
        args.add(puid);
        args.add(marketplaceId);
        sql.append(SqlStringUtil.dealInList("shop_id", shopIds, args));
        sql.append(SqlStringUtil.dealInList("if(parent_asin is null or parent_asin='', asin, parent_asin)", parentAsins, args));
        sql.append(" group by shop_id, asin, sku, if(parent_asin is null or parent_asin='', asin, parent_asin) ");
        return getJdbcTemplate().query(sql.toString(), new ObjectMapper<>(OdsProduct.class), args.toArray());
    }

    @Override
    public List<OdsProduct> listByTrueParentAsin(Integer puid, String marketplaceId, List<Integer> shopIds, List<String> parentAsins) {
        List<Object> args = new ArrayList<>();
        StringBuilder sql = new StringBuilder("select shop_id, asin, sku, parent_asin from ods_t_product where asin is not null and asin !=''")
                .append(" and parent_asin is not null and parent_asin!=''")
                .append(" and is_variation=2 and puid=? and marketplace_id=?");
        args.add(puid);
        args.add(marketplaceId);
        sql.append(SqlStringUtil.dealInList("shop_id", shopIds, args));
        sql.append(SqlStringUtil.dealInList("parent_asin", parentAsins, args));
        sql.append(" group by shop_id, asin, sku, parent_asin ");
        return getJdbcTemplate().query(sql.toString(), new ObjectMapper<>(OdsProduct.class), args.toArray());
    }

    @Override
    public List<OdsProduct> listParentAsin(Integer puid, String marketplaceId, List<Integer> shopIds,
                                           List<String> skus, List<String> asin, List<String> parentAsins) {
        if (CollectionUtils.isEmpty(skus) && CollectionUtils.isEmpty(asin) && CollectionUtils.isEmpty(parentAsins)) {
            return Collections.emptyList();
        }

        List<Object> args = new ArrayList<>();
        StringBuilder sql = new StringBuilder("select shop_id, asin, sku, if(parent_asin is null or parent_asin='',asin,parent_asin) parent_asin ")
                .append(" from ods_t_product where asin is not null and asin !=''")
                .append(" and puid=? and marketplace_id=?");
        args.add(puid);
        args.add(marketplaceId);
        sql.append(SqlStringUtil.dealInList("shop_id", shopIds, args));
        if (CollectionUtils.isNotEmpty(skus)) {
            sql.append(SqlStringUtil.dealInList("sku", skus, args));
        }
        if (CollectionUtils.isNotEmpty(asin)) {
            sql.append(SqlStringUtil.dealInList("asin", asin, args));
        }
        if (CollectionUtils.isNotEmpty(parentAsins)) {
            sql.append(SqlStringUtil.dealInList("if(parent_asin is null or parent_asin='', asin, parent_asin)", parentAsins, args));
        }
        sql.append(" group by shop_id, asin, sku, if(parent_asin is null or parent_asin='',asin,parent_asin) ");

        log.info("listParentAsin sql {}", SqlStringUtil.exactSql(sql.toString(), args));

        return getJdbcTemplate().query(sql.toString(), new ObjectMapper<>(OdsProduct.class), args.toArray());
    }

    @Override
    public List<String> listBySku(Integer puid, Integer shopId, List<String> sku) {
        List<Object> args = new ArrayList<>();
        StringBuilder select = new StringBuilder("select DISTINCT asin from ods_t_product where puid = ? and shop_id = ? ");
        args.add(puid);
        args.add(shopId);
        if (CollectionUtils.isNotEmpty(sku)) {
            select.append(SqlStringUtil.dealInList("sku", sku, args));
        }
        Object[] arg = args.toArray();
        return getJdbcTemplate().queryForList(select.toString(), String.class, arg);
    }

    @Override
    public List<String> listBySkuAllShop(Integer puid, List<Integer> shopId, List<String> marketplaceId, List<String> sku) {
        List<Object> args = new ArrayList<>();
        StringBuilder select = new StringBuilder("select DISTINCT asin from ods_t_product where puid = ? ");
        args.add(puid);
        select.append(SqlStringUtil.dealInList("shop_id", shopId, args));
        select.append(SqlStringUtil.dealInList("marketplace_id", marketplaceId, args));
        if (CollectionUtils.isNotEmpty(sku)) {
            select.append(SqlStringUtil.dealInList("sku", sku, args));
        }
        Object[] arg = args.toArray();
        return getJdbcTemplate().queryForList(select.toString(), String.class, arg);
    }

    @Override
    public List<OdsProduct> listBySkuList(Integer puid, String marketplaceId, List<Integer> shopIds, List<String> sku) {
        if (CollectionUtils.isEmpty(sku)) {
            return Collections.emptyList();
        }
        List<Object> args = new ArrayList<>();
        StringBuilder select = new StringBuilder("select shop_id, asin, sku from ods_t_product where asin is not null and asin !='' and puid=? and marketplace_id=? ");
        args.add(puid);
        args.add(marketplaceId);
        select.append(SqlStringUtil.dealInList("shop_id", shopIds, args));
        select.append(SqlStringUtil.dealInList("sku", sku, args));
        select.append(" group by shop_id, asin, sku ");
        Object[] arg = args.toArray();
        return getJdbcTemplate().query(select.toString(), new ObjectMapper<>(OdsProduct.class), arg);
    }

    @Override
    public List<String> listAsinBySku(Integer puid, List<Integer> shopIds, List<String> sku) {
        List<Object> args = new ArrayList<>();
        StringBuilder sql = new StringBuilder("select DISTINCT asin from ods_t_product where puid = ? ");
        args.add(puid);
        sql.append(SqlStringUtil.dealInList("shop_id", shopIds, args));
        sql.append(SqlStringUtil.dealInList("sku", sku, args));
        Object[] arg = args.toArray();
        return getJdbcTemplate().queryForList(sql.toString(), String.class, arg);
    }

    @Override
    public List<OdsProduct> listByAsin(Integer puid, Integer shopId, List<String> asin, String title) {
        List<Object> args = new ArrayList<>();
        StringBuilder select = new StringBuilder("SELECT id, marketplace_id, asin, sku, online_status, title, main_image from ods_t_product "
                + "where puid = ? and shop_id = ? and (dxm_publish_state is null || dxm_publish_state != 'delete') and is_variation in (0,2) ");
        args.add(puid);
        args.add(shopId);
        if (CollectionUtils.isNotEmpty(asin)) {
            select.append("and asin in ('").append(StringUtils.join(asin, "','")).append("') ");
        }
        if (StringUtils.isNotBlank(title)) {
            select.append(" and lower(title) like ? ");
            args.add("%" + title.trim().toLowerCase() + "%");
        }
        return getJdbcTemplate().query(select.toString(), new ObjectMapper<>(OdsProduct.class), args.toArray());

    }

    @Override
    public List<String> listByParentAsinMultiShop(Integer puid, List<Integer> shopIdList, List<String> parentAsin) {
        List<Object> args = new ArrayList<>();
        StringBuilder select = new StringBuilder("select DISTINCT asin from ods_t_product where puid = ? ");
        args.add(puid);
        if (CollectionUtils.isNotEmpty(shopIdList)) {
            select.append("and shop_id in ('").append(StringUtils.join(shopIdList, "','")).append("') ");
        }
        if (CollectionUtils.isNotEmpty(parentAsin)) {
            select.append(" and (");
            select.append(SqlStringUtil.dealInListNotAnd("asin", parentAsin, args));
            select.append(" or ");
            select.append(SqlStringUtil.dealInListNotAnd("parent_asin", parentAsin, args));
            select.append(")");
        }
        Object[] arg = args.toArray();
        return getJdbcTemplate().queryForList(select.toString(), String.class, arg);
    }

    @Override
    public List<String> listBySkuMultiShop(Integer puid, List<Integer> shopIdList, List<String> sku) {
        List<Object> args = new ArrayList<>();
        StringBuilder select = new StringBuilder("select DISTINCT asin from ods_t_product where puid = ? ");
        args.add(puid);
        if (CollectionUtils.isNotEmpty(shopIdList)) {
            select.append("and shop_id in ('").append(StringUtils.join(shopIdList, "','")).append("') ");
        }
        if (CollectionUtils.isNotEmpty(sku)) {
            select.append(SqlStringUtil.dealInList("sku", sku, args));
        }
        Object[] arg = args.toArray();
        return getJdbcTemplate().queryForList(select.toString(), String.class, arg);
    }

    @Override
    public List<RepeatTargetingProductVo> getSpProductByGroupId(Integer puid, List<Integer> shopIdList, List<String> adGroupId) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sql = new StringBuilder("select k.asin asin, k.sku sku, t.main_image mainImage, k.ad_group_id adGroupId from ods_t_amazon_ad_product k ");
        sql.append("left join ods_t_product t ");
        sql.append(" on k.asin = t.asin and k.puid = t.puid and k.shop_id = t.shop_id and k.sku = t.sku and k.marketplace_id = t.marketplace_id ");
        sql.append(" and t.puid = ? ");
        argsList.add(puid);
        if (CollectionUtils.isNotEmpty(shopIdList)) {
            sql.append(SqlStringUtil.dealInList("t.shop_id", shopIdList, argsList));
        }
        sql.append(" where k.puid = ? ");
        argsList.add(puid);
        if (CollectionUtils.isNotEmpty(shopIdList)) {
            sql.append(SqlStringUtil.dealInList("k.shop_id", shopIdList, argsList));
        }
        if (CollectionUtils.isNotEmpty(adGroupId)) {
            sql.append(SqlStringUtil.dealInList("k.ad_group_id", adGroupId, argsList));
        }
        return getJdbcTemplate().query(sql.toString(), new ObjectMapper<>(RepeatTargetingProductVo.class), argsList.toArray());
    }

    /**
     * 只返回图片url和查询条件字段
     *
     * @return
     */
    @Override
    public List<OdsProduct> getMainImgByAsinOrSku(int puid, List<Integer> shopIds, List<String> asin, List<String> skus) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sb = new StringBuilder();
        sb.append(" SELECT asin, sku, main_image ");
        sb.append(" from ").append(getJdbcHelper().getTable());
        sb.append(" where puid = ? ");
        argsList.add(puid);
        if (CollectionUtils.isNotEmpty(shopIds)) {
            sb.append("and shop_id in ('").append(StringUtils.join(shopIds, "','")).append("') ");
        }
        if (CollectionUtils.isNotEmpty(asin)) {
            sb.append("and asin in ('").append(StringUtils.join(asin, "','")).append("') ");
        }
        if (CollectionUtils.isNotEmpty(skus)) {
            sb.append("and sku in ('").append(StringUtils.join(skus, "','")).append("') ");
        }
        return getJdbcTemplate().query(sb.toString(), new ObjectMapper<>(OdsProduct.class), argsList.toArray());
    }

    @Override
    public List<OdsProduct> listByParentId(Integer puid, List<Integer> shopIds, List<Long> parentId) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sb = new StringBuilder();
        sb.append(" SELECT shop_id, asin,sku, parent_id, main_image");
        sb.append(" from ").append(getJdbcHelper().getTable());
        sb.append(" where puid = ? ");
        argsList.add(puid);
        if (CollectionUtils.isNotEmpty(shopIds)) {
            sb.append(SqlStringUtil.dealInList("shop_id", shopIds, argsList));
        }
        if (CollectionUtils.isNotEmpty(parentId)) {
            sb.append(SqlStringUtil.dealInList("parent_id", parentId, argsList));
        }
        return getJdbcTemplate().query(sb.toString(), getRowMapper(), argsList.toArray());
    }

    @Override
    public List<OdsProduct> listParentAsinById(Integer puid, List<Integer> shopIds, List<Long> parentId) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sb = new StringBuilder();
        sb.append(" SELECT asin,sku,id, shop_id, marketplace_id, main_image ");
        sb.append(" from ").append(getJdbcHelper().getTable());
        sb.append(" where puid = ? ");
        argsList.add(puid);
        //
        if (CollectionUtils.isNotEmpty(shopIds)) {
            sb.append(SqlStringUtil.dealInList("shop_id", shopIds, argsList));
        }
        if (CollectionUtils.isNotEmpty(parentId)) {
            if (parentId.size() < 10000) {
                sb.append(SqlStringUtil.dealInList("id", parentId, argsList));
            } else {
                sb.append(SqlStringUtil.dealBitMapDorisInList("id", parentId, argsList));
            }
        }
        return getJdbcTemplate().query(sb.toString(), getRowMapper(), argsList.toArray());
    }

    @Override
    public List<OdsProduct> listByAsinSku(Integer puid, String marketplaceId, List<Integer> shopIds, List<String> asinList, List<String> skuList) {
        if (CollectionUtils.isEmpty(shopIds) || CollectionUtils.isEmpty(asinList)) {
            return Collections.emptyList();
        }

        List<Object> args = new ArrayList<>();
        StringBuilder sql = new StringBuilder();
        sql.append(" SELECT id, shop_id, asin, sku, parent_id, parent_asin ");
        sql.append(" from ").append(getJdbcHelper().getTable());
        sql.append(" where puid=? and marketplace_id=? ");
        args.add(puid);
        args.add(marketplaceId);

        sql.append(SqlStringUtil.dealInList("shop_id", shopIds, args));
        if (asinList.size() > 1) {
            if (asinList.size() < 10000) {
                sql.append(SqlStringUtil.dealInList("asin", asinList, args));
            } else {
                List<List<String>> lists = Lists.partition(asinList, 8000);
                sql.append(" and (");
                int size = lists.size() - 1;
                for (int i = 0; i <= size; i++) {
                    sql.append(SqlStringUtil.dealInListNotAnd("asin", lists.get(i), args));
                    if (i < size) {
                        sql.append(" or ");
                    }
                }
                sql.append(" ) ");
            }
        } else {
            sql.append(" and asin=? ");
            args.add(asinList.get(0).trim());
        }
        if (CollectionUtils.isNotEmpty(skuList)) {
            if (skuList.size() > 1) {
                if (skuList.size() < 10000) {
                    sql.append(SqlStringUtil.dealInList("sku", skuList, args));
                } else {
                    List<List<String>> lists = Lists.partition(skuList, 8000);
                    sql.append(" and (");
                    int size = lists.size() - 1;
                    for (int i = 0; i <= size; i++) {
                        sql.append(SqlStringUtil.dealInListNotAnd("sku", lists.get(i), args));
                        if (i < size) {
                            sql.append(" or ");
                        }
                    }
                    sql.append(" ) ");
                }
            } else {
                sql.append(" and sku=? ");
                args.add(skuList.get(0).trim());
            }
        }
        return getJdbcTemplate().query(sql.toString(), getRowMapper(), args.toArray());
    }

    @Override
    public List<OdsProduct> listByAsins(Integer puid, String marketplaceId, List<Integer> shopIds, List<String> asinList) {
        if (CollectionUtils.isEmpty(shopIds) || CollectionUtils.isEmpty(asinList)) {
            return Collections.emptyList();
        }
        List<Object> args = new ArrayList<>();
        StringBuilder sql = new StringBuilder("SELECT shop_id, asin, sku, main_image, title, parent_asin ")
                .append(" from ods_t_product where puid=? and marketplace_id=? ");
        args.add(puid);
        args.add(marketplaceId);

        sql.append(SqlStringUtil.dealInList("shop_id", shopIds, args));
        sql.append(SqlStringUtil.dealInList("asin", asinList, args));
//        sql.append(" group by shop_id, asin, sku ");

        return getJdbcTemplate().query(sql.toString(), new ObjectMapper<>(OdsProduct.class), args.toArray());
    }

    @Override
    public List<OdsProduct> listProductByAsins(Integer puid, List<Integer> shopIds, List<String> asins) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sb = new StringBuilder();
        sb.append(" SELECT shop_id, asin, parent_asin, sku ");
        sb.append(" from ").append(getJdbcHelper().getTable());
        sb.append(" where puid = ? ");
        argsList.add(puid);
        if (CollectionUtils.isNotEmpty(shopIds)) {
            sb.append(SqlStringUtil.dealInList("shop_id", shopIds, argsList));
        }
        if (CollectionUtils.isNotEmpty(asins)) {
            sb.append(SqlStringUtil.dealInList("asin", asins, argsList));
        }
        return getJdbcTemplate().query(sb.toString(), getRowMapper(), argsList.toArray());
    }
    @Override
    public List<OdsProduct> listTitleAndImgByAsins(Integer puid, List<Integer> shopIds, List<String> asins) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sb = new StringBuilder();
        sb.append(" SELECT shop_id, asin, title, main_image ");
        sb.append(" from ").append(getJdbcHelper().getTable());
        sb.append(" where puid = ? ");
        argsList.add(puid);
        if (CollectionUtils.isNotEmpty(shopIds)) {
            sb.append(SqlStringUtil.dealInList("shop_id", shopIds, argsList));
        }
        if (CollectionUtils.isNotEmpty(asins)) {
            sb.append(SqlStringUtil.dealInList("asin", asins, argsList));
        }
        return getJdbcTemplate().query(sb.toString(), getRowMapper(), argsList.toArray());
    }

    @Override
    public List<OdsProduct> listProductBySkus(Integer puid, List<Integer> shopIds, List<String> skus) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sb = new StringBuilder();
        sb.append(" SELECT asin, parent_asin, sku, shop_id, main_image ");
        sb.append(" from ").append(getJdbcHelper().getTable());
        sb.append(" where puid = ? ");
        argsList.add(puid);
        if (CollectionUtils.isNotEmpty(shopIds)) {
            sb.append(SqlStringUtil.dealInList("shop_id", shopIds, argsList));
        }
        if (CollectionUtils.isNotEmpty(skus)) {
            sb.append(SqlStringUtil.dealInList("sku", skus, argsList));
        }
        return getJdbcTemplate().query(sb.toString(), getRowMapper(), argsList.toArray());
    }


    @Override
    public List<CategoryNameDto> getCategoryName(Integer puid, List<String> fullCid) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sb = new StringBuilder();
        sb.append(" SELECT name,full_cid ");
        sb.append(" from ").append("ods_t_category");
        sb.append(" where puid = ? ");
        argsList.add(puid);
        if (CollectionUtils.isNotEmpty(fullCid)) {
            sb.append(SqlStringUtil.dealInList("full_cid", fullCid, argsList));
        }

        return getJdbcTemplate().query(sb.toString(), new BeanPropertyRowMapper<>(CategoryNameDto.class), argsList.toArray());
    }


    @Override
    public List<CategoryNameDto> getCategoryByName(Integer puid, List<String> names) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sb = new StringBuilder();
        sb.append(" SELECT name,full_cid ");
        sb.append(" from ").append("ods_t_category");
        sb.append(" where puid = ? ");
        argsList.add(puid);
        if (CollectionUtils.isNotEmpty(names)) {
            sb.append(SqlStringUtil.dealInList("name", names, argsList));
        }

        return getJdbcTemplate().query(sb.toString(), new BeanPropertyRowMapper<>(CategoryNameDto.class), argsList.toArray());
    }


    @Override
    public Integer getProductCommodityRelaNowMonthCount(Integer puid) {
        LocalDate now = LocalDate.now();
        List<Object> argsList = new ArrayList<>();
        StringBuilder sb = new StringBuilder();
        sb.append(" SELECT  count(1) ");
        sb.append(" from ").append("ods_t_product_commodity_rela");
        sb.append(" where puid = ? ");
        sb.append(" and month = ? and  pt = ? ");
        argsList.add(puid);
        argsList.add(LocalDateTimeUtil.formatDate(now, "yyyyMM"));
        argsList.add(LocalDateTimeUtil.formatDate(now.withDayOfMonth(1), "yyyy-MM-dd"));
        sb.append(" limit 1 ");
        return getJdbcTemplate().queryForObject(sb.toString(), Integer.class, argsList.toArray());
    }

    @Override
    public List<String> listAsinBySearchParam(int puid, String marketplaceId, List<Integer> shopIdList,
                                              String searchType, String searchField, String searchValue, Boolean searchFlag) {
        List<String> valueList = StringUtil.splitStr(searchValue, StringUtil.SPECIAL_COMMA)
                .stream().map(s -> searchFlag ? s : SqlStringUtil.dealLikeSql(s))
                .distinct().collect(Collectors.toList());

        if (StringUtils.equalsIgnoreCase(searchType, "parentAsin")) {
            List<Object> parentArgs = new ArrayList<>();
            searchField = StringUtils.equals(searchField, "parent_asin") ? "asin" : "title";
            StringBuilder parentAsinSql = new StringBuilder("select distinct asin from ods_t_product ")
                    .append(" where is_variation=1 and puid=? and marketplace_id=? ");
            parentArgs.add(puid);
            parentArgs.add(marketplaceId);
            parentAsinSql.append(SqlStringUtil.dealInList("shop_id", shopIdList, parentArgs));
            if (valueList.size() == 1) {
                if (searchFlag) {
                    parentAsinSql.append(" and lower(").append(searchField).append(") = ? ");
                    parentArgs.add(valueList.get(0).trim().toLowerCase());
                } else {
                    parentAsinSql.append(" and ").append("lower(").append(searchField).append(")").append(" like ? ");
                    parentArgs.add("%" + valueList.get(0).trim().toLowerCase() + "%");
                }
            } else {
                parentAsinSql.append(SqlStringUtil.dealInList(searchField, valueList, parentArgs));
            }
            logger.info("listAsinBySearchParam parentAsinSql = {}", SqlStringUtil.exactSql(parentAsinSql.toString(), parentArgs));
            List<String> parentAsinList = getJdbcTemplate().queryForList(parentAsinSql.toString(), String.class, parentArgs.toArray());
            logger.info("listAsinBySearchParam parentAsinList = {}", parentAsinList);
            if (CollectionUtils.isEmpty(parentAsinList)) {
                return Collections.emptyList();
            }

            List<Object> asinArgs = new ArrayList<>();
            StringBuilder asinSql = new StringBuilder("select distinct asin from ods_t_product ")
                    .append(" where is_variation=2 and puid=? and marketplace_id=? ");
            asinArgs.add(puid);
            asinArgs.add(marketplaceId);
            asinSql.append(SqlStringUtil.dealInList("shop_id", shopIdList, asinArgs));
            asinSql.append(SqlStringUtil.dealInList("parent_asin", parentAsinList, asinArgs));
            logger.info("listAsinBySearchParam asinSql = {}", SqlStringUtil.exactSql(asinSql.toString(), asinArgs));
            return getJdbcTemplate().queryForList(asinSql.toString(), String.class, asinArgs.toArray());
        } else {
            List<Object> args = new ArrayList<>();
            StringBuilder sql = new StringBuilder("select distinct asin from ods_t_product ")
                    .append(" where is_variation!=1 and puid=? and marketplace_id=? ");
            args.add(puid);
            args.add(marketplaceId);
            sql.append(SqlStringUtil.dealInList("shop_id", shopIdList, args));
            if (valueList.size() == 1) {
                if (searchFlag) {
                    sql.append(" and lower(").append(searchField).append(") = ? ");
                    args.add(valueList.get(0).trim().toLowerCase());
                } else {
                    sql.append(" and ").append("lower(").append(searchField).append(")").append(" like ? ");
                    args.add("%" + valueList.get(0).trim().toLowerCase() + "%");
                }
            } else {
                sql.append(SqlStringUtil.dealInList(searchField, valueList, args));
            }
            logger.info("listAsinBySearchParam sql = {}", SqlStringUtil.exactSql(sql.toString(), args));
            return getJdbcTemplate().queryForList(sql.toString(), String.class, args.toArray());
        }
    }

    @Override
    public List<ReportVo> getRatingByAsinList(Integer puid, Integer shopId, String marketplaceId, HashSet<String> asins) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sb = new StringBuilder();
        sb.append(" SELECT  asin, title, rating, rating_count ratingCount, standard_price price ");
        sb.append(" from ( ");
        //子查询部分，使用窗口函数 ROW_NUMBER() 进行排序
        sb.append(" SELECT asin, title, rating, rating_count, standard_price, ROW_NUMBER() OVER (PARTITION BY asin ORDER BY update_time DESC) as rn ");
        sb.append(" from ").append(getJdbcHelper().getTable());
        sb.append(" where puid = ? and shop_id = ? and marketplace_id = ? ");
        argsList.add(puid);
        argsList.add(shopId);
        argsList.add(marketplaceId);
        sb.append(SqlStringUtil.dealInList("asin", Arrays.asList(asins.toArray()), argsList));
        sb.append(" ) sub where rn = 1");
        return getJdbcTemplate().query(sb.toString(), new BeanPropertyRowMapper<>(ReportVo.class), argsList.toArray());
    }

    @Override
    public List<ReportVo> getRatingByAsinList(Integer puid, Integer shopId, String marketplaceId, List<List<String>> batches) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sb = new StringBuilder();
        for (List<String> batch : batches) {
            sb.append(" SELECT  asin, title, rating, rating_count ratingCount, standard_price price ");
            sb.append(" from ( ");
            //子查询部分，使用窗口函数 ROW_NUMBER() 进行排序
            sb.append(" SELECT asin, title, rating, rating_count, standard_price, ROW_NUMBER() OVER (PARTITION BY asin ORDER BY update_time DESC) as rn ");
            sb.append(" from ").append(getJdbcHelper().getTable());
            sb.append(" where puid = ? and shop_id = ? and marketplace_id = ? ");
            argsList.add(puid);
            argsList.add(shopId);
            argsList.add(marketplaceId);
            sb.append(SqlStringUtil.dealInList("asin", batch, argsList));
            sb.append(" ) sub where rn = 1");
            sb.append(" UNION ALL ");
        }
        // 去除最后一个 UNION ALL
        if (sb.length() >= " UNION ALL ".length()) {
            sb.delete(sb.length() - " UNION ALL ".length(), sb.length());
        }
        return getJdbcTemplate().query(sb.toString(), new BeanPropertyRowMapper<>(ReportVo.class), argsList.toArray());
    }

    @Override
    public List<AsinProductVo> listProductInfoByAsins(Integer puid, List<Integer> shopIds, String marketplaceId, List<String> asins) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder select = new StringBuilder();
        select.append(" SELECT id, shop_id shopId, asin, parent_asin parentAsin, main_image mainImage, title, rating, rating_count ratingCount, sku, standard_price price, online_status onlineStatus, is_variation isVariation, quantity available ");
        select.append(" from ").append(getJdbcHelper().getTable());
        select.append(" where puid =? ");
        argsList.add(puid);
        select.append(SqlStringUtil.dealInList("shop_id", shopIds, argsList));
        select.append(" and marketplace_id =? ");
        argsList.add(marketplaceId);
        select.append(SqlStringUtil.dealInList("asin", Arrays.asList(asins.toArray()), argsList));
        select.append(" and ( dxm_publish_state <> 'delete' or dxm_publish_state is null) ");
        return getJdbcTemplate().query(select.toString(), new BeanPropertyRowMapper<>(AsinProductVo.class), argsList.toArray());
    }

    @Override
    public List<OdsProduct> getAsinParentId(Integer puid, GetSuggestAsinRequest req, List<String> asins) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder select = new StringBuilder();
        select.append(" SELECT * from ").append(getJdbcHelper().getTable());
        select.append(" where puid =? and shop_id = ? ");
        argsList.add(puid);
        argsList.add(req.getShopId());
        select.append(" and is_variation in (0, 2)");
        select.append(" and (dxm_publish_state is null || dxm_publish_state != 'delete') ");
        select.append(SqlStringUtil.dealInList("asin", Arrays.asList(asins.toArray()), argsList));
        if (StringUtils.isNotBlank(req.getSearchField()) && StringUtils.isNotBlank(req.getSearchValue())) {
            if ("title".equals(req.getSearchField())) {
                select.append(" and title like ? ");
                argsList.add("%" + SqlStringUtil.dealLikeSql(req.getSearchValue()) + "%");
            } else if ("asin".equals(req.getSearchField())) {
                select.append(SqlStringUtil.dealInList("asin", Arrays.asList(asins.toArray()), argsList));
            } else if ("msku".equals(req.getSearchField())) {
                select.append(" and sku like ? ");
                argsList.add("%" + SqlStringUtil.dealLikeSql(req.getSearchValue()) + "%");
            }
        }
        select.append(" order by puid,shop_id,is_variation,parent_id ");
        return getJdbcTemplate().query(select.toString(), new BeanPropertyRowMapper<>(OdsProduct.class), argsList.toArray());
    }

    @Override
    public List<OdsProduct> getListByParentId(Integer puid, Integer shopId, String marketplaceId, ArrayList<Long> idList) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sql = new StringBuilder("select id, puid, shop_id shopId, marketplace_id marketplaceId, parent_id parentId, dxm_state dxmState, dxm_publish_state dxmPublishState,")
                .append("asin, is_variation isVariation, sku, title, standard_price standardPrice, quantity, main_image mainImage,")
                .append("online_status onlineStatus, unsellable, rating, rating_count ratingCount ")
                .append(" from ods_t_product where puid = ? ");
        argsList.add(puid);
        sql.append(" and shop_id = ? ");
        argsList.add(shopId);
        if (StringUtils.isNotBlank(marketplaceId)) {
            sql.append(" and marketplace_id = ? ");
            argsList.add(marketplaceId);
        }
        sql.append(" and is_variation = 2 ");
        sql.append(SqlStringUtil.dealInList("parent_id", idList, argsList));
        return getJdbcTemplate().query(sql.toString(), new BeanPropertyRowMapper<>(OdsProduct.class), argsList.toArray());
    }

    @Override
    public Page<AsinPageResponse.AsinInfo> getPageList(Integer puid, GetSuggestAsinRequest req) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder select = new StringBuilder();
        StringBuilder countSql = new StringBuilder("select count(*) from (");
        select.append(" SELECT id, shop_id shopId, marketplace_id marketplaceId, asin, sku msku, parent_id parentId, online_status onlineStatus, title, main_image imgUrl, rating, rating_count ratingCount, standard_price price,quantity fbaAvailable,dxm_state, dxm_publish_state dxmPublishState, is_variation isVariation from ").append(getJdbcHelper().getTable());
        select.append(" where puid =? and shop_id = ? ");
        argsList.add(puid);
        argsList.add(req.getShopId());
        select.append(" and is_variation in (0, 2)");
        select.append(" and (dxm_publish_state is null || dxm_publish_state != 'delete') ");
        if (StringUtils.isNotBlank(req.getSearchField()) && StringUtils.isNotBlank(req.getSearchValue())) {
            if ("title".equals(req.getSearchField())) {
                select.append(" and title like ? ");
                argsList.add("%" + SqlStringUtil.dealLikeSql(req.getSearchValue()) + "%");
            } else if ("asin".equals(req.getSearchField())) {
                select.append(SqlStringUtil.dealInList("asin", Arrays.asList(Arrays.stream(req.getSearchValue().split(StringUtil.SPLIT_COMMA)).toArray()), argsList));
            } else if ("msku".equals(req.getSearchField())) {
                select.append(" and sku like ? ");
                argsList.add("%" + SqlStringUtil.dealLikeSql(req.getSearchValue()) + "%");
            }
        }
        // 在线状态筛选
        if(StringUtils.isNotBlank(req.getOnlineStatus())){
            if("Active".equals(req.getOnlineStatus())){
                select.append(" and online_status = 'Active' ");
            } else if ("InActive".equals(req.getOnlineStatus())) {
                select.append(" and online_status != 'Active' ");
            }
        }
        select.append(" order by puid,shop_id,is_variation,parent_id ");
        if ("asc".equalsIgnoreCase(req.getOrderType())) {
            select.append(" asc");
        } else {
            select.append(" desc ");
        }
        countSql.append(select + ") c");
        Object[] arg = argsList.toArray();
        String sql = SqlStringUtil.exactSql(select.toString(), argsList);
        return getPageResultByClass(req.getPageNo(), req.getPageSize(), countSql.toString(), arg, select.toString(), arg, AsinPageResponse.AsinInfo.class);
    }


    @Override
    public List<OdsProduct> listVcParentAsinById(Integer puid, List<Integer> shopIds, List<Long> parentId) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sb = new StringBuilder();
        sb.append(" SELECT asin,id, shop_id, marketplace_id, main_image ");
        sb.append(" from ").append(" ods_t_vc_product ");
        sb.append(" where puid = ? ");
        argsList.add(puid);
        //
        if (CollectionUtils.isNotEmpty(shopIds)) {
            sb.append(SqlStringUtil.dealInList("shop_id", shopIds, argsList));
        }
        if (CollectionUtils.isNotEmpty(parentId)) {
            if (parentId.size() < 10000) {
                sb.append(SqlStringUtil.dealInList("id", parentId, argsList));
            } else {
                sb.append(SqlStringUtil.dealBitMapDorisInList("id", parentId, argsList));
            }
        }
        return getJdbcTemplate().query(sb.toString(), getRowMapper(), argsList.toArray());
    }


    @Override
    public List<OdsProduct> listVcByParentAsin(Integer puid, List<Integer> shopIds, List<String> parentAsin) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sb = new StringBuilder();
        sb.append(" SELECT shop_id, asin, parent_asin, main_image");
        sb.append(" from ods_t_vc_product ");
        sb.append(" where puid = ? ");
        argsList.add(puid);
        if (CollectionUtils.isNotEmpty(shopIds)) {
            sb.append(SqlStringUtil.dealInList("shop_id", shopIds, argsList));
        }
        if (CollectionUtils.isNotEmpty(parentAsin)) {
            sb.append(SqlStringUtil.dealInList("parent_asin", parentAsin, argsList));
        }
        return getJdbcTemplate().query(sb.toString(), getRowMapper(), argsList.toArray());
    }

}

