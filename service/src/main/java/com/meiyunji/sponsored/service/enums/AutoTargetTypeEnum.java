package com.meiyunji.sponsored.service.enums;

public enum AutoTargetTypeEnum {
    /**
     * 广告自动投放设置
     * */

    queryHighRelMatches("queryHighRelMatches","紧密匹配"),
    queryBroadRelMatches("queryBroadRelMatches","宽泛匹配"),
    asinSubstituteRelated("asinSubstituteRelated","同类产品"),
    asinAccessoryRelated("asinAccessoryRelated","关联产品");



    private String AutoTargetType;

    private String autoTargetValue;

    public static String getAutoTargetValue(String autoTargetType){
        AutoTargetTypeEnum[] values = values();
        for (AutoTargetTypeEnum value : values) {
            if(value.getAutoTargetType().equals(autoTargetType)){
                return value.getAutoTargetValue();
            }
        }
        return "";
    }

    public static String getAutoTargetValueIgnoreCase(String autoTargetType){
        AutoTargetTypeEnum[] values = values();
        for (AutoTargetTypeEnum value : values) {
            if(value.getAutoTargetType().equalsIgnoreCase(autoTargetType)){
                return value.getAutoTargetValue();
            }
        }
        return "";
    }

    public static String getAutoTargetType(String autoTargetValue){
        AutoTargetTypeEnum[] values = values();
        for (AutoTargetTypeEnum value : values) {
            if(value.getAutoTargetValue().equals(autoTargetValue)){
                return value.getAutoTargetType();
            }
        }
        return "";
    }

    AutoTargetTypeEnum(String autoTargetType, String autoTargetValue) {
        AutoTargetType = autoTargetType;
        this.autoTargetValue = autoTargetValue;
    }

    public String getAutoTargetType() {
        return AutoTargetType;
    }

    public void setAutoTargetType(String autoTargetType) {
        AutoTargetType = autoTargetType;
    }

    public String getAutoTargetValue() {
        return autoTargetValue;
    }

    public void setAutoTargetValue(String autoTargetValue) {
        this.autoTargetValue = autoTargetValue;
    }

}

