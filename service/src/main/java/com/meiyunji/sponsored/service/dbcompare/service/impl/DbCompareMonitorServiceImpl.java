package com.meiyunji.sponsored.service.dbcompare.service.impl;


import com.meiyunji.sponsored.common.util.JSONUtil;
import com.meiyunji.sponsored.service.config.CosProperties;
import com.meiyunji.sponsored.service.dbcompare.dto.CompareResultDto;
import com.meiyunji.sponsored.service.dbcompare.dto.ScanUniqueIndexDto;
import com.meiyunji.sponsored.service.dbcompare.service.IDbCompareMonitorService;
import com.meiyunji.sponsored.service.dbcompare.service.IDbCompareService;
import com.meiyunji.sponsored.service.dbcompare.service.IDbScanUniqueIndexService;
import com.meiyunji.sponsored.service.dbcompare.service.IDbTableSizeMonitorService;
import com.meiyunji.sponsored.service.util.*;
import com.qcloud.cos.COSClient;
import com.qcloud.cos.http.HttpMethodName;
import com.qcloud.cos.model.CannedAccessControlList;
import com.qcloud.cos.model.ObjectMetadata;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import javax.sql.DataSource;
import javax.ws.rs.core.MediaType;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.*;

@Service
@Slf4j
@RefreshScope
public class DbCompareMonitorServiceImpl implements IDbCompareMonitorService {

    @Autowired
    private IDbCompareService dbCompareService;

    @Autowired
    private IDbScanUniqueIndexService dbScanUniqueIndexService;

    private static final String url = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=6b5158e7-0b3c-4491-b286-5d3824a7b7ad";

    private static final String COS_OBJECT_PREFIX = "dbcompare";
    private static final String COS_SCANINDEX_PREFIX = "scanindex";

    private static final String COS_TABLESIZEMONITOR_PREFIX = "tablesizemonitor";

    @Autowired
    private CosProperties cosProperties;

    @Autowired
    private COSClient nativeCosClient;

    //所有的共用数据库
    @Resource(name = "shardingJdbcMap")
    private Map<String, JdbcTemplate> jdbcTemplateMap;

    //所有的vip数据库
    @Resource(name = "vipJdbcTemplateMap")
    private Map<String, JdbcTemplate> vipJdbcTemplateMap;

    @Autowired
    private IDbTableSizeMonitorService dbTableSizeMonitorService;

    @Override
    public void doDbCompare(List<String> paramDsBeanList) {
        //保存结果和过程信息
        CompareResultDto compareResultDto = new CompareResultDto();
        //获取数据源beanId，不使用nacos配置了，从xxljob读取，若xxljob有则使用xxljob配置，否则直接从程序中读取避免后续增加数据源还需要配置xxljob或nacos
        List<String> targetList = new ArrayList<>();
        if (CollectionUtils.isEmpty(paramDsBeanList)) {
            targetList.addAll(jdbcTemplateMap.keySet());
            targetList.addAll(vipJdbcTemplateMap.keySet());
        } else {
            targetList = paramDsBeanList;
        }

        compareResultDto.getSystemInfoList().add(String.format("通过从%s中获取的数据源beanId执行数据库比对，beanId共计%s个", CollectionUtils.isEmpty(paramDsBeanList) ? "程序" : "xxljob配置", targetList.size()));
        List<DataSource> dsList = new ArrayList<>(targetList.size());
        //获取数据源bean
        targetList.forEach(x -> {
            try {
                dsList.add((DataSource) SpringContextUtil.getBean(x));
            } catch (Exception e) {
                String str = "beanId [" + x + "] 从spring容器中获取数据源失败，";
                compareResultDto.getSystemInfoList().add(str + e.getLocalizedMessage());
                log.error(str, e);
            }
        });

        compareResultDto.getSystemInfoList().add("获取数据源数量共计" + dsList.size() + "个");

        //比对
        dbCompareService.dbCompare(compareResultDto, dsList);

        //组装和发送预警
        buildNotice(compareResultDto);

    }

    @Override
    public void doScanUniqueIndex(List<ScanUniqueIndexDto> indexList) {
        //获取数据源bean
        List<DataSource> dsList = new ArrayList<>();
        List<String> dsBeanList = new ArrayList<>();
        dsBeanList.addAll(jdbcTemplateMap.keySet());
        dsBeanList.addAll(vipJdbcTemplateMap.keySet());

        dsBeanList.forEach(x -> {
            try {
                dsList.add((DataSource) SpringContextUtil.getBean(x));
            } catch (Exception e) {
                String str = "beanId [" + x + "] 从spring容器中获取数据源失败，";
                log.error(str, e);
            }
        });

        //比对
        List<String> list = dbScanUniqueIndexService.scanUniqueIndex(indexList, dsList);

        //结果通知
        String title = "扫描表索引结果";
        buildNoticeCommon(COS_SCANINDEX_PREFIX, title, list);
    }

    @Override
    public void dbTableSizeMonitor(int rows) {
        log.info("stat table size start");
        StopWatch sw = new StopWatch();
        sw.start();
        if (jdbcTemplateMap.isEmpty() && vipJdbcTemplateMap.isEmpty()) {
            log.info("stat table size, jdbctemplate map is empty");
            return;
        }

        //统计大小并获取统计结果
        List<String> list = dbTableSizeMonitorService.getStatTableSizeResult(rows);

        sw.stop();
        //结果通知
        String title = String.format("表大小监控扫描, 共%s个数据库, 行数超过%s的表\n数据统计共耗时%s秒", jdbcTemplateMap.size() + vipJdbcTemplateMap.size(), rows, sw.getTotalTimeSeconds());
        buildNoticeCommon(COS_TABLESIZEMONITOR_PREFIX, title, list);

        log.info("stat table size end");
    }

    /**
     * 组装和发送预警
     * @param r
     */
    private void buildNotice(CompareResultDto r) {
        StringBuilder builder = new StringBuilder();
        builder.append("数据库比对完成，比对结果：").append(r.isPass() ? "通过" : "不通过").append("\n").append("\n");

        r.getSystemInfoList().forEach(x -> builder.append(x).append("\n"));

        //组装不一致信息上传到cos
        if (!r.isPass()) {
            String s = null;
            try {
                s = buildTxtUploadCos(r);
            } catch (IOException e) {
                s = "比对结果上传到cos对象存储失败";
                log.error(s, e);
            }
            builder.append("\n").append("比对结果下载地址：").append("\n").append(s);
        }

        //消息切割，2000字节分割
        List<String> list = StringSplitUtil.splitByBytes(builder.toString(), 2000);

        //循环发送
        Optional.ofNullable(list).ifPresent(l -> l.forEach(this::sendDbCompareResultInfo));
    }

    /**
     * 组装比对结果并上传到对象存储
     * @param r
     * @return
     * @throws IOException
     */
    private String buildTxtUploadCos(CompareResultDto r) throws IOException {
        //拼接objectKey
        StringBuilder fileId = new StringBuilder();
        LocalDateTime now = LocalDateTime.now();
        fileId.append(COS_OBJECT_PREFIX).append("/").append(LocalDateTimeUtil.formatTime(now, LocalDateTimeUtil.YMD_DATE_FORMAT));
        fileId.append("/").append(COS_OBJECT_PREFIX).append(LocalDateTimeUtil.formatTime(LocalDateTime.now(), LocalDateTimeUtil.YYYYMMDDHHMMSS_DATE_FORMATE));
        fileId.append(".txt");
        String key = fileId.toString();
        //文件内容
        String content = getResultContent(r);
        //上传
        return uploadCos(key, content);
    }

    /**
     * 将比对结果上传到对象存储
     * @param key
     * @param content
     * @return
     */
    private String uploadCos(String key, String content) {
        //获取bucketName
        String bucketName = cosProperties.getBuckets().get("temp").getBucket() + "-" + cosProperties.getAppId();
        //上传对象
        ByteArrayInputStream inputStream = new ByteArrayInputStream(content.getBytes(StandardCharsets.UTF_8));
        ObjectMetadata objectMetadata = new ObjectMetadata();
        objectMetadata.setContentLength(inputStream.available());
        objectMetadata.setContentType("text/plain;charset=utf-8");
        objectMetadata.setContentEncoding("UTF-8");
        nativeCosClient.putObject(bucketName, key, inputStream, objectMetadata);
        //设置私有读写
        nativeCosClient.setObjectAcl(bucketName, key, CannedAccessControlList.Private);
        //生成下载链接，半个小时后过期
        java.util.Date expirationDate = new Date(System.currentTimeMillis() + 30 * 60 * 1000);
        URL downloadUrl = nativeCosClient.generatePresignedUrl(bucketName, key, expirationDate, HttpMethodName.GET);
        try {
            inputStream.close();
        } catch (IOException e) {
            log.error("关闭流异常", e);
        }
        return downloadUrl.toString();
    }

    /**
     * 从CompareResultDto拼接预警结果字符串
     * @param r
     * @return
     */
    private String getResultContent(CompareResultDto r) {
        StringBuilder content = new StringBuilder();
        Optional.ofNullable(r.getSystemInfoList()).ifPresent(list -> list.forEach(x -> content.append(x).append("\n")));
        content.append("\n");
        Optional.ofNullable(r.getTableCompareResult()).ifPresent(list -> list.forEach(x -> content.append(x).append("\n")));
        content.append("\n");
        Optional.ofNullable(r.getColumnCompareResult()).ifPresent(list -> list.forEach(x -> content.append(x).append("\n")));
        content.append("\n");
        Optional.ofNullable(r.getColumnTypeCompareResult()).ifPresent(list -> list.forEach(x -> content.append(x).append("\n")));
        content.append("\n");
        Optional.ofNullable(r.getIndexCompareResult()).ifPresent(list -> list.forEach(x -> content.append(x).append("\n")));
        return content.toString();
    }

    /**
     * 发送预警消息
     * @param s
     */
    private void sendDbCompareResultInfo(String s) {
        OkHttpClient client = OkHttpClientUtil.getClient();
        Map<String, Object> param = new HashMap<>(2);
        param.put("msgtype", "text");
        Map<String, Object> textMap = new HashMap<>(2);
        textMap.put("content", s);
        param.put("text", textMap);
        RequestBody body = RequestBody.create(okhttp3.MediaType.parse(MediaType.APPLICATION_JSON), JSONUtil.objectToJson(param));
        Call call = client.newCall(new Request.Builder().url(url).post(body).build());
        try {
            call.execute();
        } catch (IOException e) {
            log.error("发送数据库比对预警通知结果异常", e);
        }

    }


    /**
     * 组装和发送预警通用
     */
    private void buildNoticeCommon(String keyPrefix, String wxContent, List<String> cosContentList) {
        StringBuilder builder = new StringBuilder();
        builder.append(wxContent);

        //文本信息上传cos
        StringBuilder fileId = new StringBuilder();
        LocalDateTime now = LocalDateTime.now();
        fileId.append(keyPrefix).append("/").append(LocalDateTimeUtil.formatTime(now, LocalDateTimeUtil.YMD_DATE_FORMAT));
        fileId.append("/").append(keyPrefix).append(LocalDateTimeUtil.formatTime(LocalDateTime.now(), LocalDateTimeUtil.YYYYMMDDHHMMSS_DATE_FORMATE));
        fileId.append(".txt");
        String key = fileId.toString();
        //文件内容
        StringBuilder content = new StringBuilder();
        cosContentList.forEach(x -> content.append(x).append("\n"));

        //上传
        String s = uploadCos(key, content.toString());

        builder.append("\n").append("结果下载地址：").append("\n").append(s);

        sendDbCompareResultInfo(builder.toString());
    }
}
