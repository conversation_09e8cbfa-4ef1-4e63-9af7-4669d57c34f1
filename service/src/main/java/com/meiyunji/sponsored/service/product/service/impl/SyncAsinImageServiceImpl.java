package com.meiyunji.sponsored.service.product.service.impl;

import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.support.RedisConstant;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.common.util.ThreadPoolUtil;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.product.dao.IAsinImageDao;
import com.meiyunji.sponsored.service.product.po.AsinImage;
import com.meiyunji.sponsored.service.product.service.IAsinImageService;
import com.meiyunji.sponsored.service.product.service.ISyncAsinImageService;
import com.meiyunji.sponsored.service.sellfoxApi.IAmazonAsinIamgeApi;
import com.meiyunji.sponsored.service.sellfoxApi.IProductApi;
import com.meiyunji.sponsored.service.system.service.IAmazonRequestLimitService;
import com.meiyunji.sponsored.service.vo.AmazonAsinImageResult;
import com.meiyunji.sponsored.service.vo.ProductAdReportVo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.meiyunji.sponsored.service.cpc.util.Constants.ASIN_REGEX;

@Service
public class SyncAsinImageServiceImpl implements ISyncAsinImageService {
    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private IAsinImageDao asinImageDao;
    @Autowired
    private IScVcShopAuthDao shopAuthDao;
    @Autowired
    private IAmazonRequestLimitService requestLimitService;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private IAsinImageService asinImageService;
    @Autowired
    private IAmazonAsinIamgeApi amazonIamgeApi;
    @Autowired
    private IProductApi productApi;

    @Override
    public String getMainImage(String asin, String marketplaceId) {
        //先从缓存中取
        if (StringUtils.isBlank(asin)) {
            return null;
        }
        asin = asin.toUpperCase();
        String key = String.format(RedisConstant.ASIN_IMAGE,asin);
        String mainImage = null;
        try {
            mainImage = stringRedisTemplate.opsForValue().get(key);
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (StringUtils.isNotBlank(mainImage)) {
            return mainImage;
        }
        //从数据库取
        AsinImage image = asinImageDao.getImage(asin);
        if (image!=null) {
            mainImage = image.getImage();
        }
        if (StringUtils.isNotBlank(mainImage)) {
            stringRedisTemplate.opsForValue().set(key,mainImage,30,TimeUnit.DAYS);
            return mainImage;
        }
        if (image==null) {
            asinImageService.asyncSaveAsinImage(asin,marketplaceId);
        }

        return mainImage;
    }

    @Override
    public void syncAsinImage(List<AsinImage> asins, int index, int total,int limit) {
        long startTime = System.currentTimeMillis();
        int start = 0;
        limit = limit < 1 ? 1000 : limit ;
        logger.info("syncAsinImage start :{} ", start);
        if(CollectionUtils.isNotEmpty(asins)){
            asins = asins.stream().filter(x -> Math.abs(x.hashCode() % total) == index && StringUtils.isNotBlank(x.getMarketplaceId())).collect(Collectors.toList());
            if(asins != null && asins.size()>0){
                CountDownLatch latch = new CountDownLatch(asins.size());
                ThreadPoolExecutor pool = ThreadPoolUtil.getSyncAsinImage();
                for (AsinImage asin : asins) {
                    pool.execute(() -> {
                        try {
                            syncAsinImage(Collections.singletonList(asin));
                        } catch (Exception e) {
                            logger.info(" syncAsinImage error:{}", e);
                        } finally {
                            latch.countDown();
                        }
                    });
                }
                while (latch.getCount() != 0) {
                    try {
                        latch.await();
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                }
            }
        } else {
            List<AsinImage> asinsDao = asinImageDao.listImageNull(start, limit, DateUtil.addDay(new Date(),-3));
            asins = asinsDao.stream().filter(x -> Math.abs(x.hashCode() % total) == index && StringUtils.isNotBlank(x.getMarketplaceId())).collect(Collectors.toList());
            if(asins != null && asins.size()>0){
                CountDownLatch latch = new CountDownLatch(asins.size());
                ThreadPoolExecutor pool = ThreadPoolUtil.getSyncAsinImage();
                for (AsinImage asin : asins) {
                    pool.execute(() -> {
                        try {
                            syncAsinImage(Collections.singletonList(asin));
                        } catch (Exception e) {
                            logger.info(" syncAsinImage error:{}", e);
                        } finally {
                            latch.countDown();
                        }
                    });
                }
                while (latch.getCount() != 0) {
                    try {
                        latch.await();
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                }
            }
        }

        logger.info("syncAsinImage end 耗时:{} ", System.currentTimeMillis() - startTime);
    }

    private void syncAsinImage(List<AsinImage> asinImages) {

        for (AsinImage asin : asinImages) {
            AmazonAsinImageResult asinIamge = amazonIamgeApi.getAsinIamge(asin.getMarketplaceId(), asin.getAsin().toUpperCase());
            if(asinIamge != null ){
                if (asinIamge.getStatus() == 200 && StringUtils.isNotBlank(asinIamge.getSrc())) {
                    logger.info("asin:{},image :{}", asin, asinIamge.getSrc());
                    long t1 = System.currentTimeMillis();
                    if(asin.getId() != null && asin.getId() > 0){
                        asin.setImage(asinIamge.getSrc());
                        asin.setTitle(asinIamge.getTitle() != null && asinIamge.getTitle().length() > 500 ?  asinIamge.getTitle().substring(500):asinIamge.getTitle());
                        asin.setUpdateTime(new Date());
                        asinImageDao.updateById(asin);
                    } else {
                        asinImageDao.insertOrUpdate(asin.getAsin(), asinIamge.getSrc(),asinIamge.getTitle());
                    }
                    logger.info("保存图片耗时：{}",System.currentTimeMillis()-t1);
                } else if( "invalid asin".equalsIgnoreCase(asinIamge.getMsg()) ) {
                    asinImageDao.updateIsInvalid(asin.getAsin(),0);
                }
            }

        }
    }

    /**
     * 获取asin图片，如果不存在则添加到数据库中，另一个定时任务来同步图片
     *
     *
     *
     */
    @Override
    public List<AsinImage> getListByAsins(Integer puid, String marketplaceId, List<String> asins) {
        List<AsinImage> asinImages = Lists.newArrayList();
        try {

            if (CollectionUtils.isEmpty(asins)) {
                return asinImages;
            }
            List<String> collect = asins.stream().filter(e -> StringUtils.isNotBlank(e) && e.matches(ASIN_REGEX)).map(String::toUpperCase).distinct().collect(Collectors.toList());

            if (CollectionUtils.isEmpty(collect)) {
                return asinImages;
            }

            List<ProductAdReportVo> asinMainImageAndTitle = productApi.getAsinMainImageAndTitle(puid, marketplaceId, collect);
            if(CollectionUtils.isNotEmpty(asinMainImageAndTitle)){
                List<AsinImage> collect1 = asinMainImageAndTitle.stream().map(e -> {
                    AsinImage asinImage = new AsinImage();
                    asinImage.setAsin(e.getAsin().toUpperCase());
                    asinImage.setImage(e.getMainImage());
                    asinImage.setMarketplaceId(marketplaceId);
                    asinImage.setTitle(e.getTitle());
                    return asinImage;
                }).collect(Collectors.toList());
                asinImages.addAll(collect1);
            }
            if(collect.size() == asinImages.size()){
                return asinImages;
            }

            List<String> collect1 = asinImages.stream().map(AsinImage::getAsin).collect(Collectors.toList());
            collect.removeAll(collect1);
            List<AsinImage> asinImagesDao = asinImageDao.getListByAsins(collect);
            if (CollectionUtils.isEmpty(asinImagesDao)) {
                List<AsinImage> asinimages1 = new ArrayList<>();
                collect.forEach(e -> {
                    try {

                        if (StringUtils.isNotBlank(e)) {

                            AsinImage asinImage = new AsinImage();
                            asinImage.setAsin(e);
                            asinImage.setMarketplaceId(marketplaceId);
                            asinimages1.add(asinImage);

                        }

                    } catch (Exception e1) {
                        logger.error("保存数据错误", e1);
                    }
                });
                if (CollectionUtils.isNotEmpty(asinimages1)) {
                    asinImageDao.insertOrUpdateAsin(asinimages1);
                }
                return asinImages;
            }
            asinImages.addAll(asinImagesDao);
            if (asinImagesDao.size() < collect.size()) {
                Set<String> stringSet = asinImagesDao.stream().map(e->e.getAsin().toUpperCase()).collect(Collectors.toSet());
                List<AsinImage> asinimages1 = new ArrayList<>();
                collect.forEach(e -> {
                    try {
                        if (!stringSet.contains(e)) {
                            if (StringUtils.isNotBlank(e)) {
                                AsinImage asinImage = new AsinImage();
                                asinImage.setAsin(e);
                                asinImage.setMarketplaceId(marketplaceId);
                                asinImage.setIsInvalid(1);
                                asinimages1.add(asinImage);
                            }
                        }
                    } catch (Exception e1){
                        logger.error("保存数据错误",e1);
                    }
                });
                if(CollectionUtils.isNotEmpty(asinimages1)){
                    asinImageDao.insertOrUpdateAsin(asinimages1);
                }

            }
            return asinImages;
        } catch (Exception e){
            logger.error("获取图片异常：",e);
        }
        return asinImages;

    }

    @Override
    public void syncAsinTitle(String date, int index, int total) {


        long startTime = System.currentTimeMillis();
        int start = 0;
        int limit = 1000;
        logger.info("syncAsinImage start :{} ", start);

        List<AsinImage> asinsDao = new ArrayList<>();
        List<AsinImage> asins = new ArrayList<>();
        while (true) {
            asinsDao = asinImageDao.listTitleNull(date,start,limit);
            asins = asinsDao.stream().filter(x -> Math.abs(x.hashCode() % total) == index && StringUtils.isNotBlank(x.getMarketplaceId())).collect(Collectors.toList());
            if(asins != null && asins.size()>0){
                CountDownLatch latch = new CountDownLatch(asins.size());
                ThreadPoolExecutor pool = ThreadPoolUtil.getSyncAsinImageTitle();
                for (AsinImage asin : asins) {
                    pool.execute(() -> {
                        try {
                            syncAsinImage(Collections.singletonList(asin));
                        } catch (Exception e) {
                            logger.info(" syncAsinImage error:{}", e);
                        } finally {
                            latch.countDown();
                        }
                    });
                }
                while (latch.getCount() != 0) {
                    try {
                        latch.await();
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                }
            }
            if (asinsDao.size() < limit) {
                break;
            }
            start += asinsDao.size();

        }

        logger.info("syncAsinImage end 耗时:{} ", System.currentTimeMillis() - startTime);
    }

    public List<AsinImage> getListByAsin(Integer puid, String marketplaceId,List<String> asins){
        List<AsinImage> asinImages = Lists.newArrayList();
        if (CollectionUtils.isEmpty(asins)) {
            return asinImages;
        }
        List<String> collect = asins.stream().filter(e -> StringUtils.isNotBlank(e) && e.matches(ASIN_REGEX)).map(String::toUpperCase).distinct().collect(Collectors.toList());

        if (CollectionUtils.isEmpty(collect)) {
            return asinImages;
        }

        List<ProductAdReportVo> asinMainImageAndTitle = productApi.getAsinMainImageAndTitle(puid, marketplaceId, collect);
        if(CollectionUtils.isNotEmpty(asinMainImageAndTitle)){
            List<AsinImage> collect1 = asinMainImageAndTitle.stream().map(e -> {
                AsinImage asinImage = new AsinImage();
                asinImage.setAsin(e.getAsin().toUpperCase());
                asinImage.setImage(e.getMainImage());
                asinImage.setMarketplaceId(marketplaceId);
                asinImage.setTitle(e.getTitle());
                return asinImage;
            }).collect(Collectors.toList());
            asinImages.addAll(collect1);
        }
        if(collect.size() == asinImages.size()){
            return asinImages;
        }

        List<String> collect1 = asinImages.stream().map(AsinImage::getAsin).collect(Collectors.toList());
        collect.removeAll(collect1);
        List<AsinImage> asinImagesDao = asinImageDao.getListByAsins(collect);
        if (CollectionUtils.isEmpty(asinImagesDao)) {
            List<AsinImage> images = collect.stream().filter(StringUtils::isNotBlank).map(e -> {
                AsinImage asinImage = new AsinImage();
                asinImage.setAsin(e);
                asinImage.setMarketplaceId(marketplaceId);
                return asinImage;
            }).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(images)){
                asinImageDao.insertOrUpdateAsin(images);
            }

            return asinImages;
        }
        asinImages.addAll(asinImagesDao);

        return asinImages;
    }


    @Override
    public List<AsinImage> getListByAsinsNoSave(Integer puid, String marketplaceId, List<String> asins) {
        List<AsinImage> asinImages = Lists.newArrayList();
        try {

            if (CollectionUtils.isEmpty(asins)) {
                return asinImages;
            }
            List<String> collect = asins.stream().filter(e -> StringUtils.isNotBlank(e) && e.matches(ASIN_REGEX)).map(String::toUpperCase).distinct().collect(Collectors.toList());

            if (CollectionUtils.isEmpty(collect)) {
                return asinImages;
            }

            List<ProductAdReportVo> asinMainImageAndTitle = productApi.getAsinMainImageAndTitle(puid, marketplaceId, collect);
            if(CollectionUtils.isNotEmpty(asinMainImageAndTitle)){
                List<AsinImage> collect1 = asinMainImageAndTitle.stream().map(e -> {
                    AsinImage asinImage = new AsinImage();
                    asinImage.setAsin(e.getAsin().toUpperCase());
                    asinImage.setImage(e.getMainImage());
                    asinImage.setMarketplaceId(marketplaceId);
                    asinImage.setTitle(e.getTitle());
                    return asinImage;
                }).collect(Collectors.toList());
                asinImages.addAll(collect1);
            }
            if(collect.size() == asinImages.size()){
                return asinImages;
            }

            List<String> collect1 = asinImages.stream().map(AsinImage::getAsin).collect(Collectors.toList());
            collect.removeAll(collect1);
            List<AsinImage> asinImagesDao = asinImageDao.getListByAsins(collect);
            if (CollectionUtils.isEmpty(asinImagesDao)) {
                return asinImages;
            }
            asinImages.addAll(asinImagesDao);
            return asinImages;
        } catch (Exception e){
            logger.error("获取图片异常：",e);
        }
        return asinImages;

    }

    @Override
    public void syncAsinInvalid(int index, int total) {


        long startTime = System.currentTimeMillis();
        int start = 0;
        int limit = 1000;
        logger.info("syncAsinImage start :{} ", start);

        List<AsinImage> asinsDao = new ArrayList<>();
        List<AsinImage> asins = new ArrayList<>();
        while (true) {
            asinsDao = asinImageDao.listIsInvalid(start,limit);
            asins = asinsDao.stream().filter(x -> Math.abs(x.hashCode() % total) == index && StringUtils.isNotBlank(x.getMarketplaceId())).collect(Collectors.toList());
            if(asins != null && asins.size()>0){
                CountDownLatch latch = new CountDownLatch(asins.size());
                ThreadPoolExecutor pool = ThreadPoolUtil.getSyncAsinImageTitle();
                for (AsinImage asin : asins) {
                    pool.execute(() -> {
                        try {
                            syncAsinImage(Collections.singletonList(asin));
                        } catch (Exception e) {
                            logger.info(" syncAsinImage error:{}", e);
                        } finally {
                            latch.countDown();
                        }
                    });
                }
                while (latch.getCount() != 0) {
                    try {
                        latch.await();
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                }
            }
            if (asinsDao.size() < limit) {
                break;
            }
            start += asinsDao.size();

        }

        logger.info("syncAsinImage end 耗时:{} ", System.currentTimeMillis() - startTime);
    }


}
