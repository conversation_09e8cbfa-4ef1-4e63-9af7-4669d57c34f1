package com.meiyunji.sponsored.service.multiPlatform.walmart.po;



import com.meiyunji.sponsored.common.springjdbc.DbColumn;
import com.meiyunji.sponsored.common.util.DateUtil;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @author: pxq
 * @date: 2025/02/24
 * @Description: walmart广告组报告
 */
@Data
public class WalmartAdvertisingGroupReportPage extends WalmartAdReportBase implements Serializable, IBaseWalmartAdReportGrowthRate {

    private static final long serialVersionUID = 1L;

    /**
     * 商户uid
     */
    private Integer puid;

    /**
     * 店铺ID
     */
    private Long shopId;

    /**
     * 报告日期
     */
    private Date reportDate;

    /**
     * 活动id
     */
    private Long campaignId;

    /**
     * 广告组id
     */
    private Long adGroupId;


    /**
     * 广告花费
     */
    @DbColumn(value = "adCost")
    private Double adCost;
    /**
     * 点击量
     */
    @DbColumn(value = "clicks")
    private Integer clicks;

    /**
     * 展示量
     */
    @DbColumn(value = "impressions")
    private Integer impressions;

    /**
     * 销售额
     */
    @DbColumn(value = "adSale")
    private Double adSale;

    /**
     * 订单量
     */
    @DbColumn(value = "adOrderNum")
    private Integer adOrderNum;

    /**
     * 销量
     */
    @DbColumn(value = "adSaleNum")
    private Integer adSaleNum;
    /**
     * 平均点击费用（adSpend/num_ads_clicks）
     */
    private Double adCostPerClick;

    /**
     * 花费销售比（adSpend/sales）
     */
    private Double acos;

    /**
     * 点击率（num_ads_clicks/num_ads_shown）
     */
    @DbColumn(value = "click_rate")
    private Double ctr;

    /**
     * 下单转化率（order_quantity/num_ads_clicks）
     */
    @DbColumn(value = "sales_conversion_rate")
    private Double cvr;

    /**
     * 销售额占比
     */
    private Double salesRatio;

    /**
     * 广告花费占比
     */
    private Double adSpendRatio;

    /**
     * cpc占比
     */
    private Double cpcRatio;

    /**
     * 展示量占比
     */
    private Double numAdsShownRatio;

    /**
     * 点击量占比
     */
    private Double numAdsClicksRatio;

    /**
     * 订单数占比
     */
    private Double orderQuantityRatio;

    /**
     * 销量占比
     */
    private Double saleQuantityRatio;

    /**
     * 销售额环比增长
     */
    private Double salesChainGrowth;

    /**
     * 广告花费环比增长
     */
    private Double adSpendChainGrowth;

    /**
     * cpc环比增长
     */
    private Double cpcChainGrowth;

    /**
     * acos环比增长
     */
    private Double acosChainGrowth;

    /**
     * 展示量环比增长
     */
    private Double numAdsShownChainGrowth;

    /**
     * 点击量环比增长
     */
    private Double numAdsClicksChainGrowth;

    /**
     * 订单数环比增长
     */
    private Double orderQuantityChainGrowth;

    /**
     * 销量环比增长
     */
    private Double saleQuantityChainGrowth;

    /**
     * 点击率环比增长
     */
    private Double clickRateChainGrowth;

    /**
     * 转化率环比增长
     */
    private Double salesConversionRateChainGrowth;

    private String campaignName;
    private String adGroupName;

    public String getAdCostStr() {
        return getAdCost() == null ? "0" : String.valueOf(getAdCost());
    }

    public String getClicksStr() {
        return getClicks() == null ? "0" : String.valueOf(getClicks());
    }

    public String getImpressionsStr() {
        return getImpressions() == null ? "0" : String.valueOf(getImpressions());
    }

    public String getAdSaleStr() {
        return getAdSale() == null ? "0" : String.valueOf(getAdSale());
    }

    public String getAdOrderNumStr() {
        return getAdOrderNum() == null ? "0" : String.valueOf(getAdOrderNum());
    }

    public String getAdSaleNumStr() {
        return getAdSaleNum() == null ? "0" : String.valueOf(getAdSaleNum());
    }

    public String getCpcStr() {
        return getAdCostPerClick() == null ? "0" : String.valueOf(getAdCostPerClick());
    }

    public String getAcosStr() {
        return getAcos() == null ? "0%" : String.valueOf(getAcos())+ "%";
    }

    public String getClickRateStr() {
        return getCtr() == null ? "0%" : String.valueOf(getCtr())+ "%";
    }

    public String getSalesConversionRateStr() {
        return getCvr() == null ? "0%" : String.valueOf(getCvr())+ "%";
    }

    public String getSalesRatioStr() {
        return getSalesRatio() == null ? "0%" : String.valueOf(getSalesRatio()) + "%";
    }

    public String getAdSpendRatioStr() {
        return getAdSpendRatio() == null ? "0%" : String.valueOf(getAdSpendRatio()) + "%";
    }

    public String getCpcRatioStr() {
        return getCpcRatio() == null ? "0%" : String.valueOf(getCpcRatio()) + "%";
    }

    public String getNumAdsShownRatioStr() {
        return getNumAdsShownRatio() == null ? "0%" : String.valueOf(getNumAdsShownRatio()) + "%";
    }

    public String getNumAdsClicksRatioStr() {
        return getNumAdsClicksRatio() == null ? "0%" : String.valueOf(getNumAdsClicksRatio()) + "%";
    }

    public String getOrderQuantityRatioStr() {
        return getOrderQuantityRatio() == null ? "0%" : String.valueOf(getOrderQuantityRatio()) + "%";
    }

    public String getSaleQuantityRatioStr() {
        return getSaleQuantityRatio() == null ? "0%" : String.valueOf(getSaleQuantityRatio()) + "%";
    }

    public String getSalesChainGrowthStr() {
        return getSalesChainGrowth() == null ? "0%" : String.valueOf(getSalesChainGrowth()) + "%";
    }

    public String getAdSpendChainGrowthStr() {
        return getAdSpendChainGrowth() == null ? "0%" : String.valueOf(getAdSpendChainGrowth()) + "%";
    }

    public String getCpcChainGrowthStr() {
        return getCpcChainGrowth() == null ? "0%" : String.valueOf(getCpcChainGrowth()) + "%";
    }

    public String getAcosChainGrowthStr() {
        return getAcosChainGrowth() == null ? "0%" : String.valueOf(getAcosChainGrowth()) + "%";
    }

    public String getNumAdsShownChainGrowthStr() {
        return getNumAdsShownChainGrowth() == null ? "0%" : String.valueOf(getNumAdsShownChainGrowth()) + "%";
    }

    public String getNumAdsClicksChainGrowthStr() {
        return getNumAdsClicksChainGrowth() == null ? "0%" : String.valueOf(getNumAdsClicksChainGrowth()) + "%";
    }

    public String getOrderQuantityChainGrowthStr() {
        return getOrderQuantityChainGrowth() == null ? "0%" : String.valueOf(getOrderQuantityChainGrowth()) + "%";
    }

    public String getSaleQuantityChainGrowthStr() {
        return getSaleQuantityChainGrowth() == null ? "0%" : String.valueOf(getSaleQuantityChainGrowth()) + "%";
    }

    public String getClickRateChainGrowthStr() {
        return getClickRateChainGrowth() == null ? "0%" : String.valueOf(getClickRateChainGrowth()) + "%";
    }

    public String getSalesConversionRateChainGrowthStr() {
        return getSalesConversionRateChainGrowth() == null ? "0%" : String.valueOf(getSalesConversionRateChainGrowth()) + "%";
    }
}
