package com.meiyunji.sponsored.service.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.Map;

@ConfigurationProperties(prefix = "pulsar")
@Setter
@Getter
public class PulsarClientProperties {
    private Map<String, PulsarProperties> clients;
    private Map<String, PulsarProducerProperties> producers;
    private Map<String, PulsarConsumerProperties> consumers;
}