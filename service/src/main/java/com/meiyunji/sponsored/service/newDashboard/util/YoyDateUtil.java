package com.meiyunji.sponsored.service.newDashboard.util;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;

/**
 * @author: sun<PERSON><PERSON>
 * @email: <EMAIL>
 * @date: 2024-03-25  16:54
 */
public class YoyDateUtil {

    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd");
    
    public static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    /**
     * 根据当期开始和结束时间计算同比的开始和结束时间，如果超过2年，返回空
     * @param startDateStr 开始时间 yyyy-mm-dd
     * @param endDateStr 结束时间 yyyy-mm-dd
     * @return
     */
    public static List<String> getYoyDate(String startDateStr, String endDateStr) {
        return format(startDateStr, endDateStr, DATE_FORMATTER);
    }


    public static List<String> getYoyDateFormat(String startDateStr, String endDateStr, DateTimeFormatter formatter) {
        return format(startDateStr, endDateStr, formatter);
    }

    /**
     * 根据当期开始和结束时间计算同比的开始和结束时间，如果超过2年，返回空
     * @param startDateStr 开始时间 yyyy-mm-dd
     * @param endDateStr 结束时间 yyyy-mm-dd
     * @return
     */
    private static List<String> format(String startDateStr, String endDateStr, DateTimeFormatter formatter) {
        // 将字符串转换成LocalDate对象
        LocalDate startDate = LocalDate.parse(startDateStr, formatter);
        LocalDate endDate = LocalDate.parse(endDateStr, formatter);

        // 计算同比的开始时间和结束时间
        LocalDate yoyStartDate = startDate.minusYears(1);
        LocalDate yoyEndDate = endDate.minusYears(1);


        //如果时间超出两年
        LocalDate twoYearAgoDate = LocalDate.now().minusYears(2);
        if (yoyStartDate.isBefore(twoYearAgoDate)) {
            //同比结束时间超出2年，返回空
            return null;
        }

        // 将LocalDate对象转换成字符串
        String yoyStartDateStr = yoyStartDate.format(formatter);
        String yoyEndDateStr = yoyEndDate.format(formatter);

        return Arrays.asList(yoyStartDateStr, yoyEndDateStr);
    }
}
