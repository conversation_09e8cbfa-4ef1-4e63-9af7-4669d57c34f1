package com.meiyunji.sponsored.service.doris.dao;

import com.meiyunji.sponsored.common.springjdbc.IDorisBaseDao;
import com.meiyunji.sponsored.service.doris.po.OdsAmazonAdSbGroupReport;
import com.meiyunji.sponsored.service.newDashboard.dto.CampaignOrGroupOrPortfolioDto;
import com.meiyunji.sponsored.service.newDashboard.dto.DashboardAdTopDataDto;
import com.meiyunji.sponsored.service.newDashboard.dto.DashboardAdTypeDataDto;
import com.meiyunji.sponsored.service.newDashboard.enums.DashboardDataFieldEnum;
import com.meiyunji.sponsored.service.newDashboard.enums.DashboardOrderByRateEnum;
import com.meiyunji.sponsored.service.newDashboard.enums.DashboardQueryFieldEnum;

import java.util.List;

/**
 * sb广告组报告(OdsAmazonAdSbGroupReport)数据库访问层
 *
 * <AUTHOR>
 * @since 2024-03-26 10:46:19
 */
public interface IOdsAmazonAdSbGroupReportDao extends IDorisBaseDao<OdsAmazonAdSbGroupReport> {

    /**
     * 广告看板查询sb广告类型图表数据
     * @param puid puid
     * @param marketplaceIdList 站点id集合
     * @param shopIdList 店铺id集合
     * @param currency 币种
     * @param startDate 开始时间yyyy-mm-dd
     * @param endDate 结束时间yyyy-mm-dd
     * @return
     */
    List<DashboardAdTypeDataDto> queryAdTypeCharts(Integer puid,
                                                   List<String> marketplaceIdList,
                                                   List<Integer> shopIdList,
                                                   String currency,
                                                   String startDate,
                                                   String endDate, List<String> siteToday, Boolean isSiteToday,
                                                   List<String> portfolioIds, List<String> campaignIds);
    List<DashboardAdTopDataDto> queryAdSbGroupYoyOrMomTop(String subSqlA, String subSqlB,
                                                        List<Object> queryParam, DashboardDataFieldEnum dataField,
                                                        DashboardOrderByRateEnum orderField, String orderBy,
                                                        int limit, Boolean noZero);

    List<CampaignOrGroupOrPortfolioDto> queryAdSbGroupCharts(Integer puid, List<Integer> shopIdList,
                                                           List<String> marketplaceIdList, List<String> groupIdList,
                                                           String currency, String startDate,
                                                           String endDate);

    String sbGroupQuerySql(Integer puid, List<Integer> shopIdList,
                         List<String> marketplaceIdList, List<String> groupIdList,
                         String currency, String startDate, String endDate,
                         List<Object> argsList, List<String> siteToday, Boolean isSiteToday, List<String> portfolioIds, List<String> campaignIds, Boolean noZero, DashboardDataFieldEnum dashboardDataFieldEnum);
}

