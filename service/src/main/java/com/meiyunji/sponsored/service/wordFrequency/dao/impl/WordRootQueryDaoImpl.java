package com.meiyunji.sponsored.service.wordFrequency.dao.impl;

import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.springjdbc.BaseShardingSphereDaoImpl;
import com.meiyunji.sponsored.service.wordFrequency.bo.WordRootTranslatorBo;
import com.meiyunji.sponsored.service.wordFrequency.dao.IWordRootQueryDao;
import com.meiyunji.sponsored.service.wordFrequency.po.WordRootQuery;
import org.apache.shardingsphere.infra.hint.HintManager;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;

/**
 * @author: zhulukun
 * @email: <EMAIL>
 * @date: 2023-11-06  14:24
 */
@Repository
public class WordRootQueryDaoImpl extends BaseShardingSphereDaoImpl<WordRootQuery> implements IWordRootQueryDao {

    @Override
    public void batchInsertOrUpdateSpQuery(Integer puid, List<WordRootQuery> list) {
        StringBuilder sql = new StringBuilder("INSERT INTO `t_amazon_word_root_query` (`puid`,`shop_id`,`query_id`,`word_frequency_type`,`keyword_text`,")
                .append("`word_root`,`keyword_id`,`query`,`query_cn`,`marketplace_id`,`campaign_id`,`ad_group_id`,`count_date`,`match_type`,`cost`," +
                        "`total_sales`,`ad_sales`,`impressions`,`clicks`,`order_num`,`ad_order_num`,`sale_num`,`ad_sale_num`,`query_type`," +
                        "`create_at`,`update_at`) VALUES");
        List<Object> argsList = Lists.newArrayList();
        for (WordRootQuery wordRootQuery : list) {
            sql.append(" (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,now(),now()),");
            argsList.add(puid);
            argsList.add(wordRootQuery.getShopId());
            argsList.add(wordRootQuery.getQueryId());
            argsList.add(wordRootQuery.getWordFrequencyType());
            argsList.add(wordRootQuery.getKeywordText());
            argsList.add(wordRootQuery.getWordRoot());
            argsList.add(wordRootQuery.getKeywordId());
            argsList.add(wordRootQuery.getQuery());
            argsList.add(wordRootQuery.getQueryCn());

            argsList.add(wordRootQuery.getMarketplaceId());
            argsList.add(wordRootQuery.getCampaignId());
            argsList.add(wordRootQuery.getAdGroupId());
            argsList.add(wordRootQuery.getCountDate());
            argsList.add(wordRootQuery.getMatchType());
            argsList.add(wordRootQuery.getCost());
            argsList.add(wordRootQuery.getTotalSales());
            argsList.add(wordRootQuery.getAdSales());
            argsList.add(wordRootQuery.getImpressions());
            argsList.add(wordRootQuery.getClicks());
            argsList.add(wordRootQuery.getOrderNum());
            argsList.add(wordRootQuery.getAdOrderNum());
            argsList.add(wordRootQuery.getSaleNum());
            argsList.add(wordRootQuery.getAdSaleNum());
            argsList.add(wordRootQuery.getQueryType());

        }
        sql.deleteCharAt(sql.length() - 1);
        sql.append(" on duplicate key update `puid`=values(puid),`shop_id`=values(shop_id),")
                .append("`query_id`=values(query_id),word_frequency_type=values(word_frequency_type)," +
                        "keyword_text=values(keyword_text),`word_root`=values(word_root),`keyword_id`=values(keyword_id),`query`=values(query),`query_cn`=values(query_cn)," +
                        "marketplace_id=values(marketplace_id),campaign_id=values(campaign_id),ad_group_id=values(ad_group_id),count_date=values(count_date)," +
                        "match_type=values(match_type),cost=values(cost),total_sales=values(total_sales),ad_sales=values(ad_sales),impressions=values(impressions)," +
                        "clicks=values(clicks),order_num=values(order_num),ad_order_num=values(ad_order_num),sale_num=values(sale_num),ad_sale_num=values(ad_sale_num)");
        HintManager hintManager = HintManager.getInstance();
        try {
            getJdbcTemplate(puid, hintManager).update(sql.toString(), argsList.toArray());
        } finally {
            hintManager.close();
        }
    }

    @Override
    public void batchInsertOrUpdateSpTargeting(Integer puid, List<WordRootQuery> wordRootQueryList) {
        StringBuilder sql = new StringBuilder("INSERT INTO `t_amazon_word_root_query` (`puid`,`shop_id`,`query_id`,`word_frequency_type`,")
                .append("`word_root`,`keyword_id`, `query`,`query_cn`,`marketplace_id`,`campaign_id`,`ad_group_id`,`count_date`,`cost`," +
                        "`total_sales`,`ad_sales`,`impressions`,`clicks`,`order_num`,`ad_order_num`,`sale_num`,`ad_sale_num`," +
                        "`targeting_expression`,`targeting_text`,`targeting_type`,`is_asin`,`query_type`," +
                        "`create_at`,`update_at`) VALUES");
        List<Object> argsList = Lists.newArrayList();
        for (WordRootQuery wordRootQuery : wordRootQueryList) {
            sql.append(" (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,now(),now()),");
            argsList.add(puid);
            argsList.add(wordRootQuery.getShopId());
            argsList.add(wordRootQuery.getQueryId());
            argsList.add(wordRootQuery.getWordFrequencyType());
            argsList.add(wordRootQuery.getWordRoot());
            argsList.add(wordRootQuery.getTargetId());
            argsList.add(wordRootQuery.getQuery());
            argsList.add(wordRootQuery.getQueryCn());

            argsList.add(wordRootQuery.getMarketplaceId());
            argsList.add(wordRootQuery.getCampaignId());
            argsList.add(wordRootQuery.getAdGroupId());
            argsList.add(wordRootQuery.getCountDate());
            argsList.add(wordRootQuery.getCost());
            argsList.add(wordRootQuery.getTotalSales());
            argsList.add(wordRootQuery.getAdSales());
            argsList.add(wordRootQuery.getImpressions());
            argsList.add(wordRootQuery.getClicks());
            argsList.add(wordRootQuery.getOrderNum());
            argsList.add(wordRootQuery.getAdOrderNum());
            argsList.add(wordRootQuery.getSaleNum());
            argsList.add(wordRootQuery.getAdSaleNum());

            argsList.add(wordRootQuery.getTargetingExpression());
            argsList.add(wordRootQuery.getTargetingText());
            argsList.add(wordRootQuery.getTargetingType());
            argsList.add(wordRootQuery.getIsAsin());
            argsList.add(wordRootQuery.getQueryType());

        }
        sql.deleteCharAt(sql.length() - 1);
        sql.append(" on duplicate key update `puid`=values(puid),`shop_id`=values(shop_id),")
                .append("`query_id`=values(query_id),word_frequency_type=values(word_frequency_type)," +
                        "`word_root`=values(word_root),`keyword_id`=values(keyword_id),`query`=values(query),`query_cn`=values(query_cn)," +
                        "marketplace_id=values(marketplace_id),campaign_id=values(campaign_id),ad_group_id=values(ad_group_id),count_date=values(count_date)," +
                        "cost=values(cost),total_sales=values(total_sales),ad_sales=values(ad_sales),impressions=values(impressions)," +
                        "clicks=values(clicks),order_num=values(order_num),ad_order_num=values(ad_order_num),sale_num=values(sale_num),ad_sale_num=values(ad_sale_num)," +
                        "targeting_expression=values(targeting_expression),targeting_text=values(targeting_text),targeting_type=values(targeting_type),is_asin=values(is_asin)");
        HintManager hintManager = HintManager.getInstance();
        try {
            getJdbcTemplate(puid, hintManager).update(sql.toString(), argsList.toArray());
        } finally {
            hintManager.close();
        }
    }

    @Override
    public void batchInsertOrUpdateSbQuery(Integer puid, List<WordRootQuery> wordRootQueryList) {
        StringBuilder sql = new StringBuilder("INSERT INTO `t_amazon_word_root_query` (`puid`,`shop_id`,`query_id`,`word_frequency_type`,`keyword_text`,")
                .append("`word_root`,`keyword_id`, `query`, `query_cn`,`marketplace_id`,`campaign_id`,`ad_group_id`,`count_date`,`match_type`,`cost`," +
                        "`impressions`,`clicks`,`ad_format`,`sales14d`,`conversions14d`,`impression_share`,`impression_rank`,`sales_new_to_brand14d`,`orders_new_to_brand14d`," +
                        "`units_ordered_new_to_brand_percentage14d`,`units_ordered_new_to_brand14d`,`sales_new_to_brand_percentage14d`,`order_rate_new_to_brand14d`,`orders_new_to_brand_percentage14d`," +
                        "`vtr`,`viewable_impressions`,`video_unmutes`,`video_third_quartile_views`,`video_midpoint_views`," +
                        "`video_first_quartile_views`,`video_complete_views`,`video5second_views`,`video5second_view_rate`,`vctr`, `query_type`," +
                        "`create_at`,`update_at`) VALUES");
        List<Object> argsList = Lists.newArrayList();
        for (WordRootQuery wordRootQuery : wordRootQueryList) {
            sql.append(" (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,now(),now()),");
            argsList.add(puid);
            argsList.add(wordRootQuery.getShopId());
            argsList.add(wordRootQuery.getQueryId());
            argsList.add(wordRootQuery.getWordFrequencyType());
            argsList.add(wordRootQuery.getKeywordText());
            argsList.add(wordRootQuery.getWordRoot());
            argsList.add(wordRootQuery.getKeywordId());
            argsList.add(wordRootQuery.getQuery());
            argsList.add(wordRootQuery.getQueryCn());
            argsList.add(wordRootQuery.getMarketplaceId());
            argsList.add(wordRootQuery.getCampaignId());
            argsList.add(wordRootQuery.getAdGroupId());
            argsList.add(wordRootQuery.getCountDate());
            argsList.add(wordRootQuery.getMatchType());
            argsList.add(wordRootQuery.getCost());
            argsList.add(wordRootQuery.getImpressions());
            argsList.add(wordRootQuery.getClicks());
            argsList.add(wordRootQuery.getAdFormat());
            argsList.add(wordRootQuery.getSales14d());
            argsList.add(wordRootQuery.getConversions14d());
            argsList.add(wordRootQuery.getImpressionShare());
            argsList.add(wordRootQuery.getImpressionRank());
            argsList.add(wordRootQuery.getSalesNewToBrand14d());
            argsList.add(wordRootQuery.getOrdersNewToBrand14d());
            argsList.add(wordRootQuery.getUnitsOrderedNewToBrandPercentage14d());
            argsList.add(wordRootQuery.getUnitsOrderedNewToBrand14d());
            argsList.add(wordRootQuery.getSalesNewToBrandPercentage14d());
            argsList.add(wordRootQuery.getOrderRateNewToBrand14d());
            argsList.add(wordRootQuery.getOrdersNewToBrandPercentage14d());
            argsList.add(wordRootQuery.getVtr());
            argsList.add(wordRootQuery.getViewableImpressions());
            argsList.add(wordRootQuery.getVideoUnmutes());
            argsList.add(wordRootQuery.getVideoThirdQuartileViews());
            argsList.add(wordRootQuery.getVideoMidpointViews());
            argsList.add(wordRootQuery.getVideoFirstQuartileViews());
            argsList.add(wordRootQuery.getVideoCompleteViews());
            argsList.add(wordRootQuery.getVideo5secondViews());
            argsList.add(wordRootQuery.getVideo5secondViewRate());
            argsList.add(wordRootQuery.getVctr());
            argsList.add(wordRootQuery.getQueryType());
        }
        sql.deleteCharAt(sql.length() - 1);
        sql.append(" on duplicate key update `puid`=values(puid),`shop_id`=values(shop_id),")
                .append("`query_id`=values(query_id),word_frequency_type=values(word_frequency_type)," +
                        "keyword_text=values(keyword_text),`word_root`=values(word_root),`keyword_id`=values(keyword_id),`query`=values(query),`query_cn`=values(query_cn)," +
                        "marketplace_id=values(marketplace_id),campaign_id=values(campaign_id),ad_group_id=values(ad_group_id),count_date=values(count_date)," +
                        "match_type=values(match_type),cost=values(cost),impressions=values(impressions)," +
                        "clicks=values(clicks),ad_format=values(ad_format),sales14d=values(sales14d),conversions14d=values(conversions14d),impression_share=values(impression_share)," +
                        "impression_rank=values(impression_rank),sales_new_to_brand14d=values(sales_new_to_brand14d),orders_new_to_brand14d=values(orders_new_to_brand14d),units_sold14d=values(units_sold14d),units_ordered_new_to_brand_percentage14d=values(units_ordered_new_to_brand_percentage14d)," +
                        "units_ordered_new_to_brand14d=values(units_ordered_new_to_brand14d),sales_new_to_brand_percentage14d=values(sales_new_to_brand_percentage14d),order_rate_new_to_brand14d=values(order_rate_new_to_brand14d),orders_new_to_brand_percentage14d=values(orders_new_to_brand_percentage14d),vtr=values(vtr)," +
                        "viewable_impressions=values(viewable_impressions),video_unmutes=values(video_unmutes),video_third_quartile_views=values(video_third_quartile_views),video_midpoint_views=values(video_midpoint_views),video_first_quartile_views=values(video_first_quartile_views)," +
                        "video_complete_views=values(video_complete_views),video5second_views=values(video5second_views),video5second_view_rate=values(video5second_view_rate),vctr=values(vctr)");
        HintManager hintManager = HintManager.getInstance();
        try {
            getJdbcTemplate(puid, hintManager).update(sql.toString(), argsList.toArray());
        } finally {
            hintManager.close();
        }
    }

    @Override
    public List<WordRootTranslatorBo> listTranslatorBoByShopId(Integer puid, Integer shopId, String start, String end, int limit) {
        StringBuilder sb = new StringBuilder("select id, word_root wordRoot from " + this.getJdbcHelper().getTable());
        sb.append(" where puid = ? and shop_id = ? and count_date >= ? and count_date <= ? and word_root_cn is null limit " + limit);
        List<Object> argsList = new ArrayList<>();
        argsList.add(puid);
        argsList.add(shopId);
        argsList.add(start);
        argsList.add(end);
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(sb.toString(), argsList.toArray(), new BeanPropertyRowMapper<>(WordRootTranslatorBo.class));
        } finally {
            hintManager.close();
        }
    }

    @Override
    public void batchUpdateWordRootCn(Integer puid, List<WordRootTranslatorBo> updateList) {
        StringBuilder sql = new StringBuilder("update " + this.getJdbcHelper().getTable() + " set word_root_cn = ? where id = ?");
        List<Object[]> batchArgs = Lists.newArrayList();
        Object[] batchArg;
        for (WordRootTranslatorBo bo : updateList) {
            batchArg = new Object[]{bo.getWordRootCn(), bo.getId()};
            batchArgs.add(batchArg);
        }
        HintManager hintManager = HintManager.getInstance();
        try {
            getJdbcTemplate(puid, hintManager).batchUpdate(sql.toString(), batchArgs);
        } finally {
            hintManager.close();
        }
    }

    @Override
    public int deleteByPuidAndShopId(Integer puid, Integer shopId, Integer limit) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sql = new StringBuilder("delete from ").append(this.getJdbcHelper().getTable()).append(" where puid = ? ");
        argsList.add(puid);
        sql.append(" and shop_id = ? ");
        argsList.add(shopId);
        if (limit != null) {
            sql.append(" limit ? ");
            argsList.add(limit);
        }
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).update(sql.toString(), argsList.toArray());
        } finally {
            hintManager.close();
        }
    }
}
