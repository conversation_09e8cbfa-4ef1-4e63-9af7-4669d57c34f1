package com.meiyunji.sponsored.service.doris.bo;

import com.meiyunji.sponsored.service.newDashboard.dto.base.DashboardAdCalDataDto;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * @author: liweibin
 * @email: <EMAIL>
 * @date: 2024-04-15  14:01
 */
@Data
public class AmazonAdCampaignAllReportBo extends DashboardAdCalDataDto {

    private String campaignId;

    //广告花费
    private BigDecimal cost;

    //广告销售额
    private BigDecimal totalSales;

    //广告曝光量
    private Long impressions;

    //广告点击量
    private Integer clicks;

    //广告订单量
    private Integer orderNum;

    //广告销量
    private Integer saleNum;

    //计算指标，以下数据没有 * 100
    //ACoS
    private BigDecimal acos;

    //ROAS
    private BigDecimal roas;

    //广告点击率
    private BigDecimal clickRate;

    //广告转化率
    private BigDecimal conversionRate;

    //CPC
    private BigDecimal cpc;

    //CPA
    private BigDecimal cpa;

    private LocalDate countDay;
}
