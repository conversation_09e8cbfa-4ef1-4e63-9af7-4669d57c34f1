package com.meiyunji.sponsored.service.cpc.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;

@Data
@ApiModel
public class TargetAndQueryReportExcelVo {

    @ExcelProperty(value = "投放")
    private String target;
    @ExcelProperty(value = "搜索词")
    private String query;
    @ExcelProperty(value = "匹配类型")
    private String matchType;
    @ExcelProperty(value = "广告活动")
    private String campaignName;
    @ExcelProperty(value = "广告组")
    private String adGroupName;
    @ExcelProperty(value = "ASIN")
    private String asin;
    @ExcelProperty(value = "父ASIN")
    private String parentAsin;
    @ExcelProperty(value = "店铺")
    private String shopName;
    @ExcelProperty(value = "站点")
    private String marketplaceCN;
    @ExcelProperty(value = "曝光量")
    private Integer impressions;
    @ExcelProperty(value = "点击量")
    private Integer clicks;
    @ExcelProperty(value = "点击率")
    private String ctr;
    @ExcelProperty(value = "订单转化率")
    private String cvr;
    @ExcelProperty(value = "ACoS")
    private String acos;
    @ExcelProperty(value = "ACoTS")
    private String acots;
    @ExcelProperty(value = "ASoTS")
    private String asots;
    @ExcelProperty(value = "广告订单数")
    private Integer adOrderNum;
    @ExcelProperty(value = "广告花费")
    private String adCost;
    @ExcelProperty(value = "平均点击费用")
    private String adCostPerClick;
    @ExcelProperty(value = "广告销售额")
    private String adSale;
    @ExcelProperty(value = "ROAS")
    private String roas;


}
