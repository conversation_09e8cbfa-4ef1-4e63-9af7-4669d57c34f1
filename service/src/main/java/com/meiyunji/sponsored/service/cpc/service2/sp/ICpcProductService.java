package com.meiyunji.sponsored.service.cpc.service2.sp;

import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.rpc.adCommon.AllProductAggregateDataResponse;
import com.meiyunji.sponsored.rpc.adCommon.AllProductDataResponse;
import com.meiyunji.sponsored.rpc.sp.campaign.ProductInfoResp;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.cpc.dto.AdProductCommonInfoDto;
import com.meiyunji.sponsored.service.cpc.dto.CpcProductDto;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdProduct;
import com.meiyunji.sponsored.service.cpc.vo.*;
import com.meiyunji.sponsored.service.doris.po.OdsAmazonAdProduct;
import com.meiyunji.sponsored.service.doris.po.OdsAmazonSbAds;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdProfile;
import com.meiyunji.sponsored.service.cpc.vo.AdPerformanceParam;
import com.meiyunji.sponsored.service.cpc.vo.AdProductPageParam;
import com.meiyunji.sponsored.service.cpc.vo.AdProductPageVo;
import com.meiyunji.sponsored.service.cpc.vo.AddAdProductVo;

import java.util.List;
import java.util.Map;

/**
 * Created by xp on 2021/3/30.
 * 广告产品相关业务
 */
public interface ICpcProductService {


    /**
     * 展示已经添加的广告产品
     * @param puid:
     * @param shopId:
     * @param dxmGroupId:
     */
    Result<List<AdProductPageVo>> showAdd(Integer puid, Integer shopId, Long dxmGroupId);

    /**
     * 添加广告产品
     *
     * @param addAdProductVo@return ：Result
     */
    Result addProduct(AddAdProductVo addAdProductVo, String loginIp);

    /**
     * 添加广告产品
     *
     * @param addAdProductVo@return ：Result
     */
    Result<List<ProductInfoResp>> addProductToGroup(ShopAuth shop, String campaignId, String groupId, List<CpcProductDto> products, String loginIp, Integer uid, AmazonAdProfile targetAmazonAdProfile);

    /**
     * 改状态
     *
     * @param puid
     * @param uid
     * @param id    ：
     * @param state ：
     * @return ：
     */
    Result updateState(Integer puid,Integer uid,  Long id, String state, String loginIp);

    /**
     * 广告-归档
     *
     * @param puid
     * @param uid
     * @param id ：
     * @return ：
     */
    Result archive(Integer puid,Integer uid,Long id, String loginIp);

    /**
     * 获取广告表现数据
     * @param puid:
     * @param param:
     * @return :
     */
    Result showAdPerformance(int puid, AdPerformanceParam param);

    /**
     * 广告产品首页数据
     *
     * @param puid
     * @param param
     * @return
     */
    AllProductDataResponse.AdProductHomeVo getAllProductData(int puid, AdProductPageParam param);

    Map<String, AdProductCommonInfoDto> getAdProductFbaInfo(List<AdProductPageVo> rows, int puid);

    AllProductDataResponse.AdProductHomeVo getAllSpProductDorisData(int puid, AdProductPageParam param);

    AllProductAggregateDataResponse.AdProductHomeVo getAllProductAggregateData(int puid, AdProductPageParam param);

    AllProductAggregateDataResponse.AdProductHomeVo getAllSpProductDorisAggregateData(int puid, AdProductPageParam param);

    List<AdProductPageVo> getSpProductVoList(ShopAuth shopAuth, Integer puid, AdProductPageParam param, Page<AdProductPageVo> voPage, boolean isExport);

    List<AdProductPageVo> getSdProductVoList(ShopAuth shopAuth, Integer puid, AdProductPageParam param, Page<AdProductPageVo> voPage, boolean isExport);


    Result updateBatch(List<AmazonAdProduct> amazonAdProducts, String loginIp);

    void saveDoris(List<AmazonAdProduct> succList, boolean create, boolean update);

    List<OdsAmazonAdProduct> getSpProductGroup(MultiShopGroupByProductParam param);

    List<AdProductPageVo> getSpProductClickData(MultiShopGroupByProductParam param, List<OdsAmazonAdProduct> productGroup);

    List<OdsAmazonSbAds> getSbProductGroup(MultiShopGroupByProductParam param);
}
