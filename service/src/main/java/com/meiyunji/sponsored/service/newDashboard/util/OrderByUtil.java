package com.meiyunji.sponsored.service.newDashboard.util;

import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.common.util.ReflexUtils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.time.LocalDateTime;
import java.util.*;


/**
 * @description: some desc
 * @author: pxq
 * @email: <EMAIL>
 * @date: 2025/1/7 13:55
 */
@Slf4j
public class OrderByUtil {

    /**
     * @param list
     * @param orderField
     * @param orderValue
     * @param orderDefaultField 第二排序字段，如果排序字段为0 则再用这个字段排序，非必传
     * @param <T>
     */
    public static <T> void sortedByOrderField(List<T> list, String orderField, String orderValue, String orderDefaultField) {
        Collections.sort(list, new Comparator<T>() {
            @Override
            public int compare(T a, T b) {
                try {
                    Object aValue = ReflexUtils.getField(a, orderField);
                    Object bValue = ReflexUtils.getField(b, orderField);
                    int i = compareValue(aValue, bValue, orderValue);
                    if (i == 0 && StringUtils.isNotBlank(orderDefaultField)) {
                        aValue = ReflexUtils.getField(a, orderDefaultField);
                        bValue = ReflexUtils.getField(b, orderDefaultField);
                        i = compareValue(aValue, bValue, orderDefaultField);
                    }
                    return i;
                } catch (Exception e) {
                    log.info("排序错误，{}", e);
                    return 0;
                }

            }
        });
    }

    private static int compareValue(Object aValue, Object bValue, String orderValue) {
        boolean isDesc = "desc".equalsIgnoreCase(orderValue);
        if (aValue == null || bValue == null) {
            int sign = isDesc ? -1 : 1;

            if (aValue != null && bValue == null) {
                return sign;
            }
            if (aValue == null && bValue != null) {
                return -sign;
            }
            return 0;
        }
        String aStr = aValue.toString();
        String bStr = bValue.toString();
        //默认按该字段的字符串排序
        if (aValue instanceof String) {
            String rateValue1 = aStr.replace("%", "");
            String rateValue2 = bStr.replace("%", "");
            boolean a = canConvertToBigDecimalWithFormat(rateValue1);
            boolean b = canConvertToBigDecimalWithFormat(rateValue2);
            if (a && b) {
                if (("--".equalsIgnoreCase(rateValue2) || "null".equalsIgnoreCase(rateValue2)) && ("--".equalsIgnoreCase(rateValue1) || "null".equalsIgnoreCase(rateValue1))) {
                    return 0;
                } else if ((!"--".equalsIgnoreCase(rateValue1) && !"null".equalsIgnoreCase(rateValue1)) && ("--".equalsIgnoreCase(rateValue2) || "null".equalsIgnoreCase(rateValue2))) {
                    return isDesc ? -1 : 1;
                } else if ("--".equalsIgnoreCase(rateValue1) || "null".equalsIgnoreCase(rateValue1)) {
                    return isDesc ? 1 : -1;
                } else {
                    if (isDesc) {
                        return new BigDecimal(rateValue2).compareTo(new BigDecimal(rateValue1));
                    } else {
                        return new BigDecimal(rateValue1).compareTo(new BigDecimal(rateValue2));
                    }
                }
            } else {
                if (isDesc) {
                    return bStr.compareTo(aStr);
                } else {
                    return aStr.compareTo(bStr);
                }
            }
        } else if (aValue instanceof BigDecimal || aValue instanceof Double) {
            if (isDesc) {
                return new BigDecimal(bStr).compareTo(new BigDecimal(aStr));
            } else {
                return new BigDecimal(aStr).compareTo(new BigDecimal(bStr));
            }
        } else if (aValue instanceof Integer) {
            if (isDesc) {
                return Integer.compare(Integer.parseInt(bStr), Integer.parseInt(aStr));
            } else {
                return Integer.compare(Integer.parseInt(aStr), Integer.parseInt(bStr));
            }
        } else if (aValue instanceof LocalDateTime) {
            if (isDesc) {
                return ((LocalDateTime) bValue).compareTo(((LocalDateTime) aValue));
            } else {
                return ((LocalDateTime) aValue).compareTo(((LocalDateTime) bValue));
            }
        } else if (aValue instanceof Long) {
            if (isDesc) {
                return Long.compare(Integer.parseInt(bStr), Integer.parseInt(aStr));
            } else {
                return Long.compare(Integer.parseInt(aStr), Integer.parseInt(bStr));
            }
        } else {
            if (isDesc) {
                return DateUtil.stringToDate(bStr).compareTo(DateUtil.stringToDate(aStr));
            } else {
                return DateUtil.stringToDate(aStr).compareTo(DateUtil.stringToDate(bStr));
            }
        }

    }


    private static boolean canConvertToBigDecimalWithFormat(String str) {
        NumberFormat format = DecimalFormat.getInstance(Locale.US);
        try {
            format.parse(str);
            return true;
        } catch (Exception e) {
            return false;
        }
    }


}
