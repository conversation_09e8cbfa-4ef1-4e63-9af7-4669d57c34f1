package com.meiyunji.sponsored.service.kafka.message;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class SponsoredIndexSelectMessage {

    private Integer puid;

    private Long taskId;

    private String mt;

    private String ver;

    private String deadline;

    private List<Integer> shopIds;

    private List<SponsoredIndexObject> mv;

    private List<SponsoredIndexTarget> targetList;

    private Map<String, List<SponsoredIndexTarget>> targetMap;
}
