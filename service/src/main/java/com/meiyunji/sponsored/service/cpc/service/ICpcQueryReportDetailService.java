package com.meiyunji.sponsored.service.cpc.service;

import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.rpc.adCommon.AllQueryTargetAggregateDataResponse;
import com.meiyunji.sponsored.rpc.adCommon.AllQueryTargetDataResponse;
import com.meiyunji.sponsored.service.cpc.vo.*;
import com.meiyunji.sponsored.service.reportHour.vo.AdQueryKeywordAndTargetVo;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface ICpcQueryReportDetailService {

    List<AdQueryKeywordAndTargetVo> getWeeklyList(int puid, String adType, CpcQueryWordDetailDto param, List<AdQueryKeywordAndTargetVo> compares);

    List<AdQueryKeywordAndTargetVo> getDailyList(int puid, String adType, CpcQueryWordDetailDto param, List<AdQueryKeywordAndTargetVo> compares);

    List<AdQueryKeywordAndTargetVo> getMonthlyList(int puid, String adType, CpcQueryWordDetailDto param, List<AdQueryKeywordAndTargetVo> compares);

    List<AdQueryKeywordAndTargetVo> allSearchTermDailyList(int puid, CpcQueryWordDetailDto param, List<AdQueryKeywordAndTargetVo> compares);

    List<AdQueryKeywordAndTargetVo> allSearchTermWeeklyList(int puid, CpcQueryWordDetailDto param, List<AdQueryKeywordAndTargetVo> compares);

    List<AdQueryKeywordAndTargetVo> allSearchTermMonthlyList(int puid, CpcQueryWordDetailDto param, List<AdQueryKeywordAndTargetVo> compares);
}
