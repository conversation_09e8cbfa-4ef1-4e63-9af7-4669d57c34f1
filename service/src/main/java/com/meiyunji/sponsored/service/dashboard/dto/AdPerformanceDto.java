package com.meiyunji.sponsored.service.dashboard.dto;

import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 广告效果
 */
@Data
public class AdPerformanceDto {
    private String countDate;
    /**
     * 广告曝光量
     */
    private Long impressions = 0L;
    /**
     * 广告曝光量
     */
    private Long clicks = 0L;
    /**
     * 广告点击率   点击量/曝光量 * 100%
     */
    private BigDecimal clickRate = BigDecimal.ZERO;
    /**
     * CPC 广告花费/广告点击量(非百分比数据)
     */
    private BigDecimal cpc = BigDecimal.ZERO;
    /**
     * 广告转化率  广告订单量/点击量 * 100%
     */
    private BigDecimal salesConversionRate = BigDecimal.ZERO;
    /**
     * 花费
     */
    private BigDecimal cost = BigDecimal.ZERO;
    /**
     * 广告订单量
     */
    private Integer adOrder = 0;
    /**
     * 广告销售额
     */
    private BigDecimal adSales = BigDecimal.ZERO;
    /**
     * acos  广告费/销售额*100%
     */
    private BigDecimal acos = BigDecimal.ZERO;
    /**
     * roas 销售额/广告费(非百分比数据)
     */
    private BigDecimal roas = BigDecimal.ZERO;
    /**
     * spc 广告销售额/点击(非百分比数据)
     */
    private BigDecimal spc = BigDecimal.ZERO;
}
