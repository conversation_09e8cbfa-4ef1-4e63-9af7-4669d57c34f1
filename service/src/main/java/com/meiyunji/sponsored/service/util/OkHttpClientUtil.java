package com.meiyunji.sponsored.service.util;

import com.meiyunji.sponsored.common.util.ApiSignUtil;
import com.meiyunji.sponsored.common.util.JSONUtil;
import com.meiyunji.sponsored.service.enums.SellfoxApiEnum;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.collections4.MapUtils;

import java.net.URLEncoder;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * @author: wade
 * @date: 2021/11/18 21:03
 * @describe:
 */
@Slf4j
public class OkHttpClientUtil {

    private static OkHttpClient CLIENT = new OkHttpClient.Builder()
            .connectTimeout(4, TimeUnit.SECONDS)
            .readTimeout(1, TimeUnit.MINUTES)
            .build();

    static {
        CLIENT.dispatcher().setMaxRequestsPerHost(200);

    }

    public static OkHttpClient getClient() {
       return CLIENT;
    }



    public static String getParams(Map<String, Object> params) {
        //加入验签必须字段,生成签名

        StringBuffer sb = new StringBuffer("?");
        if (MapUtils.isNotEmpty(params)) {
            for (Map.Entry<String, Object> item : params.entrySet()) {
                Object value = item.getValue();
                if (value!=null) {
                    sb.append("&");
                    sb.append(item.getKey());
                    sb.append("=");
                    sb.append(URLEncoder.encode(String.valueOf(value)));
                }
            }
            return sb.toString();
        } else {
            return "";
        }
    }

    public static Request getRequest(String urlPrefix, SellfoxApiEnum apiEnum, TreeMap<String,Object> params) {
        //加入验签必须字段,生成签名
        String timestamp= String.valueOf(System.currentTimeMillis());

        TreeMap<String, Object> map = new TreeMap<>();
        String nonce = UUID.randomUUID().toString();
        map.putAll(params);
        map.put("timestamp", timestamp);
        map.put("nonce", nonce);
        //生成签名
        String sign = ApiSignUtil.getSign(map);

        return new Request.Builder().url(urlPrefix.concat(apiEnum.getCode()).concat(getParams(params))).get()
                .header("signature",sign)
                .header("timestamp",timestamp)
                .header("nonce",nonce).build();
    }

    public static Request postRequest(String urlPrefix, SellfoxApiEnum apiEnum, TreeMap<String, Object> params) {
        //加入验签必须字段,生成签名
        String timestamp = String.valueOf(System.currentTimeMillis());

        TreeMap<String, Object> map = new TreeMap<>();
        String nonce = UUID.randomUUID().toString();
        map.putAll(params);
        map.put("timestamp", timestamp);
        map.put("nonce", nonce);
        //生成签名
        String sign = ApiSignUtil.getSign(map);

        RequestBody body = RequestBody.create(MediaType.parse("application/json; charset=utf-8"), JSONUtil.objectToJson(params));

        return new Request.Builder().url(urlPrefix.concat(apiEnum.getCode()))
                .post(body)
                .header("signature", sign)
                .header("timestamp", timestamp)
                .header("nonce", nonce)
                .build();
    }

    public static Request getAsinImageRequest(String urlPrefix, TreeMap<String,Object> params) {
        return new Request.Builder().url(urlPrefix.concat(getParams(params))).get().build();
    }



    public static Request getPostcodeRequest(String urlPrefix, TreeMap<String,Object> params) {
        return new Request.Builder().url(urlPrefix.concat(getParams(params))).get().build();
    }




}
