package com.meiyunji.sponsored.service.syncTask.amc.enums;

import java.util.Arrays;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @author: sunlin<PERSON>
 * @email: <EMAIL>
 * @date: 2025-02-14  10:52
 */
public enum AmcReportTypeEnum {

    SEARCH_TERM("search_term", "amcSearchTermReportProcessor", "搜索词广告位报告", "sponsored_ads_traffic"),
    SEARCH_TERM_ATTRIBUTED("search_term_attributed", "amcSearchTermAttributedReportProcessor", "搜索词广告位归因报告", "amazon_attributed_events_by_traffic_time"),
    ;

    private String type;

    private String beanName;

    private String desc;

    private String amazonTable;

    AmcReportTypeEnum(String type, String beanName, String desc, String amazonTable) {
        this.type = type;
        this.beanName = beanName;
        this.desc = desc;
        this.amazonTable = amazonTable;
    }

    public static Set<String> typeSet = Arrays.stream(AmcReportTypeEnum.values()).map(AmcReportTypeEnum::getType).collect(Collectors.toSet());

    public static Map<String, AmcReportTypeEnum> typeMap = Arrays.stream(AmcReportTypeEnum.values())
            .collect((Collectors.groupingBy(AmcReportTypeEnum::getType, Collectors.collectingAndThen(Collectors.toList(), value -> value.get(0)))));

    public String getType() {
        return type;
    }

    public String getBeanName() {
        return beanName;
    }

    public String getAmazonTable() {
        return amazonTable;
    }

    public String getDesc() {
        return desc;
    }
}
