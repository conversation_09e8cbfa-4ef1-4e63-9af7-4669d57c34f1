package com.meiyunji.sponsored.service.newDashboard.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.meiyunji.sponsored.service.newDashboard.dto.base.DashboardAdCalDataDto;
import lombok.Data;

/**
 * @author: nongwenhua
 * @email: <EMAIL>
 * @create: 2024-04-07 14:28
 */
@Data
public class DashboardAdKeywordDataDto extends DashboardAdCalDataDto {
    // 投放关键词
    @ExcelProperty("关键词")
    private String keyword = "";
    // 匹配方式
    @ExcelProperty("匹配方式")
    private String matchType = "";
    // 投放关键词下每种匹配类型对应的数据
    private String detailData = "";

    private String matchTypeCode = "";
}
