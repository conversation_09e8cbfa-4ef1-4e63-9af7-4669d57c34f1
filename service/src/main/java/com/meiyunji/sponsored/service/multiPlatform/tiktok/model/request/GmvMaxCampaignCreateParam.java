package com.meiyunji.sponsored.service.multiPlatform.tiktok.model.request;

import com.meiyunji.sponsored.service.annotation.AllowedValues;
import com.meiyunji.sponsored.service.multiPlatform.tiktok.model.GmvMaxVideoItem;
import com.meiyunji.sponsored.service.multiPlatform.tiktok.model.Identity;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.math.BigDecimal;
import java.util.List;

@Data
public class GmvMaxCampaignCreateParam {

    @NotNull(message = "店铺ID不能为空")
    private Integer shopId;
    @NotNull(message = "广告主ID不能为空")
    private String advertiserId;
    @NotBlank(message = "店铺授权bcId不能为空")
    private String storeAuthorizedBcId;
    @NotNull(message = "广告组名称不能为空")
    private String campaignName;
    @AllowedValues(message = "商品维度参数不正确", allowedValues = {"ALL", "CUSTOMIZED_PRODUCTS"})
    private String productSpecificType;
    @Size(max = 50, message = "最多支持50个spu")
    private List<String> spuIdList;
    private BigDecimal roasBid;
    @NotNull(message = "预算不能为空")
    private BigDecimal dailyBudget;
    @NotBlank(message = "开始时间不能为空")
    private String scheduleStartTime;
    private String scheduleEndTime;
    private String productVideoSpecificType;
    private List<Identity> identityList;
    private Boolean affiliatePostsEnabled;

    //已授权作品
    private List<GmvMaxVideoItem> itemList;
    //自定义作品
    private List<GmvMaxVideoItem> customAnchorVideoList;

}
