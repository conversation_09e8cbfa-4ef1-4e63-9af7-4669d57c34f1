package com.meiyunji.sponsored.service.cpc.dao;

import com.meiyunji.sponsored.common.springjdbc.IBaseShardingDao;
import com.meiyunji.sponsored.service.cpc.bo.AmazonAdOperationLogBO;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdOperationLog;

import java.util.Date;
import java.util.List;
/**
 * @author: liweibin
 * @email: <EMAIL>
 * @date: 2023-07-07  10:39
 */
public interface IAmazonAdOperationLogDao extends IBaseShardingDao<AmazonAdOperationLog> {
    /**
     * 批量插入
     */
    Integer batchInsert(Integer puid, List<AmazonAdOperationLog> amazonAdOperationLogList);

    /**
     * 根据日志类型与ids查询列表
     */
    List<AmazonAdOperationLogBO> listByTypeAndIdentifyIds(Integer puid, Integer shopId, String type, Integer entityType, List<Integer> changeType,
                                                          List<String> identifyIds, Date startDate);



    /**
     * 获取一天的操作日志
     */
    List<AmazonAdOperationLogBO> listByOneDayAll(Integer puid, Integer shopId, Integer entityType, Integer changeType,
                                                 String beginDate, String endDate);
    /**
     * 根据日志类型与ids查询列表（多店铺）
     */
    List<AmazonAdOperationLogBO> listByTypeAndIdentifyIdsAndShopIdList(Integer puid, List<Integer> shopIdList, String type, Integer entityType, List<Integer> changeType,
                                                          List<String> identifyIds, Date startDate);

    /**
     * 根据日志类型与ids查询列表（多店铺多广告类型）
     */
    List<AmazonAdOperationLogBO> listByTypeAndIdentifyIdsAndShopIdList(Integer puid, List<Integer> shopIdList, List<String> typeList, Integer entityType,
                                                                       Integer changeType, List<String> identifyIds, Date startDate);

    int deleteByPuidAndShopId(Integer puid, Integer shopId, Integer limit);
}
