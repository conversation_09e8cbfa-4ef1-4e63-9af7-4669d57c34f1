package com.meiyunji.sponsored.service.strategyTask.enums;

import io.swagger.models.auth.In;
import org.apache.commons.lang3.StringUtils;

/**
 * @author: chenzimeng
 * @email: <EMAIL>
 * @date: 2024-01-09  15:17
 */
public enum TaskItemType {
    CAMPAIGN(0, "CAMPAIGN"),
    CAMPAIGN_PLACEMENT(1, "CAMPAIGN_PLACEMENT"),
    TARGET(2, "TARGET"),
    START_STOP(3, "START_STOP"),
    PORTFOLIO(4, "PORTFOLIO"),
    AD_GROUP_TARGET(5, "AD_GROUP_TARGET");

    private Integer code;
    private String desc;

    TaskItemType(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * 根据code 得到对应的 周（几）数字
     * @param code
     * @return
     */
    public static Integer getCode(String desc) {
        Integer result = null;
        for (TaskItemType taskItemType : TaskItemType.values()) {
            if (StringUtils.equals(taskItemType.getDesc(),desc)) {
                result = taskItemType.getCode();
                break;
            }
        }
        return result;
    }

    /**
     * 根据code 得到对应的 周（几）数字
     * @param code
     * @return
     */
    public static String getDesc(Integer code) {
        String result = null;
        for (TaskItemType taskItemType : TaskItemType.values()) {
            if (taskItemType.getCode().equals(code)) {
                result = taskItemType.getDesc();
                break;
            }
        }
        return result;
    }
}
