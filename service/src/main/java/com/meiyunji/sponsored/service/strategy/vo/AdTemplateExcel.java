package com.meiyunji.sponsored.service.strategy.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.meiyunji.sponsored.service.base.BaseExportVo;
import lombok.Data;

@Data
public class AdTemplateExcel extends BaseExportVo {
    @ExcelProperty("模板名称")
    private String templateName;
    @ExcelProperty("已应用数量")
    private Integer usageAmount;
    @ExcelProperty("策略规则详情(文字信息)")
    private String rule;
    @ExcelProperty("最后修改时间")
    private String lastUpdateTime;
    @ExcelProperty("最后修改人")
    private String lastUpdateName;
    @ExcelProperty("模板创建人")
    private String createName;
    @ExcelProperty("所属店铺")
    private String shopName;
}
