package com.meiyunji.sponsored.service.export.handler;

import com.amazon.advertising.spV3.enumeration.SpV3ExpressionEnum;
import com.meiyunji.amazon.mws.base.AmznEndpoint;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.base.ProcessMsg;
import com.meiyunji.sponsored.common.exception.SponsoredBizException;
import com.meiyunji.sponsored.common.support.StringRedisService;
import com.meiyunji.sponsored.common.util.JSONUtil;
import com.meiyunji.sponsored.service.cpc.constants.BrandMessageConstants;
import com.meiyunji.sponsored.service.cpc.constants.strategy.AdTargetStrategyTypeEnum;
import com.meiyunji.sponsored.service.cpc.dao.IAdManagePageExportTaskDao;
import com.meiyunji.sponsored.service.cpc.po.AdManagePageExportTask;
import com.meiyunji.sponsored.service.cpc.po.AdTag;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.enums.*;
import com.meiyunji.sponsored.service.excel.excelTools.WriteHandlerBuild;
import com.meiyunji.sponsored.service.excel.excelTools.service.IExcelService;
import com.meiyunji.sponsored.service.export.AdManagePageExportTaskHandler;
import com.meiyunji.sponsored.service.export.constants.AdManagePageExportTaskConstant;
import com.meiyunji.sponsored.service.export.util.ExportStringUtil;
import com.meiyunji.sponsored.service.multiple.common.utils.MultipleUtils;
import com.meiyunji.sponsored.service.multiple.targets.dto.TargetExportDto;
import com.meiyunji.sponsored.service.multiple.targets.dto.TargetReqDto;
import com.meiyunji.sponsored.service.multiple.targets.enums.TargetExportFieldEnum;
import com.meiyunji.sponsored.service.multiple.targets.resp.TargetResp;
import com.meiyunji.sponsored.service.multiple.targets.service.AbstractTargetProcessor;
import com.meiyunji.sponsored.service.util.ReportParamUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 多店铺广告活动导出handler
 *
 * @author: zzh
 * @create: 2024-11-21 10:05
 */
@Service(AdManagePageExportTaskConstant.TARGET_MULTIPLE)
@Slf4j
public class TargetMultiplePageExportTaskHandler implements AdManagePageExportTaskHandler {

    @Resource
    private Map<String, AbstractTargetProcessor> abstractTargetProcessorMap;

    @Resource
    private IAdManagePageExportTaskDao adManagePageExportTaskDao;

    @Resource
    private StringRedisService stringRedisService;

    @Resource
    private IExcelService excelService;

    @Override
    public void export(AdManagePageExportTask task) {
        long t = Instant.now().toEpochMilli();
        TargetReqDto req = JSONUtil.jsonToObject(task.getParam(), TargetReqDto.class);
        log.info("export target multiple page taskId:{} params: {}", task.getId(), req);
        try {
            req.setPageNo(1);
            req.setPageSize(Constants.EXPORT_MAX_SIZE);
            AbstractTargetProcessor targetProcessor = abstractTargetProcessorMap.get(req.getTargetTypeEnum().getBeanName());
            Page<TargetResp> page = targetProcessor.getAllTargetData(req, Boolean.TRUE);
            if (CollectionUtils.isEmpty(page.getRows())) {
                // 导出数据为空
                adManagePageExportTaskDao.updateSuccessStatus(task.getId(), null, 100);
                // 修改状态，前端收到后转圈效果停止
                stringRedisService.set(req.getUuid(), new ProcessMsg(-1, 0, "导出数据为空"));
            } else {
                // 获取导出url
                List<String> urlList = getExportUrl(task, req, page, targetProcessor);
                // 修改任务状态
                adManagePageExportTaskDao.updateSuccessStatus(task.getId(), JSONUtil.objectToJson(urlList), 100);
                stringRedisService.set(req.getUuid(), new ProcessMsg(1, urlList.size(), "导出成功", urlList));
                log.info("export target multiple page finish taskId:{} urlList: {}", task.getId(), urlList);
            }
        } catch (Exception e) {
            log.error("target multiple export error, task id : {}", task.getId(), e);
            String msg = "系统异常";
            if (e instanceof SponsoredBizException) {
                msg = ((SponsoredBizException) e).getMsg();
            }
            adManagePageExportTaskDao.updateErrorStatus(task.getId());
            // 修改状态，前端收到后转圈效果停止
            if (req != null) {
                stringRedisService.set(req.getUuid(), new ProcessMsg(-1, 0, msg));
            }
        }
        log.info("export target multiple page finish taskId:{} time: {}", task.getId(), (Instant.now().toEpochMilli() - t));
    }

    /**
     * 获取导出url
     */
    private List<String> getExportUrl(AdManagePageExportTask task, TargetReqDto req, Page<TargetResp> page, AbstractTargetProcessor targetProcessor) throws Exception {
        List<String> urlList = new ArrayList<>();

        // 文件名称
        String fileName = MultipleUtils.getExportShopName(req.getShopAuthList()) + "_" + req.getTargetTypeEnum().getDesc() + "_" + req.getStartDate() + "_" + req.getEndDate();
        // 获取过滤字段
        List<String> excludeFiledList = targetProcessor.excludeFiledList(req);
        List<TargetExportDto> adverList = new LinkedList<>();
        for (TargetResp resp : page.getRows()) {
            adverList.add(this.buildExportVo(resp));
        }
        if (StringUtils.isNotBlank(req.getExportSortField())) {
            // 自定义导出
            customColumnExport(task, req, adverList, urlList, fileName, excludeFiledList);
        } else {
            WriteHandlerBuild build = new WriteHandlerBuild().rate().currencyNew(TargetExportDto.class);
            urlList.add(excelService.easyExcelHandlerExport(task.getPuid(), adverList, fileName, TargetExportDto.class, build, excludeFiledList));
        }
        return urlList;
    }

    /**
     * 自定义导出
     */
    private void customColumnExport(AdManagePageExportTask task,  TargetReqDto req, List<TargetExportDto> adverList, List<String> urlList, String fileName, List<String> excludeFileds) throws IllegalAccessException, NoSuchFieldException {
        // Excel样式builder
        WriteHandlerBuild build = new WriteHandlerBuild().rate();
        if (req.getFreezeNum() != null) {
            // 冻结前n列前1行
            build.freezeRowAndCol(req.getFreezeNum(), 1);
        }
        List<String> sortFields = new ArrayList<>(Arrays.asList(req.getExportSortField().split(",")));
        sortFields = sortFields.stream().filter(item -> !excludeFileds.contains(item)).collect(Collectors.toList());
        // 默认导出广告策略标签
        sortFields.add("adStrategyTag");
        List<String> headNames = new ArrayList<>(sortFields.size());
        List<Integer> currencyIndex = new ArrayList<>();
        for (int i = 0; i < sortFields.size(); i++) {
            TargetExportFieldEnum fieldEnum = TargetExportFieldEnum.fromPoParamKey(sortFields.get(i));
            if (fieldEnum != null && fieldEnum.isCurrencyStyle()) {
                currencyIndex.add(i);
            }
            if (fieldEnum != null) {
                headNames.add(fieldEnum.getTableColName());
            }
        }
        // 非对象导出货币样式
        build.noModleHandler(currencyIndex);
        List<List<Object>> rows = new ArrayList<>(adverList.size());
        Class<TargetExportDto> aClass = TargetExportDto.class;
        for (TargetExportDto vo : adverList) {
            List<Object> cols = new ArrayList<>(TargetExportFieldEnum.values().length);
            for (String sortField : sortFields) {
                TargetExportFieldEnum fieldEnum = TargetExportFieldEnum.fromPoParamKey(sortField);
                if (fieldEnum == null) {
                    throw new SponsoredBizException("自定义导出包含非法字段！");
                }
                // 通过反射获取对应字段值
                Field field = aClass.getDeclaredField(fieldEnum.getVoName());
                field.setAccessible(Boolean.TRUE);
                cols.add(field.get(vo));
            }
            rows.add(cols);
        }
        urlList.add(excelService.exportByCustomColSort(task.getPuid(), headNames, rows, fileName, build));
    }

    /**
     * 列表vo转导出vo
     */
    private TargetExportDto buildExportVo(TargetResp resp) {

        // 获取货币符号
        String currency = AmznEndpoint.getByMarketplaceId(resp.getMarketplaceId()).getCurrencyCode().value();
        String icon = Objects.requireNonNull(UserCurrencyType.getByCode(currency)).icon();
        // 处理数据
        TargetExportDto export = new TargetExportDto();
        // 设置定位类型
        setTarget(resp, export, icon);
        export.setShopName(resp.getShopName());
        export.setKeywordText(resp.getKeywordText());
        export.setKeywordTextCn(resp.getKeywordTextCn());
        export.setMatchType(SbMatchValueEnum.getMatchValue(resp.getMatchType()));
        export.setState(AllAdStateEnum.getStateValue(resp.getState()));
        export.setServingStatusName(resp.getServingStatusName());
        // 广告组合
        export.setPortfolioName(resp.getPortfolioName());
        //所属广告活动
        export.setAdvertisingActivities(resp.getCampaignName());
        //所属广告组
        export.setAdvertisingGroup(resp.getAdGroupName());
        //建议竞价
        if (StringUtils.isNotBlank(resp.getSuggestBid())) {
            export.setSuggestBid(ExportStringUtil.getSuggest(resp.getSuggestBid(), icon));
        }
        if (StringUtils.isNotBlank(resp.getRangeStart()) && StringUtils.isNotBlank(resp.getRangeEnd())) {
            //建议竞价范围
            export.setSuggestBidScope(ExportStringUtil.getSuggestBidScope(resp.getRangeStart(), resp.getRangeEnd(), icon));
        }
        //竞价
        export.setBid(resp.getCurrency() + ExportStringUtil.formatToNumber(resp.getBid()));
        export.setImpressions(resp.getImpressions());
        export.setClicks(resp.getClicks());
        export.setTopImpressionShare(ReportParamUtil.getExportTopIS(resp.getTopImpressionShare()));
        export.setAdOrderNum(resp.getAdOrderNum());

        //点击率
        export.setCtr(ExportStringUtil.modifyFormat(resp.getCtr()));
        //订单转化率
        export.setCvr(ExportStringUtil.modifyFormat(resp.getCvr()));
        //ACoS
        export.setAcos(ExportStringUtil.modifyFormat(resp.getAcos()));
        //ACoTS
        export.setAcots(ExportStringUtil.modifyFormat(resp.getAcots()));
        export.setRoas(resp.getRoas());
        //ASoTS
        export.setAsots(ExportStringUtil.modifyFormat(resp.getAsots()));
        //广告花费
        export.setAdCost(resp.getCurrency() + ExportStringUtil.formatToNumber(resp.getAdCost()));
        //平均点击费用(特殊处理)
        export.setAdCostPerClick(resp.getCurrency() + ExportStringUtil.getAdCostPerClick(resp.getAdCostPerClick()));
        //广告销售额
        export.setAdSale(resp.getCurrency() + ExportStringUtil.getAdCostPerClick(resp.getAdSale()));

        //推广类型
        export.setCampaignTargetingType(TargetingEnum.getTargetingValue(resp.getCampaignTargetingType()));
        //投放类型
        export.setType(CampaignTypeEnum.getCampaignValue(resp.getAdType()));
        export.setCpa(resp.getCurrency() + ExportStringUtil.formatToNumber(resp.getCpa()));
        export.setVcpm("-".equals(resp.getVcpm()) ? "-" : resp.getCurrency() + ExportStringUtil.formatToNumber(resp.getVcpm()));
        export.setAdSaleNum(resp.getAdSaleNum());
        export.setAdOtherOrderNum(resp.getAdOtherOrderNum());
        export.setAdSales(resp.getCurrency() + ExportStringUtil.formatToNumber(resp.getAdSales()));
        export.setAdOtherSales(resp.getCurrency() + ExportStringUtil.formatToNumber(resp.getAdOtherSales()));
        export.setOrderNum(resp.getOrderNum());
        export.setAdSelfSaleNum(resp.getAdSelfSaleNum());
        export.setAdOtherSaleNum(resp.getAdOtherSaleNum());
        export.setOrdersNewToBrandFTD(resp.getOrdersNewToBrandFTD());
        export.setOrderRateNewToBrandFTD(ExportStringUtil.modifyFormat(resp.getOrderRateNewToBrandFTD()));
        export.setSalesNewToBrandFTD(resp.getCurrency() + ExportStringUtil.formatToNumber(resp.getSalesNewToBrandFTD()));
        export.setSalesRateNewToBrandFTD(ExportStringUtil.modifyFormat(resp.getSalesRateNewToBrandFTD()));
        export.setOrdersNewToBrandPercentageFTD(ExportStringUtil.modifyFormat(resp.getOrdersNewToBrandPercentageFTD()));
        // 花费占比
        export.setAdCostPercentage("-".equals(resp.getAdCostPercentage()) ? "-" : ExportStringUtil.modifyFormat(resp.getAdCostPercentage()));
        // 销售额占比
        export.setAdSalePercentage("-".equals(resp.getAdSalePercentage()) ? "-" : ExportStringUtil.modifyFormat(resp.getAdSalePercentage()));
        // 订单量占比
        export.setAdOrderNumPercentage(ExportStringUtil.modifyFormat(resp.getAdOrderNumPercentage()));
        // 销量占比
        export.setOrderNumPercentage(ExportStringUtil.modifyFormat(resp.getOrderNumPercentage()));
        export.setNewToBrandDetailPageViews(resp.getNewToBrandDetailPageViews());
        export.setAddToCart(resp.getAddToCart());
        export.setAddToCartRate(ExportStringUtil.modifyFormat(resp.getAddToCartRate()));
        export.setEcpAddToCart(resp.getCurrency() + ExportStringUtil.formatToNumber(resp.getECPAddToCart()));
        export.setVideo5SecondViews(resp.getVideo5SecondViews());
        export.setVideo5SecondViewRate(ExportStringUtil.modifyFormat(resp.getVideo5SecondViewRate()));
        export.setVideoFirstQuartileViews(resp.getVideoFirstQuartileViews());
        export.setVideoMidpointViews(resp.getVideoMidpointViews());
        export.setVideoThirdQuartileViews(resp.getVideoThirdQuartileViews());
        export.setVideoCompleteViews(resp.getVideoCompleteViews());
        export.setVideoUnmutes(resp.getVideoUnmutes());
        export.setViewImpressions(resp.getViewImpressions());
        export.setViewabilityRate(ExportStringUtil.modifyFormat(resp.getViewabilityRate()));
        export.setViewClickThroughRate(ExportStringUtil.modifyFormat(resp.getViewClickThroughRate()));
        export.setBrandedSearches(resp.getBrandedSearches());
        export.setDetailPageViews(resp.getDetailPageViews());
        export.setAdvertisingUnitPrice(resp.getCurrency() + ExportStringUtil.formatToNumber(resp.getAdvertisingUnitPrice()));

        if (resp.getSearchFrequencyRank() != null && resp.getSearchFrequencyRank() > 0) {
            export.setSearchFrequencyRank(String.valueOf(resp.getSearchFrequencyRank()));
            export.setWeekRatio(ExportStringUtil.modifyFormat(resp.getWeekRatio()));
        } else {
            export.setSearchFrequencyRank("-");
            export.setWeekRatio("-");
        }
        //处理标签，取出标签进行导出
        if (CollectionUtils.isNotEmpty(resp.getAdTags())) {
            List<String> adTag = resp.getAdTags().stream().map(AdTag::getName).collect(Collectors.toList());
            String adTagName = "";
            if (CollectionUtils.isNotEmpty(adTag) && adTag.size() > 1) {
                adTagName = String.join(",", adTag);
                export.setAdTag(adTagName);
            } else {
                adTagName = String.join("", adTag);
                export.setAdTag(adTagName);
            }
        } else {
            export.setAdTag("");
        }
        // 处理广告策略标签，取出标签名称进行导出
        if (CollectionUtils.isNotEmpty(resp.getAdStrategys())) {
            List<String> adStrategyTag = resp.getAdStrategys().stream().map(it-> AdTargetStrategyTypeEnum.getEnumByCode(it.getAdStrategyType()).getDesc()).collect(Collectors.toList());
            export.setAdStrategyTag(String.join(",", adStrategyTag));
        }else{
            export.setAdStrategyTag("");
        }
        return export;
    }


    public void setTarget(TargetResp resp, TargetExportDto export, String icon){
        //  asin 或 类目
        if (StringUtils.isNotEmpty(resp.getAsin()) || StringUtils.isNotEmpty(resp.getCategory())) {
            if (StringUtils.isNotEmpty(resp.getAsin())) {
                export.setTarget("ASIN：" + resp.getAsin());
            } else if (StringUtils.isNotEmpty(resp.getCategory())) {
                export.setTarget("类目：" + resp.getCategory());
            }
        } else {
            export.setTarget(resp.getTargetText());
        }

        if (StringUtils.isNotBlank(resp.getSelectType())) {
            if (SpV3ExpressionEnum.asinSameAs.getValue().equals(resp.getSelectType()) || SpV3ExpressionEnum.asinSameAs.getValueV3().equals(resp.getSelectType())) {
                export.setSelectType("精准");
            } else if (SpV3ExpressionEnum.asinExpandedFrom.getValue().equals(resp.getSelectType()) || SpV3ExpressionEnum.asinExpandedFrom.getValueV3().equals(resp.getSelectType())) {
                export.setSelectType("扩展");
            } else {
                export.setSelectType(resp.getSelectType());
            }
        }

        // 自动
        if (TargetingEnum.auto.getTargetingType().equalsIgnoreCase(resp.getProductTargetType())) {
            export.setTarget(AutoTargetTypeEnum.getAutoTargetValue(resp.getTargetText()));
        }

        // 受众投放导出时需要拼接title和targetText
        if (TargetingEnum.audience.getTargetingType().equals(resp.getProductTargetType()) && StringUtils.isNotBlank(export.getTarget())) {
            if (StringUtils.isNotBlank(resp.getTitle())) {
                export.setTarget(resp.getTitle().concat(" ").concat(export.getTarget()));
            }
            if (StringUtils.isNotBlank(resp.getLookback())) {
                export.setTarget(export.getTarget().concat(" 回溯期：").concat(resp.getLookback()).concat("天"));
            }
        }

        if (StringUtils.isNotEmpty(resp.getCategory())) {
            StringBuilder sb = new StringBuilder();
            if (StringUtils.isNotBlank(resp.getBrandName())) {
                sb.append(" 品牌：").append(resp.getBrandName());
            }
            if (StringUtils.isNotBlank(resp.getCommodityPriceRange())) {
                String range = resp.getCommodityPriceRange();
                if (BrandMessageConstants.DEFAULT_COMMODITY_PRICE_RANGE.equals(range)) {
                    sb.append(" 价格：").append(range);
                } else {
                    String[] ranges = resp.getCommodityPriceRange().split(",");
                    range = ranges.length == 2 ? ExportStringUtil.getSuggestBidScope(ranges[0], ranges[1], icon) : ExportStringUtil.getSuggest(range, icon);
                    sb.append(" 价格：").append(range);
                }
            }
            if (StringUtils.isNotBlank(resp.getRating())) {
                sb.append(" 星级：").append(resp.getRating());
            }
            if (StringUtils.isNotBlank(resp.getDistribution()) && !"sb".equals(resp.getAdType())) {
                sb.append(" 配送：").append(resp.getDistribution());
            }
            export.setTarget(export.getTarget().concat(sb.toString()));
        }
    }
}
