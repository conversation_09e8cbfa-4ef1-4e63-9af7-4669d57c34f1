package com.meiyunji.sponsored.service.strategy.vo;

import lombok.Data;

import java.time.LocalDateTime;

@Data
public class PeriodCampaignSpaceVo {
    private String type;
    private Integer day;
    private Integer start;
    private Integer end;
    private String newAdPlaceTopValue;
    private String newAdPlaceProductValue;
    private String newStrategy; //竞价策略
    private String siteDateTime; //站点时间
    private String BeijingDateTime; //北京时间
    private LocalDateTime siteDate; //站点时间
    private String newAdOtherValue;
}
