package com.meiyunji.sponsored.service.multiPlatform.walmart.dto;

import com.meiyunji.sponsored.common.springjdbc.DbColumn;
import com.meiyunji.sponsored.service.multiPlatform.walmart.po.IBaseWalmartAdReportDetail;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @author: ys
 * @date: 2025/2/24 15:54
 * @describe:
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class WalmartAdCampaignPageDTO extends WalmartAdReportBaseDTO {
    private Long id;

    /**
     * 商户uid
     */
    private Integer puid;

    /**
     * 店铺ID
     */
    @DbColumn(value = "shop_id")
    private Long shopId;

    private String shopName;

    /**
     * 广告主id
     */
    @DbColumn(value = "advertiser_id")
    private String advertiserId;

    /**
     * 活动id
     */
    @DbColumn(value = "campaign_id")
    private String campaignId;

    @DbColumn(value = "marketplace_code")
    private String marketplaceId;

    /**
     * 活动名称
     */
    @DbColumn(value = "name")
    private String campaignName;

    /**
     * 活动类型sponsoredProducts、sba
     */
    @DbColumn(value = "campaign_type")
    private String adType;

    /**
     * 定向策略manual手动 auto自动
     */
    @DbColumn(value = "targeting_type")
    private String targetingType;

    /**
     * enabled激活,Scheduled预定,Rescheduled改期,Live现场,paused暂停,completed已完成,extend延伸,proposal建议
     * enable 进行中 Shceduled=未开始 rescheduled=重新启动 live=有效 paused=已暂停 completed=已完成 proposal=草稿
     */
    @DbColumn(value = "status")
    private String state;

    /**
     * 开始日期
     */
    @DbColumn(value = "start_date")
    private Date startDateSource;

    private String startDate;

    /**
     * 结束时间
     */
    @DbColumn(value = "end_date")
    private Date endDateSource;

    private String endDate;


    /**
     * 总预算
     */
    @DbColumn(value = "total_budget")
    private BigDecimal totalBudget;

    /**
     * 每日预算
     */
    @DbColumn(value = "daily_budget")
    private BigDecimal dailyBudget;

    /**
     * 活动选择的预算分配类型daily每日，total共计，both两者
     */
    @DbColumn(value = "budget_type")
    private String budgetType;

    /**
     * 该指标建议前一天未使用的每日预算是否应结转到第二天的每日预算
     */
    private Integer rollover;

    /**
     * 竞价策略，DYNAMIC动态策略，FIXED固定策略，TROAS表示投标策略为目标ROAS
     */
    @DbColumn(value = "bidding_strategy")
    private String biddingStrategy;

    /**
     * 创建活动时的活动设置选项列表
     * ["BRAND_TERM_OPT_OUT"]
     */
    @DbColumn(value = "campaign_options")
    private String campaignOptions;
    //是否可以修改开始日期 0 不可修改; 1:可修改
    private int fixStartDate;

}
