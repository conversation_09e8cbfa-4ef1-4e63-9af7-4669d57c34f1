package com.meiyunji.sponsored.service.multiPlatform.shop.enums;

public enum TikTokMarketplace {
    US("US",  MultiPlatformMarketplaceEnum.US, "美国"),
    UK("UK",  MultiPlatformMarketplaceEnum.UK, "英国"),
    GB("GB",  MultiPlatformMarketplaceEnum.UK, "英国"),
    ID("ID",  MultiPlatformMarketplaceEnum.ID, "印度尼西亚"),
    SG("SG",  MultiPlatformMarketplaceEnum.SG, "新加坡"),
    VN("VN",  MultiPlatformMarketplaceEnum.VN, "越南"),
    TH("TH",  MultiPlatformMarketplaceEnum.TH, "泰国"),
    MY("MY",  MultiPlatformMarketplaceEnum.MY, "马来西亚"),
    PH("PH",  MultiPlatformMarketplaceEnum.PH, "菲律宾"),
    ;
    private String code;
    private MultiPlatformMarketplaceEnum multiPlatformMarketplace;
    private String desc;

    public static TikTokMarketplace getByCode(String code) {
        for (TikTokMarketplace value : TikTokMarketplace.values()) {
            if (value.code.equalsIgnoreCase(code)) {
                return value;
            }
        }
        return null;
    }

    TikTokMarketplace(String code, MultiPlatformMarketplaceEnum multiPlatformMarketplaceEnum, String desc) {
        this.code = code;
        this.multiPlatformMarketplace = multiPlatformMarketplaceEnum;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public MultiPlatformMarketplaceEnum getMultiPlatformMarketplace() {
        return multiPlatformMarketplace;
    }

    public String getDesc() {
        return desc;
    }
}
