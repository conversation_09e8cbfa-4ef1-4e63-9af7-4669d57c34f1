package com.meiyunji.sponsored.service.doris.po;

import com.meiyunji.sponsored.common.springjdbc.DbColumn;
import com.meiyunji.sponsored.common.springjdbc.DbTable;
import com.meiyunji.sponsored.service.annotation.AdLogFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * amazon广告关键词表(OdsAmazonAdNeKeyword)实体类
 *
 * <AUTHOR>
 * @since 2024-03-26 10:46:18
 */
@Data
@DbTable("ods_t_amazon_ad_ne_keyword")
public class OdsAmazonAdNeKeyword implements Serializable {
	/**
	 * md5(puid,shop_id,market_place_id,campaign_id,dxm_group_id,keyword_text,type)
	 */
	@DbColumn(value = "unique_key")
	private String uniqueKey;
	/**
	 * 商户uid
	 */
	@DbColumn(value = "puid")
	private Integer puid;
	/**
	 * 店铺ID
	 */
	@DbColumn(value = "shop_id")
	private Integer shopId;
	/**
	 * 站点
	 */
	@DbColumn(value = "marketplace_id")
	private String marketplaceId;
	/**
	 * 关键词id
	 */
	@DbColumn(value = "keyword_id")
	private String keywordId;
	/**
	 * 广告组id
	 */
	@DbColumn(value = "ad_group_id")
	private String adGroupId;
	/**
	 * dxm维护的广告组id
	 */
	@DbColumn(value = "dxm_group_id")
	private Long dxmGroupId;
	/**
	 * 活动id
	 */
	@DbColumn(value = "campaign_id")
	private String campaignId;
	/**
	 * 配置ID
	 */
	@DbColumn(value = "profile_id")
	private String profileId;
	/**
	 * 关键词
	 */
	@DbColumn(value = "keyword_text")
	private String keywordText;
	/**
	 * 匹配类型(broad，phrase，exact)，否定关键词(negativeExact，negativePhrase)
	 */
	@DbColumn(value = "match_type")
	private String matchType;
	/**
	 * 竞价(否定关键词没有)
	 */
	@DbColumn(value = "bid")
	@AdLogFormat(value = "竞价", methodStr = "getBid")
	private Double bid;
	/**
	 * biddable竞价关键词,negative否定关键词
	 */
	@DbColumn(value = "type")
	private String type;
	/**
	 * 广告组状态（enabled，paused，archived）
	 */
	@DbColumn(value = "state")
	@AdLogFormat(value = "状态", methodStr = "covertChState")
	private String state;

	/**
	 * 活动的具体状态,例:CAMPAIGN_ARCHIVED
	 */
	@DbColumn(value = "serving_status")
	private String servingStatus;
	/**
	 * 竞价建议范围最小值，用于刚进编辑页面展示
	 */
	@DbColumn(value = "range_start")
	private Double rangeStart;
	/**
	 * 竞价建议范围最大值，用于刚进编辑页面展示
	 */
	@DbColumn(value = "range_end")
	private Double rangeEnd;
	/**
	 * 竞价建议值，用于刚进编辑页面展示
	 */
	@DbColumn(value = "suggested")
	private Double suggested;
	/**
	 * 创建人id
	 */
	@DbColumn(value = "create_id")
	private Integer createId;
	/**
	 * 修改人id
	 */
	@DbColumn(value = "update_id")
	private Integer updateId;

	@DbColumn(value = "is_pricing")
	private Integer isPricing;
	@DbColumn(value = "pricing_state")
	private Integer pricingState;
	/**
	 * 关键词广告排名
	 */
	@DbColumn(value = "adv_rank")
	private String advRank;
	/**
	 * 报告数据最新更新时间 yyyy-MM-dd
	 */
	@DbColumn(value = "data_update_time")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	private LocalDate dataUpdateTime;

	/**
	 * 创建时间
	 */
	@DbColumn(value = "create_time")
	private Date createTime;

	/**
	 * 更新的时间
	 */
	@DbColumn(value = "update_time")
	private Date updateTime;

	/**
	 * 平台创建时间
	 */
	@DbColumn(value = "creation_date")
	private LocalDateTime creationDate;

	/**
	 * 平台的创建时间前30天
	 */
	@DbColumn(value = "creation_before_date")
	private LocalDate creationBeforeDate;

	/**
	 * 平台的创建时间后30天
	 */
	@DbColumn(value = "creation_after_date")
	private LocalDate creationAfterDate;


	/**
	 * 平台创建站点时间
	 */
	@DbColumn(value = "amazon_create_time")
	private LocalDateTime amazonCreateTime;

}

