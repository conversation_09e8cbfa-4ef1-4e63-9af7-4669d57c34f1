package com.meiyunji.sponsored.service.doris.dao;

import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.springjdbc.IDorisBaseDao;
import com.meiyunji.sponsored.service.cpc.bo.AdKeywordOrderBo;
import com.meiyunji.sponsored.service.cpc.vo.*;
import com.meiyunji.sponsored.service.cpc.vo.chartVo.AdHomePerformancedto;
import com.meiyunji.sponsored.service.doris.po.OdsAmazonAdKeywordReport;
import com.meiyunji.sponsored.service.newDashboard.dto.DashboardAdKeywordDataDto;
import com.meiyunji.sponsored.service.newDashboard.dto.DashboardAdTargetingMatrixTopDto;
import com.meiyunji.sponsored.service.newDashboard.dto.DashboardLongTailWordDataDto;
import com.meiyunji.sponsored.service.newDashboard.enums.DashboardDataFieldEnum;
import com.meiyunji.sponsored.service.newDashboard.enums.DashboardOrderByEnum;
import com.meiyunji.sponsored.service.newDashboard.vo.DashboardBaseReqVo;
import com.meiyunji.sponsored.service.wordFrequency.qo.KeywordTopQo;
import com.meiyunji.sponsored.service.wordFrequency.vo.WordRootTopVo;

import java.util.List;

/**
 * amazon关键词报告表(OdsAmazonAdKeywordReport)数据库访问层
 *
 * <AUTHOR>
 * @since 2024-03-26 10:46:18
 */
public interface IOdsAmazonAdKeywordReportDao extends IDorisBaseDao<OdsAmazonAdKeywordReport> {
    

    /**
     * 关键词投放列表页
     */
    Page<KeywordPageVo> getKeywordPage(Integer puid, KeywordsPageParam param);

    int getKeywordAllCount(Integer puid, KeywordsPageParam param);

    /**
     * 获取某词根下关键词列表页的所有keywordId;
     */
    List<String> getKeywordPageIdList(Integer puid, KeywordsPageParam param);

    /**
     * 关键词投放列表页所有id
     */
    List<WordRootTopVo> getWordRootToplist(Integer puid, KeywordTopQo qo);

    /**
     * 根据keywordIds查询时间内的报告汇总数据
     */
    List<KeywordPageVo> getReportListByKeywordIds(Integer puid, Integer shopId, List<String> keywordIds, String startStr, String endStr);

    /**
     * 关键词投放列表页占比数据
     */
    AdMetricDto getKeywordPageSumMetricData(Integer puid, KeywordsPageParam param);

    /**
     * 关键词投放汇总查询所有keywordId（join只查出有报告数据的id）
     */
    List<String> getKeywordIdListByPage(Integer puid, String startStr, String endStr, KeywordsPageParam param);

    /**
     * 关键词库已投放弹窗关键词Id查询
     * @param puid
     * @param startDate
     * @param endDate
     * @param param
     */
    Page<KeywordLibsDetailVo> getKeywordIdListByPage(Integer puid, String startDate, String endDate, KeywordLibsPageParam param);

    /**
     * 关键词投放汇总根据keywordId查询按天维度数据
     */
    List<AdHomePerformancedto> getReportAggregateByKeywordIdList(Integer puid, Integer shopId, String startStr, String endStr,
                                                                 KeywordsPageParam param, boolean isGroupByDate);

    AdHomePerformancedto getReportAggregateCompareDataByKeywordIdList(Integer puid, KeywordsPageParam param, String compareStartData, String compareEndData);

    /**
     * 广告看板查询投放关键词数据
     *
     * @param puid
     * @param marketplaceIdList
     * @param shopIdList
     * @param currency
     * @param startDate
     * @param endDate
     * @param orderByField
     * @param orderBy
     * @param limit
     * @return
     */
    List<DashboardAdKeywordDataDto> queryAdKeywordCharts(Integer puid,
                                                         List<String> marketplaceIdList,
                                                         List<Integer> shopIdList,
                                                         String currency,
                                                         String startDate,
                                                         String endDate,
                                                         String orderByField,
                                                         String orderBy,
                                                         Integer limit, List<String> siteToday, Boolean isSiteToday, List<String> portfolioIds,
                                                         List<String> campaignIds, Boolean noZero, DashboardDataFieldEnum dashboardDataFieldEnum);
    List<DashboardAdTargetingMatrixTopDto> getKeywordTopList(Integer puid, List<String> marketplaceIdList,
                                                             List<Integer> shopIdList, String currency,
                                                             String startDate, String endDate,
                                                             DashboardDataFieldEnum dataField, List<String> keywordIdList,
                                                             Integer limit, DashboardOrderByEnum orderBy, List<String> siteToday,
                                                             Boolean isSiteToday, List<String> portfolioIds, List<String> campaignIds, Boolean noZero);

    List<DashboardAdTargetingMatrixTopDto> getKeywordTopInfoList(Integer puid, List<String> marketplaceIdList,
                                                             List<Integer> shopIdList, String currency,
                                                             String startDate, String endDate,
                                                             DashboardDataFieldEnum dataField, List<String> keywordIdList,
                                                                 DashboardOrderByEnum orderBy, List<String> siteToday, Boolean isSiteToday,
                                                                 List<String> portfolioIds, List<String> campaignIds, Boolean noZero);

    /**
     * 关键词库列表页报告数据-SP
     *
     * @param puid
     * @param shopIds
     * @param param
     * @param keywordTexts
     * @return
     */
    List<AdKeywordOrderBo> getSpKeywordTexts(Integer puid, List<String> shopIds, KeywordLibsPageParam param, List<String> keywordTexts);

    /**
     * 关键词库列表页-报告数据查询-SP
     *
     * @param puid
     * @param shopIds
     * @param param
     * @param keywordTexts
     * @return
     */
    List<KeywordLibsVo> getSpKeywordReportData(Integer puid, List<String> shopIds, KeywordLibsPageParam param, List<String> keywordTexts);

    List<KeywordLibsDetailVo> getSpKeywordReportData(Integer puid, KeywordLibsPageParam param, List<String> keywordIds);

    /**
     * 查询汇总数据
     * @param puid
     * @param param
     * @return
     */
    List<KeywordLibsTotalVo> getKeywordTotalData(Integer puid, KeywordLibsPageParam param);

    List<KeywordLibsVo> getSpCompKeywordReportData(Integer puid, List<String> shopIdList, KeywordLibsPageParam param, List<String> keywords);

    /**
     * 查询sp关键词投放的报告数据
     * 无排序
     * @param puid
     * @param vo
     * @param spKeywordList
     * @return
     */
    List<RepeatTargetingCountVo> getSpKeywordReportData(Integer puid, RepeatTargetingCountPageVo vo, List<RepeatTargetingCountVo> spKeywordList);

    /**
     * 查询sp关键词投放的环比报告数据
     *
     * @param puid
     * @param vo
     * @param spKeywordList
     * @return
     */
    List<RepeatTargetingCountVo> getSpCompKeywordReportData(Integer puid, RepeatTargetingCountPageVo vo, List<RepeatTargetingCountVo> spKeywordList);

    /**
     * 查询sp关键词投放的报告数据
     * 指标值排序
     * @param puid
     * @param vo
     * @return
     */
    Page<RepeatTargetingCountVo> getSpKeywordReportDataOrderByIndex(Integer puid, RepeatTargetingCountPageVo vo);

    /**
     * 查询sp关键词投放的环比数据
     * 指标值排序
     * @param puid
     * @param vo
     * @param spKeywordIdList
     * @return
     */
    List<RepeatTargetingCountVo> getSpCompKeywordReportDataOrderByIndex(Integer puid, RepeatTargetingCountPageVo vo, List<RepeatTargetingCountVo> spKeywordIdList);

    /**
     * 查询sp关键词投放的报告数据
     *
     * @param puid
     * @param detailPageVo
     * @param keywordIds
     * @return
     */
    List<RepeatTargetingDetailVo> getSpReportData(Integer puid, RepeatTargetingDetailPageVo detailPageVo, List<String> keywordIds);

    /**
     * 查询sp关键词投放的环比数据
     *
     * @param puid
     * @param detailPageVo
     * @param keywordIds
     * @return
     */
    List<RepeatTargetingDetailVo> getSpCompReportData(Integer puid, RepeatTargetingDetailPageVo detailPageVo, List<String> keywordIds);

    /**
     * 查询关键词投放的需要排序的指标数据
     * @param puid
     * @param detailPageVo
     * @param keywordIds
     * @return
     */
    Page<RepeatTargetingDetailVo> getIndexReportData(Integer puid, RepeatTargetingDetailPageVo detailPageVo, List<String> keywordIds);

    /**
     *  重复投放-详情列表页汇总数据
     * @param puid
     * @param totalDataVo
     * @param keywordIds
     * @return
     */
    List<RepeatTargetingTotalVo> getRepeatTargetingReportTotalData(Integer puid, RepeatTargetingDetailPageVo totalDataVo, List<String> keywordIds);

    /**
     * 全景仪-长尾词面板sql
     * @param reqVo 请求参数
     * @return 响应参数
     */
    List<DashboardLongTailWordDataDto> queryLongTailWordData(DashboardBaseReqVo reqVo);

    List<OdsAmazonAdKeywordReport> getSumReportByKeywordIdsByCountDate(int puid, List<Integer> shopIds, List<String> marketplaceIds, String startStr, String endStr,
                                                                       List<String> keywordIds, boolean changeRate, String currency);
}

