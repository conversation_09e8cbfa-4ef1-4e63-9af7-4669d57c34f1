package com.meiyunji.sponsored.service.cpc.service.impl;

import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.springjdbc.BaseShardingServiceImpl;
import com.meiyunji.sponsored.common.util.ResultUtil;
import com.meiyunji.sponsored.rpc.adtag.UpdateTagSortItemRequest;
import com.meiyunji.sponsored.rpc.adtag.UpdateTagSortRequest;
import com.meiyunji.sponsored.service.cpc.dao.*;
import com.meiyunji.sponsored.service.cpc.dto.AdTagRelationIdDTO;
import com.meiyunji.sponsored.service.cpc.po.AdKeywordLibMarkupTag;
import com.meiyunji.sponsored.service.cpc.po.AdKeywordLibTag;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdAsinsLib;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdKeywordsLib;
import com.meiyunji.sponsored.service.cpc.service.IAdKeywordLibTagService;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.vo.*;
import com.meiyunji.sponsored.service.enums.AdTagQueryTypeEnum;
import com.meiyunji.sponsored.service.enums.AdTagTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class AdKeywordLibTagServiceImpl extends BaseShardingServiceImpl<AdKeywordLibTag> implements IAdKeywordLibTagService {
    @Autowired
    private IAdKeywordLibTagDao adKeywordLibTagDao;
    @Autowired
    private IAdKeywordLibMarkupTagDao adKeywordLibMarkupTagDao;
    @Autowired
    private IAmazonAdKeywordsLibDao amazonAdKeywordLibDao;
    @Autowired
    private IAmazonAdAsinsLibDao amazonAdAsinsLibDao;

    @Override
    public Result deleteByAdTagId(int puid, int uid, String loginIp, Long adTagId) {
        AdKeywordLibTag adKeywordLibTag = adKeywordLibTagDao.getByUidAndId(puid, uid, adTagId);
        if (adKeywordLibTag == null) {
            return ResultUtil.error("标签不存在");
        }
        adKeywordLibMarkupTagDao.deleteByTagId(puid, uid, adTagId);
        adKeywordLibTagDao.deleteByUidAndId(puid, uid, adTagId);
        return ResultUtil.success();
    }

    @Override
    public Result createAdTag(int puid, String loginIp, AdTagVo adTagVo) {
        int uid = adTagVo.getCommonTag() ? 0 : adTagVo.getUid();
        boolean exist = adKeywordLibTagDao.exist(puid, uid, adTagVo.getType(), adTagVo.getName(), null);
        if (exist) {
            return ResultUtil.error("标签名称重复");
        }
        Integer listCountByType = adKeywordLibTagDao.getListCountByType(puid, uid, adTagVo.getType());
        if (listCountByType >= 50) {
            return ResultUtil.error("标签数量不能超过50个");
        }
        AdKeywordLibTag adTag = new AdKeywordLibTag();
        adTag.setPuid(adTagVo.getPuid());
        adTag.setUid(uid);
        adTag.setCreateId(adTagVo.getUid());
        adTag.setColor(adTagVo.getColor());
        adTag.setName(adTagVo.getName());
        adTag.setType(adTagVo.getType());
        adTag.setTagSort(adKeywordLibTagDao.getMaxTagSort(puid, uid, adTagVo.getType()) + 1);
        adKeywordLibTagDao.insert(adTag);
        return ResultUtil.success();
    }

    @Override
    public Result updateAdTag(int puid, String loginIp, AdTagVo adTagVo) {
        int uid = adTagVo.getCommonTag() ? 0 : adTagVo.getUid();
        AdKeywordLibTag adTag = new AdKeywordLibTag();
        adTag.setId(adTagVo.getId());
        adTag.setPuid(adTagVo.getPuid());
        adTag.setUid(uid);
        adTag.setUpdateId(adTagVo.getUid());
        adTag.setColor(adTagVo.getColor());
        adTag.setName(adTagVo.getName());
        AdKeywordLibTag byPuidAndId = adKeywordLibTagDao.getByUidAndId(puid, uid, adTag.getId());
        if (byPuidAndId == null) {
            return ResultUtil.error("标签不存在");
        }
        if (!byPuidAndId.getName().equals(adTagVo.getName())) {
            boolean exist = adKeywordLibTagDao.exist(puid, uid, adTagVo.getType(), adTagVo.getName(), null);
            if (exist) {
                return ResultUtil.error("标签名称重复");
            }
        }
        adKeywordLibTagDao.updateById(adTag.getPuid(), adTag);
        return ResultUtil.success();
    }

    @Override
    public Result<AdKeywordTagVo> listAdTag(int puid, int uid, String type, String name, List<Integer> uidList) {
        Map<Long, Long> markupTagUidVos = adKeywordLibMarkupTagDao.getCountByTagId(puid, uid, uidList).stream().
                collect(Collectors.toMap(AdKeywordLibMarkupTagUidVo::getId, AdKeywordLibMarkupTagUidVo::getCount));
        List<AdKeywordLibTag> list = adKeywordLibTagDao.getList(puid, uid, type, name);
        AdKeywordTagVo vo = new AdKeywordTagVo();
        List<AdKeywordTagVo.AdKeywordTag> adKeywordTagList = list.stream().map(e -> {
            AdKeywordTagVo.AdKeywordTag adKeywordTag = new AdKeywordTagVo.AdKeywordTag();
            adKeywordTag.setId(e.getId());
            adKeywordTag.setName(e.getName());
            adKeywordTag.setType(e.getType());
            adKeywordTag.setColor(e.getColor());
            adKeywordTag.setCount(markupTagUidVos.getOrDefault(e.getId(), 0L));
            adKeywordTag.setCommonTag(e.getPuid() == 0);
            adKeywordTag.setSort(e.getTagSort());
            return adKeywordTag;
        }).sorted(Comparator.comparing(AdKeywordTagVo.AdKeywordTag::getSort)).collect(Collectors.toList());
        vo.setAdKeywordTagList(adKeywordTagList);
        vo.setCount(markupTagUidVos.size() == 0 ? 0L : markupTagUidVos.values().stream().mapToLong(e -> e).sum());
        return ResultUtil.success(vo);
    }

    @Override
    public Result<AdKeywordTagVo> listAdCommonTag(int puid, int uid, String type, String name, List<Integer> uidList) {
        // 公共标签
        Map<Long, Long> markupTagUidVos = new HashMap<>();
        if (AdTagTypeEnum.KEYWORDLIB.getType().equals(type)) {
            markupTagUidVos = adKeywordLibMarkupTagDao.getAllCountByTagId(puid, uid, uidList).stream().
                    collect(Collectors.toMap(AdKeywordLibMarkupTagUidVo::getId, AdKeywordLibMarkupTagUidVo::getCount));
        } else {
            markupTagUidVos = adKeywordLibMarkupTagDao.getAllAsinCountByTagId(puid, uid, uidList).stream().
                    collect(Collectors.toMap(AdKeywordLibMarkupTagUidVo::getId, AdKeywordLibMarkupTagUidVo::getCount));
        }

        List<AdKeywordLibTag> list = adKeywordLibTagDao.getAllList(puid, uid, AdTagTypeEnum.KEYWORDLIB.getType(), name);
        // 根据标签反查词的数量 需要查两次 一次个人 一次
        AdKeywordTagVo vo = new AdKeywordTagVo();
        Map<Long, Long> finalMarkupTagUidVos1 = markupTagUidVos;
        vo.setAdKeywordTagList(list.stream().filter(k -> k.getUid() != 0).map(k -> build(k, finalMarkupTagUidVos1)).collect(Collectors.toList()));
        Map<Long, Long> finalMarkupTagUidVos = markupTagUidVos;
        vo.setAdKeywordTagCommonList(list.stream().filter(k -> k.getUid() == 0).map(k -> build(k, finalMarkupTagUidVos)).collect(Collectors.toList()));
        if (CollectionUtils.isNotEmpty(list)) {
            List<Long> tagIdList = list.stream().map(AdKeywordLibTag::getId).collect(Collectors.toList());
            if (AdTagTypeEnum.KEYWORDLIB.getType().equals(type)) {
                vo.setCount(Long.parseLong(String.valueOf(adKeywordLibMarkupTagDao.countKeywordByTagId(puid, uid, uidList, tagIdList))));
                vo.setCommonCount(Long.parseLong(String.valueOf(adKeywordLibMarkupTagDao.countKeywordByTagId(puid, 0, uidList, tagIdList))));
            } else {
                vo.setCount(Long.parseLong(String.valueOf(adKeywordLibMarkupTagDao.countAsinByTagId(puid, uid, uidList, tagIdList))));
                vo.setCommonCount(Long.parseLong(String.valueOf(adKeywordLibMarkupTagDao.countAsinByTagId(puid, 0, uidList, tagIdList))));
            }
        } else {
            vo.setCommonCount(0L);
            vo.setCount(0L);
        }
        return ResultUtil.success(vo);
    }

    private AdKeywordTagVo.AdKeywordTag build(AdKeywordLibTag adKeywordLibTag, Map<Long, Long> markupTagUidVos) {
        AdKeywordTagVo.AdKeywordTag adKeywordTag = new AdKeywordTagVo.AdKeywordTag();
        adKeywordTag.setId(adKeywordLibTag.getId());
        adKeywordTag.setName(adKeywordLibTag.getName());
        adKeywordTag.setType(adKeywordLibTag.getType());
        adKeywordTag.setColor(adKeywordLibTag.getColor());
        adKeywordTag.setCount(markupTagUidVos.getOrDefault(adKeywordLibTag.getId(), 0L));
        adKeywordTag.setCommonTag(adKeywordLibTag.getUid() == 0);
        adKeywordTag.setSort(adKeywordLibTag.getTagSort());
        return adKeywordTag;
    }

    @Override
    public Result<List<AdKeywordLibMarkupTagVo>> makeAdTag(int puid, String loginIp, MakeAdTagVo makeAdTagVo, List<Integer> uidList) {
        List<AdKeywordLibTag> tagList = adKeywordLibTagDao.listByUidAndId(puid, makeAdTagVo.getUid(), makeAdTagVo.getAdTagIdList());
        if (tagList == null || tagList.size() != makeAdTagVo.getAdTagIdList().size()) {
            return ResultUtil.error("标签不存在, 请刷新页面!");
        }
        List<String> idsList = makeAdTagVo.getIdsList();
        if (idsList == null) {
            return ResultUtil.error("请求参数错误");
        }
        List<AdKeywordLibMarkupTagVo> errors = new ArrayList<>();
        List<AdKeywordLibMarkupTag> insList = new ArrayList<>();
        Set<Long> adTagId = new HashSet<>();
        Set<Long> tagIds = new HashSet<>();
        if (AdTagTypeEnum.KEYWORDLIB.getType().equalsIgnoreCase(makeAdTagVo.getType())) {
            List<AmazonAdKeywordsLib> relationIds = getRelationIds(idsList, makeAdTagVo, uidList);
            Map<String, List<AmazonAdKeywordsLib>> keywordMap = relationIds.stream().collect(Collectors.groupingBy(k -> k.getKeywordText().toLowerCase()));
            if (CollectionUtils.isEmpty(relationIds) || idsList.size() != relationIds.size()) {
                return ResultUtil.error("标记对象不存在，请刷新页面重试");
            }
            if (keywordMap.keySet().size() > 200) {
                return ResultUtil.error("一次最多标记200条!");
            }
            // 查找所有标签
            List<AdKeywordLibMarkupTag> relationVos = adKeywordLibMarkupTagDao.getMarkupTagByRelationId(puid, makeAdTagVo.getUid(),
                    makeAdTagVo.getType(), idsList);
            // 根据关键词Id分组
            Map<Long, Set<Long>> relationMap = relationVos.stream().collect(Collectors.groupingBy(k -> Long.parseLong(k.getRelationId()), Collectors.mapping(AdKeywordLibMarkupTag::getTagId, Collectors.toSet())));
            adTagId = tagList.stream().map(AdKeywordLibTag::getId).collect(Collectors.toSet());
            // 根据关键词分组
            Map<String, List<AmazonAdKeywordsLib>> listMap = relationIds.stream().collect(Collectors.groupingBy(k -> k.getKeywordText().toLowerCase()));
            Set<Long> finalAdTagId = adTagId;
            listMap.forEach((key, value) -> {
                List<AdKeywordLibMarkupTagVo> adKeywordLibMarkupTagVos = new ArrayList<>();
                for (AmazonAdKeywordsLib amazonAdKeywordsLib : value) {
                    AdKeywordLibMarkupTagVo adKeywordLibMarkupTagVo = new AdKeywordLibMarkupTagVo();
                    adKeywordLibMarkupTagVo.setRelationId(amazonAdKeywordsLib.getId().toString());
                    adKeywordLibMarkupTagVo.setError("关键词" + key + "添加的标签数量超过限制");
                    adKeywordLibMarkupTagVos.add(adKeywordLibMarkupTagVo);
                    Set<Long> adMarkupTagVo = relationMap.get(amazonAdKeywordsLib.getId());
                    if (adMarkupTagVo != null) {
                        tagIds.addAll(adMarkupTagVo);
                    }
                }
                tagIds.addAll(finalAdTagId);
                if (tagIds.size() > Constants.KEYWORD_LIB_TAG_MAX_SIZE) {
                    errors.addAll(adKeywordLibMarkupTagVos);
                    return;
                }
                List<AmazonAdKeywordsLib> amazonAdKeywordsLibs = value.stream().filter(k -> k.getUid().equals(makeAdTagVo.getUid())).collect(Collectors.toList());
                // 添加私有标签 如果有自己的添加词 则只加自己的词
                List<AdKeywordLibTag> noCommonTag = tagList.stream().filter(k -> k.getUid() != 0).collect(Collectors.toList());
                addMarkupTag(puid, CollectionUtils.isEmpty(amazonAdKeywordsLibs) ? value : amazonAdKeywordsLibs, noCommonTag, makeAdTagVo, insList);
                // 添加公共标签
                List<AdKeywordLibTag> commonTag = tagList.stream().filter(k -> k.getUid() == 0).collect(Collectors.toList());
                addMarkupTag(puid, value, commonTag, makeAdTagVo, insList);
            });
        } else {
            List<AmazonAdAsinsLib> relationIds = getAsinRelationIds(idsList, makeAdTagVo, uidList);
            Map<String, List<AmazonAdAsinsLib>> keywordMap = relationIds.stream().collect(Collectors.groupingBy(k -> k.getAsin().toLowerCase()));
            if (CollectionUtils.isEmpty(relationIds) || idsList.size() != relationIds.size()) {
                return ResultUtil.error("标记对象不存在，请刷新页面重试");
            }
            if (keywordMap.keySet().size() > 200) {
                return ResultUtil.error("一次最多标记200条!");
            }
            // 查找所有标签
            List<AdKeywordLibMarkupTag> relationVos = adKeywordLibMarkupTagDao.getMarkupTagByRelationId(puid, makeAdTagVo.getUid(),
                    makeAdTagVo.getType(), idsList);
            // 根据关键词Id分组
            Map<Long, Set<Long>> relationMap = relationVos.stream().collect(Collectors.groupingBy(k -> Long.parseLong(k.getRelationId()), Collectors.mapping(AdKeywordLibMarkupTag::getTagId, Collectors.toSet())));
            adTagId = tagList.stream().map(AdKeywordLibTag::getId).collect(Collectors.toSet());
            // 根据关键词分组
            Map<String, List<AmazonAdAsinsLib>> listMap = relationIds.stream().collect(Collectors.groupingBy(k -> k.getAsin().toLowerCase()));
            Set<Long> finalAdTagId1 = adTagId;
            listMap.forEach((key, value) -> {
                List<AdKeywordLibMarkupTagVo> adKeywordLibMarkupTagVos = new ArrayList<>();
                for (AmazonAdAsinsLib amazonAdAsinsLib : value) {
                    AdKeywordLibMarkupTagVo adKeywordLibMarkupTagVo = new AdKeywordLibMarkupTagVo();
                    adKeywordLibMarkupTagVo.setRelationId(amazonAdAsinsLib.getId().toString());
                    adKeywordLibMarkupTagVo.setError("关键词" + key + "添加的标签数量超过限制");
                    adKeywordLibMarkupTagVos.add(adKeywordLibMarkupTagVo);
                    Set<Long> adMarkupTagVo = relationMap.get(amazonAdAsinsLib.getId());
                    if (adMarkupTagVo != null) {
                        tagIds.addAll(adMarkupTagVo);
                    }
                }
                tagIds.addAll(finalAdTagId1);
                if (tagIds.size() > Constants.KEYWORD_LIB_TAG_MAX_SIZE) {
                    errors.addAll(adKeywordLibMarkupTagVos);
                    return;
                }
                List<AmazonAdAsinsLib> amazonAdKeywordsLibs = value.stream().filter(k -> k.getUid().equals(makeAdTagVo.getUid())).collect(Collectors.toList());
                // 添加私有标签 如果有自己的添加词 则只加自己的词
                List<AdKeywordLibTag> noCommonTag = tagList.stream().filter(k -> k.getUid() != 0).collect(Collectors.toList());
                addMarkupAsinTag(puid, CollectionUtils.isEmpty(amazonAdKeywordsLibs) ? value : amazonAdKeywordsLibs, noCommonTag, makeAdTagVo, insList);
                // 添加公共标签
                List<AdKeywordLibTag> commonTag = tagList.stream().filter(k -> k.getUid() == 0).collect(Collectors.toList());
                addMarkupAsinTag(puid, value, commonTag, makeAdTagVo, insList);
            });
        }
        Result<List<AdKeywordLibMarkupTagVo>> result = ResultUtil.success(errors);
        if (CollectionUtils.isEmpty(insList)) {
            result.setCode(Result.ERROR);
            result.setMsg("添加的标签数量超过限制");
            return result;
        }
        adKeywordLibMarkupTagDao.insertList(insList);
        return result;
    }

    private void addMarkupTag(Integer puid, List<AmazonAdKeywordsLib> value, List<AdKeywordLibTag> commonTag, MakeAdTagVo makeAdTagVo, List<AdKeywordLibMarkupTag> insList) {
        if (CollectionUtils.isEmpty(commonTag) || CollectionUtils.isEmpty(value)) {
            return;
        }
        for (AmazonAdKeywordsLib amazonAdKeywordsLib : value) {
            for (AdKeywordLibTag adKeywordLibTag : commonTag) {
                insList.add(build(puid, adKeywordLibTag, amazonAdKeywordsLib, makeAdTagVo));
            }
        }
    }

    private void addMarkupAsinTag(Integer puid, List<AmazonAdAsinsLib> value, List<AdKeywordLibTag> commonTag, MakeAdTagVo makeAdTagVo, List<AdKeywordLibMarkupTag> insList) {
        if (CollectionUtils.isEmpty(commonTag) || CollectionUtils.isEmpty(value)) {
            return;
        }
        for (AmazonAdAsinsLib amazonAdKeywordsLib : value) {
            for (AdKeywordLibTag adKeywordLibTag : commonTag) {
                insList.add(buildUp(puid, adKeywordLibTag, amazonAdKeywordsLib, makeAdTagVo));
            }
        }
    }

    private AdKeywordLibMarkupTag build(Integer puid, AdKeywordLibTag adKeywordLibTag, AmazonAdKeywordsLib amazonAdKeywordsLib, MakeAdTagVo makeAdTagVo) {
        AdKeywordLibMarkupTag in = new AdKeywordLibMarkupTag();
        in.setPuid(puid);
        in.setUid(adKeywordLibTag.getUid());
        in.setType(adKeywordLibTag.getType());
        in.setCreateId(makeAdTagVo.getUid());
        in.setUpdateId(makeAdTagVo.getUid());
        in.setTagId(adKeywordLibTag.getId());
        in.setRelationId(amazonAdKeywordsLib.getId().toString());
        return in;
    }

    private AdKeywordLibMarkupTag buildUp(Integer puid, AdKeywordLibTag adKeywordLibTag, AmazonAdAsinsLib amazonAdKeywordsLib, MakeAdTagVo makeAdTagVo) {
        AdKeywordLibMarkupTag in = new AdKeywordLibMarkupTag();
        in.setPuid(puid);
        in.setUid(adKeywordLibTag.getUid());
//        in.setType(adKeywordLibTag.getType());
        //这里是插入asinLib的标签
        in.setType(AdTagTypeEnum.ASINLIB.getType());
        in.setCreateId(makeAdTagVo.getUid());
        in.setUpdateId(makeAdTagVo.getUid());
        in.setTagId(adKeywordLibTag.getId());
        in.setRelationId(amazonAdKeywordsLib.getId().toString());
        return in;
    }

    @Override
    public Result clearMakeAdTag(int puid, String loginIp, MakeAdTagVo makeAdTagVo) {
        List<String> idsList = makeAdTagVo.getIdsList();
        if (idsList == null) {
            return ResultUtil.error("请求参数错误");
        }
        // 清除标签
        adKeywordLibMarkupTagDao.deleteAllByRelationId(puid, makeAdTagVo.getUid(), makeAdTagVo.getType(), makeAdTagVo.getAdType(), makeAdTagVo.getTargetType(), makeAdTagVo.getIdsList(), makeAdTagVo.getAdTagIdList());
        return ResultUtil.success();
    }

    /**
     * 查询关联id是否存在
     *
     * @param idsList
     * @param makeAdTagVo
     * @return
     */
    private List<AmazonAdKeywordsLib> getRelationIds(List<String> idsList, MakeAdTagVo makeAdTagVo, List<Integer> uidList) {
        return amazonAdKeywordLibDao.getTargetIds(makeAdTagVo.getPuid(), uidList, idsList.stream().map(Long::parseLong).collect(Collectors.toList()));
    }

    /**
     * 查询关联id是否存在
     * @param idsList
     * @param makeAdTagVo
     * @param uidList
     * @return
     */
    private List<AmazonAdAsinsLib> getAsinRelationIds(List<String> idsList, MakeAdTagVo makeAdTagVo, List<Integer> uidList) {
        return amazonAdAsinsLibDao.getTargetIds(makeAdTagVo.getPuid(), uidList, idsList.stream().map(Long::parseLong).collect(Collectors.toList()));
    }

    @Override
    public Result deleteMakeAdTag(int puid, String loginIp, ClearAdTagVo clearAdTagVo) {
        if (StringUtils.isBlank(clearAdTagVo.getAdTagId()) || CollectionUtils.isEmpty(clearAdTagVo.getIdList())) {
            return ResultUtil.error("请求参数错误");
        }
        adKeywordLibMarkupTagDao.deleteByRelationIdAndAdTagId(puid, clearAdTagVo);
        return ResultUtil.success();
    }

    @Override
    public Result updateTagSort(UpdateTagSortRequest request) {
        List<AdKeywordLibTag> list = adKeywordLibTagDao.getByUidAndIdList(request.getPuid().getValue(), request.getCommonTag() ? 0 : request.getUid(), request.getItemList().stream().map(UpdateTagSortItemRequest::getAdTagId).collect(Collectors.toList()));
        if (request.getItemCount() != list.size()) {
            return ResultUtil.error("参数错误,请刷新页面!");
        }
        Map<Long, UpdateTagSortItemRequest> map = request.getItemList().stream().collect(Collectors.toMap(UpdateTagSortItemRequest::getAdTagId, Function.identity()));
        list.forEach(k -> k.setTagSort(map.get(k.getId()).getTagSort()));
        adKeywordLibTagDao.batchUpdateSort(request.getPuid().getValue(), list);
        return ResultUtil.success();
    }

    @Override
    public List<Long> getRelationIdsByTagFilter(int puid, Integer uid, String type,
                                                List<Integer> tagIdList, int adTagQueryType) {
        List<String> relationIds = new ArrayList<>();
        if (adTagQueryType == 0) {
            adTagQueryType = AdTagQueryTypeEnum.ANY_ONE.getCode();
        }
        if (AdTagQueryTypeEnum.ANY_ONE.getCode() == adTagQueryType) {
            relationIds = adKeywordLibMarkupTagDao.getRelationIds(puid, uid, type, tagIdList);
        }
        else if (AdTagQueryTypeEnum.ALL_MATCH.getCode() == adTagQueryType) {
            //查询结果为relationId (tag_id1,tag_id2)
            List<AdTagRelationIdDTO> relationAndTagIdList = adKeywordLibMarkupTagDao.getRelationIdsAndTagIds(puid, uid, type);
            if (CollectionUtils.isNotEmpty(relationAndTagIdList)) {
                List<String> queryTagId = tagIdList.parallelStream().map(String::valueOf).collect(Collectors.toList());
                relationIds = relationAndTagIdList.parallelStream()
                        .filter(r -> {
                            if (StringUtils.isNotEmpty(r.getTagIdList())) {
                                List<String> filterTagIdList = Arrays.asList(r.getTagIdList().split(","));
                                if (CollectionUtils.isNotEmpty(filterTagIdList)) {
                                    return new HashSet<>(filterTagIdList).containsAll(queryTagId);
                                }
                            }
                            return false;
                        }).map(AdTagRelationIdDTO::getRelationId).collect(Collectors.toList());
            }
        }
        return relationIds.stream().filter(StringUtils::isNotEmpty).map(Long::parseLong).collect(Collectors.toList());
    }
}
