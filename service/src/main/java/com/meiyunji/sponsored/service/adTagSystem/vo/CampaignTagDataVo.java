package com.meiyunji.sponsored.service.adTagSystem.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @author: liweibin
 * @email: <EMAIL>
 * @date: 2024-10-25  16:51
 */
@Data
public class CampaignTagDataVo {
    private Long id;
    private Long tagGroupId;
    private List<shopVo> shopVoList;
    private String tagName;
    private String tagColor;
    private String createName;
    private String campaignCount;
    private String adCost; //花费
    private String impressions;  //曝光量
    private String clicks;  //点击量
    private String cpa; //cpa
    private String adCostPerClick;  //cpc
    private String ctr;  //广告点击率
    private String cvr;  //订单转化率
    private String acos;  //acos
    private String roas;  //roas
    private String acots;
    private String asots;
    private String advertisingUnitPrice;  //广告笔单价
    private String adOrderNum;  //广告订单量
    private String adSale;  //广告销售额
    private String adSaleNum; //广告销量
    private String selfAdOrderNum; //本广告产品订单量
    private String otherAdOrderNum; //其他产品广告订单量
    private String adSelfSale; //本广告产品销售额
    private String adOtherSales; //其他产品广告销售额
    private String adSelfSaleNum; //本产品广告销量
    private String adOtherSaleNum; //其他产品广告销量
    private String ordersNewToBrandFTD; //“品牌新买家”订单量
    private String orderRateNewToBrandFTD; //“品牌新买家”订单百分比
    private String salesNewToBrandFTD; //“品牌新买家”销售额
    private String salesRateNewToBrandFTD; //“品牌新买家”销售额百分比
    private String unitsOrderedNewToBrandFTD; //“品牌新买家”销量
    private String unitsOrderedRateNewToBrandFTD; //“品牌新买家”销量百分比

    private String compareAdCost; //花费
    private String compareImpressions;  //曝光量
    private String compareClicks;  //点击量
    private String compareCpa; //cpa
    private String compareAdCostPerClick;  //cpc
    private String compareCtr;  //广告点击率
    private String compareCvr;  //订单转化率
    private String compareAcos;  //acos
    private String compareRoas;  //roas
    private String compareAcots;
    private String compareAsots;
    private String compareAdvertisingUnitPrice;  //广告笔单价
    private String compareAdOrderNum;  //广告订单量
    private String compareAdSale;  //广告销售额
    private String compareAdSaleNum; //广告销量
    private String compareSelfAdOrderNum; //本广告产品订单量
    private String compareOtherAdOrderNum; //其他产品广告订单量
    private String compareAdSelfSale; //本广告产品销售额
    private String compareAdOtherSales; //其他产品广告销售额
    private String compareAdSelfSaleNum; //本产品广告销量
    private String compareAdOtherSaleNum; //其他产品广告销量
    private String compareOrdersNewToBrandFTD; //“品牌新买家”订单量
    private String compareOrderRateNewToBrandFTD; //“品牌新买家”订单百分比
    private String compareSalesNewToBrandFTD; //“品牌新买家”销售额
    private String compareSalesRateNewToBrandFTD; //“品牌新买家”销售额百分比
    private String compareUnitsOrderedNewToBrandFTD; //“品牌新买家”销量
    private String compareUnitsOrderedRateNewToBrandFTD; //“品牌新买家”销量百分比

    private String compareAdCostRate; //花费
    private String compareImpressionsRate;  //曝光量
    private String compareClicksRate;  //点击量
    private String compareCpaRate; //cpa
    private String compareAdCostPerClickRate;  //cpc
    private String compareCtrRate;  //广告点击率
    private String compareCvrRate;  //订单转化率
    private String compareAcosRate;  //acos
    private String compareRoasRate;  //roas
    private String compareAcotsRate;
    private String compareAsotsRate;
    private String compareAdvertisingUnitPriceRate;  //广告笔单价
    private String compareAdOrderNumRate;  //广告订单量
    private String compareAdSaleRate;  //广告销售额
    private String compareAdSaleNumRate; //广告销量
    private String compareSelfAdOrderNumRate; //本广告产品订单量
    private String compareOtherAdOrderNumRate; //其他产品广告订单量
    private String compareAdSelfSaleRate; //本广告产品销售额
    private String compareAdOtherSalesRate; //其他产品广告销售额
    private String compareAdSelfSaleNumRate; //本产品广告销量
    private String compareAdOtherSaleNumRate; //其他产品广告销量
    private String compareOrdersNewToBrandFTDRate; //“品牌新买家”订单量
    private String compareOrderRateNewToBrandFTDRate; //“品牌新买家”订单百分比
    private String compareSalesNewToBrandFTDRate; //“品牌新买家”销售额
    private String compareSalesRateNewToBrandFTDRate; //“品牌新买家”销售额百分比
    private String compareUnitsOrderedNewToBrandFTDRate; //“品牌新买家”销量
    private String compareUnitsOrderedRateNewToBrandFTDRate; //“品牌新买家”销量百分比
    //标签列表
    private List<CampaignTagDataVo> tagDataVoList;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class shopVo {
        private Integer id;
        private String name;
        private String marketplaceId;
        private String marketplaceCn;
    }

    public static CampaignTagDataVo initCampaignTagDataVo(Boolean isCompare) {
        CampaignTagDataVo vo = new CampaignTagDataVo();
        vo.setCampaignCount("0");
        vo.setAdCost("0"); //花费
        vo.setImpressions("0");  //曝光量
        vo.setClicks("0");  //点击量
        vo.setCpa("0"); //cpa
        vo.setAdCostPerClick("0");  //cpc
        vo.setCtr("0");  //广告点击率
        vo.setCvr("0");  //订单转化率
        vo.setAcos("0");  //acos
        vo.setRoas("0");  //roas
        vo.setAcots("0");
        vo.setAsots("0");
        vo.setAdvertisingUnitPrice("0");  //广告笔单价
        vo.setAdOrderNum("0");  //广告订单量
        vo.setAdSale("0");  //广告销售额
        vo.setAdSaleNum("0"); //广告销量
        vo.setSelfAdOrderNum("0"); //本广告产品订单量
        vo.setOtherAdOrderNum("0"); //其他产品广告订单量
        vo.setAdSelfSale("0"); //本广告产品销售额
        vo.setAdOtherSales("0"); //其他产品广告销售额
        vo.setAdSelfSaleNum("0"); //本产品广告销量
        vo.setAdOtherSaleNum("0"); //其他产品广告销量
        vo.setOrdersNewToBrandFTD("0"); //“品牌新买家”订单量
        vo.setOrderRateNewToBrandFTD("0"); //“品牌新买家”订单百分比
        vo.setSalesNewToBrandFTD("0"); //“品牌新买家”销售额
        vo.setSalesRateNewToBrandFTD("0"); //“品牌新买家”销售额百分比
        vo.setUnitsOrderedNewToBrandFTD("0"); //“品牌新买家”销量
        vo.setUnitsOrderedRateNewToBrandFTD("0"); //“品牌新买家”销量百分比
        if (isCompare) {
            vo.setCompareAdCost("0"); //花费
            vo.setCompareImpressions("0");  //曝光量
            vo.setCompareClicks("0");  //点击量
            vo.setCompareCpa("0"); //cpa
            vo.setCompareAdCostPerClick("0");  //cpc
            vo.setCompareCtr("0");  //广告点击率
            vo.setCompareCvr("0");  //订单转化率
            vo.setCompareAcos("0");  //acos
            vo.setCompareRoas("0");  //roas
            vo.setCompareAcots("0");
            vo.setCompareAsots("0");
            vo.setCompareAdvertisingUnitPrice("0");  //广告笔单价
            vo.setCompareAdOrderNum("0");  //广告订单量
            vo.setCompareAdSale("0");  //广告销售额
            vo.setCompareAdSaleNum("0"); //广告销量
            vo.setCompareSelfAdOrderNum("0"); //本广告产品订单量
            vo.setCompareOtherAdOrderNum("0"); //其他产品广告订单量
            vo.setCompareAdSelfSale("0"); //本广告产品销售额
            vo.setCompareAdOtherSales("0"); //其他产品广告销售额
            vo.setCompareAdSelfSaleNum("0"); //本产品广告销量
            vo.setCompareAdOtherSaleNum("0"); //其他产品广告销量
            vo.setCompareOrdersNewToBrandFTD("0"); //“品牌新买家”订单量
            vo.setCompareOrderRateNewToBrandFTD("0"); //“品牌新买家”订单百分比
            vo.setCompareSalesNewToBrandFTD("0"); //“品牌新买家”销售额
            vo.setCompareSalesRateNewToBrandFTD("0"); //“品牌新买家”销售额百分比
            vo.setCompareUnitsOrderedNewToBrandFTD("0"); //“品牌新买家”销量
            vo.setCompareUnitsOrderedRateNewToBrandFTD("0"); //“品牌新买家”销量百分比

            vo.setCompareAdCostRate("0"); //花费
            vo.setCompareImpressionsRate("0");  //曝光量
            vo.setCompareClicksRate("0");  //点击量
            vo.setCompareCpaRate("0"); //cpa
            vo.setCompareAdCostPerClickRate("0");  //cpc
            vo.setCompareCtrRate("0");  //广告点击率
            vo.setCompareCvrRate("0");  //订单转化率
            vo.setCompareAcosRate("0");  //acos
            vo.setCompareRoasRate("0");  //roas
            vo.setCompareAcotsRate("0");
            vo.setCompareAsotsRate("0");
            vo.setCompareAdvertisingUnitPriceRate("0");  //广告笔单价
            vo.setCompareAdOrderNumRate("0");  //广告订单量
            vo.setCompareAdSaleRate("0");  //广告销售额
            vo.setCompareAdSaleNumRate("0"); //广告销量
            vo.setCompareSelfAdOrderNumRate("0"); //本广告产品订单量
            vo.setCompareOtherAdOrderNumRate("0"); //其他产品广告订单量
            vo.setCompareAdSelfSaleRate("0"); //本广告产品销售额
            vo.setCompareAdOtherSalesRate("0"); //其他产品广告销售额
            vo.setCompareAdSelfSaleNumRate("0"); //本产品广告销量
            vo.setCompareAdOtherSaleNumRate("0"); //其他产品广告销量
            vo.setCompareOrdersNewToBrandFTDRate("0"); //“品牌新买家”订单量
            vo.setCompareOrderRateNewToBrandFTDRate("0"); //“品牌新买家”订单百分比
            vo.setCompareSalesNewToBrandFTDRate("0"); //“品牌新买家”销售额
            vo.setCompareSalesRateNewToBrandFTDRate("0"); //“品牌新买家”销售额百分比
            vo.setCompareUnitsOrderedNewToBrandFTDRate("0"); //“品牌新买家”销量
            vo.setCompareUnitsOrderedRateNewToBrandFTDRate("0"); //“品牌新买家”销量百分比
        }
        return vo;
    }
}
