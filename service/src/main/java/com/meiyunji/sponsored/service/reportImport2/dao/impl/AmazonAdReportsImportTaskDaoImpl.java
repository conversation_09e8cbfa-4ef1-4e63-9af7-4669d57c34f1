package com.meiyunji.sponsored.service.reportImport2.dao.impl;

import com.meiyunji.sponsored.common.springjdbc.AdBaseDaoImpl;
import com.meiyunji.sponsored.common.springjdbc.ConditionBuilder;
import com.meiyunji.sponsored.service.reportImport.enums.ReportImportStatus;
import com.meiyunji.sponsored.service.reportImport2.dao.IAmazonAdReportsImportTaskDao;
import com.meiyunji.sponsored.service.reportImport2.entity.AmazonAdReportsImportTask;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 */
@Repository
public class AmazonAdReportsImportTaskDaoImpl extends AdBaseDaoImpl<AmazonAdReportsImportTask> implements IAmazonAdReportsImportTaskDao {
    @Override
    public long insertTask(AmazonAdReportsImportTask task) throws Exception {
        try {
            return save(task);
        } catch (Exception e) {
            throw e;
        }
    }

    @Override
    public int updateStatusById(Integer puid, Long id, ReportImportStatus newStatus, ReportImportStatus originStatus) {
        String sql = "update t_amazon_ad_reports_import_task set status = ? where puid = ? and id = ? and status = ? ";
        return getJdbcTemplate().update(sql, newStatus.name(), puid, id, originStatus.name());
    }


    @Override
    public AmazonAdReportsImportTask getTaskByUuId(Integer puid, String uuid) {
        return getByCondition(new ConditionBuilder.Builder().equalToWithoutCheck("puid", puid)
                .equalToWithoutCheck("uuid", uuid).build());
    }
}
