package com.meiyunji.sponsored.service.multiPlatform.walmart.po;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.meiyunji.sponsored.common.springjdbc.DbColumn;
import com.meiyunji.sponsored.common.springjdbc.DbTable;
import com.meiyunji.sponsored.service.annotation.AdLogFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @author: ys
 * @date: 2025/2/21 10:38
 * @describe:
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@DbTable(value = "t_walmart_advertising_placement")
public class WalmartAdvertisingPlacement implements Serializable {
    private static final long serialVersionUID = -6282720921895395813L;
    /**
     * id
     */
    @DbColumn(value = "id",autoIncrement=true,key = true)
    private Long id;

    /**
     * 商户uid
     */
    @DbColumn(value = "puid")
    private Integer puid;

    /**
     * 店铺ID
     */
    @DbColumn(value = "shop_id")
    private Integer shopId;

    /**
     * 站点CODE;US;CA;MX;CL
     */
    @DbColumn(value = "marketplace_code")
    private String marketplaceCode;

    /**
     * 活动id
     */
    @DbColumn(value = "campaign_id")
    private String campaignId;

    /**
     * 位置 Search Carousel、Item Buybox、Item Carousel
     */
    @DbColumn(value = "placement")
    private String placement;

    /**
     * 广告位对应报告位 search In-grid,Carousel,Buybox , others
     */
    @DbColumn(value = "placement_report")
    private String placementReport;

    /**
     * 状态 excluded、included
     */
    @DbColumn(value = "status")
    @AdLogFormat(value = "状态", methodStr = "getStatus")
    private String status;

    /**
     * 创建时间
     */
    @DbColumn(value = "create_time")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新的时间
     */
    @DbColumn(value = "update_time")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

}
