package com.meiyunji.sponsored.service.export.handler;

import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.base.ProcessMsg;
import com.meiyunji.sponsored.common.support.StringRedisService;
import com.meiyunji.sponsored.common.util.JSONUtil;
import com.meiyunji.sponsored.common.util.StringUtil;
import com.meiyunji.sponsored.rpc.export.KeywordDataResponse;
import com.meiyunji.sponsored.rpc.export.TargetingDataResponse;
import com.meiyunji.sponsored.rpc.repeatTargeting.AdTagVo;
import com.meiyunji.sponsored.rpc.repeatTargeting.TargetingDetailVo;
import com.meiyunji.sponsored.rpc.vo.AdAdvancedFilterData;
import com.meiyunji.sponsored.service.cpc.dao.IAdManagePageExportTaskDao;
import com.meiyunji.sponsored.service.cpc.po.AdManagePageExportTask;
import com.meiyunji.sponsored.service.cpc.po.AdTag;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdKeyword;
import com.meiyunji.sponsored.service.cpc.service2.ICpcRepeatTargetingService;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.vo.RepeatTargetingDetailPageVo;
import com.meiyunji.sponsored.service.cpc.vo.RepeatTargetingDetailVo;
import com.meiyunji.sponsored.service.cpc.vo.RepeatTargetingListExcelVo;
import com.meiyunji.sponsored.service.cpc.vo.TargetingPageParam;
import com.meiyunji.sponsored.service.enums.AllAdStateEnum;
import com.meiyunji.sponsored.service.enums.SBThemesEnum;
import com.meiyunji.sponsored.service.enums.SpKeywordGroupValueEnum;
import com.meiyunji.sponsored.service.enums.UserCurrencyType;
import com.meiyunji.sponsored.service.excel.excelTools.WriteHandlerBuild;
import com.meiyunji.sponsored.service.excel.excelTools.service.IExcelService;
import com.meiyunji.sponsored.service.export.AdManagePageExportTaskHandler;
import com.meiyunji.sponsored.service.export.constants.AdManagePageExportTaskConstant;
import com.meiyunji.sponsored.service.vo.KeywordTargetingVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: heqiwen
 * @email: <EMAIL>
 * @date: 2024-10-15  10:20
 */
@Service(AdManagePageExportTaskConstant.REPEAT_TARGETING)
@Slf4j
public class RepeatTargetingPageExportTaskHandler implements AdManagePageExportTaskHandler {

    @Autowired
    private IAdManagePageExportTaskDao adManagePageExportTaskDao;
    @Autowired
    private ICpcRepeatTargetingService cpcRepeatTargetingService;
    @Autowired
    private StringRedisService stringRedisService;
    @Autowired
    private IExcelService excelService;

    @Override
    public void export(AdManagePageExportTask task) {
        RepeatTargetingDetailPageVo detailPageVo = JSONUtil.jsonToObject(task.getParam(), RepeatTargetingDetailPageVo.class);
        if (detailPageVo == null) {
            log.error(String.format("target export error, param is null, task id : %d", task.getId()));
            adManagePageExportTaskDao.updateErrorStatus(task.getId());
            return;
        }
        List<RepeatTargetingDetailVo> voList = cpcRepeatTargetingService.exportList(detailPageVo).getData();
        if (CollectionUtils.isEmpty(voList)) {
            //修改任务状态
            adManagePageExportTaskDao.updateSuccessStatus(task.getId(), null, 100);
            //修改状态，前端收到后转圈效果停止
            stringRedisService.set(detailPageVo.getUuid(), new ProcessMsg(-1, 0, "导出数据为空"));
            return;
        }
        String fileName = "关键词重复投放" + "_" + detailPageVo.getStartDate() + "_" + detailPageVo.getEndDate();
        int count = 0;
        List<String> urlList = new ArrayList<>();
        //页面渲染
        WriteHandlerBuild build = new WriteHandlerBuild().rate();
        //集合分片
        List<List<RepeatTargetingDetailVo>> partition = Lists.partition(voList, Constants.EXPORT_MAX_SIZE);
        for (List<RepeatTargetingDetailVo> list1 : partition) {
            List<RepeatTargetingListExcelVo> repeatTargetingDetailVo = new LinkedList<>();
            for (RepeatTargetingDetailVo vo : list1) {
                repeatTargetingDetailVo.add(this.buildExportVo(detailPageVo, vo));
            }
            if (repeatTargetingDetailVo.size() > 0) {
                //设置表头
                Class clazz = RepeatTargetingListExcelVo.class;
                build = build.currencyNew(clazz);
                List<String> excludeFileds = Lists.newArrayList();
                urlList.add(excelService.easyExcelHandlerExport(detailPageVo.getPuid(), repeatTargetingDetailVo, fileName + "(" + count++ + ")", clazz, build, excludeFileds));
            }
        }
        //修改任务状态
        adManagePageExportTaskDao.updateSuccessStatus(task.getId(), JSONUtil.objectToJson(urlList), 100);
        stringRedisService.set(detailPageVo.getUuid(), new ProcessMsg(1, urlList.size(), "导出成功", urlList));
    }

//    private RepeatTargetingDetailVo buildGrpcVo(RepeatTargetingDetailPageVo detailPageVo, RepeatTargetingDetailVo item) {
//        TargetingDetailVo.Builder newBuilder = TargetingDetailVo.newBuilder();
//        newBuilder.setPuid(Optional.ofNullable(detailPageVo.getPuid()).orElse(0));
//        newBuilder.setId(Optional.ofNullable(item.getId()).orElse(0L));
//                //关键词
//        newBuilder.setKeywordText(Optional.ofNullable(item.getKeywordText()).orElse("-"));
//        newBuilder.setKeywordId(Optional.ofNullable(item.getKeywordId()).orElse("-"));
//        newBuilder.setShopId(item.getShopId());
//                //匹配类型
//        newBuilder.setMatchType(Optional.ofNullable(item.getMatchType()).orElse("-"));
//                //广告类型
//        newBuilder.setType(Optional.ofNullable(item.getType()).orElse("-"));
//                //有效状态
//        newBuilder.setState(Optional.ofNullable(item.getState()).orElse("-"));
//                //服务状态
//        newBuilder.setServingStatus(Optional.ofNullable(item.getStatus()).orElse("-"));
//        newBuilder.setGroupType(Optional.ofNullable(item.getGroupType()).orElse("-"));
//                //服务状态返回值
//        newBuilder.setServingStatusDec(Optional.ofNullable(AmazonAdKeyword.servingStatusEnum.getServingStatusDesc(item.getStatus())).orElse("-"));
//        newBuilder.setServingStatusName(Optional.ofNullable(AmazonAdKeyword.servingStatusEnum.getServingStatusName(item.getStatus())).orElse("-"));
//                //广告组合
//        newBuilder.setPortfolioId(Optional.ofNullable(item.getPortfolioId()).orElse("-"));
//        newBuilder.setPortfolioName(Optional.ofNullable(item.getPortfolioName()).orElse("-"));
//        newBuilder.setIsHidden(Optional.ofNullable(item.getIsHidden()).orElse(0).toString());
//                //广告活动
//        newBuilder.setCampaignId(Optional.ofNullable(item.getCampaignId()).orElse("-"));
//        newBuilder.setCampaignName(Optional.ofNullable(item.getCampaignName()).orElse("-"));
//        newBuilder.setAdCampaignIsArchived(Optional.ofNullable(item.getAdCampaignIsArchived()).orElse(false));
//        newBuilder.setCampaignBid(Optional.ofNullable(item.getCampaignBid()).orElse(BigDecimal.ZERO).toPlainString());
//                //广告组
//        newBuilder.setAdGroupId(Optional.ofNullable(item.getAdGroupId()).orElse("-"));
//        newBuilder.setAdGroupName(Optional.ofNullable(item.getAdGroupName()).orElse("-"));
//        newBuilder.setAdGroupIsArchived(Optional.ofNullable(item.getAdGroupIsArchived()).orElse(false));
//                //ABA搜索词排名和周变化率
//        newBuilder.setSearchFrequencyRank(Optional.ofNullable(item.getSearchFrequencyRank()).orElse(0));
////                            .setWeekRatio(Optional.ofNullable(item.getWeekRatio().setScale(2, BigDecimal.ROUND_HALF_UP)).orElse(BigDecimal.ZERO).toPlainString())
//                //竞价
//        newBuilder.setBid(Optional.ofNullable(item.getBid()).orElse("0.00"));
//                //建议竞价
//        newBuilder.setSuggestBid(Optional.ofNullable(item.getSuggestBid()).orElse("0.00"));
//        newBuilder.setRangeStart(Optional.ofNullable(item.getRangeStart()).orElse("0.00"));
//        newBuilder.setRangeEnd(Optional.ofNullable(item.getRangeEnd()).orElse("0.00"));
//        newBuilder.setImpressions(Optional.ofNullable(item.getImpressions()).orElse(0));  //广告曝光量
//        newBuilder.setClicks(Optional.ofNullable(item.getClicks()).orElse(0));    //广告点击量
//        newBuilder.setCtr(Optional.ofNullable(item.getClickRate().setScale(2, BigDecimal.ROUND_HALF_UP)).orElse(BigDecimal.ZERO).toPlainString());  //广告点击率
//        newBuilder.setAdCost(Optional.ofNullable(item.getCost().setScale(2, BigDecimal.ROUND_HALF_UP)).orElse(BigDecimal.ZERO).toPlainString());   //广告花费
//        newBuilder.setAdCostPerClick(Optional.ofNullable(item.getCpc().setScale(2, BigDecimal.ROUND_HALF_UP)).orElse(BigDecimal.ZERO).toPlainString());   //CPC
//        newBuilder.setAdOrderNum(Optional.ofNullable(item.getSaleNum()).orElse(0));    //广告订单量
//        newBuilder.setAdSale(Optional.ofNullable(item.getTotalSales().setScale(2, BigDecimal.ROUND_HALF_UP)).orElse(BigDecimal.ZERO).toPlainString());  //广告销售额
//        newBuilder.setCvr(Optional.ofNullable(item.getSalesConversionRate().setScale(2, BigDecimal.ROUND_HALF_UP)).orElse(BigDecimal.ZERO).toPlainString());    //广告转化率
//        newBuilder.setCpa(Optional.ofNullable(item.getCpa().setScale(2, BigDecimal.ROUND_HALF_UP)).orElse(BigDecimal.ZERO).toPlainString());
//        newBuilder.setAcos(Optional.ofNullable(item.getAcos().setScale(2, BigDecimal.ROUND_HALF_UP)).orElse(BigDecimal.ZERO).toPlainString());
//        newBuilder.setRoas(Optional.ofNullable(item.getRoas().setScale(2, BigDecimal.ROUND_HALF_UP)).orElse(BigDecimal.ZERO).toPlainString());
//        newBuilder.setCompareImpressions(Optional.ofNullable(item.getCompareImpressions()).orElse(0));
//        newBuilder.setCompareClicks(Optional.ofNullable(item.getCompareClicks()).orElse(0));
//        newBuilder.setCompareCtr(Optional.ofNullable(item.getCompareClickRate().setScale(2, BigDecimal.ROUND_HALF_UP)).orElse(BigDecimal.ZERO).toPlainString());
//        newBuilder.setCompareAdCost(Optional.ofNullable(item.getCompareCost().setScale(2, BigDecimal.ROUND_HALF_UP)).orElse(BigDecimal.ZERO).toPlainString());
//        newBuilder.setCompareAdCostPerClick(Optional.ofNullable(item.getCompareCpc().setScale(2, BigDecimal.ROUND_HALF_UP)).orElse(BigDecimal.ZERO).toPlainString());
//        newBuilder.setCompareAdOrderNum(Optional.ofNullable(item.getCompareSaleNum()).orElse(0));
//        newBuilder.setCompareAdSale(Optional.ofNullable(item.getCompareTotalSales().setScale(2, BigDecimal.ROUND_HALF_UP)).orElse(BigDecimal.ZERO).toPlainString());
//        newBuilder.setCompareCvr(Optional.ofNullable(item.getCompareSalesConversionRate().setScale(2, BigDecimal.ROUND_HALF_UP)).orElse(BigDecimal.ZERO).toPlainString());
//        newBuilder.setCompareCpa(Optional.ofNullable(item.getCompareCpa().setScale(2, BigDecimal.ROUND_HALF_UP)).orElse(BigDecimal.ZERO).toPlainString());
//        newBuilder.setCompareAcos(Optional.ofNullable(item.getCompareAcos().setScale(2, BigDecimal.ROUND_HALF_UP)).orElse(BigDecimal.ZERO).toPlainString());
//        newBuilder.setCompareRoas(Optional.ofNullable(item.getCompareRoas().setScale(2, BigDecimal.ROUND_HALF_UP)).orElse(BigDecimal.ZERO).toPlainString());
//        newBuilder.setCompareImpressionsRate(Optional.ofNullable(item.getDiffImpressions()).orElse("-"));
//        newBuilder.setCompareClicksRate(Optional.ofNullable(item.getDiffClicks()).orElse("-"));
//        newBuilder.setCompareCtrRate(Optional.ofNullable(item.getDiffClickRate()).orElse("-"));
//        newBuilder.setCompareAdOrderNumRate(Optional.ofNullable(item.getDiffSaleNum()).orElse(""));
//        newBuilder.setCompareCvrRate(Optional.ofNullable(item.getDiffSalesConversionRate()).orElse("-"));
//        newBuilder.setCompareAdSaleRate(Optional.ofNullable(item.getDiffTotalSales()).orElse("-"));
//        newBuilder.setCompareAdCostRate(Optional.ofNullable(item.getDiffCost()).orElse("-"));
//        newBuilder.setCompareAdCostPerClickRate(Optional.ofNullable(item.getDiffCpc()).orElse("-"));
//        newBuilder.setCompareCpaRate(Optional.ofNullable(item.getDiffCpa()).orElse("-"));
//        newBuilder.setCompareAcosRate(Optional.ofNullable(item.getDiffAcos()).orElse("-"));
//        newBuilder.setCompareRoasRate(Optional.ofNullable(item.getDiffRoas()).orElse("-"));
//        //标签
//        List<AdTag> adTags = item.getAdTags();
//        if (CollectionUtils.isNotEmpty(adTags)) {
//            List<AdTagVo> adTagList = adTags.stream().filter(Objects::nonNull).map(adTag -> {
//                AdTagVo.Builder adTagVo = AdTagVo.newBuilder()
//                        .setId(adTag.getId().toString())
//                        .setName(adTag.getName())
//                        .setColor(adTag.getColor());
//                return adTagVo.build();
//            }).collect(Collectors.toList());
//            newBuilder.addAllAdTags(adTagList);
//        }
//        return newBuilder.build();
//    }

    private RepeatTargetingListExcelVo buildExportVo(RepeatTargetingDetailPageVo detailPageVo, RepeatTargetingDetailVo vo) {
        RepeatTargetingListExcelVo excelVo = new RepeatTargetingListExcelVo();
        BeanUtils.copyProperties(vo, excelVo);
        //对特殊值进行加符号处理以及状态映射文字
        //keywordText字段无需查询，该投放详情下所有的投放关键词均一样
        if (Constants.EXACT.equalsIgnoreCase(vo.getMatchType())) {
            excelVo.setMatchType("精确匹配");
        } else if (Constants.PHRASE.equalsIgnoreCase(vo.getMatchType())) {
            excelVo.setMatchType("词组匹配");
        } else if (Constants.BROAD.equalsIgnoreCase(vo.getMatchType())) {
            excelVo.setMatchType("广泛匹配");
        } else if (Constants.SP_KEYWORD_GROUP_MATCH_TYPE.equalsIgnoreCase(vo.getMatchType())) {
            excelVo.setMatchType("主题匹配");
        }
        //主题类型的KeywordText需要做额外映射
        if (Constants.SP_KEYWORD_GROUP_MATCH_TYPE.equals(detailPageVo.getDetailMatchType()) && SpKeywordGroupValueEnum.getKeywordGroupValueEnumByText(detailPageVo.getKeywordText()) != null) {
            excelVo.setKeywordText(SpKeywordGroupValueEnum.getKeywordGroupValueEnumByText(detailPageVo.getKeywordText()).getTextCn());
        } else if (Constants.SP_KEYWORD_GROUP_MATCH_TYPE.equals(detailPageVo.getDetailMatchType()) && SBThemesEnum.getSBThemesEnumByVal(detailPageVo.getKeywordText()) != null) {
            excelVo.setKeywordText(SBThemesEnum.getSBThemesEnumByVal(detailPageVo.getKeywordText()).getMsg());
        }
        else {
            excelVo.setKeywordText(detailPageVo.getKeywordText());
        }
        if (StringUtils.isNotBlank(vo.getRangeStart()) && StringUtils.isNotBlank(vo.getRangeEnd())) {
            excelVo.setSuggest(vo.getRangeStart() + "~" +vo.getRangeEnd());
        }
        excelVo.setState(Optional.ofNullable(AllAdStateEnum.getStateValue(vo.getState())).orElse("-"));
        excelVo.setStatus(Optional.ofNullable(AmazonAdKeyword.servingStatusEnum.getServingStatusName(vo.getStatus())).orElse("-"));
        excelVo.setClickRate(Optional.ofNullable(vo.getClickRate().setScale(2, BigDecimal.ROUND_HALF_UP)).orElse(BigDecimal.ZERO).toPlainString().concat("%"));
        excelVo.setSalesConversionRate(Optional.ofNullable(vo.getSalesConversionRate().setScale(2, BigDecimal.ROUND_HALF_UP)).orElse(BigDecimal.ZERO).toPlainString().concat("%"));
        excelVo.setAcos(Optional.ofNullable(vo.getAcos().setScale(2, BigDecimal.ROUND_HALF_UP)).orElse(BigDecimal.ZERO).toPlainString().concat("%"));
        excelVo.setRoas(Optional.ofNullable(vo.getRoas().setScale(2, BigDecimal.ROUND_HALF_UP)).orElse(BigDecimal.ZERO).toPlainString());
        excelVo.setTotalSales(UserCurrencyType.getIconByMarketplaceId(detailPageVo.getMarketplaceId()) + Optional.ofNullable(vo.getTotalSales().setScale(2, BigDecimal.ROUND_HALF_UP)).orElse(BigDecimal.ZERO).toPlainString());
        excelVo.setCost(UserCurrencyType.getIconByMarketplaceId(detailPageVo.getMarketplaceId()) + Optional.ofNullable(vo.getCost().setScale(2, BigDecimal.ROUND_HALF_UP)).orElse(BigDecimal.ZERO).toPlainString());
        excelVo.setCpc(UserCurrencyType.getIconByMarketplaceId(detailPageVo.getMarketplaceId()) + Optional.ofNullable(vo.getCpc().setScale(2, BigDecimal.ROUND_HALF_UP)).orElse(BigDecimal.ZERO).toPlainString());
        excelVo.setCpa(UserCurrencyType.getIconByMarketplaceId(detailPageVo.getMarketplaceId()) + Optional.ofNullable(vo.getCpa().setScale(2, BigDecimal.ROUND_HALF_UP)).orElse(BigDecimal.ZERO).toPlainString());
        excelVo.setDisplaySearchFrequencyRank(Optional.ofNullable(vo.getSearchFrequencyRank()).orElse(0) == 0 ? "-" : vo.getSearchFrequencyRank().toString());
        if (CollectionUtils.isNotEmpty(vo.getAdTags())) {
            List<String> tagName = vo.getAdTags().stream()
                    .filter(Objects::nonNull)
                    .map(AdTag::getName)
                    .filter(StringUtils::isNotBlank)
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(tagName)) {
                excelVo.setAdTagName(String.join(StringUtil.SPLIT_COMMA, tagName));
            }
        }
        return excelVo;
    }

}
