package com.meiyunji.sponsored.service.syncTask.report.strategy.sponsoredDisplay;

import com.alibaba.fastjson.JSONReader;
import com.google.common.collect.Lists;
import com.meiyunji.sellfox.aadas.types.enumeration.AmazonReportV3Type;
import com.meiyunji.sellfox.aadas.types.message.notification.ReportReadyNotification;
import com.meiyunji.sponsored.common.springjdbc.PartitionSqlUtil;
import com.meiyunji.sponsored.service.config.CosBucketClient;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdCampaignAllDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdSdCampaignMatchedTargetReportDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdCampaignAll;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdSdCampaignMatchedTargetReport;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.syncTask.entity.sd.SponsoredDisplayCampaigns;
import com.meiyunji.sponsored.service.syncTask.report.strategy.AbstractReportProcessStrategy;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.io.ByteArrayInputStream;
import java.io.InputStreamReader;
import java.math.BigDecimal;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.zip.GZIPInputStream;

/**
 * @author: wade
 * @date: 2021/12/28 16:32
 * @describe: sd 广告活动T00030报告处理类
 */
@Component
@Slf4j
public class SdCampaignsMatchedTargetReportV3Strategy extends AbstractReportProcessStrategy {

    private final IAmazonAdSdCampaignMatchedTargetReportDao amazonAdSdCampaignReportDao;
    private final IAmazonAdCampaignAllDao amazonAdCampaignAllDao;
    private final PartitionSqlUtil partitionSqlUtil;

    public SdCampaignsMatchedTargetReportV3Strategy(CosBucketClient dataBucketClient, IAmazonAdSdCampaignMatchedTargetReportDao amazonAdSdCampaignReportDao, IAmazonAdCampaignAllDao amazonAdCampaignAllDao, PartitionSqlUtil partitionSqlUtil) {
        super(dataBucketClient);
        this.amazonAdSdCampaignReportDao = amazonAdSdCampaignReportDao;
        this.amazonAdCampaignAllDao = amazonAdCampaignAllDao;
        this.partitionSqlUtil = partitionSqlUtil;
    }

    @Override
    public Boolean checkValid(ReportReadyNotification notification) {
        return notification.getVersion() == 3 && AmazonReportV3Type.sd_campaigns_matchedTarget == notification.getV3Type();
    }


    @Override
    public void processReport(ReportReadyNotification dto) {
        try (InputStreamReader inputStreamReader = new InputStreamReader(new GZIPInputStream(new ByteArrayInputStream(dataBucketClient.getObjectToBytes(dto.getPath()))));JSONReader jsonReader = new JSONReader(inputStreamReader)) {
            jsonReader.startArray();
            List<SponsoredDisplayCampaigns> reports = Lists.newArrayListWithExpectedSize(500);
            while (jsonReader.hasNext()) {
                jsonReader.startObject();
                SponsoredDisplayCampaigns report = new SponsoredDisplayCampaigns();
                report.readFromJsonReader(jsonReader);
                jsonReader.endObject();
                if (report.getImpressions() != 0) {
                    reports.add(report);
                }
                if (reports.size() >= 500) {
                    dealReport(dto, reports);
                    reports = Lists.newArrayListWithExpectedSize(500);
                }
            }
            jsonReader.endArray();
            if (CollectionUtils.isNotEmpty(reports)) {
                dealReport(dto, reports);
            }
        } catch (Exception e) {
            log.info("报告处理发生错误{}@{} reportType={} countDate={}", dto.getSellerIdentifier(), dto.getMarketplaceIdentifier(), dto.getV3Type(), dto.getV3StartDate(), e);
        }
    }

    private void dealReport(ReportReadyNotification dto, List<SponsoredDisplayCampaigns> reports) {
        List<String> campaignIds = new ArrayList<>();
        List<SponsoredDisplayCampaigns> validReports = reports.stream().filter(item -> BigDecimal.valueOf(item.getImpressions()).compareTo(BigDecimal.ZERO) != 0).peek(e -> {
            campaignIds.add(String.valueOf(e.getCampaignId()));
        }).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(validReports)) {
            return;
        }

        Map<String, AmazonAdCampaignAll> sdCampaignTacticByCampaignIds = amazonAdCampaignAllDao.getSdCampaignByCampaignIds(dto.getSellerIdentifier(), dto.getMarketplaceIdentifier(), campaignIds);

        List<AmazonAdSdCampaignMatchedTargetReport> poList = getPoBySdReportCampaign(dto, validReports, sdCampaignTacticByCampaignIds);
        List<List<AmazonAdSdCampaignMatchedTargetReport>> partition = Lists.partition(poList, 200);
        for (List<AmazonAdSdCampaignMatchedTargetReport> amazonAdSdCampaignReports : partition) {
            partitionSqlUtil.save(dto.getSellerIdentifier(), amazonAdSdCampaignReports, 0, amazonAdSdCampaignReportDao::insertOrUpdateList);
        }
    }


    private List<AmazonAdSdCampaignMatchedTargetReport> getPoBySdReportCampaign(ReportReadyNotification dto, List<SponsoredDisplayCampaigns> reports, Map<String, AmazonAdCampaignAll> tacticType) {
        List<AmazonAdSdCampaignMatchedTargetReport> list = Lists.newArrayListWithExpectedSize(reports.size());
        AmazonAdSdCampaignMatchedTargetReport sdCampaignReport;
        for (SponsoredDisplayCampaigns campaigm : reports) {
            if (tacticType == null || tacticType.get(String.valueOf(campaigm.getCampaignId())) == null) {
                log.info("sd campaign id :{},not fund, puid:{}, shopId:{}", campaigm.getCampaignId(), dto.getSellerIdentifier(), dto.getMarketplaceIdentifier());
                continue;
            }
            sdCampaignReport = new AmazonAdSdCampaignMatchedTargetReport();
            sdCampaignReport.setPuid(dto.getSellerIdentifier());
            sdCampaignReport.setShopId(dto.getMarketplaceIdentifier());
            sdCampaignReport.setMarketplaceId(dto.getMarketplace().getId());
            sdCampaignReport.setCountDate(campaigm.getDate().format(DateTimeFormatter.ofPattern("yyyyMMdd")));
            sdCampaignReport.setTacticType(tacticType.get(String.valueOf(campaigm.getCampaignId())).getTactic());
            sdCampaignReport.setCurrency(campaigm.getCampaignBudgetCurrencyCode());
            sdCampaignReport.setCampaignName(campaigm.getCampaignName());
            sdCampaignReport.setCampaignId(String.valueOf(campaigm.getCampaignId()));
            sdCampaignReport.setClicks(campaigm.getClicks());
            sdCampaignReport.setCost(campaigm.getCost());

            sdCampaignReport.setConversions14dSameSKU(campaigm.getPurchasesPromotedClicks());
            sdCampaignReport.setSales14dSameSKU(campaigm.getSalesPromotedClicks());
            sdCampaignReport.setViewImpressions(campaigm.getImpressionsViews());
            sdCampaignReport.setImpressions(campaigm.getImpressions());
            sdCampaignReport.setCostType(campaigm.getCostType());
            sdCampaignReport.setAttributedBrandedSearches14d(campaigm.getBrandedSearches());
            sdCampaignReport.setViewAttributedBrandedSearches14d(campaigm.getBrandedSearchesViews());
            sdCampaignReport.setMatchedTarget(campaigm.getMatchedTargetAsin());


            if (Constants.SD_REPORT_VCPM.equalsIgnoreCase(campaigm.getCostType())) {
                sdCampaignReport.setUnitsOrdered14d(campaigm.getUnitsSold());
                sdCampaignReport.setConversions14d(campaigm.getPurchases());
                sdCampaignReport.setSales14d(campaigm.getSales());
                sdCampaignReport.setDetailPageView14d(campaigm.getDetailPageViews());
                sdCampaignReport.setOrdersNewToBrand14d(campaigm.getNewToBrandPurchases());
                sdCampaignReport.setSalesNewToBrand14d(campaigm.getNewToBrandSales());
                sdCampaignReport.setUnitsOrderedNewToBrand14d(campaigm.getNewToBrandUnitsSold());

            } else {

                sdCampaignReport.setUnitsOrdered14d(campaigm.getUnitsSoldClicks());
                sdCampaignReport.setConversions14d(campaigm.getPurchasesClicks());
                sdCampaignReport.setSales14d(campaigm.getSalesClicks());
                sdCampaignReport.setDetailPageView14d(campaigm.getDetailPageViewsClicks());
                sdCampaignReport.setOrdersNewToBrand14d(campaigm.getNewToBrandPurchasesClicks());
                sdCampaignReport.setSalesNewToBrand14d(campaigm.getNewToBrandSalesClicks());
                sdCampaignReport.setUnitsOrderedNewToBrand14d(campaigm.getNewToBrandUnitsSoldClicks());
            }
            list.add(sdCampaignReport);
        }
        return list;
    }


}
