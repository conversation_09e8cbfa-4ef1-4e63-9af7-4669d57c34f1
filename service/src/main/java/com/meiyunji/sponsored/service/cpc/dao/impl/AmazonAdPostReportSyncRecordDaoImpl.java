package com.meiyunji.sponsored.service.cpc.dao.impl;

import com.meiyunji.sponsored.common.springjdbc.AdBaseDaoImpl;
import com.meiyunji.sponsored.common.springjdbc.ObjectMapper;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdPostReportSyncRecordDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdPostReportSyncRecord;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * @author: nongwenhua
 * @email: <EMAIL>
 * @create: 2025-04-08 10:03
 */
@Repository
public class AmazonAdPostReportSyncRecordDaoImpl extends AdBaseDaoImpl<AmazonAdPostReportSyncRecord> implements IAmazonAdPostReportSyncRecordDao {
    @Override
    public void upsertPostReportSyncRecords(List<AmazonAdPostReportSyncRecord> postReportSyncRecords) {
        if (CollectionUtils.isEmpty(postReportSyncRecords)) {
            return;
        }
        StringBuilder sql = new StringBuilder("INSERT INTO ");
        sql.append(getJdbcHelper().getTable());
        sql.append(" (`puid`, `shop_id`, `post_profile_id`, `last_sync_day`, `next_execute_date`) values ");
        List<Object> argsList = new ArrayList<>();
        for (AmazonAdPostReportSyncRecord postReportSyncRecord : postReportSyncRecords) {
            sql.append(" (?, ?, ?, ?, ?),");
            argsList.add(postReportSyncRecord.getPuid());
            argsList.add(postReportSyncRecord.getShopId());
            argsList.add(postReportSyncRecord.getPostProfileId());
            argsList.add(postReportSyncRecord.getLastSyncDay());
            argsList.add(postReportSyncRecord.getNextExecuteDate());
        }
        sql.deleteCharAt(sql.length() - 1);
        sql.append(" ON DUPLICATE KEY UPDATE\n" +
                "    `update_time`=now()");
        getJdbcTemplate().update(sql.toString(), argsList.toArray());
    }

    @Override
    public List<AmazonAdPostReportSyncRecord> listByUniqueKeyAndExecuteTime(Integer puid, Integer shopId, String postProfileId, Date date, Boolean isInitFinish) {
        StringBuilder sql = new StringBuilder("select puid,shop_id from ");
        sql.append(getJdbcHelper().getTable());
        sql.append(" where next_execute_date <= ? ");
        List<Object> argsList = new ArrayList<>();
        argsList.add(date);
        if (Objects.nonNull(puid)) {
            sql.append(" and puid = ? ");
            argsList.add(puid);
        }
        if (Objects.nonNull(shopId)) {
            sql.append(" and shop_id = ? ");
            argsList.add(shopId);
        }
        if (Objects.nonNull(postProfileId)) {
            sql.append(" and post_profile_id = ? ");
            argsList.add(postProfileId);
        }
        if (Objects.nonNull(isInitFinish)) {
            sql.append(" and is_init_finish = ? ");
            argsList.add(isInitFinish);
        }
        sql.append(" group by puid,shop_id ");
        return getJdbcTemplate().query(sql.toString(), argsList.toArray(), getMapper());
    }

    @Override
    public List<AmazonAdPostReportSyncRecord> listByPuidAndShopId(Integer puid, Integer shopId) {
        StringBuilder sql = new StringBuilder("select * from ");
        sql.append(getJdbcHelper().getTable());
        sql.append(" where puid = ? and shop_id = ? ");
        List<Object> argsList = new ArrayList<>();
        argsList.add(puid);
        argsList.add(shopId);
        return getJdbcTemplate().query(sql.toString(), argsList.toArray(), getMapper());
    }

    @Override
    public void updateLastSyncDate(Long id, String metricDate) {
        StringBuilder sql = new StringBuilder("update ");
        sql.append(getJdbcHelper().getTable());
        sql.append(" set last_sync_day = ?, update_time=now(3) where id = ? ");
        List<Object> argsList = new ArrayList<>();
        argsList.add(metricDate);
        argsList.add(id);
        getJdbcTemplate().update(sql.toString(), argsList.toArray());
    }

    @Override
    public void updateNextExecuteTimeAndInitFinish(Long id, Date nextExecuteTime) {
        StringBuilder sql = new StringBuilder("update ");
        sql.append(getJdbcHelper().getTable());
        sql.append(" set next_execute_date = ?, is_init_finish = 1, update_time=now(3) where id = ? ");
        List<Object> argsList = new ArrayList<>();
        argsList.add(nextExecuteTime);
        argsList.add(id);
        getJdbcTemplate().update(sql.toString(), argsList.toArray());
    }
}
