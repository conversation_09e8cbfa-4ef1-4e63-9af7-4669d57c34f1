package com.meiyunji.sponsored.service.cpc.service2.sp.impl;

import com.amazon.advertising.mode.StrategyEnum;
import com.amazon.advertising.mode.TargetingTypeEnum;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.util.ThreadPoolUtil;
import com.meiyunji.sponsored.rpc.sp.campaign.CampaignResp;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdProfileDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdCampaign;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdCampaignAll;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdProfile;
import com.meiyunji.sponsored.service.cpc.service2.sp.ICpcSpCampaignService;
import com.meiyunji.sponsored.service.cpc.vo.CampaignVo;
import com.meiyunji.sponsored.service.log.enums.OperationLogResultEnum;
import com.meiyunji.sponsored.service.log.po.AdManageOperationLog;
import com.meiyunji.sponsored.service.log.service.IAdManageOperationLogService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * SpCampaignService
 *
 * @Author: hejh
 * @Date: 2024/7/12 13:29
 */
@Service
@Slf4j
public class SpCampaignService {
    @Autowired
    private ICpcSpCampaignService cpcSpCampaignService;
    @Autowired
    private IAmazonAdProfileDao amazonAdProfileDao;
    @Autowired
    private IAdManageOperationLogService adManageOperationLogService;

    /**
     * 创建sp广告活动
     *
     * @param vo CampaignVo
     * @return campaignId
     */
    public CampaignResp createSpCampaign(CampaignVo vo, ShopAuth shop, AmazonAdProfile amazonAdProfile) {
        CampaignResp.Builder builder = CampaignResp.newBuilder();
        //1，参数校验
        String err = checkCreateCampaignParams(vo);
        if (StringUtils.isNotBlank(err)) {
            builder.setCampaignErrMsg(err);
            builder.setCode(Result.ERROR);
            return builder.build();
        }
        //2，创建广告活动
        Result<String> result = cpcSpCampaignService.createCampaignWithAuthed(vo, shop, amazonAdProfile);
        if (StringUtils.isNotBlank(result.getMsg())) {
            builder.setCode(Result.ERROR);
            builder.setCampaignErrMsg(result.getMsg());
        } else {
            builder.setCode(Result.SUCCESS);
            builder.setCampaignId(String.valueOf(result.getData()));
        }

        //3，记录操作日志到es
        CompletableFuture.runAsync(() -> printOperationLog(vo, result), ThreadPoolUtil.getCreateAdForEsExecutor()).exceptionally(e -> {
            log.error("createCampaign log to es error, ", e);
            return null;
        });

        return builder.build();
    }

    private String checkCreateCampaignParams(CampaignVo vo) {
        if (vo == null) {
            return "请求参数为空";
        }
        if (StringUtils.isBlank(vo.getName())) {
            return "活动名称为空";
        }
        if (StringUtils.isBlank(vo.getStartDateStr())) {
            return "开始日期为空";
        }
        if (vo.getDailyBudget() == null) {
            return "每日预算为空";
        }
        if (TargetingTypeEnum.fromValue(vo.getTargetingType()) == null) {
            return "投放类型错误";
        }
        if (StrategyEnum.fromValue(vo.getStrategy()) == null && !"ruleBased".equalsIgnoreCase(vo.getStrategy())) {
            return "竞价策略错误";
        }
        if (vo.getName().length() > AmazonAdCampaign.nameLimit) {
            return MessageFormat.format("活动名称不能超过{0}个字符", AmazonAdCampaign.nameLimit);
        }
        return null;
    }

    /**
     * 记录操作日志
     *
     * @param vo     vo
     * @param result result
     */
    private void printOperationLog(CampaignVo vo, Result<String> result) {
        log.info("record operation log, CampaignVo:{}, result:{}", vo, result);
        AmazonAdProfile amazonAdProfile = amazonAdProfileDao.getProfile(vo.getPuid(), vo.getShopId());
        AmazonAdCampaignAll amazonAdCampaign = cpcSpCampaignService.convertVoToCreatePo(vo, amazonAdProfile);
        List<AdManageOperationLog> adManageOperationLogs = new ArrayList<>();
        AdManageOperationLog adManageOperationLog = adManageOperationLogService.getAdManageOperationLog(null, amazonAdCampaign);
        adManageOperationLog.setIp(vo.getLoginIp());
        if (OperationLogResultEnum.SUCCESS.getResultValue().equals(result.getCode())) {
            adManageOperationLog.setResult(OperationLogResultEnum.SUCCESS.getResultValue());
            adManageOperationLog.setCampaignId(result.getData() == null ? null : result.getData());
        } else {
            adManageOperationLog.setResult(OperationLogResultEnum.FAIL.getResultValue());
            adManageOperationLog.setResultInfo(result.getMsg());
        }
        adManageOperationLogs.add(adManageOperationLog);
        adManageOperationLogService.printAdOperationLog(adManageOperationLogs);
    }
}
