package com.meiyunji.sponsored.service.strategyTask.po;

import com.meiyunji.sponsored.common.springjdbc.DbColumn;
import com.meiyunji.sponsored.common.springjdbc.DbTable;
import lombok.Data;

import java.io.Serializable;

/**
 * @author: chenzimeng
 * @email: <EMAIL>
 * @date: 2024-01-08  09:33
 */
@DbTable(value = "t_advertise_strategy_task_sequence")
@Data
public class AdvertiseStrategyTaskSequence implements Serializable {
    @DbColumn(value = "id", key = true, autoIncrement = true)
    private Long id;

    @DbColumn(value = "stub")
    private String stub;
}
