package com.meiyunji.sponsored.service.monitor.service;

import com.meiyunji.sponsored.service.monitor.enums.MonitorPageFunctionEnum;
import com.meiyunji.sponsored.service.monitor.enums.MonitorTypeEnum;

import java.io.IOException;

/**
 * @description: some desc
 * @author: pxq
 * @email: <EMAIL>
 * @date: 2023/7/26 6:52
 */
public interface IAmazonAdListMonitorService {
    void handleMonitorData(Object[] args, Object result, MonitorPageFunctionEnum monitorPageFunctionEnum, MonitorTypeEnum monitorTypeEnum, boolean monitorAnalysis);

    void compareMonitorData() throws IOException;

    void deleteMonitorData() throws IOException;
}
