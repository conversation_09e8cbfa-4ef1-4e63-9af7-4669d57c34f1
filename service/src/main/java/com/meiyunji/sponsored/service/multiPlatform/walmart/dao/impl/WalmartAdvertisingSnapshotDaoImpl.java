package com.meiyunji.sponsored.service.multiPlatform.walmart.dao.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meiyunji.sponsored.common.springjdbc.AdBaseDaoImpl;
import com.meiyunji.sponsored.common.springjdbc.ConditionBuilder;
import com.meiyunji.sponsored.common.util.SqlStringUtil;
import com.meiyunji.sponsored.common.util.StringUtil;
import com.meiyunji.sponsored.service.multiPlatform.walmart.dao.IWalmartAdvertisingSnapshotDao;
import com.meiyunji.sponsored.service.multiPlatform.walmart.po.WalmartAdvertisingCampaign;
import com.meiyunji.sponsored.service.multiPlatform.walmart.po.WalmartAdvertisingGroup;
import com.meiyunji.sponsored.service.multiPlatform.walmart.po.WalmartAdvertisingSnapshot;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.support.GeneratedKeyHolder;
import org.springframework.jdbc.support.KeyHolder;
import org.springframework.stereotype.Repository;

import java.sql.PreparedStatement;
import java.sql.Statement;
import java.util.*;

/**
 * @author: pxq
 * @date: 2025/02/24
 * @Description: walmart广告快照任务Dao
 */
@Slf4j
@Repository
public class WalmartAdvertisingSnapshotDaoImpl extends AdBaseDaoImpl<WalmartAdvertisingSnapshot> implements IWalmartAdvertisingSnapshotDao {


    @Override
    public int add(WalmartAdvertisingSnapshot advertisingSnapshot) {
        String sql = "insert into t_walmart_advertising_snapshot (puid, shop_id, snapshot_id,job_status,`type`,`state`,`error`,create_time,update_time )" +
                " values (?,?,?,?,?,?,?,now(),now())";
        KeyHolder keyHolder = new GeneratedKeyHolder();
        getJdbcTemplate().update(connection -> {
            int i = 0;
            PreparedStatement ps = connection.prepareStatement(sql, Statement.RETURN_GENERATED_KEYS);
            ps.setInt(++i, advertisingSnapshot.getPuid());
            ps.setLong(++i, advertisingSnapshot.getShopId());
            ps.setString(++i, advertisingSnapshot.getSnapshotId());
            ps.setString(++i, advertisingSnapshot.getJobStatus());
            ps.setInt(++i, advertisingSnapshot.getType());
            ps.setInt(++i, advertisingSnapshot.getState());
            ps.setString(++i, advertisingSnapshot.getError());
            return ps;
        }, keyHolder);
        return keyHolder.getKey() == null ? 0 : keyHolder.getKey().intValue();
    }

    @Override
    public int addIncludeUuid(WalmartAdvertisingSnapshot advertisingSnapshot) {
        StringBuilder sb = new StringBuilder();
        sb.append("insert into t_walmart_advertising_snapshot (puid, shop_id, snapshot_id,job_status,`type`,`state`,`error`, `uuid`,create_time,update_time )");
        sb.append("  values (?,?,?,?,?,?,?,?,now(),now())");
        List<Object> argsList = new ArrayList<>();
        argsList.add(advertisingSnapshot.getPuid());
        argsList.add(advertisingSnapshot.getShopId());
        argsList.add(advertisingSnapshot.getSnapshotId());
        argsList.add(advertisingSnapshot.getJobStatus());
        argsList.add(advertisingSnapshot.getType());
        argsList.add(advertisingSnapshot.getState());
        argsList.add(advertisingSnapshot.getError());
        argsList.add(advertisingSnapshot.getUuid());
        return update(getJdbcTemplate(), sb.toString(), argsList.toArray());
    }

    @Override
    public int update(WalmartAdvertisingSnapshot advertisingSnapshot) {
        return updateTable(getParamMap(advertisingSnapshot),
                "where `id`=" + advertisingSnapshot.getId());
    }

    private Map<String, Object> getParamMap(WalmartAdvertisingSnapshot advertisingSnapshot) {
        Map<String, Object> params = Maps.newHashMap();
        params.put("`job_status`", advertisingSnapshot.getJobStatus());
        params.put("`update_time`", "now()");
        return params;
    }


    @Override
    public int updateState(Long id, Integer state, String jobStatus, String error) {
        StringBuilder sql = new StringBuilder("update t_walmart_advertising_snapshot set state = ?,");
        List<Object> args = new ArrayList<>();
        args.add(state);
        if (StringUtils.isNotBlank(jobStatus)) {
            sql.append(" job_status = ?,");
            args.add(jobStatus);
        }
        if (error != null) {
            sql.append(" error = ?,");
            args.add(error);
        }
        sql.append(" update_time = now() where id = ? ");
        args.add(id);
        return getJdbcTemplate().update(sql.toString(), args.toArray());
    }

    @Override
    public WalmartAdvertisingSnapshot getById(Long id) {
        String sql = "select * from `t_walmart_advertising_snapshot` where id = " + id;
        List<WalmartAdvertisingSnapshot> list = getJdbcTemplate().query(sql, getRowMapper());
        if (list != null && list.size() > 0) {
            return list.get(0);
        }
        return null;
    }

    @Override
    public WalmartAdvertisingSnapshot getBySnapshotId(String snapshotId) {
        List<Object> argsList = Lists.newArrayList();
        StringBuilder sb = new StringBuilder("select * from ").append(" t_walmart_advertising_snapshot ")
                .append(" where snapshot_id = ? ");
        argsList.add(snapshotId);

        return getJdbcTemplate().queryForObject(sb.toString(), argsList.toArray(), getRowMapper());
    }

    @Override
    public int getUndoneCountByType(Integer type) {
        String sql = "select count(*) from t_walmart_advertising_snapshot where `type` = ? and state in(0,1)";
        return getJdbcTemplate().queryForObject(sql, new Object[]{type}, Integer.class);
    }

    @Override
    public int getUndoneCountByType(Integer puid, Integer shopId, Integer type) {
        String sql = "select count(*) from t_walmart_advertising_snapshot where puid = ? and shop_id = ? and `type` = ? and state in(0,1)";
        return getJdbcTemplate().queryForObject(sql, Integer.class, new Object[]{puid, shopId, type});
    }


    @Override
    public List<Long> getUndonePuidAndShopIdByType(Integer puid, Integer shopId, Integer type) {
        String sql = "select id from t_walmart_advertising_snapshot where puid = ? and shop_id = ? and `type` = ? and state in(0,1)";
        return getJdbcTemplate().queryForList(sql, Long.class, new Object[]{puid, shopId, type});
    }

    @Override
    public List<Long> getUndoneId() {
        String sql = "select id from t_walmart_advertising_snapshot where state = 0 and create_time < DATE_SUB(NOW(), INTERVAL 5 MINUTE) ";
        return getJdbcTemplate().queryForList(sql, Long.class);
    }

    @Override
    public List<Long> getUndoneIdByTime(Date intervalTime) {
        if (Objects.isNull(intervalTime)) {
            return null;
        }
        List<Object> argsList = Lists.newArrayList();
        StringBuilder sql = new StringBuilder("select id from t_walmart_advertising_snapshot where state in (0,1) and type =5 ");
        sql.append(" and create_time < ?");
        argsList.add(intervalTime);
        return getJdbcTemplate().queryForList(sql.toString(), Long.class, argsList.toArray());
    }

    @Override
    public WalmartAdvertisingSnapshot getUnderwayByType(Integer type) {
        String sql = "select * from `t_walmart_advertising_snapshot` where `type` = ? and state in (1, 0)";
        List<WalmartAdvertisingSnapshot> list = getJdbcTemplate().query(sql, new Object[]{type}, getRowMapper());
        if (list != null && list.size() > 0) {
            return list.get(0);
        }
        return null;
    }

    @Override
    public List<WalmartAdvertisingSnapshot> getByUuid(Integer puid, List<Integer> shopIdList, String uuid) {
        List<Object> argsList = Lists.newArrayList();
        StringBuilder sb = new StringBuilder("select * from ").append(" t_walmart_advertising_snapshot ")
                .append(" where puid = ?");
        argsList.add(puid);
        if (CollectionUtils.isNotEmpty(shopIdList)) {
            sb.append(SqlStringUtil.dealInList("shop_id", shopIdList, argsList));
        }
        sb.append(" and uuid = ? ");
        argsList.add(uuid);
        return getJdbcTemplate().query(sb.toString(), new BeanPropertyRowMapper<>(WalmartAdvertisingSnapshot.class), argsList.toArray());
    }

    @Override
    public int deleteCreateGT7Day() {
        String sql = "delete from `t_walmart_advertising_snapshot` where  create_time < NOW() - INTERVAL 7 DAY LIMIT 5000";
        return getJdbcTemplate().update(sql);
    }
}
