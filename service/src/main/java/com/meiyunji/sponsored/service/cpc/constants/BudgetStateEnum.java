package com.meiyunji.sponsored.service.cpc.constants;

import com.meiyunji.sponsored.common.enums.BaseEnum;

/**
 * @author: liweibin
 * @email: <EMAIL>
 * @date: 2023-08-28  21:06
 */
public enum BudgetStateEnum implements BaseEnum {

    underBudget("under","未超过预算"),
    budgetExceeded("exceeded","已超预算"),
    ;

    BudgetStateEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    private String code;
    private String desc;

    @Override
    public String getCode() {
        return code;
    }

    @Override
    public String getDescription() {
        return desc;
    }
}
