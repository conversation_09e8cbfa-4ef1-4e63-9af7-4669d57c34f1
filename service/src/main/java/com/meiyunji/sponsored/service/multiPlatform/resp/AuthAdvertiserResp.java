package com.meiyunji.sponsored.service.multiPlatform.resp;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class AuthAdvertiserResp {

    //提示
    private String msg;

    //账号信息
    private List<AuthAdvertiser> list;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class AuthAdvertiser {
        //广告账号id
        private String advertiserId;

        //广告账号名称
        private String advertiserName;
    }
}
