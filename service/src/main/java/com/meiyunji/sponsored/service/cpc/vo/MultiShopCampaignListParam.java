package com.meiyunji.sponsored.service.cpc.vo;

import lombok.Data;

import java.util.List;

/**
 * @author: sun<PERSON><PERSON>
 * @email: <EMAIL>
 * @date: 2024-05-20  09:36
 */
@Data
public class MultiShopCampaignListParam {

    private Integer puid;
    //店铺id集合
    private List<Integer> shopIdList;
    //广告组合id集合
    private List<String> portfolioIdList;

    private String searchValue;
    //广告活动名称
    private List<String> searchValueList;
    //搜索类型，exact为精确匹配
    private String searchType;
    //活动状态(enabled，paused，archived)
    private List<String> stateList;
    //广告类型(sp,sb,sd)
    private List<String> adTypeList;

    private Integer pageNo;

    private Integer pageSize;
    //站点列表数组
    private List<String> marketplaceId;

    private String campaignId;

    private Integer shopId;
    //广告活动id集合
    private List<String> campaignIdList;
    //是否需要根据状态排序
    private Boolean needOrder;
    //广告活动投放类型
    private String adTargetType;
}
