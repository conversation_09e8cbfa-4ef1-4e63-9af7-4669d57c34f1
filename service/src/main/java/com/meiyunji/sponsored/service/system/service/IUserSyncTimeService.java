package com.meiyunji.sponsored.service.system.service;

import com.meiyunji.sponsored.common.springjdbc.IBaseService;
import com.meiyunji.sponsored.service.system.po.UserSyncTime;
import com.meiyunji.sponsored.service.system.po.UserSyncTypeEnum;
import com.meiyunji.sponsored.service.util.AmazonAdUtils;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface IUserSyncTimeService extends IBaseService<UserSyncTime> {
    /**
     * 保存或更新
     * @param puid puid
     * @param shopId shopId
     * @param mid marketplaceId
     * @param userSyncTypeEnum sync type
     * @return int
     */
    int saveOrUpdate(Integer puid, Integer shopId, String mid, UserSyncTypeEnum userSyncTypeEnum);

    int saveOrUpdateDate(Integer puid, Integer shopId, String mid, Date lastDate, UserSyncTypeEnum userSyncTypeEnum);

    List<UserSyncTime> getUserSyncTimeList(Integer puid, Integer shopId, Integer syncType);
    long getLastSyncTime(Integer puid, Integer shop, Integer syncType);

    boolean isCanSync(String amazonLastUpdateTime, long lastSyncTime);
}