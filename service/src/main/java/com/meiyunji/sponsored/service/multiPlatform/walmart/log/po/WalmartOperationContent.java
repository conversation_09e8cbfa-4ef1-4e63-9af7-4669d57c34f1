package com.meiyunji.sponsored.service.multiPlatform.walmart.log.po;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class WalmartOperationContent {
    /**
     * 直段名称
     */
    private String name;
    /**
     * 新值
     */
    private String newValue;
    /**
     * 旧值
     */
    private String previousValue;
    /**
     * title
     */
    private String title;

    // 24-07 操作日志调整，前端根据type字段渲染品牌、价格、星级范围
    private String type;
    private String brandDesc;
    private String priceDesc;
    private String ratingDesc;
    private String distribution;
    private String lookback;
    private Integer operatorId;

    public WalmartOperationContent(boolean initialize) {
        if(initialize){
            this.name = "";
            this.newValue = "";
            this.previousValue = "";
            this.title = "";
        }
    }
}
