package com.meiyunji.sponsored.service.cache.dao;

import com.meiyunji.sponsored.common.springjdbc.AdBaseDaoImpl;
import com.meiyunji.sponsored.common.util.SqlStringUtil;
import com.meiyunji.sponsored.service.cache.enums.PayPackTypeEnum;
import com.meiyunji.sponsored.service.cache.po.UserPlanType;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;

@Repository
public class UserPlanTypeDaoImpl extends AdBaseDaoImpl<UserPlanType> implements UserPlanTypeDao {

    @Override
    public List<Integer> selectPuidByPlanType(PayPackTypeEnum type, List<Integer> puid) {

        String sql = "select distinct puid from t_user_plan_type where id = puid and status != 2 and plan_type = ?";
        List<Object> args = new ArrayList<>();
        args.add(type.getValue());
        if (CollectionUtils.isNotEmpty(puid)) {
           sql = sql + SqlStringUtil.dealInList("puid", puid, args);
        }
        return getJdbcTemplate().queryForList(sql, args.toArray(), Integer.class);
    }
}
