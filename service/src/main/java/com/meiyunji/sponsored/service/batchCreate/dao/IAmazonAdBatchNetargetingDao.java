package com.meiyunji.sponsored.service.batchCreate.dao;

import com.meiyunji.sponsored.common.springjdbc.IBaseShardingDao;
import com.meiyunji.sponsored.service.batchCreate.dto.task.TaskStatusSetDto;
import com.meiyunji.sponsored.service.batchCreate.po.AmazonAdBatchCampaign;
import com.meiyunji.sponsored.service.batchCreate.po.AmazonAdBatchGroup;
import com.meiyunji.sponsored.service.batchCreate.po.AmazonAdBatchNetargeting;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @author: sunlin<PERSON>
 * @email: sunin<PERSON>@dianxiaomi.com
 * @date: 2023-11-16  09:30
 */
public interface IAmazonAdBatchNetargetingDao extends IBaseShardingDao<AmazonAdBatchNetargeting> {

    void insertList(Integer puid, List<AmazonAdBatchNetargeting> netargetingList);

    void updateStatusByTaskId(Integer puid, Integer shopId, Long taskId, List<Long> idList, Byte status);

    /**
     * 批量修改为失败状态
     */
    void updateErrTaskStatusByIdList(Integer puid, Map<Long, String> idErrMsgMap, boolean updateExecuteCount);

    /**
     * 根据广告组id列表获取id列表
     * @param puid
     * @param taskId
     * @param groupIdList
     * @return
     */
    List<Long> listIdByGroupIdList(Integer puid, Long taskId, List<Long> groupIdList);
    Map<Long, String> listIdByGroupIdListAndStatus(Integer puid, Long taskId, List<Long> groupIdList, List<Integer> status);

    void batchUpdateCampaignId(List<AmazonAdBatchCampaign> batchCampaignList);

    void batchUpdateAdGroupId(List<AmazonAdBatchGroup> batchGroupList);


    /**
     * 根据任务id获取需要创建的广告否定投放
     * @param puid
     * @param shopId
     * @param taskId
     * @param groupIdList
     * @param taskStatus
     * @return
     */
    List<AmazonAdBatchNetargeting> listByGroupIdList(Integer puid, Integer shopId, Long taskId, List<Long> groupIdList, List<Byte> taskStatus);

    void updateRetryTaskStatusByIdList(Integer puid, List<Long> idList, Date nextRetryTime);

    void updateSuccTaskStatusByIdList(Integer puid, Map<Long, String> idAdIdMap);

    List<AmazonAdBatchNetargeting> listByIdList(Integer puid, Integer shopId, List<Long> idList, List<Byte> taskStatus);

    List<TaskStatusSetDto> distinctStatusByTaskId(Integer puid, Integer shopId, Long taskId);

    int terminateByTaskId(Integer puid, Integer shopId, Long taskId, Byte status,
                          List<Byte> includeStatus, String errMsg);

    List<AmazonAdBatchNetargeting> selectNeedRetryAmazon(Integer puid, Integer shopId, Long taskId, List<Long> groupIds);

    int deleteByPuidAndShopId(Integer puid, Integer shopId, Integer limit);
}
