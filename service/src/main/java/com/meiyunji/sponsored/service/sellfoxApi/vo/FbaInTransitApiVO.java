package com.meiyunji.sponsored.service.sellfoxApi.vo;

import io.swagger.annotations.ApiModel;
import lombok.Data;

/**
 * fba在途详情
 */
@Data
@ApiModel
public class FbaInTransitApiVO {
    private String replenishUk;

    /**
     * Asin
     */
    private String asin;

    /**
     * Msku
     */
    private String msku;

    /**
     * 用来分组匹配
     */
    private String shopAsin;

    /**
     * 店铺信息
     */
    private ShopVO shopVO;

    /**
     * 发货单id
     */
    private Long shipOrderId;

    /**
     * 发货单号
     */
    private String shipNo;

    /**
     * 货件名称
     */
    private String shipmentName;

    /**
     * 货件单号
     */
    private String shipmentNo;

    /**
     * 数量
     */
    private Integer quantity;

    /**
     * 头程物流
     */
    private String logisticsName;

    /**
     * 头程物流时效天数
     */
    private Integer logisticsTimeLimit;

    /**
     * 货件创建时间
     */
    private String createDate;

    /**
     * 实际发货时间
     */
    private String realShippedDate;

    /**
     * 预计到货时间
     */
    private String expectArrivalDate;

    /**
     * 间隔今天时间
     */
    private Long betweenArrivalTime;
}
