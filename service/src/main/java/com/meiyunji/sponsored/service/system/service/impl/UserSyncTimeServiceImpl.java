package com.meiyunji.sponsored.service.system.service.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.meiyunji.sponsored.common.springjdbc.BaseServiceImpl;
import com.meiyunji.sponsored.common.util.JSONUtil;
import com.meiyunji.sponsored.service.system.dao.IUserSyncTimeDao;
import com.meiyunji.sponsored.service.system.po.UserSyncTime;
import com.meiyunji.sponsored.service.system.po.UserSyncTypeEnum;
import com.meiyunji.sponsored.service.system.service.IUserSyncTimeService;
import com.meiyunji.sponsored.service.util.AmazonAdUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
public class UserSyncTimeServiceImpl extends BaseServiceImpl<UserSyncTime> implements IUserSyncTimeService {
    @Resource
    private IUserSyncTimeDao userSyncTimeDao;

    @Override
    public int saveOrUpdate(Integer puid, Integer shopId, String mid, UserSyncTypeEnum userSyncTypeEnum) {
        int t = userSyncTimeDao.updateSyncTime(puid, shopId, mid, userSyncTypeEnum.getType());

        if (t == 0) {
            try {
                t = userSyncTimeDao.saveSyncTime(puid, shopId, mid, userSyncTypeEnum.getType());
            } catch (Exception e) {
                t = userSyncTimeDao.updateSyncTime(puid, shopId, mid, userSyncTypeEnum.getType());
            }
        }

        return t;
    }

    @Override
    public int saveOrUpdateDate(Integer puid, Integer shopId, String mid, Date lastDate, UserSyncTypeEnum userSyncTypeEnum) {
        int t = userSyncTimeDao.updateSyncTimeDate(puid, shopId, mid, userSyncTypeEnum.getType(),lastDate);

        if (t == 0) {
            try {
                t = userSyncTimeDao.saveSyncTimeDate(puid, shopId, mid, userSyncTypeEnum.getType(),lastDate);
            } catch (Exception e) {
                t = userSyncTimeDao.updateSyncTimeDate(puid, shopId, mid, userSyncTypeEnum.getType(),lastDate);
            }
        }

        return t;
    }

    @Override
    public List<UserSyncTime> getUserSyncTimeList(Integer puid, Integer shopId, Integer syncType) {
        return userSyncTimeDao.getUserSyncTime(puid, shopId, syncType);
    }

    @Override
    public long getLastSyncTime(Integer puid, Integer shop, Integer syncType) {
        List<UserSyncTime> userSyncTimeList = new ArrayList<>();
        long lastSyncTime = 0; // 记录最近一次同步数据的时间
        for (UserSyncTime userSyncTime : userSyncTimeList) {
            lastSyncTime = userSyncTime.getSyncLastTime().getTime() > lastSyncTime ? userSyncTime.getSyncLastTime().getTime() : lastSyncTime;
        }
        return lastSyncTime;
    }

    @Override
    public boolean isCanSync(String amazonLastUpdateTime, long lastSyncTime) {
        long syncBufferTime = 60 * 60 * 1000; // 同步数据缓冲时间1h
        if(AmazonAdUtils.pacificTimeToBjTime(amazonLastUpdateTime) > lastSyncTime - syncBufferTime) {
            return true;
        }
        return false;
    }

}