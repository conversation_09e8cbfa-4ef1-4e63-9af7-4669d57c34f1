package com.meiyunji.sponsored.service.cpc.service2;


import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.rpc.adCommon.AllProductAggregateDataResponse;
import com.meiyunji.sponsored.rpc.adCommon.AllProductDataResponse;
import com.meiyunji.sponsored.rpc.vo.NeKeywordsPageRpcVo;
import com.meiyunji.sponsored.rpc.vo.NeTargetingPageRpcVo;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.cpc.vo.AdProductPageParam;
import com.meiyunji.sponsored.service.cpc.vo.AdProductPageVo;
import com.meiyunji.sponsored.service.cpc.vo.NeKeywordsPageParam;
import com.meiyunji.sponsored.service.cpc.vo.NeTargetingPageParam;

import java.util.List;


/**
 *
 * <AUTHOR>
 * @date 2023/02/06
 * 微信-广告组相关业务接口
 */
public interface IWxCpcGroupService {

    /**
     * 广告产品首页数据
     * @param puid
     * @param param
     * @return
     */
    AllProductDataResponse.AdProductHomeVo getAllProductData(int puid, AdProductPageParam param);

    /**
     * 广告产品汇总数据
     * @param puid
     * @param param
     * @return
     */
    AllProductAggregateDataResponse.AdProductHomeVo getAllProductAggregateData(int puid, AdProductPageParam param);

    /**
     * 获取所有SP广告产品数据
     * @param shopAuth
     * @param puid
     * @param param
     * @param voPage
     * @param isExport
     * @return
     */
    List<AdProductPageVo> getSpProductVoList(ShopAuth shopAuth, Integer puid, AdProductPageParam param, Page<AdProductPageVo> voPage, boolean isExport);

    /**
     * 获取所有SD广告产品数据
     * @param shopAuth
     * @param puid
     * @param param
     * @param voPage
     * @param isExport
     * @return
     */
    List<AdProductPageVo> getSdProductVoList(ShopAuth shopAuth, Integer puid, AdProductPageParam param, Page<AdProductPageVo> voPage, boolean isExport);

    /**
     * 查询所有否定关键词 包含sp sb
     * @param param
     * @return
     */
    Page<NeKeywordsPageRpcVo> getAllNeKeywordPageList(NeKeywordsPageParam param);

    /**
     * 所有类型否定投放
     * @param puid
     * @param param
     * @return
     */
    Page<NeTargetingPageRpcVo> getAllNeTargetingPageList(int puid, NeTargetingPageParam param);
}
