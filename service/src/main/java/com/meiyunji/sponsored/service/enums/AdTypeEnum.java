package com.meiyunji.sponsored.service.enums;

/**
 * @author: wade
 * @date: 2021/9/8 17:18
 * @describe:
 */
public enum AdTypeEnum {

    /**
     * 广告活动 推广类型
     * */
    sp("sp", "SP", "产品推广"),

    sb("sb","SB", "品牌推广"),

    sd("sd","SD", "展示型推广");

    private String campaignType;

    private String campaignTypeUp;

    private String campaignValue;

    public static String getCampaignValue(String campaignType){
        AdTypeEnum[] values = values();
        for (AdTypeEnum value : values) {
            if(value.getCampaignType().equals(campaignType)){
                return value.getCampaignValue();
            }
        }
        return "";
    }

    AdTypeEnum(String campaignType, String campaignTypeUp, String campaignValue) {
        this.campaignType = campaignType;
        this.campaignTypeUp = campaignTypeUp;
        this.campaignValue = campaignValue;
    }

    public String getCampaignType() {
        return campaignType;
    }


    public String getCampaignValue() {
        return campaignValue;
    }

    public String getCampaignTypeUp() {
        return campaignTypeUp;
    }
}

