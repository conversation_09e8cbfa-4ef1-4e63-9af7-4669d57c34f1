package com.meiyunji.sponsored.service.adTagSystem.po;

import com.meiyunji.sponsored.common.base.BasePo;
import com.meiyunji.sponsored.common.springjdbc.DbColumn;
import com.meiyunji.sponsored.common.springjdbc.DbTable;
import lombok.Data;

/**
 * @author: liweibin
 * @email: <EMAIL>
 * @date: 2024-10-22  22:44
 * 广告标签与层级管理表
 */
@Data
@DbTable(value = "t_ad_manage_tag_relation")
public class AdManageTagRelation extends BasePo {
    @DbColumn(value = "id")
    private Long id;

    @DbColumn(value = "puid")
    private Integer puid;

    //com.meiyunji.sponsored.service.adTagSystem.enums.AdManageTagTypeEnum
    @DbColumn(value = "type")
    private Integer type;

    @DbColumn(value = "tag_id")
    private Long tagId;

    @DbColumn(value = "relation_id")
    private String relationId;

    @DbColumn(value = "shop_id")
    private Integer shopId;

    @DbColumn(value = "group_id")
    private Long groupId;

    @DbColumn(value = "del_flag")
    private Long delFlag;

    @DbColumn(value = "create_id")
    private Integer createId;

    @DbColumn(value = "update_id")
    private Integer updateId;
}
