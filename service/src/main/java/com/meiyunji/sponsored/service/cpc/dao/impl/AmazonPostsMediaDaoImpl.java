package com.meiyunji.sponsored.service.cpc.dao.impl;

import com.meiyunji.sponsored.common.springjdbc.BaseShardingDaoImpl;
import com.meiyunji.sponsored.common.util.SqlStringUtil;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonPostsMediaDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonPost;
import com.meiyunji.sponsored.service.cpc.po.AmazonPostMedia;
import com.meiyunji.sponsored.service.post.po.MediaPo;
import com.meiyunji.sponsored.service.post.po.ProductPo;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;

@Repository
public class AmazonPostsMediaDaoImpl extends BaseShardingDaoImpl<AmazonPostMedia> implements IAmazonPostsMediaDao {
    @Override
    public void upsertPostMedias(List<AmazonPostMedia> postMedias) {
        if (CollectionUtils.isEmpty(postMedias)) {
            return;
        }
        int puid = postMedias.get(0).getPuid();
        StringBuilder sql = new StringBuilder("INSERT INTO ");
        sql.append(getJdbcHelper().getTable());
        sql.append(" (`puid`, `shop_id`, `post_profile_id`, `post_id`, `media_id`, `media_url`, `media_type`, `ai_generated`, `ai_featured_asins`, `creator_content`, `entity_has_asset_permission`) values ");
        List<Object> argsList = new ArrayList<>(postMedias.size());
        for (AmazonPostMedia postMedia : postMedias) {
            sql.append(" (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?),");
            argsList.add(postMedia.getPuid());
            argsList.add(postMedia.getShopId());
            argsList.add(postMedia.getPostProfileId());
            argsList.add(postMedia.getPostId());
            argsList.add(postMedia.getMediaId());
            argsList.add(postMedia.getMediaUrl());
            argsList.add(postMedia.getMediaType());
            argsList.add(postMedia.getAiGenerated());
            argsList.add(postMedia.getAiFeaturedAsins());
            argsList.add(postMedia.getCreatorContent());
            argsList.add(postMedia.getEntityHasAssetPermission());
        }
        sql.deleteCharAt(sql.length() - 1);
        sql.append(" ON DUPLICATE KEY UPDATE\n" +
                "    `media_url` = VALUES(`media_url`),\n" +
                "    `media_type` = VALUES(`media_type`),\n" +
                "    `ai_generated` = VALUES(`ai_generated`),\n" +
                "    `ai_featured_asins` = VALUES(`ai_featured_asins`),\n" +
                "    `creator_content` = VALUES(`creator_content`),\n" +
                "    `entity_has_asset_permission` = VALUES(`entity_has_asset_permission`),\n" +
                "    `update_time`=now()");
        getJdbcTemplate(puid).update(sql.toString(), argsList.toArray());
    }

    @Override
    public List<MediaPo> getMediaInfo(Integer puid, List<Integer> shopIds, Set<String> postProfileIds, List<String> postIds) {
        StringBuilder selectSql = new StringBuilder("select post_id postId, post_profile_id postProfileId, media_id mediaId, media_type mediaType from ").append(getJdbcHelper().getTable());
        List<Object> argsList = new ArrayList<>();
        selectSql.append(" where puid = ? ");
        argsList.add(puid);
        selectSql.append(SqlStringUtil.dealInList("shop_id", shopIds, argsList));
        selectSql.append(SqlStringUtil.dealInList("post_profile_id", new ArrayList<>(postProfileIds), argsList));
        selectSql.append(SqlStringUtil.dealInList("post_id", postIds, argsList));
        return getJdbcTemplate(puid).query(selectSql.toString(), argsList.toArray(), new BeanPropertyRowMapper<>(MediaPo.class));
    }
}
