package com.meiyunji.sponsored.service.attribution.service.impl;

import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.attribution.api.AttributionApiClient;
import com.meiyunji.sponsored.service.attribution.dao.IAmazonAdAttributionTagDao;
import com.meiyunji.sponsored.service.attribution.entity.AmazonAdAttributionTag;
import com.meiyunji.sponsored.service.attribution.service.IAmazonAdAttributionTagService;
import com.meiyunji.sponsored.service.cpc.util.CpcApiHelper;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * @author: wade
 * @date: 2022/3/12 17:25
 * @describe:
 */
@Service
public class AmazonAdAttributionTagService implements IAmazonAdAttributionTagService {

    private final IAmazonAdAttributionTagDao amazonAdAttributionTagDao;
    private final AttributionApiClient attributionApiClient;

    public AmazonAdAttributionTagService(IAmazonAdAttributionTagDao amazonAdAttributionTagDao, AttributionApiClient attributionApiClient) {
        this.amazonAdAttributionTagDao = amazonAdAttributionTagDao;
        this.attributionApiClient = attributionApiClient;
    }

    @Override
    public AmazonAdAttributionTag getTag(ShopAuth shopAuth, String profileId, String publisherId, String advertiserId, Boolean isMacronTag) {
        AmazonAdAttributionTag tag = amazonAdAttributionTagDao.getTag(shopAuth.getPuid(), shopAuth.getId(), publisherId, advertiserId);
        if (tag != null) {
            return tag;
        }
        //请求接口
        if (isMacronTag) {
            Map<String, Map<String, String>> macroTagsMap = attributionApiClient.getMacroTags(shopAuth, profileId, shopAuth.getMarketplaceId(), publisherId, advertiserId);
            if (MapUtils.isNotEmpty(macroTagsMap)) {
                Map<String, String> map = macroTagsMap.get(advertiserId);
                if (MapUtils.isNotEmpty(map) && StringUtils.isNotBlank(map.get(publisherId))) {
                    AmazonAdAttributionTag amazonAdAttributionTag = AmazonAdAttributionTag.builder()
                            .puid(shopAuth.getPuid())
                            .marketplaceId(shopAuth.getMarketplaceId())
                            .shopId(shopAuth.getId())
                            .advertiserId(advertiserId)
                            .macroEnable(1)
                            .publisherId(publisherId)
                            .tag(map.get(publisherId)).build();
                    amazonAdAttributionTagDao.insertOrUpdate(shopAuth.getPuid(), Collections.singletonList(amazonAdAttributionTag));
                    return amazonAdAttributionTag;
                }
            }
        } else {
            Map<String, Map<String, String>> macroTags = attributionApiClient.getNonMacroTags(shopAuth, profileId, shopAuth.getMarketplaceId(), publisherId, advertiserId);
            if (MapUtils.isNotEmpty(macroTags)) {
                Map<String, String> map = macroTags.get(advertiserId);
                if (MapUtils.isNotEmpty(map) && StringUtils.isNotBlank(map.get(publisherId))) {
                    AmazonAdAttributionTag amazonAdAttributionTag = AmazonAdAttributionTag.builder()
                            .puid(shopAuth.getPuid())
                            .marketplaceId(shopAuth.getMarketplaceId())
                            .shopId(shopAuth.getId())
                            .advertiserId(advertiserId)
                            .macroEnable(0)
                            .publisherId(publisherId)
                            .tag(map.get(publisherId)).build();
                    amazonAdAttributionTagDao.insertOrUpdate(shopAuth.getPuid(), Collections.singletonList(amazonAdAttributionTag));
                    return amazonAdAttributionTag;
                }
            }
        }
        return null;
    }
}
