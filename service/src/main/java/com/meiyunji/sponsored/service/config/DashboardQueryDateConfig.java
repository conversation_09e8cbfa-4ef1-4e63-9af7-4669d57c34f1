package com.meiyunji.sponsored.service.config;

import com.meiyunji.sponsored.common.util.StringUtil;
import com.meiyunji.sponsored.service.newDashboard.enums.DashboardModuleEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;


/**
 * 全景仪查询时间最大上限配置
 * <AUTHOR>
 * @date 2024/12/09
 */
@RefreshScope
@Configuration
@Slf4j
public class DashboardQueryDateConfig {


    @Value("#{T(com.meiyunji.sponsored.service.config.DashboardQueryDateConfig).splitQueryTimeToMap('${dashboard.quryTimeLimit:}')}")
    private Map<String, Integer> queryTime;

    public Integer getDashboardQueryTime(String query) {
        if (queryTime != null) {
            return queryTime.get(query);
        }
        return null;
    }

    public Map<String, Integer> getDashboardQueryTimeAll() {
        if (queryTime == null) {
            Map<String, Integer> map = new HashMap<>();
            for (DashboardModuleEnum dashboardModuleEnum :DashboardModuleEnum.values()) {
                map.put(dashboardModuleEnum.name(), dashboardModuleEnum.getQueryTimeLimit());
            }
            return map;
        }
        return queryTime;
    }


    public static Map<String, Integer> splitQueryTimeToMap(String str) {
        Map<String, Integer> map = new HashMap<>();
        for (DashboardModuleEnum dashboardModuleEnum :DashboardModuleEnum.values()) {
            map.put(dashboardModuleEnum.name(), dashboardModuleEnum.getQueryTimeLimit());
        }
        try {
            if (StringUtils.isBlank(str)) {
                return map;
            }
            List<String> strings = StringUtil.splitStr(str, ",");
            for (String s : strings) {
                String trim = s.trim();
                List<String> e = StringUtil.splitStr(trim, ":");
                String key = e.get(0).trim();
                Integer value = Integer.valueOf(e.get(1).trim());
                Integer oldValue = map.get(key);
                if (oldValue == null || oldValue < value) {
                    map.put(key, value);
                }
            }
            return map;
        } catch (Exception e) {
            log.error("传入字符串为：" + str + " ------- 错误信息为：", e);
            return map;
        }
    }

}
