package com.meiyunji.sponsored.service.autoRule.enums;

/**
 * @author: <PERSON><PERSON><PERSON>
 * @email: sun<PERSON><EMAIL>
 * @date: 2024-08-14  20:37
 */

/**
 * 添加受控对象，待添加列表，需要类似广告管理展示投放的信息
 * 自动和关键词直接展示词即可
 * 类目需要展示类目名称，品牌，价格，星级，配送
 * asin需要展示asin，标题，图片
 * 受众需要展示受众类型，类目，品牌，价格，星级，配送，回溯期等
 */
public enum QueryPageAdKeywordRespTypeEnum {
    //asin
    asin,
    //类目
    category,
    //自动
    auto,
    //关键词
    keyword,
    //受众
    audience
}
