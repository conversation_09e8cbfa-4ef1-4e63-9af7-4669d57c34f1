package com.meiyunji.sponsored.service.newDashboard.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.meiyunji.sponsored.service.newDashboard.dto.base.DashboardAdCalDataDto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * @author: ys
 * @date: 2024/4/24 20:33
 * @describe:
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class DashboardAdTargetingMatrixDto  extends DashboardAdCalDataDto {
    private String targetId;
    private String name;
    private String matchType;
    private String shopName;
    private String campaignName;
    private String adGroupName;
    private String type;
    private String targetText;
    private String title;
    private String brandName;
    private String categoryName;
    private String imgUrl;
    private String expressionType;
    private String campaignId;
    private String marketplaceId;
    private String adGroupId;
    private Integer shopId;
    private String campaignType;
    private String query;
    private String price;
    private String rating;
}
