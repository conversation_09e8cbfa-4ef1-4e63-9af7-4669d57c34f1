package com.meiyunji.sponsored.service.reportHour.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.meiyunji.sponsored.common.util.MathUtil;
import com.meiyunji.sponsored.service.excel.excelTools.ExportFormat;
import com.meiyunji.sponsored.service.excel.excelTools.ExportFormatType;
import com.meiyunji.sponsored.service.excel.excelTools.converter.StringConverter;
import com.meiyunji.sponsored.service.util.ReflectionUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.experimental.SuperBuilder;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Objects;
import java.util.Optional;

/**
 * @description: some desc
 * @author: pxq
 * @email: <EMAIL>
 * @date: 2022/12/1 20:45
 */
@Data
@SuperBuilder
@AllArgsConstructor
public class AdCampaignHourVo extends AdAnalysisAndCompareVo {

    private String time;
    private String date;
    @ExcelProperty(value = "小时")
    private String label;
    /**
     * 广告花费/广告订单量
     */
    @ExcelProperty(value = "每笔订单花费")
    @ExportFormat(value = ExportFormatType.CURRENCY)
    private BigDecimal cpa;
    @ExcelProperty(value = "CPC", converter = StringConverter.class)
    @ExportFormat(value = ExportFormatType.CURRENCY)
    private BigDecimal adCostPerClick;
    @ExcelProperty(value = "广告点击率", converter = StringConverter.class)
    private BigDecimal ctr;
    @ExcelProperty(value = "广告转化率", converter = StringConverter.class)
    private BigDecimal  cvr;
    @ExcelProperty(value = "ACoS", converter = StringConverter.class)
    private BigDecimal acos;
    @ExcelProperty(value = "ROAS")
    private BigDecimal roas;
    @ExcelProperty(value = "ACOTS", converter = StringConverter.class)
    private BigDecimal acots;
    @ExcelProperty(value = "ASOTS", converter = StringConverter.class)
    private BigDecimal asots;
    @ExcelProperty(value = "本广告产品订单量")
    private Integer selfAdOrderNum;
    @ExcelProperty(value = "其他产品广告订单量")
    private Integer otherAdOrderNum;
    @ExcelProperty(value = "星期")
    private Integer weekDay;
    @ExcelProperty(value = "本广告产品销售额", converter = StringConverter.class)
    @ExportFormat(value = ExportFormatType.CURRENCY)
    private BigDecimal adSelfSale;
    @ExcelProperty(value = "其他产品广告销售额", converter = StringConverter.class)
    @ExportFormat(value = ExportFormatType.CURRENCY)
    private BigDecimal adOtherSale;
    @ExcelProperty(value = "本广告产品销量")
    private Integer adSelfSaleNum;
    @ExcelProperty(value = "其他产品广告销量")
    private Integer adOtherSaleNum;
    @ExcelProperty(value = "广告花费占比")
    private BigDecimal adCostPercentage;
    @ExcelProperty(value = "广告订单量占比")
    private BigDecimal adSalePercentage;
    @ExcelProperty(value = "广告销售额占比")
    private BigDecimal adOrderNumPercentage;
    @ExcelProperty(value = "广告销量占比")
    private BigDecimal orderNumPercentage;


    /**
     * 品牌新买家订单量
     */
    @ExcelProperty(value = "可见展示次数")
    private Integer viewableImpressions;
    /**
     * VTR
     */
    @ExcelProperty(value = "VTR")
    private BigDecimal vrt;
    /**
     * vCTR
     */
    @ExcelProperty(value = "vCTR")
    private BigDecimal vCtr;
    /**
     * 品牌新买家订单量
     */
    @ExcelProperty(value = "品牌新买家订单量")
    private Integer ordersNewToBrand;

    /**
     * 品牌新买家销量
     */
    @ExcelProperty(value = "品牌新买家销量")
    private Integer unitsOrderedNewToBrand;

    /**
     * 品牌新买家销售额
     */
    @ExcelProperty(value = "品牌新买家销售额", converter = StringConverter.class)
    @ExportFormat(value = ExportFormatType.CURRENCY)
    private BigDecimal salesNewToBrand;


    /**
     * 广告笔单价
     */
    @ExcelProperty(value = "广告笔单价", converter = StringConverter.class)
    private BigDecimal advertisingUnitPrice;

    /**
     * 本广告产品笔单价
     */
    @ExcelProperty(value = "本广告产品笔单价", converter = StringConverter.class)
    private BigDecimal advertisingProductUnitPrice;

    /**
     * 其它产品广告笔单价
     */
    @ExcelProperty(value = "其它产品广告笔单价", converter = StringConverter.class)
    private BigDecimal advertisingOtherProductUnitPrice;


    /**
     * 品牌新买家订单占比
     */
    @ExcelProperty(value = "品牌新买家订单占比")
    private BigDecimal ordersNewToBrandPercentage;

    /**
     * 品牌新买家销量占比
     */
    @ExcelProperty(value = "品牌新买家销量占比")
    private BigDecimal unitsOrderedNewToBrandPercentage;

    /**
     * 品牌新买家销售额
     */
    @ExcelProperty(value = "品牌新买家销售额占比")
    private BigDecimal salesNewToBrandPercentage;

    /**
     * vcpm 广告花费/展示次数*1000
     */
    @ExcelProperty(value = "vcpm")
    private BigDecimal vcpm;

    /**
     * vcpm 花费
     */
    private BigDecimal vcpmCost;
    /**
     * vcpm 展示
     */
    private Long vcpmImpressions;

    private Long totalImpressions;

    private Long totalClicks;

    //用户小时字段排序
    private Integer hour;

    /**
     * 统计非vcpm的本广告产品销售额
     */
    private BigDecimal totalAdSelfSale;
    /**
     * 统计非vcpm的广告销售额
     */
    private BigDecimal totalAdSale;

    private String budgetAdjust;

    private String budgetSurplus;

    private BigDecimal budgetAdjustMax;

    private BudgetUsage budgetUsage;


    //新增 对比

    /**
     * 每笔订单花费(对比)
     */
    private BigDecimal cpaCompare;

    /**
     * 每笔订单花费(比率)
     */
    private BigDecimal cpaCompareRate;



    /**
     * CPC(对比)
     */
    private BigDecimal adCostPerClickCompare;


    /**
     * CPC(比率)
     */
    private BigDecimal adCostPerClickCompareRate;


    /**
     * 广告点击率(对比)
     */
    private BigDecimal ctrCompare;

    /**
     * 广告点击率(比率)
     */
    private BigDecimal ctrCompareRate;


    /**
     * 广告转化率(对比)
     */
    private BigDecimal cvrCompare;


    /**
     * 广告转化率(比率)
     */
    private BigDecimal cvrCompareRate;



    /**
     * ACoS(对比)
     */
    private BigDecimal acosCompare;


    /**
     * ACoS(比率)
     */
    private BigDecimal acosCompareRate;



    /**
     * ROAS(对比)
     */
    private BigDecimal roasCompare;


    /**
     * ROAS(比率)
     */
    private BigDecimal roasCompareRate;


    /**
     * ACOTS(对比)
     */
    private BigDecimal acotsCompare;


    /**
     * ACOTS(比率)
     */
    private BigDecimal acotsCompareRate;

    /**
     * ASOTS(对比)
     */
    private BigDecimal asotsCompare;


    /**
     * ASOTS(比率)
     */
    private BigDecimal asotsCompareRate;


    /**
     * 本广告产品订单量(对比)
     */
    private Integer selfAdOrderNumCompare;


    /**
     * 本广告产品订单量(比率)
     */
    private BigDecimal selfAdOrderNumCompareRate;

    /**
     * 其他产品广告订单量(对比)
     */
    private Integer otherAdOrderNumCompare;


    /**
     * 其他产品广告订单量(比率)
     */
    private BigDecimal otherAdOrderNumCompareRate;

    /**
     * 本广告产品销售额(对比)
     */
    private BigDecimal adSelfSaleCompare;


    /**
     * 本广告产品销售额(比率)
     */
    private BigDecimal adSelfSaleCompareRate;



    /**
     * 其他产品广告销售额(对比)
     */
    private BigDecimal adOtherSaleCompare;


    /**
     * 其他产品广告销售额(比率)
     */
    private BigDecimal adOtherSaleCompareRate;

    /**
     * 本广告产品销量（对比）
     */
    private Integer adSelfSaleNumCompare;


    /**
     * 本广告产品销量（比率）
     */
    private BigDecimal adSelfSaleNumCompareRate;

    /**
     * 其他产品广告销量（对比）
     */
    private Integer adOtherSaleNumCompare;

    /**
     * 其他产品广告销量（比率）
     */
    private BigDecimal adOtherSaleNumCompareRate;



    public static BigDecimal calculateCompareRete(BigDecimal currVal, BigDecimal compareVal) {
        currVal = Optional.ofNullable(currVal).map(c -> c.setScale(2, RoundingMode.HALF_UP)).orElse(BigDecimal.ZERO);
        compareVal = Optional.ofNullable(compareVal).map(p -> p.setScale(2, RoundingMode.HALF_UP)).orElse(BigDecimal.ZERO);
        if (BigDecimal.ZERO.compareTo(compareVal) == 0 && Objects.nonNull(currVal) && BigDecimal.ZERO.compareTo(currVal) !=0) {
            return new BigDecimal(100);
        }
        BigDecimal subVal = MathUtil.subtract(currVal, compareVal);
        return MathUtil.divideByZeroScale2(subVal.multiply(BigDecimal.valueOf(100)), compareVal);
    }




    /**
     * 完成click等属性设置后，调用此方法为对比率进行设值
     */
    @Override
    public void afterPropertiesSet() {
        super.afterPropertiesSet();
//        this.cpaCompareRate = calculateCompareRete(this.cpa, this.cpaCompare);
        this.adCostPerClickCompareRate = calculateCompareRete(this.adCostPerClick, this.adCostPerClickCompare);
        this.ctrCompareRate = calculateCompareRete(this.ctr, this.ctrCompare);
        this.cvrCompareRate = calculateCompareRete(this.cvr, this.cvrCompare);
        this.acosCompareRate = calculateCompareRete(this.acos, this.acosCompare);
        this.roasCompareRate = calculateCompareRete(this.roas, this.roasCompare);
//        this.selfAdOrderNumCompareRate = calculateCompareRete(BigDecimal.valueOf(Optional.ofNullable(this.selfAdOrderNum).orElse(0)), BigDecimal.valueOf(Optional.ofNullable(this.selfAdOrderNumCompare).orElse(0)));
//        this.otherAdOrderNumCompareRate = calculateCompareRete(BigDecimal.valueOf(Optional.ofNullable(this.otherAdOrderNum).orElse(0)), BigDecimal.valueOf(Optional.ofNullable(this.otherAdOrderNumCompare).orElse(0)));
//        this.adSelfSaleCompareRate = calculateCompareRete(this.adSelfSale, this.adSelfSaleCompare);
//        this.adOtherSaleCompareRate = calculateCompareRete(this.adOtherSale, this.adOtherSaleCompare);
//        this.adSelfSaleNumCompareRate = calculateCompareRete(BigDecimal.valueOf(Optional.ofNullable(this.adSelfSaleNum).orElse(0)), BigDecimal.valueOf(Optional.ofNullable(this.adSelfSaleNumCompare).orElse(0)));
//        this.adOtherSaleNumCompareRate = calculateCompareRete(BigDecimal.valueOf(Optional.ofNullable(this.adOtherSaleNum).orElse(0)), BigDecimal.valueOf(Optional.ofNullable(this.adOtherSaleNumCompare).orElse(0)));
//        this.asotsCompareRate = calculateCompareRete(this.asots, this.asotsCompare);
    }




    @Data
    public static class BudgetUsage {
        private Boolean isNoData;
        private double percent;
        private double currentBudget;
    }

    public AdCampaignHourVo() {
        this.acos = BigDecimal.ZERO;
        this.cpa = BigDecimal.ZERO;
        this.adCostPerClick = BigDecimal.ZERO;
        this.asots = BigDecimal.ZERO;
        this.ctr = BigDecimal.ZERO;
        this.cvr = BigDecimal.ZERO;
        this.roas = BigDecimal.ZERO;
        this.acots = BigDecimal.ZERO;
        this.selfAdOrderNum = 0;
        this.otherAdOrderNum = 0;
        this.adSelfSale = BigDecimal.ZERO;
        this.adOtherSale = BigDecimal.ZERO;
        this.adSelfSaleNum = 0;
        this.adOtherSaleNum = 0;
        this.viewableImpressions = 0;
        this.vrt = BigDecimal.ZERO;
        this.vCtr = BigDecimal.ZERO;
        this.ordersNewToBrand = 0;
        this.unitsOrderedNewToBrand = 0;
        this.salesNewToBrand = BigDecimal.ZERO;
        this.advertisingUnitPrice = BigDecimal.ZERO;
        this.advertisingProductUnitPrice = BigDecimal.ZERO;
        this.advertisingOtherProductUnitPrice = BigDecimal.ZERO;
        this.ordersNewToBrandPercentage = BigDecimal.ZERO;
        this.unitsOrderedNewToBrandPercentage = BigDecimal.ZERO;
        this.salesNewToBrandPercentage = BigDecimal.ZERO;
        this.vcpm = BigDecimal.ZERO;
        this.vcpmCost = BigDecimal.ZERO;
        this.vcpmImpressions = 0L;
    }

    public void compareDataSet(AdCampaignHourVo compare) {
        if (Objects.isNull(compare)) {
            ReflectionUtil.setCompareNull(this);
            return;
        }
        super.compareDataSet(compare);
        this.adCostPerClickCompare = compare.getAdCostPerClick();
        this.ctrCompare = compare.getCtr();
        this.cvrCompare = compare.getCvr();
        this.acosCompare = compare.getAcos();
        this.roasCompare = compare.getRoas();
        afterPropertiesSet();
    }

}
