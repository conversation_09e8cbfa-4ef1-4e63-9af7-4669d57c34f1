package com.meiyunji.sponsored.service.cpc.service;

import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.service.cpc.vo.ReportVo;
import com.meiyunji.sponsored.service.cpc.vo.SearchVo;
import com.meiyunji.sponsored.service.cpc.vo.SumReportVo;

import java.util.Date;
import java.util.List;

/**
 * @ClassName IAmazonAdShopReportService
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/6/17 19:54
 **/
public interface IAmazonAdShopReportService {
    /**
     * 店铺汇总数据
     * @param puid
     * @param shopId
     * @param marketplaceId
     * @param start
     * @param end
     * @param lastMonth
     * @return
     */
    SumReportVo getSumReport(int puid, Integer shopId, String marketplaceId, Date start, Date end, Integer lastMonth);

    /**
     * 获取图表信息
     * @param puid
     * @param shopId
     * @param marketplaceId
     * @param start
     * @param end
     * @return
     */
    List<ReportVo> getChartList(int puid, Integer shopId, String marketplaceId, Date start, Date end);

    /**
     * 获取列表信息
     * @param puid
     * @param search
     * @param page
     * @return
     */
    Page pageList(int puid, SearchVo search, Page page);

}
