package com.meiyunji.sponsored.service.dataWarehouse.statsDo;

import com.meiyunji.sponsored.common.util.MathUtil;
import com.meiyunji.sponsored.service.util.CurrencyConversion;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.SneakyThrows;

import java.math.BigDecimal;
import java.time.LocalDate;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class ShopOrderDto implements Cloneable {

    private Integer puid;
    private LocalDate nowDate;
    private Integer orderNum;


    public ShopOrderDto merge(ShopOrderDto obj) {
        if (obj != null) {
            this.orderNum = MathUtil.add(this.orderNum, obj.getOrderNum());
        }
        return this;
    }

    @SneakyThrows
    @Override
    public ShopOrderDto clone() {
        return (ShopOrderDto) super.clone();
    }
}
