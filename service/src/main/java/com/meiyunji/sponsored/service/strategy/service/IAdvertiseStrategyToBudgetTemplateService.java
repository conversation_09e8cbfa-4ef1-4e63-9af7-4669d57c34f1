package com.meiyunji.sponsored.service.strategy.service;

import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.strategy.po.AdvertiseStrategyTopBudgetTemplate;
import com.meiyunji.sponsored.service.strategy.vo.*;

import java.util.List;

public interface IAdvertiseStrategyToBudgetTemplateService {

    /**
     * 顶级预算列表分页查询
     *
     * @param param
     * @return
     */
    Result<Page<AdvertiseStrategyTopBudgetTemplate>> getPageList(PageTopBudgetParam param);

    /**
     * 管理页查询分页查询
     *
     * @param param
     * @return
     */
    Result<Page<AdvertiseStrategyTopBudgetTemplate>> getAdmPageList(PageTopBudgetParam param);

    /**
     * 顶级预算添加模板
     *
     * @param addTopBudgetVo
     * @return
     */
    Result<List<ErrorToBudgetMsg>> insertTemplate(AddTopBudgetVo addTopBudgetVo, String traceId);

    /**
     * 顶级预算修改模板
     *
     * @param updateTopBudgetVo
     * @return
     */
    Result<Integer> updateTemplate(UpdateTopBudgetVo updateTopBudgetVo, String traceId);

    /**
     * 修改顶级预算状态
     *
     * @param updateTopBudgetStatusVo
     * @return
     */
    Result<Integer> updateStatus(UpdateTopBudgetStatusVo updateTopBudgetStatusVo, String traceId);

    /**
     * 顶级预算删除模板
     *
     * @param deleteTopBudgetVo
     * @return
     */
    Result<Integer> deleteTemplate(DeleteTopBudgetVo deleteTopBudgetVo, String traceId);

    /**
     * 顶级预算转移模板
     *
     * @param transferTopBudgetVo
     * @return
     */
    Result<Integer> transferTemplate(TransferTopBudgetVo transferTopBudgetVo, String traceId);

    /**
     * 顶级预算转移模板
     *
     * @param puid
     * @param userId
     * @return
     */
    Result<List<MarketplaceVo>> getSiteIdList (Integer puid, Integer userId, String traceId);

    /**
     * 顶级预算转移模板
     *
     * @param puid
     * @param marketplaceId
     * @param userId
     * @return
     */
    Result<List<ShopAuth>> getShopIdList (Integer puid, String marketplaceId, Integer userId, String traceId);

    /**
     * 批量修改顶级预算状态
     *
     * @param puid
     * @param status
     * @param idList
     * @return
     */
    Result<Integer> batchUpdateStatus(Integer puid, String status, List<Long> idList, List<Integer> authedShopIdList, Integer uid, String loginIp);

    /**
     * 批量删除顶级预算
     *
     * @param puid
     * @param idList
     * @return
     */
    Result<Integer> batchDeleteTemplate(Integer puid, List<Long> idList, List<Integer> authedShopIdList, Integer uid, String loginIp);

    void disableAllTemplateStatus(Integer puid, Integer shopId);

}
