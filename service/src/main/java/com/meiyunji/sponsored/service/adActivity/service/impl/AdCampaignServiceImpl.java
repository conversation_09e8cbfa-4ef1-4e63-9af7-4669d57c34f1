package com.meiyunji.sponsored.service.adActivity.service.impl;

import com.google.common.collect.Lists;
import com.meiyunji.amazon.mws.base.AmznEndpoint;
import com.meiyunji.sellfox.right.utils.RightContextUtil;
import com.meiyunji.sponsored.common.enums.AmazonAd;
import com.meiyunji.sponsored.common.exception.SponsoredBizException;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.adActivity.dto.AdCampaignProductQueryReq;
import com.meiyunji.sponsored.service.adActivity.service.AdCampaignService;
import com.meiyunji.sponsored.service.adActivity.vo.AdCampaignProductQueryVo;
import com.meiyunji.sponsored.service.cpc.dto.AdProductDetailDto;
import com.meiyunji.sponsored.service.cpc.dto.CpcProductDto;
import com.meiyunji.sponsored.service.cpc.dto.ProductStatusDto;
import com.meiyunji.sponsored.service.cpc.service2.ICpcProductApiService;
import com.meiyunji.sponsored.service.doris.dao.IOdsAmazonAdProductDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
public class AdCampaignServiceImpl implements AdCampaignService {

    @Autowired
    private IOdsAmazonAdProductDao amazonAdProductDao;

    @Autowired
    private IScVcShopAuthDao shopAuthDao;

    @Autowired
    private ICpcProductApiService cpcProductApiService;

    /**
     * 获取广告活动下的产品列表
     * @param req 请求参数
     * @return
     */
    @Override
    public List<AdCampaignProductQueryVo> getGroupProductList(AdCampaignProductQueryReq req) {
        log.info("getGroupProductList req: {}", req);
        // 限制rows默认or最大值为1000
        if (req.getLimit() == null || req.getLimit() > 1000) {
            req.setLimit(1000);
        }
        int puid = RightContextUtil.getPuid().intValue();
        List<ShopAuth> authShopByShopIdList = shopAuthDao.getAuthShopByShopIdList(puid, Lists.newArrayList(req.getShopId()));
        if (authShopByShopIdList.isEmpty()) {
            throw new SponsoredBizException("店铺未授权");
        }
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("getGroupProductList.listProductsByGroupIdList");
        String marketplaceId = authShopByShopIdList.get(0).getMarketplaceId();
        AmznEndpoint amznEndpoint = AmznEndpoint.getByMarketplaceId(marketplaceId);
        List<AdCampaignProductQueryVo> res = Lists.newArrayList();
        List<AdProductDetailDto> adProductDetailDtos = amazonAdProductDao.listProductsByGroupIdList(puid, req.getShopId(), req.getAdGroupIds(), req.getLimit());
        if (adProductDetailDtos.isEmpty()) {
            return res;
        }
        stopWatch.stop();
        List<CpcProductDto> productDtos = adProductDetailDtos.stream().map(AdCampaignProductQueryVo::toCpcProductDto).collect(Collectors.toList());
        // filter asin、sku是否可以投放 返回不能投放产品map  key: asin_sku,value: ProductStatusDto
//        stopWatch.start("getGroupProductList.productEligibility");
//        List<ProductStatusDto> resultDtoList = cpcProductApiService.productEligibility(puid, req.getShopId(),
//                AmazonAd.AdCommonEnum.SB.getAdName(), "", productDtos);
//        stopWatch.stop();

        stopWatch.start("getGroupProductList.convertVo");
//        Map<String, ProductStatusDto> collect = resultDtoList.stream().collect(Collectors.toMap(e -> e.getAsin() + "_" + e.getSku(), e -> e, (v1, v2) -> v1));
        adProductDetailDtos.forEach(item -> {
                    AdCampaignProductQueryVo vo = AdCampaignProductQueryVo.fromDto(item);
//                    vo.setIsMeetConditions(!collect.containsKey(item.getAsin() + "_" + item.getSku()));
//                    vo.setEligibilityStatus(collect.get(item.getAsin() + "_" + item.getSku()) == null ? null : collect.get(item.getAsin() + "_" + item.getSku()).getStatus());
                    vo.setDomain(amznEndpoint.getDomain());
                    // collect result
                    res.add(vo);
                });
        stopWatch.stop();
        log.info("getGroupProductList cost time---------->{},totalTime:{} seconds",stopWatch.prettyPrint(),stopWatch.getTotalTimeSeconds());
        return res;
    }
}
