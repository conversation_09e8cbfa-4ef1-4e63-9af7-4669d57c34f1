package com.meiyunji.sponsored.service.log.enums;

/**
 * @author: li<PERSON><PERSON>
 * @email: liwei<PERSON>@dianxiaomi.com
 * @date: 2023-07-11  19:14
 */
public enum OperationLogProductTargetingTypeEnum {

    PREDEFINED("PREDEFINED", "自动投放"),
    EXPRESSION("EXPRESSION", "商品投放");

    private String type;

    private String value;

    public String getType() {
        return type;
    }

    public String getValue() {
        return value;
    }

    OperationLogProductTargetingTypeEnum(String type, String value) {
        this.type = type;
        this.value = value;
    }
}
