package com.meiyunji.sponsored.service.cpc.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @author: ys
 * @date: 2023/10/23 10:55
 * @describe:
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CampaignAggregateHourParamVO {
    private Integer puid;
    @ApiModelProperty(value = "shopId",required = true)
    private Integer shopId;
    @ApiModelProperty("店铺id集合")
    private List<Integer> shopIdList;
    @ApiModelProperty(value ="开始时间",required = true)
    private String startDate;
    @ApiModelProperty(value ="结束时间",required = true)
    private String endDate;
    @ApiModelProperty("是否对比")
    private Integer isCompare;
    @ApiModelProperty("对比开始时间")
    private String startDateCompare;
    @ApiModelProperty("对比结束时间")
    private String endDateCompare;
    @ApiModelProperty("星期逗号分隔")
    private String weeks;
    @ApiModelProperty("广告活动")
    private String campaignId;
    @ApiModelProperty("排序字段")
    private String orderField;
    @ApiModelProperty("排序类型")
    private String orderType;
    @ApiModelProperty("pageNo")
    private Integer pageNo;

    @ApiModelProperty("pageSize")
    private Integer pageSize;


    @ApiModelProperty("搜索字段名称")
    private String searchField;

    @ApiModelProperty("搜索内容--对应字段")
    private String searchValue;

    @ApiModelProperty("状态")
    private String status;

    @ApiModelProperty("广告类型 sp, sd, sb")
    private String type; // sp, sd, sb

    //StrategyEnum枚举类
    @ApiModelProperty("竞价策略类型 legacyForSales,autoForSales,manual->sp广告  none->sd sb广告")
    private String strategyType;

    @ApiModelProperty("预算状态 under->未超 exceeded->已超")
    private String budgetState;

    //高级搜索
    @ApiModelProperty(value = "是否开启高级搜索")
    private Boolean useAdvanced;

    //高级搜索值
    @ApiModelProperty(value = "高级搜索展示量最小值")
    private Integer impressionsMin;  //展示量
    @ApiModelProperty(value = "高级搜索展示量最大值")
    private Integer impressionsMax;
    @ApiModelProperty(value = "高级搜索点击量小值")
    private Integer clicksMin;  //点击量
    @ApiModelProperty(value = "高级搜索点击量大值")
    private Integer clicksMax;
    @ApiModelProperty(value = "高级搜索点击率小值")
    private Double clickRateMin;  //点击率
    @ApiModelProperty(value = "高级搜索点击率大值")
    private Double clickRateMax;
    @ApiModelProperty(value = "高级搜索花费小值")
    private Double costMin; //花费
    @ApiModelProperty(value = "高级搜索花费大值")
    private Double costMax;
    @ApiModelProperty(value = "高级搜索cpc小值")
    private Double cpcMin;  //cpc
    @ApiModelProperty(value = "高级搜索cpc大值")
    private Double cpcMax;
    @ApiModelProperty(value = "高级搜索广告订单量小值")
    private Integer orderNumMin;  //广告订单量
    @ApiModelProperty(value = "高级搜索广告订单量大值")
    private Integer orderNumMax;
    @ApiModelProperty(value = "高级搜索sales小值")
    private Double salesMin;  //sales
    @ApiModelProperty(value = "高级搜索sales大值")
    private Double salesMax;
    @ApiModelProperty(value = "高级搜索acos小值")
    private Double acosMin;  //acos
    @ApiModelProperty(value = "高级搜索acos大值")
    private Double acosMax;
    @ApiModelProperty(value = "高级搜索roas小值")
    private Double roasMin;   //roas
    @ApiModelProperty(value = "高级搜索roas大值")
    private Double roasMax;
    @ApiModelProperty(value = "高级搜索广告订单转化率小值")
    private Double salesConversionRateMin;  //订单转化率
    @ApiModelProperty(value = "高级搜索广告订单转化率大值")
    private Double salesConversionRateMax;
    @ApiModelProperty(value = "高级搜索acots小值")
    private Double acotsMin;  //acots
    @ApiModelProperty(value = "高级搜索acots大值")
    private Double acotsMax;
    @ApiModelProperty(value = "高级搜索asots小值")
    private Double asotsMin;  //acos
    @ApiModelProperty(value = "高级搜索asots大值")
    private Double asotsMax;

    @ApiModelProperty(value = "高级搜索投放类型")
    private String deliveryType;
    @ApiModelProperty(value = "高级搜索每日预算最小")
    private Double dailyBudgetMin;
    @ApiModelProperty(value = "高级搜索每日预算最大")
    private Double dailyBudgetMax;
    @ApiModelProperty(value = "高级搜索开始日期")
    private String filterStartDate;
    @ApiModelProperty(value = "高级搜索结束日期")
    private String filterEndDate;
    @ApiModelProperty(value = "高级搜索广告位顶部最小")
    private Double placementTopMin;
    @ApiModelProperty(value = "高级搜索广告位顶部最大")
    private Double placementTopMax;
    @ApiModelProperty(value = "高级搜索广告位产品页面最小")
    private Double placementProductMin;
    @ApiModelProperty(value = "高级搜索广告位产品页面最大")
    private Double placementProductMax;

    @ApiModelProperty(value = "高级搜索广告花费占比最小")
    private Double adCostPercentageMin;
    @ApiModelProperty(value = "高级搜索广告花费占比最大")
    private Double adCostPercentageMax;
    @ApiModelProperty(value = "高级搜索广告销售额占比最小")
    private Double adSalePercentageMin;
    @ApiModelProperty(value = "高级搜索广告销售额占比最大")
    private Double adSalePercentageMax;
    @ApiModelProperty(value = "高级搜索广告订单量占比最小")
    private Double adOrderNumPercentageMin;
    @ApiModelProperty(value = "高级搜索广告订单量占比最大")
    private Double adOrderNumPercentageMax;
    @ApiModelProperty(value = "高级搜索广告销量占比最小")
    private Double orderNumPercentageMin;
    @ApiModelProperty(value = "高级搜索广告销量占比最大")
    private Double orderNumPercentageMax;

    @ApiModelProperty(value = "广告组合id")
    private String portfolioId;
    @ApiModelProperty(value = "付费方式")
    private String costType;

    @ApiModelProperty(value = "仅展示正在投放字段 勾选后传值 enabled ，服务赛选传枚举：CAMPAIGN_STATUS_ENABLED：运行中，CAMPAIGN_PAUSED：已暂停，CAMPAIGN_ARCHIVED：已归档，CAMPAIGN_OUT_OF_BUDGET:超预算,PENDING_START_DATE:已安排,SCHEDULED:已预定,ENDED:已结束,PENDING_REVIEW:待审核,CAMPAIGN_INCOMPLETE:不完整,ADVERTISER_PAYMENT_FAILED:付款失败,LANDING_PAGE_NOT_AVAILABLE:着陆页失效,REJECTED:未获得批准")
    private String servingStatus;
    @ApiModelProperty("搜索产品类型：asin,msku")
    private String productType;
    @ApiModelProperty("搜索产品值")
    private String productValue;
    private String compareStartDate;
    private String compareEndDate;


    /*******************高级搜索新增查询字段***************************/
    @ApiModelProperty(value = "可见展示次数最小值")
    private Integer viewImpressionsMin;
    @ApiModelProperty(value = "可见展示次数最大值")
    private Integer viewImpressionsMax;


    @ApiModelProperty(value = "cpa最小值")
    private Double cpaMin;
    @ApiModelProperty(value = "cpa最大值")
    private Double cpaMax;


    @ApiModelProperty(value = "vcpm最小值")
    private Double vcpmMin;
    @ApiModelProperty(value = "vcpm最大值")
    private Double vcpmMax;


    @ApiModelProperty(value = "本广告产品订单量最小值")
    private Integer adSaleNumMin;
    @ApiModelProperty(value = "本广告产品订单量最大值")
    private Integer adSaleNumMax;


    @ApiModelProperty(value = "其他产品广告订单量最小值")
    private Integer adOtherOrderNumMin;
    @ApiModelProperty(value = "其他产品广告订单量最大值")
    private Integer adOtherOrderNumMax;


    @ApiModelProperty(value = "本广告产品销售额最小值")
    private Double adSalesMin;
    @ApiModelProperty(value = "本广告产品销售额最大值")
    private Double adSalesMax;


    @ApiModelProperty(value = "其他产品广告销售额最小值")
    private Double adOtherSalesMin;
    @ApiModelProperty(value = "其他产品广告销售额最大值")
    private Double adOtherSalesMax;


    @ApiModelProperty(value = "本廣告产品销量最小值")
    private Integer adSelfSaleNumMin;
    @ApiModelProperty(value = "本廣告产品销量最大值")
    private Integer adSelfSaleNumMax;


    @ApiModelProperty(value = "其他产品广告销量最小值")
    private Integer adOtherSaleNumMin;
    @ApiModelProperty(value = "其他产品广告销量最大值")
    private Integer adOtherSaleNumMax;


    @ApiModelProperty(value = "“品牌新买家”订单量最小值")
    private Integer ordersNewToBrandFTDMin;
    @ApiModelProperty(value = "“品牌新买家”订单量最大值")
    private Integer ordersNewToBrandFTDMax;


    @ApiModelProperty(value = "“品牌新买家”订单百分比最小值")
    private Double orderRateNewToBrandFTDMin;
    @ApiModelProperty(value = "“品牌新买家”订单百分比最大值")
    private Double orderRateNewToBrandFTDMax;


    @ApiModelProperty(value = "“品牌新买家”销售额最小值")
    private Double salesNewToBrandFTDMin;
    @ApiModelProperty(value = "“品牌新买家”销售额最大值")
    private Double salesNewToBrandFTDMax;

    @ApiModelProperty(value = "“品牌新买家”销售额百分比最小值")
    private Double salesRateNewToBrandFTDMin;

    @ApiModelProperty(value = "“品牌新买家”销售额百分比最大值")
    private Double salesRateNewToBrandFTDMax;

    @ApiModelProperty(value = "“品牌新买家”销量最小值")
    private Integer unitsOrderedNewToBrandFTDMin;

    @ApiModelProperty(value = "“品牌新买家”销量最大值")
    private Integer unitsOrderedNewToBrandFTDMax;

    @ApiModelProperty(value = "“品牌新买家”销量百分比最小值")
    private Double unitsOrderedRateNewToBrandFTDMin;

    @ApiModelProperty(value = "“品牌新买家”销量百分比最大值")
    private Double unitsOrderedRateNewToBrandFTDMax;

    @ApiModelProperty(value = "广告销量最小值")
    private Integer adSalesTotalMin;

    @ApiModelProperty(value = "广告销量最大值")
    private Integer adSalesTotalMax;

    @ApiModelProperty("请求标识")
    private String pageSign;
}
