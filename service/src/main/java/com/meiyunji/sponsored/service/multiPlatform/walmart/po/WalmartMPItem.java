package com.meiyunji.sponsored.service.multiPlatform.walmart.po;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.Map;

public class WalmartMPItem {

    /**
     * {"Office":{"shortDescription":"Storage","smallPartsWarnings":["0 - No warning applicable"],"variantAttributeNames":["color"]}}
     */
    @JsonProperty("Visible")
    private Map<String, Map<String, Object>> visible;

    @JsonProperty("Orderable")
    private WalmartMPItemOrderable orderable;

    public Map<String, Map<String, Object>> getVisible() {
        return visible;
    }

    public void setVisible(Map<String, Map<String, Object>> visible) {
        this.visible = visible;
    }

    public WalmartMPItemOrderable getOrderable() {
        return orderable;
    }

    public void setOrderable(WalmartMPItemOrderable orderable) {
        this.orderable = orderable;
    }
}
