package com.meiyunji.sponsored.service.sellfoxApi.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 列表页回显的店铺数据
 *
 * <AUTHOR> on 2023/4/6
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ShopVO {

    /**
     * 店铺id
     */
    @ApiModelProperty("店铺id")
    private Integer shopId;

    /**
     * 店铺名字
     */
    @ApiModelProperty("店铺名字")
    private String shopName;

    /**
     * 站点id
     */
    @ApiModelProperty("站点id")
    private String marketplaceId;

    /**
     * 站点名字
     */
    @ApiModelProperty("站点名字")
    private String marketplaceName;
}