package com.meiyunji.sponsored.service.cpc.vo;

import com.meiyunji.sponsored.service.cpc.po.ReportBase;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * @author: liweibin
 * @email: <EMAIL>
 * @date: 2024-01-02  13:25
 */
@Data
public class SbKeywordPageVo extends ReportBase {
    /**
     * 自增主键id
     */
    private Long id;

    /**
     * 主商户uid
     */
    private Integer puid;

    /**
     * 店铺ID
     */
    private Integer shopId;

    /**
     * 站点
     */
    private String marketplaceId;

    /**
     * 配置ID
     */
    private String profileId;

    /**
     * 关键词id
     */
    private String keywordId;

    /**
     * 活动id
     */
    private String campaignId;

    /**
     * 广告组id
     */
    private String adGroupId;

    /**
     * 关键词
     */
    private String keywordText;

    /**
     * 本地化关键字
     */
    private String nativeLanguageKeyword;

    /**
     * 本地化关键字语言
     */
    private String nativeLanguageLocale;

    /**
     * 匹配类型  broad, exact, phrase
     */
    private String matchType;

    /**
     * 关键词状态 enabled, paused, pending, archived, draft
     */
    private String state;

    /**
     * 关键词竞价
     */
    private BigDecimal bid;

    /**
     * 竞价建议值，用于刚进编辑页面展示
     */
    private BigDecimal suggested;

    /**
     * 竞价建议范围最小值，用于刚进编辑页面展示
     */
    private BigDecimal rangeStart;

    /**
     * 竞价建议范围最大值，用于刚进编辑页面展示
     */
    private BigDecimal rangeEnd;

    /**
     * 1在amzup创建，0从amazon同步
     */
    private Integer createInAmzup;

    private Integer isPricing;

    private Integer pricingState;

    private LocalDate dataUpdateTime;
}
