package com.meiyunji.sponsored.service.cpc.dao;

import com.meiyunji.sponsored.common.springjdbc.ISlaveBaseDao;
import com.meiyunji.sponsored.service.cpc.bo.AmazonAdProfileBo;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdProfile;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * AmazonAdProfile
 *
 * <AUTHOR>
 */
public interface ISlaveAmazonAdProfileDao extends ISlaveBaseDao<AmazonAdProfile> {


    /**
     * 获取有数据的店铺配置
     *
     * @return ：AmazonAdProfile
     */
    List<AmazonAdProfile> getDataProfile(Integer limit);

    /**
     * 查询全部的ProfileId
     *
     * @return
     */
    List<AmazonAdProfile> queryAllProfile(Integer limit);

    /**
     * 获取指定店铺的配置
     *
     * @param puid：
     * @param shopId：
     * @return ：AmazonAdProfile
     */
    AmazonAdProfile getProfile(int puid, int shopId);

    /**
     * 根据站点获取配置信息
     *
     * @param puid
     * @param shopId
     * @param marketPlaceId
     * @return
     */
    AmazonAdProfile getProfileByMarketId(Integer puid, Integer shopId, String marketPlaceId);

    /**
     * 判断是否有对应的配置文件
     *
     * @param puid
     * @param shopId
     * @param marketplaceId
     * @return
     */
    boolean exist(Integer puid, Integer shopId, String marketplaceId);

    /**
     * 获取所有授权的店铺id用于
     *
     * @return
     */
    List<Integer> getShopIds();

    /**
     * 获取站点信息
     *
     * @param puid
     * @param shopId
     * @return
     */
    List<String> getMarketplaceIds(Integer puid, Integer shopId);

    /**
     * 获取用户有授权cpc的店铺站点
     *
     * @param puid
     * @return
     */
    List<AmazonAdProfile> getList(int puid);

    List<AmazonAdProfile> listByShopIds(List<Integer> shopIdList);

    List<AmazonAdProfile> listByMarketplaceIdList(Integer puid, List<String> marketplaceIdList);

    List<AmazonAdProfile> listForPricingAvailable();

    AmazonAdProfile getAttributionAdvertiser();

    List<AmazonAdProfile> getAttributionAvailableShopsByPuid(Integer puid, List<Integer> shopIds);

    List<AmazonAdProfile> getNeedSyncAttribution(Integer puid, Integer shopId);

    List<AmazonAdProfile> getListByPuidOrShopId(Integer puid, Integer shopId);

    List<AmazonAdProfile> getByCreateTimeRange(LocalDateTime start, LocalDateTime end);

    List<AmazonAdProfile> getNeedStopSyncProfile();

    List<AmazonAdProfile> getValidSpAdProfile();

    AmazonAdProfile getByAccountIdAndMarketplaceId(String accountId, String marketplaceId);

    //获取所有的puid
    List<Integer> getAllPuid();

    //获取所有的puid
    List<AmazonAdProfile> getAllAmazonAdProfile();

    List<AmazonAdProfile> getByCreateTimeRange(LocalDateTime start, LocalDateTime end, Integer index, Integer limit);

    List<Integer> getAllPuidByLimit(int start, int limit);

    List<AmazonAdProfile> getByPuidAndShopIdList(int puid, ArrayList<Integer> shopIdList);

    List<AmazonAdProfile> getProfilesByProfileId(List<String> profileIds);

    List<AmazonAdProfile> getProfilesByShopIds(List<Integer> shopIds);

    /**
     * 根据店铺id查询
     */
    AmazonAdProfileBo listAmazonAdProfileBoByShopId(Integer puid, Integer shopId);

    /**
     * 查询所有
     */
    List<AmazonAdProfileBo> listAmazonAdProfileBo();
}