package com.meiyunji.sponsored.service.newDashboard.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/09/03
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class DashboardAdUpdateKeywordRequestVo {
    /**
     * puid
     */
    @ApiModelProperty("puid")
    private Integer puid ;
    /**
     * 店铺id
     */
    @ApiModelProperty("店铺id")
    private Integer shopId;
    /**
     *数据id
     */
    @ApiModelProperty("数据id")
    private Long id;
    /**
     * 关键词id
     */
    @ApiModelProperty("关键词id")
    private String keywordId;
    /**
     * 调整类型 ： 1 调竞价，2 状态，3 归档
     */
    @ApiModelProperty("调整类型 ： 1 调竞价，2 状态，3 归档")
    private Integer updateType;
    /**
     * 广告类型 sp， sb
     */
    @ApiModelProperty("广告类型 sp， sb")
    private String adType;
    /**
     * uid
     */
    @ApiModelProperty("uid")
    private Integer uid;
    /**
     * 登录ip
     */
    @ApiModelProperty("用户登录ip")
    private String ip;

    private String status;

    private Double bid;

}
