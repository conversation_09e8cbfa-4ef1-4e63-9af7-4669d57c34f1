package com.meiyunji.sponsored.service.log.enums;


/**
 * 运营笔记操作日志类型
 * <AUTHOR>
 * @date 2024/09/11
 */
public enum OperationNotesTypeEnum {
    /**
     * 添加至广告组
     */
    ADD_AD_GROUP("ADD_AD_GROUP", "添加至广告组"),
    /**
     * 添加投放
     */
    ADD_TARGET("ADD_TARGET", "添加投放"),
    /**
     * 添加否定
     */
    ADD_NEGATIVE("ADD_NEGATIVE", "添加否定"),
    /**
     * 编辑广告位
     */
    EDIT_AD_POSITION("EDIT_AD_POSITION", "编辑广告位"),
    /**
     * 编辑预算
     */
    EDIT_BUDGET("EDIT_BUDGET", "编辑预算"),
    /**
     * 编辑商品投放竞价
     */
    EDIT_TARGET_BID("EDIT_TARGET_BID", "编辑商品投放竞价"),
    /**
     * 编辑关键词投放竞价
     */
    EDIT_KEYWORD_BID("EDIT_KEYWORD_BID", "编辑关键词投放竞价"),
    ;

    private String operationType;

    private String desc;

    OperationNotesTypeEnum(String operationType, String desc) {
        this.operationType = operationType;
        this.desc = desc;
    }

    public String getOperationType() {
        return operationType;
    }

    public String getDesc() {
        return desc;
    }

}
