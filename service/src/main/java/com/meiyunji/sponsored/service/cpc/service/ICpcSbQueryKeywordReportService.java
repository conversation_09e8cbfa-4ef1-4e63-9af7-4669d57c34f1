package com.meiyunji.sponsored.service.cpc.service;

import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.rpc.adCommon.AllQueryWordAggregateDataResponse;
import com.meiyunji.sponsored.rpc.adCommon.AllQueryWordDataResponse;
import com.meiyunji.sponsored.service.cpc.vo.*;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface ICpcSbQueryKeywordReportService {
    /**
     * 用户搜索词列表页
     * @param puid
     * @param dto
     * @param page
     * @return
     */
    Page pageList(Integer puid, CpcQueryWordDto dto, Page page);

    /**
     * 搜索词页面导出
     */
    Page pageManageList(Integer puid, CpcQueryWordDto dto, Page page);

    /**
     * 搜索词页面doris导出
     */
    Page dorisPageExportList(Integer puid, CpcQueryWordDto dto, Page page);

    Page pageManageExportList(Integer puid, SearchVo searchVo, Page page);

    /**
     * 搜索词广告管理首页数据
     *
     * @param puid
     * @param dto
     * @param page
     * @return
     */
    AllQueryWordDataResponse.AdQueryWordsHomeVo getAllQueryWordData(Integer puid, CpcQueryWordDto dto, Page page);

    Page getAdQueryWordsPageVo(Integer puid, CpcQueryWordDto dto, Page page);

    AllQueryWordAggregateDataResponse.AdQueryWordsHomeVo getAllQueryWordAggregateData(Integer puid, CpcQueryWordDto dto);

    AllQueryWordDataResponse.AdQueryWordsHomeVo getDorisAllQueryWordData(Integer puid, CpcQueryWordDto dto, Page page);

    Page getDorisAdQueryWordsPageVo(Integer puid, CpcQueryWordDto dto, Page page);

    AllQueryWordAggregateDataResponse.AdQueryWordsHomeVo getDorisAllQueryWordAggregateData(Integer puid, CpcQueryWordDto dto);

    /**
     * 详情列表
     *
     * @param puid
     * @param dto
     * @param page
     * @return
     */
    Page detailList(Integer puid, CpcQueryWordDetailDto dto, Page page);

    /**
     * 获取详情页汇总数据
     *
     * @param puid
     * @param dto
     * @return
     */
    ReportVo sumDetailReport(Integer puid, CpcQueryWordDetailDto dto);



    /**
     * 获取详情页列表数据
     * @param puid
     * @param dto
     * @return
     */
    List<ReportVo> detailListChart(int puid, CpcQueryWordDetailDto dto);

    /**
     * 填充广告策略标签
     * @param dto
     * @param rows
     */
    void fillAdStrategy(CpcQueryWordDto dto, List<ReportVo> rows);

//    /**
//     * 获取汇总数据
//     * @param puid
//     * @param dto
//     * @return
//     */
//    ReportVo sumReport(Integer puid, CpcQueryWordDto dto);
//
//    /**
//     * 获取汇总数据
//     * @param puid
//     * @param dto
//     * @return
//     */
//    ReportVo sumManageReport(Integer puid, CpcQueryWordDto dto, BigDecimal sumRange);
//

//
//


//
//

//
//    List<QueryReportVo> getReportVoListByGroupIds(Integer puid, List<String> spGroupIds, QueryReportSearchVo searchVo);
//
//    List<TargetQueryReportVo> getListByKeywordId(Integer puid, String targetId, TargetQuerySearchVo searchVo);
//
//    /**
//     * 获取搜索词弹层数据
//     */
//    void getDetailsSumVo(QueryReportDetailsVo detailsVo,  CpcCommPageVo sumVo,  BigDecimal sumShopSale);
//
//    /**
//     * 获取搜索词弹层每天数据
//     * @param adReportDetailsVo
//     * @param detailsVo
//     */
//    void getQueryDetailsDay(AdReportDetailsVo adReportDetailsVo, QueryReportDetailsVo detailsVo);

}
