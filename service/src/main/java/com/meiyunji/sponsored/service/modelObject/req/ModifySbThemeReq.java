package com.meiyunji.sponsored.service.modelObject.req;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
/**
 * 搜索广告位分析列表页查询req
 *
 * @Author: hejh
 * @Date: 2025/2/11 18:53
 */
@Data
public class ModifySbThemeReq implements Serializable {

    private static final long serialVersionUID = 123456L;

    @NotNull(message = "shopId is empty")
    private Integer shopId;
    @NotNull(message = "id is empty")
    private Long id;
    /**
     * SBThemeState: "enabled" "paused" "archived"
     *
     * @see com.meiyunji.sponsored.service.enums.StateEnum
     */
    private String state;
    private Double bid;

}
