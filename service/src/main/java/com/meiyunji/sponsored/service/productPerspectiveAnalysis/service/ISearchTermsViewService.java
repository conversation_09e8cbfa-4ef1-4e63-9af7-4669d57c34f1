package com.meiyunji.sponsored.service.productPerspectiveAnalysis.service;

import com.meiyunji.sponsored.service.productPerspectiveAnalysis.qo.DiagnoseCountParam;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.qo.SearchTermsViewParam;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.qo.TargetViewParam;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.vo.SearchTermsViewPageVo;

import java.util.List;
import java.util.Map;


/**
 * 搜索词视图
 * <AUTHOR>
 * @date 2024/05/16
 */
public interface ISearchTermsViewService {
    /**
     * 获取搜索词视图列表页、汇总
     * @param puid
     * @param param
     * @return
     */
    SearchTermsViewPageVo getSearchTermsViewPageVoList(Integer puid, SearchTermsViewParam param);

    /**
     * 获取搜索词视图列表页、汇总，支持多选广告类型
     * @param puid
     * @param param
     * @return
     */
    SearchTermsViewPageVo getAllSearchTermsViewPageVoList(Integer puid, SearchTermsViewParam param);


    Map<Long, Integer> getDiagnoseCount4SearchTerms(Integer puid, List<DiagnoseCountParam> diagnoseCountParams);

    Map<Long, Integer> getAllTypeDiagnoseCount4SearchTerms(Integer puid, List<DiagnoseCountParam> diagnoseCountParams);
}
