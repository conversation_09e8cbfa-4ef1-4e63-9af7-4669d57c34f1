package com.meiyunji.sponsored.service.newDashboard.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: ys
 * @date: 2024/4/9 14:44
 * @describe:
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class DashboardAdSalesmanReqVo extends DashboardBaseReqVo {
    @ApiModelProperty("是否勾选占比，1是0否")
    private Integer percent = 0;
    @ApiModelProperty("是否勾选同比，1是0否")
    private Integer yoy = 0;
    @ApiModelProperty("是否勾选环比，1是0否")
    private Integer mom = 0;
    @ApiModelProperty("指标条件")
    private String dataField;
    @ApiModelProperty("排序指标条件")
    private String orderByField;
    @ApiModelProperty("排序方式")
    private String orderBy;
    @ApiModelProperty("排序取名次数量")
    private Integer limit;
    @ApiModelProperty("pageNo")
    private Integer pageNo;
    @ApiModelProperty("pageSize")
    private Integer pageSize;
}
