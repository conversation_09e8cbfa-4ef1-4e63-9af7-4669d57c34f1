package com.meiyunji.sponsored.service.strategy.vo;

import lombok.Data;
import org.springframework.validation.beanvalidation.SpringValidatorAdapter;

import java.util.List;

@Data
public class ExecuteCampaignVo {
    private Integer puid;
    private Integer shopId;
    private String marketplaceId;
    private String marketplaceName;
    private String shopName;
    private String campaignId;
    private String campaignName;
    private String type;
    private List<PeriodCampaignVo> periodCampaignVoList;
}
