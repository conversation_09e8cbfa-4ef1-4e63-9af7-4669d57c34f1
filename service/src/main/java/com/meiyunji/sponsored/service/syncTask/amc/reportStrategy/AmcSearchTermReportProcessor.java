package com.meiyunji.sponsored.service.syncTask.amc.reportStrategy;

import com.google.common.collect.Lists;
import com.meiyunji.sellfox.aadas.types.message.notification.AmcReadyNotification;
import com.meiyunji.sponsored.common.util.MD5Util;
import com.meiyunji.sponsored.service.amc.enums.AmcAdTypeEnum;
import com.meiyunji.sponsored.service.amc.enums.AmcMatchTypeEnum;
import com.meiyunji.sponsored.service.amc.enums.AmcPlacementTypeEnum;
import com.meiyunji.sponsored.service.category.dao.AmazonAdTargetCategoriesDao;
import com.meiyunji.sponsored.service.category.entity.AmazonAdTargetCategories;
import com.meiyunji.sponsored.service.doris.po.OdsAmcCampaignInfo;
import com.meiyunji.sponsored.service.doris.po.OdsAmcQueryTargetingReport;
import com.meiyunji.sponsored.service.doris.service.IDorisService;
import com.meiyunji.sponsored.service.syncTask.amc.dto.AmcTargetingParseDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static com.meiyunji.sponsored.service.cpc.util.Constants.ASIN_REGEX;

/**
 * @author: sunlinfeng
 * @email: <EMAIL>
 * @date: 2025-02-14  13:40
 */

@Component
@Slf4j
public class AmcSearchTermReportProcessor extends AmcReportProcessor<OdsAmcQueryTargetingReport> {

    @Autowired
    private AmazonAdTargetCategoriesDao amazonAdTargetCategoriesDao;

    @Autowired
    private IDorisService dorisService;

    @Override
    protected OdsAmcQueryTargetingReport parseCsvLine2Obj(int puid, String[] values, String countDate, Map<String, AmcReadyNotification.ShopInfo> entityShopMap) {
        if (values.length != 11) {
            //数据无效
            log.error("amc report line data invalid, puid: {}, line data: {}", puid, values);
            return null;
        }

        String entityId = values[0];
        String campaignName = values[1];
        String campaignIdString = values[2];
        String searchWord = values[3];
        String sourceMatchType = values[4];
        String sourceTarget = values[5];
        String placement = values[6];
        String sourceAdType = values[7];

        if (StringUtils.isAnyBlank(entityId, campaignName, campaignIdString, searchWord, sourceMatchType, sourceTarget, placement, sourceAdType)) {
            log.error("amc report line data blank, puid: {}, line data: {}", puid, values);
            return null;
        }

        OdsAmcQueryTargetingReport report = new OdsAmcQueryTargetingReport();
        report.setPuid(puid);
        report.setEntityId(entityId);
        report.setCountDay(LocalDate.parse(countDate, formatter));

        //店铺信息
        AmcReadyNotification.ShopInfo shopInfo = entityShopMap.get(entityId);
        if (Objects.isNull(shopInfo)) {
            log.error("amc report line data shopInfo is null, puid: {}, line data: {}", puid, values);
            return null;
        }
        report.setShopId(Integer.parseInt(shopInfo.getMarketplaceIdentifier()));
        report.setMarketplaceId(shopInfo.getMarketplaceId());

        //活动信息
        report.setCampaign(campaignName);
        report.setCampaignIdString(campaignIdString);
        //搜索词
        report.setCustomerSearchTerm(searchWord);

        //广告位
        if (AmcPlacementTypeEnum.typeSet.contains(placement)) {
            report.setPlacementType(placement);
        } else {
            log.error("amc report line data placement invalid, puid: {}, line data: {}", puid, values);
            return null;
        }

        //广告类型
        AmcAdTypeEnum adTypeEnum = AmcAdTypeEnum.typeMap.getOrDefault(sourceAdType, null);
        if (Objects.nonNull(adTypeEnum)) {
            report.setAdProductType(adTypeEnum.getCode());
        } else {
            log.error("amc report line data ad type invalid, puid: {}, line data: {}", puid, values);
            return null;
        }

        //投放类型、匹配类型、投放值
        report.setTargeting(sourceTarget);
        AmcTargetingParseDto parseDto = getTargetingAndMatchType(sourceMatchType, sourceTarget);
        if (Objects.nonNull(parseDto) && !StringUtils.isAnyBlank(parseDto.getTargetingType(), parseDto.getMatchType(), parseDto.getTargetingText())) {
            report.setTargetingType(parseDto.getTargetingType());
            report.setMatchType(parseDto.getMatchType());
            report.setTargetingText(parseDto.getTargetingText());
        } else {
            log.error("amc report line data match type invalid, puid: {}, line data: {}", puid, values);
            return null;
        }

        //query_id，使用原始值生成
        String queryIdStr = StringUtils.join(entityId, searchWord, campaignIdString, sourceMatchType, placement, sourceTarget);
        report.setQueryId(MD5Util.getMD5(queryIdStr));

        //搜索词是否asin
        if (searchWord.matches(ASIN_REGEX)) {
            report.setIsAsin(1);
        } else {
            report.setIsAsin(0);
        }

        //曝光
        report.setImpressions(Integer.parseInt(values[8]));
        //点击
        report.setClicks(Integer.parseInt(values[9]));
        //花费
        report.setSpend(parseSpend(values[10]));
        //时间
        LocalDateTime date = LocalDateTime.now();
        report.setCreateTime(date);
        report.setUpdateTime(date);

        return report;
    }

    @Override
    protected List<OdsAmcQueryTargetingReport> processData(List<OdsAmcQueryTargetingReport> dataList) {
        //处理类目投放的类目id，需要转换为文本
        Map<String, Set<Long>> categoryIdMap = dataList.stream()
                .filter(x -> x.getMatchType().equals(AmcMatchTypeEnum.target_category.getType()))
                .collect(Collectors.groupingBy(OdsAmcQueryTargetingReport::getMarketplaceId,
                        Collectors.mapping(t -> Long.parseLong(t.getTargetingText()), Collectors.toSet())));

        //查询数据库
        Map<String, Map<String, String>> categoryNameMap = new HashMap<>();
        categoryIdMap.forEach((k, v) -> {
            ArrayList<Long> categoryIdList = new ArrayList<>(v);
            List<List<Long>> partitionList = Lists.partition(categoryIdList, 2000);
            partitionList.forEach(x -> {
                List<AmazonAdTargetCategories> categoryList = amazonAdTargetCategoriesDao.listCategoryByCategoryIds(k, x);
                if (CollectionUtils.isNotEmpty(categoryList)) {
                    HashMap<String, String> collect = categoryList.stream().collect(Collectors.toMap(
                            category -> String.valueOf(category.getCategoryId()),
                            AmazonAdTargetCategories::getName,
                            (existing, replacement) -> existing,
                            HashMap::new));
                    Map<String, String> marketplaceCategoryNameMap = categoryNameMap.getOrDefault(k, new HashMap<>(v.size()));
                    marketplaceCategoryNameMap.putAll(collect);
                    categoryNameMap.put(k,marketplaceCategoryNameMap);
                }
            });
        });

        //遍历填充
        dataList.forEach(x -> {
            if (x.getMatchType().equals(AmcMatchTypeEnum.target_category.getType())) {
                String categoryId = x.getTargetingText();
                Map<String, String> marketplaceCategoryNameMap = categoryNameMap.getOrDefault(x.getMarketplaceId(), null);
                if (MapUtils.isNotEmpty(marketplaceCategoryNameMap)) {
                    String categoryName = marketplaceCategoryNameMap.getOrDefault(categoryId, null);
                    if (StringUtils.isNotBlank(categoryName)) {
                        x.setTargetingText(categoryName);
                    }
                }
            }
        });

        return dataList;
    }

    @Override
    protected void saveData(List<OdsAmcQueryTargetingReport> dataList) {

        //已处理的活动
        Set<String> keySet = new HashSet<>();
        //需要保存的活动
        List<OdsAmcCampaignInfo> campaignInfoList = new ArrayList<>();
        LocalDateTime date = LocalDateTime.now();
        for (OdsAmcQueryTargetingReport x : dataList) {
            String key = StringUtils.join(new String[]{String.valueOf(x.getPuid()), String.valueOf(x.getShopId()), x.getCampaignIdString()}, ",");
            if (keySet.contains(key)) {
                continue;
            }
            OdsAmcCampaignInfo campaignInfo = new OdsAmcCampaignInfo();
            campaignInfo.setPuid(x.getPuid());
            campaignInfo.setShopId(x.getShopId());
            campaignInfo.setCampaignIdString(x.getCampaignIdString());
            campaignInfo.setCampaign(x.getCampaign());
            campaignInfo.setAdProductType(x.getAdProductType());
            campaignInfo.setCreateTime(date);
            campaignInfo.setUpdateTime(date);
            campaignInfoList.add(campaignInfo);
            keySet.add(key);
        }
        //保存活动信息数据
        if (CollectionUtils.isNotEmpty(campaignInfoList)) {
            dorisService.saveDorisByRoutineLoad("doris_ods_amc_campaign_info", campaignInfoList);
        }

        //保存搜索词数据
        if (CollectionUtils.isNotEmpty(dataList)) {
            dorisService.saveDorisByRoutineLoad("doris_ods_amc_query_targeting_report", dataList);
        }

    }

    public static Double parseSpend(String spendStr) {
        double spend = Double.parseDouble(spendStr);
        double result = spend / Math.pow(10, 8);
        BigDecimal bd = new BigDecimal(result);
        bd = bd.setScale(2, RoundingMode.HALF_UP);
        return bd.doubleValue();
    }


}
