package com.meiyunji.sponsored.service.multiPlatform.walmart.dao.impl;

import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.springjdbc.BaseShardingDaoImpl;
import com.meiyunji.sponsored.service.multiPlatform.walmart.dao.IWalmartAdvertisingKeywordReportDao;
import com.meiyunji.sponsored.service.multiPlatform.walmart.po.WalmartAdvertisingGroupReportPage;
import com.meiyunji.sponsored.service.multiPlatform.walmart.po.WalmartAdvertisingItemReport;
import com.meiyunji.sponsored.service.multiPlatform.walmart.po.WalmartAdvertisingKeywordReport;
import com.meiyunji.sponsored.service.multiPlatform.walmart.po.WalmartAdvertisingKeywordReportPage;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import java.util.*;

/**
 * @author: pxq
 * @date: 2025/02/24
 * @Description: walmart广告关键词报告Dao
 */

@Repository
public class WalmartAdvertisingKeywordReportDaoImpl extends BaseShardingDaoImpl<WalmartAdvertisingKeywordReport> implements IWalmartAdvertisingKeywordReportDao {
    @Override
    public Long add(WalmartAdvertisingKeywordReport keywordReport) {
        return null;
    }

    @Override
    public int update(WalmartAdvertisingKeywordReport keywordReport) {
        return 0;
    }

    @Override
    public int delete(Integer puid, Long id) {
        return 0;
    }

    @Override
    public WalmartAdvertisingKeywordReport getByKeywordId(int puid, Long shopId, String reportDate, Long campaignId, Long gruopId, Long keywordId) {
        return null;
    }

    @Override
    public WalmartAdvertisingKeywordReport getLastKeywordReport(int puid, Long shopId) {
        return null;
    }

    @Override
    public Page getPageListKeywordReport(int puid, int pageNo, int pageSize, Map<String, Object> queryParams) {
        return null;
    }

    @Override
    public List<WalmartAdvertisingKeywordReportPage> getListKeywordReport(int puid, Map<String, Object> queryParams) {
        return null;
    }

    @Override
    public List<WalmartAdvertisingGroupReportPage> getSumReportOrderByReportDate(int puid, Map<String, Object> queryParams) {
        return null;
    }

    @Override
    public Page getSumReportOrderByReportDatePage(int puid, int pageNo, int pageSize, Map<String, Object> queryParams) {
        return null;
    }

    @Override
    public WalmartAdvertisingGroupReportPage getSumReportDate(int puid, int pageNo, int pageSize, Map<String, Object> queryParams) {
        return null;
    }

    @Override
    public WalmartAdvertisingGroupReportPage getSumReport(int puid, Map<String, Object> queryParams) {
        return null;
    }
//    private static Logger logger = LoggerFactory.getLogger(WalmartAdvertisingKeywordReportDaoImpl.class);
//    private WalmartAdvertisingKeywordReportMapper mapper = new WalmartAdvertisingKeywordReportMapper();
//    private static final WalmartAdReportMapper<WalmartAdvertisingKeywordReportPage, WalmartAdvertisingKeywordReportPageMapper>
//            shopReportPageMapper = new WalmartAdReportMapper<>(new WalmartAdvertisingKeywordReportPageMapper());
//    private static final WalmartAdReportMapper<WalmartAdvertisingGroupReportPage, WalmartAdvertisingDateReportPageMapper>
//            reportDateMapper = new WalmartAdReportMapper<>(new WalmartAdvertisingDateReportPageMapper());
//    private static final WalmartAdReportMapper<WalmartAdvertisingGroupReportPage, WalmartAdvertisingSumDateReportMapper>
//            reportSumDateMapper = new WalmartAdReportMapper<>(new WalmartAdvertisingSumDateReportMapper());
//
//    private static final WalmartAdReportMapper<WalmartAdvertisingGroupReportPage, WalmartAdvertisingReportPageMapper>
//            reportIndicatorMapper = new WalmartAdReportMapper<>(new WalmartAdvertisingReportPageMapper());
//    @Override
//    protected String getSeqTable() {
//        return "t_dxm_walmart_advertising_report_seq";
//    }
//
//    @Override
//    public Long add(WalmartAdvertisingKeywordReport keywordReport) {
//        JdbcTemplate jdbcTemplate = getJdbcTemplate(Long.valueOf(keywordReport.getPuid()));
//        Long id = getId(keywordReport.getPuid());
//        StringBuilder sb = new StringBuilder();
//        sb.append("insert into t_walmart_advertising_keyword_report (id,puid,shop_id,report_date,campaign_id,ad_group_id,keyword_id,ad_spend,num_ads_clicks,num_ads_shown,");
//        sb.append(" advertised_sku_sales_3days,advertised_sku_sales_14days,advertised_sku_sales_30days,advertised_sku_units_3days,advertised_sku_units_14days,advertised_sku_units_30days,attributed_orders_3days,attributed_orders_14days,attributed_orders_30days,");
//        sb.append(" attributed_units_3days,attributed_units_14days,attributed_units_30days,attributed_sales_3days,attributed_sales_14days,attributed_sales_30days,other_sku_sales_3days,other_sku_sales_14days,other_sku_sales_30days,other_sku_units_3days,other_sku_units_14days,other_sku_units_30days,");
//        sb.append(" in_store_advertised_sales_3days,in_store_advertised_sales_14days,in_store_advertised_sales_30days,in_store_attributed_sales_3days,in_store_attributed_sales_14days,in_store_attributed_sales_30days,in_store_orders_3days,in_store_orders_14days,in_store_orders_30days,in_store_other_sales_3days,in_store_other_sales_14days,in_store_other_sales_30days,in_store_units_sold_3days,in_store_units_sold_14days,in_store_units_sold_30days,");
//        sb.append(" create_time,update_time)");
//        sb.append(" values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,now(),now() )");
//        List<Object> argsList = new ArrayList<>();
//        argsList.add(id);
//        argsList.add(keywordReport.getPuid());
//        argsList.add(keywordReport.getShopId());
//        argsList.add(keywordReport.getReportDate());
//        argsList.add(keywordReport.getCampaignId());
//        argsList.add(keywordReport.getAdGroupId());
//        argsList.add(keywordReport.getKeywordId());
//        argsList.add(keywordReport.getAdSpend());
//        argsList.add(keywordReport.getNumAdsClicks());
//        argsList.add(keywordReport.getNumAdsShown());
//
//        argsList.add(keywordReport.getAdvertisedSkuSales3days());
//        argsList.add(keywordReport.getAdvertisedSkuSales14days());
//        argsList.add(keywordReport.getAdvertisedSkuSales30days());
//        argsList.add(keywordReport.getAdvertisedSkuUnits3days());
//        argsList.add(keywordReport.getAdvertisedSkuUnits14days());
//        argsList.add(keywordReport.getAdvertisedSkuUnits30days());
//        argsList.add(keywordReport.getAttributedOrders3days());
//        argsList.add(keywordReport.getAttributedOrders14days());
//        argsList.add(keywordReport.getAttributedOrders30days());
//
//        argsList.add(keywordReport.getAttributedUnits3days());
//        argsList.add(keywordReport.getAttributedUnits14days());
//        argsList.add(keywordReport.getAttributedUnits30days());
//        argsList.add(keywordReport.getAttributedSales3days());
//        argsList.add(keywordReport.getAttributedSales14days());
//        argsList.add(keywordReport.getAttributedSales30days());
//        argsList.add(keywordReport.getOtherSkuSales3days());
//        argsList.add(keywordReport.getOtherSkuSales14days());
//        argsList.add(keywordReport.getOtherSkuSales30days());
//        argsList.add(keywordReport.getOtherSkuUnits3days());
//        argsList.add(keywordReport.getOtherSkuUnits14days());
//        argsList.add(keywordReport.getOtherSkuUnits30days());
//
//        argsList.add(keywordReport.getInStoreAdvertisedSales3days());
//        argsList.add(keywordReport.getInStoreAdvertisedSales14days());
//        argsList.add(keywordReport.getInStoreAdvertisedSales30days());
//        argsList.add(keywordReport.getInStoreAttributedSales3days());
//        argsList.add(keywordReport.getInStoreAttributedSales14days());
//        argsList.add(keywordReport.getInStoreAttributedSales30days());
//        argsList.add(keywordReport.getInStoreOrders3days());
//        argsList.add(keywordReport.getInStoreOrders14days());
//        argsList.add(keywordReport.getInStoreOrders30days());
//        argsList.add(keywordReport.getInStoreOtherSales3days());
//        argsList.add(keywordReport.getInStoreOtherSales14days());
//        argsList.add(keywordReport.getInStoreOtherSales30days());
//        argsList.add(keywordReport.getInStoreUnitsSold3days());
//        argsList.add(keywordReport.getInStoreUnitsSold14days());
//        argsList.add(keywordReport.getInStoreUnitsSold30days());
//        int t = update(jdbcTemplate, sb.toString(), argsList.toArray());
//        return t > 0 ? id : 0L;
//    }
//
//    @Override
//    public int update(WalmartAdvertisingKeywordReport keywordReport) {
//        JdbcTemplate jdbcTemplate = getJdbcTemplate(Long.valueOf(keywordReport.getPuid()));
//        Map<String, Object> params = getParamMap(keywordReport);
//        return updateTable(jdbcTemplate, "t_walmart_advertising_keyword_report", params, "where `id` = " + keywordReport.getId());
//    }
//
//
//    private Map<String, Object> getParamMap(WalmartAdvertisingKeywordReport groupReport) {
//        Map<String, Object> params = new HashMap<>();
//        params.put("ad_spend", groupReport.getAdSpend());
//        params.put("num_ads_clicks", groupReport.getNumAdsClicks());
//        params.put("num_ads_shown", groupReport.getNumAdsShown());
//
//        params.put("advertised_sku_sales_3days", groupReport.getAdvertisedSkuSales3days());
//        params.put("advertised_sku_sales_14days", groupReport.getAdvertisedSkuSales14days());
//        params.put("advertised_sku_sales_30days", groupReport.getAdvertisedSkuSales30days());
//        params.put("advertised_sku_units_3days", groupReport.getAdvertisedSkuUnits3days());
//        params.put("advertised_sku_units_14days", groupReport.getAdvertisedSkuUnits14days());
//        params.put("advertised_sku_units_30days", groupReport.getAdvertisedSkuUnits30days());
//        params.put("attributed_orders_3days", groupReport.getAttributedOrders3days());
//        params.put("attributed_orders_14days", groupReport.getAttributedOrders14days());
//        params.put("attributed_orders_30days", groupReport.getAttributedOrders30days());
//
//        params.put("attributed_units_3days", groupReport.getAttributedUnits3days());
//        params.put("attributed_units_14days", groupReport.getAttributedUnits14days());
//        params.put("attributed_units_30days", groupReport.getAttributedUnits30days());
//        params.put("attributed_sales_3days", groupReport.getAttributedSales3days());
//        params.put("attributed_sales_14days", groupReport.getAttributedSales14days());
//        params.put("attributed_sales_30days", groupReport.getAttributedSales30days());
//        params.put("other_sku_sales_3days", groupReport.getOtherSkuSales3days());
//        params.put("other_sku_sales_14days", groupReport.getOtherSkuSales14days());
//        params.put("other_sku_sales_30days", groupReport.getOtherSkuSales30days());
//        params.put("other_sku_units_3days", groupReport.getOtherSkuUnits3days());
//        params.put("other_sku_units_14days", groupReport.getOtherSkuUnits14days());
//        params.put("other_sku_units_30days", groupReport.getOtherSkuUnits30days());
//
//        params.put("in_store_advertised_sales_3days", groupReport.getInStoreAdvertisedSales3days());
//        params.put("in_store_advertised_sales_14days", groupReport.getInStoreAdvertisedSales14days());
//        params.put("in_store_advertised_sales_30days", groupReport.getInStoreAdvertisedSales30days());
//        params.put("in_store_attributed_sales_3days", groupReport.getInStoreAttributedSales3days());
//        params.put("in_store_attributed_sales_14days", groupReport.getInStoreAttributedSales14days());
//        params.put("in_store_attributed_sales_30days", groupReport.getInStoreAttributedSales30days());
//        params.put("in_store_orders_3days", groupReport.getInStoreOrders3days());
//        params.put("in_store_orders_14days", groupReport.getInStoreOrders14days());
//        params.put("in_store_orders_30days", groupReport.getInStoreOrders30days());
//        params.put("in_store_other_sales_3days", groupReport.getInStoreOtherSales3days());
//        params.put("in_store_other_sales_14days", groupReport.getInStoreOtherSales14days());
//        params.put("in_store_other_sales_30days", groupReport.getInStoreOtherSales30days());
//        params.put("in_store_units_sold_3days", groupReport.getInStoreUnitsSold3days());
//        params.put("in_store_units_sold_14days", groupReport.getInStoreUnitsSold14days());
//        params.put("in_store_units_sold_30days", groupReport.getInStoreUnitsSold30days());
//
//        params.put("update_time", "now()");
//        return params;
//    }
//
//    @Override
//    public int delete(Integer puid, Long id) {
//        JdbcTemplate jdbcTemplate = getJdbcTemplate(Long.valueOf(puid));
//        return jdbcTemplate.update("DELETE from t_walmart_advertising_keyword_report where puid = ? and id = ?", puid, id);
//    }
//
//    @Override
//    public WalmartAdvertisingKeywordReport getByKeywordId(int puid, Long shopId, String reportDate, Long campaignId, Long gruopId, Long keywordId) {
//        JdbcTemplate jdbcTemplate = getJdbcTemplate((long) puid);
//        String sql = "select * from `t_walmart_advertising_keyword_report` where puid = ? and shop_id = ? and report_date = ? and campaign_id = ? and ad_group_id = ? and keyword_id = ?";
//        List<WalmartAdvertisingKeywordReport> list = jdbcTemplate.query(sql, new Object[]{puid, shopId, reportDate, campaignId, gruopId, keywordId}, mapper);
//        if (list != null && list.size() > 0) {
//            return list.get(0);
//        }
//        return null;
//    }
//
//
//    @Override
//    public WalmartAdvertisingKeywordReport getLastKeywordReport(int puid, Long shopId) {
//        JdbcTemplate jdbcTemplate = getJdbcTemplate((long) puid);
//        String sql = "select * from `t_walmart_advertising_keyword_report` where puid = ? and shop_id = ? order by report_date desc limit 1";
//        List<WalmartAdvertisingKeywordReport> list = jdbcTemplate.query(sql, new Object[]{puid, shopId}, mapper);
//        if (list != null && list.size() > 0) {
//            return list.get(0);
//        }
//        return null;
//    }
//
//    @Override
//    public Page getPageListKeywordReport(int puid, int pageNo, int pageSize, Map<String, Object> queryParams) {
//        JdbcTemplate jdbcTemplate = getJdbcTemplate((long) puid);
//        // 条件搜索
//        List<Object> argsList = Lists.newArrayList();
//        // 联表查询，统计指定时间段内的报告数据
//        StringBuilder selectSql = new StringBuilder("select * from (select `puid`,`shop_id`,`keyword_id`," + WalmartAdDaoUtil.reportFieldsSum(MapUtils.getInteger(queryParams, "attributeDay")));
//        selectSql.append(" from `t_walmart_advertising_keyword_report`");
//        StringBuilder countSql = new StringBuilder("SELECT count(*) FROM (SELECT * FROM`t_walmart_advertising_keyword_report`");
//        StringBuilder whereSql = new StringBuilder(" where puid = ? ");
//        argsList.add(puid);
//        combinationSqlString(queryParams,argsList, whereSql);
//        selectSql.append(whereSql);
//        countSql.append(whereSql);
//        String sortName = MapUtils.getString(queryParams, "sortName");
//        if(StringUtils.isBlank(sortName)){
//            sortName = "sales";
//        }
//        Integer sortValue = MapUtils.getIntValue(queryParams, "sortValue");
//        selectSql.append(" group by puid, shop_id, keyword_id ) t ").append(WalmartAdDaoUtil.getOrderByState(sortName, sortValue));
//        countSql.append(" group by puid, shop_id, keyword_id ) t ");
//
//        logger.info("{}", countSql);
//        logger.info("{}", selectSql);
//        Object[] args = argsList.toArray();
//        logger.info(Arrays.toString(args));
//        return getPageResult(jdbcTemplate, pageNo, pageSize, countSql.toString(), args, selectSql.toString(), args, shopReportPageMapper);
//    }
//
//    @Override
//    public List<WalmartAdvertisingKeywordReportPage> getListKeywordReport(int puid, Map<String, Object> queryParams) {
//        JdbcTemplate jdbcTemplate = getJdbcTemplate((long) puid);
//        // 条件搜索
//        List<Object> argsList = Lists.newArrayList();
//        // 联表查询，统计指定时间段内的报告数据
//        StringBuilder selectSql = new StringBuilder("select `puid`,`shop_id`,`keyword_id`," + WalmartAdDaoUtil.reportFieldsSum(MapUtils.getInteger(queryParams, "attributeDay")));
//        selectSql.append(" from `t_walmart_advertising_keyword_report`");
//        StringBuilder whereSql = new StringBuilder(" where puid = ? ");
//        argsList.add(puid);
//        combinationSqlString(queryParams, argsList, whereSql);
//        selectSql.append(whereSql);
//        selectSql.append(" group by puid, shop_id, keyword_id ");
//        logger.info("{}", selectSql);
//        Object[] args = argsList.toArray();
//        logger.info(Arrays.toString(args));
//        return jdbcTemplate.query(selectSql.toString(), args, shopReportPageMapper);
//    }
//
//    @Override
//    public List<WalmartAdvertisingGroupReportPage> getSumReportOrderByReportDate(int puid, Map<String, Object> queryParams) {
//        JdbcTemplate jdbcTemplate = getJdbcTemplate((long) puid);
//        // 条件搜索
//        List<Object> argsList = Lists.newArrayList();
//        // 联表查询，统计指定时间段内的报告数据
//        StringBuilder selectSql = new StringBuilder("select `puid`,`report_date`," + WalmartAdDaoUtil.reportFieldsSum(MapUtils.getInteger(queryParams, "attributeDay")));
//        selectSql.append(" from `t_walmart_advertising_keyword_report`");
//        StringBuilder whereSql = new StringBuilder(" where puid = ? ");
//        argsList.add(puid);
//        combinationSqlString(queryParams, argsList, whereSql);
//        selectSql.append(whereSql);
//        selectSql.append(" group by puid, report_date");
//        logger.info("{}", selectSql);
//        Object[] args = argsList.toArray();
//        logger.info(Arrays.toString(args));
//        return jdbcTemplate.query(selectSql.toString(), args, reportDateMapper);
//    }
//
//    @Override
//    public Page getSumReportOrderByReportDatePage(int puid, int pageNo, int pageSize, Map<String, Object> queryParams) {
//        JdbcTemplate jdbcTemplate = getJdbcTemplate((long) puid);
//        // 条件搜索
//        List<Object> argsList = Lists.newArrayList();
//        // 联表查询，统计指定时间段内的报告数据
//        StringBuilder selectSql = new StringBuilder("select * from (select `puid`,`report_date`," + WalmartAdDaoUtil.reportFieldsSum(MapUtils.getInteger(queryParams, "attributeDay")));
//        selectSql.append(" from `t_walmart_advertising_keyword_report`");
//        StringBuilder countSql = new StringBuilder("SELECT count(*) FROM ( SELECT * FROM `t_walmart_advertising_keyword_report`");
//        StringBuilder whereSql = new StringBuilder(" where puid = ? ");
//        argsList.add(puid);
//        combinationSqlString(queryParams, argsList, whereSql);
//        selectSql.append(whereSql);
//        countSql.append(whereSql);
//        String sortName = MapUtils.getString(queryParams, "sortName");
//        if (StringUtils.isBlank(sortName)) {
//            sortName = "report_date";
//        }
//        Integer sortValue = MapUtils.getIntValue(queryParams, "sortValue");
//        selectSql.append(" group by puid, report_date ) t ").append(WalmartAdDaoUtil.getOrderByState(sortName, sortValue));
//        countSql.append(" group by puid, report_date ) t ");
//
//        logger.info("{}", countSql);
//        logger.info("{}", selectSql);
//        Object[] args = argsList.toArray();
//        logger.info(Arrays.toString(args));
//        return getPageResult(jdbcTemplate, pageNo, pageSize, countSql.toString(), args, selectSql.toString(), args, reportDateMapper);
//    }
//
//    @Override
//    public WalmartAdvertisingGroupReportPage getSumReportDate(int puid, int pageNo, int pageSize, Map<String, Object> queryParams) {
//        JdbcTemplate jdbcTemplate = getJdbcTemplate((long) puid);
//        // 条件搜索
//        List<Object> argsList = Lists.newArrayList();
//        // 联表查询，统计指定时间段内的报告数据
//        StringBuilder selectSql = new StringBuilder("select " + WalmartAdDaoUtil.reportFieldsSum(MapUtils.getInteger(queryParams, "attributeDay")));
//        selectSql.append(" from `t_walmart_advertising_keyword_report`");
//        StringBuilder whereSql = new StringBuilder(" where puid = ? ");
//        argsList.add(puid);
//        combinationSqlString(queryParams, argsList, whereSql);
//        selectSql.append(whereSql);
//        int start = pageSize * (pageNo - 1);
//        selectSql.append(" limit ?,? ");
//        argsList.add(start);
//        argsList.add(pageSize);
//        logger.info("{}", selectSql);
//        Object[] args = argsList.toArray();
//        logger.info(Arrays.toString(args));
//        List<WalmartAdvertisingGroupReportPage> list = jdbcTemplate.query(selectSql.toString(), args, reportSumDateMapper);
//        if (list != null && list.size() > 0) {
//            return list.get(0);
//        }
//        return null;
//    }
//
//    @Override
//    public WalmartAdvertisingGroupReportPage getSumReport(int puid, Map<String, Object> queryParams) {
//        JdbcTemplate jdbcTemplate = getJdbcTemplate((long) puid);
//        // 条件搜索
//        List<Object> argsList = Lists.newArrayList();
//        // 联表查询，统计指定时间段内的报告数据
//        StringBuilder selectSql = new StringBuilder("select `puid`," + WalmartAdDaoUtil.reportFieldsSum(MapUtils.getInteger(queryParams, "attributeDay")));
//        selectSql.append(" from `t_walmart_advertising_keyword_report`");
//        StringBuilder whereSql = new StringBuilder(" where puid = ? ");
//        argsList.add(puid);
//        combinationSqlString(queryParams, argsList, whereSql);
//        selectSql.append(whereSql);
//        logger.info("{}", selectSql);
//        Object[] args = argsList.toArray();
//        logger.info(Arrays.toString(args));
//        List<WalmartAdvertisingGroupReportPage> list = jdbcTemplate.query(selectSql.toString(), args, reportIndicatorMapper);
//        if (list != null && list.size() > 0) {
//            return list.get(0);
//        }
//        return null;
//    }
//
//    private void combinationSqlString(Map<String, Object> queryParams,
//                                      List<Object> argsList, StringBuilder whereSql) {
//        Object shopIds = MapUtils.getObject(queryParams, "shopId");
//        if (Objects.nonNull(shopIds)) {
//            whereSql.append(" and shop_id in (").append(Joiner.on(",").join((List<Long>) shopIds)).append(") ");
//        }
//        if (queryParams.containsKey("startDate")) {
//            whereSql.append(" and report_date >= ? ");
//            argsList.add(queryParams.get("startDate"));
//        }
//        if (queryParams.containsKey("endDate")) {
//            whereSql.append(" and report_date <= ? ");
//            argsList.add(queryParams.get("endDate"));
//        }
//
//        Object keywordIds = MapUtils.getObject(queryParams, "keywordIds");
//        if (Objects.nonNull(keywordIds)) {
//            whereSql.append(" and keyword_id in (").append(Joiner.on(",").join((List<Long>) keywordIds)).append(") ");
//        }
//
//        String keywordId = MapUtils.getString(queryParams, "keywordId");
//        if (StringUtils.isNotBlank(keywordId)) {
//            whereSql.append(" and keyword_id =? ");
//            argsList.add(keywordId);
//        }
//
//        String adGroupId = MapUtils.getString(queryParams, "adGroupId");
//        if (StringUtils.isNotBlank(adGroupId)) {
//            whereSql.append(" and ad_group_id = ? ");
//            argsList.add(adGroupId);
//        }
//
//        String campaignId = MapUtils.getString(queryParams, "campaignId");
//        if (StringUtils.isNotBlank(campaignId)) {
//            whereSql.append(" and campaign_id = ? ");
//            argsList.add(campaignId);
//        }
//    }


    @Override
    public WalmartAdvertisingKeywordReport getLastKeywordReport(int puid, Integer shopId) {
        JdbcTemplate jdbcTemplate = getJdbcTemplate(puid);
        String sql = "select * from `t_walmart_advertising_keyword_report` where puid = ? and shop_id = ? order by report_date desc limit 1";
        List<WalmartAdvertisingKeywordReport> list = jdbcTemplate.query(sql, new Object[]{puid, shopId}, getRowMapper());
        if (list != null && list.size() > 0) {
            return list.get(0);
        }
        return null;
    }


    @Override
    public WalmartAdvertisingKeywordReport getByKeywordId(int puid, Integer shopId, String reportDate, String campaignId, String gruopId, String keywordId) {
        JdbcTemplate jdbcTemplate = getJdbcTemplate(puid);
        String sql = "select * from `t_walmart_advertising_keyword_report` where puid = ? and shop_id = ? and report_date = ? and campaign_id = ? and ad_group_id = ? and keyword_id = ?";
        List<WalmartAdvertisingKeywordReport> list = jdbcTemplate.query(sql, new Object[]{puid, shopId, reportDate, campaignId, gruopId, keywordId}, getRowMapper());
        if (list != null && list.size() > 0) {
            return list.get(0);
        }
        return null;
    }


    @Override
    public int insertOrUpdate(int puid, List<WalmartAdvertisingKeywordReport> list) {



        StringBuilder sql = new StringBuilder();

        sql.append(" insert into " + getJdbcHelper().getTable() + " (puid,shop_id, marketplace_code ,report_date,campaign_id,ad_group_id,keyword_id,ad_spend,num_ads_clicks,num_ads_shown, ");
        sql.append(" advertised_sku_sales_3days,advertised_sku_sales_14days,advertised_sku_sales_30days,advertised_sku_units_3days,advertised_sku_units_14days,advertised_sku_units_30days,attributed_orders_3days,attributed_orders_14days,attributed_orders_30days,");
        sql.append(" attributed_units_3days,attributed_units_14days,attributed_units_30days,attributed_sales_3days,attributed_sales_14days,attributed_sales_30days,other_sku_sales_3days,other_sku_sales_14days,other_sku_sales_30days,other_sku_units_3days,other_sku_units_14days,other_sku_units_30days,");
        //sql.append(" in_store_advertised_sales_3days,in_store_advertised_sales_14days,in_store_advertised_sales_30days,in_store_attributed_sales_3days,in_store_attributed_sales_14days,in_store_attributed_sales_30days,in_store_orders_3days,in_store_orders_14days,in_store_orders_30days,in_store_other_sales_3days,in_store_other_sales_14days,in_store_other_sales_30days,in_store_units_sold_3days,in_store_units_sold_14days,in_store_units_sold_30days,");
        sql.append(" create_time,update_time)");

        sql.append(" values ");
        List<Object> argsList = Lists.newArrayList();
        for (WalmartAdvertisingKeywordReport report : list) {
            sql.append(" (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,now(),now()),");
            argsList.add(report.getPuid());
            argsList.add(report.getShopId());
            argsList.add(report.getMarketplaceCode());
            argsList.add(report.getReportDate());
            argsList.add(report.getCampaignId());
            argsList.add(report.getAdGroupId());
            argsList.add(report.getKeywordId());
            argsList.add(report.getAdSpend());
            argsList.add(report.getNumAdsClicks());
            argsList.add(report.getNumAdsShown());

            argsList.add(report.getAdvertisedSkuSales3days());
            argsList.add(report.getAdvertisedSkuSales14days());
            argsList.add(report.getAdvertisedSkuSales30days());
            argsList.add(report.getAdvertisedSkuUnits3days());
            argsList.add(report.getAdvertisedSkuUnits14days());
            argsList.add(report.getAdvertisedSkuUnits30days());
            argsList.add(report.getAttributedOrders3days());
            argsList.add(report.getAttributedOrders14days());
            argsList.add(report.getAttributedOrders30days());

            argsList.add(report.getAttributedUnits3days());
            argsList.add(report.getAttributedUnits14days());
            argsList.add(report.getAttributedUnits30days());
            argsList.add(report.getAttributedSales3days());
            argsList.add(report.getAttributedSales14days());
            argsList.add(report.getAttributedSales30days());
            argsList.add(report.getOtherSkuSales3days());
            argsList.add(report.getOtherSkuSales14days());
            argsList.add(report.getOtherSkuSales30days());
            argsList.add(report.getOtherSkuUnits3days());
            argsList.add(report.getOtherSkuUnits14days());
            argsList.add(report.getOtherSkuUnits30days());

//            argsList.add(report.getInStoreAdvertisedSales3days());
//            argsList.add(report.getInStoreAdvertisedSales14days());
//            argsList.add(report.getInStoreAdvertisedSales30days());
//            argsList.add(report.getInStoreAttributedSales3days());
//            argsList.add(report.getInStoreAttributedSales14days());
//            argsList.add(report.getInStoreAttributedSales30days());
//            argsList.add(report.getInStoreOrders3days());
//            argsList.add(report.getInStoreOrders14days());
//            argsList.add(report.getInStoreOrders30days());
//            argsList.add(report.getInStoreOtherSales3days());
//            argsList.add(report.getInStoreOtherSales14days());
//            argsList.add(report.getInStoreOtherSales30days());
//            argsList.add(report.getInStoreUnitsSold3days());
//            argsList.add(report.getInStoreUnitsSold14days());
//            argsList.add(report.getInStoreUnitsSold30days());


        }
        sql.deleteCharAt(sql.length() - 1);
        sql.append(" on duplicate key update ");

        sql.append("`ad_spend`=values(ad_spend),`num_ads_clicks`=values(num_ads_clicks),");
        sql.append("`num_ads_shown`=values(num_ads_shown),`advertised_sku_sales_3days`=values(advertised_sku_sales_3days),");
        sql.append("`advertised_sku_sales_14days`=values(advertised_sku_sales_14days),`advertised_sku_sales_30days`=values(advertised_sku_sales_30days),");

        sql.append("`advertised_sku_units_3days`=values(advertised_sku_units_3days),`advertised_sku_units_14days`=values(advertised_sku_units_14days),");

        sql.append("`advertised_sku_units_30days`=values(advertised_sku_units_30days),`attributed_orders_3days`=values(attributed_orders_3days),");
        sql.append("`attributed_orders_14days`=values(attributed_orders_14days),`attributed_orders_30days`=values(attributed_orders_30days),");

        sql.append("`attributed_units_3days`=values(attributed_units_3days),`attributed_units_14days`=values(attributed_units_14days),");
        sql.append("`attributed_units_30days`=values(attributed_units_30days),`attributed_sales_3days`=values(attributed_sales_3days),");
        sql.append("`attributed_sales_14days`=values(attributed_sales_14days),`attributed_sales_30days`=values(attributed_sales_30days),");
        sql.append("`other_sku_sales_3days`=values(other_sku_sales_3days),`other_sku_sales_14days`=values(other_sku_sales_14days),");
        sql.append("`other_sku_sales_30days`=values(other_sku_sales_30days),`other_sku_units_3days`=values(other_sku_units_3days),");
        sql.append("`other_sku_units_14days`=values(other_sku_units_14days),`other_sku_units_30days`=values(other_sku_units_30days)");

//        sql.append("`in_store_advertised_sales_3days`=values(in_store_advertised_sales_3days),`in_store_advertised_sales_14days`=values(in_store_advertised_sales_14days),");
//        sql.append("`in_store_advertised_sales_30days`=values(in_store_advertised_sales_30days),`in_store_attributed_sales_3days`=values(in_store_attributed_sales_3days),");
//        sql.append("`in_store_attributed_sales_14days`=values(in_store_attributed_sales_14days),`in_store_attributed_sales_30days`=values(in_store_attributed_sales_30days),");
//        sql.append("`in_store_orders_3days`=values(in_store_orders_3days),`in_store_orders_14days`=values(in_store_orders_14days),");
//        sql.append("`in_store_orders_30days`=values(in_store_orders_30days),`in_store_other_sales_3days`=values(in_store_other_sales_3days),");
//        sql.append("`in_store_other_sales_14days`=values(in_store_other_sales_14days),`in_store_other_sales_30days`=values(in_store_other_sales_30days),");
//        sql.append("`in_store_units_sold_3days`=values(in_store_units_sold_3days),`in_store_units_sold_14days`=values(in_store_units_sold_14days),");
//        sql.append("`in_store_units_sold_30days`=values(in_store_units_sold_30days)");

        return getJdbcTemplate(puid).update(sql.toString(), argsList.toArray());

    }
}
