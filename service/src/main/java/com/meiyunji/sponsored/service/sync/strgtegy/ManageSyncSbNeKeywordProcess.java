package com.meiyunji.sponsored.service.sync.strgtegy;

import com.meiyunji.sponsored.common.support.RedisService;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.config.DynamicRefreshConfiguration;
import com.meiyunji.sponsored.service.cpc.service2.sb.impl.CpcSbNeKeywordApiService;
import com.meiyunji.sponsored.service.stream.enums.StreamConstants;
import com.meiyunji.sponsored.service.stream.service.IAmazonManagementStreamTaskRetryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Collections;
import java.util.Optional;

/**
 * @author: ys
 * @date: 2024/1/17 20:33
 * @describe:
 */
@Service
public class ManageSyncSbNeKeywordProcess extends AbstractSyncServerStatusProcessStrategy {

    @Autowired
    private CpcSbNeKeywordApiService cpcSbNeKeywordApiService;


    public ManageSyncSbNeKeywordProcess(IAmazonManagementStreamTaskRetryService amazonManagementStreamTaskRetryService, DynamicRefreshConfiguration dynamicRefreshConfiguration, RedisService redisService) {
        super(amazonManagementStreamTaskRetryService, dynamicRefreshConfiguration, redisService);

    }

    @Override
    public int getMaxCount() {
        return StreamConstants.SB_MAX_NE_KEYWORD_IDS_COUNT;
    }

    @Override
    public void doExecute(ShopAuth shopAuth, String ids) {
        cpcSbNeKeywordApiService.syncNeKeywords(shopAuth, null, null,
                Optional.ofNullable(ids).map(l -> l.split(",")).map(Arrays::asList).orElse(Collections.emptyList()), null, true);
    }
}
