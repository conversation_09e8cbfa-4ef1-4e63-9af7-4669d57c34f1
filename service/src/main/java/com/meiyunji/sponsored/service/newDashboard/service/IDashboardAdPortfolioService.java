package com.meiyunji.sponsored.service.newDashboard.service;

import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.rpc.newDashboard.DashboardAdCampaignOrGroupOrPortfolioResponseVo;
import com.meiyunji.sponsored.service.newDashboard.vo.DashboardCampaignOrGroupOrPortfolioReqVo;

import java.util.List;

/**
 * @author: ys
 * @date: 2024/4/15 14:41
 * @describe:
 */
public interface IDashboardAdPortfolioService {
    DashboardAdCampaignOrGroupOrPortfolioResponseVo.Page queryPortfolioCharts(DashboardCampaignOrGroupOrPortfolioReqVo req);

    List<String> exportPortfolioCharts(DashboardCampaignOrGroupOrPortfolioReqVo reqVo);
}
