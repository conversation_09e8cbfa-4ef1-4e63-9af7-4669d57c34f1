package com.meiyunji.sponsored.service.autoRule.dao.impl;

import com.google.api.client.util.Lists;
import com.meiyunji.sponsored.common.springjdbc.AdBaseDaoImpl;
import com.meiyunji.sponsored.common.springjdbc.BaseDaoImpl;
import com.meiyunji.sponsored.service.autoRule.dao.AdvertiseAutoRuleStatusSequenceDao;
import com.meiyunji.sponsored.service.autoRule.po.AdvertiseAutoRuleStatusSequence;
import org.springframework.jdbc.support.GeneratedKeyHolder;
import org.springframework.jdbc.support.KeyHolder;
import org.springframework.stereotype.Repository;

import java.math.BigInteger;
import java.sql.Statement;
import java.util.List;
import java.util.Map;

@Repository
public class AdvertiseAutoRuleStatusSequenceDaoImpl extends AdBaseDaoImpl<AdvertiseAutoRuleStatusSequence> implements AdvertiseAutoRuleStatusSequenceDao {
    /**
     * 兼容新旧版本分时调价taskId的taskId生成器
     *
     * @return
     */
    @Override
    public Long genId() {
        BigInteger id = null;
        String sql = "REPLACE INTO `t_ad_auto_rule_status_sequence` (`stub`) VALUES ('S')";
        KeyHolder keyHolder = new GeneratedKeyHolder();
        this.getJdbcTemplate().update(connection ->
                connection.prepareStatement(sql, Statement.RETURN_GENERATED_KEYS), keyHolder);

        List<Map<String, Object>> list = keyHolder.getKeyList();
        if (list.size() > 0) {
            Map<String, Object> map = list.get(0);
            id = (BigInteger) map.get("GENERATED_KEY");
        }
        return id.longValue();
    }

    @Override
    public List<Long> batchGenId(Integer size) {
        List<Long> longIds = Lists.newArrayList();
        StringBuilder sql = new StringBuilder("REPLACE INTO " + getJdbcHelper().getTable() + " (`stub`) VALUES");
        for (int i = 0; i<size; i++) {
            sql.append(" ('S'),");
        }
        sql.deleteCharAt(sql.length() - 1);
        KeyHolder keyHolder = new GeneratedKeyHolder();
        this.getJdbcTemplate().update(connection ->
                connection.prepareStatement(sql.toString(), Statement.RETURN_GENERATED_KEYS), keyHolder);

        List<Map<String, Object>> list = keyHolder.getKeyList();
        if (list.size() > 0) {
            for (Map<String,Object> map : list) {
                BigInteger id = (BigInteger) map.get("GENERATED_KEY");
                longIds.add(id.longValue());
            }
        }
        return longIds;
    }
}
