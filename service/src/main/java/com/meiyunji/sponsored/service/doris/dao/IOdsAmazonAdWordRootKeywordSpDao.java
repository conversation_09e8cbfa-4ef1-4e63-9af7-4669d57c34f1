package com.meiyunji.sponsored.service.doris.dao;

import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.springjdbc.IDorisBaseDao;
import com.meiyunji.sponsored.service.doris.po.OdsAmazonAdWordRootKeywordSp;
import com.meiyunji.sponsored.service.wordFrequency.dto.WordFrequencyAdMetricDto;
import com.meiyunji.sponsored.service.wordFrequency.qo.GetWordRootAggregateDataQo;
import com.meiyunji.sponsored.service.wordFrequency.qo.GetWordRootDataQo;
import com.meiyunji.sponsored.service.wordFrequency.vo.GetWordRootAggregateDataVo;
import com.meiyunji.sponsored.service.wordFrequency.vo.GetWordRootDataVo;

import java.util.List;

/**
 * @author: liweibin
 * @email: <EMAIL>
 * @date: 2024-05-29  17:14
 */
public interface IOdsAmazonAdWordRootKeywordSpDao extends IDorisBaseDao<OdsAmazonAdWordRootKeywordSp> {

    /**
     * 关键词词频列表页获取所有词根
     */
    Page<GetWordRootDataVo> pageList(Integer puid, GetWordRootDataQo qo);

    /**
     * 关键词词频列表页根据词根获取所有数据
     */
    List<GetWordRootDataVo> pageListByWordRoots(Integer puid, GetWordRootDataQo qo, List<String> wordRoots);

    /**
     * 关键词词频列表页占比字段
     */
    WordFrequencyAdMetricDto getPageListAdMetricDto(Integer puid, GetWordRootDataQo qo);

    /**
     * 关键词词频列表页汇总数据
     */
    GetWordRootAggregateDataVo getPageListAggregateData(Integer puid, GetWordRootAggregateDataQo qo);
}
