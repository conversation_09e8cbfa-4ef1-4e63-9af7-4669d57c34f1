package com.meiyunji.sponsored.service.cpc.service2.sp;

import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.rpc.adCommon.AllTargetAggregateDataResponse;
import com.meiyunji.sponsored.rpc.adCommon.AllTargetDataResponse;
import com.meiyunji.sponsored.rpc.sp.campaign.NeTargetInfoResp;
import com.meiyunji.sponsored.rpc.sp.campaign.TargetInfoResp;
import com.meiyunji.sponsored.rpc.sp.targeting.BatchSpDataResponse;
import com.meiyunji.sponsored.rpc.vo.NeTargetingPageRpcVo;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.cpc.po.*;
import com.meiyunji.sponsored.service.cpc.qo.TargetSuggestBidBatchQo;
import com.meiyunji.sponsored.service.cpc.vo.*;
import com.meiyunji.sponsored.service.negative.request.NegativeArchiveRequest;

import java.util.List;

/**
 * Created by xp on 2021/3/30.
 * 投放相关业务
 */
public interface ICpcTargetingService {

    /**
     * 广告组下的投放-列表页
     * @param puid:
     * @param param:
     * @return :
     */
    Result<Page<TargetingPageVo>> targetingPageList(Integer puid, TargetingPageParam param);

    /**
     * 自动广告组下的投放-列表页
     * @param puid：
     * @param param：
     * @return ：
     */
    Result<List<AutoTargetingPageVo>> autoTargetingList(Integer puid, TargetingPageParam param);

    /**
     * 广告组下的否定投放-列表页
     * @param puid:
     * @param param:
     * @return :
     */
    Result<Page<NeTargetingPageVo>> neTargetingPageList(Integer puid, NeTargetingPageParam param);

    /**
     * 创建自动投放
     *
     * @param createAutoTargetingVo ：
     * @return ：
     */
    Result createAutoTargeting(CreateAutoTargetingVo createAutoTargetingVo, String loginIp);

    /**
     * 创建自动投放
     *
     * @param createAutoTargetingVo ：
     * @return ：
     */
    Result<List<TargetInfoResp>> createAutoTargetingWithAuthed(AmazonAdProfile amazonAdProfile, AmazonAdGroup amazonAdGroup, CreateAutoTargetingVo createAutoTargetingVo, ShopAuth shop, String loginIp, Boolean sync);

    /**
     * 创建手动投放
     *
     * @param createManualTargetingVo :
     * @return :
     */
    Result createManualTargeting(CreateManualTargetingVo createManualTargetingVo, String loginIp);

    /**
     * 创建关键词投放
     *
     * @param createManualTargetingVo
     * @param loginIp
     * @return
     */
    Result<List<TargetInfoResp>> createKeywordsTargetingWithAuthed(List<AmazonAdKeyword> amazonAdKeywords, AmazonAdGroup amazonAdGroup, String loginIp, ShopAuth shop, int indexOffset);

    /**
     * 创建产品投放
     *
     * @param createManualTargetingVo
     * @param loginIp
     * @return
     */
    Result<List<TargetInfoResp>> createProductTargetingWithAuthed(List<AmazonAdTargeting> amazonAdTargetings, AmazonAdGroup amazonAdGroup, String loginIp, ShopAuth shop, int indexOffset);

    /**
     * 创建投放
     *
     * @param addTargetingVo :
     * @return :
     */
    Result createTargeting(AddTargetingVo addTargetingVo, String loginIp);

    List<AmazonAdTargeting> convertAddTargetingVoToPO(Integer uid, AmazonAdGroup amazonAdGroup, List<TargetingVo> targetings);

    /**
     * 投放-改状态
     *
     * @param puid
     * @param uid
     * @param id    :
     * @param state :
     * @return :
     */
    Result updateState(Integer puid, Integer uid, Long id, String state, String loginIp);

    /**
     * 投放-改竞价
     *
     * @param puid
     * @param uid
     * @param id :
     * @param bid :
     * @return :
     */
    Result updateBid(Integer puid, Integer uid, Long id, Double bid, String loginIp);

    /**
     * 投放归档
     *
     * @param puid
     * @param uid
     * @param id ：
     * @return ：
     */
    Result archive(Integer puid, Integer uid, Long id, String loginIp);

    /**
     * 创建否定投放
     *
     * @param addNeTargetingVo :
     * @return :
     */
    Result createNeTargeting(AddNeTargetingVo addNeTargetingVo, String loginIp);

    /**
     * 创建否定投放
     *
     * @param addNeTargetingVo :
     * @return :
     */
    Result<List<NeTargetInfoResp>> createNeProductTargetingWithAuthed(List<SpNeTargetingVo> spNeTargetingVos, AmazonAdGroup amazonAdGroup, String loginIp, ShopAuth shop, int indexOffset);

    List<SpNeTargetingVo> convertNeProductTargetingVoToPo(AmazonAdGroup amazonAdGroup, Integer uid, List<NeTargetingVo> neTargetingVoList);

    /**
     * 否定投放-改竞价
     *
     * @param puid
     * @param uid
     * @param id :
     * @param state
     * @return :
     */
    Result updateNeTargetingState(Integer puid,Integer uid, Long id, String state);

    /**
     * 投放归档
     *
     * @param puid
     * @param uid
     * @param id ：
     * @return ：
     */
    Result archiveNeTargeting(Integer puid,  Integer uid, Long id, String loginIp);


    /**
     * Sp否定商品定位投放批量归档（组）
     * @param puid
     * @param shopId
     * @param uid
     * @param loginIp
     * @param idList
     * @return
     */
    Result batchArchiveNeTargeting(Integer puid, Integer shopId, Integer uid, String loginIp, List<Long> idList);

    /**
     * 根据asin获取建议分类
     * @param puid：
     * @param shopId：
     * @param adGroupId：
     */
    Result<List<SuggestCategoryVo>> suggestCategory(Integer puid, Integer shopId, String adGroupId);

    /**
     * 根据asin获取建议分类
     * @param puid：
     * @param shopId：
     * @param asinList：
     */
    Result<List<SuggestCategoryVo>> suggestCategoryByAsin(Integer puid, Integer shopId, List<String> asinList);

    /**
     * 根据分类id取建议分类
     * @param puid:
     * @param shopId:
     * @param categoryId:
     */
    Result<List<SuggestCategoryBrandVo>> suggestCategoryBrand(int puid, Integer shopId, String categoryId);

    /**
     * 获取指定投放的表现
     * @param puid:
     * @param param:
     * @return :
     */
    Result showTargetPerformance(int puid, AdPerformanceParam param);

    /**
     * 获取投放的建议竞价
     * @param puid:
     * @param shopId:
     * @param targetId:
     */
    Result<List<SuggestedTargetVo>> getSuggestedBid(int puid, Integer shopId, List<String> targetId);

    /**
     * 获取投放的建议竞价,不对投放的建议竞价进行更新
     * @param puid
     * @param shopId
     * @param details
     * @return
     */
    Result<List<SuggestedTargetVo>> batchGetSuggestedBid(int puid, Integer shopId, List<Integer> sourceShopIds, List<TargetSuggestedBidDetail> details, String targetingType);

    /**
     * 获取投放的建议竞价(多店铺)
     * @param puid:
     * @param qoList:
     */
    Result<List<SuggestedTargetVo>> getSuggestedBidMultiShop(int puid, List<TargetSuggestBidBatchQo> qoList);

    /**
     * 根据投放文本获取建议竞价
     * @param puid:
     * @param addTargetingVo:
     */
    Result getSuggestedBidByText(Integer puid, AddTargetingVo addTargetingVo);


    /**
     * 查询所有类型投放(自动,类目,asin)
     * @param puid
     * @param param
     * @return
     */
    AllTargetDataResponse.AdTargetingHomeRpcVo getOldAllTargetData(int puid, TargetingPageParam param);

    AllTargetDataResponse.AdTargetingHomeRpcVo getNewAllTargetData(int puid, TargetingPageParam param, boolean tacticTargetSearch);

    AllTargetDataResponse.AdTargetingHomeRpcVo getAllTargetData(int puid, TargetingPageParam param, boolean tacticTargetSearch);

    List<TargetingPageVo> getTargetVoList(ShopAuth shopAuth, Integer puid, TargetingPageParam param, Page<TargetingPageVo> voPage, boolean isExport);
    List<TargetingPageVo> getTacticTargetNewVoList(ShopAuth shopAuth, Integer puid, TargetingPageParam param, Page<TargetingPageVo> voPage, boolean isExport);

    AllTargetAggregateDataResponse.AdTargetingHomeRpcVo getOldAllTargetAggregateData(int puid, TargetingPageParam param);

    AllTargetAggregateDataResponse.AdTargetingHomeRpcVo getAllTargetAggregateData(int puid, TargetingPageParam param, boolean tacticTargetSearch);

    List<TargetingPageVo> getSpTargetVoList(ShopAuth shopAuth, Integer puid, TargetingPageParam param, Page<TargetingPageVo> voPage, boolean isExport);

    List<TargetingPageVo> getSbTargetVoList(ShopAuth shopAuth, Integer puid, TargetingPageParam param, Page<TargetingPageVo> voPage, boolean isExport);

    List<TargetingPageVo> getSdTargetVoList(ShopAuth shopAuth, Integer puid, TargetingPageParam param, Page<TargetingPageVo> voPage, boolean isExport);
    List<TargetingPageVo> getSdTacticTargetVoNewList(ShopAuth shopAuth, Integer puid, TargetingPageParam param, Page<TargetingPageVo> voPage, boolean isExport);

    List<TargetingPageVo> getNewSpTargetVoList(ShopAuth shopAuth, Integer puid, TargetingPageParam param, Page<TargetingPageVo> voPage, boolean isExport);

    List<TargetingPageVo> getNewSbTargetVoList(ShopAuth shopAuth, Integer puid, TargetingPageParam param, Page<TargetingPageVo> voPage, boolean isExport);

    List<TargetingPageVo> getNewSdTargetVoList(ShopAuth shopAuth, Integer puid, TargetingPageParam param, Page<TargetingPageVo> voPage, boolean isExport);
    List<TargetingPageVo> getTacticNewSdTargetVoList(ShopAuth shopAuth, Integer puid, TargetingPageParam param, Page<TargetingPageVo> voPage, boolean isExport);

    /**
     * 所有类型否定投放
     * @param puid
     * @param param
     * @return
     */
    Page<NeTargetingPageRpcVo> getAllNeTargetingPageList(int puid, NeTargetingPageParam param);

    /**
     * 所有类型否定投放
     * @param puid
     * @param param
     * @return
     */
    Page<NeTargetingPageRpcVo> getAllNeTargetingPageListV2(NeTargetingPageParam param);

    abstract Result updateBatch(List<UpdateBatchTargetVo> vos, String type, String loginIp);

    Result updateBatchMultiShop(List<UpdateBatchTargetVo> vos, String type, String loginIp);

    AllTargetDataResponse.AdTargetingHomeRpcVo getOldAllWxTargetData(int puid, TargetingPageParam param);

    AllTargetDataResponse.AdTargetingHomeRpcVo getAllWxTargetData(int puid, TargetingPageParam param);

    AllTargetAggregateDataResponse.AdTargetingHomeRpcVo getOldAllWxTargetAggregateData(int puid, TargetingPageParam param);

    AllTargetAggregateDataResponse.AdTargetingHomeRpcVo getAllWxTargetAggregateData(int puid, TargetingPageParam param);

    /**
     * 根据品牌关键词获取品牌信息
     * @param puid
     * @param shopId
     * @param keyword
     * @return
     */
    Result<List<SuggestCategoryBrandVo>> getBrandByKeyword(int puid, Integer shopId, String keyword);

    /**
     * 批量创建否定投放
     */
    Result<BatchSpDataResponse> batchAddNeTargeting(AddNeTargetingVo addNeTargetingVo, String loginIp);

    List<com.meiyunji.sponsored.rpc.sp.targeting.TargetVo> getListTarget(int puid, List<Integer> shopIds, List<String> targetIds);

    void saveDoris(List<AmazonAdTargeting> targetList);

    void saveDoris(Integer puid, Integer shopId, List<String> targetIdList);

    AllTargetAggregateDataResponse.AdTargetingHomeRpcVo getDorisAllTargetAggregateData(int puid, TargetingPageParam param);

    AllTargetDataResponse.AdTargetingHomeRpcVo getDorisAllTargetData(int puid, TargetingPageParam param);

    Page<TargetingPageVo> getDorisSpTargetVoList(ShopAuth shopAuth, Integer puid, TargetingPageParam param, boolean isExport);

    Page<TargetingPageVo> getDorisSbTargetVoList(ShopAuth shopAuth, Integer puid, TargetingPageParam param, boolean isExport);

    Page<TargetingPageVo> getDorisSdTargetVoList(ShopAuth shopAuth, Integer puid, TargetingPageParam param, boolean isExport);

    void saveDorisNe(List<AmazonAdNeTargeting> targetList);

    void saveDorisNe(Integer puid, Integer shopId, List<String> targetIdList);

    void fillAdStrategy(TargetingPageParam param, List<TargetingPageVo> rows);

    List<AmazonAdNeTargeting> listNeByTargetingValue(Integer puid, Integer shopId, List<NegativeArchiveRequest.NegativeInfo> infoList);
}
