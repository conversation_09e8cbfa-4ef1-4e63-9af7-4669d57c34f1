package com.meiyunji.sponsored.service.cpc.service;

import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.rpc.adCommon.AllQueryTargetAggregateDataResponse;
import com.meiyunji.sponsored.rpc.adCommon.AllQueryTargetDataResponse;
import com.meiyunji.sponsored.service.cpc.vo.*;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface ICpcQueryTargetingReportService{
    /**
     * 用户搜索词列表页
     * @param puid
     * @param dto
     * @param page
     * @return
     */
    Page pageList(Integer puid, CpcQueryWordDto dto, Page page);

    Page pageManageList(Integer puid, CpcQueryWordDto dto, Page page);

    Page dorisPageExportList(Integer puid, CpcQueryWordDto dto, Page page);

    /**
     * 获取汇总数据
     * @param puid
     * @param dto
     * @return
     */
    ReportVo sumReport(Integer puid, CpcQueryWordDto dto, boolean type, BigDecimal sumRange);

    /**
     * 详情列表
     * @param puid
     * @param dto
     * @param page
     * @return
     */
    Page detailList(Integer puid, CpcQueryWordDetailDto dto, Page page);

    /**
     * 获取汇总数据
     * @param puid
     * @param dto
     * @return
     */
    ReportVo sumDetailReport(Integer puid, CpcQueryWordDetailDto dto);

    /**
     * 获取详情页列表数据
     * @param puid
     * @param dto
     * @return
     */
    List<ReportVo> detailListChart(int puid, CpcQueryWordDetailDto dto);


    /**
     * 查询所有搜索词(投放产生)
     * @param puid
     * @param dto
     * @param page
     * @return
     */
    AllQueryTargetDataResponse.AdQueryTargetingHomeVo getAllQueryTargetData(Integer puid, CpcQueryWordDto dto, Page page);

    AllQueryTargetAggregateDataResponse.AdQueryTargetingHomeVo getAllQueryTargetAggregateData(Integer puid, CpcQueryWordDto dto);

    List<QueryReportVo> getReportVoListByGroupIds(Integer puid, List<String> spGroupIds, QueryReportSearchVo searchVo);

    List<TargetQueryReportVo> getListByTargetId(Integer puid, String targetId, TargetQuerySearchVo searchVo);

    /**
     * 获取搜索词 弹层数据
     */
    void getDetailsSumVo(QueryReportDetailsVo detailsVo,  CpcCommPageVo sumVo,  BigDecimal sumShopSale);

    /**
     * 获取搜索词弹层每天数据
     * @param adReportDetailsVo
     * @param detailsVo
     */
    void getQueryDetailsDay(AdReportDetailsVo adReportDetailsVo, QueryReportDetailsVo detailsVo);

    AllQueryTargetDataResponse.AdQueryTargetingHomeVo getDorisAllQueryTargetData(Integer puid, CpcQueryWordDto dto, Page page);

    AllQueryTargetAggregateDataResponse.AdQueryTargetingHomeVo getDorisAllQueryTargetAggregateData(Integer puid, CpcQueryWordDto dto);

    void fillAdStrategy(CpcQueryWordDto dto, List<ReportVo> rows);
}
