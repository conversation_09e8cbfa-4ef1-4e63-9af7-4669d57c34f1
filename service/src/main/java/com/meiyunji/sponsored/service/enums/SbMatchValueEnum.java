package com.meiyunji.sponsored.service.enums;



public enum SbMatchValueEnum {
    /**
     * keyword搜索词 匹配类型
     * */
    EXACT("EXACT","精确匹配"),
    BROAD("BROAD","广泛匹配"),
    PHRASE("PHRASE","词组匹配"),
    exact("exact","精确匹配"),
    broad("broad","广泛匹配"),
    phrase("phrase","词组匹配"),
    //sb 一种新的投放类型但是创建和同步都是另外一个接口
    theme("theme","主题"),
    THEME("THEME","主题");

    SbMatchValueEnum(String matchType, String matchValue){
        this.matchType=matchType;
        this.matchValue=matchValue;
    }

    private String matchType;
    private String matchValue;

    public static String getMatchValue(String matchType){
        SbMatchValueEnum[] values = values();
        for (SbMatchValueEnum value : values) {
            if (value.getMatchType().equals(matchType)){
                return value.getMatchValue();
            }
        }
        return "";
    }

    public static SbMatchValueEnum getMatchValueEnumByMatchType(String matchType){
        SbMatchValueEnum[] values = values();
        for (SbMatchValueEnum value : values) {
            if (value.getMatchType().equals(matchType)){
                return value;
            }
        }
        return null;
    }

    public String getMatchType() {
        return matchType;
    }

    public void setMatchType(String matchType) {
        this.matchType = matchType;
    }

    public String getMatchValue() {
        return matchValue;
    }

    public void setMatchValue(String matchValue) {
        this.matchValue = matchValue;
    }
}
