package com.meiyunji.sponsored.service.function;

import java.util.Objects;
import java.util.function.Function;

/**
 *  4参数有返回值的函数接口
 * @param <P>
 * @param <T>
 * @param <R>
 */

@FunctionalInterface
public interface FourFunction<P, T, U, S, R> {


    R apply(P p, T t, U u, S s);


    default <V> FourFunction<P, T, U, S, V> andThen(Function<? super R, ? extends V> after) {
        Objects.requireNonNull(after);
        return (P p, T t, U u, S s) -> after.apply(apply(p, t, u, s));
    }
}
