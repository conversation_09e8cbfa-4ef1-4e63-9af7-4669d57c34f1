package com.meiyunji.sponsored.service.common.service;

import com.meiyunji.sponsored.service.common.qo.QueryWordAndTargetOriginalBidReq;
import com.meiyunji.sponsored.service.common.vo.QueryWordAndTargetOriginalBidVo;

import java.util.List;

/**
 * @author: ys
 * @date: 2025/4/15 20:34
 * @describe:
 */
public interface IQueryWordAndTargetService {

    List<QueryWordAndTargetOriginalBidVo> getOriginalBidList(Integer puid, QueryWordAndTargetOriginalBidReq reqVo);
}
