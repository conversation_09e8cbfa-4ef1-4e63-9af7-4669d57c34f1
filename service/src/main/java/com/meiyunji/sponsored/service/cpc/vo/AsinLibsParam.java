package com.meiyunji.sponsored.service.cpc.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel
public class AsinLibsParam {
    @ApiModelProperty("商户ID")
    private Integer puid;
    @ApiModelProperty("pageNo")
    private Integer pageNo;
    @ApiModelProperty("pageSize")
    private Integer pageSize;
    @ApiModelProperty(value = "关键词")
    private String asin;
    @ApiModelProperty("查询内容")
    private String searchVal;
    @ApiModelProperty("关键词类型：否定，非否定")
    private String targetType;
    @ApiModelProperty("uid下的所有店铺id")
    private List<Integer> shopIds;
    @ApiModelProperty("查询广告组合id列表")
    private List<String> portfolioList;
    @ApiModelProperty("查询广告活动id列表")
    private List<String> campaignList;
    @ApiModelProperty("查询广告组id列表")
    private List<String> adGroupList;
}
