package com.meiyunji.sponsored.service.strategyTask.dao.impl;

import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.springjdbc.BaseShardingDaoImpl;
import com.meiyunji.sponsored.common.springjdbc.ConditionBuilder;
import com.meiyunji.sponsored.service.strategyTask.dao.AdvertiseStrategyTaskRecordDao;
import com.meiyunji.sponsored.service.strategyTask.po.AdvertiseStrategyTask;
import com.meiyunji.sponsored.service.strategyTask.po.AdvertiseStrategyTaskRecord;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * @author: chenzimeng
 * @email: <EMAIL>
 * @date: 2024-01-08  14:30
 */
@Repository
public class AdvertiseStrategyTaskRecordDaoImpl extends BaseShardingDaoImpl<AdvertiseStrategyTaskRecord> implements AdvertiseStrategyTaskRecordDao {

    @Override
    public Page<AdvertiseStrategyTaskRecord> pageListByTaskId(Integer puid, Long taskId, Integer pageNo, Integer pageSize) {
        StringBuilder sql = new StringBuilder("select * from t_advertise_strategy_task_record where puid = ? and task_id = ? and state = -1 and create_time > DATE_SUB(CURDATE(), INTERVAL 7 DAY) ");
        List<Object> args = new ArrayList<>();
        args.add(puid);
        args.add(taskId);
        StringBuilder whereSql = new StringBuilder();
        whereSql.append(" ORDER BY update_time desc");
        StringBuilder countSql = new StringBuilder("select count(t.id) from t_advertise_strategy_task_record t where t.puid = ? and t.task_id = ? and state = -1 and create_time > DATE_SUB(CURDATE(), INTERVAL 7 DAY)");
        sql.append(whereSql);
        countSql.append(whereSql);
        return this.getPageResult(puid, pageNo, pageSize, countSql.toString(),
                args.toArray(), sql.toString(), args.toArray(), AdvertiseStrategyTaskRecord.class);
    }

    @Override
    public List<AdvertiseStrategyTaskRecord> getListByTaskId(Integer puid, Integer shopId, Long id, Long taskId) {
        ConditionBuilder conditionBuilder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .equalTo("task_id", taskId)
                .equalTo("is_retry",0)
                .in("state",new Object[]{0,-1})
                .greaterThan("id", id)
                .lessThanOrEqualTo("retry_count",3)
                .orderBy("id")
                .limit(500)
                .build();
        return listByCondition(puid,conditionBuilder);
    }

    @Override
    public int updateByPrimaryKey(int puid, AdvertiseStrategyTaskRecord record) {
        return 0;
    }

    @Override
    public int batchInsert(int puid, List<AdvertiseStrategyTaskRecord> list,Boolean falg) {
        StringBuilder sql = new StringBuilder("insert into t_advertise_strategy_task_record (id, puid, shop_id," +
                "      marketplace_id, task_id, status_id, status_task_id, target_type,status,origin_budget_Value," +
                "      `origin_adPlaceTopValue`, origin_adPlaceProductValue, origin_strategy, " +
                "      origin_bidding_value, `origin_state`, ad_type, state_error," +
                "      `item_type`,retry_count, state, item_id, item_name, is_retry,create_time, update_time" +
                "      )" +
                "    values ");
        List<Object> argsList = Lists.newArrayList();
        for (AdvertiseStrategyTaskRecord record : list) {
            sql.append("(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, now(), now()),");
            argsList.add(record.getId());
            argsList.add(puid);
            argsList.add(record.getShopId());
            argsList.add(record.getMarketplaceId());
            argsList.add(record.getTaskId());
            argsList.add(record.getStatusId());
            argsList.add(record.getStatusTaskId());
            argsList.add(record.getTargetType());
            argsList.add(record.getStatus());
            argsList.add(record.getOriginBudgetValue());
            argsList.add(record.getOriginAdPlaceTopValue());
            argsList.add(record.getOriginAdPlaceProductValue());
            argsList.add(record.getOriginStrategy());
            argsList.add(record.getOriginBiddingValue());
            argsList.add(record.getOriginState());
            argsList.add(record.getAdType());
            argsList.add(record.getStateError());
            argsList.add(record.getItemType());
            argsList.add(record.getRetryCount());
            argsList.add(record.getState());
            argsList.add(record.getItemId());
            argsList.add(record.getItemName());
            argsList.add(record.getIsRetry());
        }
        sql.deleteCharAt(sql.length() - 1);
        sql.append(" on duplicate key update is_retry=values(is_retry) ,state_error=values(state_error), state=values(state),retry_count=values(retry_count)");
        return getJdbcTemplate(puid).update(sql.toString(), argsList.toArray());
    }

    @Override
    public void deleteByShop(Integer puid, Integer shopId) {
        String sql = "delete from t_advertise_strategy_task_record where puid=? and shop_id = ? and create_time < ?";
        List<Object> argsList = Lists.newArrayList();
        argsList.add(puid);
        argsList.add(shopId);
        argsList.add(LocalDate.now().minusDays(6));
        getJdbcTemplate(puid).update(sql,argsList.toArray());
    }

    @Override
    public int queryCount(Integer puid, Integer shopId, Long taskId, Integer state) {
        StringBuilder sql = new StringBuilder("select count(*) from t_advertise_strategy_task_record where puid = ? and shop_id = ? and task_id = ?");
        List<Object> args = Lists.newArrayList();
        args.add(puid);
        args.add(shopId);
        args.add(taskId);
        if (state != null) {
            sql.append(" and state = ? ");
            args.add(state);
        }
        return getJdbcTemplate(puid).queryForObject(sql.toString(), Integer.class, args.toArray());
    }

    @Override
    public List<AdvertiseStrategyTaskRecord> getListByTaskId(Integer puid, Integer shopId, Long taskId) {
        ConditionBuilder conditionBuilder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .equalTo("task_id", taskId)
                .build();
        return listByCondition(puid,conditionBuilder);
    }

    @Override
    public int deleteByPuidAndShopId(Integer puid, Integer shopId, Integer limit) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sql = new StringBuilder("delete from ").append(this.getJdbcHelper().getTable()).append(" where puid = ? ");
        argsList.add(puid);
        if (shopId != null) {
            sql.append("and shop_id = ?");
            argsList.add(shopId);
        }
        if (limit != null) {
            sql.append(" limit ? ");
            argsList.add(limit);
        }
        return getJdbcTemplate(puid).update(sql.toString(), argsList.toArray());
    }

}
