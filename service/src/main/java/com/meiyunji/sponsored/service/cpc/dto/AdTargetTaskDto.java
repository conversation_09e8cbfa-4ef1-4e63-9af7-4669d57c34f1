package com.meiyunji.sponsored.service.cpc.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @author: nongwenhua
 * @email: <EMAIL>
 * @create: 2024-05-07 14:11
 */
@Data
public class AdTargetTaskDto {
    private int puid;
    private int uid;
    private String loginIp;
    private int shopId;
    /**
     * 任务类型 {@link com.meiyunji.sponsored.service.cpc.constants.AdTargetTaskTypeEnum}
     */
    private int type;
    /**
     * targetId对应的投放类型 {@link com.meiyunji.sponsored.service.cpc.constants.TargetingTypeEnum}
     */
    private String targetingType;
    private List<AdTargetTaskDetailDto> taskDetails;
    private int targetPageType;
    private String sourceAdCampaignId;
    // 源shopId
    private int sourceShopId;

    @Data
    public static class AdTargetTaskDetailDto {
        private String adGroupId;
        private String adCampaignId;
        private BigDecimal bid;
        /**
         * 匹配类型 {@link com.meiyunji.sponsored.service.cpc.constants.AdTargetTaskMatchTypeEnum}
         */
        private String matchType;
        private String targetObject;
        private String targetObjectDesc;
        /**
         * 投放类型 {@link com.meiyunji.sponsored.service.cpc.constants.AdTargetObjectTypeEnum}
         */
        private int targetObjectType;
        private BigDecimal suggested;
        private BigDecimal rangeStart;
        private BigDecimal rangeEnd;
        private String imgUrl;
        // 投放页面的投放id
        private String targetId;
        // 投放id对应的shopId
        private int sourceShopId;
    }
}
