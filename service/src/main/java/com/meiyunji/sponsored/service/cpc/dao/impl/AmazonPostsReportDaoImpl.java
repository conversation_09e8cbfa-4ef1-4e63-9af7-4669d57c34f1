package com.meiyunji.sponsored.service.cpc.dao.impl;

import com.meiyunji.sponsored.common.springjdbc.BaseShardingDaoImpl;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.common.util.SqlStringUtil;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonPostsReportDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonPostReport;
import com.meiyunji.sponsored.service.post.request.GetPostsRequest;
import com.meiyunji.sponsored.service.post.request.PostPageFilterParam;
import com.meiyunji.sponsored.service.post.response.GetPostsResponse;
import com.meiyunji.sponsored.service.post.vo.PostsAggregateVo;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;

@Repository
public class AmazonPostsReportDaoImpl extends BaseShardingDaoImpl<AmazonPostReport> implements IAmazonPostsReportDao {

    @Override
    public List<GetPostsResponse.Posts> getReportByPostIds(PostPageFilterParam param, List<String> postProfileId, List<String> postIds) {
        StringBuilder selectSql = new StringBuilder("SELECT any_value(puid) puid, any_value(shop_id) shopId, post_profile_id postProfileId, post_id postId, ifNull(sum(impressions), 0) impressions,");
        List<Object> argsList = new ArrayList<>();
        selectSql.append(" ifNull(sum(clicks_to_follow), 0) clicksToFollow, ifNull(sum(clicks_to_detail_page), 0) clicksToDetailPage, ifNull(sum(engagement), 0) clicks, ifNull(sum(clicks_to_brand_store), 0) clicksToBrandStore, ifNull(sum(reach), 0) reach, ROUND(ifnull(sum(engagement) / sum(impressions), 0), 4) ctr ");
        selectSql.append(" from ").append(getJdbcHelper().getTable());
        selectSql.append(" where puid = ? ");
        argsList.add(param.getPuid());
        selectSql.append(SqlStringUtil.dealInList("shop_id", param.getShopIdList(), argsList));
        selectSql.append(SqlStringUtil.dealInList("post_profile_id", postProfileId, argsList));
        selectSql.append(SqlStringUtil.dealInList("post_id", postIds, argsList));
        selectSql.append(" and count_day >= ? and count_day <= ?");
        argsList.add(param.getStartDate());
        argsList.add(param.getEndDate());
        selectSql.append(" group by post_id, post_profile_id");
        return getJdbcTemplate(param.getPuid()).query(selectSql.toString(), argsList.toArray(), new BeanPropertyRowMapper<>(GetPostsResponse.Posts.class));
    }

    @Override
    public List<PostsAggregateVo> getChartData(Integer puid, GetPostsRequest req) {
        List<Object> argsList = new ArrayList<>();
        String startDate = DateUtil.getDateSqlFormat(req.getFilterDto().getStartDate());
        String endDate = DateUtil.getDateSqlFormat(req.getFilterDto().getEndDate());
        //按天聚合
        StringBuilder selectSql = new StringBuilder("SELECT count_day countDate, ifNull(sum(impressions), 0) impressions, ifNull(sum(clicks_to_follow), 0) clicksToFollow, ifNull(sum(clicks_to_detail_page), 0) clicksToDetailPage, ");
        selectSql.append("ifNull(sum(engagement), 0) clicks, ifNull(sum(clicks_to_brand_store), 0) clicksToBrandStore, ifNull(sum(reach), 0) reach, ROUND(ifnull(sum(engagement) / sum(impressions), 0) , 4) ctr ");
        selectSql.append(" from ").append(getJdbcHelper().getTable());
        selectSql.append(" where puid =? ");
        argsList.add(puid);
        selectSql.append(SqlStringUtil.dealInList("shop_id", req.getFilterDto().getShopIdList(), argsList));
        selectSql.append(" and count_day >= ? and count_day <= ?");
        argsList.add(startDate);
        argsList.add(endDate);
        if (CollectionUtils.isNotEmpty(req.getFilterDto().getPostProfileIds())) {
            selectSql.append(SqlStringUtil.dealInList("post_profile_id", req.getFilterDto().getPostProfileIds(), argsList));
        }
        if (CollectionUtils.isNotEmpty(req.getFilterDto().getPostIds())) {
            selectSql.append(SqlStringUtil.dealInList("post_id", req.getFilterDto().getPostIds(), argsList));
        }
        selectSql.append(" group by count_day");
        return getJdbcTemplate(puid).query(selectSql.toString(), argsList.toArray(), new BeanPropertyRowMapper<>(PostsAggregateVo.class));
    }

    @Override
    public PostsAggregateVo getAggregateReport(PostPageFilterParam filterDto) {
        List<Object> argsList = new ArrayList<>();
        String startDate = DateUtil.getDateSqlFormat(filterDto.getStartDate());
        String endDate = DateUtil.getDateSqlFormat(filterDto.getEndDate());
        StringBuilder selectSql = new StringBuilder("SELECT ifNull(sum(impressions), 0) impressions, ifNull(sum(clicks_to_follow), 0) clicksToFollow, ifNull(sum(clicks_to_detail_page), 0) clicksToDetailPage, ");
        selectSql.append("ifNull(sum(engagement), 0) clicks, ifNull(sum(clicks_to_brand_store), 0) clicksToBrandStore, ifNull(sum(reach), 0) reach, ROUND(ifnull(sum(engagement) / sum(impressions), 0), 4) ctr ");
        selectSql.append(" from ").append(getJdbcHelper().getTable());
        selectSql.append(" where puid =? ");
        argsList.add(filterDto.getPuid());
        selectSql.append(SqlStringUtil.dealInList("shop_id", filterDto.getShopIdList(), argsList));
        selectSql.append(" and count_day >= ? and count_day <= ?");
        argsList.add(startDate);
        argsList.add(endDate);
        if (CollectionUtils.isNotEmpty(filterDto.getPostProfileIds())) {
            selectSql.append(SqlStringUtil.dealInList("post_profile_id", filterDto.getPostProfileIds(), argsList));
        }
        if (CollectionUtils.isNotEmpty(filterDto.getPostIds())) {
            selectSql.append(SqlStringUtil.dealInList("post_id", filterDto.getPostIds(), argsList));
        }
        List<PostsAggregateVo> list = getJdbcTemplate(filterDto.getPuid()).query(selectSql.toString(), argsList.toArray(), new BeanPropertyRowMapper<>(PostsAggregateVo.class));
        return list != null && list.size() > 0 ? list.get(0) : null;
    }

    @Override
    public void upsertPostReports(List<AmazonPostReport> postReports) {
        if (CollectionUtils.isEmpty(postReports)) {
            return;
        }
        int puid = postReports.get(0).getPuid();
        StringBuilder sql = new StringBuilder("INSERT INTO ");
        sql.append(getJdbcHelper().getTable());
        sql.append(" (`puid`, `shop_id`, `post_profile_id`, `post_id`, `impressions`, `clicks_to_follow`, `clicks_to_detail_page`, `engagement`, `clicks_to_brand_store`, `reach`, `count_day`) values ");
        List<Object> argsList = new ArrayList<>();
        for (AmazonPostReport amazonPostReport : postReports) {
            sql.append(" (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?),");
            argsList.add(amazonPostReport.getPuid());
            argsList.add(amazonPostReport.getShopId());
            argsList.add(amazonPostReport.getPostProfileId());
            argsList.add(amazonPostReport.getPostId());
            argsList.add(amazonPostReport.getImpressions());
            argsList.add(amazonPostReport.getClicksToFollow());
            argsList.add(amazonPostReport.getClicksToDetailPage());
            argsList.add(amazonPostReport.getEngagement());
            argsList.add(amazonPostReport.getClicksToBrandStore());
            argsList.add(amazonPostReport.getReach());
            argsList.add(amazonPostReport.getCountDay());
        }
        sql.deleteCharAt(sql.length() - 1);
        sql.append(" ON DUPLICATE KEY UPDATE\n" +
                "`impressions` = VALUES(`impressions`),\n" +
                "    `clicks_to_follow` = VALUES(`clicks_to_follow`),\n" +
                "    `clicks_to_detail_page` = VALUES(`clicks_to_detail_page`),\n" +
                "    `engagement` = VALUES(`engagement`),\n" +
                "    `clicks_to_brand_store` = VALUES(`clicks_to_brand_store`),\n" +
                "    `reach` = VALUES(`reach`)," +
                "`update_time`=now()");
        getJdbcTemplate(puid).update(sql.toString(), argsList.toArray());
    }
}
