package com.meiyunji.sponsored.service.cpc.service2.sb.impl;


import com.alibaba.fastjson.JSONObject;
import com.amazon.advertising.sb.entity.group.SbGroupResult;
import com.amazon.advertising.sb.entity.group.sbV4AdGroupResult.error.SbV4AdGroupErrorResult;
import com.amazon.advertising.sb.entity.group.sbV4AdGroupResult.success.SbV4AdGroupInfo;
import com.amazon.advertising.sb.entity.group.sbV4AdGroupResult.success.SbV4AdGroupSuccessResult;
import com.google.api.client.util.Maps;
import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.exception.ServiceException;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.common.util.JSONUtil;
import com.meiyunji.sponsored.common.util.ResultUtil;
import com.meiyunji.sponsored.common.util.ThreadPoolUtil;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.cpc.dao.*;
import com.meiyunji.sponsored.service.cpc.po.*;
import com.meiyunji.sponsored.service.cpc.service2.impl.CpcShopDataService;
import com.meiyunji.sponsored.service.cpc.service2.sb.ICpcSbGroupService;
import com.meiyunji.sponsored.service.cpc.service2.sp.impl.CpcCommService;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.vo.*;
import com.meiyunji.sponsored.service.dataWarehouse.statsDo.ShopSaleDto;
import com.meiyunji.sponsored.service.doris.po.OdsAmazonAdGroupSb;
import com.meiyunji.sponsored.service.doris.service.IDorisService;
import com.meiyunji.sponsored.service.log.enums.OperationLogResultEnum;
import com.meiyunji.sponsored.service.log.po.AdManageOperationLog;
import com.meiyunji.sponsored.service.log.service.IAdManageOperationLogService;
import com.meiyunji.sponsored.service.enums.SBCreateErrorEnum;
import com.meiyunji.sponsored.service.util.LocalDateTimeUtil;
import com.meiyunji.sponsored.service.util.ZoneUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> on 2021/7/6
 */
@Service
@Slf4j
public class CpcSbGroupServiceImpl implements ICpcSbGroupService {

    @Autowired
    private IAmazonSbAdGroupDao amazonSbAdGroupDao;
    @Autowired
    private IAmazonAdSbGroupReportDao amazonAdSbGroupReportDao;
    @Autowired
    private CpcCommService cpcCommService;
    @Autowired
    private IScVcShopAuthDao shopAuthDao;
    @Autowired
    private IAmazonAdProfileDao amazonAdProfileDao;
    @Autowired
    private CpcSbGroupApiService cpcSbGroupApiService;
    @Autowired
    private CpcSbAdsApiService cpcSbAdsApiService;
    @Autowired
    private IAmazonAdCampaignAllDao amazonAdCampaignAllDao;
    @Autowired
    private CpcShopDataService cpcShopDataService;
    @Autowired
    private IDorisService dorisService;
    @Autowired
    private IAdManageOperationLogService operationAdGroupLogService;


    @Override
    public Result showGroupPerformance(int puid, AdPerformanceParam param) {
        if (param.getShopId() == null
                || StringUtils.isBlank(param.getGroupId())) {
            return ResultUtil.returnErr("请求参数错误");
        }

        AmazonSbAdGroup amazonAdGroup = amazonSbAdGroupDao.getByGroupId(puid, param.getShopId(), param.getGroupId());
        if (amazonAdGroup == null) {
            return ResultUtil.returnErr("没有广告组信息");
        }

        // 拼装返回的数据VO
        AdPerformanceVo adPerformanceVo = new AdPerformanceVo();
        adPerformanceVo.setShopId(amazonAdGroup.getShopId());
        adPerformanceVo.setCampaignId(amazonAdGroup.getCampaignId());
        adPerformanceVo.setGroupId(amazonAdGroup.getAdGroupId());

        // 初始化每天数据，应前端要求保证日期是连续的
        Map<String, CpcCommPageVo> map = new LinkedHashMap<>();
        adPerformanceVo.setMap(map);
        LocalDate startLocalDate = LocalDate.parse(param.getStartDate(), DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD));
        LocalDate endLocalDate = LocalDate.parse(param.getEndDate(), DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD));
        while (startLocalDate.equals(endLocalDate) || startLocalDate.isBefore(endLocalDate)) {
            map.put(startLocalDate.format(DateTimeFormatter.ofPattern(DateUtil.PATTERN)), new CpcCommPageVo());
            startLocalDate = startLocalDate.plusDays(1);
        }

        List<AmazonAdSbGroupReport> reports = null;

        reports = amazonAdSbGroupReportDao.listReports(puid, param.getShopId(), param.getStartDate(),
                param.getEndDate(), param.getGroupId());

        if (CollectionUtils.isNotEmpty(reports)) {
            // 取店铺销售额
            String start = LocalDate.parse(param.getStartDate(), DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD)).format(DateTimeFormatter.ofPattern(DateUtil.PATTERN));
            String end = LocalDate.parse(param.getEndDate(), DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD)).format(DateTimeFormatter.ofPattern(DateUtil.PATTERN));

            ShopSaleDto shopSaleDto = cpcShopDataService.getShopSaleData(param.getShopId(), start, end);

            Map<String, CpcCommPageVo> resultMap = reports.stream().collect(Collectors.toMap(
                    e -> LocalDate.parse(e.getCountDate(), DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD)).format(DateTimeFormatter.ofPattern(DateUtil.PATTERN)),
                    e -> {
                        CpcCommPageVo campaignPageVo = new CpcCommPageVo();

                        // 填充报告数据
                        cpcCommService.fillReportDataIntoPageVo(campaignPageVo, e.getReportBase(), shopSaleDto);
                        return campaignPageVo;

                    }, (p1, p2) -> p1));

            adPerformanceVo.getMap().putAll(resultMap);
        }

        return ResultUtil.returnSucc(adPerformanceVo);
    }


    // po -> 列表页vo
    private void convertPoToPageVo(AmazonSdAdGroup amazonAdGroup, GroupPageVo vo) {
        vo.setShopId(amazonAdGroup.getShopId());
        vo.setAdGroupId(amazonAdGroup.getAdGroupId());
        vo.setName(amazonAdGroup.getName());
        vo.setState(amazonAdGroup.getState());
        vo.setDefaultBid(String.valueOf(amazonAdGroup.getDefaultBid()));
        vo.setTargetingType(amazonAdGroup.getTactic());
    }

    // 创建活动时vo->po
    private AmazonSdAdGroup convertVoToCreatePo(SPadGroupVo vo, AmazonAdCampaignAll amazonSdAdCampaign) {
        AmazonSdAdGroup amazonAdGroup = new AmazonSdAdGroup();
        amazonAdGroup.setPuid(vo.getPuid());
        amazonAdGroup.setShopId(amazonSdAdCampaign.getShopId());
        amazonAdGroup.setMarketplaceId(amazonSdAdCampaign.getMarketplaceId());
        amazonAdGroup.setCampaignId(amazonSdAdCampaign.getCampaignId());
        amazonAdGroup.setProfileId(amazonSdAdCampaign.getProfileId());
        amazonAdGroup.setName(vo.getName().trim());
        amazonAdGroup.setDefaultBid(BigDecimal.valueOf(vo.getDefaultBid()));
        amazonAdGroup.setState(Constants.ENABLED);
        amazonAdGroup.setTactic(amazonSdAdCampaign.getTactic());
        amazonAdGroup.setCreateId(vo.getUid());
        amazonAdGroup.setCreateInAmzup(1);
        amazonAdGroup.setBidOptimization(vo.getBidOptimization());
        return amazonAdGroup;
    }

    @Override
    public Result<String> createGroup(SbAdGroupVo vo) {
        int puid = vo.getPuid();
        int shopId = vo.getShopId();

        // 判断广告组所属广告活动是否允许添加多个广告组(isMultiAdGroupsEnabled)
        AmazonAdCampaignAll sbCampaign = amazonAdCampaignAllDao.getCampaignByCampaignId(puid, shopId, vo.getCampaignId(), Constants.SB);
        if (!sbCampaign.getIsMultiAdGroupsEnabled()) {
            return ResultUtil.returnErr("该广告活动为旧版sb活动，只能添加一个广告组，请重新选择广告活动");
        }

        //判断广告组名称是否存在
        if (amazonSbAdGroupDao.exist(puid, shopId, vo.getCampaignId(), vo.getName())) {
            return ResultUtil.returnErr("广告组名称已存在");
        }

        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(shopId, puid);
        if (shop == null) {
            return ResultUtil.returnErr("没有CPC授权");
        }

        AmazonAdProfile profile = amazonAdProfileDao.getProfile(puid, shopId);
        if (profile == null) {
            return ResultUtil.returnErr("没有站点对应的配置信息");
        }
        Result result = cpcSbGroupApiService.v4Create(shop, profile, vo);

        if (!result.success()) {
            return ResultUtil.returnErr(result.getMsg());
        }

        SbGroupResult groupResult = (SbGroupResult) result.getData();
        SbV4AdGroupSuccessResult successResult = groupResult.getAdGroups().getSuccessResultList().get(0);
        SbV4AdGroupInfo groupInfo = successResult.getAdGroup();
        String adGroupId = groupInfo.getAdGroupId();
        if (StringUtils.isBlank(adGroupId)) {
            return null;
        }
        SbAdVo sbAdVo = vo.getSbAdVo();
        sbAdVo.setAdGroupId(adGroupId);
        sbAdVo.setUid(vo.getUid());
        sbAdVo.setCampaignId(vo.getCampaignId());
        sbAdVo.setIp(vo.getIp());
        // 入库
        boolean saveSuccess = false;
        try {
            batchSaveOrUpdate(vo, Collections.singletonList(successResult), profile, true);
            saveSuccess = true;
        } catch (Exception e) {
            log.error("createSbAdGroup save fail:", e);
        }

        logSbAdGroupCreate(vo, saveSuccess, profile, groupInfo);

        // 创建广告
        Result sbAdresult = cpcSbAdsApiService.createSbAd(shop, profile, sbAdVo);

        if (!sbAdresult.success()) {
            Map<String, String> dataMap = Maps.newHashMap();
            dataMap.put("adGroupId", adGroupId);
            dataMap.put("failMsg", sbAdresult.getMsg());

            Result sbAdsResult = ResultUtil.error(Result.ERROR, JSONObject.toJSONString(dataMap));
            return sbAdsResult;
        }

        return ResultUtil.returnSucc(adGroupId);
    }

    private void logSbAdGroupCreate(SbAdGroupVo vo, boolean saveSuccess, AmazonAdProfile profile, SbV4AdGroupInfo groupInfo) {
        try {
            AmazonSbAdGroup adGroup = new AmazonSbAdGroup();
            adGroup.setPuid(vo.getPuid());
            adGroup.setShopId(profile.getShopId());
            adGroup.setMarketplaceId(profile.getMarketplaceId());
            adGroup.setAdGroupId(groupInfo.getAdGroupId());
            adGroup.setCampaignId(groupInfo.getCampaignId());
            adGroup.setName(groupInfo.getName());
            adGroup.setCreateId(vo.getUid());

            AdManageOperationLog sdGroupOperationLog = operationAdGroupLogService.getSbAdGroupLog(null, adGroup);
            sdGroupOperationLog.setIp(vo.getIp());
            if (saveSuccess) {
                sdGroupOperationLog.setResult(OperationLogResultEnum.SUCCESS.getResultValue());
                sdGroupOperationLog.setAdGroupId(groupInfo.getAdGroupId());
            } else {
                sdGroupOperationLog.setResult(OperationLogResultEnum.FAIL.getResultValue());
                sdGroupOperationLog.setResultInfo("新增广告组异常");
            }
            operationAdGroupLogService.printAdOperationLog(Lists.newArrayList(sdGroupOperationLog));
        } catch (Exception e) {
            log.error("SB广告组创建 记录操作日志异常", e);
        }
    }

    @Override
    public NewCreateResultResultVo createGroup(SbAdGroupVo vo, ShopAuth shop, AmazonAdProfile profile) {
        int puid = vo.getPuid();
        int shopId = vo.getShopId();

        //TODO 这个逻辑是否有问题？即使不允许多广告组，那么此为首次创建广告活动，至少应该允许创建一个广告组?
        // 判断广告组所属广告活动是否允许添加多个广告组(isMultiAdGroupsEnabled)
        AmazonAdCampaignAll sbCampaign = amazonAdCampaignAllDao.getCampaignByCampaignId(puid, shopId, vo.getCampaignId(), Constants.SB);
        if (!sbCampaign.getIsMultiAdGroupsEnabled()) {
            throw new ServiceException(JSONObject.toJSONString(Collections.singletonList(SBCommonErrorVo.getErrorVo(SBCreateErrorEnum.CREATE_LIMIT_OLD_VERSION.getMsg()))));
        }

        //判断广告组名称是否存在
        if (amazonSbAdGroupDao.exist(puid, shopId, vo.getCampaignId(), vo.getName())) {
            throw new ServiceException(JSONObject.toJSONString(Collections.singletonList(SBCommonErrorVo.getErrorVo(SBCreateErrorEnum.AD_GROUP_NAME_EXISTS.getMsg()))));
        }

        Result result = cpcSbGroupApiService.v4CreateNew(shop, profile, vo);

        if (!result.success()) {
            throw new ServiceException(result.getMsg());
        }

        SbGroupResult groupResult = (SbGroupResult) result.getData();
        SbV4AdGroupSuccessResult successResult = groupResult.getAdGroups().getSuccessResultList().get(0);
        SbV4AdGroupInfo groupInfo = successResult.getAdGroup();
        String adGroupId = groupInfo.getAdGroupId();
        if (StringUtils.isBlank(adGroupId)) {
            return null;
        }

        String errMsg;
        // 入库
        try {
            errMsg = batchSaveOrUpdate(vo, Arrays.asList(successResult), profile, true);
        } catch (Exception e) {
            log.error("createSbAdGroup save fail:", e);
            throw new ServiceException(JSONObject.toJSONString(Collections.singletonList(SBCommonErrorVo.getErrorVo(SBCreateErrorEnum.AD_GROUP_SAVE_FAIL.getMsg()))));
        }

        if (StringUtils.isNotBlank(errMsg)) {
            throw new ServiceException(JSONObject.toJSONString(Collections.singletonList(SBCommonErrorVo.getErrorVo(errMsg))));
        }

        return NewCreateResultResultVo.builder()
                .campaignId(vo.getCampaignId())
                .adGroupId(adGroupId)
                .build();
    }

    @Override
    public Result<String> updateGroup(List<SbAdGroupVo> voList) {
        SbAdGroupVo vo = voList.get(0);
        Integer puid = vo.getPuid();
        Integer shopId = vo.getShopId();
        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(shopId, puid);
        if (shop == null) {
            return ResultUtil.returnErr("没有CPC授权");
        }

        AmazonAdProfile profile = amazonAdProfileDao.getProfile(puid, shopId);
        if (profile == null) {
            return ResultUtil.returnErr("没有站点对应的配置信息");
        }
        //判断广告组名称是否存在
        if (amazonSbAdGroupDao.exist(puid, shopId, vo.getCampaignId(), vo.getName())) {
            return ResultUtil.returnErr("该广告组名称已存在");
        }

        List<SbAdGroupVo> errorList = Lists.newArrayList();
        List<AmazonSbAdGroup> updateList = Lists.newArrayList();
        BatchResponseVo<SbAdGroupVo, AmazonSbAdGroup> data = new BatchResponseVo<>();
        data.setCountNum(voList.size());

        List<String> adGroupIds = voList.stream().filter(Objects::nonNull)
                .map(SbAdGroupVo::getAdGroupId)
                .collect(Collectors.toList());
        Map<String, AmazonSbAdGroup> sbAdGroupMap = amazonSbAdGroupDao.getAdGroupByIds(puid, shopId, adGroupIds)
                .stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(AmazonSbAdGroup::getAdGroupId, item -> item, (a, b) -> a));

        AmazonSbAdGroup sbAdGroup;
        for (SbAdGroupVo groupVo : voList) {
            sbAdGroup = sbAdGroupMap.get(groupVo.getAdGroupId());
            if (sbAdGroup == null) {
                groupVo.setFailReason("广告组不存在");
                errorList.add(groupVo);
                continue;
            }
            AmazonSbAdGroup updateGroup = new AmazonSbAdGroup();
            BeanUtils.copyProperties(sbAdGroup, updateGroup);
            updateList.add(updateGroup);
        }

        // 部分成功部分失败的情况，成功要执行，失败个数及原因要抛出
        if (CollectionUtils.isNotEmpty(errorList)) {
            data.setErrorList(errorList);
            data.setFailNum(errorList.size());
        }

        if (CollectionUtils.isEmpty(updateList)) {
            data.setSuccessNum(0);
            return ResultUtil.success(JSONUtil.objectToJson(data));
        }

        Result result = cpcSbGroupApiService.v4Update(shop, profile, voList);

        if (!result.success()) {
            return ResultUtil.returnErr(result.getMsg());
        }

        SbGroupResult groupResult = (SbGroupResult) result.getData();
        List<SbV4AdGroupSuccessResult> successResult = groupResult.getAdGroups().getSuccessResultList();
        List<SbV4AdGroupErrorResult> errorResult = groupResult.getAdGroups().getErrorResultList();

        // 入库
        try {
            batchSaveOrUpdate(vo , successResult, profile, true);
        } catch (Exception e) {
            log.error("updateSbAdGroup update fail:", e);
        }
        if (CollectionUtils.isNotEmpty(successResult)) {
            List<String> successIds = successResult.stream().filter(Objects::nonNull)
                    .map(SbV4AdGroupSuccessResult::getAdGroupId)
                    .collect(Collectors.toList());
            List<AmazonSbAdGroup> successList = updateList.stream().filter(item -> successIds.contains(item.getAdGroupId())).collect(Collectors.toList());
            data.setSuccessNum(successResult.size());
            data.setSuccessList(successList);
        }

        if (CollectionUtils.isNotEmpty(errorResult)) {
            List<SbAdGroupVo> errors = errorResult.stream().filter(Objects::nonNull).map(item -> {
                String failReason = JSONUtil.objectToJson(item.getErrorsList());
                SbAdGroupVo sbAdGroupVo = voList.get(item.getIndex());
                sbAdGroupVo.setFailReason(failReason);
                return sbAdGroupVo;
            }).collect(Collectors.toList());
            data.addErrorList(errors);
            data.addFailNum(errorResult.size());
        }
        logSbAdGroupUpdate(sbAdGroupMap, voList, data.getSuccessList(), data.getErrorList());
        return ResultUtil.returnSucc(JSONUtil.objectToJson(data));
    }

    private void logSbAdGroupUpdate(Map<String, AmazonSbAdGroup> adGroupMap, List<SbAdGroupVo> voList,
                                    List<AmazonSbAdGroup> successList, List<SbAdGroupVo> errors) {
        try {
            List<AdManageOperationLog> adOperationLogs = Lists.newArrayList();
            Map<String, SbAdGroupVo> voMap = voList.stream().collect(Collectors.toMap(SbAdGroupVo::getAdGroupId, item -> item, (a, b) -> a));
            if (CollectionUtils.isNotEmpty(successList)) {
                for (AmazonSbAdGroup adGroup : successList) {
                    String adGroupId = adGroup.getAdGroupId();
                    AmazonSbAdGroup oldAdGroup = adGroupMap.get(adGroupId);
                    SbAdGroupVo groupVo = voMap.get(adGroupId);
                    AmazonSbAdGroup newAdGroup = new AmazonSbAdGroup();
                    BeanUtils.copyProperties(oldAdGroup, newAdGroup);
                    if (StringUtils.isNotBlank(groupVo.getName())) {
                        newAdGroup.setName(groupVo.getName());
                    }
                    if (StringUtils.isNotBlank(groupVo.getState())) {
                        newAdGroup.setState(groupVo.getState());
                    }
                    newAdGroup.setUpdateId(groupVo.getUid());

                    AdManageOperationLog sdGroupOperationLog = operationAdGroupLogService.getSbAdGroupLog(oldAdGroup, newAdGroup);
                    sdGroupOperationLog.setIp(groupVo.getIp());
                    sdGroupOperationLog.setResult(OperationLogResultEnum.SUCCESS.getResultValue());
                    sdGroupOperationLog.setAdGroupId(adGroupId);
                    adOperationLogs.add(sdGroupOperationLog);
                }
            }
            if (CollectionUtils.isNotEmpty(errors)) {
                for (SbAdGroupVo errorVo : errors) {
                    String adGroupId = errorVo.getAdGroupId();
                    AmazonSbAdGroup oldAdGroup = adGroupMap.get(adGroupId);
                    if (Objects.isNull(oldAdGroup)) {
                        continue;
                    }
                    AmazonSbAdGroup newAdGroup = new AmazonSbAdGroup();
                    BeanUtils.copyProperties(oldAdGroup, newAdGroup);
                    if (StringUtils.isNotBlank(errorVo.getName())) {
                        newAdGroup.setName(errorVo.getName());
                    }
                    if (StringUtils.isNotBlank(errorVo.getState())) {
                        newAdGroup.setState(errorVo.getState());
                    }
                    newAdGroup.setUpdateId(errorVo.getUid());

                    AdManageOperationLog sdGroupOperationLog = operationAdGroupLogService.getSbAdGroupLog(oldAdGroup, newAdGroup);
                    sdGroupOperationLog.setIp(errorVo.getIp());
                    sdGroupOperationLog.setResult(OperationLogResultEnum.FAIL.getResultValue());
                    sdGroupOperationLog.setResultInfo(errorVo.getFailReason());
                    sdGroupOperationLog.setAdGroupId(adGroupId);
                    adOperationLogs.add(sdGroupOperationLog);
                }
            }
            operationAdGroupLogService.batchLogsMergeByAdGroup(adOperationLogs);
        } catch (Exception e) {
            log.error("SB广告组更新 记录操作日志异常", e);
        }
    }

    @Override
    public Result<String> deleteGroup(SbAdGroupVo vo, List<String> adGroupIdList) {
        Integer puid = vo.getPuid();
        Integer shopId = vo.getShopId();
        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(shopId, puid);
        if (shop == null) {
            return ResultUtil.returnErr("没有CPC授权");
        }

        AmazonAdProfile profile = amazonAdProfileDao.getProfile(puid, shopId);
        if (profile == null) {
            return ResultUtil.returnErr("没有站点对应的配置信息");
        }

        List<SbAdGroupVo> errorList = Lists.newArrayList();
        List<AmazonSbAdGroup> updateList = Lists.newArrayList();
        BatchResponseVo<SbAdGroupVo, AmazonSbAdGroup> data = new BatchResponseVo<>();
        data.setCountNum(adGroupIdList.size());

        Map<String, AmazonSbAdGroup> sbAdGroupMap = amazonSbAdGroupDao.getAdGroupByIds(puid, shopId, adGroupIdList)
                .stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(AmazonSbAdGroup::getAdGroupId, item -> item, (a, b) -> a));
        AmazonSbAdGroup sbAdGroup;
        SbAdGroupVo groupVo;
        for (String adGroupId : adGroupIdList) {
            sbAdGroup = sbAdGroupMap.get(adGroupId);
            if (sbAdGroup == null) {
                groupVo = new SbAdGroupVo();
                groupVo.setFailReason("广告组不存在");
                errorList.add(groupVo);
                continue;
            }
            AmazonSbAdGroup updateGroup = new AmazonSbAdGroup();
            BeanUtils.copyProperties(sbAdGroup, updateGroup);
            updateList.add(updateGroup);
        }

        // 部分成功部分失败的情况，成功要执行，失败个数及原因要抛出
        if (CollectionUtils.isNotEmpty(errorList)) {
            data.setErrorList(errorList);
            data.setFailNum(errorList.size());
        }

        if (CollectionUtils.isEmpty(updateList)) {
            data.setSuccessNum(0);
            return ResultUtil.success(JSONUtil.objectToJson(data));
        }

        Result result = cpcSbGroupApiService.v4Delete(shop, profile, adGroupIdList);

        if (!result.success()) {
            return ResultUtil.returnErr(result.getMsg());
        }

        SbGroupResult groupResult = (SbGroupResult) result.getData();
        List<SbV4AdGroupSuccessResult> successResult = groupResult.getAdGroups().getSuccessResultList();
        List<SbV4AdGroupErrorResult> errorResult = groupResult.getAdGroups().getErrorResultList();
        // 入库
        try {
            batchSaveOrUpdate(vo, successResult, profile, true);
        } catch (Exception e) {
            log.error("deleteSbAdGroup delete fail:", e);
        }
        if (CollectionUtils.isNotEmpty(successResult)) {
            List<String> successIds = successResult.stream().filter(Objects::nonNull)
                    .map(SbV4AdGroupSuccessResult::getAdGroupId)
                    .collect(Collectors.toList());
            List<AmazonSbAdGroup> successList = updateList.stream().filter(item -> successIds.contains(item.getAdGroupId())).collect(Collectors.toList());
            data.setSuccessNum(successResult.size());
            data.setSuccessList(successList);
        }

        if (CollectionUtils.isNotEmpty(errorResult)) {
            List<SbAdGroupVo> errors = errorResult.stream().filter(Objects::nonNull).map(item -> {
                String failReason = JSONUtil.objectToJson(item.getErrorsList());
                SbAdGroupVo sbAdGroupVo = new SbAdGroupVo();
                sbAdGroupVo.setAdGroupId(adGroupIdList.get(item.getIndex()));
                sbAdGroupVo.setFailReason(failReason);
                return sbAdGroupVo;
            }).collect(Collectors.toList());
            data.addErrorList(errors);
            data.addFailNum(errorResult.size());
        }
        logSbAdGroupArchive(sbAdGroupMap, data.getSuccessList(), data.getErrorList(), vo.getIp(), vo.getUid());
        return ResultUtil.returnSucc(JSONUtil.objectToJson(data));
    }

    private void logSbAdGroupArchive(Map<String, AmazonSbAdGroup> adGroupMap, List<AmazonSbAdGroup> successList,
                                     List<SbAdGroupVo> errors, String ip, Integer uid) {
        try {
            List<AdManageOperationLog> adOperationLogs = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(successList)) {
                for (AmazonSbAdGroup adGroup : successList) {
                    String adGroupId = adGroup.getAdGroupId();
                    AmazonSbAdGroup oldAdGroup = adGroupMap.get(adGroupId);
                    AmazonSbAdGroup newAdGroup = new AmazonSbAdGroup();
                    BeanUtils.copyProperties(oldAdGroup, newAdGroup);
                    newAdGroup.setState("archived");
                    newAdGroup.setUpdateId(uid);
                    AdManageOperationLog sdGroupOperationLog = operationAdGroupLogService.getSbAdGroupLog(oldAdGroup, newAdGroup);
                    sdGroupOperationLog.setIp(ip);
                    sdGroupOperationLog.setResult(OperationLogResultEnum.SUCCESS.getResultValue());
                    sdGroupOperationLog.setAdGroupId(adGroupId);
                    adOperationLogs.add(sdGroupOperationLog);
                }
            }
            if (CollectionUtils.isNotEmpty(errors)) {
                for (SbAdGroupVo errorVo : errors) {
                    String adGroupId = errorVo.getAdGroupId();
                    AmazonSbAdGroup oldAdGroup = adGroupMap.get(adGroupId);
                    if (Objects.isNull(oldAdGroup)) {
                        continue;
                    }
                    AmazonSbAdGroup newAdGroup = new AmazonSbAdGroup();
                    BeanUtils.copyProperties(oldAdGroup, newAdGroup);
                    newAdGroup.setState("archived");
                    newAdGroup.setUpdateId(errorVo.getUid());
                    AdManageOperationLog sdGroupOperationLog = operationAdGroupLogService.getSbAdGroupLog(oldAdGroup, newAdGroup);
                    sdGroupOperationLog.setIp(errorVo.getIp());
                    sdGroupOperationLog.setResult(OperationLogResultEnum.FAIL.getResultValue());
                    sdGroupOperationLog.setResultInfo(errorVo.getFailReason());
                    sdGroupOperationLog.setAdGroupId(adGroupId);
                    adOperationLogs.add(sdGroupOperationLog);
                }
            }
            operationAdGroupLogService.batchLogsMergeByAdGroup(adOperationLogs);
        } catch (Exception e) {
            log.error("SB广告组归档 记录操作日志异常", e);
        }
    }

    private String batchSaveOrUpdate(SbAdGroupVo vo, List<SbV4AdGroupSuccessResult> successResultList, AmazonAdProfile profile, boolean saveDoris) {
        String errMsg = "创建广告组失败";
        if (CollectionUtils.isEmpty(successResultList) || profile == null) {
            return errMsg;
        }

        int puid = vo.getPuid();
        int shopId = profile.getShopId();
        String marketplaceId = profile.getMarketplaceId();
        String profileId = profile.getProfileId();
        ZoneId zoneId = ZoneUtil.getZoneIdByAmzSite(marketplaceId);

        Map<String, AmazonSbAdGroup> adGroupMap = amazonSbAdGroupDao.getAdGroupByIds(puid, shopId,
                        successResultList.stream().filter(e -> StringUtils.isNotBlank(e.getAdGroupId())).map(SbV4AdGroupSuccessResult::getAdGroupId).collect(Collectors.toList()))
                .stream().filter(Objects::nonNull).collect(Collectors.toMap(AmazonSbAdGroup::getAdGroupId, item -> item, (i, j) -> i));

        AmazonSbAdGroup sbAdGroup;
        List<AmazonSbAdGroup> updateList = Lists.newArrayList();
        List<AmazonSbAdGroup> addList = Lists.newArrayList();
        for (SbV4AdGroupSuccessResult successResult : successResultList) {
            SbV4AdGroupInfo adGroupInfo = successResult.getAdGroup();
            String adGroupId = adGroupInfo.getAdGroupId();
            if (adGroupMap.get(adGroupId) != null) {
                sbAdGroup = adGroupMap.get(adGroupId);
                if (StringUtils.isNotBlank(adGroupInfo.getName())) {
                    sbAdGroup.setName(adGroupInfo.getName());
                }
                sbAdGroup.setState(adGroupInfo.getState().toLowerCase());
                sbAdGroup.setUpdateId(vo.getUid());
                sbAdGroup.setServingStatus(adGroupInfo.getExtendedData().getServingStatus());
                if (adGroupInfo.getExtendedData() != null) {
                    sbAdGroup.setServingStatus(adGroupInfo.getExtendedData().getServingStatus());
                    if (StringUtils.isNotBlank(adGroupInfo.getExtendedData().getCreationDate())) {
                        LocalDateTime localDateTime = LocalDateTimeUtil.timestampToDateTime(Long.valueOf(adGroupInfo.getExtendedData().getCreationDate()));
                        localDateTime = LocalDateTimeUtil.getZoneTime(localDateTime, ZoneId.systemDefault(), zoneId);
                        sbAdGroup.setCreationDate(localDateTime);
                    }
                    if (StringUtils.isNotBlank(adGroupInfo.getExtendedData().getLastUpdatedDate())) {
                        LocalDateTime localDateTime = LocalDateTimeUtil.timestampToDateTime(Long.valueOf(adGroupInfo.getExtendedData().getLastUpdatedDate()));
                        localDateTime = LocalDateTimeUtil.getZoneTime(localDateTime, ZoneId.systemDefault(), zoneId);
                        sbAdGroup.setCreationDate(localDateTime);
                    }
                }
                updateList.add(sbAdGroup);
            } else {
                sbAdGroup = new AmazonSbAdGroup();
                sbAdGroup.setPuid(puid);
                sbAdGroup.setShopId(shopId);
                sbAdGroup.setMarketplaceId(marketplaceId);
                sbAdGroup.setProfileId(profileId);
                sbAdGroup.setCampaignId(adGroupInfo.getCampaignId());
                sbAdGroup.setAdGroupId(adGroupInfo.getAdGroupId());
                sbAdGroup.setName(adGroupInfo.getName());
                sbAdGroup.setState(adGroupInfo.getState().toLowerCase());
                sbAdGroup.setCreateId(vo.getUid());
                if (adGroupInfo.getExtendedData() != null) {
                    sbAdGroup.setServingStatus(adGroupInfo.getExtendedData().getServingStatus());
                    if (StringUtils.isNotBlank(adGroupInfo.getExtendedData().getCreationDate())) {
                        LocalDateTime localDateTime = LocalDateTimeUtil.timestampToDateTime(Long.valueOf(adGroupInfo.getExtendedData().getCreationDate()));
                        localDateTime = LocalDateTimeUtil.getZoneTime(localDateTime, ZoneId.systemDefault(), zoneId);
                        sbAdGroup.setCreationDate(localDateTime);
                    }
                    if (StringUtils.isNotBlank(adGroupInfo.getExtendedData().getLastUpdatedDate())) {
                        LocalDateTime localDateTime = LocalDateTimeUtil.timestampToDateTime(Long.valueOf(adGroupInfo.getExtendedData().getLastUpdatedDate()));
                        localDateTime = LocalDateTimeUtil.getZoneTime(localDateTime, ZoneId.systemDefault(), zoneId);
                        sbAdGroup.setCreationDate(localDateTime);
                    }
                }
                addList.add(sbAdGroup);
            }
        }

        try {
            amazonSbAdGroupDao.batchAdd(puid, addList);
            amazonSbAdGroupDao.batchUpdate(puid, updateList);
            //写入doris
            if (saveDoris) {
                saveDoris(addList, updateList);
            }
            return "";
        } catch (Exception e) {
            log.error("createSbAdGroup fail:", e);
        }

        return errMsg;
    }

    /**
     * 写入doris
     * @param addList
     * @param updateList
     */
    @Override
    public void saveDoris(List<AmazonSbAdGroup> addList, List<AmazonSbAdGroup> updateList) {
        try {
            List<OdsAmazonAdGroupSb> collect = new ArrayList<>();
            Date date = new Date();
            if (CollectionUtils.isNotEmpty(addList)) {
                addList.forEach(x -> {
                    OdsAmazonAdGroupSb odsAmazonAdGroupSb = new OdsAmazonAdGroupSb();
                    BeanUtils.copyProperties(x, odsAmazonAdGroupSb);
                    odsAmazonAdGroupSb.setCreateTime(date);
                    odsAmazonAdGroupSb.setUpdateTime(date);
                    if (StringUtils.isNotBlank(odsAmazonAdGroupSb.getState())) {
                        odsAmazonAdGroupSb.setState(odsAmazonAdGroupSb.getState().toLowerCase());
                    }
                    collect.add(odsAmazonAdGroupSb);
                });
            }
            if (CollectionUtils.isNotEmpty(updateList)) {
                updateList.forEach(x -> {
                    OdsAmazonAdGroupSb odsAmazonAdGroupSb = new OdsAmazonAdGroupSb();
                    BeanUtils.copyProperties(x, odsAmazonAdGroupSb);
                    odsAmazonAdGroupSb.setUpdateTime(date);
                    if (StringUtils.isNotBlank(odsAmazonAdGroupSb.getState())) {
                        odsAmazonAdGroupSb.setState(odsAmazonAdGroupSb.getState().toLowerCase());
                    }
                    collect.add(odsAmazonAdGroupSb);
                });
            }

            dorisService.saveDoris(collect);
        } catch (Exception e) {
            log.error("sb group save doris error", e);
        }
    }

    /**
     * 写入doris
     * @param groupList
     */
    @Override
    public void saveDoris(List<AmazonSbAdGroup> groupList) {
        try {
            List<OdsAmazonAdGroupSb> collect = groupList.stream().map(x -> {
                OdsAmazonAdGroupSb odsAmazonAdGroupSb = new OdsAmazonAdGroupSb();
                BeanUtils.copyProperties(x, odsAmazonAdGroupSb);
                if (StringUtils.isNotBlank(odsAmazonAdGroupSb.getState())) {
                    odsAmazonAdGroupSb.setState(odsAmazonAdGroupSb.getState().toLowerCase());
                }
                return odsAmazonAdGroupSb;
            }).collect(Collectors.toList());
            dorisService.saveDoris(collect);
        } catch (Exception e) {
            log.error("sb group save doris error", e);
        }
    }

    /**
     * 写入doris
     * @param puid
     * @param shopId
     * @param adGroupIdList
     */
    @Override
    public void saveDoris(Integer puid, Integer shopId, List<String> adGroupIdList) {
        try {
            if (CollectionUtils.isEmpty(adGroupIdList)) {
                return;
            }
            List<AmazonSbAdGroup> list = amazonSbAdGroupDao.getAdGroupByIds(puid, shopId, adGroupIdList);
            saveDoris(list);
        } catch (Exception e) {
            log.error("sb group save doris error", e);
        }
    }
}