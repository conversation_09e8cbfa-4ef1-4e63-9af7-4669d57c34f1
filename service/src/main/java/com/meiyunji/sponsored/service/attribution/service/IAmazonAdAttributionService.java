package com.meiyunji.sponsored.service.attribution.service;

import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.service.attribution.entity.AmazonAdAttribution;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdProfile;

import java.util.List;

/**
 * @author: wade
 * @date: 2022/3/12 11:50
 * @describe:
 */
public interface IAmazonAdAttributionService {

    List<AmazonAdProfile> getAttributionAvailableShopsByPuid(Integer puid, List<Integer> shopIds);

    AmazonAdProfile getAttributionAdvertiserInfo(Integer puid, Integer shopId);

    Integer batchInsert(Integer puid, List<AmazonAdAttribution> amazonAdAttributions, AmazonAdProfile profile);

    Page<AmazonAdAttribution> page(
            Integer puid, List<Integer> shopIds, Integer pageNo, Integer pageSize, AmazonAdAttribution.SearchTypeEnum searchType,
            String searchValue, String publishId);

    List<AmazonAdAttribution> getSkusByShopId(Integer shopId);

    List<AmazonAdAttribution> listByAsin(Integer puid, Integer shopId, String advertiserId, String publisherId, List<String> asins);

    List<AmazonAdAttribution> getTopUrl(Integer puid, Integer shopId, String advertiserId, String publisherId, String topUrl);

    List<AmazonAdAttribution> list(Integer puid, List<Integer> shopIds, AmazonAdAttribution.SearchTypeEnum searchType, String searchValue, String publisherId);

    AmazonAdAttribution getByCreativeId(int puid, String creativeId);

    List<String> listAllCreativeIds(int puid, List<Integer> shopId, AmazonAdAttribution.SearchTypeEnum searchType, String searchValue, String publisherId);
}
