package com.meiyunji.sponsored.service.export.handler;

import com.google.common.collect.Lists;
import com.google.protobuf.Int32Value;
import com.meiyunji.amazon.mws.base.AmznEndpoint;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.base.ProcessMsg;
import com.meiyunji.sponsored.common.enums.SearchTypeEnum;
import com.meiyunji.sponsored.common.support.StringRedisService;
import com.meiyunji.sponsored.common.util.JSONUtil;
import com.meiyunji.sponsored.common.util.StringUtil;
import com.meiyunji.sponsored.rpc.export.AdGroupDataResponse;
import com.meiyunji.sponsored.rpc.export.KeywordDataResponse;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.cpc.constants.strategy.AdGroupStrategyTypeEnum;
import com.meiyunji.sponsored.service.cpc.dao.IAdManagePageExportTaskDao;
import com.meiyunji.sponsored.service.cpc.dao.IAdMarkupTagDao;
import com.meiyunji.sponsored.service.cpc.dao.IAdTagDao;
import com.meiyunji.sponsored.service.cpc.po.AdManagePageExportTask;
import com.meiyunji.sponsored.service.cpc.po.AdTag;
import com.meiyunji.sponsored.service.cpc.service2.ICpcCommonService;
import com.meiyunji.sponsored.service.cpc.service2.impl.CpcShopDataService;
import com.meiyunji.sponsored.service.cpc.service2.sp.ICpcAdGroupService;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.vo.*;
import com.meiyunji.sponsored.service.enums.*;
import com.meiyunji.sponsored.service.excel.excelTools.WriteHandlerBuild;
import com.meiyunji.sponsored.service.excel.excelTools.service.IExcelService;
import com.meiyunji.sponsored.service.export.AdManagePageExportTaskHandler;
import com.meiyunji.sponsored.service.vo.AdvertisingGroupVo;
import com.meiyunji.sponsored.service.vo.KeywordSbTargetingVo;
import com.meiyunji.sponsored.service.vo.KeywordTargetingVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.meiyunji.sponsored.service.export.constants.AdManagePageExportTaskConstant;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.meiyunji.sponsored.service.export.util.ExportStringUtil.*;

@Service(AdManagePageExportTaskConstant.GROUP)
@Slf4j
public class GroupPageExportTaskHandler implements AdManagePageExportTaskHandler {
    @Autowired
    private IAdManagePageExportTaskDao adManagePageExportTaskDao;
    @Autowired
    private CpcShopDataService cpcShopDataService;
    @Autowired
    private StringRedisService stringRedisService;
    @Autowired
    private IScVcShopAuthDao shopAuthDao;
    @Autowired
    private IExcelService excelService;
    @Autowired
    private ICpcAdGroupService cpcAdGroupService;
    @Autowired
    private IAdMarkupTagDao adMarkupTagDao;
    @Autowired
    private IAdTagDao adTagDao;

    private static String currency;

    @Override
    public void export(AdManagePageExportTask task) {
        GroupPageParam param = JSONUtil.jsonToObject(task.getParam(), GroupPageParam.class);
        if (param == null) {
            //查询参数为空
            log.error(String.format("Group export error, param is null, task id : %d", task.getId()));
            adManagePageExportTaskDao.updateErrorStatus(task.getId());
            return;
        }
        //搜索值
        //对批量查询做处理
        if (StringUtils.isNotBlank(param.getSearchValue()) && param.getSearchValue().contains("%±%")) {
            param.setSearchValueList(StringUtil.splitStr(param.getSearchValue().trim(), StringUtil.SPECIAL_COMMA));
            param.setSearchValue(null);
        }
        if (param.getSearchValue() != null && !SearchTypeEnum.EXACT.getValue().equalsIgnoreCase(param.getSearchType())) {
            String searchValue = param.getSearchValue();
            if (searchValue.contains("\\")) {
                searchValue = searchValue.replace("\\", "\\\\");
            }
            if (searchValue.contains("%")) {
                searchValue = searchValue.replace("%", "\\%");
            }
            // 将替换后的字符串重新设置回 param 对象
            param.setSearchValue(searchValue);
        }
        ShopAuth shop = shopAuthDao.getScAndVcById(param.getShopId());
        // 取店铺销售额
        BigDecimal shopSalesByDate = cpcShopDataService.getShopSalesByDate(param.getShopId(), param.getStartDate(), param.getEndDate());
        if (shopSalesByDate == null) {
            shopSalesByDate = BigDecimal.ZERO;
        }
        param.setShopSales(shopSalesByDate);
        // 获取页面数据
        List<GroupPageVo> groupPageVoList = new ArrayList<>();
        if (Constants.SP.equalsIgnoreCase(param.getType())) {
            Page<GroupPageVo> voPage = new Page<>(1, Constants.EXPORT_MAX_SIZE);
            voPage = cpcAdGroupService.getSpDorisPageList(task.getPuid(), param, voPage, true);
            if (CollectionUtils.isNotEmpty(voPage.getRows())) {
                groupPageVoList = voPage.getRows();
            }
        } else if (Constants.SD.equalsIgnoreCase(param.getType())) {
            groupPageVoList = cpcAdGroupService.getSdGroupVoList(task.getPuid(), param, null, true);
        } else {
            groupPageVoList = cpcAdGroupService.getSbGroupVoList(task.getPuid(), param, null, true);
        }
        //填充标签
        this.fillAdTagData(task.getPuid(), param.getShopId(), param, groupPageVoList);
        //填充广告策略信息
        cpcAdGroupService.fillAdStrategy(param,groupPageVoList);
        List<AdGroupDataResponse.GroupPageVo> list = groupPageVoList.stream().filter(Objects::nonNull).map(GroupPageExportTaskHandler::buildGrpcVo).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(list)) {
            //修改任务状态
            adManagePageExportTaskDao.updateSuccessStatus(task.getId(), null, 100);
            //修改状态，前端收到后转圈效果停止
            stringRedisService.set(param.getUuid(), new ProcessMsg(-1, 0, "导出数据为空"));
            return;
        }
        currency = AmznEndpoint.getByMarketplaceId(shop.getMarketplaceId()).getCurrencyCode().value();
        String fileName = shop.getName() + "_广告组" + "_" + param.getStartDate() + "_" + param.getEndDate();
        int count = 0;
        List<String> urlList = new ArrayList<>();
        //页面渲染
        WriteHandlerBuild build = new WriteHandlerBuild().rate().currencyNew(AdvertisingGroupVo.class);
        //集合分片
        List<List<AdGroupDataResponse.GroupPageVo>> partition = Lists.partition(list, Constants.EXPORT_MAX_SIZE);
        for (List<AdGroupDataResponse.GroupPageVo> list1 : partition) {
            List<AdvertisingGroupVo> AdvertisingGroupVoList = new LinkedList<>();
            for (AdGroupDataResponse.GroupPageVo keyVo : list1) {
                AdvertisingGroupVoList.add(buildExportVo(keyVo));
            }
            if (!AdvertisingGroupVoList.isEmpty()) {
                //设置表头
                Class clazz = AdvertisingGroupVo.class;
                if (CollectionUtils.isNotEmpty(AdvertisingGroupVoList)) {
                    List<String> excludeFileds = Lists.newArrayList();
                    if (Constants.SP.equalsIgnoreCase(param.getType())) {
                        excludeFileds = Lists.newArrayList("viewImpressions", "vcpm", "ordersNewToBrandFTD", "orderRateNewToBrandFTD", "salesNewToBrandFTD", "salesRateNewToBrandFTD", "unitsOrderedNewToBrandFTD", "unitsOrderedRateNewToBrandFTD",
                                "newToBrandDetailPageViews", "addToCart", "addToCartRate", "ecpAddToCart", "video5SecondViews", "video5SecondViewRate", "videoFirstQuartileViews", "videoMidpointViews", "videoThirdQuartileViews", "videoCompleteViews", "videoUnmutes",
                                "viewabilityRate", "viewClickThroughRate", "brandedSearches", "detailPageViews", "cumulativeReach", "impressionsFrequencyAverage");
                    }
                    if (Constants.SD.equalsIgnoreCase(param.getType())) {
                        excludeFileds = Lists.newArrayList("adSelfSaleNum", "adOtherSaleNum", "video5SecondViews", "video5SecondViewRate");
                    }
                    if (Constants.SB.equalsIgnoreCase(param.getType())) {
                        excludeFileds = Lists.newArrayList("defaultBid", "targetingNum", "adProductNum", "newToBrandDetailPageViews", "addToCart", "addToCartRate", "ecpAddToCart", "detailPageViews", "cumulativeReach", "impressionsFrequencyAverage",
                                "vcpm", "ordersNewToBrandFTD", "orderRateNewToBrandFTD", "salesNewToBrandFTD", "salesRateNewToBrandFTD", "unitsOrderedNewToBrandFTD", "unitsOrderedRateNewToBrandFTD");
                    }
                    urlList.add(excelService.easyExcelHandlerExport(param.getPuid(), AdvertisingGroupVoList, fileName + "(" + count++ + ")", clazz, build, excludeFileds));
                }
            }
        }
        //修改任务状态
        adManagePageExportTaskDao.updateSuccessStatus(task.getId(), JSONUtil.objectToJson(urlList), 100);
        stringRedisService.set(param.getUuid(), new ProcessMsg(1, urlList.size(), "导出成功", urlList));
    }

    private static AdvertisingGroupVo buildExportVo(AdGroupDataResponse.GroupPageVo gpVo)
        {
            AdvertisingGroupVo agVo = new AdvertisingGroupVo();
            agVo.setServingStatusName(gpVo.getServingStatusName());
            agVo.setName(gpVo.getName());
            agVo.setTargetingNum(gpVo.getTargetingNum().getValue());
            agVo.setAdProductNum(gpVo.getAdProductNum().getValue());
            agVo.setImpressions(gpVo.getImpressions().getValue());
            agVo.setClicks(gpVo.getClicks().getValue());
            agVo.setAdOrderNum(gpVo.getAdOrderNum().getValue());
            //默认竞价
            agVo.setDefaultBid(currency + gpVo.getDefaultBid());
            //点击率
            agVo.setCtr(modifyFormat(gpVo.getCtr()));
            //订单转化率
            agVo.setCvr(modifyFormat(gpVo.getCvr()));
            //ACoS
            agVo.setAcos(modifyFormat(gpVo.getAcos()));
            //ACoTS
            agVo.setAcots(modifyFormat(gpVo.getAcots()));
            agVo.setRoas(gpVo.getRoas());
            //ASoTS
            agVo.setAsots(modifyFormat(gpVo.getAsots()));
            //广告花费
            agVo.setAdCost(currency + formatToNumber(gpVo.getAdCost()));
            //平均点击费用(特殊处理)
            agVo.setAdCostPerClick(currency + getAdCostPerClick(gpVo.getAdCostPerClick()));
            //广告销售额
            agVo.setAdSale(currency + getAdCostPerClick(gpVo.getAdSale()));
            //创建者
            //获取广告名称
            agVo.setCampaignName(gpVo.getCampaignName());
            //状态
            agVo.setState(StateEnum.getStateValue(gpVo.getState()));
            //活动类型
            agVo.setCampaignType(CampaignTypeEnum.getCampaignValue(gpVo.getType()));
            //投放类型
            agVo.setTargetingType(TargetingEnum.getTargetingValue(gpVo.getCampaignTargetingType()));
            //创建者
            agVo.setCreator(gpVo.getCreator());
            //标签
            agVo.setAdTag(gpVo.getAdTag());
            //广告策略标签
            agVo.setAdStrategyTag(gpVo.getAdStrategyTag());

            //广告组合
            agVo.setPortfolioName(gpVo.getPortfolioName());

            agVo.setViewImpressions(gpVo.getViewImpressions().getValue());
            agVo.setCpa(currency + formatToNumber(gpVo.getCpa()));
            agVo.setVcpm("-".equals(gpVo.getVcpm()) ? "-" : currency + formatToNumber(gpVo.getVcpm()));
            agVo.setAdSaleNum(gpVo.getAdSaleNum().getValue());
            agVo.setAdOtherOrderNum(gpVo.getAdOtherOrderNum().getValue());
            agVo.setAdSales(currency + formatToNumber(gpVo.getAdSales()));
            agVo.setAdOtherSales(currency + formatToNumber(gpVo.getAdOtherSales()));
            agVo.setOrderNum(gpVo.getOrderNum().getValue());
            agVo.setAdSelfSaleNum(gpVo.getAdSelfSaleNum().getValue());
            agVo.setAdOtherSaleNum(gpVo.getAdOtherSaleNum().getValue());
            agVo.setOrdersNewToBrandFTD(gpVo.getOrdersNewToBrandFTD().getValue());
            agVo.setOrderRateNewToBrandFTD(modifyFormat(gpVo.getOrderRateNewToBrandFTD()));
            agVo.setSalesNewToBrandFTD(currency + formatToNumber(gpVo.getSalesNewToBrandFTD()));
            agVo.setSalesRateNewToBrandFTD(modifyFormat(gpVo.getSalesRateNewToBrandFTD()));
            agVo.setUnitsOrderedNewToBrandFTD(gpVo.getUnitsOrderedNewToBrandFTD().getValue());
            agVo.setUnitsOrderedRateNewToBrandFTD(modifyFormat(gpVo.getUnitsOrderedRateNewToBrandFTD()));
            // 花费占比
            agVo.setAdCostPercentage(modifyFormat(gpVo.getAdCostPercentage()));
            // 销售额占比
            agVo.setAdSalePercentage(modifyFormat(gpVo.getAdSalePercentage()));
            // 订单量占比
            agVo.setAdOrderNumPercentage(modifyFormat(gpVo.getAdOrderNumPercentage()));
            // 销量占比
            agVo.setOrderNumPercentage(modifyFormat(gpVo.getOrderNumPercentage()));

            agVo.setNewToBrandDetailPageViews(gpVo.getNewToBrandDetailPageViews());
            agVo.setAddToCart(gpVo.getAddToCart());
            agVo.setAddToCartRate(modifyFormat(gpVo.getAddToCartRate()));
            agVo.setEcpAddToCart(currency + formatToNumber(gpVo.getECPAddToCart()));
            agVo.setVideo5SecondViews(gpVo.getVideo5SecondViews());
            agVo.setVideo5SecondViewRate(modifyFormat(gpVo.getVideo5SecondViewRate()));
            agVo.setVideoFirstQuartileViews(gpVo.getVideoFirstQuartileViews());
            agVo.setVideoMidpointViews(gpVo.getVideoMidpointViews());
            agVo.setVideoThirdQuartileViews(gpVo.getVideoThirdQuartileViews());
            agVo.setVideoCompleteViews(gpVo.getVideoCompleteViews());
            agVo.setVideoUnmutes(gpVo.getVideoUnmutes());
            agVo.setViewabilityRate(modifyFormat(gpVo.getViewabilityRate()));
            agVo.setViewClickThroughRate(modifyFormat(gpVo.getViewClickThroughRate()));
            agVo.setBrandedSearches(gpVo.getBrandedSearches());
            agVo.setDetailPageViews(gpVo.getDetailPageViews());
            agVo.setCumulativeReach(gpVo.getCumulativeReach());
            agVo.setImpressionsFrequencyAverage(gpVo.getImpressionsFrequencyAverage());
            agVo.setAdvertisingUnitPrice(currency + formatToNumber(gpVo.getAdvertisingUnitPrice()));
            return agVo;
        }



    private static AdGroupDataResponse.GroupPageVo buildGrpcVo(GroupPageVo item){
        AdGroupDataResponse.GroupPageVo.Builder vo = AdGroupDataResponse.GroupPageVo.newBuilder();
        if (StringUtils.isNotBlank(item.getName())) {
            vo.setName(item.getName());
        }
        if (StringUtils.isNotBlank(item.getCampaignTargetingType())) {
            vo.setCampaignTargetingType(item.getCampaignTargetingType());
        }
        if (StringUtils.isNotBlank(item.getState())) {
            vo.setState(item.getState());
        }
        if (StringUtils.isNotBlank(item.getType())) {
            vo.setType(item.getType());
        }
        if (StringUtils.isNotBlank(item.getTargetingType())) {
            vo.setTargetingType(item.getTargetingType());
        }
        if (StringUtils.isNotBlank(item.getCampaignName())) {
            vo.setCampaignName(item.getCampaignName());
        }
        if (StringUtils.isNotBlank(item.getDefaultBid())) {
            vo.setDefaultBid(item.getDefaultBid());
        }
        if (item.getTargetingNum() != null) {
            vo.setTargetingNum(Int32Value.of(item.getTargetingNum()));
        }
        if (item.getAdProductNum() != null) {
            vo.setAdProductNum(Int32Value.of(item.getAdProductNum()));
        }
        if (StringUtils.isNotBlank(item.getCreator())) {
            vo.setCreator(item.getCreator());
        }
        if (StringUtils.isNotBlank(item.getPortfolioName())) {
            vo.setPortfolioName(item.getPortfolioName());
        }
        if (StringUtils.isNotBlank(item.getServingStatusName())) {
            vo.setServingStatusName(item.getServingStatusName());
        }
        vo.setImpressions(Int32Value.of(Optional.ofNullable(item.getImpressions()).orElse(0)));
        vo.setClicks(Int32Value.of(Optional.ofNullable(item.getClicks()).orElse(0)));
        vo.setAdOrderNum(Int32Value.of(Optional.ofNullable(item.getAdOrderNum()).orElse(0)));
        vo.setAdCostPerClick(StringUtils.isNotBlank(item.getAdCostPerClick()) ? item.getAdCostPerClick() : "0");
        vo.setCtr(StringUtils.isNotBlank(item.getCtr()) ? item.getCtr() : "0");
        vo.setCvr(StringUtils.isNotBlank(item.getCvr()) ? item.getCvr() : "0");
        vo.setAcos(StringUtils.isNotBlank(item.getAcos()) ? item.getAcos() : "0");
        vo.setAdCost(StringUtils.isNotBlank(item.getAdCost()) ? item.getAdCost() : "0");
        vo.setAcots(StringUtils.isNotBlank(item.getAcots()) ? item.getAcots() : "0");
        vo.setRoas(StringUtils.isNotBlank(item.getRoas()) ? item.getRoas() : "0");
        vo.setAsots(StringUtils.isNotBlank(item.getAsots()) ? item.getAsots() : "0");
        vo.setAdSale(StringUtils.isNotBlank(item.getAdSale()) ? item.getAdSale() : "0");
        vo.setViewImpressions(Int32Value.of(Optional.ofNullable(item.getViewImpressions()).orElse(0)));
        vo.setCpa(StringUtils.isNotBlank(item.getCpa()) ? item.getCpa() : "0");
        if(SBCampaignCostTypeEnum.VCPM.getCode().equals(item.getCostType())){
            vo.setVcpm(StringUtils.isNotBlank(item.getVcpm()) ? item.getVcpm() : "0");
        }else{
            vo.setVcpm("-");
        }
        vo.setAdSaleNum(Int32Value.of(Optional.ofNullable(item.getAdSaleNum()).orElse(0)));
        vo.setAdOtherOrderNum(Int32Value.of(Optional.ofNullable(item.getAdOtherOrderNum()).orElse(0)));
        vo.setAdSales(StringUtils.isNotBlank(item.getAdSales()) ? item.getAdSales() : "0");
        vo.setAdOtherSales(StringUtils.isNotBlank(item.getAdOtherSales()) ? item.getAdOtherSales() : "0");
        vo.setOrderNum(Int32Value.of(Optional.ofNullable(item.getOrderNum()).orElse(0)));
        vo.setAdSelfSaleNum(Int32Value.of(Optional.ofNullable(item.getAdSelfSaleNum()).orElse(0)));
        vo.setAdOtherSaleNum(Int32Value.of(Optional.ofNullable(item.getAdOtherSaleNum()).orElse(0)));
        vo.setOrdersNewToBrandFTD(Int32Value.of(Optional.ofNullable(item.getOrdersNewToBrandFTD()).orElse(0)));
        vo.setOrderRateNewToBrandFTD(StringUtils.isNotBlank(item.getOrderRateNewToBrandFTD()) ? item.getOrderRateNewToBrandFTD() : "0");
        vo.setSalesNewToBrandFTD(StringUtils.isNotBlank(item.getSalesNewToBrandFTD()) ? item.getSalesNewToBrandFTD() : "0");
        vo.setSalesRateNewToBrandFTD(StringUtils.isNotBlank(item.getSalesRateNewToBrandFTD()) ? item.getSalesRateNewToBrandFTD() : "0");
        vo.setUnitsOrderedNewToBrandFTD(Int32Value.of(Optional.ofNullable(item.getUnitsOrderedNewToBrandFTD()).orElse(0)));
        vo.setUnitsOrderedRateNewToBrandFTD(StringUtils.isNotBlank(item.getUnitsOrderedRateNewToBrandFTD()) ? item.getUnitsOrderedRateNewToBrandFTD() : "0");
        // 花费占比
        vo.setAdCostPercentage(StringUtils.isNotBlank(item.getAdCostPercentage()) ? item.getAdCostPercentage() : "0");
        // 销售额占比
        vo.setAdSalePercentage(StringUtils.isNotBlank(item.getAdSalePercentage()) ? item.getAdSalePercentage() : "0");
        // 订单量占比
        vo.setAdOrderNumPercentage(StringUtils.isNotBlank(item.getAdOrderNumPercentage()) ? item.getAdOrderNumPercentage() : "0");
        // 销量占比
        vo.setOrderNumPercentage(StringUtils.isNotBlank(item.getOrderNumPercentage()) ? item.getOrderNumPercentage() : "0");

        vo.setNewToBrandDetailPageViews(Optional.ofNullable(item.getNewToBrandDetailPageViews()).map(String::valueOf).orElse("0"));
        vo.setAddToCart(Optional.ofNullable(item.getAddToCart()).map(String::valueOf).orElse("0"));
        vo.setAddToCartRate(Optional.ofNullable(item.getAddToCartRate()).map(String::valueOf).orElse("0"));
        vo.setECPAddToCart(Optional.ofNullable(item.getECPAddToCart()).map(String::valueOf).orElse("0"));
        vo.setVideo5SecondViews(Optional.ofNullable(item.getVideo5SecondViews()).map(String::valueOf).orElse("0"));
        vo.setVideo5SecondViewRate(Optional.ofNullable(item.getVideo5SecondViewRate()).map(String::valueOf).orElse("0"));
        vo.setVideoFirstQuartileViews(Optional.ofNullable(item.getVideoFirstQuartileViews()).map(String::valueOf).orElse("0"));
        vo.setVideoMidpointViews(Optional.ofNullable(item.getVideoMidpointViews()).map(String::valueOf).orElse("0"));
        vo.setVideoThirdQuartileViews(Optional.ofNullable(item.getVideoThirdQuartileViews()).map(String::valueOf).orElse("0"));
        vo.setVideoCompleteViews(Optional.ofNullable(item.getVideoCompleteViews()).map(String::valueOf).orElse("0"));
        vo.setVideoUnmutes(Optional.ofNullable(item.getVideoUnmutes()).map(String::valueOf).orElse("0"));
        vo.setViewabilityRate(Optional.ofNullable(item.getViewabilityRate()).map(String::valueOf).orElse("0"));
        vo.setViewClickThroughRate(Optional.ofNullable(item.getViewClickThroughRate()).map(String::valueOf).orElse("0"));
        vo.setBrandedSearches(Optional.ofNullable(item.getBrandedSearches()).map(String::valueOf).orElse("0"));
        vo.setDetailPageViews(Optional.ofNullable(item.getDetailPageViews()).map(String::valueOf).orElse("0"));
        vo.setCumulativeReach(Optional.ofNullable(item.getCumulativeReach()).map(String::valueOf).orElse("0"));
        vo.setImpressionsFrequencyAverage(Optional.ofNullable(item.getImpressionsFrequencyAverage()).map(String::valueOf).orElse("0"));
        vo.setAdvertisingUnitPrice(Optional.ofNullable(item.getAdvertisingUnitPrice()).map(String::valueOf).orElse("0"));
        //处理标签，取出标签名称进行导出
        String adTagName = "";
        if (CollectionUtils.isNotEmpty(item.getAdTags())) {
            List<String> adTag = item.getAdTags().stream().map(AdTag::getName).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(adTag) && adTag.size() > 1) {
                adTagName = String.join(",", adTag);
                vo.setAdTag(adTagName);
            } else {
                adTagName = String.join(",", adTag);
                vo.setAdTag(adTagName);
            }
        }
        vo.setAdTag(adTagName);
        // 处理广告策略标签，取出标签名称进行导出
        if (CollectionUtils.isNotEmpty(item.getStrategyList())) {
            List<String> adStrategyTag = item.getStrategyList().stream().map(it-> AdGroupStrategyTypeEnum.getEnumByCode(it.getAdStrategyType()).getDesc()).collect(Collectors.toList());
            vo.setAdStrategyTag(String.join(",", adStrategyTag));
        }else{
            vo.setAdStrategyTag("");
        }
        return vo.build();
    }

    private void fillAdTagData(Integer puid, Integer shopId,GroupPageParam param,List<GroupPageVo> rows){
        if(CollectionUtils.isEmpty(rows)){
            return ;
        }
        List<String> groups = rows.stream().map(GroupPageVo::getAdGroupId).distinct().collect(Collectors.toList());
        List<AdMarkupTagVo> relationVos = adMarkupTagDao.getRelationVos(puid, shopId, AdTagTypeEnum.GROUP.getType(), param.getType(), null, null, groups);
        if(CollectionUtils.isEmpty(relationVos)){
            return;
        }
        List<Long> collect = relationVos.stream().map(AdMarkupTagVo::getTagIds).flatMap(Collection::stream).distinct().collect(Collectors.toList());
        if(CollectionUtils.isEmpty(collect)){
            return;
        }
        List<AdTag> byLongIdList = adTagDao.getListByLongIdList(puid, collect);
        if(CollectionUtils.isEmpty(byLongIdList)){
            return;
        }
        Map<Long, AdTag> adTagMap = byLongIdList.stream().collect(Collectors.toMap(AdTag::getId, e -> e, (e1, e2) -> e2));
        Map<String, AdMarkupTagVo> adMarkupTagVoMap = relationVos.stream().collect(Collectors.toMap(AdMarkupTagVo::getRelationId, e -> e, (e1, e2) -> e2));
        for (GroupPageVo vo : rows) {
            AdMarkupTagVo adMarkupTagVo = adMarkupTagVoMap.get(vo.getAdGroupId());
            if(adMarkupTagVo == null){
                continue;
            }
            List<Long> tagIds = adMarkupTagVo.getTagIds();
            if(tagIds == null){
                continue;
            }
            List<AdTag> collect1 = tagIds.stream().map(e -> adTagMap.get(e)).collect(Collectors.toList());
            vo.setAdTags(collect1);
        }
    }
}
