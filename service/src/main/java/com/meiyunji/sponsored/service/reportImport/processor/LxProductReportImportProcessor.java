package com.meiyunji.sponsored.service.reportImport.processor;

import com.amazonaws.util.json.Jackson;
import com.meiyunji.sellfox.aadas.types.enumeration.Marketplace;
import com.meiyunji.sponsored.common.exception.BizServiceException;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.cpc.dao.*;
import com.meiyunji.sponsored.service.cpc.po.*;
import com.meiyunji.sponsored.service.enums.CampaignTypeEnum;
import com.meiyunji.sponsored.service.reportImport.constanst.LxReportConstant;
import com.meiyunji.sponsored.service.reportImport.enums.ReportImportErrType;
import com.meiyunji.sponsored.service.reportImport.message.AdReportImportMessage;
import com.meiyunji.sponsored.service.reportImport.model.LxAdProductReport;
import com.meiyunji.sponsored.service.reportImport.vo.DuplicationCampaignVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * lx产品报告导入处理器
 *
 * <AUTHOR>
 * @date 2023/05/26
 */
@Service
@Slf4j
public class LxProductReportImportProcessor extends AbstractLxReportImportProcessor<LxAdProductReport> {

    private final String productFormat = "%s#%s#%s#%s";

    private final IAmazonSdAdProductDao amazonSdAdProductDao;
    private final IAmazonAdProductDao amazonAdProductDao;
    private final IAmazonAdProductReportDao amazonAdProductReportDao;
    private final IAmazonAdSdProductReportDao amazonAdSdProductReportDao;

    protected LxProductReportImportProcessor(
            IAmazonAdCampaignAllDao amazonAdCampaignAllDao,
            IAmazonAdGroupDao amazonAdGroupDao,
            IAmazonSdAdGroupDao amazonSdAdGroupDao,
            IAmazonSbAdGroupDao amazonSbAdGroupDao, IAmazonSdAdProductDao amazonSdAdProductDao,
            IAmazonAdProductDao amazonAdProductDao, IAmazonAdProductReportDao amazonAdProductReportDao,
            IAmazonAdSdProductReportDao amazonAdSdProductReportDao) {
        super(amazonAdCampaignAllDao, amazonAdGroupDao, amazonSdAdGroupDao, amazonSbAdGroupDao);
        this.amazonSdAdProductDao = amazonSdAdProductDao;
        this.amazonAdProductDao = amazonAdProductDao;
        this.amazonAdProductReportDao = amazonAdProductReportDao;
        this.amazonAdSdProductReportDao = amazonAdSdProductReportDao;
    }

    /**
     * 导入报告
     *
     * @param importMessage           导入消息
     * @param reports                 报告
     * @param shopAuthMap    店铺数据
     */
    @Override
    public void importReport(AdReportImportMessage importMessage, List<LxAdProductReport> reports, Map<String, ShopAuth> shopAuthMap) {
        Integer puid = importMessage.getPuid();
        Long taskId = importMessage.getScheduleId();


        List<Integer> shopIds = shopAuthMap.values().stream().map(ShopAuth::getId).collect(Collectors.toList());
        //按活动名称查询所有广告活动
        List<AmazonAdCampaignAll> allCampaigns = listByTypeAndCampaignNames(puid, reports, shopIds);

        //记录重复活动
        Map<String, List<AmazonAdCampaignAll>> mapList = allCampaigns.stream()
                .collect(Collectors.groupingBy(k -> getCampaignKeyFormat(
                        k.getShopId(), k.getType(), k.getName())));
        for (Map.Entry<String, List<AmazonAdCampaignAll>> entry : mapList.entrySet()) {
            if (entry.getValue().size() > 1) {
                Map<Integer, ShopAuth> shopIdMap = shopAuthMap.values().stream().collect(Collectors.toMap(ShopAuth::getId, Function.identity(), (a, b) -> a));
                Map<String, String> map = new HashMap<>();
                map.put("campaignName", entry.getValue().get(0).getName());
                map.put("country", Marketplace.fromId(shopIdMap.get(entry.getValue().get(0).getShopId()).getMarketplaceId()).getCountryCode());
                map.put("shopName", shopIdMap.get(entry.getValue().get(0).getShopId()).getName());
                throw new BizServiceException(ReportImportErrType.DUPLICATION_CAMPAIGN.name(), Jackson.toJsonString(map));              }
        }

        Map<String, AmazonAdCampaignAll> campaignNameMap = getCampaignNameMap(allCampaigns);
        Map<String, AmazonAdCampaignAll> campaignIdMap = getCampaignIdMap(allCampaigns);

        Map<String, List<LxAdProductReport>> lxAdProductReportMap = reports.stream().collect(
                Collectors.groupingBy(LxAdProductReport::getAdType));

        //填充campaignId,用于更精确查询ad-group
        lxAdProductReportMap.forEach((k, v) -> {
            fillCampaignIdForReport(puid, campaignNameMap, v);
        });



        Map<String, AmazonAdGroup> spAdGroupMap = getSpAdGroupMap(puid, reports);
        Map<String, AmazonSdAdGroup> sdAdGroupMap = getSdAdGroupMap(puid, reports);;

        //填充groupId,以便理精确查询ad-product (sp,sd)
        lxAdProductReportMap.forEach((k, v) -> {
            if (CampaignTypeEnum.sp.name().equalsIgnoreCase(k)) {
                for (LxAdProductReport report : v) {
                    String mapKey = getGroupKeyFormat( report.getShopId(), report.getCampaignId(), report.getAdGroupName());
                    if (!spAdGroupMap.containsKey(mapKey)) {
                        throw new BizServiceException("[行号 "+ (report.getRowNumber() + 1) +"] " + "SP广告组名称不存在 (广告组名称: " + report.getAdGroupName()+")");
                    }
                    AmazonAdGroup adGroup = spAdGroupMap.get(mapKey);
                    report.setGroupId(adGroup.getAdGroupId());
                }
            } else  if (CampaignTypeEnum.sd.name().equalsIgnoreCase(k)) {
                for (LxAdProductReport report : v) {
                    String mapKey = getGroupKeyFormat( report.getShopId(), report.getCampaignId(), report.getAdGroupName());
                    if (!sdAdGroupMap.containsKey(mapKey)) {
                        throw new BizServiceException("[行号 "+ (report.getRowNumber() + 1) +"] " + "SP广告组名称不存在 (广告组名称: " + report.getAdGroupName()+")");
                    }
                    AmazonSdAdGroup adGroup = sdAdGroupMap.get(mapKey);
                    report.setGroupId(adGroup.getAdGroupId());
                }
            }

        });


        //按groupId查询product
        List<String> skus = reports.stream().map(LxAdProductReport::getMsku)
                .collect(Collectors.toList());
        List<AmazonAdProduct> spAdProducts = amazonAdProductDao.getBySkus(puid, skus);
        List<AmazonSdAdProduct> sdAdProducts = amazonSdAdProductDao.getBySkus(puid, skus);

        Map<String, AmazonAdProduct> spAdProductMap = spAdProducts.stream().collect(
                Collectors.toMap(k -> String.format(productFormat, k.getShopId(),
                        k.getCampaignId(), k.getAdGroupId(), k.getSku()), Function.identity(), (a, b) -> a));
        Map<String, AmazonSdAdProduct> sdAdProductMap = sdAdProducts.stream().collect(
                Collectors.toMap(k -> String.format(productFormat, k.getShopId(),
                        k.getCampaignId(), k.getAdGroupId(), k.getSku()), Function.identity(), (a, b) -> a));


        List<AmazonAdProductReport> spReports = new ArrayList<>();
        List<AmazonAdSdProductReport> sdReports = new ArrayList<>();

        lxAdProductReportMap.forEach((k, v)-> {
            if (CampaignTypeEnum.sp.name().equalsIgnoreCase(k)) {
                for (LxAdProductReport report : v) {
                    String mapKey = String.format(productFormat, report.getShopId(), report.getCampaignId(), report.getGroupId(), report.getMsku());
                    if (!spAdProductMap.containsKey(mapKey)) {
                        throw new BizServiceException("[行号 "+ (report.getRowNumber() + 1) +"] " + "msku不存在 (广告组名称: " + report.getAdGroupName() + "; msku: "+ report.getMsku()+")");
                    }
                    AmazonAdProduct adProduct = spAdProductMap.get(mapKey);

                    AmazonAdProductReport amazonAdProductReport = new AmazonAdProductReport();
                    amazonAdProductReport.setCountDate(report.getSfCountDate());
                    amazonAdProductReport.setPuid(report.getPuid());
                    amazonAdProductReport.setShopId(report.getShopId());
                    amazonAdProductReport.setMarketplaceId(report.getMarketplaceId());
                    amazonAdProductReport.setAdId(adProduct.getAdId());
                    amazonAdProductReport.setCampaignId(adProduct.getCampaignId());
                    amazonAdProductReport.setAdGroupId(adProduct.getAdGroupId());
                    amazonAdProductReport.setAsin(adProduct.getAsin());
                    amazonAdProductReport.setSku(adProduct.getSku());

                    amazonAdProductReport.setAdGroupName(report.getAdGroupName());
                    amazonAdProductReport.setCampaignName(report.getCampaignName());

                    amazonAdProductReport.setImpressions(isDxmNumeric(report.getImpressions()) ? Integer.valueOf(report.getImpressions()) : 0);
                    amazonAdProductReport.setClicks(isDxmNumeric(report.getClicks()) ? Integer.valueOf(report.getClicks()) : 0);
                    amazonAdProductReport.setCost(isDxmNumeric(report.getAdCost()) ? new BigDecimal(report.getAdCost()) : BigDecimal.ZERO);
                    amazonAdProductReport.setTotalSales(isDxmNumeric(report.getAdSales()) ? new BigDecimal(report.getAdSales()) : BigDecimal.ZERO);
                    amazonAdProductReport.setAdSales(isDxmNumeric(report.getAdSelfSales()) ? new BigDecimal(report.getAdSelfSales()) : BigDecimal.ZERO);
                    //销量和订单里字段存反,历史遗留
                    amazonAdProductReport.setSaleNum(isDxmNumeric(report.getAdOrder()) ? Integer.valueOf(report.getAdOrder()) : 0);
                    amazonAdProductReport.setAdSaleNum(isDxmNumeric(report.getAdSelfOrder()) ? Integer.valueOf(report.getAdSelfOrder()) : 0);
                    amazonAdProductReport.setOrderNum(isDxmNumeric(report.getAdSaleNum()) ? Integer.valueOf(report.getAdSaleNum()) : 0);
                    amazonAdProductReport.setAdOrderNum(isDxmNumeric(report.getAdSelfSaleNum()) ? Integer.valueOf(report.getAdSelfSaleNum()) : 0);
                    spReports.add(amazonAdProductReport);

                }
            } else  if (CampaignTypeEnum.sd.name().equalsIgnoreCase(k)) {
                for (LxAdProductReport report : v) {
                    String mapKey = String.format(productFormat, report.getShopId(), report.getCampaignId(), report.getGroupId(), report.getMsku());
                    if (!sdAdProductMap.containsKey(mapKey)) {
                        throw new BizServiceException("[行号 "+ (report.getRowNumber() + 1) +"] " + "msku不存在 (广告组名称: " + report.getAdGroupName() + "; msku: "+ report.getMsku()+")");
                    }

                    AmazonAdCampaignAll campaignAll = campaignIdMap.get(
                            getCampaignKeyFormat( report.getShopId(), report.getAdType().toLowerCase(), report.getCampaignId()));

                    AmazonSdAdProduct adProduct = sdAdProductMap.get(mapKey);

                    AmazonAdSdProductReport sdProductReport = new AmazonAdSdProductReport();
                    sdProductReport.setPuid(report.getPuid());
                    sdProductReport.setShopId(report.getShopId());
                    sdProductReport.setMarketplaceId(report.getMarketplaceId());
                    sdProductReport.setCountDate(report.getSfCountDate());
                    sdProductReport.setTacticType(campaignAll.getTactic());
                    sdProductReport.setCurrency(Marketplace.fromId(report.getMarketplaceId()).getCurrencyCode().name());
                    sdProductReport.setCampaignName(campaignAll.getName());
                    sdProductReport.setCampaignId(campaignAll.getCampaignId());
                    sdProductReport.setAdGroupName(report.getAdGroupName());
                    sdProductReport.setAdGroupId(adProduct.getAdGroupId());
                    sdProductReport.setAsin(adProduct.getAsin());
                    sdProductReport.setSku(adProduct.getSku());
                    sdProductReport.setAdId(adProduct.getAdId());
                    sdProductReport.setClicks(isDxmNumeric(report.getClicks()) ? Integer.valueOf(report.getClicks()) : 0);
                    sdProductReport.setCost(isDxmNumeric(report.getAdCost()) ? new BigDecimal(report.getAdCost()) : BigDecimal.ZERO);
                    sdProductReport.setConversions14dSameSKU(isDxmNumeric(report.getAdSelfOrder()) ? Integer.valueOf(report.getAdSelfOrder()) : 0);
                    sdProductReport.setSales14dSameSKU(isDxmNumeric(report.getAdSelfSales()) ? new BigDecimal(report.getAdSelfSales()) : BigDecimal.ZERO);
                    sdProductReport.setImpressions(isDxmNumeric(report.getImpressions()) ? Integer.valueOf(report.getImpressions()) : 0);
                    sdProductReport.setCostType(campaignAll.getCostType());
                    sdProductReport.setUnitsOrdered14d(isDxmNumeric(report.getAdSaleNum()) ? Integer.valueOf(report.getAdSaleNum()) : 0);
                    sdProductReport.setConversions14d(isDxmNumeric(report.getAdOrder()) ? Integer.valueOf(report.getAdOrder()) : 0);
                    sdProductReport.setSales14d(isDxmNumeric(report.getAdSales()) ? new BigDecimal(report.getAdSales()) : BigDecimal.ZERO);

                    sdProductReport.setViewImpressions(isDxmNumeric(report.getViewImpressions()) ? Integer.valueOf(report.getViewImpressions()) : null);
                    sdReports.add(sdProductReport);
                }
            }
        });

        if (CollectionUtils.isNotEmpty(spReports)) {
            amazonAdProductReportDao.insertList(puid, spReports);
        }

        if (CollectionUtils.isNotEmpty(sdReports)) {
            amazonAdSdProductReportDao.insertOrUpdateList(puid, sdReports);
        }


    }

    private void fillCampaignIdForReport(Integer puid, Map<String, AmazonAdCampaignAll> campaignNameMap, List<LxAdProductReport> v) {
        for (LxAdProductReport report : v) {
            String mapKey = getCampaignKeyFormat(
                    report.getShopId(), report.getAdType().toLowerCase(), report.getCampaignName());
            if (!campaignNameMap.containsKey(mapKey)) {
                throw new BizServiceException("[行号 "+ (report.getRowNumber() + 1) +"] " + "广告活动不存在 (活动名称: " + report.getCampaignName()+")");
            }
            AmazonAdCampaignAll campaignAll = campaignNameMap.get(mapKey);
            report.setCampaignId(campaignAll.getCampaignId());
        }
    }


}
