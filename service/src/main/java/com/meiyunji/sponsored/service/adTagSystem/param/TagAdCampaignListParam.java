package com.meiyunji.sponsored.service.adTagSystem.param;

import lombok.Data;

import java.util.List;

@Data
public class TagAdCampaignListParam {

    private int puid;
    private int uid;
    private List<Integer> shopIds;
    private List<String> portfolioIds;
    private List<String> adTypes;
    private String state;
    private List<String> servingStatus;
    private String campaignName;
    private String productType;
    private String productValue;
    private boolean onlyNoTagCampaign;
    private int pageNum;
    private int pageSize;

    private List<String> campaignIds;
    private List<Long> tagGroupIds;
    private Boolean isAdmin;
}
