package com.meiyunji.sponsored.service.newDashboard.service;

import com.meiyunji.sponsored.service.newDashboard.dto.DashboardInsufficientInventoryDataDto;
import com.meiyunji.sponsored.service.newDashboard.vo.DashboardInsufficientInventoryReqVo;

import java.util.List;

/**
 * 库存不足
 * @author: zzh
 * @date: 2025-03-12  10:42
 */
public interface IDashboardInsufficientInventoryService {

    /**
     * 获取库存不足接口
     * @param reqVo 请求参数
     * @return 响应参数
     */
    List<DashboardInsufficientInventoryDataDto> queryInsufficientInventoryData(DashboardInsufficientInventoryReqVo reqVo);

    /**
     * 导出库存不足接口
     * @param reqVo 请求参数
     * @return 响应参数
     */
    List<String> exportInsufficientInventoryData(DashboardInsufficientInventoryReqVo reqVo);
}
