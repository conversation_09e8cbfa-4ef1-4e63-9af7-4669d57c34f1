package com.meiyunji.sponsored.service.enums;

public enum AdStateV3 {

    ENABLED("ENABLED", "enabled"),
    PAUSED ("PAUSED", "paused"),
    ;

    public String getValue() {
        return value;
    }

    AdStateV3(String value, String oldValue) {
        this.value = value;
        this.oldValue = oldValue;
    }

    private String value;
    private String oldValue;


    public static AdStateV3 fromValue(String value){
        for (AdStateV3 typeV3 : values()) {
            if (typeV3.getValue().equals(value)) {
                return typeV3;
            }
        }
        return null;
    }

    public String getOldValue() {
        return oldValue;
    }


    public static AdStateV3 fromOldValue(String oldValue){
        for (AdStateV3 typeV3 : values()) {
            if (typeV3.getOldValue().equals(oldValue)) {
                return typeV3;
            }
        }
        return null;
    }

}
