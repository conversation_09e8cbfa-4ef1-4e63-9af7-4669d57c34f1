package com.meiyunji.sponsored.service.autoRule.vo.rule;

import com.meiyunji.sponsored.service.autoRule.vo.BidDataOperate;
import lombok.Data;

/**
 * 操作动作
 */
@Data
public class AdDataOperateVo {
    /**
     * 调整类型(百分比:percentage,国定值:fixed)
     */
    private String ruleAdjust;

    /**
     * 调整值的类型(提高:increase；降低:reduce)
     */
    private String ruleAction;

    /**
     * 调整的具体数值
     */
    private String adjustValue;
    /**
     * 边界值
     */
    private String limitValue;

    /**
     * 竞价
     */
    private BidDataOperate bid;

    /**
     * 顶部竞价比例
     */
    private BidDataOperate placementTopBidRatio;
}
