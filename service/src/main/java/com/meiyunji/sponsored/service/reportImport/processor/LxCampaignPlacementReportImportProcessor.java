package com.meiyunji.sponsored.service.reportImport.processor;

import com.amazonaws.util.json.Jackson;
import com.meiyunji.sellfox.aadas.types.enumeration.Marketplace;
import com.meiyunji.sponsored.common.exception.BizServiceException;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.cpc.dao.*;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdCampaignAll;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdCampaignAllReport;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdCampaignPlacementReport;
import com.meiyunji.sponsored.service.enums.CampaignTypeEnum;
import com.meiyunji.sponsored.service.reportImport.enums.ReportImportErrType;
import com.meiyunji.sponsored.service.reportImport.message.AdReportImportMessage;
import com.meiyunji.sponsored.service.reportImport.model.LxCampaignPlacementReport;
import com.meiyunji.sponsored.service.reportImport.vo.DuplicationCampaignVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * lx广告位报告导入处理器
 *
 * <AUTHOR>
 * @date 2023/05/26
 */
@Service
@Slf4j
public class LxCampaignPlacementReportImportProcessor extends AbstractLxReportImportProcessor<LxCampaignPlacementReport> {

    protected final IAmazonAdCampaignAllReportDao amazonAdCampaignAllReportDao;
    protected final IAmazonAdCampaignPlacementReportDao amazonAdCampaignPlacementReportDao;

    protected LxCampaignPlacementReportImportProcessor(IAmazonAdCampaignAllDao amazonAdCampaignAllDao, IAmazonAdGroupDao amazonAdGroupDao, IAmazonSdAdGroupDao amazonSdAdGroupDao, IAmazonSbAdGroupDao amazonSbAdGroupDao, IAmazonAdCampaignAllReportDao amazonAdCampaignAllReportDao,IAmazonAdCampaignPlacementReportDao amazonAdCampaignPlacementReportDao) {
        super(amazonAdCampaignAllDao, amazonAdGroupDao, amazonSdAdGroupDao, amazonSbAdGroupDao);
        this.amazonAdCampaignAllReportDao = amazonAdCampaignAllReportDao;
        this.amazonAdCampaignPlacementReportDao = amazonAdCampaignPlacementReportDao;
    }


    /**
     * 导入报告
     *
     * @param importMessage           导入消息
     * @param reports                 报告
     * @param shopAuthMap    店铺数据
     */
    @Override
    public void importReport(AdReportImportMessage importMessage, List<LxCampaignPlacementReport> reports, Map<String, ShopAuth> shopAuthMap) {
        Integer puid = importMessage.getPuid();
        Long taskId = importMessage.getScheduleId();

        List<Integer> shopIds = shopAuthMap.values().stream().map(ShopAuth::getId).collect(Collectors.toList());
        //按活动名称查询所有广告活动
        List<AmazonAdCampaignAll> allCampaigns = listByTypeAndCampaignNames(puid, reports, shopIds);

        //记录重复活动
        Map<String, List<AmazonAdCampaignAll>> mapList = allCampaigns.stream()
                .collect(Collectors.groupingBy(k -> getCampaignKeyFormat(
                        k.getShopId(), k.getType(), k.getName())));
        for (Map.Entry<String, List<AmazonAdCampaignAll>> entry : mapList.entrySet()) {
            if (entry.getValue().size() > 1) {
                Map<Integer, ShopAuth> shopIdMap = shopAuthMap.values().stream().collect(Collectors.toMap(ShopAuth::getId, Function.identity(), (a, b) -> a));
                Map<String, String> map = new HashMap<>();
                map.put("campaignName", entry.getValue().get(0).getName());
                map.put("country", Marketplace.fromId(shopIdMap.get(entry.getValue().get(0).getShopId()).getMarketplaceId()).getCountryCode());
                map.put("shopName", shopIdMap.get(entry.getValue().get(0).getShopId()).getName());
                throw new BizServiceException(ReportImportErrType.DUPLICATION_CAMPAIGN.name(), Jackson.toJsonString(map));             }
        }

        Map<String, AmazonAdCampaignAll> campaignNameMap = getCampaignNameMap(allCampaigns);

        Map<String, List<LxCampaignPlacementReport>> campaignMap = reports.stream().collect(
                Collectors.groupingBy(LxCampaignPlacementReport::getAdType));

        // 注释入库活动报告表，后续删除该代码
//        // 广告活动报告表落库
//        List<AmazonAdCampaignAllReport> placeReports = buildAllReport(campaignMap, campaignNameMap);
//        if (CollectionUtils.isNotEmpty(placeReports)) {
//            amazonAdCampaignAllReportDao.insertOrUpdateList(puid, placeReports);
//        }

        // 广告活动报告表落库
        List<AmazonAdCampaignPlacementReport> placementReports = buildPlacementReport(campaignMap, campaignNameMap);
        if (CollectionUtils.isNotEmpty(placementReports)) {
            amazonAdCampaignPlacementReportDao.insertOrUpdateList(puid, placementReports);
        }
    }

    private List<AmazonAdCampaignAllReport> buildAllReport(Map<String, List<LxCampaignPlacementReport>> campaignMap, Map<String, AmazonAdCampaignAll> campaignNameMap) {
        List<AmazonAdCampaignAllReport> placeReports = new ArrayList<>();

        campaignMap.forEach((k, v) -> {
            if (CampaignTypeEnum.sp.name().equalsIgnoreCase(k)) {
                for (LxCampaignPlacementReport report : v) {
                    String mapKey = getCampaignKeyFormat(
                            report.getShopId(), report.getAdType().toLowerCase(), report.getCampaignName());
                    if (!campaignNameMap.containsKey(mapKey)) {
                        throw new BizServiceException("[行号 "+ (report.getRowNumber() + 1) +"] " + "广告活动不存在 (活动名称: " + report.getCampaignName()+")");
                    }
                    AmazonAdCampaignAll campaignAll = campaignNameMap.get(mapKey);

                    AmazonAdCampaignAllReport amazonAdCampaignReport = new AmazonAdCampaignAllReport();
                    amazonAdCampaignReport.setPuid(report.getPuid());
                    amazonAdCampaignReport.setShopId(report.getShopId());
                    amazonAdCampaignReport.setMarketplaceId(report.getMarketplaceId());
                    amazonAdCampaignReport.setCountDate(report.getSfCountDate());
                    amazonAdCampaignReport.setCampaignId(campaignAll.getCampaignId());
                    amazonAdCampaignReport.setType(CampaignTypeEnum.sp.getCampaignType());
                    String placement;
                    if ("商品页面".equalsIgnoreCase(report.getPlacement())){
                        placement = "Detail Page on-Amazon";
                    } else if ("搜索结果顶部(首页)".equalsIgnoreCase(report.getPlacement())) {
                        placement = "Top of Search on-Amazon";
                    } else if ("搜索结果的其余位置".equalsIgnoreCase(report.getPlacement())) {
                        placement = "Other on-Amazon";
                    } else {
                        placement = "Search on-Amazon";
                    }

                    amazonAdCampaignReport.setCampaignType(placement); //All表示汇总信息
                    amazonAdCampaignReport.setPlacement(placement);
                    amazonAdCampaignReport.setIsSummary(0);
                    amazonAdCampaignReport.setCampaignName(report.getCampaignName());
                    amazonAdCampaignReport.setConversions7d(isDxmNumeric(report.getAdOrder()) ? Integer.valueOf(report.getAdOrder()) : 0);
                    amazonAdCampaignReport.setConversions7dSameSKU(isDxmNumeric(report.getAdSelfOrder()) ? Integer.valueOf(report.getAdSelfOrder()) : 0);
                    amazonAdCampaignReport.setSales7d(isDxmNumeric(report.getAdSales()) ? new BigDecimal(report.getAdSales()) : BigDecimal.ZERO);
                    amazonAdCampaignReport.setSales7dSameSKU(isDxmNumeric(report.getAdSelfSales()) ? new BigDecimal(report.getAdSelfSales()) : BigDecimal.ZERO);
                    amazonAdCampaignReport.setUnitsOrdered7d(isDxmNumeric(report.getAdSaleNum()) ? Integer.valueOf(report.getAdSaleNum()) : 0);
                    amazonAdCampaignReport.setUnitsOrdered7dSameSKU(isDxmNumeric(report.getAdSelfSaleNum()) ? Integer.valueOf(report.getAdSelfSaleNum()) : 0);
                    amazonAdCampaignReport.setClicks(isDxmNumeric(report.getClicks()) ? Integer.valueOf(report.getClicks()) : 0);
                    amazonAdCampaignReport.setCost(isDxmNumeric(report.getAdCost()) ? new BigDecimal(report.getAdCost()) : BigDecimal.ZERO);
                    amazonAdCampaignReport.setImpressions(isDxmNumeric(report.getImpressions()) ? Integer.valueOf(report.getImpressions()) : 0);

                    placeReports.add(amazonAdCampaignReport);
                }
            } else {
                //目前只支持SP,其它类型丢弃
            }
        });
        return placeReports;
    }

    private List<AmazonAdCampaignPlacementReport> buildPlacementReport(Map<String, List<LxCampaignPlacementReport>> campaignMap, Map<String, AmazonAdCampaignAll> campaignNameMap) {
        List<AmazonAdCampaignPlacementReport> placeReports = new ArrayList<>();

        campaignMap.forEach((k, v) -> {
            if (CampaignTypeEnum.sp.name().equalsIgnoreCase(k)) {
                for (LxCampaignPlacementReport report : v) {
                    String mapKey = getCampaignKeyFormat(
                            report.getShopId(), report.getAdType().toLowerCase(), report.getCampaignName());
                    if (!campaignNameMap.containsKey(mapKey)) {
                        throw new BizServiceException("[行号 "+ (report.getRowNumber() + 1) +"] " + "广告活动不存在 (活动名称: " + report.getCampaignName()+")");
                    }
                    AmazonAdCampaignAll campaignAll = campaignNameMap.get(mapKey);

                    AmazonAdCampaignPlacementReport amazonAdCampaignReport = new AmazonAdCampaignPlacementReport();
                    amazonAdCampaignReport.setPuid(report.getPuid());
                    amazonAdCampaignReport.setShopId(report.getShopId());
                    amazonAdCampaignReport.setMarketplaceId(report.getMarketplaceId());
                    amazonAdCampaignReport.setCountDate(report.getSfCountDate());
                    amazonAdCampaignReport.setCampaignId(campaignAll.getCampaignId());
                    amazonAdCampaignReport.setType(CampaignTypeEnum.sp.getCampaignType());
                    String placement;
                    if ("商品页面".equalsIgnoreCase(report.getPlacement())){
                        placement = "Detail Page on-Amazon";
                    } else if ("搜索结果顶部(首页)".equalsIgnoreCase(report.getPlacement())) {
                        placement = "Top of Search on-Amazon";
                    } else if ("搜索结果的其余位置".equalsIgnoreCase(report.getPlacement())) {
                        placement = "Other on-Amazon";
                    } else {
                        placement = "Search on-Amazon";
                    }

                    amazonAdCampaignReport.setCampaignType(placement); //All表示汇总信息
                    amazonAdCampaignReport.setPlacement(placement);
                    amazonAdCampaignReport.setIsSummary(0);
                    amazonAdCampaignReport.setCampaignName(report.getCampaignName());
                    amazonAdCampaignReport.setConversions7d(isDxmNumeric(report.getAdOrder()) ? Integer.valueOf(report.getAdOrder()) : 0);
                    amazonAdCampaignReport.setConversions7dSameSKU(isDxmNumeric(report.getAdSelfOrder()) ? Integer.valueOf(report.getAdSelfOrder()) : 0);
                    amazonAdCampaignReport.setSales7d(isDxmNumeric(report.getAdSales()) ? new BigDecimal(report.getAdSales()) : BigDecimal.ZERO);
                    amazonAdCampaignReport.setSales7dSameSKU(isDxmNumeric(report.getAdSelfSales()) ? new BigDecimal(report.getAdSelfSales()) : BigDecimal.ZERO);
                    amazonAdCampaignReport.setUnitsOrdered7d(isDxmNumeric(report.getAdSaleNum()) ? Integer.valueOf(report.getAdSaleNum()) : 0);
                    amazonAdCampaignReport.setUnitsOrdered7dSameSKU(isDxmNumeric(report.getAdSelfSaleNum()) ? Integer.valueOf(report.getAdSelfSaleNum()) : 0);
                    amazonAdCampaignReport.setClicks(isDxmNumeric(report.getClicks()) ? Integer.valueOf(report.getClicks()) : 0);
                    amazonAdCampaignReport.setCost(isDxmNumeric(report.getAdCost()) ? new BigDecimal(report.getAdCost()) : BigDecimal.ZERO);
                    amazonAdCampaignReport.setImpressions(isDxmNumeric(report.getImpressions()) ? Integer.valueOf(report.getImpressions()) : 0);

                    placeReports.add(amazonAdCampaignReport);
                }
            } else {
                //目前只支持SP,其它类型丢弃
            }
        });
        return placeReports;
    }
}
