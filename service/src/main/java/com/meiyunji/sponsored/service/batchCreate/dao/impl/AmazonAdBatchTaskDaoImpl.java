package com.meiyunji.sponsored.service.batchCreate.dao.impl;

import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.springjdbc.BaseShardingDaoImpl;
import com.meiyunji.sponsored.common.springjdbc.ConditionBuilder;
import com.meiyunji.sponsored.service.batchCreate.dao.IAmazonAdBatchTaskDao;
import com.meiyunji.sponsored.service.batchCreate.dto.task.TaskBasicInfoDTO;
import com.meiyunji.sponsored.service.batchCreate.dto.taskView.TaskViewBatchSpListDTO;
import com.meiyunji.sponsored.service.batchCreate.enums.SpBatchCreateTaskStatusEnum;
import com.meiyunji.sponsored.service.batchCreate.po.AmazonAdBatchTask;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;


/**
 * @author: sun<PERSON><PERSON>
 * @email: <EMAIL>
 * @date: 2023-09-06  18:41
 */

@Repository
public class AmazonAdBatchTaskDaoImpl extends BaseShardingDaoImpl<AmazonAdBatchTask> implements IAmazonAdBatchTaskDao {

    @Override
    public void insertList(Integer puid, List<AmazonAdBatchTask> taskList) {
        StringBuilder sql = new StringBuilder(" INSERT INTO ");
        sql.append(getJdbcHelper().getTable());
        sql.append(" (id, puid, shop_id, marketplace_id, task_name, ad_structure, campaign_type, status, source_id, login_ip, create_time, create_id) values ");
        List<Object> argsList = new ArrayList<>(taskList.size());
        for (AmazonAdBatchTask task : taskList) {
            sql.append(" (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?),");
            argsList.add(task.getId());
            argsList.add(task.getPuid());
            argsList.add(task.getShopId());
            argsList.add(task.getMarketplaceId());
            argsList.add(task.getTaskName());
            argsList.add(task.getAdStructure());
            argsList.add(task.getCampaignType());
            argsList.add(task.getStatus());
            argsList.add(task.getSourceId());
            argsList.add(task.getLoginIp());
            argsList.add(task.getCreateTime());
            argsList.add(task.getCreateId());
        }
        sql.deleteCharAt(sql.length() - 1);
        getJdbcTemplate(puid).update(sql.toString(), argsList.toArray());
    }

    @Override
    public List<TaskBasicInfoDTO> getTaskIdListByName(Integer puid, List<Integer> shopIdList, List<String> marketIdList, String searchName) {
        StringBuilder sql = new StringBuilder(" SELECT id, task_name name from ").append(getJdbcHelper().getTable());
        List<Object> argsList = Lists.newArrayList();
        sql.append(" WHERE puid = ? ");
        argsList.add(puid);
        if (CollectionUtils.isNotEmpty(marketIdList)) {
            sql.append(" and marketplace_id in ( \"").append(StringUtils.join(marketIdList, "\",\"")).append("\" ) ");
        }

        if (CollectionUtils.isNotEmpty(shopIdList)) {
            sql.append(" and shop_id in ( ").append(StringUtils.join(shopIdList, ",")).append(" ) ");
        }

        if (StringUtils.isNotEmpty(searchName)) {
            sql.append(" and task_name like ? ");
            argsList.add("%" + searchName.trim() + "%");
        }

        Object[] args = argsList.toArray();
        return getJdbcTemplate(puid).query(sql.toString(), args, (row, i) -> TaskBasicInfoDTO.builder()
                .id(row.getLong("id"))
                .name(row.getString("name")).build());
    }

    @Override
    public int updateStatusById(Integer puid, Long taskId, Byte status) {
        StringBuilder sb = new StringBuilder("update ").append(getJdbcHelper().getTable());
        sb.append(" set status = ? , update_time = now() where id = ?");
        return getJdbcTemplate(puid).update(sb.toString(), status, taskId);
    }

    @Override
    public Page<TaskViewBatchSpListDTO> getTaskAndProductInfoList(Integer puid, Integer pageNum,
                                                                  Integer pageSize, List<Integer> shopIdList,
                                                                  List<String> marketIdList, String asin,
                                                                  String createTime, String endTime, List<Integer> creatorId, String taskName) {
        StringBuilder selectSql ;
        List<Object> argsList = Lists.newArrayList();
        if (StringUtils.isNotEmpty(asin)) {
            selectSql = new StringBuilder("select DISTINCT a.id as taskId, a.task_name as taskName, a.shop_id as shopId, a.create_time as createTime, a.create_id  as creatorId, a.status as taskStatus from t_amazon_ad_batch_task a ");
            selectSql.append(" inner join t_amazon_ad_batch_product b on a.puid = b.puid and a.shop_id = b.shop_id and a.marketplace_id = b.marketplace_id and  a.id = b.task_id ");
        } else {
            selectSql = new StringBuilder("select DISTINCT a.id as taskId, a.task_name as taskName, a.shop_id as shopId, a.create_time as createTime, a.create_id  as creatorId, a.status as taskStatus from t_amazon_ad_batch_task a");
        }

        StringBuilder whereSql = new StringBuilder(" where a.puid = ? ");
        argsList.add(puid);
        if (CollectionUtils.isNotEmpty(marketIdList)) {
            whereSql.append(" and a.marketplace_id in ( \"").append(StringUtils.join(marketIdList, "\",\"")).append("\" ) ");
        }

        if (CollectionUtils.isNotEmpty(shopIdList)) {
            whereSql.append(" and a.shop_id in ( \"").append(StringUtils.join(shopIdList, "\",\"")).append("\" ) ");
        }

        if (StringUtils.isNotEmpty(taskName)) {
            whereSql.append(" and a.task_name like ? ");
            argsList.add("%" + taskName.trim() + "%");
        }

        if (CollectionUtils.isNotEmpty(creatorId)) {
            whereSql.append(" and a.create_id in ( \"").append(StringUtils.join(creatorId, "\",\"")).append("\" ) ");
        }

        whereSql.append(" and a.create_time >= ? ");
        whereSql.append(" and a.create_time <= ? ");
        argsList.add(createTime);
        argsList.add(endTime);

        if (StringUtils.isNotEmpty(asin)) {
            whereSql.append(" and b.asin = ? ");
            argsList.add(asin);
        }

        selectSql.append(whereSql);
        selectSql.append(" order by a.id desc ");

        StringBuilder countSql;
        if (StringUtils.isNotEmpty(asin)) {
            countSql = new StringBuilder("select count(DISTINCT a.id) from t_amazon_ad_batch_task a inner join t_amazon_ad_batch_product b on a.puid = b.puid and a.shop_id = b.shop_id and a.marketplace_id = b.marketplace_id and  a.id = b.task_id");
        } else {
            countSql = new StringBuilder("select count(DISTINCT a.id) from t_amazon_ad_batch_task a");
        }
        countSql.append(whereSql);
        Object[] args = argsList.toArray();
        return this.getPageResultByClass(puid, pageNum, pageSize, countSql.toString(), args, selectSql.toString(), args, TaskViewBatchSpListDTO.class);
    }

    @Override
    public int terminateTask(Integer puid, Long taskId, Byte status, List<Byte> excludeStatus) {
        StringBuilder sb = new StringBuilder("update ").append(getJdbcHelper().getTable());
        sb.append(" set status = ?, stop_time = now(), update_time = now() where id = ? ");
        if (CollectionUtils.isNotEmpty(excludeStatus)) {
            sb.append(" and status in (").append(StringUtils.join(excludeStatus, ",")).append(") ");
        }
        return getJdbcTemplate(puid).update(sb.toString(), status, taskId);
    }


    @Override
    public List<AmazonAdBatchTask> getNeedRetryListByPuidAndshopId(Integer puid, Integer shopId, Long taskId) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .equalTo("id",taskId)
                .in("status", new Object[]{SpBatchCreateTaskStatusEnum.DOING.getCode()});
        return listByCondition(puid, builder.build());
    }

    @Override
    public int deleteByPuidAndShopId(Integer puid, Integer shopId, Integer limit) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sql = new StringBuilder("delete from ").append(this.getJdbcHelper().getTable()).append(" where puid = ? ");
        argsList.add(puid);
        sql.append("and shop_id = ?");
        argsList.add(shopId);
        if (limit != null) {
            sql.append(" limit ? ");
            argsList.add(limit);
        }
        return getJdbcTemplate(puid).update(sql.toString(), argsList.toArray());
    }
}