package com.meiyunji.sponsored.service.enums;

import lombok.Getter;

/**
 * @author: ys
 * @date: 2024/6/26 17:12
 * @describe:
 */
@Getter
public enum SBKeywordRecommendationCreativeTypeEnum {
    PRODUCT_COLLECTION(1, "PRODUCT_COLLECTION", "商品集"),
    AUTHOR_COLLECTION(2, "AUTHOR_COLLECTION", "品牌集"),
    STORE_SPOTLIGHT(3, "STORE_SPOTLIGHT", "品牌旗舰店焦点"),
    VIDEO(4, "VIDEO", "SBV-商品详情页"),
    BRAND_VIDEO(5, "BRAND_VIDEO", "SBV-亚马逊旗舰店")
    ;

    private Integer code;
    private String val;
    private String msg;

    SBKeywordRecommendationCreativeTypeEnum(Integer code, String val, String msg) {
        this.code = code;
        this.val = val;
        this.msg = msg;
    }

    public static SBKeywordRecommendationCreativeTypeEnum getSBKeywordRecommendationCreativeTypeEnumByCode(int code) {
        for(SBKeywordRecommendationCreativeTypeEnum en : SBKeywordRecommendationCreativeTypeEnum.values()) {
            if (en.getCode() == code) {
                return en;
            }
        }
        return null;
    }
}
