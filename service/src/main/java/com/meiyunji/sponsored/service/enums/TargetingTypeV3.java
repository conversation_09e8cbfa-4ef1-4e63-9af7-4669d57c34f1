package com.meiyunji.sponsored.service.enums;

public enum TargetingTypeV3 {

    AUTO("AUTO", "auto"),
    MANUAL("MANUAL", "manual"),
    ;

    TargetingTypeV3(String value, String oldValue) {
        this.value = value;
        this.oldValue = oldValue;
    }

    private String value;
    private String oldValue;

    public String getOldValue() {
        return oldValue;
    }

    public String getValue() {
        return value;
    }


    public static TargetingTypeV3 fromValue(String value){
        for (TargetingTypeV3 typeV3 : values()) {
            if (typeV3.getValue().equals(value)) {
                return typeV3;
            }
        }
        return null;
    }

    public static TargetingTypeV3 fromOldValue(String oldValue){
        for (TargetingTypeV3 typeV3 : values()) {
            if (typeV3.getOldValue().equals(oldValue)) {
                return typeV3;
            }
        }
        return null;
    }
}
