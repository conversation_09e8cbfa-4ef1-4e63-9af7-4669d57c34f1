package com.meiyunji.sponsored.service.export.handler;

import com.meiyunji.sponsored.common.base.ProcessMsg;
import com.meiyunji.sponsored.common.support.StringRedisService;
import com.meiyunji.sponsored.common.util.JSONUtil;
import com.meiyunji.sponsored.rpc.adCommon.OperationContent;
import com.meiyunji.sponsored.rpc.adCommon.OperationLogPageResponse;
import com.meiyunji.sponsored.rpc.adCommon.OperationLogVo;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.cpc.dao.IAdManagePageExportTaskDao;
import com.meiyunji.sponsored.service.cpc.po.AdManagePageExportTask;
import com.meiyunji.sponsored.service.cpc.service2.ICpcCommonService;
import com.meiyunji.sponsored.service.enums.AdTypeEnum;
import com.meiyunji.sponsored.service.excel.excelTools.ExcelExtraParam;
import com.meiyunji.sponsored.service.excel.excelTools.WriteHandlerBuild;
import com.meiyunji.sponsored.service.excel.excelTools.service.IExcelService;
import com.meiyunji.sponsored.service.export.AdManagePageExportTaskHandler;
import com.meiyunji.sponsored.service.export.constants.AdManagePageExportTaskConstant;
import com.meiyunji.sponsored.service.log.enums.OperationLogFromEnum;
import com.meiyunji.sponsored.service.log.qo.OperationLogQo;
import com.meiyunji.sponsored.service.vo.AdExportLogVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: nongwenhua
 * @email: <EMAIL>
 * @create: 2024-07-23 19:27
 */
@Service(AdManagePageExportTaskConstant.AD_LOG)
@Slf4j
public class AdLogExportTaskHandler implements AdManagePageExportTaskHandler {
    @Autowired
    private ICpcCommonService cpcCommonService;
    @Autowired
    private IAdManagePageExportTaskDao adManagePageExportTaskDao;
    @Autowired
    private IExcelService excelService;
    @Autowired
    private StringRedisService stringRedisService;
    @Autowired
    private IScVcShopAuthDao shopAuthDao;
    private static final String SUCCESS_RESULT = "0";
    private static final String DEFAULT_DISPLAYED_EMPTY_DATA = "-";
    private static final String CELL_LENGTH_OUT_OF_LIMIT = "操作内容过长，请到赛狐系统中查看";
    private static final int CELL_LENGTH_LIMIT = 30000;

    @Override
    public void export(AdManagePageExportTask task) {
        OperationLogQo operationLogQo = JSONUtil.jsonToObject(task.getParam(), OperationLogQo.class);
        if (operationLogQo == null) {
            log.error(String.format("ad log export error, param is null, task id : %d", task.getId()));
            adManagePageExportTaskDao.updateErrorStatus(task.getId());
            return;
        }
        log.info("begin = {}", JSONUtil.objectToJson(task));
        OperationLogPageResponse.Page page = cpcCommonService.operationLogPageList(operationLogQo);
        OperationLogFromEnum operationLogFromEnum = OperationLogFromEnum.getByType(operationLogQo.getFrom());
        if (!OperationLogFromEnum.SUPPORT_EXPORT_LIST.contains(operationLogFromEnum)) {
            log.warn("invalid operationLogFromEnum.type:{}", operationLogQo.getFrom());
        }
        List<AdExportLogVo> rows = page.getRowsList().stream().map(each -> buildExportVo(each, operationLogFromEnum)).collect(Collectors.toList());
        List<String> urlList = new ArrayList<>();
        WriteHandlerBuild build = new WriteHandlerBuild().rate().currencyNew(AdExportLogVo.class);
        ShopAuth shop = shopAuthDao.getScAndVcById(operationLogQo.getShopId());
        String logType = OperationLogFromEnum.SELLFOX.equals(operationLogFromEnum) ? "赛狐手动调整" : "亚马逊记录";
        String startTime = operationLogQo.getSiteStart().split(" ")[0];
        String endTime = operationLogQo.getSiteEnd().split(" ")[0];
        String fileName = shop.getName() + "_广告日志_" + logType + "_" + startTime + "_" + endTime;
        if (CollectionUtils.isNotEmpty(rows)) {
            ExcelExtraParam excelExtraParam = new ExcelExtraParam();
            excelExtraParam.setWrapped(true);
            urlList.add(excelService.easyExcelHandlerExportWithExtraParam(task.getPuid(), rows, fileName,
                    AdExportLogVo.class, build, buildVoExcludeFields(operationLogFromEnum), excelExtraParam));
        }
        //修改任务状态
        adManagePageExportTaskDao.updateSuccessStatus(task.getId(), JSONUtil.objectToJson(urlList), 100);
        stringRedisService.set(operationLogQo.getUuid(), new ProcessMsg(1, urlList.size(), "导出成功", urlList));
    }

    private List<String> buildVoExcludeFields(OperationLogFromEnum operationLogFromEnum) {
        if (OperationLogFromEnum.SELLFOX.equals(operationLogFromEnum)) {
            return Collections.emptyList();
        } else {
            return Arrays.asList("campaignType", "operator");
        }
    }

    private AdExportLogVo buildExportVo(OperationLogVo operationLogVo, OperationLogFromEnum operationLogFromEnum) {
        AdExportLogVo adExportLogVo = new AdExportLogVo();
        adExportLogVo.setPortfolio(StringUtils.defaultIfBlank(operationLogVo.getPortfolioName(), DEFAULT_DISPLAYED_EMPTY_DATA));
        adExportLogVo.setCampaign(StringUtils.defaultIfBlank(operationLogVo.getCampaignName(), DEFAULT_DISPLAYED_EMPTY_DATA));
        adExportLogVo.setGroup(StringUtils.defaultIfBlank(operationLogVo.getGroupName(), DEFAULT_DISPLAYED_EMPTY_DATA));
        adExportLogVo.setOperationTarget(operationLogVo.getOperationObject());
        adExportLogVo.setOperationType(operationLogVo.getAction());
        adExportLogVo.setOperationContent(buildOperationContent(operationLogVo, operationLogFromEnum));
        adExportLogVo.setOperationResult(SUCCESS_RESULT.equals(operationLogVo.getResult()) ? "成功" : "失败");
        adExportLogVo.setOperationTime(buildOperationTime(operationLogVo));
        adExportLogVo.setCampaignType(buildCampaignType(operationLogVo, operationLogFromEnum));
        adExportLogVo.setOperator(operationLogVo.getUserName());
        return adExportLogVo;
    }

    private String buildOperationContent(OperationLogVo operationLogVo, OperationLogFromEnum operationLogFromEnum) {
        List<OperationContent> operationContents = operationLogVo.getOperationContentList();
        StringBuilder builder = new StringBuilder();
        for (int i = 0; i < operationContents.size(); i++) {
            OperationContent operationContent = operationContents.get(i);
            if ((i == 0 || !operationContents.get(i - 1).getTitle().equals(operationContent.getTitle()))
                    && StringUtils.isNotBlank(operationContent.getTitle())) {
                builder.append(operationContent.getTitle()).append("\n");
            }
            if (OperationLogFromEnum.SELLFOX.equals(operationLogFromEnum)) {
                builder.append(i + 1).append("、");
            }
            if (StringUtils.isNotBlank(operationContent.getName())) {
                builder.append(operationContent.getName()).append("：");
            }
            builder.append(operationContent.getPreviousValue());
            if (StringUtils.isNotBlank(operationContent.getPreviousValue()) && StringUtils.isNotBlank(operationContent.getNewValue())) {
                builder.append("→");
            }
            builder.append(operationContent.getNewValue()).append("\n");
            if (operationContent.getType().equals("targeting")) {
                builder.append("品牌：").append(operationContent.getBrandDesc()).append("\n");
                builder.append("价格范围：").append(operationContent.getPriceDesc()).append("\n");
                builder.append("星级：").append(operationContent.getRatingDesc()).append("\n");
            }
        }
        String result = builder.toString();
        if (result.length() >= CELL_LENGTH_LIMIT) {
            result = result.substring(0, CELL_LENGTH_LIMIT) + CELL_LENGTH_OUT_OF_LIMIT;
        }
        return result;
    }

    private String buildOperationTime(OperationLogVo operationLogVo) {
        StringBuilder builder = new StringBuilder();
        builder.append("站点 ")
                .append(operationLogVo.getSiteOperationTime())
                .append("\n北京 ")
                .append(operationLogVo.getOperationTime());
        return builder.toString();
    }

    private String buildCampaignType(OperationLogVo operationLogVo, OperationLogFromEnum operationLogFromEnum) {
        if (OperationLogFromEnum.SELLFOX.equals(operationLogFromEnum)) {
            String adType = operationLogVo.getAdType();
            if (AdTypeEnum.sp.getCampaignType().equalsIgnoreCase(adType)) {
                return "商品推广(SP)";
            } else if (AdTypeEnum.sb.getCampaignType().equalsIgnoreCase(adType)) {
                return "品牌推广(SB)";
            } else if (AdTypeEnum.sd.getCampaignType().equalsIgnoreCase(adType)) {
                return "展示型推广(SD)";
            }
        }
        return StringUtils.EMPTY;
    }

}
