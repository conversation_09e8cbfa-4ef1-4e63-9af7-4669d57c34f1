package com.meiyunji.sponsored.service.cpc.dao.impl;

import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.config.DynamicRefreshNacosConfiguration;
import com.meiyunji.sponsored.common.springjdbc.BaseShardingSphereDaoImpl;
import com.meiyunji.sponsored.common.springjdbc.ConditionBuilder;
import com.meiyunji.sponsored.common.springjdbc.LogicType;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.common.util.SqlStringUtil;
import com.meiyunji.sponsored.common.util.StringUtil;
import com.meiyunji.sponsored.service.autoRule.vo.AutoRuleObjectParam;
import com.meiyunji.sponsored.service.autoRuleTask.vo.ProcessTaskParam;
import com.meiyunji.sponsored.service.config.DynamicRefreshConfiguration;
import com.meiyunji.sponsored.service.cpc.dao.ICpcQueryTargetingReportDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdKeyword;
import com.meiyunji.sponsored.service.cpc.po.CpcQueryKeywordReport;
import com.meiyunji.sponsored.service.cpc.po.CpcQueryTargetingReport;
import com.meiyunji.sponsored.service.cpc.po.SearchQueryTagParam;
import com.meiyunji.sponsored.service.cpc.service.impl.ReportService;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.vo.*;
import com.meiyunji.sponsored.service.cpc.vo.chartVo.AdHomePerformancedto;
import com.meiyunji.sponsored.service.enums.MatchValueEnum;
import com.meiyunji.sponsored.service.wordFrequency.bo.WordBo;
import com.meiyunji.sponsored.service.wordFrequency.enums.WordRoot;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.shardingsphere.infra.hint.HintManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.meiyunji.sponsored.service.cpc.util.Constants.ASIN_REGEX;


/**
 * CpcQueryTargetingReport
 * <AUTHOR>
 * 顾客反馈order_num数据不准,排查发现应该使用sale_num字段作为订单量值,以下把order_num值设为sale_num的值,但字段名不变
 */
@Repository
public class  CpcQueryTargetingReportDaoImpl extends BaseShardingSphereDaoImpl<CpcQueryTargetingReport> implements ICpcQueryTargetingReportDao {

    private final int imageLimit = 1000;
    private final int titleLimit = 500;
    private final int LIMIT = 2000;

    @Autowired
    private DynamicRefreshNacosConfiguration nacosConfiguration;
    @Autowired
    private DynamicRefreshConfiguration dynamicRefreshConfiguration;

    @Override
    public void insertList(Integer puid, List<CpcQueryTargetingReport> list) {
        //插入原表
        insertListOriginAndHotTable(puid, list, getJdbcHelper().getTable());
        if (nacosConfiguration.isHotTableWritePhase2Enable()) {
            //插入热表
            insertListOriginAndHotTable(puid, list, getHotTableName());
        }
    }

    private void insertListOriginAndHotTable(Integer puid, List<CpcQueryTargetingReport> list, String tableName) {
        StringBuilder sql = new StringBuilder("INSERT INTO ");
        sql.append(tableName);
        sql.append(" (`puid`,`shop_id`,`marketplace_id`,`campaign_id`,")
                .append("`ad_group_id`,`target_id`,`count_date`,`targeting_expression`,`targeting_text`,")
                .append("`targeting_type`,`ad_group_name`,`campaign_name`,`query`,`is_asin`,`cost`,`cost_rmb`, ")
                .append("`cost_usd`,`total_sales`,`total_sales_rmb`,`total_sales_usd`,`ad_sales`,`ad_sales_rmb`,")
                .append("`ad_sales_usd`,`impressions`,`clicks`,`order_num`,`ad_order_num`,`sale_num`,`ad_sale_num`,`query_id`,`create_time`,`update_time`) VALUES");
        List<Object> argsList = Lists.newArrayList();
        for (CpcQueryTargetingReport report : list) {
            sql.append(" (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,now(),now()),");
            argsList.add(puid);
            argsList.add(report.getShopId());
            argsList.add(report.getMarketplaceId());
            argsList.add(report.getCampaignId());
            argsList.add(report.getAdGroupId());
            argsList.add(report.getTargetId());
            argsList.add(report.getCountDate());
            argsList.add(report.getTargetingExpression());
            argsList.add(report.getTargetingText());
            argsList.add(report.getTargetingType());
            argsList.add(report.getAdGroupName());
            argsList.add(report.getCampaignName());
            argsList.add(report.getQuery());
            argsList.add(report.getIsAsin());
            argsList.add(report.getCost());
            argsList.add(report.getCostRmb());
            argsList.add(report.getCostUsd());
            argsList.add(report.getTotalSales());
            argsList.add(report.getTotalSalesRmb());
            argsList.add(report.getTotalSalesUsd());
            argsList.add(report.getAdSales());
            argsList.add(report.getAdSalesRmb());
            argsList.add(report.getAdSalesUsd());
            argsList.add(report.getImpressions());
            argsList.add(report.getClicks());
            argsList.add(report.getOrderNum());
            argsList.add(report.getAdOrderNum());
            argsList.add(report.getSaleNum());
            argsList.add(report.getAdSaleNum());
            argsList.add(report.getQueryId());
        }
        sql.deleteCharAt(sql.length() - 1);
        sql.append(" on duplicate key update `ad_group_name`=values(ad_group_name),`campaign_name`=values(campaign_name),")
                .append("`cost`=values(cost),cost_rmb=values(cost_rmb),cost_usd=values(cost_usd),`total_sales`=values(total_sales),`total_sales_rmb`=values(total_sales_rmb),`total_sales_usd`=values(total_sales_usd),")
                .append("`ad_sales`=values(ad_sales),`ad_sales_rmb`=values(ad_sales_rmb),`ad_sales_usd`=values(ad_sales_usd),`impressions`=values(impressions),`clicks`=values(clicks),")
                .append("`order_num`=values(order_num),`ad_order_num`=values(ad_order_num),`sale_num`=values(sale_num),`ad_sale_num`=values(ad_sale_num) ");

        HintManager hintManager = HintManager.getInstance();
        try {
            getJdbcTemplate(puid, hintManager).update(sql.toString(), argsList.toArray());
        } finally {
            hintManager.close();
        }
    }

    @Override
    public Page pageList(Integer puid, CpcQueryWordDto dto, Page page) {
        //订单量字段展示修改为 sale_num 更为准确
        StringBuilder selectSql = new StringBuilder("SELECT `query`,target_id,main_image,targeting_expression,targeting_type,ad_group_id,ad_group_name,campaign_id,campaign_name,")
                .append("SUM(impressions) impressions,SUM(clicks) clicks,SUM(cost) cost,SUM(sale_num) sale_num,SUM(ad_order_num) ad_order_num,")
                .append("SUM(total_sales) total_sales FROM ");
        selectSql.append(getTableNameByStartDate(DateUtil.strToDate(dto.getStart(), DateUtil.PATTERN_YYYYMMDD))).append(" ");
        StringBuilder countSql = new StringBuilder("select count(*) from ( ");
        List<Object> argsList = Lists.newArrayList();
        String whereSql = getWhereSqlCountByTargetId(puid,dto,argsList, false);
        selectSql.append(whereSql);

        StringBuilder sql = new StringBuilder("");
        sql.append("select ");
        sql.append("t.query,t.target_id,t.main_image,t.targeting_expression,t.targeting_type,t.ad_group_id,t.ad_group_name,t.campaign_id,t.campaign_name,");
        sql.append("t.impressions,t.clicks,t.cost,t.sale_num,t.ad_order_num,t.total_sales");
        sql.append(" from ( ");
        sql.append(selectSql);
        sql.append(") t left join t_amazon_ad_targeting w on t.target_id = w.target_id ");
        sql.append(" where w.shop_id=? ");
        argsList.add(dto.getShopId());

        if (StringUtils.isNotBlank(dto.getState())) {
            sql.append(" and w.state = ? ");
            argsList.add(dto.getState());
        }
        if(StringUtils.isNotBlank(dto.getOrderField()) && StringUtils.isNotBlank(dto.getOrderValue())){
            String orderField = ReportService.getOrderField(dto.getOrderField(),false);
            if(StringUtils.isNotBlank(orderField)){
                sql.append(" order by ").append(orderField);
                if("desc".equals(dto.getOrderValue())){
                    sql.append(" desc");
                }
                sql.append(" , query desc ");
            }
        }

        countSql.append(sql).append(") c");

        Object[] args = argsList.toArray();
        return this.getPageResult(puid, page.getPageNo(), page.getPageSize(), countSql.toString(), args, sql.toString(), args,CpcQueryTargetingReport.class);

    }

    @Override
    public Page pageManageList(Integer puid, CpcQueryWordDto dto, Page page) {
        //订单量字段展示修改为 sale_num 更为准确
        StringBuilder sql = new StringBuilder("SELECT `query`,target_id,main_image,targeting_expression,targeting_type,ad_group_id,ad_group_name,campaign_id,campaign_name,")
                .append("SUM(impressions) impressions,SUM(clicks) clicks,SUM(cost) cost,SUM(sale_num) sale_num,SUM(ad_order_num) ad_order_num,")
                .append("SUM(total_sales) total_sales, SUM(ad_sales)  `ad_sales`,SUM(ad_sale_num)  `ad_sale_num`, ")
                .append("SUM(order_num) order_num FROM ");
        sql.append(getTableNameByStartDate(DateUtil.strToDate(dto.getStart(), DateUtil.PATTERN_YYYYMMDD))).append(" ");
        StringBuilder countSql = new StringBuilder("select count(*) from ( ");
        List<Object> argsList = Lists.newArrayList();
        String whereSql = getWhereSqlCountByTargetId(puid,dto,argsList, true);
        sql.append(whereSql);

        if(StringUtils.isNotBlank(dto.getOrderField()) && StringUtils.isNotBlank(dto.getOrderValue())){
            String orderField = ReportService.getOrderField(dto.getOrderField(),true);
            if(StringUtils.isNotBlank(orderField)){
                sql.append(" order by ").append(orderField);
                if("desc".equals(dto.getOrderValue())){
                    sql.append(" desc");
                }
                sql.append(" , query desc ");
            }
        }

        countSql.append(sql).append(") c");

        Object[] args = argsList.toArray();
        return this.getPageResult(puid, page.getPageNo(), page.getPageSize(), countSql.toString(), args, sql.toString(), args,CpcQueryTargetingReport.class);

    }

    @Override
    public CpcQueryTargetingReport sumReport(Integer puid, CpcQueryWordDto dto, Boolean type) {
        //订单量字段展示修改为 sale_num 更为准确
        StringBuilder selectSql = new StringBuilder("SELECT SUM(impressions) impressions,SUM(clicks) clicks,SUM(cost) cost,SUM(sale_num) sale_num,SUM(ad_order_num) ad_order_num,")
                .append("SUM(total_sales) total_sales FROM ");
        selectSql.append(getTableNameByStartDate(DateUtil.strToDate(dto.getStart(), DateUtil.PATTERN_YYYYMMDD))).append(" ");
        List<Object> argsList = Lists.newArrayList();
        String whereSql = getWhereSqlCountByTargetId(puid,dto,argsList, type);
        selectSql.append(whereSql);
        StringBuilder sumSql = new StringBuilder("select SUM(impressions) impressions,SUM(clicks) clicks,SUM(cost) cost,SUM(sale_num) sale_num,SUM(ad_order_num) ad_order_num,SUM(total_sales) total_sales from ( ")
                .append(selectSql).append(" ) t");
        Object[] args = argsList.toArray();
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).queryForObject(sumSql.toString(),args,getMapper());
        } finally {
            hintManager.close();
        }

    }

    @Override
    public Page detailList(Integer puid, CpcQueryWordDetailDto dto, Page page) {
        StringBuilder selectSql = new StringBuilder(" SELECT ");
        if("day".equals(dto.getDateType())){
            selectSql.append(" count_date,");
        }else if("week".equals(dto.getDateType())){
            selectSql.append(" concat(from_days(floor((to_days(`count_date`) - 2) / 7) * 7 + 2),'~',from_days(floor((to_days(`count_date`) - 2) / 7) * 7 + 8)) as count_date ,");
        }else if("month".equals(dto.getDateType())){
            selectSql.append(" substring(count_date, 1, 6) as count_date,");
        }else{
            return page;
        }

        if("day".equals(dto.getDateType()) && StringUtils.isNotEmpty(dto.getTargetId()) && StringUtils.isNotEmpty(dto.getQuery())){
            selectSql.append("impressions,clicks,cost,sale_num as sale_num,ad_order_num,total_sales ");
        }else{
            selectSql.append("SUM(impressions) impressions,SUM(clicks) clicks,SUM(cost) cost,SUM(sale_num) sale_num,SUM(ad_order_num) ad_order_num,SUM(total_sales) total_sales ");
        }
        String tableName = getTableNameByStartDate(DateUtil.strToDate(dto.getStart(), DateUtil.PATTERN_YYYYMMDD));
        selectSql.append(" FROM ").append(tableName).append(" ");
        StringBuilder countSql = new StringBuilder("select count(*) from ( select 1 FROM ").append(tableName).append(" ");
        StringBuilder whereSql = new StringBuilder(" where puid=? and shop_id=? and marketplace_id=? and count_date>=? and count_date<=? ");
        List<Object> argsList = Lists.newArrayList();
        argsList.add(puid);
        argsList.add(dto.getShopId());
        argsList.add(dto.getMarketplaceId());
        argsList.add(dto.getStart());
        argsList.add(dto.getEnd());
        if(StringUtils.isNotEmpty(dto.getTargetId()) && StringUtils.isNotEmpty(dto.getQuery())){
            whereSql.append(" and target_id=? and `query`=? ");
            argsList.add(dto.getTargetId());
            argsList.add(dto.getQuery());
        }
        if("day".equals(dto.getDateType())){
            whereSql.append(" group by count_date ");
        }else if("week".equals(dto.getDateType())){
            whereSql.append(" group by concat(from_days(floor((to_days(`count_date`) - 2) / 7) * 7 + 2),'~',from_days(floor((to_days(`count_date`) - 2) / 7) * 7 + 8)) ");
        }else if("month".equals(dto.getDateType())){
            whereSql.append(" group by substring(count_date, 1, 6) ");
        }
        selectSql.append(whereSql);
        countSql.append(whereSql).append(") t");

        if(StringUtils.isNotBlank(dto.getOrderField()) && StringUtils.isNotBlank(dto.getOrderValue())){
            String orderField = ReportService.getOrderField(dto.getOrderField(),true);
            if(StringUtils.isNotBlank(orderField)){
                selectSql.append(" order by ").append(orderField);
                if("desc".equals(dto.getOrderValue())){
                    selectSql.append(" desc");
                }
            }
        }
        Object[] args = argsList.toArray();
        return this.getPageResult(puid, page.getPageNo(), page.getPageSize(), countSql.toString(), args, selectSql.toString(), args,CpcQueryTargetingReport.class);
    }

    @Override
    public CpcQueryTargetingReport sumDetailReport(Integer puid, CpcQueryWordDetailDto dto) {
        StringBuilder selectSql = new StringBuilder(" SELECT SUM(impressions) impressions,SUM(clicks) clicks,SUM(cost) cost,SUM(sale_num) sale_num,SUM(ad_order_num) ad_order_num,SUM(total_sales) total_sales FROM ");
        selectSql.append(getTableNameByStartDate(DateUtil.strToDate(dto.getStart(), DateUtil.PATTERN_YYYYMMDD)));
        selectSql.append(" where puid=? and shop_id=? and marketplace_id=? and count_date>=? and count_date<=? ");
        List<Object> argsList = Lists.newArrayList();
        argsList.add(puid);
        argsList.add(dto.getShopId());
        argsList.add(dto.getMarketplaceId());
        argsList.add(dto.getStart());
        argsList.add(dto.getEnd());
        if(StringUtils.isNotEmpty(dto.getTargetId()) && StringUtils.isNotEmpty(dto.getQuery())){
            selectSql.append(" and target_id=? and `query`=? ");
            argsList.add(dto.getTargetId());
            argsList.add(dto.getQuery());
        }
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).queryForObject(selectSql.toString(),argsList.toArray(),getMapper());
        } finally {
            hintManager.close();
        }

    }

    @Override
    public List<CpcQueryTargetingReport> detailListChart(Integer puid, CpcQueryWordDetailDto dto) {
        StringBuilder selectSql = new StringBuilder(" SELECT ");
        if("day".equals(dto.getDateType())){
            selectSql.append(" count_date,");
        }else if("week".equals(dto.getDateType())){
            selectSql.append(" concat(from_days(floor((to_days(`count_date`) - 2) / 7) * 7 + 2),'~',from_days(floor((to_days(`count_date`) - 2) / 7) * 7 + 8)) as count_date ,");
        }else if("month".equals(dto.getDateType())){
            selectSql.append(" substring(count_date, 1, 6) as count_date,");
        }else{
            return null;
        }

        if("day".equals(dto.getDateType()) && StringUtils.isNotEmpty(dto.getTargetId()) && StringUtils.isNotEmpty(dto.getQuery())){
            selectSql.append("impressions,clicks,cost,sale_num as sale_num,ad_order_num,total_sales  ");
        }else{
            selectSql.append("SUM(impressions) impressions,SUM(clicks) clicks,SUM(cost) cost,SUM(sale_num) sale_num,SUM(ad_order_num) ad_order_num,SUM(total_sales) total_sales ");
        }
        selectSql.append(" FROM ").append(getTableNameByStartDate(DateUtil.strToDate(dto.getStart(), DateUtil.PATTERN_YYYYMMDD))).append(" ");
        StringBuilder whereSql = new StringBuilder(" where puid=? and shop_id=? and marketplace_id=? and count_date>=? and count_date<=? ");
        List<Object> argsList = Lists.newArrayList();
        argsList.add(puid);
        argsList.add(dto.getShopId());
        argsList.add(dto.getMarketplaceId());
        argsList.add(dto.getStart());
        argsList.add(dto.getEnd());
        if(StringUtils.isNotEmpty(dto.getTargetId()) && StringUtils.isNotEmpty(dto.getQuery())){
            whereSql.append(" and target_id=? and `query`=? ");
            argsList.add(dto.getTargetId());
            argsList.add(dto.getQuery());
        }
        if("day".equals(dto.getDateType())){
            whereSql.append(" group by count_date ");
        }else if("week".equals(dto.getDateType())){
            whereSql.append(" group by concat(from_days(floor((to_days(`count_date`) - 2) / 7) * 7 + 2),'~',from_days(floor((to_days(`count_date`) - 2) / 7) * 7 + 8)) ");
        }else if("month".equals(dto.getDateType())){
            whereSql.append(" group by substring(count_date, 1, 6) ");
        }
        selectSql.append(whereSql).append(" order by count_date");
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(selectSql.toString(),argsList.toArray(),getMapper());
        } finally {
            hintManager.close();
        }

    }

    @Override
    public List<CpcQueryTargetingReport> listByPageParam(Integer puid, QueryWordPageParam param) {
        String sql = "SELECT id,puid,shop_id,marketplace_id,campaign_id,ad_group_id,target_id,targeting_expression,targeting_text,targeting_type," +
                "ad_group_name,campaign_name,`query`,is_asin, main_image, title, sum(`cost`) cost, sum(`cost_rmb`) cost_rmb, sum(`cost_usd`) cost_usd," +
                "sum(`total_sales`) total_sales, sum(`total_sales_rmb`) total_sales_rmb, sum(`total_sales_usd`) total_sales_usd," +
                "sum(`ad_sales`) ad_sales,sum(`ad_sales_rmb`) ad_sales_rmb,sum(`ad_sales_usd`) ad_sales_usd," +
                "sum(`impressions`) impressions, sum(`clicks`) clicks, sum(`sale_num`) order_num, sum(`ad_order_num`) ad_order_num, sum(`sale_num`) sale_num," +
                "sum(`ad_sale_num`) ad_sale_num " +
                "FROM " + getTableNameByStartDate(DateUtil.strToDate(param.getStartDate(), DateUtil.PATTERN_YYYYMMDD)) + " where ";

        ConditionBuilder.Builder conditionBuilder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", param.getShopId())
                .equalTo("campaign_id", param.getCampaignId())
                .equalTo("ad_group_id", param.getGroupId())
                .greaterThanOrEqualTo("count_date", param.getStartDate())
                .lessThanOrEqualTo("count_date", param.getEndDate());

        if (StringUtils.isNotBlank(param.getSearchField()) && StringUtils.isNotBlank(param.getSearchValue())) {
            if (StringUtils.isNotBlank(param.getSearchType()) && "exact".equalsIgnoreCase(param.getSearchType())) {  // asin看板跳转精确搜索
                conditionBuilder.equalTo("`query`", param.getSearchValue());
            } else {
                conditionBuilder.like("query", param.getSearchValue());
            }
        }

        conditionBuilder.groupBy("target_id", "`query`");

        ConditionBuilder condition = conditionBuilder.build();
        sql += condition.getSql();
        Object[] values = condition.getValues();
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(sql, getMapper(), values);
        } finally {
            hintManager.close();
        }

    }

    @Override
    public List<CpcQueryTargetingReport> listReports(Integer puid, Integer shopId, String startDate, String endDate, String targetId, String query) {
        String sql = "SELECT puid,shop_id,marketplace_id,count_date,target_id," +
                "sum(`cost`) cost, sum(`cost_rmb`) cost_rmb, sum(`cost_usd`) cost_usd," +
                "sum(`total_sales`) total_sales, sum(`total_sales_rmb`) total_sales_rmb, sum(`total_sales_usd`) total_sales_usd," +
                "sum(`ad_sales`) ad_sales,sum(`ad_sales_rmb`) ad_sales_rmb,sum(`ad_sales_usd`) ad_sales_usd," +
                "sum(`impressions`) impressions, sum(`clicks`) clicks, sum(`sale_num`) order_num, sum(`ad_order_num`) ad_order_num, sum(`sale_num`) sale_num," +
                "sum(`ad_sale_num`) ad_sale_num " +
                "FROM " + getTableNameByStartDate(DateUtil.strToDate(startDate, DateUtil.PATTERN_YYYYMMDD)) + " where ";

        ConditionBuilder conditionBuilder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .greaterThanOrEqualTo("count_date", startDate)
                .lessThanOrEqualTo("count_date", endDate)
                .equalTo("target_id", targetId)
                .equalTo("`query`", query)
                .groupBy("count_date")
                .build();

        sql += conditionBuilder.getSql();
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(sql, getMapper(), conditionBuilder.getValues());
        } finally {
            hintManager.close();
        }

    }

    @Override
    public List<CpcQueryTargetingReport> listNoAsinInfo(Integer puid, Integer shopId, String startDate, String endDate, String campaignId, long offset, int limit) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .greaterThanOrEqualTo("count_date", startDate)
                .lessThanOrEqualTo("count_date", endDate)
                .equalTo("is_asin", 1)
                .and()
                .leftBracket()
                .equalToWithoutCheck(LogicType.EPT, "main_image", "")
                .or()
                .equalToWithoutCheck(LogicType.EPT, "title", "")
                .rightBracket();

        if (StringUtils.isNotBlank(campaignId)) {
            builder.equalTo("campaign_id", campaignId);
        }
        builder.greaterThan("id", offset);
        builder.orderBy("create_time");
        builder.limit(limit);

        return listByCondition(puid, builder.build());
    }

    @Override
    public void batchSetAsinInfo(Integer puid, List<CpcQueryTargetingReport> needUpdateList) {
        if (CollectionUtils.isNotEmpty(needUpdateList)) {
            //更新总表
            batchSetAsinInfoOrigin(puid, needUpdateList);
            if (nacosConfiguration.isHotTableWritePhase2Enable()) {
                //更新hot表
                batchSetAsinInfoHot(puid, needUpdateList);
            }

        }
    }

    private void batchSetAsinInfoOrigin(Integer puid, List<CpcQueryTargetingReport> needUpdateList) {
        String sql = "update " + getJdbcHelper().getTable() + " set main_image=?, title=?, update_time=now(3) where id=? and puid = ? ";
        List<Object[]> argsList = new ArrayList<>();
        List<Object> args;
        for (CpcQueryTargetingReport report : needUpdateList) {
            args = new ArrayList<>(2);
            args.add(report.getMainImage() == null ? ""
                    : (report.getMainImage().length() > imageLimit ? report.getMainImage().substring(0, imageLimit) : report.getMainImage()));
            args.add(report.getTitle() == null ? ""
                    : (report.getTitle().length() > titleLimit ? report.getTitle().substring(0, titleLimit) : report.getTitle()));
            args.add(report.getId());
            args.add(puid);
            argsList.add(args.toArray());
        }
        HintManager hintManager = HintManager.getInstance();
        try {
            getJdbcTemplate(puid, hintManager).batchUpdate(sql, argsList);
        } finally {
            hintManager.close();
        }
    }

    private void batchSetAsinInfoHot(Integer puid, List<CpcQueryTargetingReport> needUpdateList) {
        StringBuilder sql = new StringBuilder("INSERT INTO ");
        sql.append(getHotTableName());
        sql.append(" (`puid`,`shop_id`,`marketplace_id`,`campaign_id`,")
                .append("`ad_group_id`,`target_id`,`count_date`,`targeting_expression`,`targeting_text`, `main_image`, `title`,")
                .append("`targeting_type`,`ad_group_name`,`campaign_name`,`query`,`query_cn`,`is_asin`,`cost`,`cost_rmb`, ")
                .append("`cost_usd`,`total_sales`,`total_sales_rmb`,`total_sales_usd`,`ad_sales`,`ad_sales_rmb`,")
                .append("`ad_sales_usd`,`impressions`,`clicks`,`order_num`,`ad_order_num`,`sale_num`,`ad_sale_num`,`query_id`,`create_time`,`update_time`) VALUES");
        List<Object> argsList = Lists.newArrayList();
        for (CpcQueryTargetingReport report : needUpdateList) {
            sql.append(" (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,now(),now()),");
            argsList.add(puid);
            argsList.add(report.getShopId());
            argsList.add(report.getMarketplaceId());
            argsList.add(report.getCampaignId());
            argsList.add(report.getAdGroupId());
            argsList.add(report.getTargetId());
            argsList.add(report.getCountDate());
            argsList.add(report.getTargetingExpression());
            argsList.add(report.getTargetingText());
            argsList.add(report.getMainImage() == null ? ""
                    : (report.getMainImage().length() > imageLimit ? report.getMainImage().substring(0, imageLimit) : report.getMainImage()));
            argsList.add(report.getTitle() == null ? ""
                    : (report.getTitle().length() > titleLimit ? report.getTitle().substring(0, titleLimit) : report.getTitle()));
            argsList.add(report.getTargetingType());
            argsList.add(report.getAdGroupName());
            argsList.add(report.getCampaignName());
            argsList.add(report.getQuery());
            argsList.add(report.getQueryCn());
            argsList.add(report.getIsAsin());
            argsList.add(report.getCost());
            argsList.add(report.getCostRmb());
            argsList.add(report.getCostUsd());
            argsList.add(report.getTotalSales());
            argsList.add(report.getTotalSalesRmb());
            argsList.add(report.getTotalSalesUsd());
            argsList.add(report.getAdSales());
            argsList.add(report.getAdSalesRmb());
            argsList.add(report.getAdSalesUsd());
            argsList.add(report.getImpressions());
            argsList.add(report.getClicks());
            argsList.add(report.getOrderNum());
            argsList.add(report.getAdOrderNum());
            argsList.add(report.getSaleNum());
            argsList.add(report.getAdSaleNum());
            argsList.add(report.getQueryId());
        }
        sql.deleteCharAt(sql.length() - 1);
        sql.append("  on duplicate key update `main_image`=values(main_image), `title`=values(title), `update_time`=now(3)");

        HintManager hintManager = HintManager.getInstance();
        try {
            getJdbcTemplate(puid, hintManager).update(sql.toString(), argsList.toArray());
        } finally {
            hintManager.close();
        }
    }

    @Override
    public List<AdHomePerformancedto> listAllTargetingReportByDate(Integer puid, CpcQueryWordDto dto) {
        StringBuilder selectSql = new StringBuilder("select target_id, count_date, sum(impressions) impressions, sum(clicks) clicks,sum(cost) cost,sum(sale_num) sale_num, sum(ad_order_num) ad_order_num,")
                .append("sum(total_sales) total_sales, sum(ad_sales)  `ad_sales`,sum(ad_sale_num)  `ad_sale_num`, ")
                .append("sum(order_num) order_num FROM ")
                .append(getTableNameByStartDate(DateUtil.strToDate(dto.getStart(), DateUtil.PATTERN_YYYYMMDD)))
                .append(" ");
        List<Object> argsList = Lists.newArrayList();
        String whereSql = getWhereSqlCountByDate(puid,dto,argsList);
        selectSql.append(whereSql);
        Object[] args = argsList.toArray();

        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(selectSql.toString(), new RowMapper<AdHomePerformancedto>() {
                @Override
                public AdHomePerformancedto mapRow(ResultSet re, int i) throws SQLException {
                    return  AdHomePerformancedto.builder()
                            .targetId(re.getString("target_id"))
                            .adCost(Optional.ofNullable(re.getBigDecimal("cost")).orElse(BigDecimal.ZERO))
                            .adOrderNum(Optional.ofNullable(re.getInt("sale_num")).orElse(0))
                            .adSale(Optional.ofNullable(re.getBigDecimal("total_sales")).orElse(BigDecimal.ZERO))
                            .clicks(Optional.ofNullable(re.getInt("clicks")).orElse(0))
                            .impressions(Optional.ofNullable(re.getInt("impressions")).orElse(0))
                            .countDate(re.getString("count_date"))
                            /**
                             * TODO 广告报告重构
                             * 本广告产品订单量
                             */
                            .adSaleNum(Optional.ofNullable(re.getInt("ad_sale_num")).orElse(0))
                            //本广告产品销售额
                            .adSales(Optional.ofNullable(re.getBigDecimal("ad_sales")).orElse(BigDecimal.ZERO))
                            //广告销量
                            .salesNum(Optional.ofNullable(re.getInt("order_num")).orElse(0))
                            //本广告产品销量
                            .orderNum(Optional.ofNullable(re.getInt("ad_order_num")).orElse(0))
                            .build();
                }
            }, args);
        } finally {
            hintManager.close();
        }

    }

    @Override
    public List<AdHomePerformancedto> getReportByTargetIdList(Integer puid, Integer shopId, String startStr, String endStr, List<String> targetIdList,
                                                              CpcQueryWordDto dto) {
        if (CollectionUtils.isEmpty(targetIdList)) {
            return new ArrayList<>();
        }
        List<Object> argsList = new ArrayList<>();
        //按天聚合
        StringBuilder sql = new StringBuilder(" select  count_date, sum(impressions) impressions, sum(clicks) clicks,sum(cost) cost,sum(sale_num) sale_num, sum(ad_order_num) ad_order_num,");
        sql.append(" sum(total_sales) total_sales, sum(ad_sales)  `ad_sales`,sum(ad_sale_num)  `ad_sale_num`, ");
        sql.append(" sum(order_num) order_num from " + getTableNameByStartDate(DateUtil.strToDate(startStr, DateUtil.PATTERN_YYYYMMDD)) + " where puid= ? and shop_id= ? ");

        argsList.add(puid);
        argsList.add(shopId);

        sql.append(SqlStringUtil.dealInList("target_id", targetIdList, argsList));
        sql.append(" and query REGEXP '" + ASIN_REGEX + "' ");

        if(StringUtils.isNotBlank(dto.getSearchField()) && StringUtils.isNotBlank(dto.getSearchValue())){
            String field = SqlStringUtil.getSqlField(CpcQueryTargetingReport.class,dto.getSearchField());
            if(StringUtils.isNotEmpty(field)){
                if("blur".equals(dto.getSearchType())){ //模糊搜索
                    sql.append(" and ").append(field).append(" like ?");
                    argsList.add("%"+SqlStringUtil.dealLikeSql(dto.getSearchValue())+"%");
                }else{//默认精确
                    sql.append(" and ").append(field).append(" = ?");
                    argsList.add(dto.getSearchValue());
                }
            }
        }

        sql.append("  and count_date >= ? and count_date <= ? group by count_date ");
        argsList.add(startStr);
        argsList.add(endStr);

        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(sql.toString(), new RowMapper<AdHomePerformancedto>() {
                @Override
                public AdHomePerformancedto mapRow(ResultSet re, int i) throws SQLException {
                    AdHomePerformancedto dto = AdHomePerformancedto.builder()
                            .adCost(Optional.ofNullable(re.getBigDecimal("cost")).orElse(BigDecimal.ZERO))
                            .adOrderNum(Optional.ofNullable(re.getInt("sale_num")).orElse(0))  //销量字段订单
                            .adSale(Optional.ofNullable(re.getBigDecimal("total_sales")).orElse(BigDecimal.ZERO))
                            .clicks(Optional.ofNullable(re.getInt("clicks")).orElse(0))
                            .impressions(Optional.ofNullable(re.getInt("impressions")).orElse(0))
                            .countDate(re.getString("count_date"))
                            /**
                             * TODO 广告报告重构
                             * 本广告产品订单量
                             */
                            .adSaleNum(Optional.ofNullable(re.getInt("ad_sale_num")).orElse(0))
                            //本广告产品销售额
                            .adSales(Optional.ofNullable(re.getBigDecimal("ad_sales")).orElse(BigDecimal.ZERO))
                            //广告销量
                            .salesNum(Optional.ofNullable(re.getInt("order_num")).orElse(0))
                            //本广告产品销量
                            .orderNum(Optional.ofNullable(re.getInt("ad_order_num")).orElse(0))
                            .build();
                    return dto;
                }
            }, argsList.toArray());
        } finally {
            hintManager.close();
        }
    }

    @Override
    public List<CpcQueryTargetingReport> getReportVoListByGroupIds(Integer puid, List<String> spGroupIds, QueryReportSearchVo searchVo) {
        String sql = "SELECT campaign_id,ad_group_id,campaign_name,ad_group_name,target_id,shop_id,marketplace_id,`query`,sum(`cost`) cost,targeting_text,main_image,title, " +
                " sum(`total_sales`) total_sales, " +
                " sum(`impressions`) impressions, sum(`clicks`) clicks, sum(`sale_num`) sale_num " +
                " FROM " + getTableNameByStartDate(searchVo.getStart()) + " where ";

        ConditionBuilder conditionBuilder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", searchVo.getShopId())
                .greaterThanOrEqualTo("count_date", DateUtil.dateToStrWithFormat(searchVo.getStart(),"yyyyMMdd"))
                .lessThanOrEqualTo("count_date", DateUtil.dateToStrWithFormat(searchVo.getEnd(),"yyyyMMdd"))
                .inStrList("ad_group_id", spGroupIds.toArray(new String[]{}))
                .groupBy("target_id","query")
                .build();

        sql += conditionBuilder.getSql();
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(sql, getMapper(), conditionBuilder.getValues());
        } finally {
            hintManager.close();
        }

    }

    @Override
    public List<CpcQueryTargetingReport> getListByTargetId(Integer puid, String targetId, TargetQuerySearchVo searchVo) {
        String sql = "SELECT campaign_id,ad_group_id,campaign_name,ad_group_name,target_id,shop_id,marketplace_id,`query`,sum(`cost`) cost,targeting_text,main_image,title," +
                " sum(`total_sales`) total_sales, " +
                " sum(`impressions`) impressions, sum(`clicks`) clicks, sum(`sale_num`) sale_num " +
                " FROM " + getTableNameByStartDate(searchVo.getStart()) + " where ";

        ConditionBuilder conditionBuilder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", searchVo.getShopId())
                .greaterThanOrEqualTo("count_date", DateUtil.dateToStrWithFormat(searchVo.getStart(),"yyyyMMdd"))
                .lessThanOrEqualTo("count_date", DateUtil.dateToStrWithFormat(searchVo.getEnd(),"yyyyMMdd"))
                .equalTo("target_id", targetId)
                .groupBy("query")
                .build();

        sql += conditionBuilder.getSql();
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(sql, getMapper(), conditionBuilder.getValues());
        } finally {
            hintManager.close();
        }

    }

    @Override
    public CpcQueryTargetingReport getDetailsSumVo(Integer puid, QueryReportDetailsVo detailsVo) {
        StringBuilder selectSql = new StringBuilder("SELECT shop_id,marketplace_id, ")
                .append("sum(`impressions`) impressions,sum(`clicks`) clicks,sum(`sale_num`) sale_num,sum(`cost`) cost,")
                .append("sum(`total_sales`) total_sales  FROM ")
                .append(getTableNameByStartDate(DateUtil.strToDate(detailsVo.getStartDate(), DateUtil.PATTERN_YYYYMMDD)))
                .append(" ");

        List<Object> argsList = Lists.newArrayList();
        StringBuilder whereSql = new StringBuilder(" where puid=? and shop_id=? and target_id= ? and `query`=?  ");
        argsList.add(puid);
        argsList.add(detailsVo.getShopId());
        argsList.add(detailsVo.getTargetId());
        argsList.add(detailsVo.getQuery());

        whereSql.append("and count_date>=? and count_date<=?   ");
        argsList.add(detailsVo.getStartDate());
        argsList.add(detailsVo.getEndDate());
        selectSql.append(whereSql);

        HintManager hintManager = HintManager.getInstance();
        try {
            List<CpcQueryTargetingReport> reportList = getJdbcTemplate(puid, hintManager).query(selectSql.toString(), argsList.toArray(), getMapper());
            return CollectionUtils.isNotEmpty(reportList) ? reportList.get(0) : null;
        } finally {
            hintManager.close();
        }

    }

    @Override
    public List<CpcQueryTargetingReport> getListQueryDetailsDay(Integer puid, QueryReportDetailsVo detailsVo) {
        StringBuilder selectSql = new StringBuilder("SELECT shop_id,marketplace_id,count_date, ")
                .append("sum(`impressions`) impressions,sum(`clicks`) clicks,sum(`sale_num`) sale_num,sum(`cost`) cost,")
                .append("sum(`total_sales`) total_sales  FROM ")
                .append(getTableNameByStartDate(detailsVo.getStart()))
                .append(" ");

        List<Object> argsList = Lists.newArrayList();
        StringBuilder whereSql = new StringBuilder(" where puid=? and shop_id=? and target_id= ? and `query`=? ");
        argsList.add(puid);
        argsList.add(detailsVo.getShopId());
        argsList.add(detailsVo.getTargetId());
        argsList.add(detailsVo.getQuery());
        whereSql.append("and count_date>=? and count_date<=? group by count_date");
        argsList.add(DateUtil.dateToStrWithFormat(detailsVo.getStart(),"yyyyMMdd"));
        argsList.add(DateUtil.dateToStrWithFormat(detailsVo.getEnd(),"yyyyMMdd"));
        selectSql.append(whereSql);

        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(selectSql.toString(), argsList.toArray(), getMapper());
        } finally {
            hintManager.close();
        }

    }

    @Override
    public List<CpcQueryTargetingReport> getReportTargetByDate(Integer puid, Integer shopId, String start, String end) {
        String sql = "select * from " + getTableNameByStartDate(DateUtil.strToDate(start, DateUtil.PATTERN_YYYYMMDD)) + " where puid= ? and shop_id=? and `count_date` >= ? and `count_date` <= ? ";
        List<Object> args = Lists.newArrayList();
        args.add(puid);
        args.add(shopId);
        args.add(start);
        args.add(end);

        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(sql,args.toArray(),getMapper());
        } finally {
            hintManager.close();
        }

    }

    @Override
    public List<CpcQueryTargetingReport> getNeedsLocalizationKeywords(Integer puid, Integer shopId, Integer limit) {
        return listByCondition(puid, new ConditionBuilder.Builder().equalTo("puid", puid)
                .equalTo("shop_id", shopId).equalTo("is_asin", 0)
                .equalToWithoutCheck("query_cn", "").limit(limit).build());
    }

    @Override
    public int updateQueryCnById(Integer puid, CpcQueryTargetingReport report) {
        //更新总表
        int i = updateQueryCnByIdOrigin(puid, report);
        if (nacosConfiguration.isHotTableWritePhase2Enable()) {
            //更新hot表
            updateQueryCnByIdHot(puid, report);
        }
        return i;
    }

    private int updateQueryCnByIdOrigin(Integer puid, CpcQueryTargetingReport report) {
        StringBuilder sql = new StringBuilder("update t_cpc_query_targeting_report " +
                "set query_cn = ?,update_time = NOW(3)  where id = ? and puid = ? and shop_id = ?");
        List<Object> args = Lists.newArrayList(report.getQueryCn(), report.getId(), report.getPuid(),
                report.getShopId());

        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).update(sql.toString(), args.toArray());
        } finally {
            hintManager.close();
        }
    }

    private void updateQueryCnByIdHot(Integer puid, CpcQueryTargetingReport report) {
        StringBuilder sql = new StringBuilder("INSERT INTO ");
        sql.append(getHotTableName());
        sql.append(" (`puid`,`shop_id`,`count_date`,`target_id`,`query`,`query_cn`, `update_time`) values ");
        List<Object> argsList = Lists.newArrayList();
        sql.append(" (?, ?, ?, ?, ?, ?, now(3)) ");
        argsList.add(puid);
        argsList.add(report.getShopId());
        argsList.add(report.getCountDate());
        argsList.add(report.getTargetId());
        argsList.add(report.getQuery());
        argsList.add(report.getQueryCn());

        sql.append(" on duplicate key update `query_cn`=values(query_cn), `update_time`=now(3)");
        HintManager hintManager = HintManager.getInstance();
        try {
            getJdbcTemplate(puid, hintManager).update(sql.toString(), argsList.toArray());
        } finally {
            hintManager.close();
        }
    }

    private String getWhereSqlCountByTargetId(int puid, CpcQueryWordDto dto, List<Object> argsList, boolean type) {
        StringBuilder whereSql = new StringBuilder(" where puid=? and shop_id=? and marketplace_id=? and count_date>=? and count_date<=? ");
        argsList.add(puid);
        argsList.add(dto.getShopId());
        argsList.add(dto.getMarketplaceId());
        argsList.add(dto.getStart());
        argsList.add(dto.getEnd());
        if(StringUtils.isNotBlank(dto.getCampaignId())){
            List<String> list = StringUtil.splitStr(dto.getCampaignId());
            whereSql.append(SqlStringUtil.dealInList("campaign_id", list, argsList));
        }
        if (CollectionUtils.isNotEmpty(dto.getCampaignIdList())) { //广告组合查询
            whereSql.append(SqlStringUtil.dealInList("campaign_id", dto.getCampaignIdList(), argsList));
        }
        if(StringUtils.isNotBlank(dto.getGroupId())){
            List<String> list = StringUtil.splitStr(dto.getGroupId());
            whereSql.append(SqlStringUtil.dealInList("ad_group_id", list, argsList));
        }
        if (type) {  // 查询手动类型的广告或自动类型的 商品类型
            whereSql.append(" and (targeting_type = 'TARGETING_EXPRESSION' or targeting_expression in ('substitutes','complements')) ");
        }
        if(StringUtils.isNotBlank(dto.getSearchField()) && StringUtils.isNotBlank(dto.getSearchValue())){
            String field = SqlStringUtil.getSqlField(CpcQueryTargetingReport.class,dto.getSearchField());
            if(StringUtils.isNotEmpty(field)){

                if("blur".equals(dto.getSearchType())){ //模糊搜索
                    if(dto.getSearchVelueList().size() > 1){
                        whereSql.append(" and (").append(SqlStringUtil.dealLikeListOr(field,dto.getSearchVelueList(),argsList,false)).append(")");
                    } else {
                        whereSql.append(" and ").append(field).append(" like ?");
                        argsList.add("%"+SqlStringUtil.dealLikeSql(dto.getSearchVelueList().get(0))+"%");
                    }

                }else{//默认精确
                    if(dto.getSearchVelueList().size() > 1){
                        whereSql.append(SqlStringUtil.dealInList(field,dto.getSearchVelueList(),argsList));
                    } else {
                        whereSql.append(" and ").append(field).append(" = ?");
                        argsList.add(dto.getSearchVelueList().get(0));
                    }
                }
            }
        }
        whereSql.append("group by target_id,`query` ");
        if(dto.getUseAdvanced()){
            BigDecimal shopSales = dto.getShopSales() != null ? dto.getShopSales() : BigDecimal.valueOf(0);

            whereSql.append(" having 1=1 ");
            //展示量
            if(dto.getImpressionsMin() != null){
                whereSql.append(" and impressions >= ?");
                argsList.add(dto.getImpressionsMin());
            }
            if(dto.getImpressionsMax() != null){
                whereSql.append(" and impressions <= ?");
                argsList.add(dto.getImpressionsMax());
            }
            //点击量
            if(dto.getClicksMin() != null){
                whereSql.append(" and clicks >= ?");
                argsList.add(dto.getClicksMin());
            }
            if(dto.getClicksMax() != null){
                whereSql.append(" and clicks <= ?");
                argsList.add(dto.getClicksMax());
            }
            //点击率（clicks/impressions）
            if(dto.getClickRateMin() != null){
                whereSql.append(" and ROUND(ifnull(clicks/impressions,0),4) >= ?");
                argsList.add(dto.getClickRateMin());
            }
            if(dto.getClickRateMax() != null){
                whereSql.append(" and ROUND(ifnull(clicks/impressions,0),4) <= ?");
                argsList.add(dto.getClickRateMax());
            }
            //花费
            if(dto.getCostMin() != null){
                whereSql.append(" and cost >= ?");
                argsList.add(dto.getCostMin());
            }
            if(dto.getCostMax() != null){
                whereSql.append(" and cost <= ?");
                argsList.add(dto.getCostMax());
            }
            //cpc  平均点击费用
            if(dto.getCpcMin() != null){
                whereSql.append(" and ROUND(ifnull(cost/clicks,0),2) >= ?");
                argsList.add(dto.getCpcMin());
            }
            if(dto.getCpcMax() != null){
                whereSql.append(" and ROUND(ifnull(cost/clicks,0),2) <= ?");
                argsList.add(dto.getCpcMax());
            }
            //广告订单量
            if(dto.getOrderNumMin() != null){
                whereSql.append(" and sale_num >= ?");
                argsList.add(dto.getOrderNumMin());
            }
            if(dto.getOrderNumMax() != null){
                whereSql.append(" and sale_num <= ?");
                argsList.add(dto.getOrderNumMax());
            }
            //广告销售额
            if(dto.getSalesMin() != null){
                whereSql.append(" and total_sales >= ?");
                argsList.add(dto.getSalesMin());
            }
            if(dto.getSalesMax() != null){
                whereSql.append(" and total_sales <= ?");
                argsList.add(dto.getSalesMax());
            }
            //订单转化率
            if(dto.getSalesConversionRateMin() != null){
                whereSql.append(" and ROUND(ifnull(sale_num/clicks,0),4) >= ?");
                argsList.add(dto.getSalesConversionRateMin());
            }
            if(dto.getSalesConversionRateMax() != null){
                whereSql.append(" and ROUND(ifnull(sale_num/clicks,0),4) <= ?");
                argsList.add(dto.getSalesConversionRateMax());
            }
            //acos
            if(dto.getAcosMin() != null){
                whereSql.append(" and ROUND(ifnull(cost/total_sales,0),4) >= ?");
                argsList.add(dto.getAcosMin());
            }
            if(dto.getAcosMax() != null){
                whereSql.append(" and ROUND(ifnull(cost/total_sales,0),4) <= ?");
                argsList.add(dto.getAcosMax());
            }
            // roas
            if (dto.getRoasMin() != null) {
                whereSql.append(" and ROUND(ifnull(total_sales/cost,0),2) >= ?");
                argsList.add(dto.getRoasMin());
            }
            // roas
            if (dto.getRoasMax() != null) {
                whereSql.append(" and ROUND(ifnull(total_sales/cost,0),2) <= ?");
                argsList.add(dto.getRoasMax());
            }
            // acots  需要乘以店铺销售额
            if (dto.getAcotsMin() != null) {
                if (shopSales.doubleValue() > 0) {
                    whereSql.append(" and ROUND((ifnull(cost,0) / ").append(shopSales).append(" ),4) >= ? ");
                    argsList.add(dto.getAcotsMin());
                } else {
                    whereSql.append(" and 0 >= ? ");
                    argsList.add(dto.getAcotsMin());
                }
            }
            // acots  需要乘以店铺销售额
            if (dto.getAcotsMax() != null) {
                if (shopSales.doubleValue() > 0) {
                    whereSql.append(" and ROUND((ifnull(cost,0) / ").append(shopSales).append(" ),4) <= ? ");
                    argsList.add(dto.getAcotsMax());
                } else {
                    whereSql.append(" and 0 <= ? ");
                    argsList.add(dto.getAcotsMin());
                }
            }
            // asots 需要乘以店铺销售额
            if (dto.getAsotsMin() != null) {
                if (shopSales.doubleValue() > 0) {
                    whereSql.append(" and ROUND((ifnull(total_sales,0) / ").append(shopSales).append(" ),4) >= ? ");
                    argsList.add(dto.getAsotsMin());
                } else {
                    whereSql.append(" and 0 >= ? ");
                    argsList.add(dto.getAcotsMin());
                }
            }
            // asots  需要乘以店铺销售额
            if (dto.getAsotsMax() != null) {
                if (shopSales.doubleValue() > 0) {
                    whereSql.append(" and ROUND((ifnull(total_sales,0) / ").append(shopSales).append(" ),4) <= ? ");
                    argsList.add(dto.getAsotsMax());
                } else {
                    whereSql.append(" and 0 <= ? ");
                    argsList.add(dto.getAcotsMin());
                }
            }
        }
        return whereSql.toString();
    }




    private String getWhereSqlCountByDate(int puid, CpcQueryWordDto dto, List<Object> argsList) {
        StringBuilder whereSql = new StringBuilder(" where puid=? and shop_id=? and count_date>=? and count_date<=? ");
        argsList.add(puid);
        argsList.add(dto.getShopId());
        argsList.add(dto.getStart());
        argsList.add(dto.getEnd());
        if(StringUtils.isNotBlank(dto.getCampaignId())){
            List<String> list = StringUtil.splitStr(dto.getCampaignId());
            whereSql.append(SqlStringUtil.dealInList("campaign_id", list, argsList));
        }
        if (CollectionUtils.isNotEmpty(dto.getCampaignIdList())) { //广告组合查询
            whereSql.append(SqlStringUtil.dealInList("campaign_id", dto.getCampaignIdList(), argsList));
        }
        if(StringUtils.isNotBlank(dto.getGroupId())){
            List<String> list = StringUtil.splitStr(dto.getGroupId());
            whereSql.append(SqlStringUtil.dealInList("ad_group_id", list, argsList));
        }
        if (StringUtils.isNotBlank(dto.getState())) {
            List<String> statusList = StringUtil.splitStr(dto.getState(), ",");
            whereSql.append(SqlStringUtil.dealInList("state", statusList, argsList));
        }

        whereSql.append(" and (targeting_type = 'TARGETING_EXPRESSION' or targeting_expression in ('substitutes','complements')) ");

        if(StringUtils.isNotBlank(dto.getSearchField()) && StringUtils.isNotBlank(dto.getSearchValue())){
            String field = SqlStringUtil.getSqlField(CpcQueryTargetingReport.class,dto.getSearchField());
            if(StringUtils.isNotEmpty(field)){
                if("blur".equals(dto.getSearchType())){ //模糊搜索
                    whereSql.append(" and ").append(field).append(" like ?");
                    argsList.add("%"+SqlStringUtil.dealLikeSql(dto.getSearchValue())+"%");
                }else{//默认精确
                    whereSql.append(" and ").append(field).append(" = ?");
                    argsList.add(dto.getSearchValue());
                }
            }
        }
        whereSql.append("group by target_id,`query` ");
        if(dto.getUseAdvanced()){
            BigDecimal shopSales = dto.getShopSales() != null ? dto.getShopSales() : BigDecimal.valueOf(0);

            whereSql.append(" having 1=1 ");
            //展示量
            if(dto.getImpressionsMin() != null){
                whereSql.append(" and impressions >= ?");
                argsList.add(dto.getImpressionsMin());
            }
            if(dto.getImpressionsMax() != null){
                whereSql.append(" and impressions <= ?");
                argsList.add(dto.getImpressionsMax());
            }
            //点击量
            if(dto.getClicksMin() != null){
                whereSql.append(" and clicks >= ?");
                argsList.add(dto.getClicksMin());
            }
            if(dto.getClicksMax() != null){
                whereSql.append(" and clicks <= ?");
                argsList.add(dto.getClicksMax());
            }
            //点击率（clicks/impressions）
            if(dto.getClickRateMin() != null){
                whereSql.append(" and ROUND(ifnull(clicks/impressions,0),4) >= ?");
                argsList.add(dto.getClickRateMin());
            }
            if(dto.getClickRateMax() != null){
                whereSql.append(" and ROUND(ifnull(clicks/impressions,0),4) <= ?");
                argsList.add(dto.getClickRateMax());
            }
            //花费
            if(dto.getCostMin() != null){
                whereSql.append(" and cost >= ?");
                argsList.add(dto.getCostMin());
            }
            if(dto.getCostMax() != null){
                whereSql.append(" and cost <= ?");
                argsList.add(dto.getCostMax());
            }
            //cpc  平均点击费用
            if(dto.getCpcMin() != null){
                whereSql.append(" and ROUND(ifnull(cost/clicks,0),2) >= ?");
                argsList.add(dto.getCpcMin());
            }
            if(dto.getCpcMax() != null){
                whereSql.append(" and ROUND(ifnull(cost/clicks,0),2) <= ?");
                argsList.add(dto.getCpcMax());
            }
            //广告订单量
            if(dto.getOrderNumMin() != null){
                whereSql.append(" and sale_num >= ?");
                argsList.add(dto.getOrderNumMin());
            }
            if(dto.getOrderNumMax() != null){
                whereSql.append(" and sale_num <= ?");
                argsList.add(dto.getOrderNumMax());
            }
            //广告销售额
            if(dto.getSalesMin() != null){
                whereSql.append(" and total_sales >= ?");
                argsList.add(dto.getSalesMin());
            }
            if(dto.getSalesMax() != null){
                whereSql.append(" and total_sales <= ?");
                argsList.add(dto.getSalesMax());
            }
            //订单转化率
            if(dto.getSalesConversionRateMin() != null){
                whereSql.append(" and ROUND(ifnull(sale_num/clicks,0),4) >= ?");
                argsList.add(dto.getSalesConversionRateMin());
            }
            if(dto.getSalesConversionRateMax() != null){
                whereSql.append(" and ROUND(ifnull(sale_num/clicks,0),4) <= ?");
                argsList.add(dto.getSalesConversionRateMax());
            }
            //acos
            if(dto.getAcosMin() != null){
                whereSql.append(" and ROUND(ifnull(cost/total_sales,0),4) >= ?");
                argsList.add(dto.getAcosMin());
            }
            if(dto.getAcosMax() != null){
                whereSql.append(" and ROUND(ifnull(cost/total_sales,0),4) <= ?");
                argsList.add(dto.getAcosMax());
            }
            // roas
            if (dto.getRoasMin() != null) {
                whereSql.append(" and ROUND(ifnull(total_sales/cost,0),2) >= ?");
                argsList.add(dto.getRoasMin());
            }
            // roas
            if (dto.getRoasMax() != null) {
                whereSql.append(" and ROUND(ifnull(total_sales/cost,0),2) <= ?");
                argsList.add(dto.getRoasMax());
            }
            // acots  需要乘以店铺销售额
            if (dto.getAcotsMin() != null) {
                if (shopSales.doubleValue() > 0) {
                    whereSql.append(" and ROUND((ifnull(cost,0) / ").append(shopSales).append(" ),4) >= ? ");
                    argsList.add(dto.getAcotsMin());
                } else {
                    whereSql.append(" and 0 >= ? ");
                    argsList.add(dto.getAcotsMin());
                }
            }
            // acots  需要乘以店铺销售额
            if (dto.getAcotsMax() != null) {
                if (shopSales.doubleValue() > 0) {
                    whereSql.append(" and ROUND((ifnull(cost,0) / ").append(shopSales).append(" ),4) <= ? ");
                    argsList.add(dto.getAcotsMax());
                } else {
                    whereSql.append(" and 0 <= ? ");
                    argsList.add(dto.getAcotsMin());
                }
            }
            // asots 需要乘以店铺销售额
            if (dto.getAsotsMin() != null) {
                if (shopSales.doubleValue() > 0) {
                    whereSql.append(" and ROUND((ifnull(total_sales,0) / ").append(shopSales).append(" ),4) >= ? ");
                    argsList.add(dto.getAsotsMin());
                } else {
                    whereSql.append(" and 0 >= ? ");
                    argsList.add(dto.getAcotsMin());
                }
            }
            // asots  需要乘以店铺销售额
            if (dto.getAsotsMax() != null) {
                if (shopSales.doubleValue() > 0) {
                    whereSql.append(" and ROUND((ifnull(total_sales,0) / ").append(shopSales).append(" ),4) <= ? ");
                    argsList.add(dto.getAsotsMax());
                } else {
                    whereSql.append(" and 0 <= ? ");
                    argsList.add(dto.getAcotsMin());
                }
            }
        }
        return whereSql.toString();
    }

    private String getWhereSqlCountByKeyword(int puid, CpcQueryWordDto dto, List<Object> argsList) {
        StringBuilder whereSql = new StringBuilder();

        whereSql.append(" where puid=? and shop_id=? and marketplace_id=? and count_date>=? and count_date<=? ");
        argsList.add(puid);
        argsList.add(dto.getShopId());
        argsList.add(dto.getMarketplaceId());
        argsList.add(dto.getStart());
        argsList.add(dto.getEnd());
        whereSql.append(" and query REGEXP '" + ASIN_REGEX + "' ");
        if (StringUtils.isNotBlank(dto.getFilterTargetType())) {
            List<String> stringList = StringUtil.splitStr(dto.getFilterTargetType()).stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
            if (stringList.size() < 7) {
                whereSql.append(" and match_type = 'noType' ");
            }
        }

        /**
         *   根据匹配类型（matchType）查对应表的数据
         *   matchType in ('close-match','loose-match')紧密匹配，宽泛匹配 不查询该表数据
         *   matchType in ('broad','phrase','exact')'广泛匹配','词组匹配','精准匹配' 查询该表数据
         *   matchTypes：符合查询该表数据的条件('广泛匹配','词组匹配','精准匹配')数组
         *   MatchValueEnum值是('广泛匹配','词组匹配','精准匹配')的枚举
         */
        List<String> matchTypeList = StringUtil.stringToList(dto.getMatchType(),StringUtil.SPLIT_COMMA);
        List<String> matchTypes = Lists.newArrayList();
        //不带匹配类型条件查询不走下面逻辑
        // start
        if (CollectionUtils.isNotEmpty(matchTypeList)) {
            for (String matchType : matchTypeList) {
                if (StringUtils.isNotBlank(MatchValueEnum.getMatchValue(matchType))) {
                    matchTypes.add(matchType);
                }
            }
            //如果匹配条件为(紧密匹配，宽泛匹配),则不查询数据
            //把matchTypes作为条件查不出数据
            if (CollectionUtils.isEmpty(matchTypes)){
                whereSql.append(" group by keyword_id,`query` having 1=0 ");
                return whereSql.toString();
            }
        }
        //end
        if (CollectionUtils.isNotEmpty(matchTypes)){
            whereSql.append(SqlStringUtil.dealInList("match_type",matchTypes,argsList));
        }
        if(StringUtils.isNotBlank(dto.getCampaignId())){
            List<String> list = StringUtil.splitStr(dto.getCampaignId());
            whereSql.append(SqlStringUtil.dealInList("campaign_id", list, argsList));

        }
        if (CollectionUtils.isNotEmpty(dto.getCampaignIdList())) { //广告组合查询
            whereSql.append(SqlStringUtil.dealInList("campaign_id", dto.getCampaignIdList(), argsList));
        }
        if(StringUtils.isNotBlank(dto.getGroupId())){
            List<String> list = StringUtil.splitStr(dto.getGroupId());
            whereSql.append(SqlStringUtil.dealInList("ad_group_id", list, argsList));
        }
        if (CollectionUtils.isNotEmpty(dto.getSearchQueryTagParamList())) {
            whereSql.append(" and (ad_group_id, query)  ");
            if (dto.getQueryWordTagTypeList().contains(Constants.QUERY_NOT_TARGET)) {
                whereSql.append(" not in ");
            } else {
                whereSql.append(" in ");
            }
            StringBuilder stringBuilder = new StringBuilder(" ( ");
            for (SearchQueryTagParam param : dto.getSearchQueryTagParamList()) {
                stringBuilder.append("( ?,? ),");
                argsList.add(param.getAdGroupId());
                argsList.add(param.getQuery());
            }
            stringBuilder.deleteCharAt(stringBuilder.length() - 1);
            stringBuilder.append(" )");
            whereSql.append(stringBuilder);
        }
        if(StringUtils.isNotBlank(dto.getSearchField()) && StringUtils.isNotBlank(dto.getSearchValue())){
            String field = SqlStringUtil.getSqlField(CpcQueryKeywordReport.class,dto.getSearchField());
            if(StringUtils.isNotEmpty(field)){
                if("blur".equals(dto.getSearchType())){ //模糊搜索
                    whereSql.append(" and ").append(field).append(" like ?");
                    argsList.add("%" + SqlStringUtil.dealLikeSql(dto.getSearchValue()) + "%");
                } else {//默认精确
                    if (dto.getListSearchValue().size() > 1) {
                        whereSql.append(SqlStringUtil.dealInList(field, dto.getListSearchValue(), argsList));
                    } else {
                        whereSql.append(" and ").append(field).append(" = ?");
                        argsList.add(dto.getListSearchValue().get(0));
                    }
                }
            }
        }
        whereSql.append(" group by keyword_id,`query` ");
        if(dto.getUseAdvanced()){
            BigDecimal shopSales = dto.getShopSales() != null ? dto.getShopSales() : BigDecimal.valueOf(0);

            whereSql.append(" having 1=1 ");
            //展示量
            if(dto.getImpressionsMin() != null){
                whereSql.append(" and impressions >= ?");
                argsList.add(dto.getImpressionsMin());
            }
            if(dto.getImpressionsMax() != null){
                whereSql.append(" and impressions <= ?");
                argsList.add(dto.getImpressionsMax());
            }
            //点击量
            if(dto.getClicksMin() != null){
                whereSql.append(" and clicks >= ?");
                argsList.add(dto.getClicksMin());
            }
            if(dto.getClicksMax() != null){
                whereSql.append(" and clicks <= ?");
                argsList.add(dto.getClicksMax());
            }
            //点击率（clicks/impressions）
            if(dto.getClickRateMin() != null){
                whereSql.append(" and ROUND(ifnull(clicks/impressions,0),4) >= ?");
                argsList.add(dto.getClickRateMin());
            }
            if(dto.getClickRateMax() != null){
                whereSql.append(" and ROUND(ifnull(clicks/impressions,0),4) <= ?");
                argsList.add(dto.getClickRateMax());
            }
            //花费
            if(dto.getCostMin() != null){
                whereSql.append(" and cost >= ?");
                argsList.add(dto.getCostMin());
            }
            if(dto.getCostMax() != null){
                whereSql.append(" and cost <= ?");
                argsList.add(dto.getCostMax());
            }
            //cpc  平均点击费用
            if(dto.getCpcMin() != null){
                whereSql.append(" and ROUND(ifnull(cost/clicks,0),2) >= ?");
                argsList.add(dto.getCpcMin());
            }
            if(dto.getCpcMax() != null){
                whereSql.append(" and ROUND(ifnull(cost/clicks,0),2) <= ?");
                argsList.add(dto.getCpcMax());
            }
            //广告订单量
            if(dto.getOrderNumMin() != null){
                whereSql.append(" and sale_num >= ?");
                argsList.add(dto.getOrderNumMin());
            }
            if(dto.getOrderNumMax() != null){
                whereSql.append(" and sale_num <= ?");
                argsList.add(dto.getOrderNumMax());
            }
            //广告销售额
            if(dto.getSalesMin() != null){
                whereSql.append(" and total_sales >= ?");
                argsList.add(dto.getSalesMin());
            }
            if(dto.getSalesMax() != null){
                whereSql.append(" and total_sales <= ?");
                argsList.add(dto.getSalesMax());
            }
            //订单转化率
            if(dto.getSalesConversionRateMin() != null){
                whereSql.append(" and ROUND(ifnull(sale_num/clicks,0),4) >= ?");
                argsList.add(dto.getSalesConversionRateMin());
            }
            if(dto.getSalesConversionRateMax() != null){
                whereSql.append(" and ROUND(ifnull(sale_num/clicks,0),4) <= ?");
                argsList.add(dto.getSalesConversionRateMax());
            }
            //acos
            if(dto.getAcosMin() != null){
                whereSql.append(" and ROUND(ifnull(cost/total_sales,0),4) >= ?");
                argsList.add(dto.getAcosMin());
            }
            if(dto.getAcosMax() != null){
                whereSql.append(" and ROUND(ifnull(cost/total_sales,0),4) <= ?");
                argsList.add(dto.getAcosMax());
            }
            // roas
            if (dto.getRoasMin() != null) {
                whereSql.append(" and ROUND(ifnull(total_sales/cost,0),2) >= ?");
                argsList.add(dto.getRoasMin());
            }
            // roas
            if (dto.getRoasMax() != null) {
                whereSql.append(" and ROUND(ifnull(total_sales/cost,0),2) <= ?");
                argsList.add(dto.getRoasMax());
            }
            // acots  需要乘以店铺销售额
            if (dto.getAcotsMin() != null) {
                if (shopSales.doubleValue() > 0) {
                    whereSql.append(" and ROUND((ifnull(cost,0) / ").append(shopSales).append(" ),4) >= ? ");
                    argsList.add(dto.getAcotsMin());
                } else {
                    whereSql.append(" and 0 >= ? ");
                    argsList.add(dto.getAcotsMin());
                }
            }
            // acots  需要乘以店铺销售额
            if (dto.getAcotsMax() != null) {
                if (shopSales.doubleValue() > 0) {
                    whereSql.append(" and ROUND((ifnull(cost,0) / ").append(shopSales).append(" ),4) <= ? ");
                    argsList.add(dto.getAcotsMax());
                } else {
                    whereSql.append(" and 0 <= ? ");
                    argsList.add(dto.getAcotsMin());
                }
            }
            // asots 需要乘以店铺销售额
            if (dto.getAsotsMin() != null) {
                if (shopSales.doubleValue() > 0) {
                    whereSql.append(" and ROUND((ifnull(total_sales,0) / ").append(shopSales).append(" ),4) >= ? ");
                    argsList.add(dto.getAsotsMin());
                } else {
                    whereSql.append(" and 0 >= ? ");
                    argsList.add(dto.getAcotsMin());
                }
            }
            // asots  需要乘以店铺销售额
            if (dto.getAsotsMax() != null) {
                if (shopSales.doubleValue() > 0) {
                    whereSql.append(" and ROUND((ifnull(total_sales,0) / ").append(shopSales).append(" ),4) <= ? ");
                    argsList.add(dto.getAsotsMax());
                } else {
                    whereSql.append(" and 0 <= ? ");
                    argsList.add(dto.getAcotsMin());
                }
            }

            /*****************************高级搜索新增查询指标字段******************************/

            //cpa
            if (dto.getCpaMin() != null) {
                whereSql.append(" and ROUND(ROUND(ifnull(cost/sale_num,0), 4), 2) >= ?");
                argsList.add(dto.getCpaMin());
            }
            if (dto.getCpaMax() != null) {
                whereSql.append(" and ROUND(ROUND(ifnull(cost/sale_num,0), 4), 2) <= ?");
                argsList.add(dto.getCpaMax());
            }

            //本广告产品订单量
            if (dto.getAdSaleNumMin() != null) {
                whereSql.append(" and ifnull(ad_sale_num, 0) >= ? ");
                argsList.add(dto.getAdSaleNumMin());
            }
            if (dto.getAdSaleNumMax() != null) {
                whereSql.append(" and ifnull(ad_sale_num, 0) <= ? ");
                argsList.add(dto.getAdSaleNumMax());
            }
            //其他产品广告订单量
            if (dto.getAdOtherOrderNumMin() != null) {
                whereSql.append(" and ifnull(sale_num - ad_sale_num, 0) >= ? ");
                argsList.add(dto.getAdOtherOrderNumMin());
            }
            if (dto.getAdOtherOrderNumMax() != null) {
                whereSql.append(" and ifnull(sale_num - ad_sale_num, 0) <= ? ");
                argsList.add(dto.getAdOtherOrderNumMax());
            }

            //本广告产品销售额
            if (dto.getAdSalesMin() != null) {
                whereSql.append(" and ifnull(ad_sales, 0) >= ? ");
                argsList.add(dto.getAdSalesMin());
            }
            if (dto.getAdSalesMax() != null) {
                whereSql.append(" and ifnull(ad_sales, 0) <= ? ");
                argsList.add(dto.getAdSalesMax());
            }
            //其他产品广告销售额
            if (dto.getAdOtherSalesMin() != null) {
                whereSql.append(" and ifnull(total_sales - ad_sales, 0) >= ? ");
                argsList.add(dto.getAdOtherSalesMin());
            }
            if (dto.getAdOtherSalesMax() != null) {
                whereSql.append(" and ifnull(total_sales - ad_sales, 0) <= ? ");
                argsList.add(dto.getAdOtherSalesMax());
            }
            //广告销量
            if (dto.getAdSalesTotalMin() != null) {
                whereSql.append(" and ifnull(order_num, 0) >= ? ");
                argsList.add(dto.getAdSalesTotalMin());
            }
            if (dto.getAdSalesTotalMax() != null) {
                whereSql.append(" and ifnull(order_num, 0) <= ? ");
                argsList.add(dto.getAdSalesTotalMax());
            }
            //本广告产品销量
            if (dto.getAdSelfSaleNumMin() != null) {
                whereSql.append(" and ifnull(ad_order_num, 0) >= ? ");
                argsList.add(dto.getAdSelfSaleNumMin());
            }
            if (dto.getAdSelfSaleNumMax() != null) {
                whereSql.append(" and ifnull(ad_order_num, 0) <= ? ");
                argsList.add(dto.getAdSelfSaleNumMax());
            }
            //其他产品广告销量
            if (dto.getAdOtherSaleNumMin() != null) {
                whereSql.append(" and ifnull(order_num - ad_order_num, 0) >= ? ");
                argsList.add(dto.getAdOtherSaleNumMin());
            }
            if (dto.getAdOtherSaleNumMax() != null) {
                whereSql.append(" and ifnull(order_num - ad_order_num, 0) <= ? ");
                argsList.add(dto.getAdOtherSaleNumMax());
            }
            // 广告笔单价 广告销售额÷广告订单量×100%
            if (dto.getAdvertisingUnitPriceMin() != null){
                whereSql.append(" and ROUND(ifnull(total_sales/sale_num, 0), 2) >= ?");
                argsList.add(dto.getAdvertisingUnitPriceMin());
            }
            if (dto.getAdvertisingUnitPriceMax() != null){
                whereSql.append(" and ROUND(ifnull(total_sales/sale_num, 0), 2) <= ?");
                argsList.add(dto.getAdvertisingUnitPriceMax());
            }


        }
        return whereSql.toString();
    }

    /**
     * 产品要求不管是搜索词投放还是asin投放，只要是asin格式都需要查询出来，
     * 为了不影响其它地方查询新建此方法用于查询两张表带asin格式的数据
     * @param puid
     * @param dto
     * @param page
     * @return
     */
    @Override
    public Page pageKeywordAndTargetManageList(Integer puid, CpcQueryWordDto dto, Page page) {

        StringBuilder sql = new StringBuilder("SELECT `query`,marketplace_id, type, keywordId, target_id,main_image, keywordText,matchType,targeting_expression,targeting_type,ad_group_id,ad_group_name,campaign_id,campaign_name,")
                .append("impressions,clicks,cost,sale_num,ad_order_num,")
                .append("total_sales,`ad_sales`,`ad_sale_num`, ")
                .append("order_num  from ( ");
        StringBuilder countSql = new StringBuilder("select count(*) from ( ");

        String tableName = getTableNameByStartDate(DateUtil.strToDate(dto.getStart(), DateUtil.PATTERN_YYYYMMDD));
        //订单量字段展示修改为 sale_num 更为准确
        StringBuilder selectTargetSql = new StringBuilder("SELECT `query`,marketplace_id,'target' as type,'' as keywordId, target_id,main_image, '' as keywordText,'' as matchType,targeting_expression,targeting_type,ad_group_id,ad_group_name,campaign_id,campaign_name,")
                .append("SUM(impressions) impressions,SUM(clicks) clicks,SUM(cost) cost,SUM(sale_num) sale_num,SUM(ad_order_num) ad_order_num,")
                .append("SUM(total_sales) total_sales, SUM(ad_sales)  `ad_sales`,SUM(ad_sale_num)  `ad_sale_num`, ")
                .append("SUM(order_num) order_num FROM ")
                .append(tableName)
                .append(" ");
        List<Object> argsList = Lists.newArrayList();
        String whereSql = getWhereSqlCountByTargetIdAndIsAsin(puid,dto,argsList);
        selectTargetSql.append(whereSql);

        String unionTableName = getTableNameByStartDateAndTableName(DateUtil.strToDate(dto.getStart(), DateUtil.PATTERN_YYYYMMDD), "t_cpc_query_keyword_report");
        StringBuilder selectKeywordSql = new StringBuilder("SELECT `query`,marketplace_id,'keyword' as type, keyword_id as keywordId, '' as target_id,")
                .append(" '' as main_image, keyword_text as keywordText,match_type as matchType,'' as targeting_expression,'' as targeting_type,")
                .append(" ad_group_id,ad_group_name,campaign_id,campaign_name,")
                .append("SUM(impressions) impressions,SUM(clicks) clicks,SUM(cost) cost,SUM(sale_num) sale_num,SUM(ad_order_num) ad_order_num,")
                .append("SUM(total_sales) total_sales, SUM(ad_sales)  `ad_sales`,SUM(ad_sale_num)  `ad_sale_num`, ")
                .append("SUM(order_num) order_num FROM ")
                .append(unionTableName)
                .append(" ");

        String keywordWhereSql = getWhereSqlCountByKeyword(puid,dto,argsList);
        selectKeywordSql.append(keywordWhereSql);

        sql.append(selectTargetSql);
        sql.append(" UNION ALL ");
        sql.append(selectKeywordSql);
        sql.append(" ) p ");

        if(StringUtils.isNotBlank(dto.getOrderField()) && StringUtils.isNotBlank(dto.getOrderValue())){
            String orderField = ReportService.getOrderField(dto.getOrderField(),false);
            if(StringUtils.isNotBlank(orderField)){
                sql.append(" order by ").append(orderField);
                if("desc".equals(dto.getOrderValue())){
                    sql.append(" desc");
                }
                sql.append(" , query desc ");
            }
        }

        countSql.append(selectTargetSql);
        countSql.append(" UNION ALL ");
        countSql.append(selectKeywordSql);
        countSql.append(" ) p ");

        Object[] args = argsList.toArray();
        return getPageResult(puid, page.getPageNo(), page.getPageSize(), countSql.toString(), args, sql.toString(), args,CpcQueryTargetingReport.class);
    }


    @Override
    public AdMetricDto getSumAdMetricDto(Integer puid, CpcQueryWordDto dto) {
        StringBuilder sql = new StringBuilder("SELECT SUM(cost) cost, SUM(sale_num) sale_num, ");
        sql.append("SUM(total_sales) total_sales, SUM(order_num) order_num from ( ");

        String tableName = getTableNameByStartDate(DateUtil.strToDate(dto.getStart(), DateUtil.PATTERN_YYYYMMDD));
        //订单量字段展示修改为 sale_num 更为准确
        StringBuilder selectTargetSql = new StringBuilder("SELECT `query`,'target' as type,'' as keywordId, target_id,main_image, '' as keywordText,'' as matchType,targeting_expression,targeting_type,ad_group_id,ad_group_name,campaign_id,campaign_name,")
                .append("SUM(impressions) impressions,SUM(clicks) clicks,SUM(cost) cost,SUM(sale_num) sale_num,SUM(ad_order_num) ad_order_num,")
                .append("SUM(total_sales) total_sales, SUM(ad_sales)  `ad_sales`,SUM(ad_sale_num)  `ad_sale_num`, ")
                .append("SUM(order_num) order_num FROM ")
                .append(tableName)
                .append(" ");
        List<Object> argsList = Lists.newArrayList();
        String whereSql = getWhereSqlCountByTargetIdAndIsAsin(puid,dto,argsList);
        selectTargetSql.append(whereSql);

        String unionTableName = getTableNameByStartDateAndTableName(DateUtil.strToDate(dto.getStart(), DateUtil.PATTERN_YYYYMMDD), "t_cpc_query_keyword_report");
        StringBuilder selectKeywordSql = new StringBuilder("SELECT `query`,'keyword' as type, keyword_id as keywordId, '' as target_id,")
                .append(" '' as main_image, keyword_text as keywordText,match_type as matchType,'' as targeting_expression,'' as targeting_type,")
                .append(" ad_group_id,ad_group_name,campaign_id,campaign_name,")
                .append("SUM(impressions) impressions,SUM(clicks) clicks,SUM(cost) cost,SUM(sale_num) sale_num,SUM(ad_order_num) ad_order_num,")
                .append("SUM(total_sales) total_sales, SUM(ad_sales)  `ad_sales`,SUM(ad_sale_num)  `ad_sale_num`, ")
                .append("SUM(order_num) order_num FROM ")
                .append(unionTableName)
                .append(" ");

        String keywordWhereSql = getWhereSqlCountByKeyword(puid,dto,argsList);
        selectKeywordSql.append(keywordWhereSql);

        sql.append(selectTargetSql);
        sql.append(" UNION ALL ");
        sql.append(selectKeywordSql);
        sql.append(" ) p ");


        HintManager hintManager = HintManager.getInstance();
        try {
            List<AdMetricDto> adMetricDtoList = getJdbcTemplate(puid, hintManager).query(sql.toString(), new RowMapper<AdMetricDto>() {
                @Override
                public AdMetricDto mapRow(ResultSet re, int i) throws SQLException {
                    AdMetricDto dto = AdMetricDto.builder()
                            .sumCost(Optional.ofNullable(re.getBigDecimal("cost")).orElse(BigDecimal.ZERO))
                            .sumAdSale(Optional.ofNullable(re.getBigDecimal("total_sales")).orElse(BigDecimal.ZERO))
                            .sumAdOrderNum(Optional.ofNullable(re.getBigDecimal("sale_num")).orElse(BigDecimal.ZERO))
                            .sumOrderNum(Optional.ofNullable(re.getBigDecimal("order_num")).orElse(BigDecimal.ZERO))
                            .build();
                    return dto;
                }
            }, argsList.toArray());
            return adMetricDtoList != null && adMetricDtoList.size() > 0 ? adMetricDtoList.get(0) : null;
        } finally {
            hintManager.close();
        }
    }

    /**
     * 产品要求不管是搜索词投放还是asin投放，只要是asin格式都需要查询出来，
     * 为了不影响其它地方查询新建此方法用于查询两张表带asin格式的数据
     * @param puid
     * @param dto
     * @return
     */
    @Override
    public List<AdHomePerformancedto> getKeywordAndTargetListAllTargetingReportByDate(Integer puid, CpcQueryWordDto dto) {
        StringBuilder sql = new StringBuilder("SELECT target_id, keyword_id, count_date, ")
                .append("impressions,clicks,cost,sale_num,ad_order_num,")
                .append("total_sales, `ad_sales`,`ad_sale_num`, ")
                .append("order_num FROM ( ");

        String tableName = getTableNameByStartDate(DateUtil.strToDate(dto.getStart(), DateUtil.PATTERN_YYYYMMDD));
        //订单量字段展示修改为 sale_num 更为准确
        StringBuilder selectTargetSql = new StringBuilder("SELECT target_id, '' as keyword_id, count_date, ")
                .append("SUM(impressions) impressions,SUM(clicks) clicks,SUM(cost) cost,SUM(sale_num) sale_num,SUM(ad_order_num) ad_order_num,")
                .append("SUM(total_sales) total_sales, SUM(ad_sales)  `ad_sales`,SUM(ad_sale_num)  `ad_sale_num`, ")
                .append("SUM(order_num) order_num FROM ")
                .append(tableName)
                .append(" ");
        List<Object> argsList = Lists.newArrayList();
        String whereSql = getWhereSqlCountByTargetIdAndIsAsin(puid,dto,argsList);
        selectTargetSql.append(whereSql);

        String unionTableName = getTableNameByStartDateAndTableName(DateUtil.strToDate(dto.getStart(), DateUtil.PATTERN_YYYYMMDD), "t_cpc_query_keyword_report");
        StringBuilder selectKeywordSql = new StringBuilder("SELECT '' as target_id, keyword_id, count_date, ")
                .append("SUM(impressions) impressions,SUM(clicks) clicks,SUM(cost) cost,SUM(sale_num) sale_num,SUM(ad_order_num) ad_order_num, ")
                .append("SUM(total_sales) total_sales, SUM(ad_sales)  `ad_sales`,SUM(ad_sale_num)  `ad_sale_num`, ")
                .append("SUM(order_num) order_num FROM ")
                .append(unionTableName)
                .append(" ");

        String keywordWhereSql = getWhereSqlCountByKeyword(puid,dto,argsList);
        selectKeywordSql.append(keywordWhereSql);

        sql.append(selectTargetSql);
        sql.append(" UNION ALL ");
        sql.append(selectKeywordSql);
        sql.append(" ) p ");

        Object[] args = argsList.toArray();

        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(sql.toString(), new RowMapper<AdHomePerformancedto>() {
                @Override
                public AdHomePerformancedto mapRow(ResultSet re, int i) throws SQLException {
                    return  AdHomePerformancedto.builder()
                            .targetId(re.getString("target_id"))
                            .keywordId(re.getString("keyword_id"))
                            .adCost(Optional.ofNullable(re.getBigDecimal("cost")).orElse(BigDecimal.ZERO))
                            .adOrderNum(Optional.ofNullable(re.getInt("sale_num")).orElse(0))
                            .adSale(Optional.ofNullable(re.getBigDecimal("total_sales")).orElse(BigDecimal.ZERO))
                            .clicks(Optional.ofNullable(re.getInt("clicks")).orElse(0))
                            .impressions(Optional.ofNullable(re.getInt("impressions")).orElse(0))
                            .countDate(re.getString("count_date"))
                            /**
                             * TODO 广告报告重构
                             * 本广告产品订单量
                             */
                            .adSaleNum(Optional.ofNullable(re.getInt("ad_sale_num")).orElse(0))
                            //本广告产品销售额
                            .adSales(Optional.ofNullable(re.getBigDecimal("ad_sales")).orElse(BigDecimal.ZERO))
                            //广告销量
                            .salesNum(Optional.ofNullable(re.getInt("order_num")).orElse(0))
                            //本广告产品销量
                            .orderNum(Optional.ofNullable(re.getInt("ad_order_num")).orElse(0))
                            .build();
                }
            }, args);
        } finally {
            hintManager.close();
        }

    }

    @Override
    public List<Long> getAllIdByPuidLimit(Integer puid, int start, int limit) {
        String sql = "select id from " + getJdbcHelper().getTable() + " where puid = ? and query_id = '' order by id limit ?, ?";
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).queryForList(sql, Long.class, puid, start, limit);
        } finally {
            hintManager.close();
        }
    }

    @Override
    public void updateQueryId(Integer puid, Long startId, Long endId) {
        String sql = "update " + getJdbcHelper().getTable() + " set query_id = md5(CONCAT(target_id, query)) where puid = ? and id >= ? and id <= ?";
        HintManager hintManager = HintManager.getInstance();
        try {
            getJdbcTemplate(puid, hintManager).update(sql, puid, startId, endId);
        } finally {
            hintManager.close();
        }
    }

    @Override
    public List<CpcQueryTargetingReport> listSpByTargetRule(AutoRuleObjectParam param, List<String> itemIdList) {
        StringBuilder selectSqlAll = new StringBuilder();
        List<Object> argsList = Lists.newArrayList();
        StringBuilder spTargetWhereSql = new StringBuilder(" where u.puid = ? ");
        argsList.add(param.getPuid());
        StringBuilder selectSqlSpTargeting = new StringBuilder("  select 'sp' adType, 'spTargeting' queryType, u.id, u.query_id, t.campaign_id, t.ad_group_id,u.campaign_name, u.ad_group_name, u.query, u.puid, u.shop_id, u.marketplace_id, u.targeting_text matchType , t.state, t.serving_status as servingStatus, u.targeting_text as targetKeywordText, u.targeting_expression, u.target_id , u.targeting_type  from t_cpc_query_targeting_report u " +
                " left join t_amazon_ad_targeting t on u.puid = t.puid and u.shop_id = t.shop_id and u.target_id = t.target_id  ");
        if (StringUtils.isNotBlank(param.getMatchType())) {
            spTargetWhereSql.append(" and u.targeting_text = ?");
            argsList.add(param.getMatchType());
        }
        if (param.getShopId() != null) {
            spTargetWhereSql.append(" and u.shop_id = ?");
            argsList.add(param.getShopId());
        }

        if (CollectionUtils.isNotEmpty(param.getServingStatusList())) {
            spTargetWhereSql.append(SqlStringUtil.dealInList(" t.serving_status", param.getServingStatusList(), argsList));
        }

        if (StringUtils.isNotBlank(param.getState())) {
            spTargetWhereSql.append(" and t.state = ?");
            argsList.add(param.getState());
        }

        //过滤模板关系
        if (CollectionUtils.isNotEmpty(itemIdList)) {
            //过滤模板关系
            spTargetWhereSql.append(SqlStringUtil.dealInList("u.query_id", itemIdList, argsList));
        }
        if (CollectionUtils.isNotEmpty(param.getCampaignIds())) { //广告活动查询
            spTargetWhereSql.append(SqlStringUtil.dealInList("t.campaign_id", param.getCampaignIds(), argsList));
        }

        if (CollectionUtils.isNotEmpty(param.getGroupIds())) { //广告组查询
            spTargetWhereSql.append(SqlStringUtil.dealInList("t.ad_group_id", param.getGroupIds(), argsList));
        }
        if (StringUtils.isNotBlank(param.getSearchValue())) {
            spTargetWhereSql.append(" and u.query like ? ");
            argsList.add("%" + param.getSearchValue() + "%");
        }
        selectSqlSpTargeting.append(spTargetWhereSql);
        selectSqlAll.append(selectSqlSpTargeting);
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(param.getPuid(), hintManager).query(selectSqlAll.toString(),argsList.toArray(),getMapper());
        } finally {
            hintManager.close();
        }
    }

    @Override
    public List<CpcQueryTargetingReport> listSpTargetingReport(Integer puid, Integer shopId, Integer page) {
        //订单量字段展示修改为 sale_num 更为准确
        StringBuilder selectSql = new StringBuilder("SELECT * FROM `t_cpc_query_targeting_report` ");
        List<Object> argsList = Lists.newArrayList();
        selectSql.append(" where query not REGEXP '" + Constants.ASIN_REGEX + "' ");
        if (page != null) {
            selectSql.append("  and puid=? and shop_id=? group by query_id order by update_time asc limit " + (page - 1) * LIMIT + "," + LIMIT);
        } else {
            selectSql.append("  and puid=? and shop_id=? group by query_id order by update_time desc limit " + LIMIT);
        }
        argsList.add(puid);
        argsList.add(shopId);
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(selectSql.toString(), argsList.toArray(), getMapper());
        } finally {
            hintManager.close();
        }
    }

    @Override
    public List<CpcQueryTargetingReport> listSpTargetingReportByTimeRange(Integer puid, Integer shopId, Integer timeRange) {
        //订单量字段展示修改为 sale_num 更为准确
        StringBuilder selectSql = new StringBuilder("SELECT * FROM " + getHotTableName());
        List<Object> argsList = Lists.newArrayList();
        selectSql.append(" where query not REGEXP '" + Constants.ASIN_REGEX + "' ");
        selectSql.append("  and puid=? and shop_id=? and update_time > ? group by query_id order by update_time desc limit " + LIMIT);
        argsList.add(puid);
        argsList.add(shopId);
        argsList.add(DateUtil.getDayByDaysAgo(timeRange, DateUtil.PATTERN_DATE_TIME));
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(selectSql.toString(), argsList.toArray(), getMapper());
        } finally {
            hintManager.close();
        }
    }

    @Override
    public List<WordBo> listWordBo(Integer puid, Integer shopId, Integer page) {
        StringBuilder selectSql = new StringBuilder("SELECT id wordId, query word from " + getJdbcHelper().getTable());
        List<Object> argsList = Lists.newArrayList();
        if (page != null) {
            selectSql.append(" where puid=? and shop_id=? and count_date >= ? and query not REGEXP '" + Constants.ASIN_REGEX + "' order by id desc limit " + (page - 1) * dynamicRefreshConfiguration.getWordRootCalculateLimit() + "," + dynamicRefreshConfiguration.getWordRootCalculateLimit());
        } else {
            selectSql.append(" where puid=? and shop_id=? and count_date >= ? and query not REGEXP '" + Constants.ASIN_REGEX + "' order by id desc limit " + dynamicRefreshConfiguration.getWordRootCalculateLimit());
        }
        argsList.add(puid);
        argsList.add(shopId);
        argsList.add(DateUtil.dateToStrWithTime(DateUtil.addDay(new Date(), dynamicRefreshConfiguration.getWordRootCalculateDateBeforeLimit()), DateUtil.PATTERN_YYYYMMDD));
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(selectSql.toString(), argsList.toArray(), new BeanPropertyRowMapper<>(WordBo.class));
        } finally {
            hintManager.close();
        }
    }

    @Override
    public List<WordBo> listWordBoTimeRange(Integer puid, Integer shopId, Integer timeRange) {
        StringBuilder selectSql = new StringBuilder("SELECT id wordId, query word from " + getJdbcHelper().getTable());
        List<Object> argsList = Lists.newArrayList();
        selectSql.append(" where puid=? and shop_id=? and count_date >= ? and update_time > ? and query not REGEXP '" + Constants.ASIN_REGEX + "' order by id desc limit " + Constants.WORD_ROOT_QUERY_INCREMENT_LIMIE);
        argsList.add(puid);
        argsList.add(shopId);
        argsList.add(DateUtil.dateToStrWithTime(DateUtil.addDay(new Date(), dynamicRefreshConfiguration.getWordRootCalculateDateBeforeLimit()), DateUtil.PATTERN_YYYYMMDD));
        argsList.add(DateUtil.getDayByDaysAgo(timeRange, DateUtil.PATTERN_DATE_TIME));
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(selectSql.toString(), argsList.toArray(), new BeanPropertyRowMapper<>(WordBo.class));
        } finally {
            hintManager.close();
        }
    }

    @Override
    public List<CpcQueryTargetingReport> wordListByIds(int puid, Integer shopId, List<Long> ids) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sql = new StringBuilder("select id, puid, shop_id, query_id, query, query_cn, target_id, marketplace_id, campaign_id, ad_group_id, count_date, ")
                .append(" cost, total_sales, ad_sales, impressions, clicks, order_num, ad_order_num, sale_num, ad_sale_num, ")
                .append(" targeting_expression, targeting_text, targeting_type, is_asin from ")
                .append(this.getJdbcHelper().getTable());
        sql.append(" where puid = ? and shop_id = ? ");
        argsList.add(puid);
        argsList.add(shopId);
        sql.append(SqlStringUtil.dealInList("id", ids, argsList));
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(sql.toString(), argsList.toArray(), new BeanPropertyRowMapper<>(CpcQueryTargetingReport.class));
        } finally {
            hintManager.close();
        }
    }

    private String getWhereSqlCountByTargetId(int puid, CpcQueryWordDto dto, List<Object> argsList) {
        StringBuilder whereSql = new StringBuilder(" where puid=? and shop_id=? and marketplace_id=? and count_date>=? and count_date<=? ");
        argsList.add(puid);
        argsList.add(dto.getShopId());
        argsList.add(dto.getMarketplaceId());
        argsList.add(dto.getStart());
        argsList.add(dto.getEnd());
        whereSql.append(" and query REGEXP '" + ASIN_REGEX + "' ");
        if(StringUtils.isNotBlank(dto.getCampaignId())){
            List<String> list = StringUtil.splitStr(dto.getCampaignId());
            whereSql.append(SqlStringUtil.dealInList("campaign_id", list, argsList));
        }
        if (CollectionUtils.isNotEmpty(dto.getCampaignIdList())) { //广告组合查询
            whereSql.append(SqlStringUtil.dealInList("campaign_id", dto.getCampaignIdList(), argsList));
        }
        if(StringUtils.isNotBlank(dto.getGroupId())){
            List<String> list = StringUtil.splitStr(dto.getGroupId());
            whereSql.append(SqlStringUtil.dealInList("ad_group_id", list, argsList));
        }
        if(StringUtils.isNotBlank(dto.getSearchField()) && StringUtils.isNotBlank(dto.getSearchValue())){
            String field = SqlStringUtil.getSqlField(CpcQueryTargetingReport.class,dto.getSearchField());
            if(StringUtils.isNotEmpty(field)){

                if("blur".equals(dto.getSearchType())){ //模糊搜索
                    if(dto.getSearchVelueList().size() > 1){
                        whereSql.append(" and (").append(SqlStringUtil.dealLikeListOr(field,dto.getSearchVelueList(),argsList,false)).append(")");
                    } else {
                        whereSql.append(" and ").append(field).append(" like ?");
                        argsList.add("%"+SqlStringUtil.dealLikeSql(dto.getSearchVelueList().get(0))+"%");
                    }

                }else{//默认精确
                    if(dto.getSearchVelueList().size() > 1){
                        whereSql.append(SqlStringUtil.dealInList(field,dto.getSearchVelueList(),argsList));
                    } else {
                        whereSql.append(" and ").append(field).append(" = ?");
                        argsList.add(dto.getSearchVelueList().get(0));
                    }
                }
            }
        }
        whereSql.append("group by target_id,`query` ");
        if(dto.getUseAdvanced()){
            BigDecimal shopSales = dto.getShopSales() != null ? dto.getShopSales() : BigDecimal.valueOf(0);

            whereSql.append(" having 1=1 ");
            //展示量
            if(dto.getImpressionsMin() != null){
                whereSql.append(" and impressions >= ?");
                argsList.add(dto.getImpressionsMin());
            }
            if(dto.getImpressionsMax() != null){
                whereSql.append(" and impressions <= ?");
                argsList.add(dto.getImpressionsMax());
            }
            //点击量
            if(dto.getClicksMin() != null){
                whereSql.append(" and clicks >= ?");
                argsList.add(dto.getClicksMin());
            }
            if(dto.getClicksMax() != null){
                whereSql.append(" and clicks <= ?");
                argsList.add(dto.getClicksMax());
            }
            //点击率（clicks/impressions）
            if(dto.getClickRateMin() != null){
                whereSql.append(" and ROUND(ifnull(clicks/impressions,0),4) >= ?");
                argsList.add(dto.getClickRateMin());
            }
            if(dto.getClickRateMax() != null){
                whereSql.append(" and ROUND(ifnull(clicks/impressions,0),4) <= ?");
                argsList.add(dto.getClickRateMax());
            }
            //花费
            if(dto.getCostMin() != null){
                whereSql.append(" and cost >= ?");
                argsList.add(dto.getCostMin());
            }
            if(dto.getCostMax() != null){
                whereSql.append(" and cost <= ?");
                argsList.add(dto.getCostMax());
            }
            //cpc  平均点击费用
            if(dto.getCpcMin() != null){
                whereSql.append(" and ROUND(ifnull(cost/clicks,0),2) >= ?");
                argsList.add(dto.getCpcMin());
            }
            if(dto.getCpcMax() != null){
                whereSql.append(" and ROUND(ifnull(cost/clicks,0),2) <= ?");
                argsList.add(dto.getCpcMax());
            }
            //广告订单量
            if(dto.getOrderNumMin() != null){
                whereSql.append(" and sale_num >= ?");
                argsList.add(dto.getOrderNumMin());
            }
            if(dto.getOrderNumMax() != null){
                whereSql.append(" and sale_num <= ?");
                argsList.add(dto.getOrderNumMax());
            }
            //广告销售额
            if(dto.getSalesMin() != null){
                whereSql.append(" and total_sales >= ?");
                argsList.add(dto.getSalesMin());
            }
            if(dto.getSalesMax() != null){
                whereSql.append(" and total_sales <= ?");
                argsList.add(dto.getSalesMax());
            }
            //订单转化率
            if(dto.getSalesConversionRateMin() != null){
                whereSql.append(" and ROUND(ifnull(sale_num/clicks,0),4) >= ?");
                argsList.add(dto.getSalesConversionRateMin());
            }
            if(dto.getSalesConversionRateMax() != null){
                whereSql.append(" and ROUND(ifnull(sale_num/clicks,0),4) <= ?");
                argsList.add(dto.getSalesConversionRateMax());
            }
            //acos
            if(dto.getAcosMin() != null){
                whereSql.append(" and ROUND(ifnull(cost/total_sales,0),4) >= ?");
                argsList.add(dto.getAcosMin());
            }
            if(dto.getAcosMax() != null){
                whereSql.append(" and ROUND(ifnull(cost/total_sales,0),4) <= ?");
                argsList.add(dto.getAcosMax());
            }
            // roas
            if (dto.getRoasMin() != null) {
                whereSql.append(" and ROUND(ifnull(total_sales/cost,0),2) >= ?");
                argsList.add(dto.getRoasMin());
            }
            // roas
            if (dto.getRoasMax() != null) {
                whereSql.append(" and ROUND(ifnull(total_sales/cost,0),2) <= ?");
                argsList.add(dto.getRoasMax());
            }
            // acots  需要乘以店铺销售额
            if (dto.getAcotsMin() != null) {
                if (shopSales.doubleValue() > 0) {
                    whereSql.append(" and ROUND((ifnull(cost,0) / ").append(shopSales).append(" ),4) >= ? ");
                    argsList.add(dto.getAcotsMin());
                } else {
                    whereSql.append(" and 0 >= ? ");
                    argsList.add(dto.getAcotsMin());
                }
            }
            // acots  需要乘以店铺销售额
            if (dto.getAcotsMax() != null) {
                if (shopSales.doubleValue() > 0) {
                    whereSql.append(" and ROUND((ifnull(cost,0) / ").append(shopSales).append(" ),4) <= ? ");
                    argsList.add(dto.getAcotsMax());
                } else {
                    whereSql.append(" and 0 <= ? ");
                    argsList.add(dto.getAcotsMin());
                }
            }
            // asots 需要乘以店铺销售额
            if (dto.getAsotsMin() != null) {
                if (shopSales.doubleValue() > 0) {
                    whereSql.append(" and ROUND((ifnull(total_sales,0) / ").append(shopSales).append(" ),4) >= ? ");
                    argsList.add(dto.getAsotsMin());
                } else {
                    whereSql.append(" and 0 >= ? ");
                    argsList.add(dto.getAcotsMin());
                }
            }
            // asots  需要乘以店铺销售额
            if (dto.getAsotsMax() != null) {
                if (shopSales.doubleValue() > 0) {
                    whereSql.append(" and ROUND((ifnull(total_sales,0) / ").append(shopSales).append(" ),4) <= ? ");
                    argsList.add(dto.getAsotsMax());
                } else {
                    whereSql.append(" and 0 <= ? ");
                    argsList.add(dto.getAcotsMin());
                }
            }
        }
        return whereSql.toString();
    }

    private String getWhereSqlCountByTargetIdAndIsAsin(int puid, CpcQueryWordDto dto, List<Object> argsList) {
        StringBuilder whereSql = new StringBuilder(" where puid=? and shop_id=? and marketplace_id=? and count_date>=? and count_date<=? ");
        argsList.add(puid);
        argsList.add(dto.getShopId());
        argsList.add(dto.getMarketplaceId());
        argsList.add(dto.getStart());
        argsList.add(dto.getEnd());
        if(StringUtils.isNotBlank(dto.getCampaignId())){
            List<String> list = StringUtil.splitStr(dto.getCampaignId());
            whereSql.append(SqlStringUtil.dealInList("campaign_id", list, argsList));
        }
        if (CollectionUtils.isNotEmpty(dto.getCampaignIdList())) { //广告组合查询
            whereSql.append(SqlStringUtil.dealInList("campaign_id", dto.getCampaignIdList(), argsList));
        }
        if(StringUtils.isNotBlank(dto.getGroupId())){
            List<String> list = StringUtil.splitStr(dto.getGroupId());
            whereSql.append(SqlStringUtil.dealInList("ad_group_id", list, argsList));
        }
        if (CollectionUtils.isNotEmpty(dto.getSearchQueryTagParamList())) {
            whereSql.append(" and (ad_group_id, query) ");
            if (dto.getQueryWordTagTypeList().contains(Constants.QUERY_NOT_TARGET)) {
                whereSql.append(" not in ");
            } else {
                whereSql.append(" in ");
            }
            StringBuilder stringBuilder = new StringBuilder(" ( ");
            for (SearchQueryTagParam param : dto.getSearchQueryTagParamList()) {
                stringBuilder.append("( ?,? ),");
                argsList.add(param.getAdGroupId());
                argsList.add(param.getQuery());
            }
            stringBuilder.deleteCharAt(stringBuilder.length() - 1);
            stringBuilder.append(" )");
            whereSql.append(stringBuilder);
        }

        if (StringUtils.isNotBlank(dto.getFilterTargetType())) {
            if (dto.getFilterTargetType().contains("=")) {
                List<String> list = StringUtil.splitStr(dto.getFilterTargetType()).stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
                if (list.size() < 7) {
                    List<String> inList = list.stream().filter(s -> !s.contains("=")).collect(Collectors.toList());
                    List<String> likeList = list.stream().filter(s -> s.contains("=")).collect(Collectors.toList());
                    whereSql.append(" and (");
                    if (CollectionUtils.isNotEmpty(inList)) {
                        whereSql.append(SqlStringUtil.dealInListNotAnd("targeting_expression", inList, argsList));
                        whereSql.append(" or ");
                        whereSql.append(SqlStringUtil.dealPrefixLikeListOr("targeting_expression", likeList, argsList));
                    } else {
                        whereSql.append(SqlStringUtil.dealPrefixLikeListOr("targeting_expression", likeList, argsList));
                    }
                    whereSql.append(")");
                }
            } else {
                whereSql.append(SqlStringUtil.dealInList("targeting_expression", StringUtil.splitStr(dto.getFilterTargetType()), argsList));
            }
        }

        whereSql.append(" and query REGEXP '" + ASIN_REGEX + "' ");
        if(StringUtils.isNotBlank(dto.getSearchField()) && StringUtils.isNotBlank(dto.getSearchValue())){
            String field = SqlStringUtil.getSqlField(CpcQueryTargetingReport.class,dto.getSearchField());
            if(StringUtils.isNotEmpty(field)){
                if("blur".equals(dto.getSearchType())){ //模糊搜索
                    whereSql.append(" and ").append(field).append(" like ?");
                    argsList.add("%" + SqlStringUtil.dealLikeSql(dto.getSearchValue()) + "%");
                } else {//默认精确
                    if (dto.getListSearchValue().size() > 1) {
                        whereSql.append(SqlStringUtil.dealInList(field, dto.getListSearchValue(), argsList));
                    } else {
                        whereSql.append(" and ").append(field).append(" = ?");
                        argsList.add(dto.getListSearchValue().get(0));
                    }
                }
            }
        }
        whereSql.append("group by target_id,`query` ");
        if(dto.getUseAdvanced()){
            BigDecimal shopSales = dto.getShopSales() != null ? dto.getShopSales() : BigDecimal.valueOf(0);

            whereSql.append(" having 1=1 ");
            //展示量
            if(dto.getImpressionsMin() != null){
                whereSql.append(" and impressions >= ?");
                argsList.add(dto.getImpressionsMin());
            }
            if(dto.getImpressionsMax() != null){
                whereSql.append(" and impressions <= ?");
                argsList.add(dto.getImpressionsMax());
            }
            //点击量
            if(dto.getClicksMin() != null){
                whereSql.append(" and clicks >= ?");
                argsList.add(dto.getClicksMin());
            }
            if(dto.getClicksMax() != null){
                whereSql.append(" and clicks <= ?");
                argsList.add(dto.getClicksMax());
            }
            //点击率（clicks/impressions）
            if(dto.getClickRateMin() != null){
                whereSql.append(" and ROUND(ifnull(clicks/impressions,0),4) >= ?");
                argsList.add(dto.getClickRateMin());
            }
            if(dto.getClickRateMax() != null){
                whereSql.append(" and ROUND(ifnull(clicks/impressions,0),4) <= ?");
                argsList.add(dto.getClickRateMax());
            }
            //花费
            if(dto.getCostMin() != null){
                whereSql.append(" and cost >= ?");
                argsList.add(dto.getCostMin());
            }
            if(dto.getCostMax() != null){
                whereSql.append(" and cost <= ?");
                argsList.add(dto.getCostMax());
            }
            //cpc  平均点击费用
            if(dto.getCpcMin() != null){
                whereSql.append(" and ROUND(ifnull(cost/clicks,0),2) >= ?");
                argsList.add(dto.getCpcMin());
            }
            if(dto.getCpcMax() != null){
                whereSql.append(" and ROUND(ifnull(cost/clicks,0),2) <= ?");
                argsList.add(dto.getCpcMax());
            }
            //广告订单量
            if(dto.getOrderNumMin() != null){
                whereSql.append(" and sale_num >= ?");
                argsList.add(dto.getOrderNumMin());
            }
            if(dto.getOrderNumMax() != null){
                whereSql.append(" and sale_num <= ?");
                argsList.add(dto.getOrderNumMax());
            }
            //广告销售额
            if(dto.getSalesMin() != null){
                whereSql.append(" and total_sales >= ?");
                argsList.add(dto.getSalesMin());
            }
            if(dto.getSalesMax() != null){
                whereSql.append(" and total_sales <= ?");
                argsList.add(dto.getSalesMax());
            }
            //订单转化率
            if(dto.getSalesConversionRateMin() != null){
                whereSql.append(" and ROUND(ifnull(sale_num/clicks,0),4) >= ?");
                argsList.add(dto.getSalesConversionRateMin());
            }
            if(dto.getSalesConversionRateMax() != null){
                whereSql.append(" and ROUND(ifnull(sale_num/clicks,0),4) <= ?");
                argsList.add(dto.getSalesConversionRateMax());
            }
            //acos
            if(dto.getAcosMin() != null){
                whereSql.append(" and ROUND(ifnull(cost/total_sales,0),4) >= ?");
                argsList.add(dto.getAcosMin());
            }
            if(dto.getAcosMax() != null){
                whereSql.append(" and ROUND(ifnull(cost/total_sales,0),4) <= ?");
                argsList.add(dto.getAcosMax());
            }
            // roas
            if (dto.getRoasMin() != null) {
                whereSql.append(" and ROUND(ifnull(total_sales/cost,0),2) >= ?");
                argsList.add(dto.getRoasMin());
            }
            // roas
            if (dto.getRoasMax() != null) {
                whereSql.append(" and ROUND(ifnull(total_sales/cost,0),2) <= ?");
                argsList.add(dto.getRoasMax());
            }
            // acots  需要乘以店铺销售额
            if (dto.getAcotsMin() != null) {
                if (shopSales.doubleValue() > 0) {
                    whereSql.append(" and ROUND((ifnull(cost,0) / ").append(shopSales).append(" ),4) >= ? ");
                    argsList.add(dto.getAcotsMin());
                } else {
                    whereSql.append(" and 0 >= ? ");
                    argsList.add(dto.getAcotsMin());
                }
            }
            // acots  需要乘以店铺销售额
            if (dto.getAcotsMax() != null) {
                if (shopSales.doubleValue() > 0) {
                    whereSql.append(" and ROUND((ifnull(cost,0) / ").append(shopSales).append(" ),4) <= ? ");
                    argsList.add(dto.getAcotsMax());
                } else {
                    whereSql.append(" and 0 <= ? ");
                    argsList.add(dto.getAcotsMin());
                }
            }
            // asots 需要乘以店铺销售额
            if (dto.getAsotsMin() != null) {
                if (shopSales.doubleValue() > 0) {
                    whereSql.append(" and ROUND((ifnull(total_sales,0) / ").append(shopSales).append(" ),4) >= ? ");
                    argsList.add(dto.getAsotsMin());
                } else {
                    whereSql.append(" and 0 >= ? ");
                    argsList.add(dto.getAcotsMin());
                }
            }
            // asots  需要乘以店铺销售额
            if (dto.getAsotsMax() != null) {
                if (shopSales.doubleValue() > 0) {
                    whereSql.append(" and ROUND((ifnull(total_sales,0) / ").append(shopSales).append(" ),4) <= ? ");
                    argsList.add(dto.getAsotsMax());
                } else {
                    whereSql.append(" and 0 <= ? ");
                    argsList.add(dto.getAcotsMin());
                }
            }

            /*****************************高级搜索新增查询指标字段******************************/

            //cpa
            if (dto.getCpaMin() != null) {
                whereSql.append(" and ROUND(ROUND(ifnull(cost/sale_num,0), 4), 2) >= ?");
                argsList.add(dto.getCpaMin());
            }
            if (dto.getCpaMax() != null) {
                whereSql.append(" and ROUND(ROUND(ifnull(cost/sale_num,0), 4), 2) <= ?");
                argsList.add(dto.getCpaMax());
            }

            //本广告产品订单量
            if (dto.getAdSaleNumMin() != null) {
                whereSql.append(" and ifnull(ad_sale_num, 0) >= ? ");
                argsList.add(dto.getAdSaleNumMin());
            }
            if (dto.getAdSaleNumMax() != null) {
                whereSql.append(" and ifnull(ad_sale_num, 0) <= ? ");
                argsList.add(dto.getAdSaleNumMax());
            }
            //其他产品广告订单量
            if (dto.getAdOtherOrderNumMin() != null) {
                whereSql.append(" and ifnull(sale_num - ad_sale_num, 0) >= ? ");
                argsList.add(dto.getAdOtherOrderNumMin());
            }
            if (dto.getAdOtherOrderNumMax() != null) {
                whereSql.append(" and ifnull(sale_num - ad_sale_num, 0) <= ? ");
                argsList.add(dto.getAdOtherOrderNumMax());
            }

            //本广告产品销售额
            if (dto.getAdSalesMin() != null) {
                whereSql.append(" and ifnull(ad_sales, 0) >= ? ");
                argsList.add(dto.getAdSalesMin());
            }
            if (dto.getAdSalesMax() != null) {
                whereSql.append(" and ifnull(ad_sales, 0) <= ? ");
                argsList.add(dto.getAdSalesMax());
            }
            //其他产品广告销售额
            if (dto.getAdOtherSalesMin() != null) {
                whereSql.append(" and ifnull(total_sales - ad_sales, 0) >= ? ");
                argsList.add(dto.getAdOtherSalesMin());
            }
            if (dto.getAdOtherSalesMax() != null) {
                whereSql.append(" and ifnull(total_sales - ad_sales, 0) <= ? ");
                argsList.add(dto.getAdOtherSalesMax());
            }
            //广告销量
            if (dto.getAdSalesTotalMin() != null) {
                whereSql.append(" and ifnull(order_num, 0) >= ? ");
                argsList.add(dto.getAdSalesTotalMin());
            }
            if (dto.getAdSalesTotalMax() != null) {
                whereSql.append(" and ifnull(order_num, 0) <= ? ");
                argsList.add(dto.getAdSalesTotalMax());
            }
            //本广告产品销量
            if (dto.getAdSelfSaleNumMin() != null) {
                whereSql.append(" and ifnull(ad_order_num, 0) >= ? ");
                argsList.add(dto.getAdSelfSaleNumMin());
            }
            if (dto.getAdSelfSaleNumMax() != null) {
                whereSql.append(" and ifnull(ad_order_num, 0) <= ? ");
                argsList.add(dto.getAdSelfSaleNumMax());
            }
            //其他产品广告销量
            if (dto.getAdOtherSaleNumMin() != null) {
                whereSql.append(" and ifnull(order_num - ad_order_num, 0) >= ? ");
                argsList.add(dto.getAdOtherSaleNumMin());
            }
            if (dto.getAdOtherSaleNumMax() != null) {
                whereSql.append(" and ifnull(order_num - ad_order_num, 0) <= ? ");
                argsList.add(dto.getAdOtherSaleNumMax());
            }
            // 广告笔单价 广告销售额÷广告订单量×100%
            if (dto.getAdvertisingUnitPriceMin() != null){
                whereSql.append(" and ROUND(ifnull(total_sales/sale_num, 0), 2) >= ?");
                argsList.add(dto.getAdvertisingUnitPriceMin());
            }
            if (dto.getAdvertisingUnitPriceMax() != null){
                whereSql.append(" and ROUND(ifnull(total_sales/sale_num, 0), 2) <= ?");
                argsList.add(dto.getAdvertisingUnitPriceMax());
            }

        }
        return whereSql.toString();
    }

    @Override
    public int deleteByPuidAndShopId(Integer puid, Integer shopId, Integer limit) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sql = new StringBuilder("delete from ").append(this.getJdbcHelper().getTable()).append(" where puid = ? ");
        argsList.add(puid);
        sql.append(" and shop_id = ? ");
        argsList.add(shopId);
        if (limit != null) {
            sql.append(" limit ? ");
            argsList.add(limit);
        }
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).update(sql.toString(), argsList.toArray());
        } finally {
            hintManager.close();
        }
    }

    @Override
    public List<String> listQueryId(ProcessTaskParam param) {
        StringBuilder selectSqlAll = new StringBuilder();
        List<Object> argsList = Lists.newArrayList();
        StringBuilder spTargetWhereSql = new StringBuilder(" where u.puid = ? ");
        argsList.add(param.getPuid());
        StringBuilder selectSqlSpTargeting = new StringBuilder("  select u.query_id from t_cpc_query_targeting_report u " +
                " left join t_amazon_ad_targeting t on u.puid = t.puid and u.shop_id = t.shop_id and u.target_id = t.target_id  ");
        if (StringUtils.isNotBlank(param.getMatchType())) {
            spTargetWhereSql.append(" and u.targeting_text = ?");
            argsList.add(param.getMatchType());
        }
        if (param.getShopId() != null) {
            spTargetWhereSql.append(" and u.shop_id = ?");
            argsList.add(param.getShopId());
        }

        if (CollectionUtils.isNotEmpty(param.getServingStatusList())) {
            spTargetWhereSql.append(SqlStringUtil.dealInList(" t.serving_status", param.getServingStatusList(), argsList));
        }

        if (StringUtils.isNotBlank(param.getState())) {
            spTargetWhereSql.append(" and t.state = ?");
            argsList.add(param.getState());
        }

        //过滤模板关系
        if (CollectionUtils.isNotEmpty(param.getItemIdList())) {
            //过滤模板关系
            spTargetWhereSql.append(SqlStringUtil.dealInList("u.query_id", param.getItemIdList(), argsList));
        }
        if (CollectionUtils.isNotEmpty(param.getCampaignIds())) { //广告活动查询
            spTargetWhereSql.append(SqlStringUtil.dealInList("t.campaign_id", param.getCampaignIds(), argsList));
        }

        if (CollectionUtils.isNotEmpty(param.getGroupIds())) { //广告组查询
            spTargetWhereSql.append(SqlStringUtil.dealInList("t.ad_group_id", param.getGroupIds(), argsList));
        }
        if (StringUtils.isNotBlank(param.getSearchValue())) {
            spTargetWhereSql.append(" and u.query like ? ");
            argsList.add("%" + param.getSearchValue() + "%");
        }
        selectSqlSpTargeting.append(spTargetWhereSql);
        selectSqlAll.append(selectSqlSpTargeting);
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(param.getPuid(), hintManager).queryForList(selectSqlAll.toString(), argsList.toArray(), String.class);
        } finally {
            hintManager.close();
        }
    }

    @Override
    public List<SearchQueryTagParam> listAdGroupIdByQueryWordDto(CpcQueryWordDto dto) {

        StringBuilder sql = new StringBuilder("SELECT `ad_group_id` adGroupId, query query FROM ( ");

        String tableName = getTableNameByStartDate(DateUtil.strToDate(dto.getStart(), DateUtil.PATTERN_YYYYMMDD));
        //订单量字段展示修改为 sale_num 更为准确
        StringBuilder selectTargetSql = new StringBuilder("SELECT `query`,marketplace_id,'target' as type,'' as keywordId, target_id,main_image, '' as keywordText,'' as matchType,targeting_expression,targeting_type,ad_group_id,ad_group_name,campaign_id,campaign_name,")
                .append("SUM(impressions) impressions,SUM(clicks) clicks,SUM(cost) cost,SUM(sale_num) sale_num,SUM(ad_order_num) ad_order_num,")
                .append("SUM(total_sales) total_sales, SUM(ad_sales)  `ad_sales`,SUM(ad_sale_num)  `ad_sale_num`, ")
                .append("SUM(order_num) order_num FROM ")
                .append(tableName)
                .append(" ");
        List<Object> argsList = Lists.newArrayList();
        String whereSql = getWhereSqlCountByTargetIdAndIsAsin(dto.getPuid(),dto,argsList);
        selectTargetSql.append(whereSql);

        String unionTableName = getTableNameByStartDateAndTableName(DateUtil.strToDate(dto.getStart(), DateUtil.PATTERN_YYYYMMDD), "t_cpc_query_keyword_report");
        StringBuilder selectKeywordSql = new StringBuilder("SELECT `query`,marketplace_id,'keyword' as type, keyword_id as keywordId, '' as target_id,")
                .append(" '' as main_image, keyword_text as keywordText,match_type as matchType,'' as targeting_expression,'' as targeting_type,")
                .append(" ad_group_id,ad_group_name,campaign_id,campaign_name,")
                .append("SUM(impressions) impressions,SUM(clicks) clicks,SUM(cost) cost,SUM(sale_num) sale_num,SUM(ad_order_num) ad_order_num,")
                .append("SUM(total_sales) total_sales, SUM(ad_sales)  `ad_sales`,SUM(ad_sale_num)  `ad_sale_num`, ")
                .append("SUM(order_num) order_num FROM ")
                .append(unionTableName)
                .append(" ");

        String keywordWhereSql = getWhereSqlCountByKeyword(dto.getPuid(),dto,argsList);
        selectKeywordSql.append(keywordWhereSql);

        sql.append(selectTargetSql);
        sql.append(" UNION ALL ");
        sql.append(selectKeywordSql);
        sql.append(" ) p ");


        Object[] args = argsList.toArray();
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(dto.getPuid(), hintManager).query(sql.toString(), args, new BeanPropertyRowMapper<>(SearchQueryTagParam.class));
        } catch (Exception e) {
            logger.error("获取所有搜索词接口查询失败: ", e);
            throw e;
        } finally {
            hintManager.close();
        }
    }



    @Override
    public List<CpcQueryTargetingReport> getDetailList(Integer puid, CpcQueryWordDetailDto dto) {
        StringBuilder selectSql = new StringBuilder(" SELECT ");
        selectSql.append(" count_date,");
        selectSql.append("impressions,clicks,cost,sale_num as sale_num,ad_order_num,order_num,total_sales ");

        String tableName = getTableNameByStartDate(DateUtil.strToDate(dto.getStart(), DateUtil.PATTERN_YYYYMMDD));
        selectSql.append(" FROM ").append(tableName).append(" ");
        StringBuilder whereSql = new StringBuilder(" where puid=? and shop_id=? and marketplace_id=? and count_date>=? and count_date<=? ");
        List<Object> argsList = Lists.newArrayList();
        argsList.add(puid);
        argsList.add(dto.getShopId());
        argsList.add(dto.getMarketplaceId());
        argsList.add(dto.getStart());
        argsList.add(dto.getEnd());
        whereSql.append(" and target_id=? and `query`=? ");
        argsList.add(dto.getTargetId());
        argsList.add(dto.getQuery());
        selectSql.append(whereSql);
        Object[] args = argsList.toArray();
        HintManager hintManager = HintManager.getInstance();
        try {
            return this.getJdbcTemplate(puid, hintManager).query(selectSql.toString(), getMapper(), args);
        } catch (Exception e) {
            logger.error("获取所有搜索词接口查询失败: ", e);
            throw e;
        } finally {
            hintManager.close();
        }
    }

}