package com.meiyunji.sponsored.service.attribution.service;

import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.attribution.entity.AmazonAdAttributionTag;

/**
 * @author: wade
 * @date: 2022/3/12 16:55
 * @describe:
 */
public interface IAmazonAdAttributionTagService {
    AmazonAdAttributionTag getTag(ShopAuth shopAuth, String profileId, String publisherId, String advertiserId, Boolean isMacronTag);
}
