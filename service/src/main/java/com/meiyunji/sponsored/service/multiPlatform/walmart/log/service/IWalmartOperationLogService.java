package com.meiyunji.sponsored.service.multiPlatform.walmart.log.service;

import com.meiyunji.sponsored.service.multiPlatform.walmart.log.po.WalmartAdvertisingOperationLog;
import com.meiyunji.sponsored.service.multiPlatform.walmart.po.*;

import java.util.List;
import java.util.Map;

/**
 * @author: ys
 * @date: 2025/2/21 10:21
 * @describe:
 */
public interface IWalmartOperationLogService {

    List<WalmartAdvertisingOperationLog> getCampaignLog(Integer operatorId, WalmartAdvertisingCampaign oldCampaign,
                                                  WalmartAdvertisingCampaign currentCampaign);
    WalmartAdvertisingOperationLog getCampaignDeleteLog(Integer operatorId, WalmartAdvertisingCampaign currentCampaign);
    List<WalmartAdvertisingOperationLog> getGroupLog(String adType,  Integer operatorId,
                                               WalmartAdvertisingGroup oldGroup, WalmartAdvertisingGroup currentGroup);
    WalmartAdvertisingOperationLog getItemLog(Integer puid, Integer shopId,
                                              String campaignId, String marketplaceCode,
                                              String adType,  Integer operatorId,
                                              Map<String, WalmartAdvertisingItem> oldItemMap, List<WalmartAdvertisingItem> currentItemList);
    WalmartAdvertisingOperationLog getPlacementLog(Integer puid, Integer shopId,
                                                   String campaignId, String marketplaceCode,
                                                   String adType,  Integer operatorId,
                                                   Map<String, WalmartAdvertisingPlacement> oldPlacementMap, List<WalmartAdvertisingPlacement> currentPlacement);
    WalmartAdvertisingOperationLog getKeywordLog(Integer puid, Integer shopId,
                                                 String campaignId, String marketplaceCode,
                                                 String adType,  Integer operatorId,
                                                 Map<String, WalmartAdvertisingKeyword> oldKeywordMap, List<WalmartAdvertisingKeyword> currentKeywordList);
    WalmartAdvertisingOperationLog getMultiplierLog(Integer puid, Integer shopId,
                                                    String campaignId, String marketplaceCode,
                                                    String adType, Integer operatorId,
                                                    Map<String, WalmartAdvertisingMultiplier> oldMultiplierMap, List<WalmartAdvertisingMultiplier> currentMultiplier);

    void saveLog(Integer puid, List<WalmartAdvertisingOperationLog> operationLogs);
    void printAdOperationLog(List<WalmartAdvertisingOperationLog> adOperationLogs);
}
