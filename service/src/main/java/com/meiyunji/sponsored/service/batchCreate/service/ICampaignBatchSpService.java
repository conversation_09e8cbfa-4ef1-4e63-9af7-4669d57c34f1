package com.meiyunji.sponsored.service.batchCreate.service;

import com.meiyunji.sponsored.service.batchCreate.dto.campaign.CampaignInfoDTO;
import com.meiyunji.sponsored.service.batchCreate.dto.productView.AsinViewBatchSpListDTO;

import java.util.List;

/**
 * @author: ys
 * @date: 2023/11/23 14:59
 * @describe:
 */
public interface ICampaignBatchSpService {

    AsinViewBatchSpListDTO getCampaignWithTaskInfoById(Integer puid, Long id);

    List<CampaignInfoDTO> getCampaignListByTaskId(Integer puid, String marketplaceId,
                                                  Integer shopId, Long taskId);

    void terminateCampaignsByTaskId(Integer puid, Long taskId);
}
