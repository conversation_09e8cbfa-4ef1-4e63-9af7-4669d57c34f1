package com.meiyunji.sponsored.service.download.dao.impl;

import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.springjdbc.BaseShardingDaoImpl;
import com.meiyunji.sponsored.common.springjdbc.ConditionBuilder;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdInvoiceDetails;
import com.meiyunji.sponsored.service.download.dao.IAdDownloadCenterDetailsDao;
import com.meiyunji.sponsored.service.download.po.AdDownloadCenterDetails;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Repository
public class AdDownloadCenterDetailsDaoImpl extends BaseShardingDaoImpl<AdDownloadCenterDetails> implements IAdDownloadCenterDetailsDao {
    @Override
    public List<AdDownloadCenterDetails> getList(Integer puid, List<Long> ids,List<Integer> shopIdList) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder();
        builder.equalTo("puid",puid);
        if (CollectionUtils.isNotEmpty(ids)) {
            builder.in("task_id",ids.toArray());
        }
        if (CollectionUtils.isNotEmpty(shopIdList)) {
            builder.in("shop_id",shopIdList.toArray());
        }
        return listByCondition(puid,builder.build());
    }

    @Override
    public void batchInsert(Integer puid, List<AdDownloadCenterDetails> list) {
        StringBuilder sql = new StringBuilder("INSERT INTO `t_ad_download_center_task_details` (`puid`,`shop_id`,`marketplace_id`,`task_id`,");
        sql.append("`create_time`,`update_time` ) values");
        List<Object> argsList = Lists.newArrayList();
        for (AdDownloadCenterDetails details : list) {
            sql.append(" (?,?,?,?,now(),now()),");
            argsList.add(puid);
            argsList.add(details.getShopId());
            argsList.add(details.getMarketplaceId());
            argsList.add(details.getTaskId());
        }
        sql.deleteCharAt(sql.length()-1);
        getJdbcTemplate(puid).update(sql.toString(),argsList.toArray());
    }

    @Override
    public void deleteDetails(Integer puid, Long taskId) {
        String sql = "DELETE FROM t_ad_download_center_task_details WHERE puid = ? and task_id = ?";
        List<Object> args = Lists.newArrayList(puid, taskId);
        getJdbcTemplate(puid).update(sql, args.toArray());
    }

    @Override
    public List<Integer> getShopIdList(Integer puid, Long id) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("task_id", id);
        return listDistinctFieldByCondition(puid,"shop_id",builder.build(), Integer.class);
    }
}
