package com.meiyunji.sponsored.service.enums;

public enum LogSubmoduleEnum {
    SYSTEM_USER("system_user","log.submodule.user"),
    SYSTEM_SHOP("system_shop","log.submodule.shop"),
    SYSTEM_CONFIG("system_config", "log.submodule.config"),

    COMMENT("comment","log.submodule.comment"),
    FBM_COST("fbm_cost","log.submodule.fbm"),
    CPC_MANAGE("cpc_manage","log.submodule.cpc.manage"),
    VCPM_MANAGE("vcpm_manage","log.submodule.vcpm.manage"),
    PRODUCT_MANAGE("product_manage","log.submodule.product.manage"),
    COMMODITY_MANAGE("commodity_manage","log.submodule.commodity.manage"),
    STATS_PROFIT("stats_profit","log.submodule.stats.profit"),
    ORDER_MANAGE("order_manage","log.submodule.order.manage"),
    KEYWORD_MANAGE("keyword_manage","log.submodule.monitor.keyword"),
    FBA_MANAGE("fba_manage","log.submodule.fba.manage"),
    CPC_TASK("cpc_task","log.submodule.cpc.task"),
    WARE_MANAGE("ware_manage","log.submodule.ware.manage"),
    WARE_IN_OUT("ware_in_out","log.submodule.ware.in.out"),
    WARE_ADJUST("ware_adjust","log.submodule.ware.adjust"),
    WARE_ASSEMBLE("ware_assemble","log.submodule.ware.assemble"),
    WARE_OVERSEA("ware_oversea","log.submodule.ware.oversea"),
    WARE_BATCH("ware_batch","log.submodule.ware.batch"),
    PURCHASE_MANAGE("purchase_manage","log.submodule.purchase.manage"),
    PRODUCT_AUTO_MATCH("product_auto_match","log.submodule.product.autoMatch"),
    ;




    private String submodule;
    private String languageCode;

    LogSubmoduleEnum(String submodule, String languageCode) {
        this.submodule = submodule;
        this.languageCode = languageCode;
    }

    public String getSubmodule() {
        return submodule;
    }

    public String getLanguageCode() {
        return languageCode;
    }

    public String getLanguageCode(String submodule) {
        for (LogSubmoduleEnum en : LogSubmoduleEnum.values()) {
            if (en.getSubmodule().equals(submodule)) {
                return en.languageCode;
            }
        }
        return null;
    }
}
