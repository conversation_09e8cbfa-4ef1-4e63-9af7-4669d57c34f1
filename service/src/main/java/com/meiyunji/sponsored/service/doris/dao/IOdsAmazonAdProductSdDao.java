package com.meiyunji.sponsored.service.doris.dao;

import com.meiyunji.sponsored.common.springjdbc.IDorisBaseDao;
import com.meiyunji.sponsored.service.doris.po.OdsAmazonAdProductSd;
import com.meiyunji.sponsored.service.doris.po.OdsProduct;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.dto.AdGroupAndAdIdDto;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.dto.AsinCampaignNumDto;

import java.util.List;

/**
 * amazon SD广告产品表(OdsAmazonAdProductSd)数据库访问层
 *
 * <AUTHOR>
 * @since 2024-03-26 10:46:19
 */
public interface IOdsAmazonAdProductSdDao extends IDorisBaseDao<OdsAmazonAdProductSd> {

    List<OdsAmazonAdProductSd> listByShopAsin(Integer puid, List<Integer> shopIdList, List<String> asinList, List<String> adGroupIds);

    List<OdsAmazonAdProductSd> listByShopParentAsin(Integer puid, List<Integer> shopIdList, List<String> parentAsinList);

    List<OdsProduct> getOdsProductByIds(Integer puid, String marketplaceId, List<Integer> shopIds, List<Long> idList);

    List<AdGroupAndAdIdDto> getAdIdAndAdGroupIdByAsin(int puid, List<Integer> shopIdList, String asin);

    List<AsinCampaignNumDto> getAsinCampaignNum(int puid, List<Integer> shopIdList, List<String> asinList);
}

