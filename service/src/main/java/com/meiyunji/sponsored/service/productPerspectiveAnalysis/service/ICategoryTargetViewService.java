package com.meiyunji.sponsored.service.productPerspectiveAnalysis.service;

import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.qo.DiagnoseCountParam;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.qo.TargetViewParam;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.vo.CategoryTargetViewAggregatePageVo;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.vo.CategoryTargetViewVo;

import java.util.List;
import java.util.Map;

/**
 * @author: liweibin
 * @email: <EMAIL>
 * @date: 2023-09-14  15:34
 */
public interface ICategoryTargetViewService {
    /**
     * 获取商品投放视图列表页（通过feed分页）
     * @param puid
     * @param param
     * @return
     */
    Page<CategoryTargetViewVo> getVoPageByFeedPage(Integer puid, TargetViewParam param);

    /**
     * 获取商品投放视图列表页（通过内存分页）
     * @param puid
     * @param param
     * @return
     */
    Page<CategoryTargetViewVo> getVoPageByInternalMemory(Integer puid, TargetViewParam param);

    /**
     * 获取商品投放视图汇总
     *
     * @param puid
     * @param param
     * @return
     */
    CategoryTargetViewAggregatePageVo getCategoryTargetViewAggregatePageVo(Integer puid, TargetViewParam param);

    /**
     * 获取商品投放视图列表页（通过feed分页），支持广告类型多选
     * @param puid
     * @param param
     * @return
     */
    Page<CategoryTargetViewVo> getAllTargetViewPageVoList(Integer puid, TargetViewParam param);

    /**
     * 获取商品投放视图汇总，支持广告类型多选
     */
    CategoryTargetViewAggregatePageVo getAllCategoryTargetViewAggregatePageVo(Integer puid, TargetViewParam param);
}
