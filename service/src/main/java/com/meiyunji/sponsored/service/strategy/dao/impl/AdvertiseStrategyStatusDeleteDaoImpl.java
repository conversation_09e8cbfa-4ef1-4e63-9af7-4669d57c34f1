package com.meiyunji.sponsored.service.strategy.dao.impl;

import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.springjdbc.BaseShardingDaoImpl;
import com.meiyunji.sponsored.common.springjdbc.ConditionBuilder;
import com.meiyunji.sponsored.service.strategy.dao.AdvertiseStrategyStatusDeleteDao;
import com.meiyunji.sponsored.service.strategy.po.AdvertiseStrategyStatus;
import com.meiyunji.sponsored.service.strategy.po.AdvertiseStrategyStatusDelete;
import com.meiyunji.sponsored.service.strategy.vo.ControlledObjectParam;
import com.meiyunji.sponsored.service.strategy.vo.ExecuteParam;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class AdvertiseStrategyStatusDeleteDaoImpl extends BaseShardingDaoImpl<AdvertiseStrategyStatusDelete> implements AdvertiseStrategyStatusDeleteDao {
    @Override
    public AdvertiseStrategyStatusDelete getByTaskId(int puid, int shopId, Long taskId, String itemType) {
        return getByCondition(puid, new ConditionBuilder.Builder().equalTo("puid",puid).
                equalTo("shop_id",shopId).equalTo("item_type",itemType).equalTo("task_id", taskId).build());
    }

    @Override
    public List<AdvertiseStrategyStatusDelete> getListByTaskIds(int puid, List<Long> taskId, String itemType) {
        return listByCondition(puid, new ConditionBuilder.Builder().
                equalTo("puid",puid).
                equalTo("item_type",itemType).
                in("task_id", taskId.toArray()).build());
    }

    @Override
    public AdvertiseStrategyStatusDelete getByStatusId(int puid, Long statusId) {
        return null;
    }

    @Override
    public List<AdvertiseStrategyStatusDelete> getByStatusIds(int puid, List<Long> statusIds) {
        return null;
    }

    @Override
    public List<AdvertiseStrategyStatusDelete> getByItemIds(int puid, int shopId, String itemType, List<String> itemIds) {
        return null;
    }

    @Override
    public int updateByPrimaryKey(int puid, AdvertiseStrategyStatusDelete record) {
        return 0;
    }

    @Override
    public int batchInsert(int puid, List<AdvertiseStrategyStatusDelete> list) {
        return 0;
    }

    @Override
    public Integer insetStrategyStatus(int puid, AdvertiseStrategyStatus advertiseStrategyStatus) {
        StringBuilder sql = new StringBuilder("insert into t_advertise_strategy_status_delete (id,task_id,puid, shop_id," +
                "      marketplace_id, ad_type, item_type," +
                "      `type`, item_id, origin_value,return_value, " +
                "      template_id, `rule`, version, " +
                "      `status`,campaign_id,ad_group_id,target_type,target_name,`children_item_type`, asin, sku, " +
                "      `start_stop_item_type`,create_at, last_update_at" +
                "      )" +
                "    values ");
        List<Object> argsList = Lists.newArrayList();
        sql.append("(?,?,?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? ,?, ?, ?,?, ?, ?, ?, ?, now(), now())");
        argsList.add(advertiseStrategyStatus.getId());
        argsList.add(advertiseStrategyStatus.getTaskId());
        argsList.add(puid);
        argsList.add(advertiseStrategyStatus.getShopId());
        argsList.add(advertiseStrategyStatus.getMarketplaceId());
        argsList.add(advertiseStrategyStatus.getAdType());
        argsList.add(advertiseStrategyStatus.getItemType());
        argsList.add(advertiseStrategyStatus.getType());
        argsList.add(advertiseStrategyStatus.getItemId());
        argsList.add(advertiseStrategyStatus.getOriginValue());
        argsList.add(advertiseStrategyStatus.getReturnValue());
        argsList.add(advertiseStrategyStatus.getTemplateId());
        argsList.add(advertiseStrategyStatus.getRule());
        argsList.add(advertiseStrategyStatus.getVersion());
        argsList.add(advertiseStrategyStatus.getStatus());
        argsList.add(advertiseStrategyStatus.getCampaignId());
        argsList.add(advertiseStrategyStatus.getAdGroupId());
        argsList.add(advertiseStrategyStatus.getTargetType());
        argsList.add(advertiseStrategyStatus.getTargetName());
        argsList.add(advertiseStrategyStatus.getChildrenItemType());
        argsList.add(advertiseStrategyStatus.getAsin());
        argsList.add(advertiseStrategyStatus.getSku());
        argsList.add(advertiseStrategyStatus.getStartStopItemType());
        return getJdbcTemplate(puid).update(sql.toString(), argsList.toArray());
    }

    @Override
    public Integer updateStrategyStatus(int puid, AdvertiseStrategyStatusDelete advertiseStrategyStatusDelete) {
        return null;
    }

    @Override
    public Integer deleteStrategyStatus(int puid, Long statusId, int shopId) {
        return null;
    }

    @Override
    public Integer deleteStrategyByTaskId(int puid, int shopId, Long taskId) {
        return null;
    }

    @Override
    public Page<AdvertiseStrategyStatusDelete> pageAdControlledCampaign(ControlledObjectParam param) {
        return null;
    }

    @Override
    public Page<AdvertiseStrategyStatusDelete> pageAdControlledCampaignState(ControlledObjectParam param) {
        return null;
    }

    @Override
    public Page<AdvertiseStrategyStatusDelete> pageAdControlledCampaignSpace(ControlledObjectParam param) {
        return null;
    }

    @Override
    public Page<AdvertiseStrategyStatusDelete> pageAdControlledTarget(ControlledObjectParam param) {
        return null;
    }

    @Override
    public Page<AdvertiseStrategyStatusDelete> pageAdControlledPortfolio(ControlledObjectParam param) {
        return null;
    }

    @Override
    public List<AdvertiseStrategyStatusDelete> getListByTemplateId(ExecuteParam param) {
        return null;
    }

    @Override
    public Integer existListByTemplateId(Integer puid, Long templateId) {
        return null;
    }

    @Override
    public List<AdvertiseStrategyStatusDelete> getTemplateUsageAmount(int puid, List<Long> templateId, String itemType) {
        return null;
    }

    @Override
    public void updateStatus(int puid, Long templateId, String type, Long statusId, String rule, String status, Integer version, String originValue, String returnValue) {

    }

    @Override
    public void updateRule(int puid, Integer shopId, String type, Long statusId, String rule) {

    }

    @Override
    public void updateRuleAndReturnValue(int puid, Integer shopId, String type, Long statusId, String rule, String returnValue) {

    }

    @Override
    public void updateStrategyStatusById(int puid, Long statusId, String status) {

    }

    @Override
    public void updateStatusTemplateById(int puid, Long statusId, Long templateId) {

    }

    @Override
    public List<AdvertiseStrategyStatusDelete> getListByPuidAndShopIds(int puid, List<Integer> shopIds, Long templateId, String itemType) {
        return null;
    }

    @Override
    public AdvertiseStrategyStatusDelete getObjectByPuidAndShopId(int puid, int shopId, String itemId, String itemType) {
        return null;
    }

    @Override
    public List<AdvertiseStrategyStatusDelete> getListByCampaignId(int puid, Integer shopId, Long templateId, List<String> campaignIds) {
        return null;
    }

    @Override
    public List<AdvertiseStrategyStatusDelete> getLisByItemIds(Integer puid, Integer shopId, String itemType, List<String> itemIds) {
        return null;
    }

    @Override
    public int deleteByPuidAndShopId(Integer puid, Integer shopId, Integer limit) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sql = new StringBuilder("delete from ").append(this.getJdbcHelper().getTable()).append(" where puid = ? ");
        argsList.add(puid);
        sql.append("and shop_id = ?");
        argsList.add(shopId);
        if (limit != null) {
            sql.append(" limit ? ");
            argsList.add(limit);
        }
        return getJdbcTemplate(puid).update(sql.toString(), argsList.toArray());
    }

    @Override
    public List<String> getItemIdList(int puid, int shopId, String adGroupId) {
        ConditionBuilder conditionBuilder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .equalTo("item_type", "TARGET")
                .equalTo("ad_group_id", adGroupId)
                .build();
        return listDistinctFieldByCondition(puid, "item_id", conditionBuilder, String.class);
    }
}
