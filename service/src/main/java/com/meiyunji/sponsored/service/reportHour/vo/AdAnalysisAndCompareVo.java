package com.meiyunji.sponsored.service.reportHour.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.meiyunji.sponsored.common.util.MathUtil;
import com.meiyunji.sponsored.service.excel.excelTools.ExportFormat;
import com.meiyunji.sponsored.service.excel.excelTools.ExportFormatType;
import com.meiyunji.sponsored.service.excel.excelTools.converter.StringConverter;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Objects;
import java.util.Optional;

/**
 * @author: ys
 * @date: 2023/10/13 13:53
 * @describe: 对比周期中增长率父类Vo
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
public class AdAnalysisAndCompareVo{

    @ExcelProperty(value = "广告点击量", index = 8)
    public Long clicks = 0L;

    @ExcelProperty(value = "广告点击量（对比）", index = 9)
    public Long clicksCompare = 0L;

    @ExcelProperty(value = "广告点击量（比率）", index = 10)
    public BigDecimal clicksCompareRate = BigDecimal.ZERO;

    @ExcelProperty(value = "广告曝光量", index = 5)
    public Long impressions = 0L;

    @ExcelProperty(value = "广告曝光量(对比)", index = 6)
    public Long impressionsCompare = 0L;

    @ExcelProperty(value = "广告曝光量（比率）", converter = StringConverter.class, index = 7)
    public BigDecimal impressionsCompareRate = BigDecimal.ZERO;

    @ExcelProperty(value = "广告订单量", index = 34)
    public Integer adOrderNum = 0;

    @ExcelProperty(value = "广告订单量（对比）", index = 35)
    public Integer adOrderNumCompare = 0;

    @ExcelProperty(value = "广告订单量（比率）", index = 36)
    public BigDecimal adOrderNumCompareRate = BigDecimal.ZERO;


    @ExcelProperty(value = "广告花费", converter = StringConverter.class, index = 2)
    @ExportFormat(value = ExportFormatType.CURRENCY)
    public BigDecimal adCost = BigDecimal.ZERO;

    @ExcelProperty(value = "广告花费", converter = StringConverter.class, index = 3)
    @ExportFormat(value = ExportFormatType.CURRENCY)
    public BigDecimal adCostCompare = BigDecimal.ZERO;

    @ExcelProperty(value = "广告花费(比率)", converter = StringConverter.class, index = 4)
    @ExportFormat(value = ExportFormatType.CURRENCY)
    public BigDecimal adCostCompareRate = BigDecimal.ZERO;

    @ExcelProperty(value = "广告销售额", converter = StringConverter.class)
    @ExportFormat(value = ExportFormatType.CURRENCY)
    public BigDecimal adSale = BigDecimal.ZERO;

    @ExcelProperty(value = "广告销售额(对比)", converter = StringConverter.class)
    @ExportFormat(value = ExportFormatType.CURRENCY)
    public BigDecimal adSaleCompare = BigDecimal.ZERO;

    @ExcelProperty(value = "广告销售额(比率)", converter = StringConverter.class)
    public BigDecimal adSaleCompareRate = BigDecimal.ZERO;

    @ExcelProperty(value = "广告销量")
    public Integer adSaleNum = 0;

    @ExcelProperty(value = "广告销量（对比）")
    public Integer adSaleNumCompare = 0;

    @ExcelProperty(value = "广告销量（比率）")
    public BigDecimal adSaleNumCompareRate = BigDecimal.ZERO;

    public static BigDecimal calculateCompareRete(BigDecimal currVal, BigDecimal compareVal) {
        currVal = Optional.ofNullable(currVal).map(c -> c.setScale(2, RoundingMode.HALF_UP)).orElse(BigDecimal.ZERO);
        compareVal = Optional.ofNullable(compareVal).map(p -> p.setScale(2, RoundingMode.HALF_UP)).orElse(BigDecimal.ZERO);
        if (BigDecimal.ZERO.compareTo(compareVal) == 0 && Objects.nonNull(currVal) && BigDecimal.ZERO.compareTo(currVal) !=0) {
            return new BigDecimal(100);
        }
        BigDecimal subVal = MathUtil.subtract(currVal, compareVal);
        return MathUtil.divideByZeroScale2(subVal.multiply(BigDecimal.valueOf(100)), compareVal);
    }

    public static String  calculateCompareReteWithLine(BigDecimal currVal, BigDecimal compareVal) {
        currVal = Optional.ofNullable(currVal).map(c -> c.setScale(2, RoundingMode.HALF_UP)).orElse(BigDecimal.ZERO);
        compareVal = Optional.ofNullable(compareVal).map(p -> p.setScale(2, RoundingMode.HALF_UP)).orElse(BigDecimal.ZERO);
        if (BigDecimal.ZERO.compareTo(compareVal) == 0) {
            return "-";
        }
        BigDecimal subVal = MathUtil.subtract(currVal, compareVal);
        return MathUtil.divideByZeroScale2(subVal.multiply(BigDecimal.valueOf(100)), compareVal).toString();
    }

    /**
     * 完成click等属性设置后，调用此方法为对比率进行设值
     */
    public void afterPropertiesSet() {
        if (Objects.nonNull(this.clicks) && Objects.nonNull(this.clicksCompare)) {
            this.clicksCompareRate = calculateCompareRete(BigDecimal.valueOf(this.clicks), BigDecimal.valueOf(this.clicksCompare));
        }
        if (Objects.nonNull(this.impressions) && Objects.nonNull(this.impressionsCompare)) {
            this.impressionsCompareRate = calculateCompareRete(BigDecimal.valueOf(this.impressions), BigDecimal.valueOf(this.impressionsCompare));
        }
        if (Objects.nonNull(this.adCost) && Objects.nonNull(this.adCostCompare)) {
            this.adCostCompareRate = calculateCompareRete(this.adCost, this.adCostCompare);
        }
        if (Objects.nonNull(this.adOrderNum) && Objects.nonNull(adOrderNumCompare)) {
            this.adOrderNumCompareRate = calculateCompareRete(BigDecimal.valueOf(this.adOrderNum), BigDecimal.valueOf(adOrderNumCompare));
        }
        if (Objects.nonNull(this.adSale) && Objects.nonNull(this.adSaleCompare)) {
            this.adSaleCompareRate = calculateCompareRete(this.adSale, this.adSaleCompare);
        }
        if (Objects.nonNull(this.adSaleNum) && Objects.nonNull(this.adSaleNumCompare)) {
            this.adSaleNumCompareRate = calculateCompareRete(BigDecimal.valueOf(this.adSaleNum), BigDecimal.valueOf(this.adSaleNumCompare));
        }
    }

    public void compareDataSet(AdAnalysisAndCompareVo compare) {
        if (Objects.isNull(compare)) {
            return;
        }
        this.clicksCompare = compare.clicks;
        this.impressionsCompare = compare.impressions;
        this.adCostCompare = compare.adCost;
        this.adOrderNumCompare = compare.adOrderNum;
        this.adSaleCompare = compare.adSale;
        this.adSaleNumCompare = compare.adSaleNum;
    }

}
