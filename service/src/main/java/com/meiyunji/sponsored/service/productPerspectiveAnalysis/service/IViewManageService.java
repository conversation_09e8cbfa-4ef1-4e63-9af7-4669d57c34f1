package com.meiyunji.sponsored.service.productPerspectiveAnalysis.service;


import com.meiyunji.sponsored.rpc.productPerspectiveAnalysis.viewManage.*;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.qo.*;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.vo.*;

import java.lang.reflect.InvocationTargetException;
import java.util.List;
import java.util.Map;

/**
 * @author: liwei<PERSON>
 * @email: <EMAIL>
 * @date: 2023-08-28  16:49
 */
public interface IViewManageService {
    /**
     * 广告活动视图
     * @param puid
     * @param param
     * @return
     */
    CampaignViewResponse.CampaignVo getCampaignView(Integer puid, CampaignViewParam param);

    /**
     * 广告位视图列表页
     * @param puid
     * @param param
     * @return
     */
    PlacementViewResponse.PlacementVo getPlacementView(Integer puid, PlacementViewParam param);

    /**
     * 广告位视图汇总
     * @param puid
     * @param param
     * @return
     */
    PlacementViewAggregateResponse.AggregateVo getPlacementViewAggregate(Integer puid, PlacementViewParam param);

    /**
     * 关键词投放视图列表页
     * @param puid
     * @param param
     * @return
     */
    KeywordViewResponse.KeywordVo getKeywordView(Integer puid, KeywordViewParam param);

    /**
     * 关键词投放视图汇总
     * @param puid
     * @param param
     * @return
     */
    KeywordViewAggregateResponse.AggregateVo getKeywordViewAggregate(Integer puid, KeywordViewParam param);

    /**
     * 自动投放视图列表页、汇总
     * @param puid
     * @param param
     * @return
     */
    AutoTargetViewResponse.TargetVo getAutoTargetView(Integer puid, TargetViewParam param);

    /**
     * 商品投放视图列表页
     * @param puid
     * @param param
     * @return
     */
    CategoryTargetViewResponse.TargetVo getCategoryTargetView(Integer puid, TargetViewParam param);

    /**
     * 商品投放视图汇总
     * @param puid
     * @param param
     * @return
     */
    CategoryTargetViewAggregateResponse.AggregateVo getCategoryTargetViewAggregate(Integer puid, TargetViewParam param);

    void fillShopSellerId(Integer puid, ViewBaseParam param);

    void fillShopSale(Integer puid, ViewBaseParam param);

    /**
     * 初始化广告活动，广告位，自动投放聚合数据
     * @param aggregateViewVo
     * @return
     */
    AggregateViewVo buildAggregateViewVo(StreamDataViewVo aggregateViewVo);

    /**
     * 初始化关键词投放聚合数据
     * @param aggregateViewVo
     * @return
     */
    KeywordViewAggregateResponse.AggregateVo.KeywordAggregateVo buildKeywordAggregateViewVo(KeywordViewAggregateVo aggregateViewVo);

    /**
     * 初始化商品投放聚合数据
     * @param aggregateViewVo
     * @return
     */
    CategoryTargetViewAggregateResponse.AggregateVo.TargetAggregateVo buildTargetAggregateViewVo(CategoryTargetViewAggregateVo aggregateViewVo);

    /**
     * 初始化受众投放聚合数据
     * @param aggregateViewVo
     * @return
     */
    AudienceTargetViewAggregateResponse.AggregateVo.TargetAggregateVo buildAudienceTargetAggregateViewVo(AudienceTargetViewAggregateVo aggregateViewVo);

    SearchTermsViewResponse.SearchTermsVo getSearchTermsView(Integer puid, SearchTermsViewParam param);

    SearchTermsViewResponse.SearchTermsVo getAllSearchTermsView(Integer puid, SearchTermsViewParam param);

    /**
     * 广告活动视图，支持广告类型多选
     * @param puid
     * @param param
     * @return
     */
    CampaignViewResponse.CampaignVo getAllCampaignView(Integer puid, CampaignViewParam param);

    /**
     * 关键词投放视图列表页，支持广告类型多选
     * @param puid
     * @param param
     * @return
     */
    KeywordViewResponse.KeywordVo getAllKeywordView(Integer puid, KeywordViewParam param);

    /**
     * 关键词投放视图汇总，支持广告类型多选
     * @param puid
     * @param param
     * @return
     */
    KeywordViewAggregateResponse.AggregateVo getAllKeywordViewAggregate(Integer puid, KeywordViewParam param);

    /**
     * 商品投放视图列表页，支持广告类型多选
     * @param puid
     * @param param
     * @return
     */
    CategoryTargetViewResponse.TargetVo getAllCategoryTargetView(Integer puid, TargetViewParam param);

    /**
     * 商品投放视图汇总，支持广告类型多选
     * @param puid
     * @param param
     * @return
     */
    CategoryTargetViewAggregateResponse.AggregateVo getAllCategoryTargetViewAggregate(Integer puid, TargetViewParam param);

    /**
     * 受众投放视图列表页
     * @param puid
     * @param param
     * @return
     */
    AudienceTargetViewResponse.TargetVo getAllAudienceTargetView(Integer puid, AudienceTargetViewParam param);

    /**
     * 受众投放视图汇总
     * @param puid
     * @param param
     * @return
     */
    AudienceTargetViewAggregateResponse.AggregateVo getAllAudienceTargetViewAggregate(Integer puid, AudienceTargetViewParam param);
}
