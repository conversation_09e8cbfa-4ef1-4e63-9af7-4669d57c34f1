# 查询优化配置
query:
  optimization:
    # 是否启用查询优化
    enable-optimization: true
    
    # 是否启用性能监控
    enable-performance-monitoring: true
    
    # 慢查询阈值（毫秒）
    slow-query-threshold: 5000
    
    # 全局阈值配置
    small-data-threshold: 1000
    medium-data-threshold: 5000
    large-data-threshold: 10000
    batch-size: 1000
    
    # Campaign 查询优化配置
    campaign:
      small-threshold: 500
      medium-threshold: 2000
      use-exists-optimization: true
      use-temp-table-optimization: true
      # 是否使用 array_contains 优化
      use-array-contains: true
      # 是否使用 bitmap 优化
      use-bitmap-optimization: false
    
    # Ad Group 查询优化配置
    ad-group:
      small-threshold: 800
      medium-threshold: 3000
      use-batch-query: true
      # 是否使用 EXISTS 子查询
      use-exists-subquery: true
    
    # Product 查询优化配置
    product:
      small-threshold: 1000
      medium-threshold: 5000
      use-partition-query: true
      partition-size: 1000
      # 是否使用分区优化
      use-partition-optimization: true

# Doris 数据库特定配置
doris:
  query:
    # 是否启用 bitmap 函数
    enable-bitmap-functions: true
    # 是否启用 array 函数
    enable-array-functions: true
    # 是否启用 lateral view explode
    enable-lateral-view-explode: true
    # 单次查询最大 IN 条件数量
    max-in-conditions: 10000
    # 分批查询的批次大小
    batch-size: 1000

# 性能监控配置
performance:
  monitoring:
    # 是否启用详细日志
    enable-detailed-logging: false
    # 统计信息保留时间（小时）
    stats-retention-hours: 24
    # 是否自动清理统计数据
    auto-cleanup-stats: true
    # 性能报告生成间隔（分钟）
    report-interval-minutes: 60

# 缓存配置（用于查询结果缓存）
cache:
  query-result:
    # 是否启用查询结果缓存
    enabled: false
    # 缓存过期时间（分钟）
    expire-minutes: 30
    # 最大缓存条目数
    max-entries: 1000
    # 缓存键前缀
    key-prefix: "query:result:"
