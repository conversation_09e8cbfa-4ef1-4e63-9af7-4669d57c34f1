# 执行阶段SQL过滤配置
db:
  config:
    # 基础配置
    log:
      enable: true  # 启用SQL日志
    
    stat:
      enable: true  # 启用统计检查
      throwError: true  # 抛出异常
      sellerIThrowError: true  # seller_id检查抛出异常
    
    # seller_id 检查配置
    sellerId:
      check:
        enable: true  # 启用预编译阶段检查
        tables: "users,orders,products,t_amazon_marketing_stream_data"  # 目标表列表
      
      execution:
        check:
          enable: true  # 启用执行阶段检查（检查实际参数值）

# 日志配置
logging:
  level:
    com.meiyunji.sponsored.common.filter.SfSqlFilter: DEBUG
    com.meiyunji.sponsored.common.filter.ParameterValueValidator: DEBUG

# 示例配置说明
# 
# 1. 预编译阶段检查 (db.config.sellerId.check.enable=true)
#    - 检查SQL模板中的字段存在性和字面值有效性
#    - 对于参数化查询 (seller_id = ?) 假设参数有效
#
# 2. 执行阶段检查 (db.config.sellerId.execution.check.enable=true)  
#    - 检查实际传入的参数值
#    - 可以拦截 seller_id = null 或 seller_id = "" 的情况
#
# 3. 性能考虑
#    - 执行阶段检查会增加一定的性能开销
#    - 建议在开发和测试环境启用，生产环境根据需要选择性启用
#
# 4. 使用场景
#    - 开发阶段：两个检查都启用，确保代码质量
#    - 测试阶段：两个检查都启用，发现潜在问题
#    - 生产阶段：可以只启用预编译检查，或根据业务需要启用执行阶段检查
